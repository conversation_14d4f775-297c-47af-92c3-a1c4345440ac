import request from '@/utils/request'

// 查询标准品台账列表
export function listTz(query) {
  return request({
    url: '/qc/tz/list',
    method: 'get',
    params: query
  })
}

// 查询标准品台账详细
export function getTz(id) {
  return request({
    url: '/qc/tz/' + id,
    method: 'get'
  })
}

// 新增标准品台账
export function addTz(data) {
  return request({
    url: '/qc/tz',
    method: 'post',
    data: data
  })
}

// 修改标准品台账
export function updateTz(data) {
  return request({
    url: '/qc/tz',
    method: 'put',
    data: data
  })
}

// 删除标准品台账
export function delTz(id) {
  return request({
    url: '/qc/tz/' + id,
    method: 'delete'
  })
}

// 导出标准品台账
export function exportTz(query) {
  return request({
    url: '/qc/tz/export',
    method: 'get',
    params: query
  })
}

export function countRemindTz(query) {
  return request({
    url: '/qc/tz/remindCount',
    method: 'get',
    params: query
  })
}

export function allRemindTz(query) {
  return request({
    url: '/qc/tz/remindAll',
    method: 'get',
    params: query
  })
}

export function exportRemindTz(query) {
  return request({
    url: '/qc/tz/exportRemind',
    method: 'get',
    params: query
  })
}

export function readAllTz() {
  return request({
    url: '/qc/tz/readAll',
    method: 'put'
  })
}
