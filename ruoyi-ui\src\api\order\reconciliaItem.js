import request from '@/utils/request'

// 查询对账清单列表
export function allReconciliaItem(query) {
  return request({
    url: '/order/reconciliaItem/all',
    method: 'get',
    params: query
  })
}

// 查询对账清单列表
export function allPaymentReconciliaOrderGoods(query) {
  return request({
    url: '/order/reconciliaItem/allPaymentReconciliaOrderGoods',
    method: 'get',
    params: query
  })
}

// 查询发票清单列表
export function allPaymentBillingOrderGoods(query) {
  return request({
    url: '/order/reconciliaItem/allPaymentBillingOrderGoods',
    method: 'get',
    params: query
  })
}

// 查询对账清单列表
export function allReconciliaPaymentLogItem(query) {
  return request({
    url: '/order/reconciliaItem/allReconciliaPaymentLogItem',
    method: 'get',
    params: query
  })
}

// 查询对账清单详细
export function getReconciliaItem(id) {
  return request({
    url: '/order/reconciliaItem/' + id,
    method: 'get'
  })
}

export function batchSaveReconciliaItem(data) {
  return request({
    url: '/order/reconciliaItem/batchSave',
    method: 'post',
    data: data
  })
}

export function orderGoods(query) {
  return request({
    url: '/order/reconciliaItem/orderGoods',
    method: 'get',
    params: query
  })
}

export function revokeReconciliaItem(data) {
  return request({
    url: '/order/reconciliaItem/revoke',
    method: 'post',
    data: data
  })
}
