import request from '@/utils/request'

// 查询客户开票信息列表
export function listBilling(query) {
  return request({
    url: '/customer/billing/list',
    method: 'get',
    params: query
  })
}

export function allBilling(query) {
  return request({
    url: '/customer/billing/all',
    method: 'get',
    params: query
  })
}

// 查询客户开票信息详细
export function getBilling(id) {
  return request({
    url: '/customer/billing/' + id,
    method: 'get'
  })
}

// 新增客户开票信息
export function addBilling(data) {
  return request({
    url: '/customer/billing',
    method: 'post',
    data: data
  })
}

// 修改客户开票信息
export function updateBilling(data) {
  return request({
    url: '/customer/billing',
    method: 'put',
    data: data
  })
}

// 删除客户开票信息
export function delBilling(id) {
  return request({
    url: '/customer/billing/' + id,
    method: 'delete'
  })
}

// 导出客户开票信息
export function exportBilling(query) {
  return request({
    url: '/customer/billing/export',
    method: 'get',
    params: query
  })
}
