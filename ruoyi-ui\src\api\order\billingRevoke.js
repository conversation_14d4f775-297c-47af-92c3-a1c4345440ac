import request from '@/utils/request'

// 查询开票红字发票列表
export function listBillingRevoke(query) {
  return request({
    url: '/order/billingRevoke/list',
    method: 'get',
    params: query
  })
}

// 查询开票红字发票详细
export function getBillingRevoke(id) {
  return request({
    url: '/order/billingRevoke/' + id,
    method: 'get'
  })
}

// 新增开票红字发票
export function addBillingRevoke(data) {
  return request({
    url: '/order/billingRevoke',
    method: 'post',
    data: data
  })
}

// 修改开票红字发票
export function updateBillingRevoke(data) {
  return request({
    url: '/order/billingRevoke',
    method: 'put',
    data: data
  })
}

// 删除开票红字发票
export function delBillingRevoke(id) {
  return request({
    url: '/order/billingRevoke/' + id,
    method: 'delete'
  })
}

// 导出开票红字发票
export function exportBillingRevoke(query) {
  return request({
    url: '/order/billingRevoke/export',
    method: 'get',
    params: query
  })
}

export function finishBillingRevoke(data) {
  return request({
    url: '/order/billingRevoke/finishBillingRevoke',
    method: 'put',
    data: data
  })
}

export function billingRevoke(data) {
  return request({
    url: '/order/billingRevoke/revoke',
    method: 'put',
    data: data
  })
}


export function auditNotPass(data) {
  return request({
    url: '/order/billingRevoke/auditNotPass',
    method: 'put',
    data: data
  })
}
