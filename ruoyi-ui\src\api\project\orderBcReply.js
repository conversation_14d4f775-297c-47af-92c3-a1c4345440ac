import request from '@/utils/request'

export function listOrderBcReply(query) {
  return request({
    url: '/project/orderBcReply/list',
    method: 'get',
    params: query
  })
}

// 查询项目订单包材回复列表
export function allOrderBcReply(query) {
  return request({
    url: '/project/orderBcReply/all',
    method: 'get',
    params: query
  })
}

export function getOrderBcReply(id) {
  return request({
    url: '/project/orderBcReply/' + id,
    method: 'get'
  })
}
