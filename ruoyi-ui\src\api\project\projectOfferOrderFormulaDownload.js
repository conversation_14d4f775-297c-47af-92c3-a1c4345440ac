import request from '@/utils/request'

// 查询项目报价订单配方下载记录列表
export function listProjectOfferOrderFormulaDownload(query) {
  return request({
    url: '/project/projectOfferOrderFormulaDownload/list',
    method: 'get',
    params: query
  })
}

// 查询项目报价订单配方下载记录详细
export function getProjectOfferOrderFormulaDownload(id) {
  return request({
    url: '/project/projectOfferOrderFormulaDownload/' + id,
    method: 'get'
  })
}

// 新增项目报价订单配方下载记录
export function addProjectOfferOrderFormulaDownload(data) {
  return request({
    url: '/project/projectOfferOrderFormulaDownload',
    method: 'post',
    data: data
  })
}

// 修改项目报价订单配方下载记录
export function updateProjectOfferOrderFormulaDownload(data) {
  return request({
    url: '/project/projectOfferOrderFormulaDownload',
    method: 'put',
    data: data
  })
}

// 删除项目报价订单配方下载记录
export function delProjectOfferOrderFormulaDownload(id) {
  return request({
    url: '/project/projectOfferOrderFormulaDownload/' + id,
    method: 'delete'
  })
}

// 导出项目报价订单配方下载记录
export function exportProjectOfferOrderFormulaDownload(query) {
  return request({
    url: '/project/projectOfferOrderFormulaDownload/export',
    method: 'get',
    params: query
  })
}