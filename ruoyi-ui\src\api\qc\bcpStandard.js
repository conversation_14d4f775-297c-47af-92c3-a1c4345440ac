import request from '@/utils/request'

// 查询半成品检验标准列表
export function listBcpStandard(query) {
  return request({
    url: '/qc/bcpStandard/list',
    method: 'get',
    params: query
  })
}


// 查询半成品检验标准详细
export function getLabNoDatas(data) {
  return request({
    url: '/qc/bcpStandard/getLabNoDatas',
    method: 'post',
    data: data
  })
}
// 查询半成品检验标准详细
export function getLabNoDatasById(data) {
  return request({
    url: '/qc/bcpStandard/getLabNoDatasById',
    method: 'post',
    data: data
  })
}

// 查询半成品检验标准详细
export function getBcpStandard(id) {
  return request({
    url: '/qc/bcpStandard/' + id,
    method: 'get'
  })
}

// 新增半成品检验标准
export function addBcpStandard(data) {
  return request({
    url: '/qc/bcpStandard',
    method: 'post',
    data: data
  })
}

// 修改半成品检验标准
export function updateBcpStandard(data) {
  return request({
    url: '/qc/bcpStandard',
    method: 'put',
    data: data
  })
}

// 删除半成品检验标准
export function delBcpStandard(id) {
  return request({
    url: '/qc/bcpStandard/' + id,
    method: 'delete'
  })
}

// 导出半成品检验标准
export function exportBcpStandard(id) {
  return request({
    url: '/qc/bcpStandard/export/' + id,
    method: 'get',
  })
}

export function getBcpStandardByErpCode(code) {
  return request({
    url: '/qc/bcpStandard/getByErpCode/' + code,
    method: 'get'
  })
}

export function allBcpStandard(query) {
  return request({
    url: '/qc/bcpStandard/all',
    method: 'get',
    params: query
  })
}

export function exportAllBcpStandard(query) {
  return request({
    url: '/qc/bcpStandard/exportAll',
    method: 'get',
    params: query
  })
}

export function asyncBcpStand() {
  return request({
    url: '/qc/bcpStandard/asyncBcpStand',
    method: 'get'
  })
}

export function asyncBcpStandItem() {
  return request({
    url: '/qc/bcpStandard/asyncBcpStandItem',
    method: 'get'
  })
}

export function getBcpStandardByType(te004) {
  return request({
    url: '/qc/bcpStandard/standardByType/' + te004,
    method: 'get'
  })
}

export function exportBcpStandardNk(id) {
  return request({
    url: '/qc/bcpStandard/exportNk/' + id,
    method: 'get',
  })
}
