import request from '@/utils/request'

// 查询功效打卡方案列表
export function listGxCase(query) {
  return request({
    url: '/rd/gxCase/list',
    method: 'get',
    params: query
  })
}

// 查询功效打卡方案详细
export function getGxCase(id) {
  return request({
    url: '/rd/gxCase/' + id,
    method: 'get'
  })
}

// 新增功效打卡方案
export function addGxCase(data) {
  return request({
    url: '/rd/gxCase',
    method: 'post',
    data: data
  })
}

// 修改功效打卡方案
export function updateGxCase(data) {
  return request({
    url: '/rd/gxCase',
    method: 'put',
    data: data
  })
}

// 删除功效打卡方案
export function delGxCase(id) {
  return request({
    url: '/rd/gxCase/' + id,
    method: 'delete'
  })
}

// 导出功效打卡方案
export function exportGxCase(query) {
  return request({
    url: '/rd/gxCase/export',
    method: 'get',
    params: query
  })
}