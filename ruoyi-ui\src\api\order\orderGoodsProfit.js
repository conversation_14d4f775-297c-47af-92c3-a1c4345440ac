import request from '@/utils/request'

// 查询订单商品完结列表
export function listOrderGoodsProfit(query) {
  return request({
    url: '/order/orderGoodsProfit/list',
    method: 'get',
    params: query
  })
}

// 查询订单商品完结详细
export function getOrderGoodsProfit(id) {
  return request({
    url: '/order/orderGoodsProfit/' + id,
    method: 'get'
  })
}

// 新增订单商品完结
export function addOrderGoodsProfit(data) {
  return request({
    url: '/order/orderGoodsProfit',
    method: 'post',
    data: data
  })
}

// 修改订单商品完结
export function updateOrderGoodsProfit(data) {
  return request({
    url: '/order/orderGoodsProfit',
    method: 'put',
    data: data
  })
}

// 删除订单商品完结
export function delOrderGoodsProfit(id) {
  return request({
    url: '/order/orderGoodsProfit/' + id,
    method: 'delete'
  })
}

// 导出订单商品完结
export function exportOrderGoodsProfit(query) {
  return request({
    url: '/order/orderGoodsProfit/export',
    method: 'get',
    params: query
  })
}