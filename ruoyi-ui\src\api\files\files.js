import request from '@/utils/request'

// 查询共享文件列表
export function listFiles(query) {
  return request({
    url: '/files/files/list',
    method: 'get',
    params: query
  })
}

// 查询共享文件详细
export function getFiles(id) {
  return request({
    url: '/files/files/' + id,
    method: 'get'
  })
}

// 新增共享文件
export function addFiles(data) {
  return request({
    url: '/files/files',
    method: 'post',
    data: data
  })
}

// 修改共享文件
export function updateFiles(data) {
  return request({
    url: '/files/files',
    method: 'put',
    data: data
  })
}

// 删除共享文件
export function delFiles(id) {
  return request({
    url: '/files/files/' + id,
    method: 'delete'
  })
}

// 导出共享文件
export function exportFiles(query) {
  return request({
    url: '/files/files/export',
    method: 'get',
    params: query
  })
}