import request from '@/utils/request'

// 查询成品BOM标准列表
export function listFinishedGoodsBom(query) {
  return request({
    url: '/resource/finishedGoodsBom/list',
    method: 'get',
    params: query
  })
}

// 查询成品BOM标准详细
export function getFinishedGoodsBom(id) {
  return request({
    url: '/resource/finishedGoodsBom/' + id,
    method: 'get'
  })
}

// 新增成品BOM标准
export function addFinishedGoodsBom(data) {
  return request({
    url: '/resource/finishedGoodsBom',
    method: 'post',
    data: data
  })
}

// 修改成品BOM标准
export function updateFinishedGoodsBom(data) {
  return request({
    url: '/resource/finishedGoodsBom',
    method: 'put',
    data: data
  })
}

// 删除成品BOM标准
export function delFinishedGoodsBom(id) {
  return request({
    url: '/resource/finishedGoodsBom/' + id,
    method: 'delete'
  })
}

// 导出成品BOM标准
export function exportFinishedGoodsBom(query) {
  return request({
    url: '/resource/finishedGoodsBom/export',
    method: 'get',
    params: query
  })
}

export function enableFinishedGoodsBom(data) {
  return request({
    url: '/resource/finishedGoodsBom/enable',
    method: 'put',
    data: data
  })
}
