import request from '@/utils/request'

// 查询配制工艺列表
export function listMakeUp(query) {
  return request({
    url: '/mes/makeUp/list',
    method: 'get',
    params: query
  })
}

// 查询配制工艺详细
export function getMakeUp(id) {
  return request({
    url: '/mes/makeUp/' + id,
    method: 'get'
  })
}

// 新增配制工艺
export function addMakeUp(data) {
  return request({
    url: '/mes/makeUp',
    method: 'post',
    data: data
  })
}

// 修改配制工艺
export function updateMakeUp(data) {
  return request({
    url: '/mes/makeUp',
    method: 'put',
    data: data
  })
}

// 删除配制工艺
export function delMakeUp(id) {
  return request({
    url: '/mes/makeUp/' + id,
    method: 'delete'
  })
}

// 导出配制工艺
export function exportMakeUp(query) {
  return request({
    url: '/mes/makeUp/export',
    method: 'get',
    params: query
  })
}
