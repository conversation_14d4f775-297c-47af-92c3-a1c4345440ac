import request from '@/utils/request'

export function listMaterialPurchase(query) {
  return request({
    url: '/process/materialPurchaseUser/list',
    method: 'get',
    params: query
  })
}

// 查询物资设备采购申请列表
export function listMaterialPurchaseByApply(query) {
  return request({
    url: '/process/materialPurchaseUser/listByApply',
    method: 'get',
    params: query
  })
}
// 查询物资设备采购申请列表--费用
export function listMaterialPurchaseByFee(query) {
  return request({
    url: '/process/materialPurchaseUser/listByFee',
    method: 'get',
    params: query
  })
}

export function listMaterialPurchaseByPurchase(query) {
  return request({
    url: '/process/materialPurchaseUser/listByPurchase',
    method: 'get',
    params: query
  })
}

// 查询物资设备采购申请审批列表
export function listAuditMaterialPurchaseUser(query) {
  return request({
    url: '/process/materialPurchaseUser/audit',
    method: 'get',
    params: query
  })
}

// 查询请假申请详情
export function getMaterialPurchaseUser(id) {
  return request({
    url: '/process/materialPurchaseUser/' + id,
    method: 'get'
  })
}
export function chooseMaterialPurchaseUser(query) {
  return request({
    url: '/process/materialPurchaseUser/choose',
    method: 'get',
    params: query
  })
}

// 新增物资设备采购申请
export function addMaterialPurchaseUser(data) {
  return request({
    url: '/process/materialPurchaseUser',
    method: 'post',
    data: data
  })
}

export function purchaseMaterial(data) {
  return request({
    url: '/process/materialPurchaseUser/purchaseMaterial',
    method: 'put',
    data: data
  })
}

export function rkOffice(data) {
  return request({
    url: '/process/materialPurchaseUser/rkOffice',
    method: 'put',
    data: data
  })
}

// 物资设备采购审批
export function auditMaterialPurchaseUser(data) {
  return request({
    url: '/process/materialPurchaseUser/audit',
    method: 'post',
    data: data
  })
}

export function submitAudit(data) {
  return request({
    url: '/process/materialPurchaseUser/submitAudit',
    method: 'put',
    data: data
  })
}
export function cancelAudit(data) {
  return request({
    url: '/process/materialPurchaseUser/cancelAudit',
    method: 'put',
    data: data
  })
}

export function pigeonholeMaterialPurchaseUser(data) {
  return request({
    url: '/process/materialPurchaseUser/pigeonhole',
    method: 'post',
    data: data
  })
}

export function editPrintNumMaterialPurchaseUser(data) {
  return request({
    url: '/process/materialPurchaseUser/editPrintNum',
    method: 'post',
    data: data
  })
}

export function exportMaterialPurchaseUser(query) {
  return request({
    url: '/process/materialPurchaseUser/export',
    method: 'get',
    params: query
  })
}
