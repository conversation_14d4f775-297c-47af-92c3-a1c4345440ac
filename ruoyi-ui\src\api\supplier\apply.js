import request from '@/utils/request'

// 查询供应商准入申请列表
export function listApply(query) {
  return request({
    url: '/supplier/apply/list',
    method: 'get',
    params: query
  })
}

// 查询供应商准入申请详细
export function getApply(id) {
  return request({
    url: '/supplier/apply/' + id,
    method: 'get'
  })
}

// 新增供应商准入申请
export function addApply(data) {
  return request({
    url: '/supplier/apply',
    method: 'post',
    data: data
  })
}

// 修改供应商准入申请
export function updateApply(data) {
  return request({
    url: '/supplier/apply',
    method: 'put',
    data: data
  })
}

// 删除供应商准入申请
export function delApply(id) {
  return request({
    url: '/supplier/apply/' + id,
    method: 'delete'
  })
}

// 导出供应商准入申请
export function exportApply(query) {
  return request({
    url: '/supplier/apply/export',
    method: 'get',
    params: query
  })
}

export function submitAudit(data) {
  return request({
    url: '/supplier/apply/submitAudit',
    method: 'put',
    data: data
  })
}

//撤销申请
export function cancelAudit(data) {
  return request({
    url: '/supplier/apply/cancelAudit',
    method: 'put',
    data: data
  })
}

export function passListApply(query) {
  return request({
    url: '/supplier/apply/passList',
    method: 'get',
    params: query
  })
}
