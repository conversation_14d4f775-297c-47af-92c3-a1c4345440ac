import request from '@/utils/request'

// 查询产线盈亏-每日盈亏列表
export function listProductionDailySummary(query) {
  return request({
    url: '/production/productionDailySummary/list',
    method: 'get',
    params: query
  })
}

// 查询产线盈亏-每日盈亏详细
export function getProductionDailySummary(id) {
  return request({
    url: '/production/productionDailySummary/' + id,
    method: 'get'
  })
}

// 新增产线盈亏-每日盈亏
export function addProductionDailySummary(data) {
  return request({
    url: '/production/productionDailySummary',
    method: 'post',
    data: data
  })
}

// 修改产线盈亏-每日盈亏
export function updateProductionDailySummary(data) {
  return request({
    url: '/production/productionDailySummary',
    method: 'put',
    data: data
  })
}

// 删除产线盈亏-每日盈亏
export function delProductionDailySummary(id) {
  return request({
    url: '/production/productionDailySummary/' + id,
    method: 'delete'
  })
}

// 导出产线盈亏-每日盈亏
export function exportProductionDailySummary(query) {
  return request({
    url: '/production/productionDailySummary/export',
    method: 'get',
    params: query
  })
}