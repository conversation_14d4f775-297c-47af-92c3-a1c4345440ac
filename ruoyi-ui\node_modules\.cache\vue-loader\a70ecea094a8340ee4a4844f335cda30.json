{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\userTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\userTable.vue", "mtime": 1753954679644}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQge2FsbEF0dGVuZGFuY2VMb2d9IGZyb20gIkAvYXBpL2hyL2F0dGVuZGFuY2VMb2ciOw0KaW1wb3J0IE1lc0hvdXJzTGlzdCBmcm9tICJAL3ZpZXdzL3Byb2R1Y3Rpb24vbWVzSG91cnMvbGlzdC52dWUiOw0KaW1wb3J0IE1lc1Byb2R1Y3RQbGFuU2F2ZSBmcm9tICJAL3ZpZXdzL21lcy9wcm9kdWN0aW9uL3Byb2R1Y3Rpb24vc2F2ZS52dWUiOw0KaW1wb3J0IHtnZXRTY2hlZHVsZVBsYW5CeUNvZGV9IGZyb20gIkAvYXBpL3Byb2R1Y3Rpb24vc2NoZWR1bGVQbGFuIjsNCmltcG9ydCB7Z2V0RGlzcG9zaXRpb25QbGFufSBmcm9tICJAL2FwaS9wcm9kdWN0aW9uL2Rpc3Bvc2l0aW9uUGxhbiI7DQppbXBvcnQgTWVzVGltZUxpbmUgZnJvbSAiQC92aWV3cy9wcm9kdWN0aW9uL2RheUhvdXJzL21lc1RpbWVMaW5lLnZ1ZSI7DQppbXBvcnQge2FsbE1lc0xvdFdhaXRWbywgYWxsV2lwTG90TG9nfSBmcm9tICJAL2FwaS9tZXMvbWVzVmlldyI7DQppbXBvcnQge2RpZmZNaW51dGVzfSBmcm9tICJAL3V0aWxzL3Byb2R1Y3Rpb24vdGltZSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ2RheUhvdXJzVXNlclRhYmxlJywNCiAgY29tcG9uZW50czoge01lc1RpbWVMaW5lLCBNZXNQcm9kdWN0UGxhblNhdmUsIE1lc0hvdXJzTGlzdH0sDQogIHByb3BzOiB7DQogICAgdXNlckFycmF5OiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgIH0sDQogICAgbWVzSG91cnNMaXN0OiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgIH0sDQogICAgc3VtSG91cnM6IHsNCiAgICAgIHR5cGU6IE51bWJlciwNCiAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgIH0sDQogICAgZGF5SG91cnM6IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgIH0sDQogIH0sDQogIHdhdGNoOiB7DQogICAgdXNlckFycmF5OiB7DQogICAgICBhc3luYyBoYW5kbGVyKCkgew0KICAgICAgICBhd2FpdCB0aGlzLmZpbHRlclVzZXIoKQ0KICAgICAgfSwNCiAgICAgIGltbWVkaWF0ZTogdHJ1ZSwNCiAgICAgIGRlZXA6IHRydWUsDQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBidG5Mb2FkaW5nOiBmYWxzZSwNCiAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgdGl0bGU6ICcnLA0KICAgICAgZnVsbHNjcmVlbkZsYWc6IHRydWUsDQogICAgICBhdHRlbmRhbmNlTG9nT3BlbjogZmFsc2UsDQogICAgICBzZWxlY3RBdHRlbmRhbmNlTG9nT3BlbjogZmFsc2UsDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIHBsYW5PcGVuOiBmYWxzZSwNCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHVzZXJDb2RlOiBudWxsLA0KICAgICAgICBuaWNrTmFtZTogbnVsbCwNCiAgICAgIH0sDQogICAgICBzdGF0dXNPcHRpb25zOiBbXSwNCiAgICAgIGZpbHRlclVzZXJBcnJheTogW10sDQogICAgICBhdHRlbmRhbmNlTG9nTGlzdDogW10sDQogICAgICBjdXJyZW50Um93OiB7fSwNCiAgICAgIGxvZ09wZW46IGZhbHNlLA0KICAgICAgbWVzTG9nQXJyYXk6IFtdLA0KICAgICAgb3BUeXBlT3B0aW9uczogWw0KICAgICAgICB7bGFiZWw6ICfov5vnq5knLHZhbHVlOiAnQ0hFQ0tJTid9LA0KICAgICAgICB7bGFiZWw6ICfmmoLlgZwnLHZhbHVlOiAnV0FJVERJU1BPU0lUSU9OJ30sDQogICAgICAgIHtsYWJlbDogJ+ino+mZpOaaguWBnC3nu6fnu63nlJ/kuqcnLHZhbHVlOiAnUkVMRUFTRS1HTyd9LA0KICAgICAgICB7bGFiZWw6ICflh7rnq5knLHZhbHVlOiAnQ0hFQ0tPVVQnfSwNCiAgICAgICAge2xhYmVsOiAn6Kej6Zmk5pqC5YGcLee7k+adn+eUn+S6pycsdmFsdWU6ICdSRUxFQVNFLUludmVudG9yeSd9LA0KICAgICAgICB7bGFiZWw6ICforr7lpIflj5jmm7QnLHZhbHVlOiAnRVFQQ0hBTkdFJ30sDQogICAgICAgIHtsYWJlbDogJ+W8gOaJuScsdmFsdWU6ICdMT1RDUkVBVEUnfSwNCiAgICAgIF0sDQogICAgICBleGNlcHRpb25PcHRpb25zOiBbDQogICAgICAgIHtsYWJlbDogJ+S4iuW3peiAg+WLpOW8guW4uCcsdmFsdWU6IDF9LA0KICAgICAgICB7bGFiZWw6ICfkuIvlt6XogIPli6TlvILluLgnLHZhbHVlOiAyfSwNCiAgICAgICAge2xhYmVsOiAnbWVz5LiK5bel5byC5bi4Jyx2YWx1ZTogM30sDQogICAgICAgIHtsYWJlbDogJ21lc+S4i+W3peW8guW4uCcsdmFsdWU6IDR9LA0KICAgICAgICB7bGFiZWw6ICfovazlnLrlvILluLgnLHZhbHVlOiA1fSwNCiAgICAgICAge2xhYmVsOiAn5pyJ5pWI5bel5pe25byC5bi4Jyx2YWx1ZTogNn0sDQogICAgICAgIHtsYWJlbDogJ3NhcOS4iuW3peW8guW4uCcsdmFsdWU6IDd9LA0KICAgICAgICB7bGFiZWw6ICdzYXDkuIvlt6XlvILluLgnLHZhbHVlOiA4fSwNCiAgICAgIF0sDQogICAgICBjdXJyZW50VHlwZTogbnVsbCwNCiAgICB9DQogIH0sDQogIGFzeW5jIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXREaWN0cygicHJvZHVjdGlvbl9zdGF0dXMiKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgIHRoaXMuc3RhdHVzT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGENCiAgICB9KQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgc2FpbGluZ3NDaGFuZ2UodXNlckNvZGUpew0KICAgICAgdGhpcy4kZW1pdCgnc2FpbGluZ3NDaGFuZ2UnLHVzZXJDb2RlKQ0KICAgIH0sDQogICAgYXN5bmMgbWVzTG90TG9ncyhyb3cpIHsNCiAgICAgIHRoaXMubG9nT3BlbiA9IHRydWUNCiAgICAgIHRoaXMudGl0bGUgPSByb3cubG90Tm8gKyAi5YWz6IGU55qE55Sf5Lqn5om55pON5L2c6K6w5b2VIjsNCiAgICAgIGNvbnN0IHdhaXRMaXN0ID0gYXdhaXQgYWxsTWVzTG90V2FpdFZvKHtsb3RObzogcm93LmxvdE5vfSkNCiAgICAgIGNvbnN0IGxvdExvZ3MgPSBhd2FpdCBhbGxXaXBMb3RMb2coe2xvdE5vOiByb3cubG90Tm99KQ0KICAgICAgZm9yIChjb25zdCBsb3RMb2cgb2YgbG90TG9ncykgew0KICAgICAgICBpZihsb3RMb2cub3BUeXBlID09PSAnV0FJVERJU1BPU0lUSU9OJykgew0KICAgICAgICAgIGNvbnN0IGFyciA9IHdhaXRMaXN0LmZpbHRlcihpPT4gaS53YWl0RGF0ZSA9PT0gbG90TG9nLmNyZWF0ZVRpbWUpDQogICAgICAgICAgaWYoYXJyICYmIGFyclswXSkgew0KICAgICAgICAgICAgbG90TG9nLnJlYXNvbk5hbWUgPSBhcnJbMF0ucmVhc29uTmFtZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgdGhpcy5tZXNMb2dBcnJheSA9IGxvdExvZ3MNCiAgICB9LA0KICAgIGFzeW5jIHBsYW5WaWV3KHJvdykgew0KICAgICAgdGhpcy5jdXJyZW50Um93ID0gcm93DQogICAgICB0aGlzLnBsYW5PcGVuID0gdHJ1ZQ0KICAgICAgdGhpcy50aXRsZSA9ICforqHliJLor6bmg4UnDQogICAgICBhd2FpdCB0aGlzLiRuZXh0VGljaygpDQogICAgICBjb25zdCBtZXNQcm9kdWN0UGxhblNhdmUgPSB0aGlzLiRyZWZzLm1lc1Byb2R1Y3RQbGFuU2F2ZQ0KICAgICAgaWYobWVzUHJvZHVjdFBsYW5TYXZlKSB7DQogICAgICAgIG1lc1Byb2R1Y3RQbGFuU2F2ZS5yZXNldCgpDQogICAgICAgIGxldCByZXMNCiAgICAgICAgaWYocm93LnBsYW5UeXBlID09PSAncHJvZHVjdGlvbicpIHsNCiAgICAgICAgICByZXMgPSBhd2FpdCBnZXRTY2hlZHVsZVBsYW5CeUNvZGUocm93LnBsYW5Db2RlKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHJlcyA9IGF3YWl0IGdldERpc3Bvc2l0aW9uUGxhbihyb3cucGxhbkNvZGUpDQogICAgICAgIH0NCiAgICAgICAgYXdhaXQgbWVzUHJvZHVjdFBsYW5TYXZlLmluaXQocmVzLmRhdGEuaWQpDQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBtZXNMb2cocm93KSB7DQogICAgICB0aGlzLmN1cnJlbnRSb3cgPSByb3cNCiAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICAgIHRoaXMudGl0bGUgPSAnbWVz5bel5pe25piO57uGJw0KICAgICAgYXdhaXQgdGhpcy4kbmV4dFRpY2soKQ0KICAgICAgYXdhaXQgdGhpcy4kcmVmcy5tZXNIb3Vyc0xpc3QuZ2V0TGlzdCgpDQogICAgfSwNCiAgICBhc3luYyBzZWxlY3RVc2VyVGltZSh0aW1lKSB7DQogICAgICB0aGlzLmN1cnJlbnRSb3dbdGhpcy5jdXJyZW50VHlwZSArICdUaW1lJ10gPSB0aW1lDQogICAgICB0aGlzLmN1cnJlbnRSb3cuYXR0ZW5kYW5jZU1pbnV0ZXMgPSBkaWZmTWludXRlcyh0aGlzLmN1cnJlbnRSb3cuYXR0ZW5kYW5jZUVuZFRpbWUsdGhpcy5jdXJyZW50Um93LmF0dGVuZGFuY2VTdGFydFRpbWUpDQogICAgICB0aGlzLmN1cnJlbnRSb3cuYXR0ZW5kYW5jZUFycmF5ID0gW3tzdGFydFRpbWU6IHRoaXMuY3VycmVudFJvdy5hdHRlbmRhbmNlU3RhcnRUaW1lLGVuZFRpbWU6IHRoaXMuY3VycmVudFJvdy5hdHRlbmRhbmNlRW5kVGltZX1dDQogICAgICB0aGlzLnNlbGVjdEF0dGVuZGFuY2VMb2dPcGVuID0gZmFsc2UNCiAgICB9LA0KICAgIGFzeW5jIHNlbGVjdEF0dGVuZGFuY2VMb2codXNlcix0eXBlKSB7DQogICAgICBjb25zdCB3b3JrRGF0ZSA9IHRoaXMuZGF5SG91cnMud29ya0RhdGUNCiAgICAgIGlmKHdvcmtEYXRlKSB7DQogICAgICAgIGNvbnN0IHNlYXJjaERhdGVBcnJheSA9IFt3b3JrRGF0ZSxdDQogICAgICAgIHNlYXJjaERhdGVBcnJheS5wdXNoKHRoaXMubW9tZW50KHdvcmtEYXRlKS5hZGQoMSwgJ2RheXMnKS5mb3JtYXQoJ1lZWVktTU0tREQnKSkNCiAgICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICAgIHVzZXJJZDogdXNlci51c2VySWQsDQogICAgICAgICAgc2VhcmNoRGF0ZUFycmF5DQogICAgICAgIH0NCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlDQogICAgICAgICAgY29uc3QgYXR0ZW5kYW5jZUxvZ0xpc3QgPSBhd2FpdCBhbGxBdHRlbmRhbmNlTG9nKHBhcmFtcykNCiAgICAgICAgICBmb3IgKGNvbnN0IGl0ZW0gb2YgYXR0ZW5kYW5jZUxvZ0xpc3QpIHsNCiAgICAgICAgICAgIGlmKGl0ZW0udXNlckNoZWNrVGltZSkgew0KICAgICAgICAgICAgICBpdGVtLnVzZXJDaGVja1RpbWUgPSB0aGlzLm1vbWVudChpdGVtLnVzZXJDaGVja1RpbWUpLmZvcm1hdCgnWVlZWS1NTS1ERCBISDptbTpzcycpDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuYXR0ZW5kYW5jZUxvZ0xpc3QgPSBhdHRlbmRhbmNlTG9nTGlzdA0KICAgICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlDQogICAgICAgICAgdGhpcy5jdXJyZW50Um93ID0gdXNlcg0KICAgICAgICAgIHRoaXMuY3VycmVudFR5cGUgPSB0eXBlDQogICAgICAgICAgdGhpcy5zZWxlY3RBdHRlbmRhbmNlTG9nT3BlbiA9IHRydWUNCiAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIGF0dGVuZGFuY2VMb2codXNlcklkKSB7DQogICAgICBjb25zdCB3b3JrRGF0ZSA9IHRoaXMuZGF5SG91cnMud29ya0RhdGUNCiAgICAgIGlmKHdvcmtEYXRlKSB7DQogICAgICAgIGNvbnN0IHNlYXJjaERhdGVBcnJheSA9IFt3b3JrRGF0ZSxdDQogICAgICAgIHNlYXJjaERhdGVBcnJheS5wdXNoKHRoaXMubW9tZW50KHdvcmtEYXRlKS5hZGQoMSwgJ2RheXMnKS5mb3JtYXQoJ1lZWVktTU0tREQnKSkNCiAgICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICAgIHVzZXJJZCwNCiAgICAgICAgICBzZWFyY2hEYXRlQXJyYXkNCiAgICAgICAgfQ0KICAgICAgICB0cnkgew0KICAgICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IHRydWUNCiAgICAgICAgICBjb25zdCBhdHRlbmRhbmNlTG9nTGlzdCA9IGF3YWl0IGFsbEF0dGVuZGFuY2VMb2cocGFyYW1zKQ0KICAgICAgICAgIGZvciAoY29uc3QgaXRlbSBvZiBhdHRlbmRhbmNlTG9nTGlzdCkgew0KICAgICAgICAgICAgaWYoaXRlbS51c2VyQ2hlY2tUaW1lKSB7DQogICAgICAgICAgICAgIGl0ZW0udXNlckNoZWNrVGltZSA9IHRoaXMubW9tZW50KGl0ZW0udXNlckNoZWNrVGltZSkuZm9ybWF0KCdZWVlZLU1NLUREIEhIOm1tOnNzJykNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5hdHRlbmRhbmNlTG9nTGlzdCA9IGF0dGVuZGFuY2VMb2dMaXN0DQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICB0aGlzLmF0dGVuZGFuY2VMb2dPcGVuID0gdHJ1ZQ0KICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuZmlsdGVyVXNlcigpDQogICAgfSwNCiAgICBmaWx0ZXJVc2VyKCkgew0KICAgICAgbGV0IGZpbHRlclVzZXJBcnJheSA9IHRoaXMudXNlckFycmF5DQogICAgICBjb25zdCBxdWVyeVBhcmFtcyA9IHRoaXMucXVlcnlQYXJhbXMNCiAgICAgIGlmIChxdWVyeVBhcmFtcy51c2VyQ29kZSkgew0KICAgICAgICBmaWx0ZXJVc2VyQXJyYXkgPSBmaWx0ZXJVc2VyQXJyYXkuZmlsdGVyKGkgPT4gaS51c2VyQ29kZSA9PT0gcXVlcnlQYXJhbXMudXNlckNvZGUpDQogICAgICB9DQogICAgICBpZiAocXVlcnlQYXJhbXMubmlja05hbWUpIHsNCiAgICAgICAgZmlsdGVyVXNlckFycmF5ID0gZmlsdGVyVXNlckFycmF5LmZpbHRlcihpID0+IGkubmlja05hbWUgPT09IHF1ZXJ5UGFyYW1zLm5pY2tOYW1lKQ0KICAgICAgfQ0KICAgICAgdGhpcy5maWx0ZXJVc2VyQXJyYXkgPSBmaWx0ZXJVc2VyQXJyYXkNCiAgICB9LA0KICB9LA0KfQ0K"}, {"version": 3, "sources": ["userTable.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "userTable.vue", "sourceRoot": "src/views/production/dayHours", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-divider content-position=\"left\" >产线</el-divider>\r\n\r\n    <el-form ref=\"queryForm\" :model=\"queryParams\" label-width=\"80px\" size=\"mini\" >\r\n      <el-row>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"工号\" prop=\"userCode\">\r\n            <el-input v-model=\"queryParams.userCode\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"姓名\" prop=\"nickName\">\r\n            <el-input v-model=\"queryParams.nickName\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"filterUser\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <div class=\"table-wrapper\">\r\n      <table class=\"base-table small-table\" >\r\n        <colgroup>\r\n          <col style=\"width: 50px\" /><!-- 序号 -->\r\n          <col style=\"width: 100px\" /><!-- 员工姓名 -->\r\n          <col style=\"width: 120px\" /><!-- 工号 -->\r\n          <col style=\"width: 100px\" /><!-- 类型 -->\r\n          <col style=\"width: 160px\" /><!-- 开始时间 -->\r\n          <col style=\"width: 160px\" /><!-- 结束时间 -->\r\n          <col style=\"width: 100px\" /><!-- 时长 -->\r\n          <col style=\"width: 100px\" /><!-- 异常状态 -->\r\n          <col style=\"width: 120px\" /><!-- 设备编号 -->\r\n          <col style=\"width: 160px\" /><!-- 批次号 -->\r\n          <col style=\"width: 120px\" /><!-- 工作日期 -->\r\n          <col style=\"width: 160px\" /><!-- 进站时间 -->\r\n          <col style=\"width: 160px\" /><!-- 出站时间 -->\r\n          <col style=\"width: 100px\" /><!-- 工时 -->\r\n          <col style=\"width: 100px\" /><!-- sap工时 -->\r\n          <col style=\"width: 100px\" /><!-- 休息工时 -->\r\n          <col style=\"width: 500px\" /><!-- 有效时段 -->\r\n          <col style=\"width: 100px\" /><!-- 有效工时 -->\r\n          <col style=\"width: 100px\" /><!-- 无效工时 -->\r\n          <col style=\"width: 100px\" /><!-- 工资工时 -->\r\n          <col style=\"width: 100px\" /><!-- 修正工时 -->\r\n          <col style=\"width: 120px\" /><!-- 异常状态 -->\r\n          <col style=\"width: 240px\" /><!-- 备注 -->\r\n        </colgroup>\r\n        <thead>\r\n          <tr >\r\n            <th :rowspan=\"2\" >序号</th>\r\n            <th :rowspan=\"2\" >员工姓名</th>\r\n            <th :rowspan=\"2\" >工号</th>\r\n            <th :colspan=\"5\" >工时对比</th>\r\n            <th :colspan=\"7\" >sap根据mes设备进出站记录拆解后的记录</th>\r\n            <th :rowspan=\"2\" >休息工时\r\n              <el-tooltip content=\"工厂标准休息时间与mes时间的交集半点向下取整\" >\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th :rowspan=\"2\" >\r\n              有效时段\r\n            </th>\r\n            <th :rowspan=\"2\" >\r\n              有效工时\r\n              <el-tooltip content=\"sap工时\" >\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th :rowspan=\"2\" >\r\n              无效工时\r\n              <el-tooltip content=\"工资工时-有效工时\" >\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th :rowspan=\"2\" >\r\n              工资工时\r\n              <el-tooltip >\r\n                <div slot=\"content\">\r\n                  mes时长半点向下取整\r\n                </div>\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th :rowspan=\"2\" >\r\n              修正工时\r\n              <el-tooltip >\r\n                <div slot=\"content\">\r\n                  修正过后以修正的工时为准\r\n                </div>\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th :rowspan=\"2\" >\r\n              异常状态\r\n              <el-tooltip >\r\n                <div slot=\"content\">\r\n                  <div>1.上工时间早于考勤打卡时间:上工考勤异常(代上工风险)</div>\r\n                  <div>2.下工时间晚于考勤打卡时间:下工考勤异常(代下工风险)</div>\r\n                  <div>3.下工 20分钟后未打卡:下班考勤异常</div>\r\n                  <div>4.末道产线记录出站,15分钟内员工未下工:下工异常</div>\r\n                  <div>5.上工后半小时没有匹配上产线:上工异常</div>\r\n                  <div>6.转场超过半小时:转场异常 (扣除标准休息时间)</div>\r\n                  <div>7.有效工时低于工资工时 85%,有效工时异常.</div>\r\n                  <div>8.末道产线记录出站后,超出 15 分钟后才下工,或没有下工数据的,且考勤时间晚于末道产线记录 20 分钟之后的,工资工时以末道产线记录出站/暂停时间为准.</div>\r\n                  <div>9.没有上下班考勤记录的,工资工时统一记录为 0,需人工核实,修改工资工时需审批</div>\r\n                </div>\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th :rowspan=\"2\" >备注</th>\r\n          </tr>\r\n          <tr>\r\n            <th >类型</th>\r\n            <th >开始时间</th>\r\n            <th >结束时间</th>\r\n            <th >时长</th>\r\n            <th >\r\n              异常状态\r\n              <el-tooltip >\r\n                <div slot=\"content\">\r\n                  <div>1.上工时间早于考勤打卡时间:上工考勤异常(代上工风险)</div>\r\n                  <div>2.下工时间晚于考勤打卡时间:下工考勤异常(代下工风险)</div>\r\n                  <div>3.下工 20分钟后未打卡:下班考勤异常</div>\r\n                  <div>4.末道产线记录出站,15分钟内员工未下工:下工异常</div>\r\n                  <div>5.上工后半小时没有匹配上产线:上工异常</div>\r\n                  <div>6.转场超过半小时:转场异常 (扣除标准休息时间)</div>\r\n                  <div>7.有效工时低于工资工时 85%,有效工时异常.</div>\r\n                  <div>8.末道产线记录出站后,超出 15 分钟后才下工,或没有下工数据的,且考勤时间晚于末道产线记录 20 分钟之后的,工资工时以末道产线记录出站/暂停时间为准.</div>\r\n                  <div>9.没有上下班考勤记录的,工资工时统一记录为 0,需人工核实,修改工资工时需审批</div>\r\n                </div>\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th >设备编号</th>\r\n            <th >批次号</th>\r\n            <th >工作日期</th>\r\n            <th >进站时间</th>\r\n            <th >出站时间</th>\r\n            <th >工时</th>\r\n            <th >sap工时</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr v-for=\"(u,i) in filterUserArray\" :key=\"u.userId\">\r\n            <td >{{i+1}}</td>\r\n            <td >{{u.nickName}}</td>\r\n            <td >{{u.userCode}}</td>\r\n            <td :colspan=\"5\" style=\"width: 620px;padding: 0\" >\r\n              <table class=\"base-table small-table\" >\r\n                <tr>\r\n                  <th style=\"width: 100px\" >考勤</th>\r\n                  <td style=\"width: 160px\" >\r\n                    <span style=\"color: #1c84c6;cursor: pointer\" @click=\"selectAttendanceLog(u,'attendanceStart')\">\r\n                      {{u.attendanceStartTime}}\r\n                    </span>\r\n                  </td>\r\n                  <td style=\"width: 160px\" >\r\n                    <span style=\"color: #1c84c6;cursor: pointer\" @click=\"selectAttendanceLog(u,'attendanceEnd')\">\r\n                      {{u.attendanceEndTime}}\r\n                    </span>\r\n                  </td>\r\n                  <td style=\"width: 100px\" >\r\n                    <span style=\"color: #1c84c6;cursor: pointer\" @click=\"attendanceLog(u.userId)\">\r\n                      {{minutesToHours(u.attendanceMinutes).toFixed(2)}}\r\n                    </span>\r\n                  </td>\r\n                  <td :rowspan=\"3\" style=\"width: 100px\" >\r\n                    <div v-for=\"e in u.exceptionArray\" :key=\"e\" style=\"color: #F56C6C\" >\r\n                      {{selectOptionsLabel(exceptionOptions,e)}}\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n                <tr>\r\n                  <th style=\"width: 100px\" >mes</th>\r\n                  <td style=\"width: 160px\" >{{u.mesMinTime}}</td>\r\n                  <td style=\"width: 160px\" >{{u.mesMaxTime}}</td>\r\n                  <td style=\"width: 100px\" >\r\n                    <span style=\"color: #1c84c6;cursor: pointer\" @click=\"mesLog(u)\">\r\n                      {{minutesToHours(u.mesMinutes).toFixed(2)}}\r\n                    </span>\r\n                  </td>\r\n                </tr>\r\n                <tr>\r\n                  <th style=\"width: 100px\" >sap</th>\r\n                  <td style=\"width: 160px\" >{{u.sapMinTime}}</td>\r\n                  <td style=\"width: 160px\" >{{u.sapMaxTime}}</td>\r\n                  <td style=\"width: 100px\" >{{minutesToHours(u.sapMinutes).toFixed(2)}}</td>\r\n                </tr>\r\n              </table>\r\n            </td>\r\n            <td :colspan=\"6\" style=\"width: 820px;padding: 0\" >\r\n              <table class=\"base-table small-table\" >\r\n                <tr v-for=\"h in u.sapArray\" :key=\"h.id\">\r\n                  <td style=\"width: 120px\" >{{h.equipmentNo}}</td>\r\n                  <td style=\"width: 160px\" >\r\n                    <span style=\"color: #1c84c6;cursor: pointer\" @click=\"mesLotLogs(h)\">\r\n                      {{h.lotNo}}\r\n                    </span>\r\n                  </td>\r\n                  <td style=\"width: 120px\" >{{h.workDate}}</td>\r\n                  <td style=\"width: 160px\" >{{h.startTime}}</td>\r\n                  <td style=\"width: 160px\" >{{h.endTime}}</td>\r\n                  <td style=\"width: 100px\" >{{minutesToHours(h.minutes).toFixed(2)}}</td>\r\n                </tr>\r\n              </table>\r\n            </td>\r\n            <td >{{minutesToHours(u.sapSumMinutes).toFixed(2)}}</td>\r\n            <td >{{minutesToHours(u.restMinutes).toFixed(2)}}</td>\r\n            <td >\r\n              <MesTimeLine\r\n                :time-array=\"u.timeArray\"\r\n                :attendance-array=\"u.attendanceArray\"\r\n                :mes-array=\"u.mesArray\"\r\n                :sap-array=\"u.sapArray\"\r\n              />\r\n            </td>\r\n            <td >{{minutesToHours(u.effectiveMinutes).toFixed(2)}}</td>\r\n            <td >{{minutesToHours(u.invalidMinutes).toFixed(2)}}</td>\r\n            <td >\r\n              {{minutesToHours(u.wagesMinutes).toFixed(2)}}\r\n            </td>\r\n            <td >\r\n              <!-- v-if=\"u.exceptionArray && u.exceptionArray.length\" -->\r\n              <el-input v-model=\"u.finalMinutes\" autosize size=\"mini\" @input=\"$emit('computeItemData')\" />\r\n            </td>\r\n            <td >\r\n              <div v-for=\"e in u.exceptionArray\" :key=\"e\" style=\"color: #F56C6C\" >\r\n                {{selectOptionsLabel(exceptionOptions,e)}}\r\n              </div>\r\n            </td>\r\n            <td >\r\n              <el-input v-model=\"u.remark\" autosize size=\"mini\" />\r\n            </td>\r\n          </tr>\r\n          <tr>\r\n            <th>合计</th>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td>{{sumHours}}</td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <el-dialog :close-on-click-modal=\"false\" :visible.sync=\"selectAttendanceLogOpen\" append-to-body width=\"600px\">\r\n      <table class=\"base-table small-table\">\r\n        <tr>\r\n          <th style=\"width: 320px\">打卡地址</th>\r\n          <th style=\"width: 180px\">打卡时间</th>\r\n        </tr>\r\n        <tr v-for=\"item in attendanceLogList\" :key=\"item.id\" >\r\n          <td>{{item.userAddress}}</td>\r\n          <td>\r\n            <span style=\"color: #00afff;cursor: pointer\" @click=\"selectUserTime(item.userCheckTime)\" >\r\n              {{item.userCheckTime}}\r\n            </span>\r\n          </td>\r\n        </tr>\r\n      </table>\r\n    </el-dialog>\r\n\r\n    <el-dialog :close-on-click-modal=\"false\" :visible.sync=\"attendanceLogOpen\" append-to-body width=\"600px\">\r\n      <table class=\"base-table small-table\">\r\n        <tr>\r\n          <th style=\"width: 320px\">打卡地址</th>\r\n          <th style=\"width: 180px\">打卡时间</th>\r\n        </tr>\r\n        <tr v-for=\"item in attendanceLogList\" :key=\"item.id\" >\r\n          <td>{{item.userAddress}}</td>\r\n          <td>{{item.userCheckTime}}</td>\r\n        </tr>\r\n      </table>\r\n    </el-dialog>\r\n\r\n    <el-dialog :fullscreen=\"fullscreenFlag\" :visible.sync=\"open\" width=\"1200px\" :close-on-click-modal=\"false\" append-to-body>\r\n      <div class=\"dialog-title\" slot=\"title\">{{ title }}\r\n        <el-button @click=\"fullscreenFlag = !fullscreenFlag\" type=\"text\"\r\n                   :icon=\"fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'\"/>\r\n      </div>\r\n      <MesHoursList ref=\"mesHoursList\" :work-date=\"dayHours.workDate\" :user-code=\"currentRow.userCode\" @sailingsChange=\"sailingsChange\" />\r\n    </el-dialog>\r\n\r\n    <el-dialog :title=\"title\" :fullscreen=\"fullscreenFlag\" :visible.sync=\"planOpen\" width=\"1200px\" :close-on-click-modal=\"false\"  append-to-body>\r\n      <div class=\"dialog-title\" slot=\"title\">\r\n        {{title}}\r\n        <el-button @click=\"fullscreenFlag = !fullscreenFlag\" type=\"text\"\r\n                   :icon=\"fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'\" />\r\n      </div>\r\n      <MesProductPlanSave ref=\"mesProductPlanSave\" :readonly=\"true\" :plan-type=\"currentRow.planType\" />\r\n    </el-dialog>\r\n\r\n    <el-dialog :fullscreen=\"fullscreenFlag\" :visible.sync=\"logOpen\" width=\"1200px\" :close-on-click-modal=\"false\"\r\n               append-to-body>\r\n      <div class=\"dialog-title\" slot=\"title\">{{ title }}\r\n        <el-button @click=\"fullscreenFlag = !fullscreenFlag\" type=\"text\"\r\n                   :icon=\"fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'\"/>\r\n      </div>\r\n      <div class=\"table-wrapper\">\r\n        <table class=\"base-table small-table\" >\r\n          <tr>\r\n            <th style=\"width: 80px\" >作业站</th>\r\n            <th style=\"width: 120px\" >类型</th>\r\n            <th style=\"width: 120px\" >时间</th>\r\n            <th style=\"width: 180px\" >暂停原因</th>\r\n            <th style=\"width: 100px\" >数量</th>\r\n            <th style=\"width: 120px\" >人员编号</th>\r\n            <th style=\"width: 80px\" >人员名称</th>\r\n          </tr>\r\n          <tr v-for=\"item in mesLogArray\" :key=\"item.lotNo\" >\r\n            <td style=\"width: 80px\" >{{item.opNo}}</td>\r\n            <td style=\"width: 120px\" >{{ selectOptionsLabel(opTypeOptions, item.opType)}}</td>\r\n            <td style=\"width: 120px\" >{{item.createTime}}</td>\r\n            <td style=\"width: 180px\" >{{item.reasonName}}</td>\r\n            <td style=\"width: 100px\" >{{item.qty}}</td>\r\n            <td style=\"width: 120px\" >{{item.userNo}}</td>\r\n            <td style=\"width: 80px\" >{{item.userName}}</td>\r\n          </tr>\r\n        </table>\r\n      </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n<script >\r\nimport {allAttendanceLog} from \"@/api/hr/attendanceLog\";\r\nimport MesHoursList from \"@/views/production/mesHours/list.vue\";\r\nimport MesProductPlanSave from \"@/views/mes/production/production/save.vue\";\r\nimport {getSchedulePlanByCode} from \"@/api/production/schedulePlan\";\r\nimport {getDispositionPlan} from \"@/api/production/dispositionPlan\";\r\nimport MesTimeLine from \"@/views/production/dayHours/mesTimeLine.vue\";\r\nimport {allMesLotWaitVo, allWipLotLog} from \"@/api/mes/mesView\";\r\nimport {diffMinutes} from \"@/utils/production/time\";\r\n\r\nexport default {\r\n  name: 'dayHoursUserTable',\r\n  components: {MesTimeLine, MesProductPlanSave, MesHoursList},\r\n  props: {\r\n    userArray: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    mesHoursList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    sumHours: {\r\n      type: Number,\r\n      required: true,\r\n    },\r\n    dayHours: {\r\n      type: Object,\r\n      required: true,\r\n    },\r\n  },\r\n  watch: {\r\n    userArray: {\r\n      async handler() {\r\n        await this.filterUser()\r\n      },\r\n      immediate: true,\r\n      deep: true,\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      loading: false,\r\n      title: '',\r\n      fullscreenFlag: true,\r\n      attendanceLogOpen: false,\r\n      selectAttendanceLogOpen: false,\r\n      open: false,\r\n      planOpen: false,\r\n      queryParams: {\r\n        userCode: null,\r\n        nickName: null,\r\n      },\r\n      statusOptions: [],\r\n      filterUserArray: [],\r\n      attendanceLogList: [],\r\n      currentRow: {},\r\n      logOpen: false,\r\n      mesLogArray: [],\r\n      opTypeOptions: [\r\n        {label: '进站',value: 'CHECKIN'},\r\n        {label: '暂停',value: 'WAITDISPOSITION'},\r\n        {label: '解除暂停-继续生产',value: 'RELEASE-GO'},\r\n        {label: '出站',value: 'CHECKOUT'},\r\n        {label: '解除暂停-结束生产',value: 'RELEASE-Inventory'},\r\n        {label: '设备变更',value: 'EQPCHANGE'},\r\n        {label: '开批',value: 'LOTCREATE'},\r\n      ],\r\n      exceptionOptions: [\r\n        {label: '上工考勤异常',value: 1},\r\n        {label: '下工考勤异常',value: 2},\r\n        {label: 'mes上工异常',value: 3},\r\n        {label: 'mes下工异常',value: 4},\r\n        {label: '转场异常',value: 5},\r\n        {label: '有效工时异常',value: 6},\r\n        {label: 'sap上工异常',value: 7},\r\n        {label: 'sap下工异常',value: 8},\r\n      ],\r\n      currentType: null,\r\n    }\r\n  },\r\n  async created() {\r\n    this.getDicts(\"production_status\").then(response => {\r\n      this.statusOptions = response.data\r\n    })\r\n  },\r\n  methods: {\r\n    sailingsChange(userCode){\r\n      this.$emit('sailingsChange',userCode)\r\n    },\r\n    async mesLotLogs(row) {\r\n      this.logOpen = true\r\n      this.title = row.lotNo + \"关联的生产批操作记录\";\r\n      const waitList = await allMesLotWaitVo({lotNo: row.lotNo})\r\n      const lotLogs = await allWipLotLog({lotNo: row.lotNo})\r\n      for (const lotLog of lotLogs) {\r\n        if(lotLog.opType === 'WAITDISPOSITION') {\r\n          const arr = waitList.filter(i=> i.waitDate === lotLog.createTime)\r\n          if(arr && arr[0]) {\r\n            lotLog.reasonName = arr[0].reasonName\r\n          }\r\n        }\r\n      }\r\n      this.mesLogArray = lotLogs\r\n    },\r\n    async planView(row) {\r\n      this.currentRow = row\r\n      this.planOpen = true\r\n      this.title = '计划详情'\r\n      await this.$nextTick()\r\n      const mesProductPlanSave = this.$refs.mesProductPlanSave\r\n      if(mesProductPlanSave) {\r\n        mesProductPlanSave.reset()\r\n        let res\r\n        if(row.planType === 'production') {\r\n          res = await getSchedulePlanByCode(row.planCode)\r\n        } else {\r\n          res = await getDispositionPlan(row.planCode)\r\n        }\r\n        await mesProductPlanSave.init(res.data.id)\r\n      }\r\n    },\r\n    async mesLog(row) {\r\n      this.currentRow = row\r\n      this.open = true\r\n      this.title = 'mes工时明细'\r\n      await this.$nextTick()\r\n      await this.$refs.mesHoursList.getList()\r\n    },\r\n    async selectUserTime(time) {\r\n      this.currentRow[this.currentType + 'Time'] = time\r\n      this.currentRow.attendanceMinutes = diffMinutes(this.currentRow.attendanceEndTime,this.currentRow.attendanceStartTime)\r\n      this.currentRow.attendanceArray = [{startTime: this.currentRow.attendanceStartTime,endTime: this.currentRow.attendanceEndTime}]\r\n      this.selectAttendanceLogOpen = false\r\n    },\r\n    async selectAttendanceLog(user,type) {\r\n      const workDate = this.dayHours.workDate\r\n      if(workDate) {\r\n        const searchDateArray = [workDate,]\r\n        searchDateArray.push(this.moment(workDate).add(1, 'days').format('YYYY-MM-DD'))\r\n        const params = {\r\n          userId: user.userId,\r\n          searchDateArray\r\n        }\r\n        try {\r\n          this.btnLoading = true\r\n          const attendanceLogList = await allAttendanceLog(params)\r\n          for (const item of attendanceLogList) {\r\n            if(item.userCheckTime) {\r\n              item.userCheckTime = this.moment(item.userCheckTime).format('YYYY-MM-DD HH:mm:ss')\r\n            }\r\n          }\r\n          this.attendanceLogList = attendanceLogList\r\n          this.btnLoading = false\r\n          this.currentRow = user\r\n          this.currentType = type\r\n          this.selectAttendanceLogOpen = true\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n    async attendanceLog(userId) {\r\n      const workDate = this.dayHours.workDate\r\n      if(workDate) {\r\n        const searchDateArray = [workDate,]\r\n        searchDateArray.push(this.moment(workDate).add(1, 'days').format('YYYY-MM-DD'))\r\n        const params = {\r\n          userId,\r\n          searchDateArray\r\n        }\r\n        try {\r\n          this.btnLoading = true\r\n          const attendanceLogList = await allAttendanceLog(params)\r\n          for (const item of attendanceLogList) {\r\n            if(item.userCheckTime) {\r\n              item.userCheckTime = this.moment(item.userCheckTime).format('YYYY-MM-DD HH:mm:ss')\r\n            }\r\n          }\r\n          this.attendanceLogList = attendanceLogList\r\n          this.btnLoading = false\r\n          this.attendanceLogOpen = true\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.filterUser()\r\n    },\r\n    filterUser() {\r\n      let filterUserArray = this.userArray\r\n      const queryParams = this.queryParams\r\n      if (queryParams.userCode) {\r\n        filterUserArray = filterUserArray.filter(i => i.userCode === queryParams.userCode)\r\n      }\r\n      if (queryParams.nickName) {\r\n        filterUserArray = filterUserArray.filter(i => i.nickName === queryParams.nickName)\r\n      }\r\n      this.filterUserArray = filterUserArray\r\n    },\r\n  },\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.table-wrapper {\r\n  max-height: 80vh;\r\n\r\n  .base-table {\r\n\r\n    thead {\r\n      position: sticky;\r\n      top: 0;\r\n      z-index: 3;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}