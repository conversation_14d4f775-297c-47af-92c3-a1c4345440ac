import request from '@/utils/request'

// 查询研发配方列表
export function listSoftwareDevelopingFormula(query) {
  return request({
    url: '/software/softwareDevelopingFormula/list',
    method: 'get',
    params: query
  })
}

// 查询研发配方列表
export function listSoftwareDevelopingMatrerialFormula(query) {
  return request({
    url: '/software/softwareDevelopingFormula/materialFormulaList',
    method: 'get',
    params: query
  })
}

// 查询研发配方 称量 列表
export function weightListSoftwareDevelopingFormula(query) {
  return request({
    url: '/software/softwareDevelopingFormula/weightList',
    method: 'get',
    params: query
  })
}
// 查询研发配方 价格  列表
export function priceListSoftwareDevelopingFormula(query) {
  return request({
    url: '/software/softwareDevelopingFormula/priceList',
    method: 'get',
    params: query
  })
}

// 查询研发配方 指定原料列表信息
export function queryFormulaAppointMaterialDataList(query) {
  return request({
    url: '/software/softwareDevelopingFormula/queryFormulaAppointMaterialDataList',
    method: 'get',
    params: query
  })
}

// 查询研发配方详细
export function getSoftwareDevelopingFormula(id) {
  return request({
    url: '/software/softwareDevelopingFormula/' + id,
    method: 'get'
  })
}

// 查询研发配方详细
export function getSoftwareDevelopingFormulaDetail(id) {
  return request({
    url: '/software/softwareDevelopingFormula/detail/' + id,
    method: 'get'
  })
}

// 查询研发配方详细
export function getSoftwareDevelopingFormulaSimple(id) {
  return request({
    url: '/software/softwareDevelopingFormula/simple/' + id,
    method: 'get'
  })
}


// 查询研发配方详细  安评数据
export function getSoftwareDevelopingFormulaProductSafetyAssessmentData(query) {
  return request({
    url: '/software/softwareDevelopingFormula/getSoftwareDevelopingFormulaProductSafetyAssessmentData',
    method: 'get',
    params: query
  })
}


//查询原料可查看tab数据
export function queryLookFormulaTabs(query) {
  return request({
    url: '/software/softwareDevelopingFormula/queryLookFormulaTabs',
    method: 'get',
    params: query
  })
}

// 新增研发配方
export function addSoftwareDevelopingFormula(data) {
  return request({
    url: '/software/softwareDevelopingFormula',
    method: 'post',
    data: data
  })
}


//保存安评详情数据
export function addSoftwareDevelopingFormulaProductSafetyAssessmentData(data) {
  return request({
    url: '/software/softwareDevelopingFormula/addSoftwareDevelopingFormulaProductSafetyAssessmentData',
    method: 'post',
    data: data
  })
}


// 修改研发配方
export function updateSoftwareDevelopingFormula(data) {
  return request({
    url: '/software/softwareDevelopingFormula',
    method: 'put',
    data: data
  })
}

export function updateBaseSoftwareDevelopingFormula(data) {
  return request({
    url: '/software/softwareDevelopingFormula/editBase',
    method: 'put',
    data
  })
}

//上传配方图片
export function updateSoftwareDevelopingFormulaImg(data) {
  return request({
    url: '/software/softwareDevelopingFormula/uploadFormulaImg',
    method: 'put',
    data: data
  })
}

//上传配方图片
export function updateSoftwareDevelopingFormulaFileImg(data) {
  return request({
    url: '/software/softwareDevelopingFormula/uploadFormulaFileImg',
    method: 'put',
    data: data
  })
}

// 删除研发配方
export function delSoftwareDevelopingFormula(id) {
  return request({
    url: '/software/softwareDevelopingFormula/' + id,
    method: 'delete'
  })
}

// 导出研发配方
export function exportSoftwareDevelopingFormula(query) {
  return request({
    url: '/software/softwareDevelopingFormula/export',
    method: 'get',
    params: query
  })
}

//获取配方字典数据
export function querySoftwareDevelopingFormulaDict(query) {
  return request({
    url: '/software/softwareDevelopingFormula/querySoftwareDevelopingFormulaDict',
    method: 'get',
    params: query
  })
}

//获取配方原料数据
export function queryFormulaMaterialData(query) {
  return request({
    url: '/software/softwareDevelopingFormula/queryFormulaMaterialData',
    method: 'get',
    params: query
  })
}


//获取配方类别数据
export function queryFormulaClassifyData(query) {
  return request({
    url: '/software/softwareDevelopingFormula/queryFormulaClassifyData',
    method: 'get',
    params: query
  })
}

const toFormualCategoryTree = (list, parentId) => {
  return list.filter(item => {
    if (item.parentId === parentId) {
      let children = toFormualCategoryTree(list, item.categoryId)
      if(children && children.length > 0) {
        item.children = children
      }
      return true
    }
    return false
  })
}

export function getFormulaCategoryTree(res) {
  let list = res
  toFormualCategoryTree(list, 0)
  list = list.filter(i=> i.parentId === 0)
  return list
}

//获取cir历史使用量数据
export function queryCirHistoryData(query) {
  return request({
    url: '/software/softwareDevelopingFormula/queryCirHistoryData',
    method: 'get',
    params: query
  })
}

const toCirDataTree = (list, parentId) => {
  return list.filter(item => {
    if (item.pid === parentId) {
      let children = toCirDataTree(list, item.id)
      if(children && children.length > 0) {
        item.children = children
      }
      return true
    }
    return false
  })
}

export function getCirDataTree(res) {
  let list = res
  toCirDataTree(list, 0)
  list = list.filter(i=> i.pid === 0)
  return list
}



//获取毒理历史使用量数据
export function queryDuliHistoryData(query) {
  return request({
    url: '/software/softwareDevelopingFormula/queryDuliHistoryData',
    method: 'get',
    params: query
  })
}

const toDuliDataTree = (list, parentId) => {
  return list.filter(item => {
    if (item.pid === parentId) {
      let children = toDuliDataTree(list, item.id)
      if(children && children.length > 0) {
        item.children = children
      }
      return true
    }
    return false
  })
}

export function getDuliDataTree(res) {
  let list = res
  toDuliDataTree(list, 0)
  list = list.filter(i=> i.pid === 0)
  return list
}

//根据实验室编码搜索配方
export function getFormulaLabNoInfoByCode(query) {
  return request({
    url: '/software/softwareDevelopingFormula/getFormulaLabNoInfoByCode',
    method: 'get',
    params:query
  })
}

//获取所有配方
export function queryAllFormula(query) {
  return request({
    url: '/software/softwareDevelopingFormula/queryAllFormula',
    method: 'get',
    params:query
  })
}


// 查询研发配方详细
export function queryFormualMaterialRecipeChangeHistoryData(query) {
  return request({
    url: '/software/softwareDevelopingFormula/queryFormualMaterialRecipeChangeHistoryData',
    method: 'get',
    params:query
  })
}

// 查询执行标准列表
export function queryFormulaZxbzDataList(query) {
  return request({
    url: '/software/softwareDevelopingFormula/queryFormulaZxbzDataList',
    method: 'get',
    params: query
  })
}

// 查询执行标准列表
export function queryFormulaZxbzDataDetail(query) {
  return request({
    url: '/software/softwareDevelopingFormula/queryFormulaZxbzDataDetail',
    method: 'get',
    params: query
  })
}

// 新增配方执行标准
export function addSoftwareDevelopingFormulaSpecZxbz(data) {
  return request({
    url: '/software/softwareDevelopingFormula/addFormulaSpecZxbz',
    method: 'post',
    data: data
  })
}
// 新增配方执行标准(用户)
export function addSoftwareDevelopingUserFormulaSpecZxbz(data) {
  return request({
    url: '/software/softwareDevelopingFormula/addSoftwareDevelopingUserFormulaSpecZxbz',
    method: 'post',
    data: data
  })
}

// 新增SPEC内容
export function addFormulaSpecPage(query) {
  return request({
    url: '/software/softwareDevelopingFormula/addFormulaSpecPage',
    method: 'get',
    params: query
  })
}


// 新增配方特殊原料
export function addFormulaSpecMaterialData(data) {
  return request({
    url: '/software/softwareDevelopingFormula/addFormulaSpecMaterialData',
    method: 'post',
    data: data
  })
}

// 新增配方安全评估使用目的
export function addFormulaSymdForm(data) {
  return request({
    url: '/software/softwareDevelopingFormula/addFormulaSymdForm',
    method: 'post',
    data: data
  })
}

// 新增配方安全评估使用目的
export function generatePFormulaInfo(data) {
  return request({
    url: '/software/softwareDevelopingFormula/generatePFormulaInfo',
    method: 'post',
    data: data
  })
}

// 生成B代码
export function generateBMaterialInfo(data) {
  return request({
    url: '/software/softwareDevelopingFormula/generateBMaterialInfo',
    method: 'post',
    data: data
  })
}

// 生成B代码配方
export function generateNewformulaInfo(data) {
  return request({
    url: '/software/softwareDevelopingFormula/genNewformulaInfo',
    method: 'post',
    data: data
  })
}


// 查询配方spec内容
export function queryMaterialFormulaSpecDataList(query) {
  return request({
    url: '/software/softwareDevelopingFormula/queryMaterialFormulaSpecDataList',
    method: 'get',
    params:query
  })
}

// 查询配方稳定性相关内容
export function queryFormulaStabilityRecordDataList(query) {
  return request({
    url: '/software/softwareDevelopingFormula/queryFormulaStabilityRecordDataList',
    method: 'get',
    params:query
  })
}


// 新增配方特殊原料
export function queryMaterialFormulaSpecDataDetail(query) {
  return request({
    url: '/software/softwareDevelopingFormula/queryMaterialFormulaSpecDataDetail',
    method: 'get',
    params:query
  })
}

// 新增配方特殊原料
export function queryFormulaLegalGy(query) {
  return request({
    url: '/software/softwareDevelopingFormula/queryFormulaLegalGy',
    method: 'get',
    params:query
  })
}


//保存备案工艺简述
export function addFormulaGyjsBeianInfo(data) {
  return request({
    url: '/software/softwareDevelopingFormula/addFormulaGyjsBeianInfo',
    method: 'post',
    data: data
  })
}


//获取分享部门信息
export function queryFormulaShareDeptDataList(query) {
  return request({
    url: '/software/softwareDevelopingFormula/queryFormulaShareDeptDataList',
    method: 'get',
    params:query
  })
}

//获取已分享部门信息
export function queryFormulaShareDeptDataDetail(query) {
  return request({
    url: '/software/softwareDevelopingFormula/queryFormulaShareDeptDataDetail',
    method: 'get',
    params:query
  })
}

//保存分享信息
export function addFormulaShareDataInfo(data) {
  return request({
    url: '/software/softwareDevelopingFormula/addFormulaShareDataInfo',
    method: 'post',
    data: data
  })
}

export function allSoftwareDevelopingFormula(params) {
  return request({
    url: '/software/softwareDevelopingFormula/all',
    method: 'get',
    params
  })
}
