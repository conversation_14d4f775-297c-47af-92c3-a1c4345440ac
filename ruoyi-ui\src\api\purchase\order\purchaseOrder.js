import request from "@/utils/request";

export function isNewPurchaseOrder() {
  return request({
    url: '/purchase/order/isNew',
    method: 'get'
  })
}
export function listPurchaseOrder(query) {
  return request({
    url: '/purchase/order/list',
    method: 'get',
    params: query
  })
}
export function listAuditPurchaseOrder(query) {
  return request({
    url: '/purchase/order/audit',
    method: 'get',
    params: query
  })
}

export function listCloseAuditPurchaseOrder(data) {
  return request({
    url: '/purchase/order/closeAudit',
    method: 'post',
    data: data
  })
}

export function listAuditNewPurchaseOrder(query) {
  return request({
    url: '/purchase/order/auditNew',
    method: 'get',
    params: query
  })
}

export function listAuditNewPurchaseCloseOrder(query) {
  return request({
    url: '/purchase/order/auditCloseNew',
    method: 'get',
    params: query
  })
}
export function getPurchaseOrder(id) {
  return request({
    url: '/purchase/order/' + id,
    method: 'get'
  })
}

export function addPurchaseOrder(data){
  return request({
    url: '/purchase/order/add',
    method: 'post',
    data: data
  })
}

export function addPurchaseOrderClose(data){
  return request({
    url: '/purchase/order/addClose',
    method: 'post',
    data: data
  })
}

export function editPurchaseOrderFile(data){
  return request({
    url: '/purchase/order/editFile',
    method: 'post',
    data: data
  })
}

export function updatePurchaseOrderGoods(data){
  return request({
    url: '/purchase/order/goods/edit',
    method: 'post',
    data: data
  })
}

export function purchaseOrderAll(query){
  return request({
    url: '/purchase/order/all',
    method: 'get',
    params: query
  })
}

export function purchaseOrderErpDataAll(query){
  return request({
    url: '/purchase/order/allOrderAndErpOrderData',
    method: 'get',
    params: query
  })
}

export function purchaseOrderFreightAll(query){
  return request({
    url: '/purchase/order/freightAll',
    method: 'get',
    params: query
  })
}
export function submitAudit(data) {
  return request({
    url: '/purchase/order/submitAudit',
    method: 'put',
    data: data
  })
}
export function cancelAudit(data) {
  return request({
    url: '/purchase/order/cancelAudit',
    method: 'put',
    data: data
  })
}
export function cancelCloseAudit(data) {
  return request({
    url: '/purchase/order/cancelCloseAudit',
    method: 'put',
    data: data
  })
}

export function pigeonholePurchaseOrder(data) {
  return request({
    url: '/purchase/order/pigeonhole',
    method: 'post',
    data: data
  })
}
export function exportPurchaseOrder(query) {
  return request({
    url: '/purchase/order/export',
    method: 'get',
    params: query
  })
}
export function jasperPurchaseOrder(data) {
  return request({
    url: '/purchase/order/jasper',
    method: 'post',
    data: data
  })
}

export function buildErpGoodsArray(data) {
  return request({
    url: '/purchase/order/buildErpGoodsArray',
    method: 'post',
    data: data
  })
}
