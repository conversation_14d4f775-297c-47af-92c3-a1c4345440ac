import request from '@/utils/request'

// 查询包材供应商列表
export function listMaterialSupplier(query) {
  return request({
    url: '/resource/materialSupplier/list',
    method: 'get',
    params: query
  })
}

// 查询包材供应商详细
export function getMaterialSupplier(id) {
  return request({
    url: '/resource/materialSupplier/' + id,
    method: 'get'
  })
}

export function getMaterialSupplierByParams(query) {
  return request({
    url: '/resource/materialSupplier/getInfoByParams',
    method: 'get',
    params: query,
  })
}

// 新增包材供应商
export function addMaterialSupplier(data) {
  return request({
    url: '/resource/materialSupplier',
    method: 'post',
    data: data
  })
}

// 修改包材供应商
export function updateMaterialSupplier(data) {
  return request({
    url: '/resource/materialSupplier',
    method: 'put',
    data: data
  })
}

// 删除包材供应商
export function delMaterialSupplier(id) {
  return request({
    url: '/resource/materialSupplier/' + id,
    method: 'delete'
  })
}

// 导出包材供应商
export function exportMaterialSupplier(query) {
  return request({
    url: '/resource/materialSupplier/export',
    method: 'get',
    params: query
  })
}

export function allMaterialSupplier(query) {
  return request({
    url: '/resource/materialSupplier/all',
    method: 'get',
    params: query
  })
}
