import request from '@/utils/request'

// 查询二级项目列表
export function listProjectItem(query) {
  return request({
    url: '/project/projectItem/list',
    method: 'get',
    params: query
  })
}

// 查询二级项目详细
export function getProjectItem(id) {
  return request({
    url: '/project/projectItem/' + id,
    method: 'get'
  })
}


// 查询二级项目详细
export function getProjectHistroyItemBom(id) {
  return request({
    url: '/project/projectItem/getProjectHistroyItemBom/' + id,
    method: 'get'
  })
}

// 查询内容物信息
export function getProjectItemNrw(data) {
  return request({
    url: '/project/projectItem/projectItemData',
    method: 'post',
    params: data
  })
}

// 查询内容物信息(三级多选)
export function getProjectItemMultiNrw(data) {
  return request({
    url: '/project/projectItem/projectItemMultiData',
    method: 'post',
    params: data
  })
}

// 查询内容物信息
export function projectItemOrderData(data) {
  return request({
    url: '/project/projectItem/projectItemOrderData',
    method: 'post',
    params: data
  })
}

// 新增二级项目
export function addProjectItem(data) {
  return request({
    url: '/project/projectItem',
    method: 'post',
    data: data
  })
}

// 修改二级项目
export function updateProjectItem(data) {
  return request({
    url: '/project/projectItem',
    method: 'put',
    data: data
  })
}

// 删除二级项目
export function delProjectItem(id) {
  return request({
    url: '/project/projectItem/' + id,
    method: 'delete'
  })
}

// 删除二级项目
export function hebingProjectItem(query) {
  return request({
    url: '/project/projectItem/hebingProp',
    method: 'get',
    params: query
  })
}

// 导出二级项目
export function exportProjectItem(query) {
  return request({
    url: '/project/projectItem/export',
    method: 'get',
    params: query
  })
}

export function projectItemAll(query) {
  let params = {
    url: '/project/projectItem/all',
    method: 'get'
  }
  if(query) {
    params.params = query
  }
  return request(params)
}

export function countProjectItem(query) {
  return request({
    url: '/project/projectItem/count',
    method: 'get',
    params: query
  })
}

export function getUsedItemNames(id) {
  return request({
    url: '/project/projectItem/usedItemNames/' + id,
    method: 'get'
  })
}

export function itemTypeChartsData(query) {
  return request({
    url: '/project/projectItem/getItemTypeChartsData',
    method: 'get',
    params: query
  })
}

export function projectConfirmOrder(query) {
  return request({
    url: '/project/projectItem/getConfirmOrder',
    method: 'get',
    params: query
  })
}

export function itemTypeStatusChartsData(query) {
  return request({
    url: '/project/projectItem/itemTypeStatusChartsData',
    method: 'get',
    params: query
  })
}
