import request from '@/utils/request'

// 查询生产可行性评估模板列表
export function listPgTemplate(query) {
  return request({
    url: '/sop/pgTemplate/list',
    method: 'get',
    params: query
  })
}

// 查询生产可行性评估模板详细
export function getPgTemplate(id) {
  return request({
    url: '/sop/pgTemplate/' + id,
    method: 'get'
  })
}

// 新增生产可行性评估模板
export function addPgTemplate(data) {
  return request({
    url: '/sop/pgTemplate',
    method: 'post',
    data: data
  })
}

// 修改生产可行性评估模板
export function updatePgTemplate(data) {
  return request({
    url: '/sop/pgTemplate',
    method: 'put',
    data: data
  })
}

// 删除生产可行性评估模板
export function delPgTemplate(id) {
  return request({
    url: '/sop/pgTemplate/' + id,
    method: 'delete'
  })
}

// 导出生产可行性评估模板
export function exportPgTemplate(query) {
  return request({
    url: '/sop/pgTemplate/export',
    method: 'get',
    params: query
  })
}

export function allPgTemplate(query) {
  return request({
    url: '/sop/pgTemplate/all',
    method: 'get',
    params: query
  })
}
