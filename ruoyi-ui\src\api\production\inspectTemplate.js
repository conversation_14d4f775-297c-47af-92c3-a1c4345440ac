import request from '@/utils/request'

// 查询检查模板列表
export function listInspectTemplate(query) {
  return request({
    url: '/production/inspectTemplate/list',
    method: 'get',
    params: query
  })
}

// 查询检查模板详细
export function getInspectTemplate(id) {
  return request({
    url: '/production/inspectTemplate/' + id,
    method: 'get'
  })
}

// 新增检查模板
export function addInspectTemplate(data) {
  return request({
    url: '/production/inspectTemplate',
    method: 'post',
    data: data
  })
}

// 修改检查模板
export function updateInspectTemplate(data) {
  return request({
    url: '/production/inspectTemplate',
    method: 'put',
    data: data
  })
}

// 删除检查模板
export function delInspectTemplate(id) {
  return request({
    url: '/production/inspectTemplate/' + id,
    method: 'delete'
  })
}

// 导出检查模板
export function exportInspectTemplate(query) {
  return request({
    url: '/production/inspectTemplate/export',
    method: 'get',
    params: query
  })
}