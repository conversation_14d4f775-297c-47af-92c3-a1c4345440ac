import request from '@/utils/request'

// 查询erp销退单列表
export function listCoptj(query) {
  return request({
    url: '/order/coptj/list',
    method: 'get',
    params: query
  })
}

// 查询erp销退单详细
export function getCoptj(id) {
  return request({
    url: '/order/coptj/' + id,
    method: 'get'
  })
}

// 新增erp销退单
export function addCoptj(data) {
  return request({
    url: '/order/coptj',
    method: 'post',
    data: data
  })
}

// 修改erp销退单
export function updateCoptj(data) {
  return request({
    url: '/order/coptj',
    method: 'put',
    data: data
  })
}

// 删除erp销退单
export function delCoptj(id) {
  return request({
    url: '/order/coptj/' + id,
    method: 'delete'
  })
}

// 导出erp销退单
export function exportCoptj(query) {
  return request({
    url: '/order/coptj/export',
    method: 'get',
    params: query
  })
}