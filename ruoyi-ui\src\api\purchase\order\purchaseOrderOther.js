import request from "@/utils/request";

export function listPurchaseOrderOther(query) {
  return request({
    url: '/purchase/order/other/list',
    method: 'get',
    params: query
  })
}
export function getPurchaseOrderOther(id) {
  return request({
    url: '/purchase/order/other/' + id,
    method: 'get'
  })
}


export function purchaseOtherOrderAll(query){
  return request({
    url: '/purchase/order/other/all',
    method: 'get',
    params: query
  })
}



export function purchaseOtherOrderGoodsAll(data){
  return request({
    url: '/purchase/order/other/allGoods',
    method: 'post',
    data
  })
}

export function addPurchaseOrderOther(data){
  return request({
    url: '/purchase/order/other/add',
    method: 'post',
    data: data
  })
}

export function getPurchaseOrderGoods(query){
  return request({
    url: '/purchase/order/other/orderGoods',
    method: 'get',
    params: query
  })
}

// 修改关联订单
export function updateOtherOrderRelations(data) {
  return request({
    url: '/purchase/order/other/updateOtherOrderRelations',
    method: 'put',
    data: data
  })
}
