import request from '@/utils/request'

// 查询项目进度列表
export function listProcess(query) {
  return request({
    url: '/project/process/list',
    method: 'get',
    params: query
  })
}

// 查询项目进度详细
export function getProcess(id) {
  return request({
    url: '/project/process/' + id,
    method: 'get'
  })
}

// 新增项目进度
export function addProcess(data) {
  return request({
    url: '/project/process',
    method: 'post',
    data: data
  })
}

// 修改项目进度
export function updateProcess(data) {
  return request({
    url: '/project/process',
    method: 'put',
    data: data
  })
}

// 删除项目进度
export function delProcess(id) {
  return request({
    url: '/project/process/' + id,
    method: 'delete'
  })
}

// 导出项目进度
export function exportProcess(query) {
  return request({
    url: '/project/process/export',
    method: 'get',
    params: query
  })
}