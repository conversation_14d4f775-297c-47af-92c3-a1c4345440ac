import request from '@/utils/request'

export function allPlan(query) {
  return request({
    url: '/hr/wages/all',
    method: 'get',
    params: query
  })
}
// 查询工资方案列表
export function listPlan(query) {
  return request({
    url: '/hr/wages/list',
    method: 'get',
    params: query
  })
}

// 查询工资方案详细
export function getPlan(id) {
  return request({
    url: '/hr/wages/' + id,
    method: 'get'
  })
}

// 新增工资方案
export function addPlan(data) {
  return request({
    url: '/hr/wages',
    method: 'post',
    data: data
  })
}

// 修改工资方案
export function updatePlan(data) {
  return request({
    url: '/hr/wages',
    method: 'put',
    data: data
  })
}

// 删除工资方案
export function delPlan(id) {
  return request({
    url: '/hr/wages/' + id,
    method: 'delete'
  })
}

// 导出工资方案
export function exportPlan(query) {
  return request({
    url: '/hr/wages/export',
    method: 'get',
    params: query
  })
}

export function queryLeaveLogByUser(params) {
  return request({
    url: '/hr/user/wages/queryLeaveLogByUser',
    method: 'get',
    params: params
  })
}