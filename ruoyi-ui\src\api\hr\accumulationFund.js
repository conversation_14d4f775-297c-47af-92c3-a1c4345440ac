import request from '@/utils/request'

export function allAccumulationFund(query) {
  return request({
    url: '/hr/accumulationFund/all',
    method: 'get',
    params: query
  })
}
// 查询公积金方案列表
export function listAccumulationFund(query) {
  return request({
    url: '/hr/accumulationFund/list',
    method: 'get',
    params: query
  })
}

// 查询公积金方案详细
export function getAccumulationFund(id) {
  return request({
    url: '/hr/accumulationFund/' + id,
    method: 'get'
  })
}

// 新增公积金方案
export function addAccumulationFund(data) {
  return request({
    url: '/hr/accumulationFund',
    method: 'post',
    data: data
  })
}

// 修改公积金方案
export function updateAccumulationFund(data) {
  return request({
    url: '/hr/accumulationFund',
    method: 'put',
    data: data
  })
}

// 删除公积金方案
export function delAccumulationFund(id) {
  return request({
    url: '/hr/accumulationFund/' + id,
    method: 'delete'
  })
}

// 导出公积金方案
export function exportAccumulationFund(query) {
  return request({
    url: '/hr/accumulationFund/export',
    method: 'get',
    params: query
  })
}
