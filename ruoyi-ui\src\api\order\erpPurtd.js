import request from '@/utils/request'

// 查询采购单单身信息档列表
export function listErpPurtd(query) {
  return request({
    url: '/order/erpPurtd/list',
    method: 'get',
    params: query
  })
}

// 查询采购单单身信息档详细
export function getErpPurtd(id) {
  return request({
    url: '/order/erpPurtd/' + id,
    method: 'get'
  })
}

// 新增采购单单身信息档
export function addErpPurtd(data) {
  return request({
    url: '/order/erpPurtd',
    method: 'post',
    data: data
  })
}

// 修改采购单单身信息档
export function updateErpPurtd(data) {
  return request({
    url: '/order/erpPurtd',
    method: 'put',
    data: data
  })
}

// 删除采购单单身信息档
export function delErpPurtd(id) {
  return request({
    url: '/order/erpPurtd/' + id,
    method: 'delete'
  })
}

// 导出采购单单身信息档
export function exportErpPurtd(query) {
  return request({
    url: '/order/erpPurtd/export',
    method: 'get',
    params: query
  })
}