import request from '@/utils/request'

// 查询产线亏损-公司纬度列表
export function listProductionCompany(query) {
  return request({
    url: '/production/productionCompany/list',
    method: 'get',
    params: query
  })
}

// 查询产线亏损-公司纬度详细
export function getProductionCompany(id) {
  return request({
    url: '/production/productionCompany/' + id,
    method: 'get'
  })
}

// 新增产线亏损-公司纬度
export function addProductionCompany(data) {
  return request({
    url: '/production/productionCompany',
    method: 'post',
    data: data
  })
}

// 修改产线亏损-公司纬度
export function updateProductionCompany(data) {
  return request({
    url: '/production/productionCompany',
    method: 'put',
    data: data
  })
}

// 删除产线亏损-公司纬度
export function delProductionCompany(id) {
  return request({
    url: '/production/productionCompany/' + id,
    method: 'delete'
  })
}

// 导出产线亏损-公司纬度
export function exportProductionCompany(query) {
  return request({
    url: '/production/productionCompany/export',
    method: 'get',
    params: query
  })
}