{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\mesLog\\save.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\mesLog\\save.vue", "mtime": 1753954679646}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQoNCmltcG9ydCB7DQogIGFkZE1lc0xvZywNCiAgZ2V0TWVzTG9nLA0KICB1cGRhdGVNZXNMb2cNCn0gZnJvbSAiQC9hcGkvcHJvZHVjdGlvbi9tZXNMb2ciOw0KaW1wb3J0IHthbGxQbGFuQXJlYUhvdXJzfSBmcm9tICJAL2FwaS9wcm9kdWN0aW9uL3BsYW5BcmVhSG91cnMiOw0KaW1wb3J0IHthbGxXaXBDb250UGFydGlhbE91dH0gZnJvbSAiQC9hcGkvbWVzL3dpcGNvbnRQYXJ0aWFsb3V0IjsNCmltcG9ydCBNZXNMb2dBcmVhIGZyb20gIkAvdmlld3MvcHJvZHVjdGlvbi9tZXNMb2cvYXJlYS52dWUiOw0KaW1wb3J0IHthbGxXaXBMb3RMb2d9IGZyb20gIkAvYXBpL21lcy9tZXNWaWV3IjsNCmltcG9ydCBQcm9jZXNzVGFibGUgZnJvbSAiQC92aWV3cy9wcm9kdWN0aW9uL2xheW91dC9wcm9jZXNzL3RhYmxlLnZ1ZSI7DQppbXBvcnQge2dldEFyY2hpdmV9IGZyb20gIkAvYXBpL3Byb2plY3QvYXJjaGl2ZSI7DQppbXBvcnQge2dldFByb2plY3R9IGZyb20gIkAvYXBpL3Byb2plY3QvcHJvamVjdCI7DQppbXBvcnQge2dldFNjaGVkdWxlU3RhbmRhcmRCeUNvZGV9IGZyb20gIkAvYXBpL29yZGVyL3NjaGVkdWxlU3RhbmRhcmQiOw0KaW1wb3J0IE1lc1FjIGZyb20gIkAvdmlld3MvbWVzL3Byb2R1Y3Rpb24vcWMudnVlIjsNCmltcG9ydCB7YWxsRmluaXNoZWRJbnNwZWN0aW9uLCBhbGxHcm91cEJ5QmF0Y2hGaW5pc2hlZEluc3BlY3Rpb259IGZyb20gIkAvYXBpL3FjL2ZpbmlzaGVkSW5zcGVjdGlvbiI7DQppbXBvcnQge2FsbEdyb3VwQnlFcXVpcG1lbnROb30gZnJvbSAiQC9hcGkvbWVzL3dpcENvbnRNYXRlcmlhbExvdCI7DQppbXBvcnQge2dldEJvbUJ5RXJwQ29kZSwgZ2V0U2NoZWR1bGVNYXRlcmlhbH0gZnJvbSAiQC9hcGkvY29tbW9uL2VycCI7DQppbXBvcnQgTWVzTG9nRGF0YUNoYXJ0cyBmcm9tICJAL3ZpZXdzL3Byb2R1Y3Rpb24vbWVzTG9nL2RhdGFDaGFydHMudnVlIjsNCmltcG9ydCB7Z2V0R3psQnlQYXJhbXN9IGZyb20gIkAvYXBpL3NvcC9nemwiOw0KaW1wb3J0IHtnZXRNYXRlcmlhbExvZ0xpc3R9IGZyb20gIkAvYXBpL3Byb2R1Y3Rpb24vbWVzTG9nIjsNCmltcG9ydCB7Z2V0U2NoZWR1bGVCeUNvZGV9IGZyb20gIkAvYXBpL29yZGVyL3NjaGVkdWxlIjsNCmltcG9ydCBNZXNMb2dNYXRlcmlhbFRhYnMgZnJvbSAiQC92aWV3cy9wcm9kdWN0aW9uL21lc0xvZy9tYXRlcmlhbFRhYnMudnVlIjsNCmltcG9ydCBNZXNMb2dQcm9kdWN0aW9uTG9nIGZyb20gIkAvdmlld3MvcHJvZHVjdGlvbi9tZXNMb2cvbG9nLnZ1ZSI7DQppbXBvcnQgTWVzUHJvZHVjdGlvbkxvZyBmcm9tICJAL3ZpZXdzL21lcy9wcm9kdWN0aW9uL2xvZy52dWUiOw0KaW1wb3J0IE1lc0xvZ1NjaGVkdWxlVGFibGUgZnJvbSAiQC92aWV3cy9wcm9kdWN0aW9uL21lc0xvZy9zY2hlZHVsZVRhYmxlLnZ1ZSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ21lc0xvZ1NhdmUnLA0KICBjb21wb25lbnRzOiB7DQogICAgTWVzTG9nU2NoZWR1bGVUYWJsZSwNCiAgICBNZXNQcm9kdWN0aW9uTG9nLA0KICAgIE1lc0xvZ1Byb2R1Y3Rpb25Mb2csDQogICAgTWVzTG9nTWF0ZXJpYWxUYWJzLA0KICAgIE1lc0xvZ0RhdGFDaGFydHMsDQogICAgTWVzUWMsDQogICAgUHJvY2Vzc1RhYmxlLA0KICAgIE1lc0xvZ0FyZWENCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBidG5Mb2FkaW5nOiBmYWxzZSwNCiAgICAgIHJlYWRvbmx5OiBmYWxzZSwNCiAgICAgIGZvcm06IHt9LA0KICAgICAgcnVsZXM6IHt9LA0KICAgICAgY3VycmVudFRhYjogJycsDQogICAgICBzYWlsaW5nc09wdGlvbnM6IFsNCiAgICAgICAge2xhYmVsOiAn55m954+tJyx2YWx1ZTogJzAnfSwNCiAgICAgICAge2xhYmVsOiAn5pma54+tJyx2YWx1ZTogJzEnfSwNCiAgICAgIF0sDQogICAgICBvdXRBcnJheTogW10sDQogICAgICBsb3RMb2dzOiBbXSwNCiAgICAgIG1lc0xvdFdhaXRMaXN0OiBbXSwNCiAgICAgIHVzZXJBcnJheTogW10sDQogICAgICBwcm9qZWN0OiB7fSwNCiAgICAgIGJvbURhdGE6IFtdLA0KICAgICAgYm9tVHJlZTogW10sDQogICAgICBkaWZmRGF0YUFycmF5OiBbXSwNCiAgICAgIGRpZmZEYXRhT3B0aW9uczoge30sDQogICAgICBpbnNwZWN0aW9uTGlzdDogW10sDQogICAgICBxY09wdGlvbnM6IHt9LA0KICAgICAgbWF0ZXJpYWxMb2dBcnJheTogW10sDQogICAgICBzY2hlZHVsZVN0YW5kYXJkOiB7fSwNCiAgICAgIG1hdGVyaWFsQXJyYXk6IFtdLA0KICAgICAgc2NoZWR1bGU6IHt9LA0KICAgICAgYmNwTGlzdDogW10sDQogICAgICBiY3BBcnJheTogW10sDQogICAgICBvdGhlckFycmF5OiBbXSwNCiAgICAgIHNjaGVkdWxlQmNwQXJyYXk6IFtdLA0KICAgICAgc2NoZWR1bGVPdGhlckFycmF5OiBbXSwNCiAgICB9DQogIH0sDQogIGFzeW5jIGNyZWF0ZWQoKSB7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBhc3luYyB0YWJDaGFuZ2UoKSB7DQogICAgICBhd2FpdCB0aGlzLiRuZXh0VGljaygpDQogICAgICBpZih0aGlzLmN1cnJlbnRUYWIgPT09ICdwZXJzb24nKSB7DQoNCiAgICAgIH0gZWxzZSBpZih0aGlzLmN1cnJlbnRUYWIgPT09ICdjaGFydHMnKSB7DQogICAgICAgIGF3YWl0IHRoaXMucmVkdWNlRGlmZkNoYXJ0KCkNCiAgICAgIH0gZWxzZSBpZih0aGlzLmN1cnJlbnRUYWIgPT09ICdxYycpIHsNCiAgICAgICAgYXdhaXQgdGhpcy5yZWR1Y2VRY0NoYXJ0KCkNCiAgICAgIH0NCiAgICB9LA0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMuJHBhcmVudC4kcGFyZW50Lm9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBpZDogbnVsbCwNCiAgICAgICAgY29kZTogbnVsbCwNCiAgICAgICAgd29ya0RhdGU6IG51bGwsDQogICAgICAgIHNhaWxpbmdzOiBudWxsLA0KICAgICAgICBhcmVhTm86IG51bGwsDQogICAgICAgIGxpbmVMZWFkZXI6IG51bGwsDQogICAgICAgIG1pbnV0ZXM6IG51bGwsDQogICAgICAgIGluTnVtczogbnVsbCwNCiAgICAgICAgZ29vZHNOdW1zOiBudWxsLA0KICAgICAgICBiYWROdW1zOiBudWxsLA0KICAgICAgfTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIikNCiAgICAgIHRoaXMucHJvamVjdCA9IHt9DQogICAgICB0aGlzLmJvbURhdGEgPSBbXQ0KICAgICAgdGhpcy5ib21UcmVlID0gW10NCiAgICAgIHRoaXMubWF0ZXJpYWxMb2dBcnJheSA9IFtdDQogICAgICB0aGlzLm91dEFycmF5ID0gW10NCiAgICAgIHRoaXMubG90TG9ncyA9IFtdDQogICAgICB0aGlzLm1lc0xvdFdhaXRMaXN0ID0gW10NCiAgICAgIHRoaXMudXNlckFycmF5ID0gW10NCiAgICAgIHRoaXMuZGlmZkRhdGFBcnJheSA9IFtdDQogICAgICB0aGlzLmRpZmZEYXRhT3B0aW9ucyA9IHt9DQogICAgICB0aGlzLmluc3BlY3Rpb25MaXN0ID0gW10NCiAgICAgIHRoaXMucWNPcHRpb25zID0ge30NCiAgICAgIHRoaXMuc2NoZWR1bGUgPSB7fQ0KICAgICAgdGhpcy5zY2hlZHVsZVN0YW5kYXJkID0ge30NCiAgICAgIHRoaXMubWF0ZXJpYWxBcnJheSA9IFtdDQogICAgICB0aGlzLmJjcExpc3QgPSBbXQ0KICAgICAgdGhpcy5iY3BBcnJheSA9IFtdDQogICAgICB0aGlzLm90aGVyQXJyYXkgPSBbXQ0KICAgICAgdGhpcy5zY2hlZHVsZUJjcEFycmF5ID0gW10NCiAgICAgIHRoaXMuc2NoZWR1bGVPdGhlckFycmF5ID0gW10NCiAgICB9LA0KICAgIGRpZmZIb3VycyhzdGFydFRpbWUsZW5kVGltZSkgew0KICAgICAgaWYoc3RhcnRUaW1lICYmIGVuZFRpbWUpIHsNCiAgICAgICAgbGV0IG1pbnV0ZXMgPSB0aGlzLm1vbWVudChlbmRUaW1lLCdZWVlZLU1NLUREIGhoOm1tJykuZGlmZih0aGlzLm1vbWVudChzdGFydFRpbWUsJ1lZWVktTU0tREQgaGg6bW0nKSwgJ21pbnV0ZXMnKQ0KICAgICAgICBpZihtaW51dGVzKSB7DQogICAgICAgICAgcmV0dXJuIHRoaXMuZGl2aWRlKG1pbnV0ZXMsNjApLnRvTnVtYmVyKCkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgcmVkdWNlUWNDaGFydCgpIHsNCiAgICAgIGF3YWl0IHRoaXMuJG5leHRUaWNrKCkNCiAgICAgIGNvbnN0IG1lc1FjID0gdGhpcy4kcmVmcy5tZXNRYw0KICAgICAgaWYobWVzUWMpIHsNCiAgICAgICAgY29uc3QgcWNDaGFydHMgPSBtZXNRYy4kcmVmcy5xY0NoYXJ0cw0KICAgICAgICBpZihxY0NoYXJ0cykgew0KICAgICAgICAgIGF3YWl0IHFjQ2hhcnRzLmluaXQodGhpcy5xY09wdGlvbnMpDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIHJlZHVjZURpZmZDaGFydCgpIHsNCiAgICAgIGF3YWl0IHRoaXMuJG5leHRUaWNrKCkNCiAgICAgIGNvbnN0IG1lc0RhdGFDaGFydHMgPSB0aGlzLiRyZWZzLm1lc0RhdGFDaGFydHMNCiAgICAgIGlmKG1lc0RhdGFDaGFydHMpIHsNCiAgICAgICAgY29uc3QgZGlmZkNoYXJ0ID0gbWVzRGF0YUNoYXJ0cy4kcmVmcy5kaWZmQ2hhcnQNCiAgICAgICAgaWYoZGlmZkNoYXJ0KSB7DQogICAgICAgICAgYXdhaXQgZGlmZkNoYXJ0LmluaXQodGhpcy5kaWZmRGF0YU9wdGlvbnMpDQogICAgICAgIH0NCiAgICAgICAgY29uc3QgdGltZUxpbmVDaGFydCA9IG1lc0RhdGFDaGFydHMuJHJlZnMudGltZUxpbmVDaGFydA0KICAgICAgICBpZih0aW1lTGluZUNoYXJ0KSB7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIGJ1aWxkUHJvZHVjdGlvbkxvZygpIHsNCiAgICAgIGNvbnN0IGZvcm0gPSB0aGlzLmZvcm0NCiAgICAgIGNvbnN0IG1hdGVyaWFsQXJyYXkgPSB0aGlzLm1hdGVyaWFsQXJyYXkNCiAgICAgIGNvbnN0IGJvbVJlcyA9IGF3YWl0IGdldEJvbUJ5RXJwQ29kZShmb3JtLnByb2R1Y3RObykNCiAgICAgIGlmKGJvbVJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgbGV0IGJjcExpc3QgPSBib21SZXMuZGF0YS5maWx0ZXIoaSA9PiBpLm1iMDA1ID09PSAnMTAzJykNCiAgICAgICAgZm9yIChjb25zdCBpdGVtIG9mIGJjcExpc3QpIHsNCiAgICAgICAgICBjb25zdCBnemxQYXJhbXMgPSB7DQogICAgICAgICAgICBtZDAwMzogaXRlbS5tZDAwMywNCiAgICAgICAgICAgIG1kMDAxOiBpdGVtLm1kMDAxLA0KICAgICAgICAgIH0NCiAgICAgICAgICBjb25zdCBnemxSZXMgPSBhd2FpdCBnZXRHemxCeVBhcmFtcyhnemxQYXJhbXMpDQogICAgICAgICAgaWYoZ3psUmVzLmNvZGUgPT09IDIwMCAmJiBnemxSZXMuZGF0YSkgew0KICAgICAgICAgICAgY29uc3QgZ3psID0gZ3psUmVzLmRhdGENCiAgICAgICAgICAgIGl0ZW0ubWF4ID0gZ3psLm1heA0KICAgICAgICAgICAgaXRlbS5hdmcgPSBnemwuYXZnDQogICAgICAgICAgICBpdGVtLm1pbiA9IGd6bC5taW4NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5iY3BMaXN0ID0gYmNwTGlzdA0KICAgICAgfQ0KDQogICAgICBsZXQgcmVzID0gYXdhaXQgZ2V0TWF0ZXJpYWxMb2dMaXN0KGZvcm0uaWQpDQogICAgICBpZihyZXMpew0KICAgICAgICBjb25zdCBiY3BBcnJheSA9IHJlcy5iY3BBcnJheQ0KICAgICAgICBjb25zdCBvdGhlckFycmF5ID0gcmVzLm90aGVyQXJyYXkNCiAgICAgICAgY29uc3Qgc2NoZWR1bGVCY3BBcnJheSA9IHJlcy5zY2hlZHVsZUJjcEFycmF5DQogICAgICAgIGNvbnN0IHNjaGVkdWxlT3RoZXJBcnJheSA9IHJlcy5zY2hlZHVsZU90aGVyQXJyYXkNCiAgICAgICAgY29uc29sZS5sb2coc2NoZWR1bGVCY3BBcnJheSkNCiAgICAgICAgY29uc29sZS5sb2coc2NoZWR1bGVPdGhlckFycmF5KQ0KICAgICAgICBpZihiY3BBcnJheSl7DQogICAgICAgICAgdGhpcy5iY3BBcnJheSA9IGJjcEFycmF5DQogICAgICAgICAgZm9yIChjb25zdCBvIG9mIGJjcEFycmF5KSB7DQogICAgICAgICAgICBpZighbWF0ZXJpYWxBcnJheS5tYXAoaT0+IGkubWF0ZXJpYWxDb2RlKS5pbmNsdWRlcyhvLm1hdGVyaWFsQ29kZSkpIHsNCiAgICAgICAgICAgICAgbWF0ZXJpYWxBcnJheS5wdXNoKHsNCiAgICAgICAgICAgICAgICBtYXRlcmlhbENvZGU6IG8ubWF0ZXJpYWxDb2RlLA0KICAgICAgICAgICAgICAgIHJlbWFyazogbnVsbCwNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgaWYob3RoZXJBcnJheSl7DQogICAgICAgICAgdGhpcy5vdGhlckFycmF5ID0gb3RoZXJBcnJheQ0KICAgICAgICAgIGZvciAoY29uc3QgbyBvZiBvdGhlckFycmF5KSB7DQogICAgICAgICAgICBpZighbWF0ZXJpYWxBcnJheS5tYXAoaT0+IGkubWF0ZXJpYWxDb2RlKS5pbmNsdWRlcyhvLm1hdGVyaWFsQ29kZSkpIHsNCiAgICAgICAgICAgICAgbWF0ZXJpYWxBcnJheS5wdXNoKHsNCiAgICAgICAgICAgICAgICBtYXRlcmlhbENvZGU6IG8ubWF0ZXJpYWxDb2RlLA0KICAgICAgICAgICAgICAgIHJlbWFyazogbnVsbCwNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgaWYoc2NoZWR1bGVCY3BBcnJheSkgew0KICAgICAgICAgIHRoaXMuc2NoZWR1bGVCY3BBcnJheSA9IHNjaGVkdWxlQmNwQXJyYXkNCiAgICAgICAgfQ0KICAgICAgICBpZihzY2hlZHVsZU90aGVyQXJyYXkpIHsNCiAgICAgICAgICB0aGlzLnNjaGVkdWxlT3RoZXJBcnJheSA9IHNjaGVkdWxlT3RoZXJBcnJheQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBidWlsZE1hdGVyaWFsQXJyYXkoKSB7DQogICAgICBjb25zdCBmb3JtID0gdGhpcy5mb3JtDQogICAgICBjb25zdCBzY2hlZHVsZSA9IHRoaXMuc2NoZWR1bGUNCiAgICAgIGNvbnN0IGZvcm1SYXRlID0gdGhpcy5kaXZpZGUoZm9ybS5wcm9kdWN0TnVtcyxzY2hlZHVsZS5wbGFuTnVtcykvL+S6p+WHuuWNoOavlA0KICAgICAgaWYoZm9ybVJhdGUpIHsNCiAgICAgICAgbGV0IHNjaGVkdWxlQ29kZSA9IGZvcm0uc2NoZWR1bGVDb2RlDQogICAgICAgIGNvbnN0IG1hdGVyaWFsTG9nTGlzdCA9IGF3YWl0IGFsbEdyb3VwQnlFcXVpcG1lbnRObyh7c2NoZWR1bGVDb2RlLHdvcmtEYXRlOiBmb3JtLndvcmtEYXRlfSkvL+aaguaXtuS4jeeul+ePreasoQ0KICAgICAgICB0aGlzLm1hdGVyaWFsTG9nTGlzdCA9IG1hdGVyaWFsTG9nTGlzdA0KICAgICAgICBjb25zdCBhcnIgPSBzY2hlZHVsZUNvZGUuc3BsaXQoJy0nKQ0KICAgICAgICBsZXQgZXJwQm9tRGF0YSA9IGF3YWl0IGdldFNjaGVkdWxlTWF0ZXJpYWwoe3dvcmtPcmRlck5vOiBhcnJbMV0sIHdvcmtPcmRlclNpbmdsZTogYXJyWzBdLH0pDQoNCiAgICAgICAgY29uc3Qgc2NoZWR1bGVOdW1zID0gc2NoZWR1bGUucGxhbk51bXMNCiAgICAgICAgY29uc3QgbWF0ZXJpYWxOb1NldCA9IG5ldyBTZXQoKQ0KICAgICAgICBmb3IgKGNvbnN0IGxvZyBvZiBtYXRlcmlhbExvZ0xpc3QpIHsNCiAgICAgICAgICBtYXRlcmlhbE5vU2V0LmFkZChsb2cubWF0ZXJpYWxObykNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnN0IG1hdGVyaWFsTG9nQXJyYXkgPSBbXQ0KICAgICAgICBmb3IgKGNvbnN0IG1hdGVyaWFsTm8gb2YgbWF0ZXJpYWxOb1NldCkgew0KICAgICAgICAgIGNvbnN0IHRlMDEwU2V0ID0gbmV3IFNldCgpLy/lnKjlk4Hlj7fnu7TluqbkuIvlho3mjpLmibnmrKENCiAgICAgICAgICBsZXQgc2pCb21OdW0gPSAwIC8vYm9t55So6YeP5a+55LqO5bel5Y2V5Lqn5ZOB5piv5oiQ5ZOB55qEDQogICAgICAgICAgbGV0IGNiMDEwID0gMC8vZXJw5o2f6ICX546HDQogICAgICAgICAgbGV0IG1hdGVyaWFsTmFtZQ0KICAgICAgICAgIGxldCB0ZTAwNi8vZXJw5Y2V5L2NDQogICAgICAgICAgbGV0IHRlMDA1U3VtID0gdGhpcy4kYmlnKDApLy/lt6XljZXlj5Hmlpnph4/lsI/orqENCiAgICAgICAgICBsZXQgbG90U3VtcyA9IHRoaXMuJGJpZygwKS8v5om55qyh5L2/55So6YeP5bCP6K6hDQogICAgICAgICAgbGV0IGVycFN5bFN1bSA9IHRoaXMuJGJpZygwKS8vZXJw5ZOB5Y+357u05bqm5L2/55So6YeP5bCP6K6hDQogICAgICAgICAgZm9yIChjb25zdCBiIG9mIGVycEJvbURhdGEpIHsNCiAgICAgICAgICAgIGlmKGIudGUwMDQgPT09IG1hdGVyaWFsTm8pIHsNCiAgICAgICAgICAgICAgc2pCb21OdW0gPSBOdW1iZXIoYi5zakJvbU51bSkNCiAgICAgICAgICAgICAgY2IwMTAgPSBOdW1iZXIoYi5jYjAxMCkNCiAgICAgICAgICAgICAgbWF0ZXJpYWxOYW1lID0gYi50ZTAxNw0KICAgICAgICAgICAgICB0ZTAwNiA9IGIudGUwMDYNCg0KICAgICAgICAgICAgICB0ZTAwNVN1bSA9IHRoaXMuYWRkKHRlMDA1U3VtLGIudGUwMDUpDQogICAgICAgICAgICAgIHRlMDEwU2V0LmFkZChiLnRlMDEwKS8v5oyJ54WnZTEw55qE5om55qyh5p2lDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICAgIGZvciAoY29uc3QgbG9nIG9mIG1hdGVyaWFsTG9nTGlzdCkgew0KICAgICAgICAgICAgaWYobWF0ZXJpYWxObyA9PT0gbG9nLm1hdGVyaWFsTm8pIHsNCiAgICAgICAgICAgICAgbG90U3VtcyA9IHRoaXMuYWRkKGxvdFN1bXMsbG9nLm51bXMpDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICAgIGNvbnN0IHRlMDEwQXJyYXkgPSBbXQ0KICAgICAgICAgIGZvciAoY29uc3QgdGUwMTAgb2YgdGUwMTBTZXQpIHsNCiAgICAgICAgICAgIGxldCBlcnBMbE51bXMgPSAwDQogICAgICAgICAgICBsZXQgZXJwU3lsTnVtcyA9IDANCiAgICAgICAgICAgIGZvciAoY29uc3Qgb3Qgb2YgdGhpcy5vdGhlckFycmF5KSB7DQogICAgICAgICAgICAgIGlmKG90Lm1hdGVyaWFsQ29kZSA9PT0gbWF0ZXJpYWxObyApIHsNCiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IHQgb2Ygb3QudGUwMTBBcnJheSkgew0KICAgICAgICAgICAgICAgICAgaWYodC50ZTAxMCA9PT0gdGUwMTApIHsNCiAgICAgICAgICAgICAgICAgICAgaWYodC5nZFN5bE51bXMpIHsNCiAgICAgICAgICAgICAgICAgICAgICBlcnBMbE51bXMgPSBOdW1iZXIodC5nZFN5bE51bXMpDQogICAgICAgICAgICAgICAgICAgICAgZXJwU3lsTnVtcyA9IE51bWJlcih0LmdkU3lsTnVtcykNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgY29uc3QgZXF1aXBtZW50U2V0ID0gbmV3IFNldCgpLy/lk4Hlj7fmibnmrKHlhoXmjpLorr7lpIcNCiAgICAgICAgICAgIGZvciAoY29uc3QgbG9nIG9mIG1hdGVyaWFsTG9nTGlzdCkgew0KICAgICAgICAgICAgICBpZihtYXRlcmlhbE5vID09PSBsb2cubWF0ZXJpYWxObyAmJiB0ZTAxMCA9PT0gbG9nLm1hdGVyaWFsTG90Tm8pIHsNCiAgICAgICAgICAgICAgICBlcXVpcG1lbnRTZXQuYWRkKGxvZy5lcXVpcG1lbnRObykNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgY29uc3QgZXF1aXBtZW50QXJyYXkgPSBbXQ0KICAgICAgICAgICAgZm9yIChjb25zdCBlcXVpcG1lbnRObyBvZiBlcXVpcG1lbnRTZXQpIHsNCiAgICAgICAgICAgICAgbGV0IGwgPSB7DQogICAgICAgICAgICAgICAgZXF1aXBtZW50Tm8sDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgZm9yIChjb25zdCBsb2cgb2YgbWF0ZXJpYWxMb2dMaXN0KSB7DQogICAgICAgICAgICAgICAgaWYobWF0ZXJpYWxObyA9PT0gbG9nLm1hdGVyaWFsTm8gJiYgdGUwMTAgPT09IGxvZy5tYXRlcmlhbExvdE5vICYmIGVxdWlwbWVudE5vID09PSBsb2cuZXF1aXBtZW50Tm8pIHsNCiAgICAgICAgICAgICAgICAgIGwubnVtcyA9IGxvZy5udW1zLy/kvb/nlKjph48NCiAgICAgICAgICAgICAgICAgIGwudGltZXMgPSBsb2cudGltZXMvL+mihuaWmeasoeaVsA0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBlcXVpcG1lbnRBcnJheS5wdXNoKGwpDQogICAgICAgICAgICB9DQogICAgICAgICAgICBjb25zdCBsbEFycmF5ID0gW10vL2VycOmihuaWmeWNleiusOW9lQ0KICAgICAgICAgICAgZm9yIChjb25zdCBiIG9mIGVycEJvbURhdGEpIHsNCiAgICAgICAgICAgICAgaWYoYi50ZTAwNCA9PT0gbWF0ZXJpYWxObyAmJiBiLnRlMDEwID09PSB0ZTAxMCkgew0KICAgICAgICAgICAgICAgIGxldCBzeWxSYXRlDQogICAgICAgICAgICAgICAgbGV0IGxsQmxOdW1zDQogICAgICAgICAgICAgICAgbGV0IGxsQmxSZWFzb24NCiAgICAgICAgICAgICAgICBsZXQgc2NCbE51bXMNCiAgICAgICAgICAgICAgICBsZXQgc2NCbFJlYXNvbg0KICAgICAgICAgICAgICAgIGZvciAoY29uc3Qgb3Qgb2YgdGhpcy5vdGhlckFycmF5KSB7DQogICAgICAgICAgICAgICAgICBpZihvdC5tYXRlcmlhbENvZGUgPT09IG1hdGVyaWFsTm8gKSB7DQogICAgICAgICAgICAgICAgICAgIGZvciAoY29uc3QgdCBvZiBvdC50ZTAxMEFycmF5KSB7DQogICAgICAgICAgICAgICAgICAgICAgaWYodC50ZTAxMCA9PT0gdGUwMTApIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHN5bFJhdGUgPSB0LnN5bFJhdGUNCiAgICAgICAgICAgICAgICAgICAgICAgIGxsQmxOdW1zID0gdC5sbEJsTnVtcw0KICAgICAgICAgICAgICAgICAgICAgICAgbGxCbFJlYXNvbiA9IHQubGxCbFJlYXNvbg0KICAgICAgICAgICAgICAgICAgICAgICAgc2NCbE51bXMgPSB0LnNjQmxOdW1zDQogICAgICAgICAgICAgICAgICAgICAgICBzY0JsUmVhc29uID0gdC5zY0JsUmVhc29uDQogICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIGxsQXJyYXkucHVzaCh7DQogICAgICAgICAgICAgICAgICB0ZTAwMTogYi50ZTAwMSwvL+mihuaWmemAmuefpeWNleWIqw0KICAgICAgICAgICAgICAgICAgdGUwMDI6IGIudGUwMDIsLy/poobmlpnpgJrnn6XljZXlj7cNCiAgICAgICAgICAgICAgICAgIHRlMDA4OiBiLnRlMDA4LC8v5LuT5bqTDQogICAgICAgICAgICAgICAgICBtZTAwMzogYi5tZTAwMywvL+acgOaXqeWFpeW6k+aXpeacnw0KICAgICAgICAgICAgICAgICAgdGUwMTM6IGIudGUwMTMsLy/poobmlpnor7TmmI4NCiAgICAgICAgICAgICAgICAgIHN5bFJhdGUsLy/kvb/nlKjph4/mr5Tkvoso55Sf5Lqn5om55om55qyh5ZOB5Y+35L2/55So6YePL+W3peWNleWTgeWPt+aJueasoeS9v+eUqOmHjykNCiAgICAgICAgICAgICAgICAgIGxsQmxOdW1zLC8v5p2l5paZ5LiN6ImvDQogICAgICAgICAgICAgICAgICBsbEJsUmVhc29uLA0KICAgICAgICAgICAgICAgICAgc2NCbE51bXMsLy/nlJ/kuqfkuI3oia8NCiAgICAgICAgICAgICAgICAgIHNjQmxSZWFzb24sDQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGUwMTBBcnJheS5wdXNoKHsNCiAgICAgICAgICAgICAgdGUwMTAsDQogICAgICAgICAgICAgIGVycExsTnVtczogZXJwTGxOdW1zLC8v6aKG5paZ6YePDQogICAgICAgICAgICAgIGVycFN5bE51bXM6IGVycFN5bE51bXMsLy/kvb/nlKjph48NCiAgICAgICAgICAgICAgZXF1aXBtZW50QXJyYXksDQogICAgICAgICAgICAgIGxsQXJyYXksDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgZXJwU3lsU3VtID0gdGhpcy5hZGQoZXJwU3lsU3VtLGVycFN5bE51bXMpDQogICAgICAgICAgfQ0KICAgICAgICAgIGlmKHNjaGVkdWxlTnVtcykgew0KICAgICAgICAgICAgbGV0IGdkWHFsID0gdGhpcy5tdWx0aXBseShzY2hlZHVsZU51bXMsc2pCb21OdW0pDQogICAgICAgICAgICBsZXQgbG90UGxhblhxbCA9IHRoaXMubXVsdGlwbHkoZm9ybS5wcm9kdWN0TnVtcyxzakJvbU51bSkvL+eUn+S6p+iusOW9leWunumZheeUn+S6p+aVsOmHjyDorqHnrpcg5pys57q/6ZyA5rGC6YePDQogICAgICAgICAgICBsZXQgbG90TGxYcWwgPSB0aGlzLm11bHRpcGx5KGZvcm0ucHJvZHVjdE51bXMsc2pCb21OdW0pLy/mibnmrKHnlJ/kuqfph4/orqHnrpcg55CG6K665L2/55So6YePDQogICAgICAgICAgICBsZXQgbG9zcyA9IHRoaXMuc3VidHJhY3QobG90U3VtcywgbG90TGxYcWwpDQogICAgICAgICAgICBjb25zdCBlcnBSYXRlTnVtcyA9IHRoaXMubXVsdGlwbHkoZXJwU3lsU3VtLGZvcm1SYXRlKS50b051bWJlcigpDQogICAgICAgICAgICBtYXRlcmlhbExvZ0FycmF5LnB1c2goew0KICAgICAgICAgICAgICBtYXRlcmlhbE5vLA0KICAgICAgICAgICAgICBtYXRlcmlhbE5hbWUsDQogICAgICAgICAgICAgIHNqQm9tTnVtLA0KICAgICAgICAgICAgICBjYjAxMCwNCiAgICAgICAgICAgICAgdGUwMDYsDQogICAgICAgICAgICAgIGdkWHFsOiBnZFhxbC50b051bWJlcigpLA0KICAgICAgICAgICAgICB0ZTAwNVN1bTogdGUwMDVTdW0udG9OdW1iZXIoKSwNCiAgICAgICAgICAgICAgeGZDeVJhdGU6IHRoaXMuZGl2aWRlKHRoaXMuc3VidHJhY3QodGUwMDVTdW0sIGdkWHFsKSwgZ2RYcWwpLnRvTnVtYmVyKCksDQogICAgICAgICAgICAgIGxvdFhxbDogbG90UGxhblhxbC50b051bWJlcigpLA0KICAgICAgICAgICAgICBsb3RMbFhxbDogbG90TGxYcWwudG9OdW1iZXIoKSwNCiAgICAgICAgICAgICAgbG90U3VtczogbG90U3Vtcy50b051bWJlcigpLA0KICAgICAgICAgICAgICBsb3NzOiBsb3NzLnRvTnVtYmVyKCksDQogICAgICAgICAgICAgIGxvdFJhdGU6IHRoaXMuZGl2aWRlKGxvc3MsIGxvdExsWHFsKS50b051bWJlcigpLA0KICAgICAgICAgICAgICBlcnBTeWxTdW0sDQogICAgICAgICAgICAgIGVycFJhdGVOdW1zLA0KICAgICAgICAgICAgICBkaWZmTnVtczogdGhpcy5zdWJ0cmFjdChsb3RTdW1zLCBlcnBSYXRlTnVtcykudG9OdW1iZXIoKSwNCiAgICAgICAgICAgICAgdGUwMTBBcnJheSwNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgdGhpcy5tYXRlcmlhbExvZ0FycmF5ID0gbWF0ZXJpYWxMb2dBcnJheQ0KICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgYnVpbGREaWZmRGF0YSgpIHsNCiAgICAgIGNvbnN0IGZvcm0gPSB0aGlzLmZvcm0NCiAgICAgIGNvbnN0IHNjaGVkdWxlU3RhbmRhcmQgPSB0aGlzLnNjaGVkdWxlU3RhbmRhcmQNCg0KICAgICAgY29uc3QgcHJvZHVjdE51bXMgPSBmb3JtLnByb2R1Y3ROdW1zDQogICAgICBjb25zdCBhY3R1YWxIb3VycyA9IHRoaXMuZGl2aWRlKGZvcm0uc3VtTWludXRlcyw2MCkNCiAgICAgIGNvbnN0IGFjdHVhbER1cmF0aW9uID0gdGhpcy5kaWZmSG91cnMoZm9ybS5zdGFydFRpbWUsZm9ybS5lbmRUaW1lKQ0KICAgICAgaWYoYWN0dWFsRHVyYXRpb24gJiYgc2NoZWR1bGVTdGFuZGFyZC5udW1zKSB7DQogICAgICAgIGNvbnN0IGFjdHVhbFByb2R1Y3Rpdml0eSA9IHRoaXMuZGl2aWRlKHByb2R1Y3ROdW1zLGFjdHVhbER1cmF0aW9uKS50b051bWJlcigpDQogICAgICAgIGNvbnN0IGFjdHVhbEhvdXJzUmF0ZSA9IHRoaXMuZGl2aWRlKHByb2R1Y3ROdW1zLCBhY3R1YWxIb3VycykudG9OdW1iZXIoKQ0KICAgICAgICBjb25zdCBkaWZmRGF0YUFycmF5ID0gW10NCg0KICAgICAgICBsZXQgc3RhbmRhcmRIb3VycyA9IDANCiAgICAgICAgbGV0IHN0YW5kYXJkUGVyc29uTnVtcyA9IDANCiAgICAgICAgbGV0IHN0YW5kYXJkSG91cnNSYXRlID0gMA0KICAgICAgICBpZihmb3JtLm9wTm8gPT09ICdHQicpIHsvL+S4gOmYtiDlj5blsJHnmoQNCiAgICAgICAgICBzdGFuZGFyZFBlcnNvbk51bXMgPSBzY2hlZHVsZVN0YW5kYXJkLm51bXMNCiAgICAgICAgICBzdGFuZGFyZEhvdXJzUmF0ZSA9IHNjaGVkdWxlU3RhbmRhcmQuY29zdEhvdXJzUmF0ZQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHN0YW5kYXJkUGVyc29uTnVtcyA9IHNjaGVkdWxlU3RhbmRhcmQuY29zdE51bXMNCiAgICAgICAgICBzdGFuZGFyZEhvdXJzUmF0ZSA9IHNjaGVkdWxlU3RhbmRhcmQuY29zdEhvdXJzUmF0ZQ0KICAgICAgICB9DQogICAgICAgIHN0YW5kYXJkSG91cnMgPSB0aGlzLm11bHRpcGx5KHN0YW5kYXJkSG91cnNSYXRlLGZvcm0ucHJvZHVjdE51bXMpLnRvTnVtYmVyKCkNCiAgICAgICAgZGlmZkRhdGFBcnJheS5wdXNoKFsn5qCH5YeGJyxzY2hlZHVsZVN0YW5kYXJkLnByb2R1Y3Rpdml0eSxzdGFuZGFyZFBlcnNvbk51bXMsc3RhbmRhcmRIb3VycyxzdGFuZGFyZEhvdXJzUmF0ZV0pDQogICAgICAgIGRpZmZEYXRhQXJyYXkucHVzaChbJ+WunumZhScsYWN0dWFsUHJvZHVjdGl2aXR5LGZvcm0uc3VtTnVtcyxhY3R1YWxIb3VycyxhY3R1YWxIb3Vyc1JhdGVdKQ0KICAgICAgICBkaWZmRGF0YUFycmF5LnB1c2goWw0KICAgICAgICAgICflt67lvIInLA0KICAgICAgICAgIHRoaXMuc3VidHJhY3QoYWN0dWFsUHJvZHVjdGl2aXR5LHNjaGVkdWxlU3RhbmRhcmQucHJvZHVjdGl2aXR5KS50b051bWJlcigpLA0KICAgICAgICAgIHRoaXMuc3VidHJhY3QoZm9ybS5zdW1OdW1zLHN0YW5kYXJkUGVyc29uTnVtcykudG9OdW1iZXIoKSwNCiAgICAgICAgICB0aGlzLnN1YnRyYWN0KGFjdHVhbEhvdXJzLHN0YW5kYXJkSG91cnMpLnRvTnVtYmVyKCksDQogICAgICAgICAgdGhpcy5zdWJ0cmFjdChhY3R1YWxIb3Vyc1JhdGUsc3RhbmRhcmRIb3Vyc1JhdGUpLnRvTnVtYmVyKCksDQogICAgICAgIF0pDQogICAgICAgIHRoaXMuZGlmZkRhdGFBcnJheSA9IGRpZmZEYXRhQXJyYXkNCg0KICAgICAgICB0aGlzLmRpZmZEYXRhT3B0aW9ucyA9ICB7DQogICAgICAgICAgdGl0bGU6IHsNCiAgICAgICAgICAgIHRleHQ6ICfmibnmrKHnu7TluqbmlbDmja7lr7nmr5QnDQogICAgICAgICAgfSwNCiAgICAgICAgICBsZWdlbmQ6IHsNCiAgICAgICAgICAgIGRhdGE6IFsn6aKE5LywJywgJ+WunumZhSddDQogICAgICAgICAgfSwNCiAgICAgICAgICByYWRhcjogew0KICAgICAgICAgICAgaW5kaWNhdG9yOiBbDQogICAgICAgICAgICAgIHsgbmFtZTogJ+eUn+S6p+S6p+iDvScsIG1heDogTWF0aC5tYXgoc2NoZWR1bGVTdGFuZGFyZC5wcm9kdWN0aXZpdHksIGFjdHVhbFByb2R1Y3Rpdml0eSkqMS4yIH0sDQogICAgICAgICAgICAgIHsgbmFtZTogJ+eUn+S6p+S6uuaVsCcsIG1heDogTWF0aC5tYXgoc3RhbmRhcmRQZXJzb25OdW1zLCBmb3JtLnN1bU51bXMpKjEuMiB9LA0KICAgICAgICAgICAgICB7IG5hbWU6ICfnlJ/kuqflt6Xml7YnLCBtYXg6IE1hdGgubWF4KHN0YW5kYXJkSG91cnMsIGFjdHVhbEhvdXJzKSoxLjIgfSwNCiAgICAgICAgICAgICAgeyBuYW1lOiAn5bel5pe25Lqn5Ye6546HJywgbWF4OiBNYXRoLm1heChzdGFuZGFyZEhvdXJzUmF0ZSwgYWN0dWFsSG91cnNSYXRlKSoxLjIgfQ0KICAgICAgICAgICAgXQ0KICAgICAgICAgIH0sDQogICAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIG5hbWU6ICfpooTkvLAgdnMg5a6e6ZmFJywNCiAgICAgICAgICAgICAgdHlwZTogJ3JhZGFyJywNCiAgICAgICAgICAgICAgZGF0YTogWw0KICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgIHZhbHVlOiBkaWZmRGF0YUFycmF5WzBdLnNsaWNlKDEpLA0KICAgICAgICAgICAgICAgICAgbmFtZTogJ+mihOS8sCcNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgIHZhbHVlOiBkaWZmRGF0YUFycmF5WzFdLnNsaWNlKDEpLA0KICAgICAgICAgICAgICAgICAgbmFtZTogJ+WunumZhScNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIF0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICBdDQogICAgICAgIH0NCiAgICAgICAgLy8gYXdhaXQgdGhpcy5yZWR1Y2VEaWZmQ2hhcnQoKQ0KICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgaW1wb3J0QXJjaGl2ZShhcmNoaXZlSWQpIHsNCiAgICAgIGxldCBhcmNoaXZlUmVzID0gYXdhaXQgZ2V0QXJjaGl2ZShhcmNoaXZlSWQpDQogICAgICBpZihhcmNoaXZlUmVzLmNvZGUgPT09IDIwMCAmJiBhcmNoaXZlUmVzLmRhdGEpIHsNCiAgICAgICAgbGV0IGFyY2hpdmUgPSBhcmNoaXZlUmVzLmRhdGENCg0KICAgICAgICBjb25zdCBwcm9qZWN0UmVzID0gYXdhaXQgZ2V0UHJvamVjdChhcmNoaXZlLnByb2plY3RJZCkNCiAgICAgICAgY29uc3QgcHJvamVjdCA9IHByb2plY3RSZXMuZGF0YQ0KDQogICAgICAgIHByb2plY3QudHlwZSA9IGFyY2hpdmUudHlwZQ0KDQogICAgICAgIGlmKFsnMCcsJzEnLCczJ10uaW5jbHVkZXMoYXJjaGl2ZS50eXBlKSkgew0KICAgICAgICAgIHByb2plY3QuYm9tUmVzb3VyY2UgPSBhcmNoaXZlLmJvbVJlc291cmNlDQogICAgICAgICAgcHJvamVjdC5ib21UeXBlID0gYXJjaGl2ZS5ib21UeXBlDQogICAgICAgICAgcHJvamVjdC5yZXNvdXJjZUZpbmlzaGVkR29vZHNJZCA9IGFyY2hpdmUucmVzb3VyY2VGaW5pc2hlZEdvb2RzSWQNCiAgICAgICAgICBwcm9qZWN0LmVycFByaWNlID0gYXJjaGl2ZS5lcnBQcmljZQ0KICAgICAgICAgIHByb2plY3QuYm9tQXJyYXkgPSBhcmNoaXZlLmJvbUFycmF5Ly/ov5nph4zlsLHmmK/lrZfnrKbkuLINCiAgICAgICAgICBwcm9qZWN0LmVycENvZGUgPSBhcmNoaXZlLmVycENvZGUNCiAgICAgICAgICBpZihhcmNoaXZlLmN1YmljbGVBcnJheSkgew0KICAgICAgICAgICAgY29uc3QgY3ViaWNsZUFycmF5ID0gSlNPTi5wYXJzZShhcmNoaXZlLmN1YmljbGVBcnJheSkNCiAgICAgICAgICAgIHByb2plY3QuY3ViaWNsZUFycmF5ID0gY3ViaWNsZUFycmF5DQoNCiAgICAgICAgICAgIGNvbnN0IGltZ3MgPSBbXQ0KICAgICAgICAgICAgY29uc3QgdmlkZW9zID0gW10NCiAgICAgICAgICAgIGZvciAoY29uc3QgaXRlbSBvZiBjdWJpY2xlQXJyYXkpIHsNCiAgICAgICAgICAgICAgZm9yIChjb25zdCBzZWNvbmQgb2YgaXRlbS5zZWN0aW9uQXJyYXkpIHsNCiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IHcgb2Ygc2Vjb25kLndvcmtUeXBlQXJyYXkpIHsNCiAgICAgICAgICAgICAgICAgIGlmKHcuaG9tZXdvcmtJbWdzKSB7DQogICAgICAgICAgICAgICAgICAgIGltZ3MucHVzaCguLi53LmhvbWV3b3JrSW1ncy5zcGxpdCgnLCcpKQ0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgaWYody5ob21ld29ya1ZpZGVvcy5sZW5ndGgpIHsNCiAgICAgICAgICAgICAgICAgICAgdmlkZW9zLnB1c2goLi4udy5ob21ld29ya1ZpZGVvcykNCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHRoaXMuaW1ncyA9IGltZ3MNCiAgICAgICAgICAgIHRoaXMudmlkZW9zID0gdmlkZW9zDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHByb2plY3QuY3ViaWNsZUFycmF5ID0gW10NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5ib21EYXRhID0gSlNPTi5wYXJzZShhcmNoaXZlLmJvbUFycmF5KQ0KICAgICAgICB0aGlzLnByb2plY3QgPSBwcm9qZWN0DQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBidWlsZFFjTG9ncygpIHsNCiAgICAgIGNvbnN0IGZvcm0gPSB0aGlzLmZvcm0NCiAgICAgIGNvbnN0IGFyciA9IGZvcm0uc2NoZWR1bGVDb2RlLnNwbGl0KCctJykNCiAgICAgIGNvbnN0IGluc3BlY3Rpb25MaXN0ID0gYXdhaXQgYWxsRmluaXNoZWRJbnNwZWN0aW9uKHtzaW5nbGVDYXRlZ29yeTogYXJyWzBdLHdvcmtPcmRlck5vOmFyclsxXX0pLy/otKjmo4ANCiAgICAgIGZvcihsZXQgaXRlbSBvZiBpbnNwZWN0aW9uTGlzdCl7DQogICAgICAgIGxldCBlcnBDb2x1bW4gPSBpdGVtLmVycENvbHVtbjsNCiAgICAgICAgaWYoZXJwQ29sdW1uKXsNCiAgICAgICAgICBsZXQgb2JqID0gSlNPTi5wYXJzZShlcnBDb2x1bW4pOw0KICAgICAgICAgIGl0ZW0udGcwMzAgPSBvYmoudGcwMzA7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMuaW5zcGVjdGlvbkxpc3QgPSBpbnNwZWN0aW9uTGlzdA0KDQogICAgICBjb25zdCBncm91cEluc3BlY3Rpb25MaXN0ID0gYXdhaXQgYWxsR3JvdXBCeUJhdGNoRmluaXNoZWRJbnNwZWN0aW9uKHtzaW5nbGVDYXRlZ29yeTogYXJyWzBdLHdvcmtPcmRlck5vOmFyclsxXX0pDQoNCiAgICAgIGNvbnN0IHJhd0RhdGEgPSBbDQogICAgICAgIGdyb3VwSW5zcGVjdGlvbkxpc3QubWFwKGk9PiBpLmJhdGNoTnVtcyAtIGkuc2FtcGxpbmdOdW1zKSwNCiAgICAgICAgZ3JvdXBJbnNwZWN0aW9uTGlzdC5tYXAoaT0+IGkuc2FtcGxpbmdOdW1zKSwNCiAgICAgIF07DQogICAgICBjb25zdCB0b3RhbERhdGEgPSBbXTsNCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcmF3RGF0YVswXS5sZW5ndGg7ICsraSkgew0KICAgICAgICBsZXQgc3VtID0gMDsNCiAgICAgICAgZm9yIChsZXQgaiA9IDA7IGogPCByYXdEYXRhLmxlbmd0aDsgKytqKSB7DQogICAgICAgICAgc3VtICs9IHJhd0RhdGFbal1baV07DQogICAgICAgIH0NCiAgICAgICAgdG90YWxEYXRhLnB1c2goc3VtKTsNCiAgICAgIH0NCiAgICAgIGNvbnN0IGdyaWQgPSB7DQogICAgICAgIGxlZnQ6IDEwMCwNCiAgICAgICAgcmlnaHQ6IDEwMCwNCiAgICAgICAgdG9wOiA1MCwNCiAgICAgICAgYm90dG9tOiA1MA0KICAgICAgfTsNCiAgICAgIGNvbnN0IHNlcmllcyA9IFsNCiAgICAgICAgJ+acquaKveagtycsDQogICAgICAgICflt7Lmir3moLcnLA0KICAgICAgXS5tYXAoKG5hbWUsIHNpZCkgPT4gew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIG5hbWUsDQogICAgICAgICAgdHlwZTogJ2JhcicsDQogICAgICAgICAgc3RhY2s6ICd0b3RhbCcsDQogICAgICAgICAgYmFyV2lkdGg6ICc2MCUnLA0KICAgICAgICAgIGxhYmVsOiB7DQogICAgICAgICAgICBzaG93OiB0cnVlLA0KICAgICAgICAgICAgZm9ybWF0dGVyOiAocGFyYW1zKSA9PiBNYXRoLnJvdW5kKHBhcmFtcy52YWx1ZSAqIDEwMDApIC8gMTAgKyAnJScNCiAgICAgICAgICB9LA0KICAgICAgICAgIGRhdGE6IHJhd0RhdGFbc2lkXS5tYXAoKGQsIGRpZCkgPT4NCiAgICAgICAgICAgIHRvdGFsRGF0YVtkaWRdIDw9IDAgPyAwIDogZCAvIHRvdGFsRGF0YVtkaWRdDQogICAgICAgICAgKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgdGhpcy5xY09wdGlvbnMgPSB7DQogICAgICAgIGxlZ2VuZDogew0KICAgICAgICAgIHNlbGVjdGVkTW9kZTogZmFsc2UNCiAgICAgICAgfSwNCiAgICAgICAgZ3JpZCwNCiAgICAgICAgeUF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAndmFsdWUnDQogICAgICAgIH0sDQogICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICBkYXRhOiBncm91cEluc3BlY3Rpb25MaXN0Lm1hcChpPT4gaS5iYXRjaE5vKQ0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXMNCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIGluaXQoaWQpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGdldE1lc0xvZyhpZCkNCiAgICAgIGNvbnN0IGZvcm0gPSByZXMuZGF0YQ0KICAgICAgaWYoZm9ybS5tYXRlcmlhbEFycmF5KSB7DQogICAgICAgIHRoaXMubWF0ZXJpYWxBcnJheSA9IEpTT04ucGFyc2UoZm9ybS5tYXRlcmlhbEFycmF5KQ0KICAgICAgfQ0KICAgICAgdGhpcy5mb3JtID0gZm9ybQ0KDQogICAgICBjb25zdCBzY2hlZHVsZUNvZGUgPSBmb3JtLnNjaGVkdWxlQ29kZQ0KICAgICAgY29uc3QgYXJyID0gc2NoZWR1bGVDb2RlLnNwbGl0KCctJykNCiAgICAgIGNvbnN0IHNjaGVkdWxlUmVzID0gYXdhaXQgZ2V0U2NoZWR1bGVCeUNvZGUoYXJyWzBdICsgIi0iICsgYXJyWzFdKQ0KICAgICAgaWYoc2NoZWR1bGVSZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgIGNvbnN0IHNjaGVkdWxlID0gc2NoZWR1bGVSZXMuZGF0YQ0KICAgICAgICB0aGlzLnNjaGVkdWxlID0gc2NoZWR1bGUNCiAgICAgIH0NCg0KICAgICAgY29uc3Qgc2NoZWR1bGVTdGFuZGFyZFJlcyA9IGF3YWl0IGdldFNjaGVkdWxlU3RhbmRhcmRCeUNvZGUoZm9ybS5wcm9kdWN0Tm8pDQogICAgICBpZihzY2hlZHVsZVN0YW5kYXJkUmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICBjb25zdCBzY2hlZHVsZVN0YW5kYXJkID0gc2NoZWR1bGVTdGFuZGFyZFJlcy5kYXRhDQogICAgICAgIHRoaXMuc2NoZWR1bGVTdGFuZGFyZCA9IHNjaGVkdWxlU3RhbmRhcmQNCiAgICAgICAgaWYoc2NoZWR1bGVTdGFuZGFyZCkgew0KICAgICAgICAgIGF3YWl0IHRoaXMuaW1wb3J0QXJjaGl2ZShzY2hlZHVsZVN0YW5kYXJkLmFyY2hpdmVJZCkNCiAgICAgICAgICBhd2FpdCB0aGlzLmJ1aWxkRGlmZkRhdGEoKQ0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIGNvbnN0IG91dExpc3QgPSBhd2FpdCBhbGxXaXBDb250UGFydGlhbE91dCh7DQogICAgICAgIGFyZWFubzogZm9ybS5hcmVhTm8sDQogICAgICAgIGV2ZW50RGF0ZTogZm9ybS53b3JrRGF0ZSwNCiAgICAgICAgZXF1aXBtZW50bm86IGZvcm0uZXF1aXBtZW50Tm8sDQogICAgICAgIHNjaGVkdWxlQ29kZTogZm9ybS5zY2hlZHVsZUNvZGUsDQogICAgICB9KQ0KICAgICAgbGV0IGluTnVtcyA9IHRoaXMuJGJpZygwKQ0KICAgICAgbGV0IGdvb2RzTnVtcyA9IHRoaXMuJGJpZygwKQ0KICAgICAgbGV0IGJhZE51bXMgPSB0aGlzLiRiaWcoMCkNCiAgICAgIHRoaXMub3V0QXJyYXkgPSBvdXRMaXN0Lm1hcChpPT4gew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIGxvdE5vOiBpLmxvdG5vLA0KICAgICAgICAgIG9wTm86IGkub3BubywNCiAgICAgICAgICBldmVudFRpbWU6IGkuZXZlbnR0aW1lLA0KICAgICAgICAgIHVzZXJObzogaS51c2Vybm8sDQogICAgICAgICAgYXJlYU5vOiBpLmFyZWFubywNCiAgICAgICAgICBpbk51bXM6IGkuaW5wdXRxdHksLy/kuqflh7rph48NCiAgICAgICAgICBnb29kc051bXM6IGkuZ29vZHF0eSwvL+iJr+WTgemHjw0KICAgICAgICAgIGJhZE51bXM6IGkuc2NyYXBxdHksLy/oia/lk4Hph48NCiAgICAgICAgICBlcXVpcG1lbnRObzogaS5lcXVpcG1lbnRubywNCiAgICAgICAgICB1c2VyTmFtZTogaS51c2VyTmFtZSwNCiAgICAgICAgfQ0KICAgICAgfSkvL+mHjeWRveWQjeWxnuaAp+WQjeensCzmlrnkvr/nkIbop6Por63kuYkNCg0KICAgICAgZm9yIChjb25zdCBpdGVtIG9mIHRoaXMub3V0QXJyYXkpIHsNCiAgICAgICAgaW5OdW1zID0gdGhpcy5hZGQoaW5OdW1zLGl0ZW0uaW5OdW1zKQ0KICAgICAgICBnb29kc051bXMgPSB0aGlzLmFkZChnb29kc051bXMsaXRlbS5nb29kc051bXMpDQogICAgICAgIGJhZE51bXMgPSB0aGlzLmFkZChiYWROdW1zLGl0ZW0uYmFkTnVtcykNCiAgICAgIH0NCg0KICAgICAgZm9ybS5pbk51bXMgPSBpbk51bXMudG9OdW1iZXIoKQ0KICAgICAgZm9ybS5nb29kc051bXMgPSBnb29kc051bXMudG9OdW1iZXIoKQ0KICAgICAgZm9ybS5iYWROdW1zID0gYmFkTnVtcy50b051bWJlcigpDQoNCiAgICAgIGNvbnN0IGxvdExvZ3MgPSBhd2FpdCBhbGxXaXBMb3RMb2coew0KICAgICAgICBhcmVhTm86IGZvcm0uYXJlYU5vLA0KICAgICAgICBjcmVhdGVUaW1lOiBmb3JtLndvcmtEYXRlLA0KICAgICAgICBlcXVpcG1lbnRObzogZm9ybS5lcXVpcG1lbnRObywNCiAgICAgICAgc2NoZWR1bGVDb2RlOiBmb3JtLnNjaGVkdWxlQ29kZSwNCiAgICAgIH0pDQogICAgICB0aGlzLmxvdExvZ3MgPSBsb3RMb2dzDQoNCiAgICAgIGNvbnN0IHBsYW5BcmVhSG91ckxpc3QgPSBhd2FpdCBhbGxQbGFuQXJlYUhvdXJzKHsNCiAgICAgICAgYXJlYU5vOiBmb3JtLmFyZWFObywNCiAgICAgICAgZXF1aXBtZW50Tm86IGZvcm0uZXF1aXBtZW50Tm8sDQogICAgICAgIHdvcmtEYXRlOiBmb3JtLndvcmtEYXRlLA0KICAgICAgICBzYWlsaW5nczogZm9ybS5zYWlsaW5ncywNCiAgICAgICAgc2NoZWR1bGVDb2RlOiBmb3JtLnNjaGVkdWxlQ29kZSwNCiAgICAgIH0pDQoNCiAgICAgIGNvbnN0IHVzZXJBcnJheSA9IFtdDQogICAgICBjb25zdCB1c2VyU2V0ID0gbmV3IFNldChwbGFuQXJlYUhvdXJMaXN0Lm1hcChpPT4gaS51c2VyQ29kZSkpDQogICAgICBsZXQgc3VtTWludXRlcyA9IHRoaXMuJGJpZygwKQ0KICAgICAgZm9yIChjb25zdCB1c2VyQ29kZSBvZiB1c2VyU2V0KSB7DQogICAgICAgIGxldCBuaWNrTmFtZQ0KICAgICAgICBsZXQgbWludXRlcyA9IHRoaXMuJGJpZygwKQ0KICAgICAgICBjb25zdCBhcnJheSA9IFtdDQogICAgICAgIGZvciAoY29uc3QgaCBvZiBwbGFuQXJlYUhvdXJMaXN0KSB7DQogICAgICAgICAgaWYodXNlckNvZGUgPT09IGgudXNlckNvZGUpIHsNCiAgICAgICAgICAgIG5pY2tOYW1lID0gaC5uaWNrTmFtZQ0KICAgICAgICAgICAgYXJyYXkucHVzaChoKQ0KICAgICAgICAgICAgbWludXRlcyA9IHRoaXMuYWRkKG1pbnV0ZXMsaC5taW51dGVzKQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICB1c2VyQXJyYXkucHVzaCh7DQogICAgICAgICAgdXNlckNvZGUsDQogICAgICAgICAgbmlja05hbWUsDQogICAgICAgICAgbWludXRlcywNCiAgICAgICAgICBhcnJheSwNCiAgICAgICAgfSkNCiAgICAgICAgc3VtTWludXRlcyA9IHRoaXMuYWRkKHN1bU1pbnV0ZXMsbWludXRlcykNCiAgICAgIH0NCiAgICAgIGZvcm0ubWludXRlcyA9IHN1bU1pbnV0ZXMudG9OdW1iZXIoKQ0KICAgICAgdGhpcy51c2VyQXJyYXkgPSB1c2VyQXJyYXkNCg0KICAgICAgYXdhaXQgdGhpcy5idWlsZFByb2R1Y3Rpb25Mb2coKS8v6L+Z5Liq6YeM6Z2i55qEb3RoZXJBcnJheSxidWlsZE1hdGVyaWFsQXJyYXnnlKjliLDkuoYNCiAgICAgIGF3YWl0IHRoaXMuYnVpbGRNYXRlcmlhbEFycmF5KCkvL+i/memHjOmcgOimgeeUqOWIsCBhcmVhTGlzdCDnmoTmlbDmja4NCiAgICAgIGF3YWl0IHRoaXMuYnVpbGRRY0xvZ3MoKQ0KDQogICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgIH0sDQogICAgYXN5bmMgc3VibWl0Rm9ybSgpIHsNCiAgICAgIGxldCBmb3JtID0gT2JqZWN0LmFzc2lnbih7fSwgdGhpcy5mb3JtKQ0KDQogICAgICBmb3JtLm1hdGVyaWFsQXJyYXkgPSBKU09OLnN0cmluZ2lmeSh0aGlzLm1hdGVyaWFsQXJyYXkpDQoNCiAgICAgIGlmIChmb3JtLmlkICE9IG51bGwpIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlDQogICAgICAgICAgYXdhaXQgdXBkYXRlTWVzTG9nKGZvcm0pDQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpDQogICAgICAgICAgdGhpcy4kcGFyZW50LiRwYXJlbnQub3BlbiA9IGZhbHNlDQogICAgICAgICAgYXdhaXQgdGhpcy4kcGFyZW50LiRwYXJlbnQuZ2V0TGlzdCgpDQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgfSwNCn0NCg=="}, {"version": 3, "sources": ["save.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "save.vue", "sourceRoot": "src/views/production/mesLog", "sourcesContent": ["<template>\r\n  <div v-loading=\"loading\" >\r\n    <el-row>\r\n      <el-col :span=\"8\">\r\n        <el-row >\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">计划日期</div>\r\n            <div class=\"content\">{{form.workDate}}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">班次</div>\r\n            <div class=\"content\">{{selectOptionsLabel(sailingsOptions, form.sailings)}}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">区域</div>\r\n            <div class=\"content\">{{form.areaNo}}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">设备</div>\r\n            <div class=\"content\">{{form.equipmentNo}}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\" style=\"width: 100px\">线长</div>\r\n            <div class=\"content\">{{ form.lineLeader }}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">生产数量</div>\r\n            <div class=\"content\">{{form.productNums}}</div>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n      <el-col :span=\"8\" class=\"header-wrapper\" >\r\n        <div class=\"header-title\">{{form.code}}</div>\r\n        <div class=\"header-img\"></div>\r\n        <div class=\"header-text\">{{form.startTime}}~{{form.endTime}}</div>\r\n      </el-col>\r\n      <el-col :span=\"8\" >\r\n        <el-row >\r\n          <el-col :span=\"24\" class=\"cell-wrapper\">\r\n            <div class=\"label\">品名</div>\r\n            <div class=\"content\">{{form.productName}}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">品号</div>\r\n            <div class=\"content\">{{form.productNo}}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\" style=\"width: 100px\">客户订单号</div>\r\n            <div class=\"content\">{{ form.customerOrderNo }}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\" style=\"width: 100px\">客户名称</div>\r\n            <div class=\"content\">{{ form.customerOrderNo }}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">\r\n              产出占比\r\n              <el-tooltip content=\"良品量 / 工单量\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n              </el-tooltip>\r\n            </div>\r\n            <div class=\"content\" v-if=\"schedule.planNums\">{{toPercent(divide(form.productNums,schedule.planNums))}}</div>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-tabs v-model=\"currentTab\" type=\"border-card\" style=\"margin-top: 20px\" @tab-click=\"tabChange\" >\r\n      <el-tab-pane label=\"人员/工时\" lazy name=\"person\" key=\"person\" >\r\n        <MesLogArea\r\n          v-if=\"form.minutes && form.inNums\"\r\n          :form=\"form\"\r\n          :mes-lot-wait-list=\"mesLotWaitList\"\r\n          :lot-logs=\"lotLogs\"\r\n          :out-array=\"outArray\"\r\n          :user-array=\"userArray\"\r\n        />\r\n      </el-tab-pane>\r\n      <el-tab-pane label=\"工艺标准\" lazy name=\"standard\" key=\"standard\" >\r\n        <ProcessTable\r\n          :project=\"project\"\r\n          :bom-data=\"bomData\"\r\n          :bom-tree=\"bomTree\"\r\n          :readonly=\"true\" />\r\n      </el-tab-pane>\r\n      <el-tab-pane key=\"material\" label=\"物料\" lazy name=\"material\">\r\n        <MesLogMaterialTabs :form=\"form\" :material-log-list=\"materialLogArray\" />\r\n      </el-tab-pane>\r\n      <el-tab-pane key=\"charts\" label=\"产线报表\" lazy name=\"charts\" >\r\n        <MesLogDataCharts ref=\"mesDataCharts\" :diff-data-array=\"diffDataArray\" />\r\n      </el-tab-pane>\r\n      <el-tab-pane key=\"equipment\" label=\"数采看板\" lazy name=\"equipment\" >\r\n\r\n      </el-tab-pane>\r\n      <el-tab-pane key=\"schedule\" label=\"工单维度分摊\" lazy name=\"schedule\" >\r\n        <MesLogScheduleTable\r\n          :plan=\"form\"\r\n          :bcp-list=\"bcpList\"\r\n          :bcp-array=\"scheduleBcpArray\"\r\n          :other-array=\"scheduleOtherArray\"\r\n        />\r\n      </el-tab-pane>\r\n      <el-tab-pane key=\"report\" label=\"生产记录\" lazy name=\"report\" >\r\n        <MesLogProductionLog\r\n          :plan=\"form\"\r\n          :bcp-list=\"bcpList\"\r\n          :bcp-array=\"bcpArray\"\r\n          :other-array=\"otherArray\"\r\n          :material-array=\"materialArray\"\r\n        />\r\n      </el-tab-pane>\r\n      <el-tab-pane key=\"qc\" label=\"品质检验\" lazy name=\"qc\" >\r\n        <MesQc ref=\"mesQc\" :inspection-list=\"inspectionList\" />\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n\r\n    <div class=\"dialog-footer\" style=\"margin-top: 20px\" v-if=\"!readonly\" >\r\n      <el-button type=\"primary\" @click=\"submitForm\" size=\"mini\" :loading=\"btnLoading\" >\r\n        确 定\r\n        <el-tooltip content=\"目前只有物料信息的备注需要保存,其他无需操作\" placement=\"top\">\r\n          <i class=\"el-icon-question\"></i>\r\n        </el-tooltip>\r\n      </el-button>\r\n    </div>\r\n\r\n  </div>\r\n</template>\r\n<script >\r\n\r\nimport {\r\n  addMesLog,\r\n  getMesLog,\r\n  updateMesLog\r\n} from \"@/api/production/mesLog\";\r\nimport {allPlanAreaHours} from \"@/api/production/planAreaHours\";\r\nimport {allWipContPartialOut} from \"@/api/mes/wipcontPartialout\";\r\nimport MesLogArea from \"@/views/production/mesLog/area.vue\";\r\nimport {allWipLotLog} from \"@/api/mes/mesView\";\r\nimport ProcessTable from \"@/views/production/layout/process/table.vue\";\r\nimport {getArchive} from \"@/api/project/archive\";\r\nimport {getProject} from \"@/api/project/project\";\r\nimport {getScheduleStandardByCode} from \"@/api/order/scheduleStandard\";\r\nimport MesQc from \"@/views/mes/production/qc.vue\";\r\nimport {allFinishedInspection, allGroupByBatchFinishedInspection} from \"@/api/qc/finishedInspection\";\r\nimport {allGroupByEquipmentNo} from \"@/api/mes/wipContMaterialLot\";\r\nimport {getBomByErpCode, getScheduleMaterial} from \"@/api/common/erp\";\r\nimport MesLogDataCharts from \"@/views/production/mesLog/dataCharts.vue\";\r\nimport {getGzlByParams} from \"@/api/sop/gzl\";\r\nimport {getMaterialLogList} from \"@/api/production/mesLog\";\r\nimport {getScheduleByCode} from \"@/api/order/schedule\";\r\nimport MesLogMaterialTabs from \"@/views/production/mesLog/materialTabs.vue\";\r\nimport MesLogProductionLog from \"@/views/production/mesLog/log.vue\";\r\nimport MesProductionLog from \"@/views/mes/production/log.vue\";\r\nimport MesLogScheduleTable from \"@/views/production/mesLog/scheduleTable.vue\";\r\n\r\nexport default {\r\n  name: 'mesLogSave',\r\n  components: {\r\n    MesLogScheduleTable,\r\n    MesProductionLog,\r\n    MesLogProductionLog,\r\n    MesLogMaterialTabs,\r\n    MesLogDataCharts,\r\n    MesQc,\r\n    ProcessTable,\r\n    MesLogArea\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      btnLoading: false,\r\n      readonly: false,\r\n      form: {},\r\n      rules: {},\r\n      currentTab: '',\r\n      sailingsOptions: [\r\n        {label: '白班',value: '0'},\r\n        {label: '晚班',value: '1'},\r\n      ],\r\n      outArray: [],\r\n      lotLogs: [],\r\n      mesLotWaitList: [],\r\n      userArray: [],\r\n      project: {},\r\n      bomData: [],\r\n      bomTree: [],\r\n      diffDataArray: [],\r\n      diffDataOptions: {},\r\n      inspectionList: [],\r\n      qcOptions: {},\r\n      materialLogArray: [],\r\n      scheduleStandard: {},\r\n      materialArray: [],\r\n      schedule: {},\r\n      bcpList: [],\r\n      bcpArray: [],\r\n      otherArray: [],\r\n      scheduleBcpArray: [],\r\n      scheduleOtherArray: [],\r\n    }\r\n  },\r\n  async created() {\r\n  },\r\n  methods: {\r\n    async tabChange() {\r\n      await this.$nextTick()\r\n      if(this.currentTab === 'person') {\r\n\r\n      } else if(this.currentTab === 'charts') {\r\n        await this.reduceDiffChart()\r\n      } else if(this.currentTab === 'qc') {\r\n        await this.reduceQcChart()\r\n      }\r\n    },\r\n    cancel() {\r\n      this.$parent.$parent.open = false;\r\n      this.reset();\r\n    },\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        code: null,\r\n        workDate: null,\r\n        sailings: null,\r\n        areaNo: null,\r\n        lineLeader: null,\r\n        minutes: null,\r\n        inNums: null,\r\n        goodsNums: null,\r\n        badNums: null,\r\n      };\r\n      this.resetForm(\"form\")\r\n      this.project = {}\r\n      this.bomData = []\r\n      this.bomTree = []\r\n      this.materialLogArray = []\r\n      this.outArray = []\r\n      this.lotLogs = []\r\n      this.mesLotWaitList = []\r\n      this.userArray = []\r\n      this.diffDataArray = []\r\n      this.diffDataOptions = {}\r\n      this.inspectionList = []\r\n      this.qcOptions = {}\r\n      this.schedule = {}\r\n      this.scheduleStandard = {}\r\n      this.materialArray = []\r\n      this.bcpList = []\r\n      this.bcpArray = []\r\n      this.otherArray = []\r\n      this.scheduleBcpArray = []\r\n      this.scheduleOtherArray = []\r\n    },\r\n    diffHours(startTime,endTime) {\r\n      if(startTime && endTime) {\r\n        let minutes = this.moment(endTime,'YYYY-MM-DD hh:mm').diff(this.moment(startTime,'YYYY-MM-DD hh:mm'), 'minutes')\r\n        if(minutes) {\r\n          return this.divide(minutes,60).toNumber()\r\n        }\r\n      }\r\n    },\r\n    async reduceQcChart() {\r\n      await this.$nextTick()\r\n      const mesQc = this.$refs.mesQc\r\n      if(mesQc) {\r\n        const qcCharts = mesQc.$refs.qcCharts\r\n        if(qcCharts) {\r\n          await qcCharts.init(this.qcOptions)\r\n        }\r\n      }\r\n    },\r\n    async reduceDiffChart() {\r\n      await this.$nextTick()\r\n      const mesDataCharts = this.$refs.mesDataCharts\r\n      if(mesDataCharts) {\r\n        const diffChart = mesDataCharts.$refs.diffChart\r\n        if(diffChart) {\r\n          await diffChart.init(this.diffDataOptions)\r\n        }\r\n        const timeLineChart = mesDataCharts.$refs.timeLineChart\r\n        if(timeLineChart) {\r\n        }\r\n      }\r\n    },\r\n    async buildProductionLog() {\r\n      const form = this.form\r\n      const materialArray = this.materialArray\r\n      const bomRes = await getBomByErpCode(form.productNo)\r\n      if(bomRes.code === 200) {\r\n        let bcpList = bomRes.data.filter(i => i.mb005 === '103')\r\n        for (const item of bcpList) {\r\n          const gzlParams = {\r\n            md003: item.md003,\r\n            md001: item.md001,\r\n          }\r\n          const gzlRes = await getGzlByParams(gzlParams)\r\n          if(gzlRes.code === 200 && gzlRes.data) {\r\n            const gzl = gzlRes.data\r\n            item.max = gzl.max\r\n            item.avg = gzl.avg\r\n            item.min = gzl.min\r\n          }\r\n        }\r\n        this.bcpList = bcpList\r\n      }\r\n\r\n      let res = await getMaterialLogList(form.id)\r\n      if(res){\r\n        const bcpArray = res.bcpArray\r\n        const otherArray = res.otherArray\r\n        const scheduleBcpArray = res.scheduleBcpArray\r\n        const scheduleOtherArray = res.scheduleOtherArray\r\n        console.log(scheduleBcpArray)\r\n        console.log(scheduleOtherArray)\r\n        if(bcpArray){\r\n          this.bcpArray = bcpArray\r\n          for (const o of bcpArray) {\r\n            if(!materialArray.map(i=> i.materialCode).includes(o.materialCode)) {\r\n              materialArray.push({\r\n                materialCode: o.materialCode,\r\n                remark: null,\r\n              })\r\n            }\r\n          }\r\n        }\r\n        if(otherArray){\r\n          this.otherArray = otherArray\r\n          for (const o of otherArray) {\r\n            if(!materialArray.map(i=> i.materialCode).includes(o.materialCode)) {\r\n              materialArray.push({\r\n                materialCode: o.materialCode,\r\n                remark: null,\r\n              })\r\n            }\r\n          }\r\n        }\r\n        if(scheduleBcpArray) {\r\n          this.scheduleBcpArray = scheduleBcpArray\r\n        }\r\n        if(scheduleOtherArray) {\r\n          this.scheduleOtherArray = scheduleOtherArray\r\n        }\r\n      }\r\n    },\r\n    async buildMaterialArray() {\r\n      const form = this.form\r\n      const schedule = this.schedule\r\n      const formRate = this.divide(form.productNums,schedule.planNums)//产出占比\r\n      if(formRate) {\r\n        let scheduleCode = form.scheduleCode\r\n        const materialLogList = await allGroupByEquipmentNo({scheduleCode,workDate: form.workDate})//暂时不算班次\r\n        this.materialLogList = materialLogList\r\n        const arr = scheduleCode.split('-')\r\n        let erpBomData = await getScheduleMaterial({workOrderNo: arr[1], workOrderSingle: arr[0],})\r\n\r\n        const scheduleNums = schedule.planNums\r\n        const materialNoSet = new Set()\r\n        for (const log of materialLogList) {\r\n          materialNoSet.add(log.materialNo)\r\n        }\r\n\r\n        const materialLogArray = []\r\n        for (const materialNo of materialNoSet) {\r\n          const te010Set = new Set()//在品号维度下再排批次\r\n          let sjBomNum = 0 //bom用量对于工单产品是成品的\r\n          let cb010 = 0//erp损耗率\r\n          let materialName\r\n          let te006//erp单位\r\n          let te005Sum = this.$big(0)//工单发料量小计\r\n          let lotSums = this.$big(0)//批次使用量小计\r\n          let erpSylSum = this.$big(0)//erp品号维度使用量小计\r\n          for (const b of erpBomData) {\r\n            if(b.te004 === materialNo) {\r\n              sjBomNum = Number(b.sjBomNum)\r\n              cb010 = Number(b.cb010)\r\n              materialName = b.te017\r\n              te006 = b.te006\r\n\r\n              te005Sum = this.add(te005Sum,b.te005)\r\n              te010Set.add(b.te010)//按照e10的批次来\r\n            }\r\n          }\r\n          for (const log of materialLogList) {\r\n            if(materialNo === log.materialNo) {\r\n              lotSums = this.add(lotSums,log.nums)\r\n            }\r\n          }\r\n          const te010Array = []\r\n          for (const te010 of te010Set) {\r\n            let erpLlNums = 0\r\n            let erpSylNums = 0\r\n            for (const ot of this.otherArray) {\r\n              if(ot.materialCode === materialNo ) {\r\n                for (const t of ot.te010Array) {\r\n                  if(t.te010 === te010) {\r\n                    if(t.gdSylNums) {\r\n                      erpLlNums = Number(t.gdSylNums)\r\n                      erpSylNums = Number(t.gdSylNums)\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n            const equipmentSet = new Set()//品号批次内排设备\r\n            for (const log of materialLogList) {\r\n              if(materialNo === log.materialNo && te010 === log.materialLotNo) {\r\n                equipmentSet.add(log.equipmentNo)\r\n              }\r\n            }\r\n            const equipmentArray = []\r\n            for (const equipmentNo of equipmentSet) {\r\n              let l = {\r\n                equipmentNo,\r\n              }\r\n              for (const log of materialLogList) {\r\n                if(materialNo === log.materialNo && te010 === log.materialLotNo && equipmentNo === log.equipmentNo) {\r\n                  l.nums = log.nums//使用量\r\n                  l.times = log.times//领料次数\r\n                }\r\n              }\r\n              equipmentArray.push(l)\r\n            }\r\n            const llArray = []//erp领料单记录\r\n            for (const b of erpBomData) {\r\n              if(b.te004 === materialNo && b.te010 === te010) {\r\n                let sylRate\r\n                let llBlNums\r\n                let llBlReason\r\n                let scBlNums\r\n                let scBlReason\r\n                for (const ot of this.otherArray) {\r\n                  if(ot.materialCode === materialNo ) {\r\n                    for (const t of ot.te010Array) {\r\n                      if(t.te010 === te010) {\r\n                        sylRate = t.sylRate\r\n                        llBlNums = t.llBlNums\r\n                        llBlReason = t.llBlReason\r\n                        scBlNums = t.scBlNums\r\n                        scBlReason = t.scBlReason\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n                llArray.push({\r\n                  te001: b.te001,//领料通知单别\r\n                  te002: b.te002,//领料通知单号\r\n                  te008: b.te008,//仓库\r\n                  me003: b.me003,//最早入库日期\r\n                  te013: b.te013,//领料说明\r\n                  sylRate,//使用量比例(生产批批次品号使用量/工单品号批次使用量)\r\n                  llBlNums,//来料不良\r\n                  llBlReason,\r\n                  scBlNums,//生产不良\r\n                  scBlReason,\r\n                })\r\n              }\r\n            }\r\n            te010Array.push({\r\n              te010,\r\n              erpLlNums: erpLlNums,//领料量\r\n              erpSylNums: erpSylNums,//使用量\r\n              equipmentArray,\r\n              llArray,\r\n            })\r\n            erpSylSum = this.add(erpSylSum,erpSylNums)\r\n          }\r\n          if(scheduleNums) {\r\n            let gdXql = this.multiply(scheduleNums,sjBomNum)\r\n            let lotPlanXql = this.multiply(form.productNums,sjBomNum)//生产记录实际生产数量 计算 本线需求量\r\n            let lotLlXql = this.multiply(form.productNums,sjBomNum)//批次生产量计算 理论使用量\r\n            let loss = this.subtract(lotSums, lotLlXql)\r\n            const erpRateNums = this.multiply(erpSylSum,formRate).toNumber()\r\n            materialLogArray.push({\r\n              materialNo,\r\n              materialName,\r\n              sjBomNum,\r\n              cb010,\r\n              te006,\r\n              gdXql: gdXql.toNumber(),\r\n              te005Sum: te005Sum.toNumber(),\r\n              xfCyRate: this.divide(this.subtract(te005Sum, gdXql), gdXql).toNumber(),\r\n              lotXql: lotPlanXql.toNumber(),\r\n              lotLlXql: lotLlXql.toNumber(),\r\n              lotSums: lotSums.toNumber(),\r\n              loss: loss.toNumber(),\r\n              lotRate: this.divide(loss, lotLlXql).toNumber(),\r\n              erpSylSum,\r\n              erpRateNums,\r\n              diffNums: this.subtract(lotSums, erpRateNums).toNumber(),\r\n              te010Array,\r\n            })\r\n          }\r\n        }\r\n\r\n        this.materialLogArray = materialLogArray\r\n      }\r\n    },\r\n    async buildDiffData() {\r\n      const form = this.form\r\n      const scheduleStandard = this.scheduleStandard\r\n\r\n      const productNums = form.productNums\r\n      const actualHours = this.divide(form.sumMinutes,60)\r\n      const actualDuration = this.diffHours(form.startTime,form.endTime)\r\n      if(actualDuration && scheduleStandard.nums) {\r\n        const actualProductivity = this.divide(productNums,actualDuration).toNumber()\r\n        const actualHoursRate = this.divide(productNums, actualHours).toNumber()\r\n        const diffDataArray = []\r\n\r\n        let standardHours = 0\r\n        let standardPersonNums = 0\r\n        let standardHoursRate = 0\r\n        if(form.opNo === 'GB') {//一阶 取少的\r\n          standardPersonNums = scheduleStandard.nums\r\n          standardHoursRate = scheduleStandard.costHoursRate\r\n        } else {\r\n          standardPersonNums = scheduleStandard.costNums\r\n          standardHoursRate = scheduleStandard.costHoursRate\r\n        }\r\n        standardHours = this.multiply(standardHoursRate,form.productNums).toNumber()\r\n        diffDataArray.push(['标准',scheduleStandard.productivity,standardPersonNums,standardHours,standardHoursRate])\r\n        diffDataArray.push(['实际',actualProductivity,form.sumNums,actualHours,actualHoursRate])\r\n        diffDataArray.push([\r\n          '差异',\r\n          this.subtract(actualProductivity,scheduleStandard.productivity).toNumber(),\r\n          this.subtract(form.sumNums,standardPersonNums).toNumber(),\r\n          this.subtract(actualHours,standardHours).toNumber(),\r\n          this.subtract(actualHoursRate,standardHoursRate).toNumber(),\r\n        ])\r\n        this.diffDataArray = diffDataArray\r\n\r\n        this.diffDataOptions =  {\r\n          title: {\r\n            text: '批次维度数据对比'\r\n          },\r\n          legend: {\r\n            data: ['预估', '实际']\r\n          },\r\n          radar: {\r\n            indicator: [\r\n              { name: '生产产能', max: Math.max(scheduleStandard.productivity, actualProductivity)*1.2 },\r\n              { name: '生产人数', max: Math.max(standardPersonNums, form.sumNums)*1.2 },\r\n              { name: '生产工时', max: Math.max(standardHours, actualHours)*1.2 },\r\n              { name: '工时产出率', max: Math.max(standardHoursRate, actualHoursRate)*1.2 }\r\n            ]\r\n          },\r\n          series: [\r\n            {\r\n              name: '预估 vs 实际',\r\n              type: 'radar',\r\n              data: [\r\n                {\r\n                  value: diffDataArray[0].slice(1),\r\n                  name: '预估'\r\n                },\r\n                {\r\n                  value: diffDataArray[1].slice(1),\r\n                  name: '实际'\r\n                }\r\n              ]\r\n            }\r\n          ]\r\n        }\r\n        // await this.reduceDiffChart()\r\n      }\r\n    },\r\n    async importArchive(archiveId) {\r\n      let archiveRes = await getArchive(archiveId)\r\n      if(archiveRes.code === 200 && archiveRes.data) {\r\n        let archive = archiveRes.data\r\n\r\n        const projectRes = await getProject(archive.projectId)\r\n        const project = projectRes.data\r\n\r\n        project.type = archive.type\r\n\r\n        if(['0','1','3'].includes(archive.type)) {\r\n          project.bomResource = archive.bomResource\r\n          project.bomType = archive.bomType\r\n          project.resourceFinishedGoodsId = archive.resourceFinishedGoodsId\r\n          project.erpPrice = archive.erpPrice\r\n          project.bomArray = archive.bomArray//这里就是字符串\r\n          project.erpCode = archive.erpCode\r\n          if(archive.cubicleArray) {\r\n            const cubicleArray = JSON.parse(archive.cubicleArray)\r\n            project.cubicleArray = cubicleArray\r\n\r\n            const imgs = []\r\n            const videos = []\r\n            for (const item of cubicleArray) {\r\n              for (const second of item.sectionArray) {\r\n                for (const w of second.workTypeArray) {\r\n                  if(w.homeworkImgs) {\r\n                    imgs.push(...w.homeworkImgs.split(','))\r\n                  }\r\n                  if(w.homeworkVideos.length) {\r\n                    videos.push(...w.homeworkVideos)\r\n                  }\r\n                }\r\n              }\r\n            }\r\n            this.imgs = imgs\r\n            this.videos = videos\r\n          } else {\r\n            project.cubicleArray = []\r\n          }\r\n        }\r\n        this.bomData = JSON.parse(archive.bomArray)\r\n        this.project = project\r\n      }\r\n    },\r\n    async buildQcLogs() {\r\n      const form = this.form\r\n      const arr = form.scheduleCode.split('-')\r\n      const inspectionList = await allFinishedInspection({singleCategory: arr[0],workOrderNo:arr[1]})//质检\r\n      for(let item of inspectionList){\r\n        let erpColumn = item.erpColumn;\r\n        if(erpColumn){\r\n          let obj = JSON.parse(erpColumn);\r\n          item.tg030 = obj.tg030;\r\n        }\r\n      }\r\n      this.inspectionList = inspectionList\r\n\r\n      const groupInspectionList = await allGroupByBatchFinishedInspection({singleCategory: arr[0],workOrderNo:arr[1]})\r\n\r\n      const rawData = [\r\n        groupInspectionList.map(i=> i.batchNums - i.samplingNums),\r\n        groupInspectionList.map(i=> i.samplingNums),\r\n      ];\r\n      const totalData = [];\r\n      for (let i = 0; i < rawData[0].length; ++i) {\r\n        let sum = 0;\r\n        for (let j = 0; j < rawData.length; ++j) {\r\n          sum += rawData[j][i];\r\n        }\r\n        totalData.push(sum);\r\n      }\r\n      const grid = {\r\n        left: 100,\r\n        right: 100,\r\n        top: 50,\r\n        bottom: 50\r\n      };\r\n      const series = [\r\n        '未抽样',\r\n        '已抽样',\r\n      ].map((name, sid) => {\r\n        return {\r\n          name,\r\n          type: 'bar',\r\n          stack: 'total',\r\n          barWidth: '60%',\r\n          label: {\r\n            show: true,\r\n            formatter: (params) => Math.round(params.value * 1000) / 10 + '%'\r\n          },\r\n          data: rawData[sid].map((d, did) =>\r\n            totalData[did] <= 0 ? 0 : d / totalData[did]\r\n          )\r\n        }\r\n      })\r\n      this.qcOptions = {\r\n        legend: {\r\n          selectedMode: false\r\n        },\r\n        grid,\r\n        yAxis: {\r\n          type: 'value'\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: groupInspectionList.map(i=> i.batchNo)\r\n        },\r\n        series\r\n      }\r\n    },\r\n    async init(id) {\r\n      this.loading = true\r\n      const res = await getMesLog(id)\r\n      const form = res.data\r\n      if(form.materialArray) {\r\n        this.materialArray = JSON.parse(form.materialArray)\r\n      }\r\n      this.form = form\r\n\r\n      const scheduleCode = form.scheduleCode\r\n      const arr = scheduleCode.split('-')\r\n      const scheduleRes = await getScheduleByCode(arr[0] + \"-\" + arr[1])\r\n      if(scheduleRes.code === 200) {\r\n        const schedule = scheduleRes.data\r\n        this.schedule = schedule\r\n      }\r\n\r\n      const scheduleStandardRes = await getScheduleStandardByCode(form.productNo)\r\n      if(scheduleStandardRes.code === 200) {\r\n        const scheduleStandard = scheduleStandardRes.data\r\n        this.scheduleStandard = scheduleStandard\r\n        if(scheduleStandard) {\r\n          await this.importArchive(scheduleStandard.archiveId)\r\n          await this.buildDiffData()\r\n        }\r\n      }\r\n\r\n      const outList = await allWipContPartialOut({\r\n        areano: form.areaNo,\r\n        eventDate: form.workDate,\r\n        equipmentno: form.equipmentNo,\r\n        scheduleCode: form.scheduleCode,\r\n      })\r\n      let inNums = this.$big(0)\r\n      let goodsNums = this.$big(0)\r\n      let badNums = this.$big(0)\r\n      this.outArray = outList.map(i=> {\r\n        return {\r\n          lotNo: i.lotno,\r\n          opNo: i.opno,\r\n          eventTime: i.eventtime,\r\n          userNo: i.userno,\r\n          areaNo: i.areano,\r\n          inNums: i.inputqty,//产出量\r\n          goodsNums: i.goodqty,//良品量\r\n          badNums: i.scrapqty,//良品量\r\n          equipmentNo: i.equipmentno,\r\n          userName: i.userName,\r\n        }\r\n      })//重命名属性名称,方便理解语义\r\n\r\n      for (const item of this.outArray) {\r\n        inNums = this.add(inNums,item.inNums)\r\n        goodsNums = this.add(goodsNums,item.goodsNums)\r\n        badNums = this.add(badNums,item.badNums)\r\n      }\r\n\r\n      form.inNums = inNums.toNumber()\r\n      form.goodsNums = goodsNums.toNumber()\r\n      form.badNums = badNums.toNumber()\r\n\r\n      const lotLogs = await allWipLotLog({\r\n        areaNo: form.areaNo,\r\n        createTime: form.workDate,\r\n        equipmentNo: form.equipmentNo,\r\n        scheduleCode: form.scheduleCode,\r\n      })\r\n      this.lotLogs = lotLogs\r\n\r\n      const planAreaHourList = await allPlanAreaHours({\r\n        areaNo: form.areaNo,\r\n        equipmentNo: form.equipmentNo,\r\n        workDate: form.workDate,\r\n        sailings: form.sailings,\r\n        scheduleCode: form.scheduleCode,\r\n      })\r\n\r\n      const userArray = []\r\n      const userSet = new Set(planAreaHourList.map(i=> i.userCode))\r\n      let sumMinutes = this.$big(0)\r\n      for (const userCode of userSet) {\r\n        let nickName\r\n        let minutes = this.$big(0)\r\n        const array = []\r\n        for (const h of planAreaHourList) {\r\n          if(userCode === h.userCode) {\r\n            nickName = h.nickName\r\n            array.push(h)\r\n            minutes = this.add(minutes,h.minutes)\r\n          }\r\n        }\r\n        userArray.push({\r\n          userCode,\r\n          nickName,\r\n          minutes,\r\n          array,\r\n        })\r\n        sumMinutes = this.add(sumMinutes,minutes)\r\n      }\r\n      form.minutes = sumMinutes.toNumber()\r\n      this.userArray = userArray\r\n\r\n      await this.buildProductionLog()//这个里面的otherArray,buildMaterialArray用到了\r\n      await this.buildMaterialArray()//这里需要用到 areaList 的数据\r\n      await this.buildQcLogs()\r\n\r\n      this.loading = false\r\n    },\r\n    async submitForm() {\r\n      let form = Object.assign({}, this.form)\r\n\r\n      form.materialArray = JSON.stringify(this.materialArray)\r\n\r\n      if (form.id != null) {\r\n        try {\r\n          this.btnLoading = true\r\n          await updateMesLog(form)\r\n          this.btnLoading = false\r\n          this.msgSuccess(\"修改成功\")\r\n          this.$parent.$parent.open = false\r\n          await this.$parent.$parent.getList()\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n  },\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.cell-wrapper {\r\n  .label {\r\n    width: 80px;\r\n  }\r\n}\r\n\r\n.header-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  .header-title {\r\n    font-size: 16px;\r\n    font-weight: 700;\r\n  }\r\n\r\n  .header-img {\r\n    background: url(~@/assets/images/production/plan/layout/head.gif) no-repeat center center;\r\n    background-size: 100%;\r\n    height: 10vh;\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* el-tabs */\r\n::v-deep .el-tabs__nav-scroll{\r\n  background-color: #fff;\r\n  padding: 20px 0;\r\n}\r\n::v-deep .el-tabs__nav {\r\n  margin: 0 20px;\r\n  /* 使用rpx没有效果 */\r\n}\r\n\r\n::v-deep .el-tabs__nav-scroll {\r\n  padding: 10px;\r\n}\r\n\r\n::v-deep .el-tabs__content {\r\n  padding-top: 0;\r\n}\r\n</style>\r\n"]}]}