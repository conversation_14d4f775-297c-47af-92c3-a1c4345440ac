import request from '@/utils/request'
// 查询劳务工资计算合计
export function totalWagesLaborCalculateMonth(query) {
  return request({
    url: '/hr/wagesLaborCalculate/monthTotal',
    method: 'get',
    params: query
  })
}
// 查询劳务工资计算列表
export function listWagesLaborCalculateMonth(query) {
  return request({
    url: '/hr/wagesLaborCalculate/month',
    method: 'get',
    params: query
  })
}
// 查询劳务工资计算列表
export function listWagesLaborCalculate(query) {
  return request({
    url: '/hr/wagesLaborCalculate/list',
    method: 'get',
    params: query
  })
}

// 查询劳务工资计算详细
export function getWagesLaborCalculate(id) {
  return request({
    url: '/hr/wagesLaborCalculate/' + id,
    method: 'get'
  })
}
export function statisticsWagesLaborCalculate(query) {
  return request({
    url: '/hr/wagesLaborCalculate/statistics',
    method: 'get',
    params: query
  })
}

// 新增劳务工资计算
export function addWagesLaborCalculate(data) {
  return request({
    url: '/hr/wagesLaborCalculate',
    method: 'post',
    data: data
  })
}

// 修改劳务工资计算
export function updateWagesLaborCalculate(data) {
  return request({
    url: '/hr/wagesLaborCalculate',
    method: 'put',
    data: data
  })
}

// 删除劳务工资计算
export function delWagesLaborCalculate(id) {
  return request({
    url: '/hr/wagesLaborCalculate/' + id,
    method: 'delete'
  })
}
export function pigeonholeWagesLaborCalculate(id) {
  return request({
    url: '/hr/wagesLaborCalculate/pigeonhole/' + id,
    method: 'delete'
  })
}

// 导出劳务工资计算
export function exportWagesLaborCalculate(query) {
  return request({
    url: '/hr/wagesLaborCalculate/export',
    method: 'get',
    params: query
  })
}
export function getProcessWagesLaborCalculate(query) {
  return request({
    url: '/hr/wagesLaborCalculate/getProcess',
    method: 'get',
    params: query
  })
}
export function processWagesLaborCalculate(data) {
  return request({
    url: '/hr/wagesLaborCalculate/process',
    method: 'post',
    data: data
  })
}
