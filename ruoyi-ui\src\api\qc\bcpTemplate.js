import request from '@/utils/request'

// 查询半成品检验模板列表
export function listBcpTemplate(query) {
  return request({
    url: '/qc/bcpTemplate/list',
    method: 'get',
    params: query
  })
}

// 查询半成品检验模板详细
export function getBcpTemplate(id) {
  return request({
    url: '/qc/bcpTemplate/' + id,
    method: 'get'
  })
}

// 新增半成品检验模板
export function addBcpTemplate(data) {
  return request({
    url: '/qc/bcpTemplate',
    method: 'post',
    data: data
  })
}

// 修改半成品检验模板
export function updateBcpTemplate(data) {
  return request({
    url: '/qc/bcpTemplate',
    method: 'put',
    data: data
  })
}

// 删除半成品检验模板
export function delBcpTemplate(id) {
  return request({
    url: '/qc/bcpTemplate/' + id,
    method: 'delete'
  })
}

// 导出半成品检验模板
export function exportBcpTemplate(query) {
  return request({
    url: '/qc/bcpTemplate/export',
    method: 'get',
    params: query
  })
}

export function allBcpTemplate(query) {
  return request({
    url: '/qc/bcpTemplate/all',
    method: 'get',
    params: query
  })
}
