import request from '@/utils/request'

export function listOutUserLeadershipSee(query) {
  return request({
    url: '/hr/outUser/leadershipSee',
    method: 'get',
    params: query
  })
}
// 查询外出用户申请列表
export function listOutUser(query) {
  return request({
    url: '/hr/outUser/list',
    method: 'get',
    params: query
  })
}
// 查询外出用户申请审批列表
export function listAuditOutUser(query) {
  return request({
    url: '/hr/outUser/audit',
    method: 'get',
    params: query
  })
}
export function chooseOutUser(query) {
  return request({
    url: '/hr/outUser/choose',
    method: 'get',
    params: query
  })
}
export function logOutUser(query) {
  return request({
    url: '/hr/outUser/log',
    method: 'get',
    params: query
  })
}
// 查询外出用户申请详细
export function getOutUser(id) {
  return request({
    url: '/hr/outUser/' + id,
    method: 'get'
  })
}
export function getOutUsage(params) {
  return request({
    url: '/hr/outUser/usage',
    method: 'get',
    params: params
  })
}
// 新增外出用户申请
export function addOutUser(data) {
  return request({
    url: '/hr/outUser',
    method: 'post',
    data: data
  })
}


// 删除外出用户申请
export function delOutUser(id) {
  return request({
    url: '/hr/outUser/' + id,
    method: 'delete'
  })
}

export function copyOutUser(query) {
  return request({
    url: '/hr/outUser/copy',
    method: 'get',
    params: query
  })
}
export function submitAudit(data) {
  return request({
    url: '/hr/outUser/submitAudit',
    method: 'put',
    data: data
  })
}
export function cancelAudit(data) {
  return request({
    url: '/hr/outUser/cancelAudit',
    method: 'put',
    data: data
  })
}

export function pigeonholeOutUser(data) {
  return request({
    url: '/hr/outUser/pigeonhole',
    method: 'post',
    data: data
  })
}
