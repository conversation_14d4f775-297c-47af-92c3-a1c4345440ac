import request from '@/utils/request'

// 查询半成品产能标准列表
export function listBcpStandard(query) {
  return request({
    url: '/sop/bcpStandard/list',
    method: 'get',
    params: query
  })
}

// 查询半成品产能标准详细
export function getBcpStandard(id) {
  return request({
    url: '/sop/bcpStandard/' + id,
    method: 'get'
  })
}

// 新增半成品产能标准
export function addBcpStandard(data) {
  return request({
    url: '/sop/bcpStandard',
    method: 'post',
    data: data
  })
}

// 修改半成品产能标准
export function updateBcpStandard(data) {
  return request({
    url: '/sop/bcpStandard',
    method: 'put',
    data: data
  })
}

// 删除半成品产能标准
export function delBcpStandard(id) {
  return request({
    url: '/sop/bcpStandard/' + id,
    method: 'delete'
  })
}

// 导出半成品产能标准
export function exportBcpStandard(query) {
  return request({
    url: '/sop/bcpStandard/export',
    method: 'get',
    params: query
  })
}