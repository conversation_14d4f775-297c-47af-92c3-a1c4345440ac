import request from '@/utils/request'

// 查询项目包材列表
export function listBc(query) {
  return request({
    url: '/project/bc/list',
    method: 'get',
    params: query
  })
}

// 查询项目包材详细
export function getBc(id) {
  return request({
    url: '/project/bc/' + id,
    method: 'get'
  })
}

// 新增项目包材
export function addBc(data) {
  return request({
    url: '/project/bc',
    method: 'post',
    data: data
  })
}

// 修改项目包材
export function updateBc(data) {
  return request({
    url: '/project/bc',
    method: 'put',
    data: data
  })
}

export function updateBcForce(data) {
  return request({
    url: '/project/bc/editForce',
    method: 'put',
    data: data
  })
}

// 删除项目包材
export function delBc(id) {
  return request({
    url: '/project/bc/' + id,
    method: 'delete'
  })
}

// 导出项目包材
export function exportBc(query) {
  return request({
    url: '/project/bc/export',
    method: 'get',
    params: query
  })
}

export function allBc(query) {
  return request({
    url: '/project/bc/all',
    method: 'get',
    params: query
  })
}

export function allBcLog(query) {
  return request({
    url: '/project/bc/allBcLog',
    method: 'get',
    params: query
  })
}

export function allBcByOrderId(orderId) {
  return request({
    url: '/project/bc/allByOrderId/' + orderId,
    method: 'get'
  })
}

export function allProjectOrderBc(projectId) {
  return request({
    url: '/project/bc/allProjectOrderBc/' + projectId,
    method: 'get'
  })
}
