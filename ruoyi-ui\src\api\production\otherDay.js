import request from '@/utils/request'

// 查询生产间接管理日排班列表
export function listOtherDay(query) {
  return request({
    url: '/production/otherDay/list',
    method: 'get',
    params: query
  })
}

// 查询生产间接管理日排班详细
export function getOtherDay(id) {
  return request({
    url: '/production/otherDay/' + id,
    method: 'get'
  })
}

export function otherDaySubmitAudit(data) {
  return request({
    url: '/production/otherDay/submitAudit',
    method: 'put',
    data,
  })
}

export function otherDayCancelAudit(data) {
  return request({
    url: '/production/otherDay/cancelAudit',
    method: 'put',
    data,
  })
}

export function otherDaySubmitChangeAudit(data) {
  return request({
    url: '/production/otherDay/submitChangeAudit',
    method: 'put',
    data,
  })
}

export function otherDayCancelChangeAudit(data) {
  return request({
    url: '/production/otherDay/cancelChangeAudit',
    method: 'put',
    data,
  })
}

export function updateOtherDay(data) {
  return request({
    url: '/production/otherDay',
    method: 'put',
    data,
  })
}
