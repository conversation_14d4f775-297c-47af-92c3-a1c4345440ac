import request from '@/utils/request'

// 查询半成品_检验记录列表
export function listBcpRecord(query) {
  return request({
    url: '/qc/bcpRecord/list',
    method: 'get',
    params: query
  })
}

// 查询半成品_检验记录详细
export function getBcpRecord(id) {
  return request({
    url: '/qc/bcpRecord/' + id,
    method: 'get'
  })
}
// 查询半成品_检验记录详细
export function getBcpRecordDetail(id) {
  return request({
    url: '/qc/bcpRecord/detail/' + id,
    method: 'get'
  })
}

// 新增半成品_检验记录
export function addBcpRecord(data) {
  return request({
    url: '/qc/bcpRecord',
    method: 'post',
    data: data
  })
}

// 修改半成品_检验记录
export function updateBcpRecord(data) {
  return request({
    url: '/qc/bcpRecord',
    method: 'put',
    data: data
  })
}

// 删除半成品_检验记录
export function delBcpRecord(id) {
  return request({
    url: '/qc/bcpRecord/' + id,
    method: 'delete'
  })
}

// 导出半成品_检验记录
export function exportBcpRecord(query) {
  return request({
    url: '/qc/bcpRecord/export',
    method: 'get',
    params: query
  })
}

// 导出半成品_检验记录
export function exportQcBcpRecord(query) {
  return request({
    url: '/qc/bcpRecord/exportSemimanufacturesRecord',
    method: 'get',
    params: query
  })
}

export function allBcpRecord(query) {
  return request({
    url: '/qc/bcpRecord/all',
    method: 'get',
    params: query
  })
}

export function exportBcp(query) {
  return request({
    url: '/qc/bcpRecord/exportBcp',
    method: 'get',
    params: query
  })
}
