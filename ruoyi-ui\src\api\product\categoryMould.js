import request from '@/utils/request'

// 查询模具类别列表
export function listCategoryMould(query) {
  return request({
    url: '/product/categoryMould/list',
    method: 'get',
    params: query
  })
}

// 查询模具类别详细
export function getCategoryMould(categoryId) {
  return request({
    url: '/product/categoryMould/' + categoryId,
    method: 'get'
  })
}

// 新增模具类别
export function addCategoryMould(data) {
  return request({
    url: '/product/categoryMould',
    method: 'post',
    data: data
  })
}

// 修改模具类别
export function updateCategoryMould(data) {
  return request({
    url: '/product/categoryMould',
    method: 'put',
    data: data
  })
}

// 删除模具类别
export function delCategoryMould(categoryId) {
  return request({
    url: '/product/categoryMould/' + categoryId,
    method: 'delete'
  })
}

// 导出模具类别
export function exportCategoryMould(query) {
  return request({
    url: '/product/categoryMould/export',
    method: 'get',
    params: query
  })
}

export function categoryMouldAll() {
  return request({
    url: '/product/categoryMould/all',
    method: 'get'
  })
}
