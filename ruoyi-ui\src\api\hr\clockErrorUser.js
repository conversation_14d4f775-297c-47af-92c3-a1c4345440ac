import request from '@/utils/request'

// 查询打卡异常用户申请列表
export function listClockErrorUser(query) {
  return request({
    url: '/hr/clockErrorUser/list',
    method: 'get',
    params: query
  })
}
export function auditClockErrorUser(query) {
  return request({
    url: '/hr/clockErrorUser/audit',
    method: 'get',
    params: query
  })
}
export function logClockErrorUser(query) {
  return request({
    url: '/hr/clockErrorUser/log',
    method: 'get',
    params: query
  })
}
export function getClockErrorUser(id) {
  return request({
    url: '/hr/clockErrorUser/' + id,
    method: 'get'
  })
}

// 新增打卡异常用户申请
export function addClockErrorUser(data) {
  return request({
    url: '/hr/clockErrorUser',
    method: 'post',
    data: data
  })
}


// 删除打卡异常用户申请
export function delClockErrorUser(id) {
  return request({
    url: '/hr/clockErrorUser/' + id,
    method: 'delete'
  })
}
export function submitAudit(data) {
  return request({
    url: '/hr/clockErrorUser/submitAudit',
    method: 'put',
    data: data
  })
}
export function cancelAudit(data) {
  return request({
    url: '/hr/clockErrorUser/cancelAudit',
    method: 'put',
    data: data
  })
}

// 归档
export function pigeonholeClockErrorUser(data) {
  return request({
    url: '/hr/clockErrorUser/pigeonhole',
    method: 'post',
    data: data
  })
}

export function planClockErrorUser(query) {
  return request({
    url: '/hr/clockErrorUser/plan',
    method: 'get',
    params: query
  })
}
