{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\mesLog\\save.vue?vue&type=style&index=0&id=18a607b2&scoped=true&lang=scss", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\mesLog\\save.vue", "mtime": 1753954679646}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1744596528942}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1744596530059}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1744596529996}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1744596552583}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5jZWxsLXdyYXBwZXIgew0KICAubGFiZWwgew0KICAgIHdpZHRoOiA4MHB4Ow0KICB9DQp9DQoNCi5oZWFkZXItd3JhcHBlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KDQogIC5oZWFkZXItdGl0bGUgew0KICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICBmb250LXdlaWdodDogNzAwOw0KICB9DQoNCiAgLmhlYWRlci1pbWcgew0KICAgIGJhY2tncm91bmQ6IHVybCh+QC9hc3NldHMvaW1hZ2VzL3Byb2R1Y3Rpb24vcGxhbi9sYXlvdXQvaGVhZC5naWYpIG5vLXJlcGVhdCBjZW50ZXIgY2VudGVyOw0KICAgIGJhY2tncm91bmQtc2l6ZTogMTAwJTsNCiAgICBoZWlnaHQ6IDEwdmg7DQogICAgd2lkdGg6IDEwMCU7DQogIH0NCn0NCg0KLyogZWwtdGFicyAqLw0KOjp2LWRlZXAgLmVsLXRhYnNfX25hdi1zY3JvbGx7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIHBhZGRpbmc6IDIwcHggMDsNCn0NCjo6di1kZWVwIC5lbC10YWJzX19uYXYgew0KICBtYXJnaW46IDAgMjBweDsNCiAgLyog5L2/55SocnB45rKh5pyJ5pWI5p6cICovDQp9DQoNCjo6di1kZWVwIC5lbC10YWJzX19uYXYtc2Nyb2xsIHsNCiAgcGFkZGluZzogMTBweDsNCn0NCg0KOjp2LWRlZXAgLmVsLXRhYnNfX2NvbnRlbnQgew0KICBwYWRkaW5nLXRvcDogMDsNCn0NCg=="}, {"version": 3, "sources": ["save.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuyBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "save.vue", "sourceRoot": "src/views/production/mesLog", "sourcesContent": ["<template>\r\n  <div v-loading=\"loading\" >\r\n    <el-row>\r\n      <el-col :span=\"8\">\r\n        <el-row >\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">计划日期</div>\r\n            <div class=\"content\">{{form.workDate}}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">班次</div>\r\n            <div class=\"content\">{{selectOptionsLabel(sailingsOptions, form.sailings)}}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">区域</div>\r\n            <div class=\"content\">{{form.areaNo}}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">设备</div>\r\n            <div class=\"content\">{{form.equipmentNo}}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\" style=\"width: 100px\">线长</div>\r\n            <div class=\"content\">{{ form.lineLeader }}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">生产数量</div>\r\n            <div class=\"content\">{{form.productNums}}</div>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n      <el-col :span=\"8\" class=\"header-wrapper\" >\r\n        <div class=\"header-title\">{{form.code}}</div>\r\n        <div class=\"header-img\"></div>\r\n        <div class=\"header-text\">{{form.startTime}}~{{form.endTime}}</div>\r\n      </el-col>\r\n      <el-col :span=\"8\" >\r\n        <el-row >\r\n          <el-col :span=\"24\" class=\"cell-wrapper\">\r\n            <div class=\"label\">品名</div>\r\n            <div class=\"content\">{{form.productName}}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">品号</div>\r\n            <div class=\"content\">{{form.productNo}}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\" style=\"width: 100px\">客户订单号</div>\r\n            <div class=\"content\">{{ form.customerOrderNo }}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\" style=\"width: 100px\">客户名称</div>\r\n            <div class=\"content\">{{ form.customerOrderNo }}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">\r\n              产出占比\r\n              <el-tooltip content=\"良品量 / 工单量\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n              </el-tooltip>\r\n            </div>\r\n            <div class=\"content\" v-if=\"schedule.planNums\">{{toPercent(divide(form.productNums,schedule.planNums))}}</div>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-tabs v-model=\"currentTab\" type=\"border-card\" style=\"margin-top: 20px\" @tab-click=\"tabChange\" >\r\n      <el-tab-pane label=\"人员/工时\" lazy name=\"person\" key=\"person\" >\r\n        <MesLogArea\r\n          v-if=\"form.minutes && form.inNums\"\r\n          :form=\"form\"\r\n          :mes-lot-wait-list=\"mesLotWaitList\"\r\n          :lot-logs=\"lotLogs\"\r\n          :out-array=\"outArray\"\r\n          :user-array=\"userArray\"\r\n        />\r\n      </el-tab-pane>\r\n      <el-tab-pane label=\"工艺标准\" lazy name=\"standard\" key=\"standard\" >\r\n        <ProcessTable\r\n          :project=\"project\"\r\n          :bom-data=\"bomData\"\r\n          :bom-tree=\"bomTree\"\r\n          :readonly=\"true\" />\r\n      </el-tab-pane>\r\n      <el-tab-pane key=\"material\" label=\"物料\" lazy name=\"material\">\r\n        <MesLogMaterialTabs :form=\"form\" :material-log-list=\"materialLogArray\" />\r\n      </el-tab-pane>\r\n      <el-tab-pane key=\"charts\" label=\"产线报表\" lazy name=\"charts\" >\r\n        <MesLogDataCharts ref=\"mesDataCharts\" :diff-data-array=\"diffDataArray\" />\r\n      </el-tab-pane>\r\n      <el-tab-pane key=\"equipment\" label=\"数采看板\" lazy name=\"equipment\" >\r\n\r\n      </el-tab-pane>\r\n      <el-tab-pane key=\"schedule\" label=\"工单维度分摊\" lazy name=\"schedule\" >\r\n        <MesLogScheduleTable\r\n          :plan=\"form\"\r\n          :bcp-list=\"bcpList\"\r\n          :bcp-array=\"scheduleBcpArray\"\r\n          :other-array=\"scheduleOtherArray\"\r\n        />\r\n      </el-tab-pane>\r\n      <el-tab-pane key=\"report\" label=\"生产记录\" lazy name=\"report\" >\r\n        <MesLogProductionLog\r\n          :plan=\"form\"\r\n          :bcp-list=\"bcpList\"\r\n          :bcp-array=\"bcpArray\"\r\n          :other-array=\"otherArray\"\r\n          :material-array=\"materialArray\"\r\n        />\r\n      </el-tab-pane>\r\n      <el-tab-pane key=\"qc\" label=\"品质检验\" lazy name=\"qc\" >\r\n        <MesQc ref=\"mesQc\" :inspection-list=\"inspectionList\" />\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n\r\n    <div class=\"dialog-footer\" style=\"margin-top: 20px\" v-if=\"!readonly\" >\r\n      <el-button type=\"primary\" @click=\"submitForm\" size=\"mini\" :loading=\"btnLoading\" >\r\n        确 定\r\n        <el-tooltip content=\"目前只有物料信息的备注需要保存,其他无需操作\" placement=\"top\">\r\n          <i class=\"el-icon-question\"></i>\r\n        </el-tooltip>\r\n      </el-button>\r\n    </div>\r\n\r\n  </div>\r\n</template>\r\n<script >\r\n\r\nimport {\r\n  addMesLog,\r\n  getMesLog,\r\n  updateMesLog\r\n} from \"@/api/production/mesLog\";\r\nimport {allPlanAreaHours} from \"@/api/production/planAreaHours\";\r\nimport {allWipContPartialOut} from \"@/api/mes/wipcontPartialout\";\r\nimport MesLogArea from \"@/views/production/mesLog/area.vue\";\r\nimport {allWipLotLog} from \"@/api/mes/mesView\";\r\nimport ProcessTable from \"@/views/production/layout/process/table.vue\";\r\nimport {getArchive} from \"@/api/project/archive\";\r\nimport {getProject} from \"@/api/project/project\";\r\nimport {getScheduleStandardByCode} from \"@/api/order/scheduleStandard\";\r\nimport MesQc from \"@/views/mes/production/qc.vue\";\r\nimport {allFinishedInspection, allGroupByBatchFinishedInspection} from \"@/api/qc/finishedInspection\";\r\nimport {allGroupByEquipmentNo} from \"@/api/mes/wipContMaterialLot\";\r\nimport {getBomByErpCode, getScheduleMaterial} from \"@/api/common/erp\";\r\nimport MesLogDataCharts from \"@/views/production/mesLog/dataCharts.vue\";\r\nimport {getGzlByParams} from \"@/api/sop/gzl\";\r\nimport {getMaterialLogList} from \"@/api/production/mesLog\";\r\nimport {getScheduleByCode} from \"@/api/order/schedule\";\r\nimport MesLogMaterialTabs from \"@/views/production/mesLog/materialTabs.vue\";\r\nimport MesLogProductionLog from \"@/views/production/mesLog/log.vue\";\r\nimport MesProductionLog from \"@/views/mes/production/log.vue\";\r\nimport MesLogScheduleTable from \"@/views/production/mesLog/scheduleTable.vue\";\r\n\r\nexport default {\r\n  name: 'mesLogSave',\r\n  components: {\r\n    MesLogScheduleTable,\r\n    MesProductionLog,\r\n    MesLogProductionLog,\r\n    MesLogMaterialTabs,\r\n    MesLogDataCharts,\r\n    MesQc,\r\n    ProcessTable,\r\n    MesLogArea\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      btnLoading: false,\r\n      readonly: false,\r\n      form: {},\r\n      rules: {},\r\n      currentTab: '',\r\n      sailingsOptions: [\r\n        {label: '白班',value: '0'},\r\n        {label: '晚班',value: '1'},\r\n      ],\r\n      outArray: [],\r\n      lotLogs: [],\r\n      mesLotWaitList: [],\r\n      userArray: [],\r\n      project: {},\r\n      bomData: [],\r\n      bomTree: [],\r\n      diffDataArray: [],\r\n      diffDataOptions: {},\r\n      inspectionList: [],\r\n      qcOptions: {},\r\n      materialLogArray: [],\r\n      scheduleStandard: {},\r\n      materialArray: [],\r\n      schedule: {},\r\n      bcpList: [],\r\n      bcpArray: [],\r\n      otherArray: [],\r\n      scheduleBcpArray: [],\r\n      scheduleOtherArray: [],\r\n    }\r\n  },\r\n  async created() {\r\n  },\r\n  methods: {\r\n    async tabChange() {\r\n      await this.$nextTick()\r\n      if(this.currentTab === 'person') {\r\n\r\n      } else if(this.currentTab === 'charts') {\r\n        await this.reduceDiffChart()\r\n      } else if(this.currentTab === 'qc') {\r\n        await this.reduceQcChart()\r\n      }\r\n    },\r\n    cancel() {\r\n      this.$parent.$parent.open = false;\r\n      this.reset();\r\n    },\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        code: null,\r\n        workDate: null,\r\n        sailings: null,\r\n        areaNo: null,\r\n        lineLeader: null,\r\n        minutes: null,\r\n        inNums: null,\r\n        goodsNums: null,\r\n        badNums: null,\r\n      };\r\n      this.resetForm(\"form\")\r\n      this.project = {}\r\n      this.bomData = []\r\n      this.bomTree = []\r\n      this.materialLogArray = []\r\n      this.outArray = []\r\n      this.lotLogs = []\r\n      this.mesLotWaitList = []\r\n      this.userArray = []\r\n      this.diffDataArray = []\r\n      this.diffDataOptions = {}\r\n      this.inspectionList = []\r\n      this.qcOptions = {}\r\n      this.schedule = {}\r\n      this.scheduleStandard = {}\r\n      this.materialArray = []\r\n      this.bcpList = []\r\n      this.bcpArray = []\r\n      this.otherArray = []\r\n      this.scheduleBcpArray = []\r\n      this.scheduleOtherArray = []\r\n    },\r\n    diffHours(startTime,endTime) {\r\n      if(startTime && endTime) {\r\n        let minutes = this.moment(endTime,'YYYY-MM-DD hh:mm').diff(this.moment(startTime,'YYYY-MM-DD hh:mm'), 'minutes')\r\n        if(minutes) {\r\n          return this.divide(minutes,60).toNumber()\r\n        }\r\n      }\r\n    },\r\n    async reduceQcChart() {\r\n      await this.$nextTick()\r\n      const mesQc = this.$refs.mesQc\r\n      if(mesQc) {\r\n        const qcCharts = mesQc.$refs.qcCharts\r\n        if(qcCharts) {\r\n          await qcCharts.init(this.qcOptions)\r\n        }\r\n      }\r\n    },\r\n    async reduceDiffChart() {\r\n      await this.$nextTick()\r\n      const mesDataCharts = this.$refs.mesDataCharts\r\n      if(mesDataCharts) {\r\n        const diffChart = mesDataCharts.$refs.diffChart\r\n        if(diffChart) {\r\n          await diffChart.init(this.diffDataOptions)\r\n        }\r\n        const timeLineChart = mesDataCharts.$refs.timeLineChart\r\n        if(timeLineChart) {\r\n        }\r\n      }\r\n    },\r\n    async buildProductionLog() {\r\n      const form = this.form\r\n      const materialArray = this.materialArray\r\n      const bomRes = await getBomByErpCode(form.productNo)\r\n      if(bomRes.code === 200) {\r\n        let bcpList = bomRes.data.filter(i => i.mb005 === '103')\r\n        for (const item of bcpList) {\r\n          const gzlParams = {\r\n            md003: item.md003,\r\n            md001: item.md001,\r\n          }\r\n          const gzlRes = await getGzlByParams(gzlParams)\r\n          if(gzlRes.code === 200 && gzlRes.data) {\r\n            const gzl = gzlRes.data\r\n            item.max = gzl.max\r\n            item.avg = gzl.avg\r\n            item.min = gzl.min\r\n          }\r\n        }\r\n        this.bcpList = bcpList\r\n      }\r\n\r\n      let res = await getMaterialLogList(form.id)\r\n      if(res){\r\n        const bcpArray = res.bcpArray\r\n        const otherArray = res.otherArray\r\n        const scheduleBcpArray = res.scheduleBcpArray\r\n        const scheduleOtherArray = res.scheduleOtherArray\r\n        console.log(scheduleBcpArray)\r\n        console.log(scheduleOtherArray)\r\n        if(bcpArray){\r\n          this.bcpArray = bcpArray\r\n          for (const o of bcpArray) {\r\n            if(!materialArray.map(i=> i.materialCode).includes(o.materialCode)) {\r\n              materialArray.push({\r\n                materialCode: o.materialCode,\r\n                remark: null,\r\n              })\r\n            }\r\n          }\r\n        }\r\n        if(otherArray){\r\n          this.otherArray = otherArray\r\n          for (const o of otherArray) {\r\n            if(!materialArray.map(i=> i.materialCode).includes(o.materialCode)) {\r\n              materialArray.push({\r\n                materialCode: o.materialCode,\r\n                remark: null,\r\n              })\r\n            }\r\n          }\r\n        }\r\n        if(scheduleBcpArray) {\r\n          this.scheduleBcpArray = scheduleBcpArray\r\n        }\r\n        if(scheduleOtherArray) {\r\n          this.scheduleOtherArray = scheduleOtherArray\r\n        }\r\n      }\r\n    },\r\n    async buildMaterialArray() {\r\n      const form = this.form\r\n      const schedule = this.schedule\r\n      const formRate = this.divide(form.productNums,schedule.planNums)//产出占比\r\n      if(formRate) {\r\n        let scheduleCode = form.scheduleCode\r\n        const materialLogList = await allGroupByEquipmentNo({scheduleCode,workDate: form.workDate})//暂时不算班次\r\n        this.materialLogList = materialLogList\r\n        const arr = scheduleCode.split('-')\r\n        let erpBomData = await getScheduleMaterial({workOrderNo: arr[1], workOrderSingle: arr[0],})\r\n\r\n        const scheduleNums = schedule.planNums\r\n        const materialNoSet = new Set()\r\n        for (const log of materialLogList) {\r\n          materialNoSet.add(log.materialNo)\r\n        }\r\n\r\n        const materialLogArray = []\r\n        for (const materialNo of materialNoSet) {\r\n          const te010Set = new Set()//在品号维度下再排批次\r\n          let sjBomNum = 0 //bom用量对于工单产品是成品的\r\n          let cb010 = 0//erp损耗率\r\n          let materialName\r\n          let te006//erp单位\r\n          let te005Sum = this.$big(0)//工单发料量小计\r\n          let lotSums = this.$big(0)//批次使用量小计\r\n          let erpSylSum = this.$big(0)//erp品号维度使用量小计\r\n          for (const b of erpBomData) {\r\n            if(b.te004 === materialNo) {\r\n              sjBomNum = Number(b.sjBomNum)\r\n              cb010 = Number(b.cb010)\r\n              materialName = b.te017\r\n              te006 = b.te006\r\n\r\n              te005Sum = this.add(te005Sum,b.te005)\r\n              te010Set.add(b.te010)//按照e10的批次来\r\n            }\r\n          }\r\n          for (const log of materialLogList) {\r\n            if(materialNo === log.materialNo) {\r\n              lotSums = this.add(lotSums,log.nums)\r\n            }\r\n          }\r\n          const te010Array = []\r\n          for (const te010 of te010Set) {\r\n            let erpLlNums = 0\r\n            let erpSylNums = 0\r\n            for (const ot of this.otherArray) {\r\n              if(ot.materialCode === materialNo ) {\r\n                for (const t of ot.te010Array) {\r\n                  if(t.te010 === te010) {\r\n                    if(t.gdSylNums) {\r\n                      erpLlNums = Number(t.gdSylNums)\r\n                      erpSylNums = Number(t.gdSylNums)\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n            const equipmentSet = new Set()//品号批次内排设备\r\n            for (const log of materialLogList) {\r\n              if(materialNo === log.materialNo && te010 === log.materialLotNo) {\r\n                equipmentSet.add(log.equipmentNo)\r\n              }\r\n            }\r\n            const equipmentArray = []\r\n            for (const equipmentNo of equipmentSet) {\r\n              let l = {\r\n                equipmentNo,\r\n              }\r\n              for (const log of materialLogList) {\r\n                if(materialNo === log.materialNo && te010 === log.materialLotNo && equipmentNo === log.equipmentNo) {\r\n                  l.nums = log.nums//使用量\r\n                  l.times = log.times//领料次数\r\n                }\r\n              }\r\n              equipmentArray.push(l)\r\n            }\r\n            const llArray = []//erp领料单记录\r\n            for (const b of erpBomData) {\r\n              if(b.te004 === materialNo && b.te010 === te010) {\r\n                let sylRate\r\n                let llBlNums\r\n                let llBlReason\r\n                let scBlNums\r\n                let scBlReason\r\n                for (const ot of this.otherArray) {\r\n                  if(ot.materialCode === materialNo ) {\r\n                    for (const t of ot.te010Array) {\r\n                      if(t.te010 === te010) {\r\n                        sylRate = t.sylRate\r\n                        llBlNums = t.llBlNums\r\n                        llBlReason = t.llBlReason\r\n                        scBlNums = t.scBlNums\r\n                        scBlReason = t.scBlReason\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n                llArray.push({\r\n                  te001: b.te001,//领料通知单别\r\n                  te002: b.te002,//领料通知单号\r\n                  te008: b.te008,//仓库\r\n                  me003: b.me003,//最早入库日期\r\n                  te013: b.te013,//领料说明\r\n                  sylRate,//使用量比例(生产批批次品号使用量/工单品号批次使用量)\r\n                  llBlNums,//来料不良\r\n                  llBlReason,\r\n                  scBlNums,//生产不良\r\n                  scBlReason,\r\n                })\r\n              }\r\n            }\r\n            te010Array.push({\r\n              te010,\r\n              erpLlNums: erpLlNums,//领料量\r\n              erpSylNums: erpSylNums,//使用量\r\n              equipmentArray,\r\n              llArray,\r\n            })\r\n            erpSylSum = this.add(erpSylSum,erpSylNums)\r\n          }\r\n          if(scheduleNums) {\r\n            let gdXql = this.multiply(scheduleNums,sjBomNum)\r\n            let lotPlanXql = this.multiply(form.productNums,sjBomNum)//生产记录实际生产数量 计算 本线需求量\r\n            let lotLlXql = this.multiply(form.productNums,sjBomNum)//批次生产量计算 理论使用量\r\n            let loss = this.subtract(lotSums, lotLlXql)\r\n            const erpRateNums = this.multiply(erpSylSum,formRate).toNumber()\r\n            materialLogArray.push({\r\n              materialNo,\r\n              materialName,\r\n              sjBomNum,\r\n              cb010,\r\n              te006,\r\n              gdXql: gdXql.toNumber(),\r\n              te005Sum: te005Sum.toNumber(),\r\n              xfCyRate: this.divide(this.subtract(te005Sum, gdXql), gdXql).toNumber(),\r\n              lotXql: lotPlanXql.toNumber(),\r\n              lotLlXql: lotLlXql.toNumber(),\r\n              lotSums: lotSums.toNumber(),\r\n              loss: loss.toNumber(),\r\n              lotRate: this.divide(loss, lotLlXql).toNumber(),\r\n              erpSylSum,\r\n              erpRateNums,\r\n              diffNums: this.subtract(lotSums, erpRateNums).toNumber(),\r\n              te010Array,\r\n            })\r\n          }\r\n        }\r\n\r\n        this.materialLogArray = materialLogArray\r\n      }\r\n    },\r\n    async buildDiffData() {\r\n      const form = this.form\r\n      const scheduleStandard = this.scheduleStandard\r\n\r\n      const productNums = form.productNums\r\n      const actualHours = this.divide(form.sumMinutes,60)\r\n      const actualDuration = this.diffHours(form.startTime,form.endTime)\r\n      if(actualDuration && scheduleStandard.nums) {\r\n        const actualProductivity = this.divide(productNums,actualDuration).toNumber()\r\n        const actualHoursRate = this.divide(productNums, actualHours).toNumber()\r\n        const diffDataArray = []\r\n\r\n        let standardHours = 0\r\n        let standardPersonNums = 0\r\n        let standardHoursRate = 0\r\n        if(form.opNo === 'GB') {//一阶 取少的\r\n          standardPersonNums = scheduleStandard.nums\r\n          standardHoursRate = scheduleStandard.costHoursRate\r\n        } else {\r\n          standardPersonNums = scheduleStandard.costNums\r\n          standardHoursRate = scheduleStandard.costHoursRate\r\n        }\r\n        standardHours = this.multiply(standardHoursRate,form.productNums).toNumber()\r\n        diffDataArray.push(['标准',scheduleStandard.productivity,standardPersonNums,standardHours,standardHoursRate])\r\n        diffDataArray.push(['实际',actualProductivity,form.sumNums,actualHours,actualHoursRate])\r\n        diffDataArray.push([\r\n          '差异',\r\n          this.subtract(actualProductivity,scheduleStandard.productivity).toNumber(),\r\n          this.subtract(form.sumNums,standardPersonNums).toNumber(),\r\n          this.subtract(actualHours,standardHours).toNumber(),\r\n          this.subtract(actualHoursRate,standardHoursRate).toNumber(),\r\n        ])\r\n        this.diffDataArray = diffDataArray\r\n\r\n        this.diffDataOptions =  {\r\n          title: {\r\n            text: '批次维度数据对比'\r\n          },\r\n          legend: {\r\n            data: ['预估', '实际']\r\n          },\r\n          radar: {\r\n            indicator: [\r\n              { name: '生产产能', max: Math.max(scheduleStandard.productivity, actualProductivity)*1.2 },\r\n              { name: '生产人数', max: Math.max(standardPersonNums, form.sumNums)*1.2 },\r\n              { name: '生产工时', max: Math.max(standardHours, actualHours)*1.2 },\r\n              { name: '工时产出率', max: Math.max(standardHoursRate, actualHoursRate)*1.2 }\r\n            ]\r\n          },\r\n          series: [\r\n            {\r\n              name: '预估 vs 实际',\r\n              type: 'radar',\r\n              data: [\r\n                {\r\n                  value: diffDataArray[0].slice(1),\r\n                  name: '预估'\r\n                },\r\n                {\r\n                  value: diffDataArray[1].slice(1),\r\n                  name: '实际'\r\n                }\r\n              ]\r\n            }\r\n          ]\r\n        }\r\n        // await this.reduceDiffChart()\r\n      }\r\n    },\r\n    async importArchive(archiveId) {\r\n      let archiveRes = await getArchive(archiveId)\r\n      if(archiveRes.code === 200 && archiveRes.data) {\r\n        let archive = archiveRes.data\r\n\r\n        const projectRes = await getProject(archive.projectId)\r\n        const project = projectRes.data\r\n\r\n        project.type = archive.type\r\n\r\n        if(['0','1','3'].includes(archive.type)) {\r\n          project.bomResource = archive.bomResource\r\n          project.bomType = archive.bomType\r\n          project.resourceFinishedGoodsId = archive.resourceFinishedGoodsId\r\n          project.erpPrice = archive.erpPrice\r\n          project.bomArray = archive.bomArray//这里就是字符串\r\n          project.erpCode = archive.erpCode\r\n          if(archive.cubicleArray) {\r\n            const cubicleArray = JSON.parse(archive.cubicleArray)\r\n            project.cubicleArray = cubicleArray\r\n\r\n            const imgs = []\r\n            const videos = []\r\n            for (const item of cubicleArray) {\r\n              for (const second of item.sectionArray) {\r\n                for (const w of second.workTypeArray) {\r\n                  if(w.homeworkImgs) {\r\n                    imgs.push(...w.homeworkImgs.split(','))\r\n                  }\r\n                  if(w.homeworkVideos.length) {\r\n                    videos.push(...w.homeworkVideos)\r\n                  }\r\n                }\r\n              }\r\n            }\r\n            this.imgs = imgs\r\n            this.videos = videos\r\n          } else {\r\n            project.cubicleArray = []\r\n          }\r\n        }\r\n        this.bomData = JSON.parse(archive.bomArray)\r\n        this.project = project\r\n      }\r\n    },\r\n    async buildQcLogs() {\r\n      const form = this.form\r\n      const arr = form.scheduleCode.split('-')\r\n      const inspectionList = await allFinishedInspection({singleCategory: arr[0],workOrderNo:arr[1]})//质检\r\n      for(let item of inspectionList){\r\n        let erpColumn = item.erpColumn;\r\n        if(erpColumn){\r\n          let obj = JSON.parse(erpColumn);\r\n          item.tg030 = obj.tg030;\r\n        }\r\n      }\r\n      this.inspectionList = inspectionList\r\n\r\n      const groupInspectionList = await allGroupByBatchFinishedInspection({singleCategory: arr[0],workOrderNo:arr[1]})\r\n\r\n      const rawData = [\r\n        groupInspectionList.map(i=> i.batchNums - i.samplingNums),\r\n        groupInspectionList.map(i=> i.samplingNums),\r\n      ];\r\n      const totalData = [];\r\n      for (let i = 0; i < rawData[0].length; ++i) {\r\n        let sum = 0;\r\n        for (let j = 0; j < rawData.length; ++j) {\r\n          sum += rawData[j][i];\r\n        }\r\n        totalData.push(sum);\r\n      }\r\n      const grid = {\r\n        left: 100,\r\n        right: 100,\r\n        top: 50,\r\n        bottom: 50\r\n      };\r\n      const series = [\r\n        '未抽样',\r\n        '已抽样',\r\n      ].map((name, sid) => {\r\n        return {\r\n          name,\r\n          type: 'bar',\r\n          stack: 'total',\r\n          barWidth: '60%',\r\n          label: {\r\n            show: true,\r\n            formatter: (params) => Math.round(params.value * 1000) / 10 + '%'\r\n          },\r\n          data: rawData[sid].map((d, did) =>\r\n            totalData[did] <= 0 ? 0 : d / totalData[did]\r\n          )\r\n        }\r\n      })\r\n      this.qcOptions = {\r\n        legend: {\r\n          selectedMode: false\r\n        },\r\n        grid,\r\n        yAxis: {\r\n          type: 'value'\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: groupInspectionList.map(i=> i.batchNo)\r\n        },\r\n        series\r\n      }\r\n    },\r\n    async init(id) {\r\n      this.loading = true\r\n      const res = await getMesLog(id)\r\n      const form = res.data\r\n      if(form.materialArray) {\r\n        this.materialArray = JSON.parse(form.materialArray)\r\n      }\r\n      this.form = form\r\n\r\n      const scheduleCode = form.scheduleCode\r\n      const arr = scheduleCode.split('-')\r\n      const scheduleRes = await getScheduleByCode(arr[0] + \"-\" + arr[1])\r\n      if(scheduleRes.code === 200) {\r\n        const schedule = scheduleRes.data\r\n        this.schedule = schedule\r\n      }\r\n\r\n      const scheduleStandardRes = await getScheduleStandardByCode(form.productNo)\r\n      if(scheduleStandardRes.code === 200) {\r\n        const scheduleStandard = scheduleStandardRes.data\r\n        this.scheduleStandard = scheduleStandard\r\n        if(scheduleStandard) {\r\n          await this.importArchive(scheduleStandard.archiveId)\r\n          await this.buildDiffData()\r\n        }\r\n      }\r\n\r\n      const outList = await allWipContPartialOut({\r\n        areano: form.areaNo,\r\n        eventDate: form.workDate,\r\n        equipmentno: form.equipmentNo,\r\n        scheduleCode: form.scheduleCode,\r\n      })\r\n      let inNums = this.$big(0)\r\n      let goodsNums = this.$big(0)\r\n      let badNums = this.$big(0)\r\n      this.outArray = outList.map(i=> {\r\n        return {\r\n          lotNo: i.lotno,\r\n          opNo: i.opno,\r\n          eventTime: i.eventtime,\r\n          userNo: i.userno,\r\n          areaNo: i.areano,\r\n          inNums: i.inputqty,//产出量\r\n          goodsNums: i.goodqty,//良品量\r\n          badNums: i.scrapqty,//良品量\r\n          equipmentNo: i.equipmentno,\r\n          userName: i.userName,\r\n        }\r\n      })//重命名属性名称,方便理解语义\r\n\r\n      for (const item of this.outArray) {\r\n        inNums = this.add(inNums,item.inNums)\r\n        goodsNums = this.add(goodsNums,item.goodsNums)\r\n        badNums = this.add(badNums,item.badNums)\r\n      }\r\n\r\n      form.inNums = inNums.toNumber()\r\n      form.goodsNums = goodsNums.toNumber()\r\n      form.badNums = badNums.toNumber()\r\n\r\n      const lotLogs = await allWipLotLog({\r\n        areaNo: form.areaNo,\r\n        createTime: form.workDate,\r\n        equipmentNo: form.equipmentNo,\r\n        scheduleCode: form.scheduleCode,\r\n      })\r\n      this.lotLogs = lotLogs\r\n\r\n      const planAreaHourList = await allPlanAreaHours({\r\n        areaNo: form.areaNo,\r\n        equipmentNo: form.equipmentNo,\r\n        workDate: form.workDate,\r\n        sailings: form.sailings,\r\n        scheduleCode: form.scheduleCode,\r\n      })\r\n\r\n      const userArray = []\r\n      const userSet = new Set(planAreaHourList.map(i=> i.userCode))\r\n      let sumMinutes = this.$big(0)\r\n      for (const userCode of userSet) {\r\n        let nickName\r\n        let minutes = this.$big(0)\r\n        const array = []\r\n        for (const h of planAreaHourList) {\r\n          if(userCode === h.userCode) {\r\n            nickName = h.nickName\r\n            array.push(h)\r\n            minutes = this.add(minutes,h.minutes)\r\n          }\r\n        }\r\n        userArray.push({\r\n          userCode,\r\n          nickName,\r\n          minutes,\r\n          array,\r\n        })\r\n        sumMinutes = this.add(sumMinutes,minutes)\r\n      }\r\n      form.minutes = sumMinutes.toNumber()\r\n      this.userArray = userArray\r\n\r\n      await this.buildProductionLog()//这个里面的otherArray,buildMaterialArray用到了\r\n      await this.buildMaterialArray()//这里需要用到 areaList 的数据\r\n      await this.buildQcLogs()\r\n\r\n      this.loading = false\r\n    },\r\n    async submitForm() {\r\n      let form = Object.assign({}, this.form)\r\n\r\n      form.materialArray = JSON.stringify(this.materialArray)\r\n\r\n      if (form.id != null) {\r\n        try {\r\n          this.btnLoading = true\r\n          await updateMesLog(form)\r\n          this.btnLoading = false\r\n          this.msgSuccess(\"修改成功\")\r\n          this.$parent.$parent.open = false\r\n          await this.$parent.$parent.getList()\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n  },\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.cell-wrapper {\r\n  .label {\r\n    width: 80px;\r\n  }\r\n}\r\n\r\n.header-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  .header-title {\r\n    font-size: 16px;\r\n    font-weight: 700;\r\n  }\r\n\r\n  .header-img {\r\n    background: url(~@/assets/images/production/plan/layout/head.gif) no-repeat center center;\r\n    background-size: 100%;\r\n    height: 10vh;\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* el-tabs */\r\n::v-deep .el-tabs__nav-scroll{\r\n  background-color: #fff;\r\n  padding: 20px 0;\r\n}\r\n::v-deep .el-tabs__nav {\r\n  margin: 0 20px;\r\n  /* 使用rpx没有效果 */\r\n}\r\n\r\n::v-deep .el-tabs__nav-scroll {\r\n  padding: 10px;\r\n}\r\n\r\n::v-deep .el-tabs__content {\r\n  padding-top: 0;\r\n}\r\n</style>\r\n"]}]}