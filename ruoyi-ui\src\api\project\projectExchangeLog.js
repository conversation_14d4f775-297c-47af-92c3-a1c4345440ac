import request from '@/utils/request'

// 查询项目变更记录列表
export function listProjectExchangeLog(query) {
  return request({
    url: '/project/projectExchangeLog/list',
    method: 'get',
    params: query
  })
}

// 查询项目变更记录详细
export function getProjectExchangeLog(id) {
  return request({
    url: '/project/projectExchangeLog/' + id,
    method: 'get'
  })
}

// 新增项目变更记录
export function addProjectExchangeLog(data) {
  return request({
    url: '/project/projectExchangeLog',
    method: 'post',
    data: data
  })
}

// 修改项目变更记录
export function updateProjectExchangeLog(data) {
  return request({
    url: '/project/projectExchangeLog',
    method: 'put',
    data: data
  })
}

// 删除项目变更记录
export function delProjectExchangeLog(id) {
  return request({
    url: '/project/projectExchangeLog/' + id,
    method: 'delete'
  })
}

// 导出项目变更记录
export function exportProjectExchangeLog(query) {
  return request({
    url: '/project/projectExchangeLog/export',
    method: 'get',
    params: query
  })
}