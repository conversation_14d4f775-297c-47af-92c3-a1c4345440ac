import request from '@/utils/request'

// 查询调研回复列表
export function listFormUser(query) {
  return request({
    url: '/gx/formUser/list',
    method: 'get',
    params: query
  })
}

// 查询调研回复详细
export function getFormUser(id) {
  return request({
    url: '/gx/formUser/' + id,
    method: 'get'
  })
}

// 新增调研回复
export function addFormUser(data) {
  return request({
    url: '/gx/formUser',
    method: 'post',
    data: data
  })
}

// 修改调研回复
export function updateFormUser(data) {
  return request({
    url: '/gx/formUser',
    method: 'put',
    data: data
  })
}

// 删除调研回复
export function delFormUser(id) {
  return request({
    url: '/gx/formUser/' + id,
    method: 'delete'
  })
}

// 导出调研回复
export function exportFormUser(query) {
  return request({
    url: '/gx/formUser/export',
    method: 'get',
    params: query
  })
}

export function allUnSendUser(query) {
  return request({
    url: '/gx/formUser/allUnSendUser',
    method: 'get',
    params: query,
  })
}

export function allFormUser(query) {
  return request({
    url: '/gx/formUser/all',
    method: 'get',
    params: query
  })
}
