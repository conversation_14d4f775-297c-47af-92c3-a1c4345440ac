import request from '@/utils/request'

// 查询task列表
export function listTask(query) {
  return request({
    url: '/process/task/list',
    method: 'get',
    params: query
  })
}
export function listKeyTask(query) {
  return request({
    url: '/process/task/list/' + query.key,
    method: 'get',
    params: query
  })
}
export function listAllKeyTask(query) {
  return request({
    url: '/process/task/list/all/' + query.key,
    method: 'get',
    params: query
  })
}
// 查询task详情
export function getTask(query) {
  return request({
    url: '/process/task/query',
    method: 'get',
    params: query
  })
}
export function doTask(data) {
  return request({
    url: '/process/task/doTask',
    method: 'post',
    data,
  })
}
export function getBatchTask(instanceIds) {
  return request({
    url: '/process/task/batchQuery/'+instanceIds,
    method: 'get'
  })
}

// 查询表单
export function doBatchTask(data) {
  return request({
    url: '/process/task/doBatchTask',
    method: 'post',
    data,
  })
}
export function backBatchProcess(data) {
  return request({
    url: '/process/task/backBatchProcess',
    method: 'post',
    data,
  })
}

// 流程历史流转记录
export function listHistoricActivityInstance(query) {
  return request({
    url: '/process/task/historicActivityInstanceList',
    method: 'get',
    params: query
  })
}

// 历史任务按时间降序排序
export function listHistoryTaskInstance(query) {
  return request({
    url: '/process/task/historyTaskInstanceList',
    method: 'get',
    params: query
  })
}

export function historyVariableList(processInstanceId) {
  return request({
    url: '/process/task/historyVariableList/' + processInstanceId,
    method: 'get',
  })
}

export function flowElementArray(processInstanceId) {
  return request({
    url: '/process/task/flowElementArray/' + processInstanceId,
    method: 'get',
  })
}

export function getProcessDefinitionId(processInstanceId) {
  return request({
    url: '/process/task/getProcessDefinitionId/' + processInstanceId,
    method: 'get',
  })
}

export function getTodoAssignUsers(query) {
  return request({
    url: '/process/task/getTodoAssignUsers',
    method: 'get',
    params: query
  })
}

//退回上一节点
export function backProcess(data) {
  return request({
    url: '/process/task/backProcess',
    method: 'put',
    data,
  })
}

export function listHistoryProcessInstance(query) {
  return request({
    url: '/process/task/historyProcessInstanceList',
    method: 'get',
    params: query
  })
}

export function listHistoryProcessInstanceInvolved(query) {
  return request({
    url: '/process/task/historyProcessInstanceInvolvedList',
    method: 'get',
    params: query
  })
}


export function listProcessNodes(query) {
  return request({
    url: '/process/task/nodes',
    method: 'get',
    params: query
  })
}
export function listProcessNodesTask(query) {
  return request({
    url: '/process/task/nodesTask',
    method: 'get',
    params: query
  })
}
export function boAssign(data) {
  return request({
    url: '/process/task/boAssign',
    method: 'post',
    data,
  })
}

export function nodesUser(query) {
  return request({
    url: '/process/task/nodesUser',
    method: 'get',
    params: query
  })
}
export function isNodesAuditUser(query){
  return request({
    url: '/process/task/isNodesAuditUser',
    method: 'get',
    params: query
  })
}

export function doTaskBatch(data) {
  return request({
    url: '/process/task/doTaskBatch',
    method: 'post',
    data,
  })
}
