import request from '@/utils/request'

// 查询产线数据列表
export function listPlanAreaOut(query) {
  return request({
    url: '/production/planAreaOut/list',
    method: 'get',
    params: query
  })
}

// 查询产线数据详细
export function getPlanAreaOut(id) {
  return request({
    url: '/production/planAreaOut/' + id,
    method: 'get'
  })
}

// 新增产线数据
export function addPlanAreaOut(data) {
  return request({
    url: '/production/planAreaOut',
    method: 'post',
    data: data
  })
}

// 修改产线数据
export function updatePlanAreaOut(data) {
  return request({
    url: '/production/planAreaOut',
    method: 'put',
    data: data
  })
}

// 删除产线数据
export function delPlanAreaOut(id) {
  return request({
    url: '/production/planAreaOut/' + id,
    method: 'delete'
  })
}

// 导出产线数据
export function exportPlanAreaOut(query) {
  return request({
    url: '/production/planAreaOut/export',
    method: 'get',
    params: query
  })
}

export function allPlanAreaOut(query) {
  return request({
    url: '/production/planAreaOut/all',
    method: 'get',
    params: query
  })
}
