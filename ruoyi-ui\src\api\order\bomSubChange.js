import request from '@/utils/request'

// 查询BOM 变更子单身档列表
export function listBomSubChange(query) {
  return request({
    url: '/order/bomSubChange/list',
    method: 'get',
    params: query
  })
}

// 查询BOM 变更子单身档详细
export function getBomSubChange(id) {
  return request({
    url: '/order/bomSubChange/' + id,
    method: 'get'
  })
}

// 新增BOM 变更子单身档
export function addBomSubChange(data) {
  return request({
    url: '/order/bomSubChange',
    method: 'post',
    data: data
  })
}

// 修改BOM 变更子单身档
export function updateBomSubChange(data) {
  return request({
    url: '/order/bomSubChange',
    method: 'put',
    data: data
  })
}

// 删除BOM 变更子单身档
export function delBomSubChange(id) {
  return request({
    url: '/order/bomSubChange/' + id,
    method: 'delete'
  })
}

// 导出BOM 变更子单身档
export function exportBomSubChange(query) {
  return request({
    url: '/order/bomSubChange/export',
    method: 'get',
    params: query
  })
}