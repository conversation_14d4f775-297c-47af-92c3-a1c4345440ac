import request from '@/utils/request'
import {praseStrEmpty} from "@/utils/ruoyi";

export function isNewUser() {
  return request({
    url: '/hr/user/isNew',
    method: 'get'
  })
}


export function getUserFormulaDeptInfo() {
  return request({
    url: '/hr/user/getUserFormulaDeptInfo',
    method: 'get'
  })
}
export function getHrUserInfo(userId) {
  return request({
    url: '/hr/user/info/'+userId,
    method: 'get'
  })
}
// 查询用户列表
export function listHrUser(query) {
  return request({
    url: '/hr/user/list',
    method: 'get',
    params: query
  })
}

export function incumbencyListHrUser(query) {
  return request({
    url: '/hr/user/incumbencyList',
    method: 'get',
    params: query
  })
}

export function probationPeriodListUser(query) {
  return request({
    url: '/hr/user/probationPeriodList',
    method: 'get',
    params: query
  })
}

export function getProbationPeriodUser(userId) {
  return request({
    url: '/hr/user/probationPeriod/'+ userId,
    method: 'get'
  })
}

// 查询用户详细
export function getHrUser(userId) {
  return request({
    url: '/hr/user/' + praseStrEmpty(userId),
    method: 'get'
  })
}
export function getHrUserBase(userId) {
  return request({
    url: '/hr/user/base/' + praseStrEmpty(userId),
    method: 'get'
  })
}
export function getHrUserPerson(userId) {
  return request({
    url: '/hr/user/person/' + praseStrEmpty(userId),
    method: 'get'
  })
}
export function getHrUserWages(userId) {
  return request({
    url: '/hr/user/wages/' + praseStrEmpty(userId),
    method: 'get'
  })
}
export function getHrUserCv(userId) {
  return request({
    url: '/hr/user/cv/' + praseStrEmpty(userId),
    method: 'get'
  })
}
export function getHrUserContract(userId) {
  return request({
    url: '/hr/user/contract/' + praseStrEmpty(userId),
    method: 'get'
  })
}
export function getHrUserCert(userId) {
  return request({
    url: '/hr/user/cert/' + praseStrEmpty(userId),
    method: 'get'
  })
}

// 新增用户
export function addHrUser(data) {
  return request({
    url: '/hr/user',
    method: 'post',
    data: data
  })
}

// 修改用户
export function updateHrUser(data) {
  return request({
    url: '/hr/user',
    method: 'put',
    data: data
  })
}

// 删除用户
export function delHrUser(userId) {
  return request({
    url: '/hr/user/' + userId,
    method: 'delete'
  })
}

// 导出用户
export function exportHrUser(query) {
  return request({
    url: '/hr/user/export',
    method: 'get',
    params: query
  })
}
export function exportHrUserDept(query) {
  return request({
    url: '/hr/user/dept/export',
    method: 'get',
    params: query
  })
}
export function exportLeaveData(query) {
  return request({
    url: '/hr/user/exportLeaveData',
    method: 'get',
    params: query
  })
}
// 下载用户导入模板
export function importHrUserTemplate() {
  return request({
    url: '/hr/user/importTemplate',
    method: 'get'
  })
}

// 查询用户考勤方案
export function getHrUserPlan() {
  return request({
    url: '/hr/user/plan',
    method: 'get'
  })
}

export function getCurrentUser() {
  return request({
    url: '/hr/user/current',
    method: 'get'
  })
}
export function getCurrentDeptInfo() {
  return request({
    url: '/hr/user/currentDeptInfo',
    method: 'get'
  })
}
export function addUserCode(data) {
  return request({
    url: '/hr/user/userCode',
    method: 'post',
    data: data
  })
}

export function getWorkUserById(userId) {
  return request({
    url: '/hr/user/getWorkUserById/' + userId,
    method: 'get'
  })
}

export function submitAudit(data) {
  return request({
    url: '/hr/user/submitAudit',
    method: 'put',
    data: data
  })
}

//撤销申请
export function cancelAudit(data) {
  return request({
    url: '/hr/user/cancelAudit',
    method: 'put',
    data: data
  })
}

export function exemptHrUserAudit(instanceId) {
  return request({
    url: '/hr/user/exemptAudit/' + instanceId,
    method: 'put',
  })
}

export function sendSyqKh(data) {
  return request({
    url: '/hr/user/sendSyqKh',
    method: 'post',
    data,
  })
}

export function sendZzKhPj(data) {
  return request({
    url: '/hr/user/sendZzKhPj',
    method: 'post',
    data,
  })
}

export function exportUserSummary(userId) {
  return request({
    url: '/hr/user/exportSummary/'+ userId,
    method: 'get'
  })
}

export function exportProbationList(query) {
  return request({
    url: '/hr/user/exportProbationList',
    method: 'get',
    params: query
  })
}

export function printLable(data) {
  return request({
    url: '/hr/user/printLable',
    method: 'post',
    data,
    responseType: 'blob'
  })
}
