import request from '@/utils/request'

// 查询包材订单商品列表
export function listMaterialOrderGoods(query) {
  return request({
    url: '/resource/materialOrderGoods/list',
    method: 'get',
    params: query
  })
}

// 查询包材订单商品详细
export function getMaterialOrderGoods(id) {
  return request({
    url: '/resource/materialOrderGoods/' + id,
    method: 'get'
  })
}

// 删除包材订单商品
export function delMaterialOrderGoods(id) {
  return request({
    url: '/resource/materialOrderGoods/' + id,
    method: 'delete'
  })
}

// 导出包材订单商品
export function exportMaterialOrderGoods(query) {
  return request({
    url: '/resource/materialOrderGoods/export',
    method: 'get',
    params: query
  })
}

export function allMaterialOrderGoods(query) {
  return request({
    url: '/resource/materialOrderGoods/all',
    method: 'get',
    params: query
  })
}
