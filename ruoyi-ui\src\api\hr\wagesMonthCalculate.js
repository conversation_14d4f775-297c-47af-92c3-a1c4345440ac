import request from '@/utils/request'

export function listWagesMonthCalculate(query) {
  return request({
    url: '/hr/wagesMonthCalculate/list',
    method: 'get',
    params: query
  })
}
export function getWagesMonthCalculate(id) {
  return request({
    url: '/hr/wagesMonthCalculate/' + id,
    method: 'get'
  })
}
export function exportWagesMonthCalculate(query) {
  return request({
    url: '/hr/wagesMonthCalculate/export',
    method: 'get',
    params: query
  })
}
export function updateWagesMonthCalculate(data) {
  return request({
    url: '/hr/wagesMonthCalculate',
    method: 'put',
    data: data
  })
}
export function getWagesMonthCalculateSocialSecurity(query) {
  return request({
    url: '/hr/wagesMonthCalculate/socialSecurity/',
    method: 'get',
    params: query
  })
}
export function getTWagesMonthCalculateMonth(data) {
  return request({
    url: '/hr/wagesMonthCalculate/calculated/',
    method: 'post',
    data: data
  })
}
export function delWagesMonthCalculate(id) {
  return request({
    url: '/hr/wagesMonthCalculate/' + id,
    method: 'delete'
  })
}


// 导出员工月度工资明细(正式工)
export function exportMonthCalculateWages(query) {
  return request({
    url: '/hr/wagesMonthCalculate/exportWages',
    method: 'get',
    params: query
  })
}

// 导出员工月度工资明细(劳务工)
export function exportMonthCalculateLaborWages(query) {
  return request({
    url: '/hr/wagesMonthCalculate/exportLaborWages',
    method: 'get',
    params: query
  })
}



