import request from '@/utils/request'

// 查询包材供应商价格列表
export function listMaterialSupplierPrice(query) {
  return request({
    url: '/resource/materialSupplierPrice/list',
    method: 'get',
    params: query
  })
}

// 查询包材供应商价格详细
export function getMaterialSupplierPrice(id) {
  return request({
    url: '/resource/materialSupplierPrice/' + id,
    method: 'get'
  })
}

// 新增包材供应商价格
export function addMaterialSupplierPrice(data) {
  return request({
    url: '/resource/materialSupplierPrice',
    method: 'post',
    data: data
  })
}

// 修改包材供应商价格
export function updateMaterialSupplierPrice(data) {
  return request({
    url: '/resource/materialSupplierPrice',
    method: 'put',
    data: data
  })
}

// 删除包材供应商价格
export function delMaterialSupplierPrice(id) {
  return request({
    url: '/resource/materialSupplierPrice/' + id,
    method: 'delete'
  })
}

// 导出包材供应商价格
export function exportMaterialSupplierPrice(query) {
  return request({
    url: '/resource/materialSupplierPrice/export',
    method: 'get',
    params: query
  })
}