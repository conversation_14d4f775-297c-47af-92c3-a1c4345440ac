import request from '@/utils/request'

// 查询CIR成分列表
export function listCirIngredients(query) {
  return request({
    url: '/rd/cirIngredients/list',
    method: 'get',
    params: query
  })
}

// 查询CIR成分详细
export function getCirIngredients(id) {
  return request({
    url: '/rd/cirIngredients/' + id,
    method: 'get'
  })
}

export function getCirIngredientsByXh(params) {
  return request({
    url: '/rd/cirIngredients/getCirIngredientsByXh',
    method: 'get',
    params
  })
}

// 新增CIR成分
export function addCirIngredients(data) {
  return request({
    url: '/rd/cirIngredients',
    method: 'post',
    data: data
  })
}

// 修改CIR成分
export function updateCirIngredients(data) {
  return request({
    url: '/rd/cirIngredients',
    method: 'put',
    data: data
  })
}

// 删除CIR成分
export function delCirIngredients(id) {
  return request({
    url: '/rd/cirIngredients/' + id,
    method: 'delete'
  })
}

// 导出CIR成分
export function exportCirIngredients(query) {
  return request({
    url: '/rd/cirIngredients/export',
    method: 'get',
    params: query
  })
}

export function importTemplateCirIngredients() {
  return request({
    url: '/rd/cirIngredients/importTemplate',
    method: 'get'
  })
}

export function allCirIngredients(query) {
  return request({
    url: '/rd/cirIngredients/all',
    method: 'get',
    params: query
  })
}
