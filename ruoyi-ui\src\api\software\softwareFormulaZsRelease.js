import request from '@/utils/request'

// 查询配方中试释放列表
export function listSoftwareFormulaZsRelease(query) {
  return request({
    url: '/software/softwareFormulaZsRelease/list',
    method: 'get',
    params: query
  })
}


//配方审核列表
export function listAuditSoftwareFormulaZsRelease(data) {
  return request({
    url: '/software/softwareFormulaZsRelease/audit',
    method: 'post',
    data: data
  })
}

// 查询配方中试释放详细
export function getSoftwareFormulaZsRelease(id) {
  return request({
    url: '/software/softwareFormulaZsRelease/' + id,
    method: 'get'
  })
}


//撤销审核
export function cancelAudit(data) {
  return request({
    url: '/software/softwareFormulaZsRelease/cancelAudit',
    method: 'put',
    data: data
  })
}


// 新增配方中试释放
export function addSoftwareFormulaZsRelease(data) {
  return request({
    url: '/software/softwareFormulaZsRelease',
    method: 'post',
    data: data
  })
}

// 修改配方中试释放
export function updateSoftwareFormulaZsRelease(data) {
  return request({
    url: '/software/softwareFormulaZsRelease',
    method: 'put',
    data: data
  })
}

// 删除配方中试释放
export function delSoftwareFormulaZsRelease(id) {
  return request({
    url: '/software/softwareFormulaZsRelease/' + id,
    method: 'delete'
  })
}

// 导出配方中试释放
export function exportSoftwareFormulaZsRelease(query) {
  return request({
    url: '/software/softwareFormulaZsRelease/export',
    method: 'get',
    params: query
  })
}
