import request from '@/utils/request'

export function listLeaveUserLeadershipSee(query) {
  return request({
    url: '/hr/leaveUser/leadershipSee',
    method: 'get',
    params: query
  })
}
// 查询请假用户申请列表
export function listLeaveUser(query) {
  return request({
    url: '/hr/leaveUser/list',
    method: 'get',
    params: query
  })
}
// 查询请假用户申请审批列表
export function listAuditLeaveUser(query) {
  return request({
    url: '/hr/leaveUser/audit',
    method: 'get',
    params: query
  })
}
// 查询请假申请列表
export function logLeaveUser(query) {
  return request({
    url: '/hr/leaveUser/log',
    method: 'get',
    params: query
  })
}

// 查询请假申请详情
export function getLeaveUser(id) {
  return request({
    url: '/hr/leaveUser/' + id,
    method: 'get'
  })
}

// 查询请假申请详情
export function queryDeputyGeneral() {
  return request({
    url: '/hr/leaveUser/queryDeputyGeneral',
    method: 'get'
  })
}

// 查询请假用户使用情况
export function getUsage(params) {
  return request({
    url: '/hr/leaveUser/usage',
    method: 'get',
    params: params
  })
}

// 新增请假用户申请
export function addLeaveUser(data) {
  return request({
    url: '/hr/leaveUser',
    method: 'post',
    data: data
  })
}

// 修改请假用户申请
export function updateLeaveUser(data) {
  return request({
    url: '/hr/leaveUser',
    method: 'put',
    data: data
  })
}

// 删除请假用户申请
export function delLeaveUser(id) {
  return request({
    url: '/hr/leaveUser/' + id,
    method: 'delete'
  })
}

// 请假用户审批
export function auditLeaveUser(data) {
  return request({
    url: '/hr/leaveUser/audit',
    method: 'post',
    data: data
  })
}

export function addLeaveUserUndo(data) {
  return request({
    url: '/hr/leaveUser/undo',
    method: 'post',
    data: data
  })
}

export function copyLeaveUser(query) {
  return request({
    url: '/hr/leaveUser/copy',
    method: 'get',
    params: query
  })
}

export function delOvertimeUser(id) {
  return request({
    url: '/hr/leaveUser/' + id,
    method: 'delete'
  })
}
export function submitAudit(data) {
  return request({
    url: '/hr/leaveUser/submitAudit',
    method: 'put',
    data: data
  })
}
export function cancelAudit(data) {
  return request({
    url: '/hr/leaveUser/cancelAudit',
    method: 'put',
    data: data
  })
}

export function pigeonholeLeaveUser(data) {
  return request({
    url: '/hr/leaveUser/pigeonhole',
    method: 'post',
    data: data
  })
}
