import request from '@/utils/request'

// 查询其它出库申请列表
export function listOtherOutboundUser(query) {
  return request({
    url: '/process/otherOutboundUser/list',
    method: 'get',
    params: query
  })
}
// 查询其它出库申请审批列表
export function listAuditOtherOutboundUser(query) {
  return request({
    url: '/process/otherOutboundUser/audit',
    method: 'get',
    params: query
  })
}
// 查询请假申请列表
export function logOtherOutboundUser(query) {
  return request({
    url: '/process/otherOutboundUser/log',
    method: 'get',
    params: query
  })
}

// 查询请假申请详情
export function getOtherOutboundUser(id) {
  return request({
    url: '/process/otherOutboundUser/' + id,
    method: 'get'
  })
}

export function chooseOtherOutboundUser(query) {
  return request({
    url: '/process/otherOutboundUser/choose',
    method: 'get',
    params: query
  })
}

// 新增其它出库申请
export function addOtherOutboundUser(data) {
  return request({
    url: '/process/otherOutboundUser',
    method: 'post',
    data: data
  })
}

// 其它出库审批
export function auditOtherOutboundUser(data) {
  return request({
    url: '/process/otherOutboundUser/audit',
    method: 'post',
    data: data
  })
}
export function editOtherOutboundUser(data) {
  return request({
    url: '/process/otherOutboundUser/edit',
    method: 'post',
    data: data
  })
}
export function submitAudit(data) {
  return request({
    url: '/process/otherOutboundUser/submitAudit',
    method: 'put',
    data: data
  })
}
export function cancelAudit(data) {
  return request({
    url: '/process/otherOutboundUser/cancelAudit',
    method: 'put',
    data: data
  })
}
export function verifyOtherOutboundUser(data) {
  return request({
    url: '/process/otherOutboundUser/verify',
    method: 'post',
    data: data
  })
}
export function pigeonholeOtherOutboundUser(data) {
  return request({
    url: '/process/otherOutboundUser/pigeonhole',
    method: 'post',
    data: data
  })
}

export function getOrderWarehouseAll() {
  return request({
    url: '/process/otherOutboundUser/warehouse',
    method: 'get'
  })
}

export function queryErpOutboundInfo(query) {
  return request({
    url: '/process/otherOutboundUser/queryErpOutboundInfo',
    method: 'get',
    params: query
  })
}
export function getOtherOutboundErpGoods(query) {
  return request({
    url: '/process/otherOutboundUser/erpGoods',
    method: 'get',
    params: query
  })
}
export function getMaterialPriceInfo(query) {
  return request({
    url: '/process/otherOutboundUser/materialPrice',
    method: 'get',
    params: query
  })
}
export function editPrintNumOtherOutboundUser(data) {
  return request({
    url: '/process/otherOutboundUser/editPrintNum',
    method: 'post',
    data: data
  })
}

export function exportOtherOutboundUser(query) {
  return request({
    url: '/process/otherOutboundUser/export',
    method: 'get',
    params: query
  })
}

export function exportOtherOutYl(query) {
  return request({
    url: '/process/otherOutboundUser/exportYl',
    method: 'get',
    params: query
  })
}
