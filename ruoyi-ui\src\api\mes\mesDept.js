import request from '@/utils/request'

// 查询部门基本数据列表
export function listMesDept(query) {
  return request({
    url: '/mes/mesDept/list',
    method: 'get',
    params: query
  })
}

// 查询部门基本数据详细
export function getMesDept(departmentno) {
  return request({
    url: '/mes/mesDept/' + departmentno,
    method: 'get'
  })
}

// 新增部门基本数据
export function addMesDept(data) {
  return request({
    url: '/mes/mesDept',
    method: 'post',
    data: data
  })
}

// 修改部门基本数据
export function updateMesDept(data) {
  return request({
    url: '/mes/mesDept',
    method: 'put',
    data: data
  })
}

// 删除部门基本数据
export function delMesDept(departmentno) {
  return request({
    url: '/mes/mesDept/' + departmentno,
    method: 'delete'
  })
}

// 导出部门基本数据
export function exportMesDept(query) {
  return request({
    url: '/mes/mesDept/export',
    method: 'get',
    params: query
  })
}