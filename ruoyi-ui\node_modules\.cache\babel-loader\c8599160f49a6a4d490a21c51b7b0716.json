{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\mesLog\\save.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\mesLog\\save.vue", "mtime": 1753954679646}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\babel.config.js", "mtime": 1743382537964}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9zZWFuL3dvcmtzcGFjZS9lbm93X3Byb2plY3QvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maWx0ZXIuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmluY2x1ZGVzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnB1c2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnNsaWNlLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuZmlsdGVyLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmpzb24uc3RyaW5naWZ5LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5udW1iZXIuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC5rZXlzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zZXQuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnNldC5kaWZmZXJlbmNlLnYyLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zZXQuaW50ZXJzZWN0aW9uLnYyLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zZXQuaXMtZGlzam9pbnQtZnJvbS52Mi5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc2V0LmlzLXN1YnNldC1vZi52Mi5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc2V0LmlzLXN1cGVyc2V0LW9mLnYyLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zZXQuc3ltbWV0cmljLWRpZmZlcmVuY2UudjIuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnNldC51bmlvbi52Mi5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLmluY2x1ZGVzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaXRlcmF0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IuanMiKTsKdmFyIF90b0NvbnN1bWFibGVBcnJheTIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkM6L3NlYW4vd29ya3NwYWNlL2Vub3dfcHJvamVjdC9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy90b0NvbnN1bWFibGVBcnJheS5qcyIpKTsKdmFyIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQzovc2Vhbi93b3Jrc3BhY2UvZW5vd19wcm9qZWN0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIuanMiKSk7CnZhciBfcmVnZW5lcmF0b3JSdW50aW1lMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQzovc2Vhbi93b3Jrc3BhY2UvZW5vd19wcm9qZWN0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL3JlZ2VuZXJhdG9yUnVudGltZS5qcyIpKTsKdmFyIF9hc3luY1RvR2VuZXJhdG9yMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQzovc2Vhbi93b3Jrc3BhY2UvZW5vd19wcm9qZWN0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2FzeW5jVG9HZW5lcmF0b3IuanMiKSk7CnZhciBfbWVzTG9nID0gcmVxdWlyZSgiQC9hcGkvcHJvZHVjdGlvbi9tZXNMb2ciKTsKdmFyIF9wbGFuQXJlYUhvdXJzID0gcmVxdWlyZSgiQC9hcGkvcHJvZHVjdGlvbi9wbGFuQXJlYUhvdXJzIik7CnZhciBfd2lwY29udFBhcnRpYWxvdXQgPSByZXF1aXJlKCJAL2FwaS9tZXMvd2lwY29udFBhcnRpYWxvdXQiKTsKdmFyIF9hcmVhID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3ZpZXdzL3Byb2R1Y3Rpb24vbWVzTG9nL2FyZWEudnVlIikpOwp2YXIgX21lc1ZpZXcgPSByZXF1aXJlKCJAL2FwaS9tZXMvbWVzVmlldyIpOwp2YXIgX3RhYmxlID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3ZpZXdzL3Byb2R1Y3Rpb24vbGF5b3V0L3Byb2Nlc3MvdGFibGUudnVlIikpOwp2YXIgX2FyY2hpdmUgPSByZXF1aXJlKCJAL2FwaS9wcm9qZWN0L2FyY2hpdmUiKTsKdmFyIF9wcm9qZWN0ID0gcmVxdWlyZSgiQC9hcGkvcHJvamVjdC9wcm9qZWN0Iik7CnZhciBfc2NoZWR1bGVTdGFuZGFyZCA9IHJlcXVpcmUoIkAvYXBpL29yZGVyL3NjaGVkdWxlU3RhbmRhcmQiKTsKdmFyIF9xYyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC92aWV3cy9tZXMvcHJvZHVjdGlvbi9xYy52dWUiKSk7CnZhciBfZmluaXNoZWRJbnNwZWN0aW9uID0gcmVxdWlyZSgiQC9hcGkvcWMvZmluaXNoZWRJbnNwZWN0aW9uIik7CnZhciBfd2lwQ29udE1hdGVyaWFsTG90ID0gcmVxdWlyZSgiQC9hcGkvbWVzL3dpcENvbnRNYXRlcmlhbExvdCIpOwp2YXIgX2VycCA9IHJlcXVpcmUoIkAvYXBpL2NvbW1vbi9lcnAiKTsKdmFyIF9kYXRhQ2hhcnRzID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3ZpZXdzL3Byb2R1Y3Rpb24vbWVzTG9nL2RhdGFDaGFydHMudnVlIikpOwp2YXIgX2d6bCA9IHJlcXVpcmUoIkAvYXBpL3NvcC9nemwiKTsKdmFyIF9zY2hlZHVsZSA9IHJlcXVpcmUoIkAvYXBpL29yZGVyL3NjaGVkdWxlIik7CnZhciBfbWF0ZXJpYWxUYWJzID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3ZpZXdzL3Byb2R1Y3Rpb24vbWVzTG9nL21hdGVyaWFsVGFicy52dWUiKSk7CnZhciBfbG9nNCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC92aWV3cy9wcm9kdWN0aW9uL21lc0xvZy9sb2cudnVlIikpOwp2YXIgX2xvZzUgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdmlld3MvbWVzL3Byb2R1Y3Rpb24vbG9nLnZ1ZSIpKTsKdmFyIF9zY2hlZHVsZVRhYmxlID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3ZpZXdzL3Byb2R1Y3Rpb24vbWVzTG9nL3NjaGVkdWxlVGFibGUudnVlIikpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgbmFtZTogJ21lc0xvZ1NhdmUnLAogIGNvbXBvbmVudHM6IHsKICAgIE1lc0xvZ1NjaGVkdWxlVGFibGU6IF9zY2hlZHVsZVRhYmxlLmRlZmF1bHQsCiAgICBNZXNQcm9kdWN0aW9uTG9nOiBfbG9nNS5kZWZhdWx0LAogICAgTWVzTG9nUHJvZHVjdGlvbkxvZzogX2xvZzQuZGVmYXVsdCwKICAgIE1lc0xvZ01hdGVyaWFsVGFiczogX21hdGVyaWFsVGFicy5kZWZhdWx0LAogICAgTWVzTG9nRGF0YUNoYXJ0czogX2RhdGFDaGFydHMuZGVmYXVsdCwKICAgIE1lc1FjOiBfcWMuZGVmYXVsdCwKICAgIFByb2Nlc3NUYWJsZTogX3RhYmxlLmRlZmF1bHQsCiAgICBNZXNMb2dBcmVhOiBfYXJlYS5kZWZhdWx0CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIGJ0bkxvYWRpbmc6IGZhbHNlLAogICAgICByZWFkb25seTogZmFsc2UsCiAgICAgIGZvcm06IHt9LAogICAgICBydWxlczoge30sCiAgICAgIGN1cnJlbnRUYWI6ICcnLAogICAgICBzYWlsaW5nc09wdGlvbnM6IFt7CiAgICAgICAgbGFiZWw6ICfnmb3nj60nLAogICAgICAgIHZhbHVlOiAnMCcKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAn5pma54+tJywKICAgICAgICB2YWx1ZTogJzEnCiAgICAgIH1dLAogICAgICBvdXRBcnJheTogW10sCiAgICAgIGxvdExvZ3M6IFtdLAogICAgICBtZXNMb3RXYWl0TGlzdDogW10sCiAgICAgIHVzZXJBcnJheTogW10sCiAgICAgIHByb2plY3Q6IHt9LAogICAgICBib21EYXRhOiBbXSwKICAgICAgYm9tVHJlZTogW10sCiAgICAgIGRpZmZEYXRhQXJyYXk6IFtdLAogICAgICBkaWZmRGF0YU9wdGlvbnM6IHt9LAogICAgICBpbnNwZWN0aW9uTGlzdDogW10sCiAgICAgIHFjT3B0aW9uczoge30sCiAgICAgIG1hdGVyaWFsTG9nQXJyYXk6IFtdLAogICAgICBzY2hlZHVsZVN0YW5kYXJkOiB7fSwKICAgICAgbWF0ZXJpYWxBcnJheTogW10sCiAgICAgIHNjaGVkdWxlOiB7fSwKICAgICAgYmNwTGlzdDogW10sCiAgICAgIGJjcEFycmF5OiBbXSwKICAgICAgb3RoZXJBcnJheTogW10sCiAgICAgIHNjaGVkdWxlQmNwQXJyYXk6IFtdLAogICAgICBzY2hlZHVsZU90aGVyQXJyYXk6IFtdCiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQucHJldiA9IF9jb250ZXh0Lm5leHQpIHsKICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgfQogICAgICB9LCBfY2FsbGVlKTsKICAgIH0pKSgpOwogIH0sCiAgbWV0aG9kczogewogICAgdGFiQ2hhbmdlOiBmdW5jdGlvbiB0YWJDaGFuZ2UoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUyKCkgewogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUyJChfY29udGV4dDIpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0Mi5wcmV2ID0gX2NvbnRleHQyLm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gMjsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMuJG5leHRUaWNrKCk7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBpZiAoIShfdGhpcy5jdXJyZW50VGFiID09PSAncGVyc29uJykpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gNTsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDEzOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDU6CiAgICAgICAgICAgICAgaWYgKCEoX3RoaXMuY3VycmVudFRhYiA9PT0gJ2NoYXJ0cycpKSB7CiAgICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDEwOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gODsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMucmVkdWNlRGlmZkNoYXJ0KCk7CiAgICAgICAgICAgIGNhc2UgODoKICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDEzOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDEwOgogICAgICAgICAgICAgIGlmICghKF90aGlzLmN1cnJlbnRUYWIgPT09ICdxYycpKSB7CiAgICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDEzOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gMTM7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzLnJlZHVjZVFjQ2hhcnQoKTsKICAgICAgICAgICAgY2FzZSAxMzoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQyLnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMik7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGNhbmNlbDogZnVuY3Rpb24gY2FuY2VsKCkgewogICAgICB0aGlzLiRwYXJlbnQuJHBhcmVudC5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICByZXNldDogZnVuY3Rpb24gcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBpZDogbnVsbCwKICAgICAgICBjb2RlOiBudWxsLAogICAgICAgIHdvcmtEYXRlOiBudWxsLAogICAgICAgIHNhaWxpbmdzOiBudWxsLAogICAgICAgIGFyZWFObzogbnVsbCwKICAgICAgICBsaW5lTGVhZGVyOiBudWxsLAogICAgICAgIG1pbnV0ZXM6IG51bGwsCiAgICAgICAgaW5OdW1zOiBudWxsLAogICAgICAgIGdvb2RzTnVtczogbnVsbCwKICAgICAgICBiYWROdW1zOiBudWxsCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICAgIHRoaXMucHJvamVjdCA9IHt9OwogICAgICB0aGlzLmJvbURhdGEgPSBbXTsKICAgICAgdGhpcy5ib21UcmVlID0gW107CiAgICAgIHRoaXMubWF0ZXJpYWxMb2dBcnJheSA9IFtdOwogICAgICB0aGlzLm91dEFycmF5ID0gW107CiAgICAgIHRoaXMubG90TG9ncyA9IFtdOwogICAgICB0aGlzLm1lc0xvdFdhaXRMaXN0ID0gW107CiAgICAgIHRoaXMudXNlckFycmF5ID0gW107CiAgICAgIHRoaXMuZGlmZkRhdGFBcnJheSA9IFtdOwogICAgICB0aGlzLmRpZmZEYXRhT3B0aW9ucyA9IHt9OwogICAgICB0aGlzLmluc3BlY3Rpb25MaXN0ID0gW107CiAgICAgIHRoaXMucWNPcHRpb25zID0ge307CiAgICAgIHRoaXMuc2NoZWR1bGUgPSB7fTsKICAgICAgdGhpcy5zY2hlZHVsZVN0YW5kYXJkID0ge307CiAgICAgIHRoaXMubWF0ZXJpYWxBcnJheSA9IFtdOwogICAgICB0aGlzLmJjcExpc3QgPSBbXTsKICAgICAgdGhpcy5iY3BBcnJheSA9IFtdOwogICAgICB0aGlzLm90aGVyQXJyYXkgPSBbXTsKICAgICAgdGhpcy5zY2hlZHVsZUJjcEFycmF5ID0gW107CiAgICAgIHRoaXMuc2NoZWR1bGVPdGhlckFycmF5ID0gW107CiAgICB9LAogICAgZGlmZkhvdXJzOiBmdW5jdGlvbiBkaWZmSG91cnMoc3RhcnRUaW1lLCBlbmRUaW1lKSB7CiAgICAgIGlmIChzdGFydFRpbWUgJiYgZW5kVGltZSkgewogICAgICAgIHZhciBtaW51dGVzID0gdGhpcy5tb21lbnQoZW5kVGltZSwgJ1lZWVktTU0tREQgaGg6bW0nKS5kaWZmKHRoaXMubW9tZW50KHN0YXJ0VGltZSwgJ1lZWVktTU0tREQgaGg6bW0nKSwgJ21pbnV0ZXMnKTsKICAgICAgICBpZiAobWludXRlcykgewogICAgICAgICAgcmV0dXJuIHRoaXMuZGl2aWRlKG1pbnV0ZXMsIDYwKS50b051bWJlcigpOwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIHJlZHVjZVFjQ2hhcnQ6IGZ1bmN0aW9uIHJlZHVjZVFjQ2hhcnQoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlMygpIHsKICAgICAgICB2YXIgbWVzUWMsIHFjQ2hhcnRzOwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUzJChfY29udGV4dDMpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0My5wcmV2ID0gX2NvbnRleHQzLm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0My5uZXh0ID0gMjsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMyLiRuZXh0VGljaygpOwogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgbWVzUWMgPSBfdGhpczIuJHJlZnMubWVzUWM7CiAgICAgICAgICAgICAgaWYgKCFtZXNRYykgewogICAgICAgICAgICAgICAgX2NvbnRleHQzLm5leHQgPSA4OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHFjQ2hhcnRzID0gbWVzUWMuJHJlZnMucWNDaGFydHM7CiAgICAgICAgICAgICAgaWYgKCFxY0NoYXJ0cykgewogICAgICAgICAgICAgICAgX2NvbnRleHQzLm5leHQgPSA4OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0My5uZXh0ID0gODsKICAgICAgICAgICAgICByZXR1cm4gcWNDaGFydHMuaW5pdChfdGhpczIucWNPcHRpb25zKTsKICAgICAgICAgICAgY2FzZSA4OgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDMuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUzKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgcmVkdWNlRGlmZkNoYXJ0OiBmdW5jdGlvbiByZWR1Y2VEaWZmQ2hhcnQoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlNCgpIHsKICAgICAgICB2YXIgbWVzRGF0YUNoYXJ0cywgZGlmZkNoYXJ0LCB0aW1lTGluZUNoYXJ0OwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWU0JChfY29udGV4dDQpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0NC5wcmV2ID0gX2NvbnRleHQ0Lm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0NC5uZXh0ID0gMjsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMzLiRuZXh0VGljaygpOwogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgbWVzRGF0YUNoYXJ0cyA9IF90aGlzMy4kcmVmcy5tZXNEYXRhQ2hhcnRzOwogICAgICAgICAgICAgIGlmICghbWVzRGF0YUNoYXJ0cykgewogICAgICAgICAgICAgICAgX2NvbnRleHQ0Lm5leHQgPSAxMDsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBkaWZmQ2hhcnQgPSBtZXNEYXRhQ2hhcnRzLiRyZWZzLmRpZmZDaGFydDsKICAgICAgICAgICAgICBpZiAoIWRpZmZDaGFydCkgewogICAgICAgICAgICAgICAgX2NvbnRleHQ0Lm5leHQgPSA4OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0NC5uZXh0ID0gODsKICAgICAgICAgICAgICByZXR1cm4gZGlmZkNoYXJ0LmluaXQoX3RoaXMzLmRpZmZEYXRhT3B0aW9ucyk7CiAgICAgICAgICAgIGNhc2UgODoKICAgICAgICAgICAgICB0aW1lTGluZUNoYXJ0ID0gbWVzRGF0YUNoYXJ0cy4kcmVmcy50aW1lTGluZUNoYXJ0OwogICAgICAgICAgICAgIGlmICh0aW1lTGluZUNoYXJ0KSB7fQogICAgICAgICAgICBjYXNlIDEwOgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDQuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU0KTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgYnVpbGRQcm9kdWN0aW9uTG9nOiBmdW5jdGlvbiBidWlsZFByb2R1Y3Rpb25Mb2coKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlNSgpIHsKICAgICAgICB2YXIgZm9ybSwgbWF0ZXJpYWxBcnJheSwgYm9tUmVzLCBiY3BMaXN0LCBfaXRlcmF0b3IsIF9zdGVwLCBpdGVtLCBnemxQYXJhbXMsIGd6bFJlcywgZ3psLCByZXMsIGJjcEFycmF5LCBvdGhlckFycmF5LCBzY2hlZHVsZUJjcEFycmF5LCBzY2hlZHVsZU90aGVyQXJyYXksIF9pdGVyYXRvcjIsIF9zdGVwMiwgbywgX2l0ZXJhdG9yMywgX3N0ZXAzLCBfbzsKICAgICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlNSQoX2NvbnRleHQ1KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDUucHJldiA9IF9jb250ZXh0NS5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBmb3JtID0gX3RoaXM0LmZvcm07CiAgICAgICAgICAgICAgbWF0ZXJpYWxBcnJheSA9IF90aGlzNC5tYXRlcmlhbEFycmF5OwogICAgICAgICAgICAgIF9jb250ZXh0NS5uZXh0ID0gNDsKICAgICAgICAgICAgICByZXR1cm4gKDAsIF9lcnAuZ2V0Qm9tQnlFcnBDb2RlKShmb3JtLnByb2R1Y3RObyk7CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICBib21SZXMgPSBfY29udGV4dDUuc2VudDsKICAgICAgICAgICAgICBpZiAoIShib21SZXMuY29kZSA9PT0gMjAwKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQ1Lm5leHQgPSAyODsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBiY3BMaXN0ID0gYm9tUmVzLmRhdGEuZmlsdGVyKGZ1bmN0aW9uIChpKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gaS5tYjAwNSA9PT0gJzEwMyc7CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgX2l0ZXJhdG9yID0gKDAsIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyMi5kZWZhdWx0KShiY3BMaXN0KTsKICAgICAgICAgICAgICBfY29udGV4dDUucHJldiA9IDg7CiAgICAgICAgICAgICAgX2l0ZXJhdG9yLnMoKTsKICAgICAgICAgICAgY2FzZSAxMDoKICAgICAgICAgICAgICBpZiAoKF9zdGVwID0gX2l0ZXJhdG9yLm4oKSkuZG9uZSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQ1Lm5leHQgPSAxOTsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBpdGVtID0gX3N0ZXAudmFsdWU7CiAgICAgICAgICAgICAgZ3psUGFyYW1zID0gewogICAgICAgICAgICAgICAgbWQwMDM6IGl0ZW0ubWQwMDMsCiAgICAgICAgICAgICAgICBtZDAwMTogaXRlbS5tZDAwMQogICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgX2NvbnRleHQ1Lm5leHQgPSAxNTsKICAgICAgICAgICAgICByZXR1cm4gKDAsIF9nemwuZ2V0R3psQnlQYXJhbXMpKGd6bFBhcmFtcyk7CiAgICAgICAgICAgIGNhc2UgMTU6CiAgICAgICAgICAgICAgZ3psUmVzID0gX2NvbnRleHQ1LnNlbnQ7CiAgICAgICAgICAgICAgaWYgKGd6bFJlcy5jb2RlID09PSAyMDAgJiYgZ3psUmVzLmRhdGEpIHsKICAgICAgICAgICAgICAgIGd6bCA9IGd6bFJlcy5kYXRhOwogICAgICAgICAgICAgICAgaXRlbS5tYXggPSBnemwubWF4OwogICAgICAgICAgICAgICAgaXRlbS5hdmcgPSBnemwuYXZnOwogICAgICAgICAgICAgICAgaXRlbS5taW4gPSBnemwubWluOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgY2FzZSAxNzoKICAgICAgICAgICAgICBfY29udGV4dDUubmV4dCA9IDEwOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDE5OgogICAgICAgICAgICAgIF9jb250ZXh0NS5uZXh0ID0gMjQ7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMjE6CiAgICAgICAgICAgICAgX2NvbnRleHQ1LnByZXYgPSAyMTsKICAgICAgICAgICAgICBfY29udGV4dDUudDAgPSBfY29udGV4dDVbImNhdGNoIl0oOCk7CiAgICAgICAgICAgICAgX2l0ZXJhdG9yLmUoX2NvbnRleHQ1LnQwKTsKICAgICAgICAgICAgY2FzZSAyNDoKICAgICAgICAgICAgICBfY29udGV4dDUucHJldiA9IDI0OwogICAgICAgICAgICAgIF9pdGVyYXRvci5mKCk7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0NS5maW5pc2goMjQpOwogICAgICAgICAgICBjYXNlIDI3OgogICAgICAgICAgICAgIF90aGlzNC5iY3BMaXN0ID0gYmNwTGlzdDsKICAgICAgICAgICAgY2FzZSAyODoKICAgICAgICAgICAgICBfY29udGV4dDUubmV4dCA9IDMwOwogICAgICAgICAgICAgIHJldHVybiAoMCwgX21lc0xvZy5nZXRNYXRlcmlhbExvZ0xpc3QpKGZvcm0uaWQpOwogICAgICAgICAgICBjYXNlIDMwOgogICAgICAgICAgICAgIHJlcyA9IF9jb250ZXh0NS5zZW50OwogICAgICAgICAgICAgIGlmIChyZXMpIHsKICAgICAgICAgICAgICAgIGJjcEFycmF5ID0gcmVzLmJjcEFycmF5OwogICAgICAgICAgICAgICAgb3RoZXJBcnJheSA9IHJlcy5vdGhlckFycmF5OwogICAgICAgICAgICAgICAgc2NoZWR1bGVCY3BBcnJheSA9IHJlcy5zY2hlZHVsZUJjcEFycmF5OwogICAgICAgICAgICAgICAgc2NoZWR1bGVPdGhlckFycmF5ID0gcmVzLnNjaGVkdWxlT3RoZXJBcnJheTsKICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKHNjaGVkdWxlQmNwQXJyYXkpOwogICAgICAgICAgICAgICAgY29uc29sZS5sb2coc2NoZWR1bGVPdGhlckFycmF5KTsKICAgICAgICAgICAgICAgIGlmIChiY3BBcnJheSkgewogICAgICAgICAgICAgICAgICBfdGhpczQuYmNwQXJyYXkgPSBiY3BBcnJheTsKICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yMiA9ICgwLCBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcjIuZGVmYXVsdCkoYmNwQXJyYXkpOwogICAgICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgICAgIGZvciAoX2l0ZXJhdG9yMi5zKCk7ICEoX3N0ZXAyID0gX2l0ZXJhdG9yMi5uKCkpLmRvbmU7KSB7CiAgICAgICAgICAgICAgICAgICAgICBvID0gX3N0ZXAyLnZhbHVlOwogICAgICAgICAgICAgICAgICAgICAgaWYgKCFtYXRlcmlhbEFycmF5Lm1hcChmdW5jdGlvbiAoaSkgewogICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gaS5tYXRlcmlhbENvZGU7CiAgICAgICAgICAgICAgICAgICAgICB9KS5pbmNsdWRlcyhvLm1hdGVyaWFsQ29kZSkpIHsKICAgICAgICAgICAgICAgICAgICAgICAgbWF0ZXJpYWxBcnJheS5wdXNoKHsKICAgICAgICAgICAgICAgICAgICAgICAgICBtYXRlcmlhbENvZGU6IG8ubWF0ZXJpYWxDb2RlLAogICAgICAgICAgICAgICAgICAgICAgICAgIHJlbWFyazogbnVsbAogICAgICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjIuZShlcnIpOwogICAgICAgICAgICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjIuZigpOwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBpZiAob3RoZXJBcnJheSkgewogICAgICAgICAgICAgICAgICBfdGhpczQub3RoZXJBcnJheSA9IG90aGVyQXJyYXk7CiAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjMgPSAoMCwgX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIyLmRlZmF1bHQpKG90aGVyQXJyYXkpOwogICAgICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgICAgIGZvciAoX2l0ZXJhdG9yMy5zKCk7ICEoX3N0ZXAzID0gX2l0ZXJhdG9yMy5uKCkpLmRvbmU7KSB7CiAgICAgICAgICAgICAgICAgICAgICBfbyA9IF9zdGVwMy52YWx1ZTsKICAgICAgICAgICAgICAgICAgICAgIGlmICghbWF0ZXJpYWxBcnJheS5tYXAoZnVuY3Rpb24gKGkpIHsKICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGkubWF0ZXJpYWxDb2RlOwogICAgICAgICAgICAgICAgICAgICAgfSkuaW5jbHVkZXMoX28ubWF0ZXJpYWxDb2RlKSkgewogICAgICAgICAgICAgICAgICAgICAgICBtYXRlcmlhbEFycmF5LnB1c2goewogICAgICAgICAgICAgICAgICAgICAgICAgIG1hdGVyaWFsQ29kZTogX28ubWF0ZXJpYWxDb2RlLAogICAgICAgICAgICAgICAgICAgICAgICAgIHJlbWFyazogbnVsbAogICAgICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjMuZShlcnIpOwogICAgICAgICAgICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjMuZigpOwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBpZiAoc2NoZWR1bGVCY3BBcnJheSkgewogICAgICAgICAgICAgICAgICBfdGhpczQuc2NoZWR1bGVCY3BBcnJheSA9IHNjaGVkdWxlQmNwQXJyYXk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBpZiAoc2NoZWR1bGVPdGhlckFycmF5KSB7CiAgICAgICAgICAgICAgICAgIF90aGlzNC5zY2hlZHVsZU90aGVyQXJyYXkgPSBzY2hlZHVsZU90aGVyQXJyYXk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfQogICAgICAgICAgICBjYXNlIDMyOgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDUuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU1LCBudWxsLCBbWzgsIDIxLCAyNCwgMjddXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGJ1aWxkTWF0ZXJpYWxBcnJheTogZnVuY3Rpb24gYnVpbGRNYXRlcmlhbEFycmF5KCkgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTYoKSB7CiAgICAgICAgdmFyIGZvcm0sIHNjaGVkdWxlLCBmb3JtUmF0ZSwgc2NoZWR1bGVDb2RlLCBtYXRlcmlhbExvZ0xpc3QsIGFyciwgZXJwQm9tRGF0YSwgc2NoZWR1bGVOdW1zLCBtYXRlcmlhbE5vU2V0LCBfaXRlcmF0b3I0LCBfc3RlcDQsIGxvZywgbWF0ZXJpYWxMb2dBcnJheSwgX2l0ZXJhdG9yNSwgX3N0ZXA1LCBtYXRlcmlhbE5vLCB0ZTAxMFNldCwgc2pCb21OdW0sIGNiMDEwLCBtYXRlcmlhbE5hbWUsIHRlMDA2LCB0ZTAwNVN1bSwgbG90U3VtcywgZXJwU3lsU3VtLCBfaXRlcmF0b3I2LCBfc3RlcDYsIGIsIF9pdGVyYXRvcjcsIF9zdGVwNywgX2xvZywgdGUwMTBBcnJheSwgX2l0ZXJhdG9yOCwgX3N0ZXA4LCB0ZTAxMCwgZXJwTGxOdW1zLCBlcnBTeWxOdW1zLCBfaXRlcmF0b3I5LCBfc3RlcDksIG90LCBfaXRlcmF0b3IxMywgX3N0ZXAxMywgdCwgZXF1aXBtZW50U2V0LCBfaXRlcmF0b3IxMCwgX3N0ZXAxMCwgX2xvZzIsIGVxdWlwbWVudEFycmF5LCBfaXRlcmF0b3IxMSwgX3N0ZXAxMSwgZXF1aXBtZW50Tm8sIGwsIF9pdGVyYXRvcjE0LCBfc3RlcDE0LCBfbG9nMywgbGxBcnJheSwgX2l0ZXJhdG9yMTIsIF9zdGVwMTIsIF9iLCBzeWxSYXRlLCBsbEJsTnVtcywgbGxCbFJlYXNvbiwgc2NCbE51bXMsIHNjQmxSZWFzb24sIF9pdGVyYXRvcjE1LCBfc3RlcDE1LCBfb3QsIF9pdGVyYXRvcjE2LCBfc3RlcDE2LCBfdCwgZ2RYcWwsIGxvdFBsYW5YcWwsIGxvdExsWHFsLCBsb3NzLCBlcnBSYXRlTnVtczsKICAgICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlNiQoX2NvbnRleHQ2KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDYucHJldiA9IF9jb250ZXh0Ni5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBmb3JtID0gX3RoaXM1LmZvcm07CiAgICAgICAgICAgICAgc2NoZWR1bGUgPSBfdGhpczUuc2NoZWR1bGU7CiAgICAgICAgICAgICAgZm9ybVJhdGUgPSBfdGhpczUuZGl2aWRlKGZvcm0ucHJvZHVjdE51bXMsIHNjaGVkdWxlLnBsYW5OdW1zKTsgLy/kuqflh7rljaDmr5QKICAgICAgICAgICAgICBpZiAoIWZvcm1SYXRlKSB7CiAgICAgICAgICAgICAgICBfY29udGV4dDYubmV4dCA9IDIxOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHNjaGVkdWxlQ29kZSA9IGZvcm0uc2NoZWR1bGVDb2RlOwogICAgICAgICAgICAgIF9jb250ZXh0Ni5uZXh0ID0gNzsKICAgICAgICAgICAgICByZXR1cm4gKDAsIF93aXBDb250TWF0ZXJpYWxMb3QuYWxsR3JvdXBCeUVxdWlwbWVudE5vKSh7CiAgICAgICAgICAgICAgICBzY2hlZHVsZUNvZGU6IHNjaGVkdWxlQ29kZSwKICAgICAgICAgICAgICAgIHdvcmtEYXRlOiBmb3JtLndvcmtEYXRlCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgICBtYXRlcmlhbExvZ0xpc3QgPSBfY29udGV4dDYuc2VudDsKICAgICAgICAgICAgICAvL+aaguaXtuS4jeeul+ePreasoQogICAgICAgICAgICAgIF90aGlzNS5tYXRlcmlhbExvZ0xpc3QgPSBtYXRlcmlhbExvZ0xpc3Q7CiAgICAgICAgICAgICAgYXJyID0gc2NoZWR1bGVDb2RlLnNwbGl0KCctJyk7CiAgICAgICAgICAgICAgX2NvbnRleHQ2Lm5leHQgPSAxMjsKICAgICAgICAgICAgICByZXR1cm4gKDAsIF9lcnAuZ2V0U2NoZWR1bGVNYXRlcmlhbCkoewogICAgICAgICAgICAgICAgd29ya09yZGVyTm86IGFyclsxXSwKICAgICAgICAgICAgICAgIHdvcmtPcmRlclNpbmdsZTogYXJyWzBdCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGNhc2UgMTI6CiAgICAgICAgICAgICAgZXJwQm9tRGF0YSA9IF9jb250ZXh0Ni5zZW50OwogICAgICAgICAgICAgIHNjaGVkdWxlTnVtcyA9IHNjaGVkdWxlLnBsYW5OdW1zOwogICAgICAgICAgICAgIG1hdGVyaWFsTm9TZXQgPSBuZXcgU2V0KCk7CiAgICAgICAgICAgICAgX2l0ZXJhdG9yNCA9ICgwLCBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcjIuZGVmYXVsdCkobWF0ZXJpYWxMb2dMaXN0KTsKICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgZm9yIChfaXRlcmF0b3I0LnMoKTsgIShfc3RlcDQgPSBfaXRlcmF0b3I0Lm4oKSkuZG9uZTspIHsKICAgICAgICAgICAgICAgICAgbG9nID0gX3N0ZXA0LnZhbHVlOwogICAgICAgICAgICAgICAgICBtYXRlcmlhbE5vU2V0LmFkZChsb2cubWF0ZXJpYWxObyk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgICAgICAgICBfaXRlcmF0b3I0LmUoZXJyKTsKICAgICAgICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgICAgICAgX2l0ZXJhdG9yNC5mKCk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIG1hdGVyaWFsTG9nQXJyYXkgPSBbXTsKICAgICAgICAgICAgICBfaXRlcmF0b3I1ID0gKDAsIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyMi5kZWZhdWx0KShtYXRlcmlhbE5vU2V0KTsKICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgZm9yIChfaXRlcmF0b3I1LnMoKTsgIShfc3RlcDUgPSBfaXRlcmF0b3I1Lm4oKSkuZG9uZTspIHsKICAgICAgICAgICAgICAgICAgbWF0ZXJpYWxObyA9IF9zdGVwNS52YWx1ZTsKICAgICAgICAgICAgICAgICAgdGUwMTBTZXQgPSBuZXcgU2V0KCk7IC8v5Zyo5ZOB5Y+357u05bqm5LiL5YaN5o6S5om55qyhCiAgICAgICAgICAgICAgICAgIHNqQm9tTnVtID0gMDsgLy9ib23nlKjph4/lr7nkuo7lt6XljZXkuqflk4HmmK/miJDlk4HnmoQKICAgICAgICAgICAgICAgICAgY2IwMTAgPSAwOyAvL2VycOaNn+iAl+eOhwogICAgICAgICAgICAgICAgICBtYXRlcmlhbE5hbWUgPSB2b2lkIDA7CiAgICAgICAgICAgICAgICAgIHRlMDA2ID0gdm9pZCAwOyAvL2VycOWNleS9jQogICAgICAgICAgICAgICAgICB0ZTAwNVN1bSA9IF90aGlzNS4kYmlnKDApOyAvL+W3peWNleWPkeaWmemHj+Wwj+iuoQogICAgICAgICAgICAgICAgICBsb3RTdW1zID0gX3RoaXM1LiRiaWcoMCk7IC8v5om55qyh5L2/55So6YeP5bCP6K6hCiAgICAgICAgICAgICAgICAgIGVycFN5bFN1bSA9IF90aGlzNS4kYmlnKDApOyAvL2VycOWTgeWPt+e7tOW6puS9v+eUqOmHj+Wwj+iuoQogICAgICAgICAgICAgICAgICBfaXRlcmF0b3I2ID0gKDAsIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyMi5kZWZhdWx0KShlcnBCb21EYXRhKTsKICAgICAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgICAgICBmb3IgKF9pdGVyYXRvcjYucygpOyAhKF9zdGVwNiA9IF9pdGVyYXRvcjYubigpKS5kb25lOykgewogICAgICAgICAgICAgICAgICAgICAgYiA9IF9zdGVwNi52YWx1ZTsKICAgICAgICAgICAgICAgICAgICAgIGlmIChiLnRlMDA0ID09PSBtYXRlcmlhbE5vKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHNqQm9tTnVtID0gTnVtYmVyKGIuc2pCb21OdW0pOwogICAgICAgICAgICAgICAgICAgICAgICBjYjAxMCA9IE51bWJlcihiLmNiMDEwKTsKICAgICAgICAgICAgICAgICAgICAgICAgbWF0ZXJpYWxOYW1lID0gYi50ZTAxNzsKICAgICAgICAgICAgICAgICAgICAgICAgdGUwMDYgPSBiLnRlMDA2OwogICAgICAgICAgICAgICAgICAgICAgICB0ZTAwNVN1bSA9IF90aGlzNS5hZGQodGUwMDVTdW0sIGIudGUwMDUpOwogICAgICAgICAgICAgICAgICAgICAgICB0ZTAxMFNldC5hZGQoYi50ZTAxMCk7IC8v5oyJ54WnZTEw55qE5om55qyh5p2lCiAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICAgICAgICAgICAgICBfaXRlcmF0b3I2LmUoZXJyKTsKICAgICAgICAgICAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgICAgICAgICAgICBfaXRlcmF0b3I2LmYoKTsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICBfaXRlcmF0b3I3ID0gKDAsIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyMi5kZWZhdWx0KShtYXRlcmlhbExvZ0xpc3QpOwogICAgICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgICAgIGZvciAoX2l0ZXJhdG9yNy5zKCk7ICEoX3N0ZXA3ID0gX2l0ZXJhdG9yNy5uKCkpLmRvbmU7KSB7CiAgICAgICAgICAgICAgICAgICAgICBfbG9nID0gX3N0ZXA3LnZhbHVlOwogICAgICAgICAgICAgICAgICAgICAgaWYgKG1hdGVyaWFsTm8gPT09IF9sb2cubWF0ZXJpYWxObykgewogICAgICAgICAgICAgICAgICAgICAgICBsb3RTdW1zID0gX3RoaXM1LmFkZChsb3RTdW1zLCBfbG9nLm51bXMpOwogICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yNy5lKGVycik7CiAgICAgICAgICAgICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yNy5mKCk7CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgdGUwMTBBcnJheSA9IFtdOwogICAgICAgICAgICAgICAgICBfaXRlcmF0b3I4ID0gKDAsIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyMi5kZWZhdWx0KSh0ZTAxMFNldCk7CiAgICAgICAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICAgICAgZm9yIChfaXRlcmF0b3I4LnMoKTsgIShfc3RlcDggPSBfaXRlcmF0b3I4Lm4oKSkuZG9uZTspIHsKICAgICAgICAgICAgICAgICAgICAgIHRlMDEwID0gX3N0ZXA4LnZhbHVlOwogICAgICAgICAgICAgICAgICAgICAgZXJwTGxOdW1zID0gMDsKICAgICAgICAgICAgICAgICAgICAgIGVycFN5bE51bXMgPSAwOwogICAgICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yOSA9ICgwLCBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcjIuZGVmYXVsdCkoX3RoaXM1Lm90aGVyQXJyYXkpOwogICAgICAgICAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgICAgICAgICAgZm9yIChfaXRlcmF0b3I5LnMoKTsgIShfc3RlcDkgPSBfaXRlcmF0b3I5Lm4oKSkuZG9uZTspIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBvdCA9IF9zdGVwOS52YWx1ZTsKICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAob3QubWF0ZXJpYWxDb2RlID09PSBtYXRlcmlhbE5vKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfaXRlcmF0b3IxMyA9ICgwLCBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcjIuZGVmYXVsdCkob3QudGUwMTBBcnJheSk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3IgKF9pdGVyYXRvcjEzLnMoKTsgIShfc3RlcDEzID0gX2l0ZXJhdG9yMTMubigpKS5kb25lOykgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHQgPSBfc3RlcDEzLnZhbHVlOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICh0LnRlMDEwID09PSB0ZTAxMCkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHQuZ2RTeWxOdW1zKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVycExsTnVtcyA9IE51bWJlcih0LmdkU3lsTnVtcyk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVycFN5bE51bXMgPSBOdW1iZXIodC5nZFN5bE51bXMpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjEzLmUoZXJyKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjEzLmYoKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgICAgICAgICAgICAgICAgICBfaXRlcmF0b3I5LmUoZXJyKTsKICAgICAgICAgICAgICAgICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjkuZigpOwogICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgZXF1aXBtZW50U2V0ID0gbmV3IFNldCgpOyAvL+WTgeWPt+aJueasoeWGheaOkuiuvuWkhwogICAgICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yMTAgPSAoMCwgX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIyLmRlZmF1bHQpKG1hdGVyaWFsTG9nTGlzdCk7CiAgICAgICAgICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgICAgICAgICBmb3IgKF9pdGVyYXRvcjEwLnMoKTsgIShfc3RlcDEwID0gX2l0ZXJhdG9yMTAubigpKS5kb25lOykgewogICAgICAgICAgICAgICAgICAgICAgICAgIF9sb2cyID0gX3N0ZXAxMC52YWx1ZTsKICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAobWF0ZXJpYWxObyA9PT0gX2xvZzIubWF0ZXJpYWxObyAmJiB0ZTAxMCA9PT0gX2xvZzIubWF0ZXJpYWxMb3RObykgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgZXF1aXBtZW50U2V0LmFkZChfbG9nMi5lcXVpcG1lbnRObyk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yMTAuZShlcnIpOwogICAgICAgICAgICAgICAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yMTAuZigpOwogICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgZXF1aXBtZW50QXJyYXkgPSBbXTsKICAgICAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjExID0gKDAsIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyMi5kZWZhdWx0KShlcXVpcG1lbnRTZXQpOwogICAgICAgICAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgICAgICAgICAgZm9yIChfaXRlcmF0b3IxMS5zKCk7ICEoX3N0ZXAxMSA9IF9pdGVyYXRvcjExLm4oKSkuZG9uZTspIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBlcXVpcG1lbnRObyA9IF9zdGVwMTEudmFsdWU7CiAgICAgICAgICAgICAgICAgICAgICAgICAgbCA9IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVxdWlwbWVudE5vOiBlcXVpcG1lbnRObwogICAgICAgICAgICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yMTQgPSAoMCwgX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIyLmRlZmF1bHQpKG1hdGVyaWFsTG9nTGlzdCk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvciAoX2l0ZXJhdG9yMTQucygpOyAhKF9zdGVwMTQgPSBfaXRlcmF0b3IxNC5uKCkpLmRvbmU7KSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9sb2czID0gX3N0ZXAxNC52YWx1ZTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKG1hdGVyaWFsTm8gPT09IF9sb2czLm1hdGVyaWFsTm8gJiYgdGUwMTAgPT09IF9sb2czLm1hdGVyaWFsTG90Tm8gJiYgZXF1aXBtZW50Tm8gPT09IF9sb2czLmVxdWlwbWVudE5vKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbC5udW1zID0gX2xvZzMubnVtczsgLy/kvb/nlKjph48KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsLnRpbWVzID0gX2xvZzMudGltZXM7IC8v6aKG5paZ5qyh5pWwCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjE0LmUoZXJyKTsKICAgICAgICAgICAgICAgICAgICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yMTQuZigpOwogICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICBlcXVpcG1lbnRBcnJheS5wdXNoKGwpOwogICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yMTEuZShlcnIpOwogICAgICAgICAgICAgICAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yMTEuZigpOwogICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgbGxBcnJheSA9IFtdOyAvL2VycOmihuaWmeWNleiusOW9lQogICAgICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yMTIgPSAoMCwgX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIyLmRlZmF1bHQpKGVycEJvbURhdGEpOwogICAgICAgICAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgICAgICAgICAgZm9yIChfaXRlcmF0b3IxMi5zKCk7ICEoX3N0ZXAxMiA9IF9pdGVyYXRvcjEyLm4oKSkuZG9uZTspIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBfYiA9IF9zdGVwMTIudmFsdWU7CiAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKF9iLnRlMDA0ID09PSBtYXRlcmlhbE5vICYmIF9iLnRlMDEwID09PSB0ZTAxMCkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgc3lsUmF0ZSA9IHZvaWQgMDsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxsQmxOdW1zID0gdm9pZCAwOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgbGxCbFJlYXNvbiA9IHZvaWQgMDsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNjQmxOdW1zID0gdm9pZCAwOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NCbFJlYXNvbiA9IHZvaWQgMDsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjE1ID0gKDAsIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyMi5kZWZhdWx0KShfdGhpczUub3RoZXJBcnJheSk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3IgKF9pdGVyYXRvcjE1LnMoKTsgIShfc3RlcDE1ID0gX2l0ZXJhdG9yMTUubigpKS5kb25lOykgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9vdCA9IF9zdGVwMTUudmFsdWU7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKF9vdC5tYXRlcmlhbENvZGUgPT09IG1hdGVyaWFsTm8pIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjE2ID0gKDAsIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyMi5kZWZhdWx0KShfb3QudGUwMTBBcnJheSk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3IgKF9pdGVyYXRvcjE2LnMoKTsgIShfc3RlcDE2ID0gX2l0ZXJhdG9yMTYubigpKS5kb25lOykgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF90ID0gX3N0ZXAxNi52YWx1ZTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoX3QudGUwMTAgPT09IHRlMDEwKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzeWxSYXRlID0gX3Quc3lsUmF0ZTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxsQmxOdW1zID0gX3QubGxCbE51bXM7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsbEJsUmVhc29uID0gX3QubGxCbFJlYXNvbjsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNjQmxOdW1zID0gX3Quc2NCbE51bXM7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzY0JsUmVhc29uID0gX3Quc2NCbFJlYXNvbjsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfaXRlcmF0b3IxNi5lKGVycik7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfaXRlcmF0b3IxNi5mKCk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yMTUuZShlcnIpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yMTUuZigpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgICAgbGxBcnJheS5wdXNoKHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGUwMDE6IF9iLnRlMDAxLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvL+mihuaWmemAmuefpeWNleWIqwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZTAwMjogX2IudGUwMDIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8v6aKG5paZ6YCa55+l5Y2V5Y+3CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRlMDA4OiBfYi50ZTAwOCwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy/ku5PlupMKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWUwMDM6IF9iLm1lMDAzLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvL+acgOaXqeWFpeW6k+aXpeacnwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZTAxMzogX2IudGUwMTMsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8v6aKG5paZ6K+05piOCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN5bFJhdGU6IHN5bFJhdGUsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8v5L2/55So6YeP5q+U5L6LKOeUn+S6p+aJueaJueasoeWTgeWPt+S9v+eUqOmHjy/lt6XljZXlk4Hlj7fmibnmrKHkvb/nlKjph48pCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxsQmxOdW1zOiBsbEJsTnVtcywKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy/mnaXmlpnkuI3oia8KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGxCbFJlYXNvbjogbGxCbFJlYXNvbiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NCbE51bXM6IHNjQmxOdW1zLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvL+eUn+S6p+S4jeiJrwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzY0JsUmVhc29uOiBzY0JsUmVhc29uCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgICAgICAgICAgICAgICAgICBfaXRlcmF0b3IxMi5lKGVycik7CiAgICAgICAgICAgICAgICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgICAgICAgICAgICAgICBfaXRlcmF0b3IxMi5mKCk7CiAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICB0ZTAxMEFycmF5LnB1c2goewogICAgICAgICAgICAgICAgICAgICAgICB0ZTAxMDogdGUwMTAsCiAgICAgICAgICAgICAgICAgICAgICAgIGVycExsTnVtczogZXJwTGxOdW1zLAogICAgICAgICAgICAgICAgICAgICAgICAvL+mihuaWmemHjwogICAgICAgICAgICAgICAgICAgICAgICBlcnBTeWxOdW1zOiBlcnBTeWxOdW1zLAogICAgICAgICAgICAgICAgICAgICAgICAvL+S9v+eUqOmHjwogICAgICAgICAgICAgICAgICAgICAgICBlcXVpcG1lbnRBcnJheTogZXF1aXBtZW50QXJyYXksCiAgICAgICAgICAgICAgICAgICAgICAgIGxsQXJyYXk6IGxsQXJyYXkKICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgICAgZXJwU3lsU3VtID0gX3RoaXM1LmFkZChlcnBTeWxTdW0sIGVycFN5bE51bXMpOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yOC5lKGVycik7CiAgICAgICAgICAgICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yOC5mKCk7CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgaWYgKHNjaGVkdWxlTnVtcykgewogICAgICAgICAgICAgICAgICAgIGdkWHFsID0gX3RoaXM1Lm11bHRpcGx5KHNjaGVkdWxlTnVtcywgc2pCb21OdW0pOwogICAgICAgICAgICAgICAgICAgIGxvdFBsYW5YcWwgPSBfdGhpczUubXVsdGlwbHkoZm9ybS5wcm9kdWN0TnVtcywgc2pCb21OdW0pOyAvL+eUn+S6p+iusOW9leWunumZheeUn+S6p+aVsOmHjyDorqHnrpcg5pys57q/6ZyA5rGC6YePCiAgICAgICAgICAgICAgICAgICAgbG90TGxYcWwgPSBfdGhpczUubXVsdGlwbHkoZm9ybS5wcm9kdWN0TnVtcywgc2pCb21OdW0pOyAvL+aJueasoeeUn+S6p+mHj+iuoeeulyDnkIborrrkvb/nlKjph48KICAgICAgICAgICAgICAgICAgICBsb3NzID0gX3RoaXM1LnN1YnRyYWN0KGxvdFN1bXMsIGxvdExsWHFsKTsKICAgICAgICAgICAgICAgICAgICBlcnBSYXRlTnVtcyA9IF90aGlzNS5tdWx0aXBseShlcnBTeWxTdW0sIGZvcm1SYXRlKS50b051bWJlcigpOwogICAgICAgICAgICAgICAgICAgIG1hdGVyaWFsTG9nQXJyYXkucHVzaCh7CiAgICAgICAgICAgICAgICAgICAgICBtYXRlcmlhbE5vOiBtYXRlcmlhbE5vLAogICAgICAgICAgICAgICAgICAgICAgbWF0ZXJpYWxOYW1lOiBtYXRlcmlhbE5hbWUsCiAgICAgICAgICAgICAgICAgICAgICBzakJvbU51bTogc2pCb21OdW0sCiAgICAgICAgICAgICAgICAgICAgICBjYjAxMDogY2IwMTAsCiAgICAgICAgICAgICAgICAgICAgICB0ZTAwNjogdGUwMDYsCiAgICAgICAgICAgICAgICAgICAgICBnZFhxbDogZ2RYcWwudG9OdW1iZXIoKSwKICAgICAgICAgICAgICAgICAgICAgIHRlMDA1U3VtOiB0ZTAwNVN1bS50b051bWJlcigpLAogICAgICAgICAgICAgICAgICAgICAgeGZDeVJhdGU6IF90aGlzNS5kaXZpZGUoX3RoaXM1LnN1YnRyYWN0KHRlMDA1U3VtLCBnZFhxbCksIGdkWHFsKS50b051bWJlcigpLAogICAgICAgICAgICAgICAgICAgICAgbG90WHFsOiBsb3RQbGFuWHFsLnRvTnVtYmVyKCksCiAgICAgICAgICAgICAgICAgICAgICBsb3RMbFhxbDogbG90TGxYcWwudG9OdW1iZXIoKSwKICAgICAgICAgICAgICAgICAgICAgIGxvdFN1bXM6IGxvdFN1bXMudG9OdW1iZXIoKSwKICAgICAgICAgICAgICAgICAgICAgIGxvc3M6IGxvc3MudG9OdW1iZXIoKSwKICAgICAgICAgICAgICAgICAgICAgIGxvdFJhdGU6IF90aGlzNS5kaXZpZGUobG9zcywgbG90TGxYcWwpLnRvTnVtYmVyKCksCiAgICAgICAgICAgICAgICAgICAgICBlcnBTeWxTdW06IGVycFN5bFN1bSwKICAgICAgICAgICAgICAgICAgICAgIGVycFJhdGVOdW1zOiBlcnBSYXRlTnVtcywKICAgICAgICAgICAgICAgICAgICAgIGRpZmZOdW1zOiBfdGhpczUuc3VidHJhY3QobG90U3VtcywgZXJwUmF0ZU51bXMpLnRvTnVtYmVyKCksCiAgICAgICAgICAgICAgICAgICAgICB0ZTAxMEFycmF5OiB0ZTAxMEFycmF5CiAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICAgICAgICAgIF9pdGVyYXRvcjUuZShlcnIpOwogICAgICAgICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICAgICAgICBfaXRlcmF0b3I1LmYoKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX3RoaXM1Lm1hdGVyaWFsTG9nQXJyYXkgPSBtYXRlcmlhbExvZ0FycmF5OwogICAgICAgICAgICBjYXNlIDIxOgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDYuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU2KTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgYnVpbGREaWZmRGF0YTogZnVuY3Rpb24gYnVpbGREaWZmRGF0YSgpIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWU3KCkgewogICAgICAgIHZhciBmb3JtLCBzY2hlZHVsZVN0YW5kYXJkLCBwcm9kdWN0TnVtcywgYWN0dWFsSG91cnMsIGFjdHVhbER1cmF0aW9uLCBhY3R1YWxQcm9kdWN0aXZpdHksIGFjdHVhbEhvdXJzUmF0ZSwgZGlmZkRhdGFBcnJheSwgc3RhbmRhcmRIb3Vycywgc3RhbmRhcmRQZXJzb25OdW1zLCBzdGFuZGFyZEhvdXJzUmF0ZTsKICAgICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlNyQoX2NvbnRleHQ3KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDcucHJldiA9IF9jb250ZXh0Ny5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBmb3JtID0gX3RoaXM2LmZvcm07CiAgICAgICAgICAgICAgc2NoZWR1bGVTdGFuZGFyZCA9IF90aGlzNi5zY2hlZHVsZVN0YW5kYXJkOwogICAgICAgICAgICAgIHByb2R1Y3ROdW1zID0gZm9ybS5wcm9kdWN0TnVtczsKICAgICAgICAgICAgICBhY3R1YWxIb3VycyA9IF90aGlzNi5kaXZpZGUoZm9ybS5zdW1NaW51dGVzLCA2MCk7CiAgICAgICAgICAgICAgYWN0dWFsRHVyYXRpb24gPSBfdGhpczYuZGlmZkhvdXJzKGZvcm0uc3RhcnRUaW1lLCBmb3JtLmVuZFRpbWUpOwogICAgICAgICAgICAgIGlmIChhY3R1YWxEdXJhdGlvbiAmJiBzY2hlZHVsZVN0YW5kYXJkLm51bXMpIHsKICAgICAgICAgICAgICAgIGFjdHVhbFByb2R1Y3Rpdml0eSA9IF90aGlzNi5kaXZpZGUocHJvZHVjdE51bXMsIGFjdHVhbER1cmF0aW9uKS50b051bWJlcigpOwogICAgICAgICAgICAgICAgYWN0dWFsSG91cnNSYXRlID0gX3RoaXM2LmRpdmlkZShwcm9kdWN0TnVtcywgYWN0dWFsSG91cnMpLnRvTnVtYmVyKCk7CiAgICAgICAgICAgICAgICBkaWZmRGF0YUFycmF5ID0gW107CiAgICAgICAgICAgICAgICBzdGFuZGFyZEhvdXJzID0gMDsKICAgICAgICAgICAgICAgIHN0YW5kYXJkUGVyc29uTnVtcyA9IDA7CiAgICAgICAgICAgICAgICBzdGFuZGFyZEhvdXJzUmF0ZSA9IDA7CiAgICAgICAgICAgICAgICBpZiAoZm9ybS5vcE5vID09PSAnR0InKSB7CiAgICAgICAgICAgICAgICAgIC8v5LiA6Zi2IOWPluWwkeeahAogICAgICAgICAgICAgICAgICBzdGFuZGFyZFBlcnNvbk51bXMgPSBzY2hlZHVsZVN0YW5kYXJkLm51bXM7CiAgICAgICAgICAgICAgICAgIHN0YW5kYXJkSG91cnNSYXRlID0gc2NoZWR1bGVTdGFuZGFyZC5jb3N0SG91cnNSYXRlOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgc3RhbmRhcmRQZXJzb25OdW1zID0gc2NoZWR1bGVTdGFuZGFyZC5jb3N0TnVtczsKICAgICAgICAgICAgICAgICAgc3RhbmRhcmRIb3Vyc1JhdGUgPSBzY2hlZHVsZVN0YW5kYXJkLmNvc3RIb3Vyc1JhdGU7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBzdGFuZGFyZEhvdXJzID0gX3RoaXM2Lm11bHRpcGx5KHN0YW5kYXJkSG91cnNSYXRlLCBmb3JtLnByb2R1Y3ROdW1zKS50b051bWJlcigpOwogICAgICAgICAgICAgICAgZGlmZkRhdGFBcnJheS5wdXNoKFsn5qCH5YeGJywgc2NoZWR1bGVTdGFuZGFyZC5wcm9kdWN0aXZpdHksIHN0YW5kYXJkUGVyc29uTnVtcywgc3RhbmRhcmRIb3Vycywgc3RhbmRhcmRIb3Vyc1JhdGVdKTsKICAgICAgICAgICAgICAgIGRpZmZEYXRhQXJyYXkucHVzaChbJ+WunumZhScsIGFjdHVhbFByb2R1Y3Rpdml0eSwgZm9ybS5zdW1OdW1zLCBhY3R1YWxIb3VycywgYWN0dWFsSG91cnNSYXRlXSk7CiAgICAgICAgICAgICAgICBkaWZmRGF0YUFycmF5LnB1c2goWyflt67lvIInLCBfdGhpczYuc3VidHJhY3QoYWN0dWFsUHJvZHVjdGl2aXR5LCBzY2hlZHVsZVN0YW5kYXJkLnByb2R1Y3Rpdml0eSkudG9OdW1iZXIoKSwgX3RoaXM2LnN1YnRyYWN0KGZvcm0uc3VtTnVtcywgc3RhbmRhcmRQZXJzb25OdW1zKS50b051bWJlcigpLCBfdGhpczYuc3VidHJhY3QoYWN0dWFsSG91cnMsIHN0YW5kYXJkSG91cnMpLnRvTnVtYmVyKCksIF90aGlzNi5zdWJ0cmFjdChhY3R1YWxIb3Vyc1JhdGUsIHN0YW5kYXJkSG91cnNSYXRlKS50b051bWJlcigpXSk7CiAgICAgICAgICAgICAgICBfdGhpczYuZGlmZkRhdGFBcnJheSA9IGRpZmZEYXRhQXJyYXk7CiAgICAgICAgICAgICAgICBfdGhpczYuZGlmZkRhdGFPcHRpb25zID0gewogICAgICAgICAgICAgICAgICB0aXRsZTogewogICAgICAgICAgICAgICAgICAgIHRleHQ6ICfmibnmrKHnu7TluqbmlbDmja7lr7nmr5QnCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgIGxlZ2VuZDogewogICAgICAgICAgICAgICAgICAgIGRhdGE6IFsn6aKE5LywJywgJ+WunumZhSddCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgIHJhZGFyOiB7CiAgICAgICAgICAgICAgICAgICAgaW5kaWNhdG9yOiBbewogICAgICAgICAgICAgICAgICAgICAgbmFtZTogJ+eUn+S6p+S6p+iDvScsCiAgICAgICAgICAgICAgICAgICAgICBtYXg6IE1hdGgubWF4KHNjaGVkdWxlU3RhbmRhcmQucHJvZHVjdGl2aXR5LCBhY3R1YWxQcm9kdWN0aXZpdHkpICogMS4yCiAgICAgICAgICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgICAgICAgICAgbmFtZTogJ+eUn+S6p+S6uuaVsCcsCiAgICAgICAgICAgICAgICAgICAgICBtYXg6IE1hdGgubWF4KHN0YW5kYXJkUGVyc29uTnVtcywgZm9ybS5zdW1OdW1zKSAqIDEuMgogICAgICAgICAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICAgICAgICAgIG5hbWU6ICfnlJ/kuqflt6Xml7YnLAogICAgICAgICAgICAgICAgICAgICAgbWF4OiBNYXRoLm1heChzdGFuZGFyZEhvdXJzLCBhY3R1YWxIb3VycykgKiAxLjIKICAgICAgICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICAgICAgICBuYW1lOiAn5bel5pe25Lqn5Ye6546HJywKICAgICAgICAgICAgICAgICAgICAgIG1heDogTWF0aC5tYXgoc3RhbmRhcmRIb3Vyc1JhdGUsIGFjdHVhbEhvdXJzUmF0ZSkgKiAxLjIKICAgICAgICAgICAgICAgICAgICB9XQogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICBzZXJpZXM6IFt7CiAgICAgICAgICAgICAgICAgICAgbmFtZTogJ+mihOS8sCB2cyDlrp7pmYUnLAogICAgICAgICAgICAgICAgICAgIHR5cGU6ICdyYWRhcicsCiAgICAgICAgICAgICAgICAgICAgZGF0YTogW3sKICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBkaWZmRGF0YUFycmF5WzBdLnNsaWNlKDEpLAogICAgICAgICAgICAgICAgICAgICAgbmFtZTogJ+mihOS8sCcKICAgICAgICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogZGlmZkRhdGFBcnJheVsxXS5zbGljZSgxKSwKICAgICAgICAgICAgICAgICAgICAgIG5hbWU6ICflrp7pmYUnCiAgICAgICAgICAgICAgICAgICAgfV0KICAgICAgICAgICAgICAgICAgfV0KICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgICAvLyBhd2FpdCB0aGlzLnJlZHVjZURpZmZDaGFydCgpCiAgICAgICAgICAgICAgfQogICAgICAgICAgICBjYXNlIDY6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Ny5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTcpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBpbXBvcnRBcmNoaXZlOiBmdW5jdGlvbiBpbXBvcnRBcmNoaXZlKGFyY2hpdmVJZCkgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTgoKSB7CiAgICAgICAgdmFyIGFyY2hpdmVSZXMsIGFyY2hpdmUsIHByb2plY3RSZXMsIHByb2plY3QsIGN1YmljbGVBcnJheSwgaW1ncywgdmlkZW9zLCBfaXRlcmF0b3IxNywgX3N0ZXAxNywgaXRlbSwgX2l0ZXJhdG9yMTgsIF9zdGVwMTgsIHNlY29uZCwgX2l0ZXJhdG9yMTksIF9zdGVwMTksIHc7CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTgkKF9jb250ZXh0OCkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ4LnByZXYgPSBfY29udGV4dDgubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQ4Lm5leHQgPSAyOwogICAgICAgICAgICAgIHJldHVybiAoMCwgX2FyY2hpdmUuZ2V0QXJjaGl2ZSkoYXJjaGl2ZUlkKTsKICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgIGFyY2hpdmVSZXMgPSBfY29udGV4dDguc2VudDsKICAgICAgICAgICAgICBpZiAoIShhcmNoaXZlUmVzLmNvZGUgPT09IDIwMCAmJiBhcmNoaXZlUmVzLmRhdGEpKSB7CiAgICAgICAgICAgICAgICBfY29udGV4dDgubmV4dCA9IDEzOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGFyY2hpdmUgPSBhcmNoaXZlUmVzLmRhdGE7CiAgICAgICAgICAgICAgX2NvbnRleHQ4Lm5leHQgPSA3OwogICAgICAgICAgICAgIHJldHVybiAoMCwgX3Byb2plY3QuZ2V0UHJvamVjdCkoYXJjaGl2ZS5wcm9qZWN0SWQpOwogICAgICAgICAgICBjYXNlIDc6CiAgICAgICAgICAgICAgcHJvamVjdFJlcyA9IF9jb250ZXh0OC5zZW50OwogICAgICAgICAgICAgIHByb2plY3QgPSBwcm9qZWN0UmVzLmRhdGE7CiAgICAgICAgICAgICAgcHJvamVjdC50eXBlID0gYXJjaGl2ZS50eXBlOwogICAgICAgICAgICAgIGlmIChbJzAnLCAnMScsICczJ10uaW5jbHVkZXMoYXJjaGl2ZS50eXBlKSkgewogICAgICAgICAgICAgICAgcHJvamVjdC5ib21SZXNvdXJjZSA9IGFyY2hpdmUuYm9tUmVzb3VyY2U7CiAgICAgICAgICAgICAgICBwcm9qZWN0LmJvbVR5cGUgPSBhcmNoaXZlLmJvbVR5cGU7CiAgICAgICAgICAgICAgICBwcm9qZWN0LnJlc291cmNlRmluaXNoZWRHb29kc0lkID0gYXJjaGl2ZS5yZXNvdXJjZUZpbmlzaGVkR29vZHNJZDsKICAgICAgICAgICAgICAgIHByb2plY3QuZXJwUHJpY2UgPSBhcmNoaXZlLmVycFByaWNlOwogICAgICAgICAgICAgICAgcHJvamVjdC5ib21BcnJheSA9IGFyY2hpdmUuYm9tQXJyYXk7IC8v6L+Z6YeM5bCx5piv5a2X56ym5LiyCiAgICAgICAgICAgICAgICBwcm9qZWN0LmVycENvZGUgPSBhcmNoaXZlLmVycENvZGU7CiAgICAgICAgICAgICAgICBpZiAoYXJjaGl2ZS5jdWJpY2xlQXJyYXkpIHsKICAgICAgICAgICAgICAgICAgY3ViaWNsZUFycmF5ID0gSlNPTi5wYXJzZShhcmNoaXZlLmN1YmljbGVBcnJheSk7CiAgICAgICAgICAgICAgICAgIHByb2plY3QuY3ViaWNsZUFycmF5ID0gY3ViaWNsZUFycmF5OwogICAgICAgICAgICAgICAgICBpbWdzID0gW107CiAgICAgICAgICAgICAgICAgIHZpZGVvcyA9IFtdOwogICAgICAgICAgICAgICAgICBfaXRlcmF0b3IxNyA9ICgwLCBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcjIuZGVmYXVsdCkoY3ViaWNsZUFycmF5KTsKICAgICAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgICAgICBmb3IgKF9pdGVyYXRvcjE3LnMoKTsgIShfc3RlcDE3ID0gX2l0ZXJhdG9yMTcubigpKS5kb25lOykgewogICAgICAgICAgICAgICAgICAgICAgaXRlbSA9IF9zdGVwMTcudmFsdWU7CiAgICAgICAgICAgICAgICAgICAgICBfaXRlcmF0b3IxOCA9ICgwLCBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcjIuZGVmYXVsdCkoaXRlbS5zZWN0aW9uQXJyYXkpOwogICAgICAgICAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgICAgICAgICAgZm9yIChfaXRlcmF0b3IxOC5zKCk7ICEoX3N0ZXAxOCA9IF9pdGVyYXRvcjE4Lm4oKSkuZG9uZTspIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBzZWNvbmQgPSBfc3RlcDE4LnZhbHVlOwogICAgICAgICAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjE5ID0gKDAsIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyMi5kZWZhdWx0KShzZWNvbmQud29ya1R5cGVBcnJheSk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvciAoX2l0ZXJhdG9yMTkucygpOyAhKF9zdGVwMTkgPSBfaXRlcmF0b3IxOS5uKCkpLmRvbmU7KSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHcgPSBfc3RlcDE5LnZhbHVlOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAody5ob21ld29ya0ltZ3MpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbWdzLnB1c2guYXBwbHkoaW1ncywgKDAsIF90b0NvbnN1bWFibGVBcnJheTIuZGVmYXVsdCkody5ob21ld29ya0ltZ3Muc3BsaXQoJywnKSkpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICh3LmhvbWV3b3JrVmlkZW9zLmxlbmd0aCkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZpZGVvcy5wdXNoLmFwcGx5KHZpZGVvcywgKDAsIF90b0NvbnN1bWFibGVBcnJheTIuZGVmYXVsdCkody5ob21ld29ya1ZpZGVvcykpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfaXRlcmF0b3IxOS5lKGVycik7CiAgICAgICAgICAgICAgICAgICAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjE5LmYoKTsKICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgICAgICAgICAgICAgICAgICBfaXRlcmF0b3IxOC5lKGVycik7CiAgICAgICAgICAgICAgICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgICAgICAgICAgICAgICBfaXRlcmF0b3IxOC5mKCk7CiAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICAgICAgICAgICAgICBfaXRlcmF0b3IxNy5lKGVycik7CiAgICAgICAgICAgICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yMTcuZigpOwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIF90aGlzNy5pbWdzID0gaW1nczsKICAgICAgICAgICAgICAgICAgX3RoaXM3LnZpZGVvcyA9IHZpZGVvczsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIHByb2plY3QuY3ViaWNsZUFycmF5ID0gW107CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzNy5ib21EYXRhID0gSlNPTi5wYXJzZShhcmNoaXZlLmJvbUFycmF5KTsKICAgICAgICAgICAgICBfdGhpczcucHJvamVjdCA9IHByb2plY3Q7CiAgICAgICAgICAgIGNhc2UgMTM6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0OC5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTgpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBidWlsZFFjTG9nczogZnVuY3Rpb24gYnVpbGRRY0xvZ3MoKSB7CiAgICAgIHZhciBfdGhpczggPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlOSgpIHsKICAgICAgICB2YXIgZm9ybSwgYXJyLCBpbnNwZWN0aW9uTGlzdCwgX2l0ZXJhdG9yMjAsIF9zdGVwMjAsIGl0ZW0sIGVycENvbHVtbiwgb2JqLCBncm91cEluc3BlY3Rpb25MaXN0LCByYXdEYXRhLCB0b3RhbERhdGEsIGksIHN1bSwgaiwgZ3JpZCwgc2VyaWVzOwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWU5JChfY29udGV4dDkpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0OS5wcmV2ID0gX2NvbnRleHQ5Lm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIGZvcm0gPSBfdGhpczguZm9ybTsKICAgICAgICAgICAgICBhcnIgPSBmb3JtLnNjaGVkdWxlQ29kZS5zcGxpdCgnLScpOwogICAgICAgICAgICAgIF9jb250ZXh0OS5uZXh0ID0gNDsKICAgICAgICAgICAgICByZXR1cm4gKDAsIF9maW5pc2hlZEluc3BlY3Rpb24uYWxsRmluaXNoZWRJbnNwZWN0aW9uKSh7CiAgICAgICAgICAgICAgICBzaW5nbGVDYXRlZ29yeTogYXJyWzBdLAogICAgICAgICAgICAgICAgd29ya09yZGVyTm86IGFyclsxXQogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgaW5zcGVjdGlvbkxpc3QgPSBfY29udGV4dDkuc2VudDsKICAgICAgICAgICAgICAvL+i0qOajgAogICAgICAgICAgICAgIF9pdGVyYXRvcjIwID0gKDAsIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyMi5kZWZhdWx0KShpbnNwZWN0aW9uTGlzdCk7CiAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgIGZvciAoX2l0ZXJhdG9yMjAucygpOyAhKF9zdGVwMjAgPSBfaXRlcmF0b3IyMC5uKCkpLmRvbmU7KSB7CiAgICAgICAgICAgICAgICAgIGl0ZW0gPSBfc3RlcDIwLnZhbHVlOwogICAgICAgICAgICAgICAgICBlcnBDb2x1bW4gPSBpdGVtLmVycENvbHVtbjsKICAgICAgICAgICAgICAgICAgaWYgKGVycENvbHVtbikgewogICAgICAgICAgICAgICAgICAgIG9iaiA9IEpTT04ucGFyc2UoZXJwQ29sdW1uKTsKICAgICAgICAgICAgICAgICAgICBpdGVtLnRnMDMwID0gb2JqLnRnMDMwOwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgICAgICAgICBfaXRlcmF0b3IyMC5lKGVycik7CiAgICAgICAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgICAgICAgIF9pdGVyYXRvcjIwLmYoKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX3RoaXM4Lmluc3BlY3Rpb25MaXN0ID0gaW5zcGVjdGlvbkxpc3Q7CiAgICAgICAgICAgICAgX2NvbnRleHQ5Lm5leHQgPSAxMDsKICAgICAgICAgICAgICByZXR1cm4gKDAsIF9maW5pc2hlZEluc3BlY3Rpb24uYWxsR3JvdXBCeUJhdGNoRmluaXNoZWRJbnNwZWN0aW9uKSh7CiAgICAgICAgICAgICAgICBzaW5nbGVDYXRlZ29yeTogYXJyWzBdLAogICAgICAgICAgICAgICAgd29ya09yZGVyTm86IGFyclsxXQogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDEwOgogICAgICAgICAgICAgIGdyb3VwSW5zcGVjdGlvbkxpc3QgPSBfY29udGV4dDkuc2VudDsKICAgICAgICAgICAgICByYXdEYXRhID0gW2dyb3VwSW5zcGVjdGlvbkxpc3QubWFwKGZ1bmN0aW9uIChpKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gaS5iYXRjaE51bXMgLSBpLnNhbXBsaW5nTnVtczsKICAgICAgICAgICAgICB9KSwgZ3JvdXBJbnNwZWN0aW9uTGlzdC5tYXAoZnVuY3Rpb24gKGkpIHsKICAgICAgICAgICAgICAgIHJldHVybiBpLnNhbXBsaW5nTnVtczsKICAgICAgICAgICAgICB9KV07CiAgICAgICAgICAgICAgdG90YWxEYXRhID0gW107CiAgICAgICAgICAgICAgZm9yIChpID0gMDsgaSA8IHJhd0RhdGFbMF0ubGVuZ3RoOyArK2kpIHsKICAgICAgICAgICAgICAgIHN1bSA9IDA7CiAgICAgICAgICAgICAgICBmb3IgKGogPSAwOyBqIDwgcmF3RGF0YS5sZW5ndGg7ICsraikgewogICAgICAgICAgICAgICAgICBzdW0gKz0gcmF3RGF0YVtqXVtpXTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIHRvdGFsRGF0YS5wdXNoKHN1bSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGdyaWQgPSB7CiAgICAgICAgICAgICAgICBsZWZ0OiAxMDAsCiAgICAgICAgICAgICAgICByaWdodDogMTAwLAogICAgICAgICAgICAgICAgdG9wOiA1MCwKICAgICAgICAgICAgICAgIGJvdHRvbTogNTAKICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgIHNlcmllcyA9IFsn5pyq5oq95qC3JywgJ+W3suaKveagtyddLm1hcChmdW5jdGlvbiAobmFtZSwgc2lkKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICAgICAgICBuYW1lOiBuYW1lLAogICAgICAgICAgICAgICAgICB0eXBlOiAnYmFyJywKICAgICAgICAgICAgICAgICAgc3RhY2s6ICd0b3RhbCcsCiAgICAgICAgICAgICAgICAgIGJhcldpZHRoOiAnNjAlJywKICAgICAgICAgICAgICAgICAgbGFiZWw6IHsKICAgICAgICAgICAgICAgICAgICBzaG93OiB0cnVlLAogICAgICAgICAgICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24gZm9ybWF0dGVyKHBhcmFtcykgewogICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIE1hdGgucm91bmQocGFyYW1zLnZhbHVlICogMTAwMCkgLyAxMCArICclJzsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgIGRhdGE6IHJhd0RhdGFbc2lkXS5tYXAoZnVuY3Rpb24gKGQsIGRpZCkgewogICAgICAgICAgICAgICAgICAgIHJldHVybiB0b3RhbERhdGFbZGlkXSA8PSAwID8gMCA6IGQgLyB0b3RhbERhdGFbZGlkXTsKICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgX3RoaXM4LnFjT3B0aW9ucyA9IHsKICAgICAgICAgICAgICAgIGxlZ2VuZDogewogICAgICAgICAgICAgICAgICBzZWxlY3RlZE1vZGU6IGZhbHNlCiAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgZ3JpZDogZ3JpZCwKICAgICAgICAgICAgICAgIHlBeGlzOiB7CiAgICAgICAgICAgICAgICAgIHR5cGU6ICd2YWx1ZScKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICB4QXhpczogewogICAgICAgICAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLAogICAgICAgICAgICAgICAgICBkYXRhOiBncm91cEluc3BlY3Rpb25MaXN0Lm1hcChmdW5jdGlvbiAoaSkgewogICAgICAgICAgICAgICAgICAgIHJldHVybiBpLmJhdGNoTm87CiAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgc2VyaWVzOiBzZXJpZXMKICAgICAgICAgICAgICB9OwogICAgICAgICAgICBjYXNlIDE3OgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDkuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU5KTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgaW5pdDogZnVuY3Rpb24gaW5pdChpZCkgewogICAgICB2YXIgX3RoaXM5ID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTEwKCkgewogICAgICAgIHZhciByZXMsIGZvcm0sIHNjaGVkdWxlQ29kZSwgYXJyLCBzY2hlZHVsZVJlcywgc2NoZWR1bGUsIHNjaGVkdWxlU3RhbmRhcmRSZXMsIHNjaGVkdWxlU3RhbmRhcmQsIG91dExpc3QsIGluTnVtcywgZ29vZHNOdW1zLCBiYWROdW1zLCBfaXRlcmF0b3IyMSwgX3N0ZXAyMSwgaXRlbSwgbG90TG9ncywgcGxhbkFyZWFIb3VyTGlzdCwgdXNlckFycmF5LCB1c2VyU2V0LCBzdW1NaW51dGVzLCBfaXRlcmF0b3IyMiwgX3N0ZXAyMiwgdXNlckNvZGUsIG5pY2tOYW1lLCBtaW51dGVzLCBhcnJheSwgX2l0ZXJhdG9yMjMsIF9zdGVwMjMsIGg7CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTEwJChfY29udGV4dDEwKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDEwLnByZXYgPSBfY29udGV4dDEwLm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF90aGlzOS5sb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgICAgICBfY29udGV4dDEwLm5leHQgPSAzOwogICAgICAgICAgICAgIHJldHVybiAoMCwgX21lc0xvZy5nZXRNZXNMb2cpKGlkKTsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIHJlcyA9IF9jb250ZXh0MTAuc2VudDsKICAgICAgICAgICAgICBmb3JtID0gcmVzLmRhdGE7CiAgICAgICAgICAgICAgaWYgKGZvcm0ubWF0ZXJpYWxBcnJheSkgewogICAgICAgICAgICAgICAgX3RoaXM5Lm1hdGVyaWFsQXJyYXkgPSBKU09OLnBhcnNlKGZvcm0ubWF0ZXJpYWxBcnJheSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzOS5mb3JtID0gZm9ybTsKICAgICAgICAgICAgICBzY2hlZHVsZUNvZGUgPSBmb3JtLnNjaGVkdWxlQ29kZTsKICAgICAgICAgICAgICBhcnIgPSBzY2hlZHVsZUNvZGUuc3BsaXQoJy0nKTsKICAgICAgICAgICAgICBfY29udGV4dDEwLm5leHQgPSAxMTsKICAgICAgICAgICAgICByZXR1cm4gKDAsIF9zY2hlZHVsZS5nZXRTY2hlZHVsZUJ5Q29kZSkoYXJyWzBdICsgIi0iICsgYXJyWzFdKTsKICAgICAgICAgICAgY2FzZSAxMToKICAgICAgICAgICAgICBzY2hlZHVsZVJlcyA9IF9jb250ZXh0MTAuc2VudDsKICAgICAgICAgICAgICBpZiAoc2NoZWR1bGVSZXMuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICAgICAgICBzY2hlZHVsZSA9IHNjaGVkdWxlUmVzLmRhdGE7CiAgICAgICAgICAgICAgICBfdGhpczkuc2NoZWR1bGUgPSBzY2hlZHVsZTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX2NvbnRleHQxMC5uZXh0ID0gMTU7CiAgICAgICAgICAgICAgcmV0dXJuICgwLCBfc2NoZWR1bGVTdGFuZGFyZC5nZXRTY2hlZHVsZVN0YW5kYXJkQnlDb2RlKShmb3JtLnByb2R1Y3RObyk7CiAgICAgICAgICAgIGNhc2UgMTU6CiAgICAgICAgICAgICAgc2NoZWR1bGVTdGFuZGFyZFJlcyA9IF9jb250ZXh0MTAuc2VudDsKICAgICAgICAgICAgICBpZiAoIShzY2hlZHVsZVN0YW5kYXJkUmVzLmNvZGUgPT09IDIwMCkpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0MTAubmV4dCA9IDI0OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHNjaGVkdWxlU3RhbmRhcmQgPSBzY2hlZHVsZVN0YW5kYXJkUmVzLmRhdGE7CiAgICAgICAgICAgICAgX3RoaXM5LnNjaGVkdWxlU3RhbmRhcmQgPSBzY2hlZHVsZVN0YW5kYXJkOwogICAgICAgICAgICAgIGlmICghc2NoZWR1bGVTdGFuZGFyZCkgewogICAgICAgICAgICAgICAgX2NvbnRleHQxMC5uZXh0ID0gMjQ7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX2NvbnRleHQxMC5uZXh0ID0gMjI7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzOS5pbXBvcnRBcmNoaXZlKHNjaGVkdWxlU3RhbmRhcmQuYXJjaGl2ZUlkKTsKICAgICAgICAgICAgY2FzZSAyMjoKICAgICAgICAgICAgICBfY29udGV4dDEwLm5leHQgPSAyNDsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXM5LmJ1aWxkRGlmZkRhdGEoKTsKICAgICAgICAgICAgY2FzZSAyNDoKICAgICAgICAgICAgICBfY29udGV4dDEwLm5leHQgPSAyNjsKICAgICAgICAgICAgICByZXR1cm4gKDAsIF93aXBjb250UGFydGlhbG91dC5hbGxXaXBDb250UGFydGlhbE91dCkoewogICAgICAgICAgICAgICAgYXJlYW5vOiBmb3JtLmFyZWFObywKICAgICAgICAgICAgICAgIGV2ZW50RGF0ZTogZm9ybS53b3JrRGF0ZSwKICAgICAgICAgICAgICAgIGVxdWlwbWVudG5vOiBmb3JtLmVxdWlwbWVudE5vLAogICAgICAgICAgICAgICAgc2NoZWR1bGVDb2RlOiBmb3JtLnNjaGVkdWxlQ29kZQogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDI2OgogICAgICAgICAgICAgIG91dExpc3QgPSBfY29udGV4dDEwLnNlbnQ7CiAgICAgICAgICAgICAgaW5OdW1zID0gX3RoaXM5LiRiaWcoMCk7CiAgICAgICAgICAgICAgZ29vZHNOdW1zID0gX3RoaXM5LiRiaWcoMCk7CiAgICAgICAgICAgICAgYmFkTnVtcyA9IF90aGlzOS4kYmlnKDApOwogICAgICAgICAgICAgIF90aGlzOS5vdXRBcnJheSA9IG91dExpc3QubWFwKGZ1bmN0aW9uIChpKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICAgICAgICBsb3RObzogaS5sb3RubywKICAgICAgICAgICAgICAgICAgb3BObzogaS5vcG5vLAogICAgICAgICAgICAgICAgICBldmVudFRpbWU6IGkuZXZlbnR0aW1lLAogICAgICAgICAgICAgICAgICB1c2VyTm86IGkudXNlcm5vLAogICAgICAgICAgICAgICAgICBhcmVhTm86IGkuYXJlYW5vLAogICAgICAgICAgICAgICAgICBpbk51bXM6IGkuaW5wdXRxdHksCiAgICAgICAgICAgICAgICAgIC8v5Lqn5Ye66YePCiAgICAgICAgICAgICAgICAgIGdvb2RzTnVtczogaS5nb29kcXR5LAogICAgICAgICAgICAgICAgICAvL+iJr+WTgemHjwogICAgICAgICAgICAgICAgICBiYWROdW1zOiBpLnNjcmFwcXR5LAogICAgICAgICAgICAgICAgICAvL+iJr+WTgemHjwogICAgICAgICAgICAgICAgICBlcXVpcG1lbnRObzogaS5lcXVpcG1lbnRubywKICAgICAgICAgICAgICAgICAgdXNlck5hbWU6IGkudXNlck5hbWUKICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgfSk7IC8v6YeN5ZG95ZCN5bGe5oCn5ZCN56ewLOaWueS+v+eQhuino+ivreS5iQogICAgICAgICAgICAgIF9pdGVyYXRvcjIxID0gKDAsIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyMi5kZWZhdWx0KShfdGhpczkub3V0QXJyYXkpOwogICAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICBmb3IgKF9pdGVyYXRvcjIxLnMoKTsgIShfc3RlcDIxID0gX2l0ZXJhdG9yMjEubigpKS5kb25lOykgewogICAgICAgICAgICAgICAgICBpdGVtID0gX3N0ZXAyMS52YWx1ZTsKICAgICAgICAgICAgICAgICAgaW5OdW1zID0gX3RoaXM5LmFkZChpbk51bXMsIGl0ZW0uaW5OdW1zKTsKICAgICAgICAgICAgICAgICAgZ29vZHNOdW1zID0gX3RoaXM5LmFkZChnb29kc051bXMsIGl0ZW0uZ29vZHNOdW1zKTsKICAgICAgICAgICAgICAgICAgYmFkTnVtcyA9IF90aGlzOS5hZGQoYmFkTnVtcywgaXRlbS5iYWROdW1zKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICAgICAgICAgIF9pdGVyYXRvcjIxLmUoZXJyKTsKICAgICAgICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgICAgICAgX2l0ZXJhdG9yMjEuZigpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBmb3JtLmluTnVtcyA9IGluTnVtcy50b051bWJlcigpOwogICAgICAgICAgICAgIGZvcm0uZ29vZHNOdW1zID0gZ29vZHNOdW1zLnRvTnVtYmVyKCk7CiAgICAgICAgICAgICAgZm9ybS5iYWROdW1zID0gYmFkTnVtcy50b051bWJlcigpOwogICAgICAgICAgICAgIF9jb250ZXh0MTAubmV4dCA9IDM4OwogICAgICAgICAgICAgIHJldHVybiAoMCwgX21lc1ZpZXcuYWxsV2lwTG90TG9nKSh7CiAgICAgICAgICAgICAgICBhcmVhTm86IGZvcm0uYXJlYU5vLAogICAgICAgICAgICAgICAgY3JlYXRlVGltZTogZm9ybS53b3JrRGF0ZSwKICAgICAgICAgICAgICAgIGVxdWlwbWVudE5vOiBmb3JtLmVxdWlwbWVudE5vLAogICAgICAgICAgICAgICAgc2NoZWR1bGVDb2RlOiBmb3JtLnNjaGVkdWxlQ29kZQogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDM4OgogICAgICAgICAgICAgIGxvdExvZ3MgPSBfY29udGV4dDEwLnNlbnQ7CiAgICAgICAgICAgICAgX3RoaXM5LmxvdExvZ3MgPSBsb3RMb2dzOwogICAgICAgICAgICAgIF9jb250ZXh0MTAubmV4dCA9IDQyOwogICAgICAgICAgICAgIHJldHVybiAoMCwgX3BsYW5BcmVhSG91cnMuYWxsUGxhbkFyZWFIb3VycykoewogICAgICAgICAgICAgICAgYXJlYU5vOiBmb3JtLmFyZWFObywKICAgICAgICAgICAgICAgIGVxdWlwbWVudE5vOiBmb3JtLmVxdWlwbWVudE5vLAogICAgICAgICAgICAgICAgd29ya0RhdGU6IGZvcm0ud29ya0RhdGUsCiAgICAgICAgICAgICAgICBzYWlsaW5nczogZm9ybS5zYWlsaW5ncywKICAgICAgICAgICAgICAgIHNjaGVkdWxlQ29kZTogZm9ybS5zY2hlZHVsZUNvZGUKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgY2FzZSA0MjoKICAgICAgICAgICAgICBwbGFuQXJlYUhvdXJMaXN0ID0gX2NvbnRleHQxMC5zZW50OwogICAgICAgICAgICAgIHVzZXJBcnJheSA9IFtdOwogICAgICAgICAgICAgIHVzZXJTZXQgPSBuZXcgU2V0KHBsYW5BcmVhSG91ckxpc3QubWFwKGZ1bmN0aW9uIChpKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gaS51c2VyQ29kZTsKICAgICAgICAgICAgICB9KSk7CiAgICAgICAgICAgICAgc3VtTWludXRlcyA9IF90aGlzOS4kYmlnKDApOwogICAgICAgICAgICAgIF9pdGVyYXRvcjIyID0gKDAsIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyMi5kZWZhdWx0KSh1c2VyU2V0KTsKICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgZm9yIChfaXRlcmF0b3IyMi5zKCk7ICEoX3N0ZXAyMiA9IF9pdGVyYXRvcjIyLm4oKSkuZG9uZTspIHsKICAgICAgICAgICAgICAgICAgdXNlckNvZGUgPSBfc3RlcDIyLnZhbHVlOwogICAgICAgICAgICAgICAgICBuaWNrTmFtZSA9IHZvaWQgMDsKICAgICAgICAgICAgICAgICAgbWludXRlcyA9IF90aGlzOS4kYmlnKDApOwogICAgICAgICAgICAgICAgICBhcnJheSA9IFtdOwogICAgICAgICAgICAgICAgICBfaXRlcmF0b3IyMyA9ICgwLCBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcjIuZGVmYXVsdCkocGxhbkFyZWFIb3VyTGlzdCk7CiAgICAgICAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICAgICAgZm9yIChfaXRlcmF0b3IyMy5zKCk7ICEoX3N0ZXAyMyA9IF9pdGVyYXRvcjIzLm4oKSkuZG9uZTspIHsKICAgICAgICAgICAgICAgICAgICAgIGggPSBfc3RlcDIzLnZhbHVlOwogICAgICAgICAgICAgICAgICAgICAgaWYgKHVzZXJDb2RlID09PSBoLnVzZXJDb2RlKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIG5pY2tOYW1lID0gaC5uaWNrTmFtZTsKICAgICAgICAgICAgICAgICAgICAgICAgYXJyYXkucHVzaChoKTsKICAgICAgICAgICAgICAgICAgICAgICAgbWludXRlcyA9IF90aGlzOS5hZGQobWludXRlcywgaC5taW51dGVzKTsKICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjIzLmUoZXJyKTsKICAgICAgICAgICAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgICAgICAgICAgICBfaXRlcmF0b3IyMy5mKCk7CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgdXNlckFycmF5LnB1c2goewogICAgICAgICAgICAgICAgICAgIHVzZXJDb2RlOiB1c2VyQ29kZSwKICAgICAgICAgICAgICAgICAgICBuaWNrTmFtZTogbmlja05hbWUsCiAgICAgICAgICAgICAgICAgICAgbWludXRlczogbWludXRlcywKICAgICAgICAgICAgICAgICAgICBhcnJheTogYXJyYXkKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIHN1bU1pbnV0ZXMgPSBfdGhpczkuYWRkKHN1bU1pbnV0ZXMsIG1pbnV0ZXMpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgICAgICAgICAgX2l0ZXJhdG9yMjIuZShlcnIpOwogICAgICAgICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICAgICAgICBfaXRlcmF0b3IyMi5mKCk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGZvcm0ubWludXRlcyA9IHN1bU1pbnV0ZXMudG9OdW1iZXIoKTsKICAgICAgICAgICAgICBfdGhpczkudXNlckFycmF5ID0gdXNlckFycmF5OwogICAgICAgICAgICAgIF9jb250ZXh0MTAubmV4dCA9IDUyOwogICAgICAgICAgICAgIHJldHVybiBfdGhpczkuYnVpbGRQcm9kdWN0aW9uTG9nKCk7CiAgICAgICAgICAgIGNhc2UgNTI6CiAgICAgICAgICAgICAgX2NvbnRleHQxMC5uZXh0ID0gNTQ7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzOS5idWlsZE1hdGVyaWFsQXJyYXkoKTsKICAgICAgICAgICAgY2FzZSA1NDoKICAgICAgICAgICAgICBfY29udGV4dDEwLm5leHQgPSA1NjsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXM5LmJ1aWxkUWNMb2dzKCk7CiAgICAgICAgICAgIGNhc2UgNTY6CiAgICAgICAgICAgICAgX3RoaXM5LmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgY2FzZSA1NzoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQxMC5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTEwKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgc3VibWl0Rm9ybTogZnVuY3Rpb24gc3VibWl0Rm9ybSgpIHsKICAgICAgdmFyIF90aGlzMTAgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlMTEoKSB7CiAgICAgICAgdmFyIGZvcm07CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTExJChfY29udGV4dDExKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDExLnByZXYgPSBfY29udGV4dDExLm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIGZvcm0gPSBPYmplY3QuYXNzaWduKHt9LCBfdGhpczEwLmZvcm0pOwogICAgICAgICAgICAgIGZvcm0ubWF0ZXJpYWxBcnJheSA9IEpTT04uc3RyaW5naWZ5KF90aGlzMTAubWF0ZXJpYWxBcnJheSk7CiAgICAgICAgICAgICAgaWYgKCEoZm9ybS5pZCAhPSBudWxsKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQxMS5uZXh0ID0gMTc7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX2NvbnRleHQxMS5wcmV2ID0gMzsKICAgICAgICAgICAgICBfdGhpczEwLmJ0bkxvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgIF9jb250ZXh0MTEubmV4dCA9IDc7CiAgICAgICAgICAgICAgcmV0dXJuICgwLCBfbWVzTG9nLnVwZGF0ZU1lc0xvZykoZm9ybSk7CiAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgICBfdGhpczEwLmJ0bkxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICBfdGhpczEwLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIF90aGlzMTAuJHBhcmVudC4kcGFyZW50Lm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICBfY29udGV4dDExLm5leHQgPSAxMjsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMxMC4kcGFyZW50LiRwYXJlbnQuZ2V0TGlzdCgpOwogICAgICAgICAgICBjYXNlIDEyOgogICAgICAgICAgICAgIF9jb250ZXh0MTEubmV4dCA9IDE3OwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDE0OgogICAgICAgICAgICAgIF9jb250ZXh0MTEucHJldiA9IDE0OwogICAgICAgICAgICAgIF9jb250ZXh0MTEudDAgPSBfY29udGV4dDExWyJjYXRjaCJdKDMpOwogICAgICAgICAgICAgIF90aGlzMTAuYnRuTG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICBjYXNlIDE3OgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDExLnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMTEsIG51bGwsIFtbMywgMTRdXSk7CiAgICAgIH0pKSgpOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_mesLog", "require", "_planAreaHours", "_wipcontPartialout", "_area", "_interopRequireDefault", "_mes<PERSON><PERSON>w", "_table", "_archive", "_project", "_scheduleStandard", "_qc", "_finishedInspection", "_wipContMaterialLot", "_erp", "_dataCharts", "_gzl", "_schedule", "_materialTabs", "_log4", "_log5", "_scheduleTable", "name", "components", "MesLogScheduleTable", "MesProductionLog", "MesLogProductionLog", "MesLogMaterialTabs", "MesLogDataCharts", "MesQc", "ProcessTable", "MesLogArea", "data", "loading", "btnLoading", "readonly", "form", "rules", "currentTab", "sailingsOptions", "label", "value", "outArray", "lotLogs", "mesLotWaitList", "userArray", "project", "bomData", "bomTree", "diffDataArray", "diffDataOptions", "inspectionList", "qcOptions", "materialLogArray", "scheduleStandard", "materialArray", "schedule", "bcpList", "bcpArray", "otherArray", "scheduleBcpArray", "scheduleOtherArray", "created", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "stop", "methods", "tabChange", "_this", "_callee2", "_callee2$", "_context2", "$nextTick", "reduceDiffChart", "reduceQcChart", "cancel", "$parent", "open", "reset", "id", "code", "workDate", "sailings", "areaNo", "lineLeader", "minutes", "inNums", "goodsNums", "badNums", "resetForm", "diffHours", "startTime", "endTime", "moment", "diff", "divide", "toNumber", "_this2", "_callee3", "mesQc", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_callee3$", "_context3", "$refs", "init", "_this3", "_callee4", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "timeLineChart", "_callee4$", "_context4", "buildProductionLog", "_this4", "_callee5", "bomRes", "_iterator", "_step", "item", "gzlParams", "gzlRes", "gzl", "res", "_iterator2", "_step2", "o", "_iterator3", "_step3", "_o", "_callee5$", "_context5", "getBomByErpCode", "productNo", "sent", "filter", "i", "mb005", "_createForOfIteratorHelper2", "s", "n", "done", "md003", "md001", "getGzlByParams", "max", "avg", "min", "t0", "e", "f", "finish", "getMaterialLogList", "console", "log", "map", "materialCode", "includes", "push", "remark", "err", "buildMaterialArray", "_this5", "_callee6", "formRate", "scheduleCode", "materialLogList", "arr", "erpBomData", "scheduleNums", "materialNoSet", "_iterator4", "_step4", "_iterator5", "_step5", "materialNo", "te010Set", "sjBomNum", "cb010", "materialName", "te006", "te005Sum", "lotSums", "erpSylSum", "_iterator6", "_step6", "b", "_iterator7", "_step7", "_log", "te010Array", "_iterator8", "_step8", "te010", "erpLlNums", "erpSylNums", "_iterator9", "_step9", "ot", "_iterator13", "_step13", "t", "equipmentSet", "_iterator10", "_step10", "_log2", "equipmentArray", "_iterator11", "_step11", "equipmentNo", "l", "_iterator14", "_step14", "_log3", "llArray", "_iterator12", "_step12", "_b", "sylRate", "llBlNums", "llBlReason", "scBlNums", "scBlReason", "_iterator15", "_step15", "_ot", "_iterator16", "_step16", "_t", "gdXql", "lotPlanXql", "lotLlXql", "loss", "erpRateNums", "_callee6$", "_context6", "productNums", "planNums", "allGroupByEquipmentNo", "split", "getScheduleMaterial", "workOrderNo", "workOrderSingle", "Set", "add", "$big", "te004", "Number", "te017", "te005", "nums", "gdSylNums", "materialLotNo", "times", "te001", "te002", "te008", "me003", "te013", "multiply", "subtract", "xfCyRate", "lotXql", "lotRate", "diffNums", "buildDiffData", "_this6", "_callee7", "actualHours", "actualDuration", "actualProductivity", "actualHoursRate", "standardHours", "standardPersonNums", "standardHoursRate", "_callee7$", "_context7", "sumMinutes", "opNo", "costHoursRate", "costNums", "productivity", "sumNums", "title", "text", "legend", "radar", "indicator", "Math", "series", "type", "slice", "importArchive", "archiveId", "_this7", "_callee8", "archiveRes", "archive", "projectRes", "cubicleArray", "imgs", "videos", "_iterator17", "_step17", "_iterator18", "_step18", "second", "_iterator19", "_step19", "w", "_callee8$", "_context8", "getArchive", "getProject", "projectId", "bomResource", "bomType", "resourceFinishedGoodsId", "erpPrice", "bomArray", "erpCode", "JSON", "parse", "sectionArray", "workTypeArray", "homeworkImgs", "apply", "_toConsumableArray2", "homeworkVideos", "length", "buildQcLogs", "_this8", "_callee9", "_iterator20", "_step20", "erpColumn", "obj", "groupInspectionList", "rawData", "totalData", "sum", "j", "grid", "_callee9$", "_context9", "allFinishedInspection", "singleCategory", "tg030", "allGroupByBatchFinishedInspection", "batchNums", "samplingNums", "left", "right", "top", "bottom", "sid", "stack", "<PERSON><PERSON><PERSON><PERSON>", "show", "formatter", "params", "round", "d", "did", "selectedMode", "yAxis", "xAxis", "batchNo", "_this9", "_callee10", "scheduleRes", "scheduleStandardRes", "outList", "_iterator21", "_step21", "planAreaHourList", "userSet", "_iterator22", "_step22", "userCode", "nick<PERSON><PERSON>", "array", "_iterator23", "_step23", "h", "_callee10$", "_context10", "getMesLog", "getScheduleByCode", "getScheduleStandardByCode", "allWipContPartialOut", "areano", "eventDate", "equipmentno", "lotNo", "lotno", "opno", "eventTime", "eventtime", "userNo", "userno", "inputqty", "goodqty", "scrapqty", "userName", "allWipLotLog", "createTime", "allPlanAreaHours", "submitForm", "_this10", "_callee11", "_callee11$", "_context11", "Object", "assign", "stringify", "updateMesLog", "msgSuccess", "getList"], "sources": ["src/views/production/mesLog/save.vue"], "sourcesContent": ["<template>\r\n  <div v-loading=\"loading\" >\r\n    <el-row>\r\n      <el-col :span=\"8\">\r\n        <el-row >\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">计划日期</div>\r\n            <div class=\"content\">{{form.workDate}}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">班次</div>\r\n            <div class=\"content\">{{selectOptionsLabel(sailingsOptions, form.sailings)}}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">区域</div>\r\n            <div class=\"content\">{{form.areaNo}}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">设备</div>\r\n            <div class=\"content\">{{form.equipmentNo}}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\" style=\"width: 100px\">线长</div>\r\n            <div class=\"content\">{{ form.lineLeader }}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">生产数量</div>\r\n            <div class=\"content\">{{form.productNums}}</div>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n      <el-col :span=\"8\" class=\"header-wrapper\" >\r\n        <div class=\"header-title\">{{form.code}}</div>\r\n        <div class=\"header-img\"></div>\r\n        <div class=\"header-text\">{{form.startTime}}~{{form.endTime}}</div>\r\n      </el-col>\r\n      <el-col :span=\"8\" >\r\n        <el-row >\r\n          <el-col :span=\"24\" class=\"cell-wrapper\">\r\n            <div class=\"label\">品名</div>\r\n            <div class=\"content\">{{form.productName}}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">品号</div>\r\n            <div class=\"content\">{{form.productNo}}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\" style=\"width: 100px\">客户订单号</div>\r\n            <div class=\"content\">{{ form.customerOrderNo }}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\" style=\"width: 100px\">客户名称</div>\r\n            <div class=\"content\">{{ form.customerOrderNo }}</div>\r\n          </el-col>\r\n          <el-col :span=\"12\" class=\"cell-wrapper\">\r\n            <div class=\"label\">\r\n              产出占比\r\n              <el-tooltip content=\"良品量 / 工单量\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n              </el-tooltip>\r\n            </div>\r\n            <div class=\"content\" v-if=\"schedule.planNums\">{{toPercent(divide(form.productNums,schedule.planNums))}}</div>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-tabs v-model=\"currentTab\" type=\"border-card\" style=\"margin-top: 20px\" @tab-click=\"tabChange\" >\r\n      <el-tab-pane label=\"人员/工时\" lazy name=\"person\" key=\"person\" >\r\n        <MesLogArea\r\n          v-if=\"form.minutes && form.inNums\"\r\n          :form=\"form\"\r\n          :mes-lot-wait-list=\"mesLotWaitList\"\r\n          :lot-logs=\"lotLogs\"\r\n          :out-array=\"outArray\"\r\n          :user-array=\"userArray\"\r\n        />\r\n      </el-tab-pane>\r\n      <el-tab-pane label=\"工艺标准\" lazy name=\"standard\" key=\"standard\" >\r\n        <ProcessTable\r\n          :project=\"project\"\r\n          :bom-data=\"bomData\"\r\n          :bom-tree=\"bomTree\"\r\n          :readonly=\"true\" />\r\n      </el-tab-pane>\r\n      <el-tab-pane key=\"material\" label=\"物料\" lazy name=\"material\">\r\n        <MesLogMaterialTabs :form=\"form\" :material-log-list=\"materialLogArray\" />\r\n      </el-tab-pane>\r\n      <el-tab-pane key=\"charts\" label=\"产线报表\" lazy name=\"charts\" >\r\n        <MesLogDataCharts ref=\"mesDataCharts\" :diff-data-array=\"diffDataArray\" />\r\n      </el-tab-pane>\r\n      <el-tab-pane key=\"equipment\" label=\"数采看板\" lazy name=\"equipment\" >\r\n\r\n      </el-tab-pane>\r\n      <el-tab-pane key=\"schedule\" label=\"工单维度分摊\" lazy name=\"schedule\" >\r\n        <MesLogScheduleTable\r\n          :plan=\"form\"\r\n          :bcp-list=\"bcpList\"\r\n          :bcp-array=\"scheduleBcpArray\"\r\n          :other-array=\"scheduleOtherArray\"\r\n        />\r\n      </el-tab-pane>\r\n      <el-tab-pane key=\"report\" label=\"生产记录\" lazy name=\"report\" >\r\n        <MesLogProductionLog\r\n          :plan=\"form\"\r\n          :bcp-list=\"bcpList\"\r\n          :bcp-array=\"bcpArray\"\r\n          :other-array=\"otherArray\"\r\n          :material-array=\"materialArray\"\r\n        />\r\n      </el-tab-pane>\r\n      <el-tab-pane key=\"qc\" label=\"品质检验\" lazy name=\"qc\" >\r\n        <MesQc ref=\"mesQc\" :inspection-list=\"inspectionList\" />\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n\r\n    <div class=\"dialog-footer\" style=\"margin-top: 20px\" v-if=\"!readonly\" >\r\n      <el-button type=\"primary\" @click=\"submitForm\" size=\"mini\" :loading=\"btnLoading\" >\r\n        确 定\r\n        <el-tooltip content=\"目前只有物料信息的备注需要保存,其他无需操作\" placement=\"top\">\r\n          <i class=\"el-icon-question\"></i>\r\n        </el-tooltip>\r\n      </el-button>\r\n    </div>\r\n\r\n  </div>\r\n</template>\r\n<script >\r\n\r\nimport {\r\n  addMesLog,\r\n  getMesLog,\r\n  updateMesLog\r\n} from \"@/api/production/mesLog\";\r\nimport {allPlanAreaHours} from \"@/api/production/planAreaHours\";\r\nimport {allWipContPartialOut} from \"@/api/mes/wipcontPartialout\";\r\nimport MesLogArea from \"@/views/production/mesLog/area.vue\";\r\nimport {allWipLotLog} from \"@/api/mes/mesView\";\r\nimport ProcessTable from \"@/views/production/layout/process/table.vue\";\r\nimport {getArchive} from \"@/api/project/archive\";\r\nimport {getProject} from \"@/api/project/project\";\r\nimport {getScheduleStandardByCode} from \"@/api/order/scheduleStandard\";\r\nimport MesQc from \"@/views/mes/production/qc.vue\";\r\nimport {allFinishedInspection, allGroupByBatchFinishedInspection} from \"@/api/qc/finishedInspection\";\r\nimport {allGroupByEquipmentNo} from \"@/api/mes/wipContMaterialLot\";\r\nimport {getBomByErpCode, getScheduleMaterial} from \"@/api/common/erp\";\r\nimport MesLogDataCharts from \"@/views/production/mesLog/dataCharts.vue\";\r\nimport {getGzlByParams} from \"@/api/sop/gzl\";\r\nimport {getMaterialLogList} from \"@/api/production/mesLog\";\r\nimport {getScheduleByCode} from \"@/api/order/schedule\";\r\nimport MesLogMaterialTabs from \"@/views/production/mesLog/materialTabs.vue\";\r\nimport MesLogProductionLog from \"@/views/production/mesLog/log.vue\";\r\nimport MesProductionLog from \"@/views/mes/production/log.vue\";\r\nimport MesLogScheduleTable from \"@/views/production/mesLog/scheduleTable.vue\";\r\n\r\nexport default {\r\n  name: 'mesLogSave',\r\n  components: {\r\n    MesLogScheduleTable,\r\n    MesProductionLog,\r\n    MesLogProductionLog,\r\n    MesLogMaterialTabs,\r\n    MesLogDataCharts,\r\n    MesQc,\r\n    ProcessTable,\r\n    MesLogArea\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      btnLoading: false,\r\n      readonly: false,\r\n      form: {},\r\n      rules: {},\r\n      currentTab: '',\r\n      sailingsOptions: [\r\n        {label: '白班',value: '0'},\r\n        {label: '晚班',value: '1'},\r\n      ],\r\n      outArray: [],\r\n      lotLogs: [],\r\n      mesLotWaitList: [],\r\n      userArray: [],\r\n      project: {},\r\n      bomData: [],\r\n      bomTree: [],\r\n      diffDataArray: [],\r\n      diffDataOptions: {},\r\n      inspectionList: [],\r\n      qcOptions: {},\r\n      materialLogArray: [],\r\n      scheduleStandard: {},\r\n      materialArray: [],\r\n      schedule: {},\r\n      bcpList: [],\r\n      bcpArray: [],\r\n      otherArray: [],\r\n      scheduleBcpArray: [],\r\n      scheduleOtherArray: [],\r\n    }\r\n  },\r\n  async created() {\r\n  },\r\n  methods: {\r\n    async tabChange() {\r\n      await this.$nextTick()\r\n      if(this.currentTab === 'person') {\r\n\r\n      } else if(this.currentTab === 'charts') {\r\n        await this.reduceDiffChart()\r\n      } else if(this.currentTab === 'qc') {\r\n        await this.reduceQcChart()\r\n      }\r\n    },\r\n    cancel() {\r\n      this.$parent.$parent.open = false;\r\n      this.reset();\r\n    },\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        code: null,\r\n        workDate: null,\r\n        sailings: null,\r\n        areaNo: null,\r\n        lineLeader: null,\r\n        minutes: null,\r\n        inNums: null,\r\n        goodsNums: null,\r\n        badNums: null,\r\n      };\r\n      this.resetForm(\"form\")\r\n      this.project = {}\r\n      this.bomData = []\r\n      this.bomTree = []\r\n      this.materialLogArray = []\r\n      this.outArray = []\r\n      this.lotLogs = []\r\n      this.mesLotWaitList = []\r\n      this.userArray = []\r\n      this.diffDataArray = []\r\n      this.diffDataOptions = {}\r\n      this.inspectionList = []\r\n      this.qcOptions = {}\r\n      this.schedule = {}\r\n      this.scheduleStandard = {}\r\n      this.materialArray = []\r\n      this.bcpList = []\r\n      this.bcpArray = []\r\n      this.otherArray = []\r\n      this.scheduleBcpArray = []\r\n      this.scheduleOtherArray = []\r\n    },\r\n    diffHours(startTime,endTime) {\r\n      if(startTime && endTime) {\r\n        let minutes = this.moment(endTime,'YYYY-MM-DD hh:mm').diff(this.moment(startTime,'YYYY-MM-DD hh:mm'), 'minutes')\r\n        if(minutes) {\r\n          return this.divide(minutes,60).toNumber()\r\n        }\r\n      }\r\n    },\r\n    async reduceQcChart() {\r\n      await this.$nextTick()\r\n      const mesQc = this.$refs.mesQc\r\n      if(mesQc) {\r\n        const qcCharts = mesQc.$refs.qcCharts\r\n        if(qcCharts) {\r\n          await qcCharts.init(this.qcOptions)\r\n        }\r\n      }\r\n    },\r\n    async reduceDiffChart() {\r\n      await this.$nextTick()\r\n      const mesDataCharts = this.$refs.mesDataCharts\r\n      if(mesDataCharts) {\r\n        const diffChart = mesDataCharts.$refs.diffChart\r\n        if(diffChart) {\r\n          await diffChart.init(this.diffDataOptions)\r\n        }\r\n        const timeLineChart = mesDataCharts.$refs.timeLineChart\r\n        if(timeLineChart) {\r\n        }\r\n      }\r\n    },\r\n    async buildProductionLog() {\r\n      const form = this.form\r\n      const materialArray = this.materialArray\r\n      const bomRes = await getBomByErpCode(form.productNo)\r\n      if(bomRes.code === 200) {\r\n        let bcpList = bomRes.data.filter(i => i.mb005 === '103')\r\n        for (const item of bcpList) {\r\n          const gzlParams = {\r\n            md003: item.md003,\r\n            md001: item.md001,\r\n          }\r\n          const gzlRes = await getGzlByParams(gzlParams)\r\n          if(gzlRes.code === 200 && gzlRes.data) {\r\n            const gzl = gzlRes.data\r\n            item.max = gzl.max\r\n            item.avg = gzl.avg\r\n            item.min = gzl.min\r\n          }\r\n        }\r\n        this.bcpList = bcpList\r\n      }\r\n\r\n      let res = await getMaterialLogList(form.id)\r\n      if(res){\r\n        const bcpArray = res.bcpArray\r\n        const otherArray = res.otherArray\r\n        const scheduleBcpArray = res.scheduleBcpArray\r\n        const scheduleOtherArray = res.scheduleOtherArray\r\n        console.log(scheduleBcpArray)\r\n        console.log(scheduleOtherArray)\r\n        if(bcpArray){\r\n          this.bcpArray = bcpArray\r\n          for (const o of bcpArray) {\r\n            if(!materialArray.map(i=> i.materialCode).includes(o.materialCode)) {\r\n              materialArray.push({\r\n                materialCode: o.materialCode,\r\n                remark: null,\r\n              })\r\n            }\r\n          }\r\n        }\r\n        if(otherArray){\r\n          this.otherArray = otherArray\r\n          for (const o of otherArray) {\r\n            if(!materialArray.map(i=> i.materialCode).includes(o.materialCode)) {\r\n              materialArray.push({\r\n                materialCode: o.materialCode,\r\n                remark: null,\r\n              })\r\n            }\r\n          }\r\n        }\r\n        if(scheduleBcpArray) {\r\n          this.scheduleBcpArray = scheduleBcpArray\r\n        }\r\n        if(scheduleOtherArray) {\r\n          this.scheduleOtherArray = scheduleOtherArray\r\n        }\r\n      }\r\n    },\r\n    async buildMaterialArray() {\r\n      const form = this.form\r\n      const schedule = this.schedule\r\n      const formRate = this.divide(form.productNums,schedule.planNums)//产出占比\r\n      if(formRate) {\r\n        let scheduleCode = form.scheduleCode\r\n        const materialLogList = await allGroupByEquipmentNo({scheduleCode,workDate: form.workDate})//暂时不算班次\r\n        this.materialLogList = materialLogList\r\n        const arr = scheduleCode.split('-')\r\n        let erpBomData = await getScheduleMaterial({workOrderNo: arr[1], workOrderSingle: arr[0],})\r\n\r\n        const scheduleNums = schedule.planNums\r\n        const materialNoSet = new Set()\r\n        for (const log of materialLogList) {\r\n          materialNoSet.add(log.materialNo)\r\n        }\r\n\r\n        const materialLogArray = []\r\n        for (const materialNo of materialNoSet) {\r\n          const te010Set = new Set()//在品号维度下再排批次\r\n          let sjBomNum = 0 //bom用量对于工单产品是成品的\r\n          let cb010 = 0//erp损耗率\r\n          let materialName\r\n          let te006//erp单位\r\n          let te005Sum = this.$big(0)//工单发料量小计\r\n          let lotSums = this.$big(0)//批次使用量小计\r\n          let erpSylSum = this.$big(0)//erp品号维度使用量小计\r\n          for (const b of erpBomData) {\r\n            if(b.te004 === materialNo) {\r\n              sjBomNum = Number(b.sjBomNum)\r\n              cb010 = Number(b.cb010)\r\n              materialName = b.te017\r\n              te006 = b.te006\r\n\r\n              te005Sum = this.add(te005Sum,b.te005)\r\n              te010Set.add(b.te010)//按照e10的批次来\r\n            }\r\n          }\r\n          for (const log of materialLogList) {\r\n            if(materialNo === log.materialNo) {\r\n              lotSums = this.add(lotSums,log.nums)\r\n            }\r\n          }\r\n          const te010Array = []\r\n          for (const te010 of te010Set) {\r\n            let erpLlNums = 0\r\n            let erpSylNums = 0\r\n            for (const ot of this.otherArray) {\r\n              if(ot.materialCode === materialNo ) {\r\n                for (const t of ot.te010Array) {\r\n                  if(t.te010 === te010) {\r\n                    if(t.gdSylNums) {\r\n                      erpLlNums = Number(t.gdSylNums)\r\n                      erpSylNums = Number(t.gdSylNums)\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n            const equipmentSet = new Set()//品号批次内排设备\r\n            for (const log of materialLogList) {\r\n              if(materialNo === log.materialNo && te010 === log.materialLotNo) {\r\n                equipmentSet.add(log.equipmentNo)\r\n              }\r\n            }\r\n            const equipmentArray = []\r\n            for (const equipmentNo of equipmentSet) {\r\n              let l = {\r\n                equipmentNo,\r\n              }\r\n              for (const log of materialLogList) {\r\n                if(materialNo === log.materialNo && te010 === log.materialLotNo && equipmentNo === log.equipmentNo) {\r\n                  l.nums = log.nums//使用量\r\n                  l.times = log.times//领料次数\r\n                }\r\n              }\r\n              equipmentArray.push(l)\r\n            }\r\n            const llArray = []//erp领料单记录\r\n            for (const b of erpBomData) {\r\n              if(b.te004 === materialNo && b.te010 === te010) {\r\n                let sylRate\r\n                let llBlNums\r\n                let llBlReason\r\n                let scBlNums\r\n                let scBlReason\r\n                for (const ot of this.otherArray) {\r\n                  if(ot.materialCode === materialNo ) {\r\n                    for (const t of ot.te010Array) {\r\n                      if(t.te010 === te010) {\r\n                        sylRate = t.sylRate\r\n                        llBlNums = t.llBlNums\r\n                        llBlReason = t.llBlReason\r\n                        scBlNums = t.scBlNums\r\n                        scBlReason = t.scBlReason\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n                llArray.push({\r\n                  te001: b.te001,//领料通知单别\r\n                  te002: b.te002,//领料通知单号\r\n                  te008: b.te008,//仓库\r\n                  me003: b.me003,//最早入库日期\r\n                  te013: b.te013,//领料说明\r\n                  sylRate,//使用量比例(生产批批次品号使用量/工单品号批次使用量)\r\n                  llBlNums,//来料不良\r\n                  llBlReason,\r\n                  scBlNums,//生产不良\r\n                  scBlReason,\r\n                })\r\n              }\r\n            }\r\n            te010Array.push({\r\n              te010,\r\n              erpLlNums: erpLlNums,//领料量\r\n              erpSylNums: erpSylNums,//使用量\r\n              equipmentArray,\r\n              llArray,\r\n            })\r\n            erpSylSum = this.add(erpSylSum,erpSylNums)\r\n          }\r\n          if(scheduleNums) {\r\n            let gdXql = this.multiply(scheduleNums,sjBomNum)\r\n            let lotPlanXql = this.multiply(form.productNums,sjBomNum)//生产记录实际生产数量 计算 本线需求量\r\n            let lotLlXql = this.multiply(form.productNums,sjBomNum)//批次生产量计算 理论使用量\r\n            let loss = this.subtract(lotSums, lotLlXql)\r\n            const erpRateNums = this.multiply(erpSylSum,formRate).toNumber()\r\n            materialLogArray.push({\r\n              materialNo,\r\n              materialName,\r\n              sjBomNum,\r\n              cb010,\r\n              te006,\r\n              gdXql: gdXql.toNumber(),\r\n              te005Sum: te005Sum.toNumber(),\r\n              xfCyRate: this.divide(this.subtract(te005Sum, gdXql), gdXql).toNumber(),\r\n              lotXql: lotPlanXql.toNumber(),\r\n              lotLlXql: lotLlXql.toNumber(),\r\n              lotSums: lotSums.toNumber(),\r\n              loss: loss.toNumber(),\r\n              lotRate: this.divide(loss, lotLlXql).toNumber(),\r\n              erpSylSum,\r\n              erpRateNums,\r\n              diffNums: this.subtract(lotSums, erpRateNums).toNumber(),\r\n              te010Array,\r\n            })\r\n          }\r\n        }\r\n\r\n        this.materialLogArray = materialLogArray\r\n      }\r\n    },\r\n    async buildDiffData() {\r\n      const form = this.form\r\n      const scheduleStandard = this.scheduleStandard\r\n\r\n      const productNums = form.productNums\r\n      const actualHours = this.divide(form.sumMinutes,60)\r\n      const actualDuration = this.diffHours(form.startTime,form.endTime)\r\n      if(actualDuration && scheduleStandard.nums) {\r\n        const actualProductivity = this.divide(productNums,actualDuration).toNumber()\r\n        const actualHoursRate = this.divide(productNums, actualHours).toNumber()\r\n        const diffDataArray = []\r\n\r\n        let standardHours = 0\r\n        let standardPersonNums = 0\r\n        let standardHoursRate = 0\r\n        if(form.opNo === 'GB') {//一阶 取少的\r\n          standardPersonNums = scheduleStandard.nums\r\n          standardHoursRate = scheduleStandard.costHoursRate\r\n        } else {\r\n          standardPersonNums = scheduleStandard.costNums\r\n          standardHoursRate = scheduleStandard.costHoursRate\r\n        }\r\n        standardHours = this.multiply(standardHoursRate,form.productNums).toNumber()\r\n        diffDataArray.push(['标准',scheduleStandard.productivity,standardPersonNums,standardHours,standardHoursRate])\r\n        diffDataArray.push(['实际',actualProductivity,form.sumNums,actualHours,actualHoursRate])\r\n        diffDataArray.push([\r\n          '差异',\r\n          this.subtract(actualProductivity,scheduleStandard.productivity).toNumber(),\r\n          this.subtract(form.sumNums,standardPersonNums).toNumber(),\r\n          this.subtract(actualHours,standardHours).toNumber(),\r\n          this.subtract(actualHoursRate,standardHoursRate).toNumber(),\r\n        ])\r\n        this.diffDataArray = diffDataArray\r\n\r\n        this.diffDataOptions =  {\r\n          title: {\r\n            text: '批次维度数据对比'\r\n          },\r\n          legend: {\r\n            data: ['预估', '实际']\r\n          },\r\n          radar: {\r\n            indicator: [\r\n              { name: '生产产能', max: Math.max(scheduleStandard.productivity, actualProductivity)*1.2 },\r\n              { name: '生产人数', max: Math.max(standardPersonNums, form.sumNums)*1.2 },\r\n              { name: '生产工时', max: Math.max(standardHours, actualHours)*1.2 },\r\n              { name: '工时产出率', max: Math.max(standardHoursRate, actualHoursRate)*1.2 }\r\n            ]\r\n          },\r\n          series: [\r\n            {\r\n              name: '预估 vs 实际',\r\n              type: 'radar',\r\n              data: [\r\n                {\r\n                  value: diffDataArray[0].slice(1),\r\n                  name: '预估'\r\n                },\r\n                {\r\n                  value: diffDataArray[1].slice(1),\r\n                  name: '实际'\r\n                }\r\n              ]\r\n            }\r\n          ]\r\n        }\r\n        // await this.reduceDiffChart()\r\n      }\r\n    },\r\n    async importArchive(archiveId) {\r\n      let archiveRes = await getArchive(archiveId)\r\n      if(archiveRes.code === 200 && archiveRes.data) {\r\n        let archive = archiveRes.data\r\n\r\n        const projectRes = await getProject(archive.projectId)\r\n        const project = projectRes.data\r\n\r\n        project.type = archive.type\r\n\r\n        if(['0','1','3'].includes(archive.type)) {\r\n          project.bomResource = archive.bomResource\r\n          project.bomType = archive.bomType\r\n          project.resourceFinishedGoodsId = archive.resourceFinishedGoodsId\r\n          project.erpPrice = archive.erpPrice\r\n          project.bomArray = archive.bomArray//这里就是字符串\r\n          project.erpCode = archive.erpCode\r\n          if(archive.cubicleArray) {\r\n            const cubicleArray = JSON.parse(archive.cubicleArray)\r\n            project.cubicleArray = cubicleArray\r\n\r\n            const imgs = []\r\n            const videos = []\r\n            for (const item of cubicleArray) {\r\n              for (const second of item.sectionArray) {\r\n                for (const w of second.workTypeArray) {\r\n                  if(w.homeworkImgs) {\r\n                    imgs.push(...w.homeworkImgs.split(','))\r\n                  }\r\n                  if(w.homeworkVideos.length) {\r\n                    videos.push(...w.homeworkVideos)\r\n                  }\r\n                }\r\n              }\r\n            }\r\n            this.imgs = imgs\r\n            this.videos = videos\r\n          } else {\r\n            project.cubicleArray = []\r\n          }\r\n        }\r\n        this.bomData = JSON.parse(archive.bomArray)\r\n        this.project = project\r\n      }\r\n    },\r\n    async buildQcLogs() {\r\n      const form = this.form\r\n      const arr = form.scheduleCode.split('-')\r\n      const inspectionList = await allFinishedInspection({singleCategory: arr[0],workOrderNo:arr[1]})//质检\r\n      for(let item of inspectionList){\r\n        let erpColumn = item.erpColumn;\r\n        if(erpColumn){\r\n          let obj = JSON.parse(erpColumn);\r\n          item.tg030 = obj.tg030;\r\n        }\r\n      }\r\n      this.inspectionList = inspectionList\r\n\r\n      const groupInspectionList = await allGroupByBatchFinishedInspection({singleCategory: arr[0],workOrderNo:arr[1]})\r\n\r\n      const rawData = [\r\n        groupInspectionList.map(i=> i.batchNums - i.samplingNums),\r\n        groupInspectionList.map(i=> i.samplingNums),\r\n      ];\r\n      const totalData = [];\r\n      for (let i = 0; i < rawData[0].length; ++i) {\r\n        let sum = 0;\r\n        for (let j = 0; j < rawData.length; ++j) {\r\n          sum += rawData[j][i];\r\n        }\r\n        totalData.push(sum);\r\n      }\r\n      const grid = {\r\n        left: 100,\r\n        right: 100,\r\n        top: 50,\r\n        bottom: 50\r\n      };\r\n      const series = [\r\n        '未抽样',\r\n        '已抽样',\r\n      ].map((name, sid) => {\r\n        return {\r\n          name,\r\n          type: 'bar',\r\n          stack: 'total',\r\n          barWidth: '60%',\r\n          label: {\r\n            show: true,\r\n            formatter: (params) => Math.round(params.value * 1000) / 10 + '%'\r\n          },\r\n          data: rawData[sid].map((d, did) =>\r\n            totalData[did] <= 0 ? 0 : d / totalData[did]\r\n          )\r\n        }\r\n      })\r\n      this.qcOptions = {\r\n        legend: {\r\n          selectedMode: false\r\n        },\r\n        grid,\r\n        yAxis: {\r\n          type: 'value'\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: groupInspectionList.map(i=> i.batchNo)\r\n        },\r\n        series\r\n      }\r\n    },\r\n    async init(id) {\r\n      this.loading = true\r\n      const res = await getMesLog(id)\r\n      const form = res.data\r\n      if(form.materialArray) {\r\n        this.materialArray = JSON.parse(form.materialArray)\r\n      }\r\n      this.form = form\r\n\r\n      const scheduleCode = form.scheduleCode\r\n      const arr = scheduleCode.split('-')\r\n      const scheduleRes = await getScheduleByCode(arr[0] + \"-\" + arr[1])\r\n      if(scheduleRes.code === 200) {\r\n        const schedule = scheduleRes.data\r\n        this.schedule = schedule\r\n      }\r\n\r\n      const scheduleStandardRes = await getScheduleStandardByCode(form.productNo)\r\n      if(scheduleStandardRes.code === 200) {\r\n        const scheduleStandard = scheduleStandardRes.data\r\n        this.scheduleStandard = scheduleStandard\r\n        if(scheduleStandard) {\r\n          await this.importArchive(scheduleStandard.archiveId)\r\n          await this.buildDiffData()\r\n        }\r\n      }\r\n\r\n      const outList = await allWipContPartialOut({\r\n        areano: form.areaNo,\r\n        eventDate: form.workDate,\r\n        equipmentno: form.equipmentNo,\r\n        scheduleCode: form.scheduleCode,\r\n      })\r\n      let inNums = this.$big(0)\r\n      let goodsNums = this.$big(0)\r\n      let badNums = this.$big(0)\r\n      this.outArray = outList.map(i=> {\r\n        return {\r\n          lotNo: i.lotno,\r\n          opNo: i.opno,\r\n          eventTime: i.eventtime,\r\n          userNo: i.userno,\r\n          areaNo: i.areano,\r\n          inNums: i.inputqty,//产出量\r\n          goodsNums: i.goodqty,//良品量\r\n          badNums: i.scrapqty,//良品量\r\n          equipmentNo: i.equipmentno,\r\n          userName: i.userName,\r\n        }\r\n      })//重命名属性名称,方便理解语义\r\n\r\n      for (const item of this.outArray) {\r\n        inNums = this.add(inNums,item.inNums)\r\n        goodsNums = this.add(goodsNums,item.goodsNums)\r\n        badNums = this.add(badNums,item.badNums)\r\n      }\r\n\r\n      form.inNums = inNums.toNumber()\r\n      form.goodsNums = goodsNums.toNumber()\r\n      form.badNums = badNums.toNumber()\r\n\r\n      const lotLogs = await allWipLotLog({\r\n        areaNo: form.areaNo,\r\n        createTime: form.workDate,\r\n        equipmentNo: form.equipmentNo,\r\n        scheduleCode: form.scheduleCode,\r\n      })\r\n      this.lotLogs = lotLogs\r\n\r\n      const planAreaHourList = await allPlanAreaHours({\r\n        areaNo: form.areaNo,\r\n        equipmentNo: form.equipmentNo,\r\n        workDate: form.workDate,\r\n        sailings: form.sailings,\r\n        scheduleCode: form.scheduleCode,\r\n      })\r\n\r\n      const userArray = []\r\n      const userSet = new Set(planAreaHourList.map(i=> i.userCode))\r\n      let sumMinutes = this.$big(0)\r\n      for (const userCode of userSet) {\r\n        let nickName\r\n        let minutes = this.$big(0)\r\n        const array = []\r\n        for (const h of planAreaHourList) {\r\n          if(userCode === h.userCode) {\r\n            nickName = h.nickName\r\n            array.push(h)\r\n            minutes = this.add(minutes,h.minutes)\r\n          }\r\n        }\r\n        userArray.push({\r\n          userCode,\r\n          nickName,\r\n          minutes,\r\n          array,\r\n        })\r\n        sumMinutes = this.add(sumMinutes,minutes)\r\n      }\r\n      form.minutes = sumMinutes.toNumber()\r\n      this.userArray = userArray\r\n\r\n      await this.buildProductionLog()//这个里面的otherArray,buildMaterialArray用到了\r\n      await this.buildMaterialArray()//这里需要用到 areaList 的数据\r\n      await this.buildQcLogs()\r\n\r\n      this.loading = false\r\n    },\r\n    async submitForm() {\r\n      let form = Object.assign({}, this.form)\r\n\r\n      form.materialArray = JSON.stringify(this.materialArray)\r\n\r\n      if (form.id != null) {\r\n        try {\r\n          this.btnLoading = true\r\n          await updateMesLog(form)\r\n          this.btnLoading = false\r\n          this.msgSuccess(\"修改成功\")\r\n          this.$parent.$parent.open = false\r\n          await this.$parent.$parent.getList()\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n  },\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.cell-wrapper {\r\n  .label {\r\n    width: 80px;\r\n  }\r\n}\r\n\r\n.header-wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  .header-title {\r\n    font-size: 16px;\r\n    font-weight: 700;\r\n  }\r\n\r\n  .header-img {\r\n    background: url(~@/assets/images/production/plan/layout/head.gif) no-repeat center center;\r\n    background-size: 100%;\r\n    height: 10vh;\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* el-tabs */\r\n::v-deep .el-tabs__nav-scroll{\r\n  background-color: #fff;\r\n  padding: 20px 0;\r\n}\r\n::v-deep .el-tabs__nav {\r\n  margin: 0 20px;\r\n  /* 使用rpx没有效果 */\r\n}\r\n\r\n::v-deep .el-tabs__nav-scroll {\r\n  padding: 10px;\r\n}\r\n\r\n::v-deep .el-tabs__content {\r\n  padding-top: 0;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiIA,IAAAA,OAAA,GAAAC,OAAA;AAKA,IAAAC,cAAA,GAAAD,OAAA;AACA,IAAAE,kBAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,QAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAF,sBAAA,CAAAJ,OAAA;AACA,IAAAO,QAAA,GAAAP,OAAA;AACA,IAAAQ,QAAA,GAAAR,OAAA;AACA,IAAAS,iBAAA,GAAAT,OAAA;AACA,IAAAU,GAAA,GAAAN,sBAAA,CAAAJ,OAAA;AACA,IAAAW,mBAAA,GAAAX,OAAA;AACA,IAAAY,mBAAA,GAAAZ,OAAA;AACA,IAAAa,IAAA,GAAAb,OAAA;AACA,IAAAc,WAAA,GAAAV,sBAAA,CAAAJ,OAAA;AACA,IAAAe,IAAA,GAAAf,OAAA;AAEA,IAAAgB,SAAA,GAAAhB,OAAA;AACA,IAAAiB,aAAA,GAAAb,sBAAA,CAAAJ,OAAA;AACA,IAAAkB,KAAA,GAAAd,sBAAA,CAAAJ,OAAA;AACA,IAAAmB,KAAA,GAAAf,sBAAA,CAAAJ,OAAA;AACA,IAAAoB,cAAA,GAAAhB,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAqB,IAAA;EACAC,UAAA;IACAC,mBAAA,EAAAA,sBAAA;IACAC,gBAAA,EAAAA,aAAA;IACAC,mBAAA,EAAAA,aAAA;IACAC,kBAAA,EAAAA,qBAAA;IACAC,gBAAA,EAAAA,mBAAA;IACAC,KAAA,EAAAA,WAAA;IACAC,YAAA,EAAAA,cAAA;IACAC,UAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACAC,QAAA;MACAC,IAAA;MACAC,KAAA;MACAC,UAAA;MACAC,eAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,QAAA;MACAC,OAAA;MACAC,cAAA;MACAC,SAAA;MACAC,OAAA;MACAC,OAAA;MACAC,OAAA;MACAC,aAAA;MACAC,eAAA;MACAC,cAAA;MACAC,SAAA;MACAC,gBAAA;MACAC,gBAAA;MACAC,aAAA;MACAC,QAAA;MACAC,OAAA;MACAC,QAAA;MACAC,UAAA;MACAC,gBAAA;MACAC,kBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;MAAA,WAAAF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;UAAA;YAAA,OAAAF,QAAA,CAAAG,IAAA;QAAA;MAAA,GAAAN,OAAA;IAAA;EACA;EACAO,OAAA;IACAC,SAAA,WAAAA,UAAA;MAAA,IAAAC,KAAA;MAAA,WAAAb,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAW,SAAA;QAAA,WAAAZ,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAU,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAR,IAAA,GAAAQ,SAAA,CAAAP,IAAA;YAAA;cAAAO,SAAA,CAAAP,IAAA;cAAA,OACAI,KAAA,CAAAI,SAAA;YAAA;cAAA,MACAJ,KAAA,CAAAtC,UAAA;gBAAAyC,SAAA,CAAAP,IAAA;gBAAA;cAAA;cAAAO,SAAA,CAAAP,IAAA;cAAA;YAAA;cAAA,MAEAI,KAAA,CAAAtC,UAAA;gBAAAyC,SAAA,CAAAP,IAAA;gBAAA;cAAA;cAAAO,SAAA,CAAAP,IAAA;cAAA,OACAI,KAAA,CAAAK,eAAA;YAAA;cAAAF,SAAA,CAAAP,IAAA;cAAA;YAAA;cAAA,MACAI,KAAA,CAAAtC,UAAA;gBAAAyC,SAAA,CAAAP,IAAA;gBAAA;cAAA;cAAAO,SAAA,CAAAP,IAAA;cAAA,OACAI,KAAA,CAAAM,aAAA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAN,IAAA;UAAA;QAAA,GAAAI,QAAA;MAAA;IAEA;IACAM,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA,CAAAA,OAAA,CAAAC,IAAA;MACA,KAAAC,KAAA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAlD,IAAA;QACAmD,EAAA;QACAC,IAAA;QACAC,QAAA;QACAC,QAAA;QACAC,MAAA;QACAC,UAAA;QACAC,OAAA;QACAC,MAAA;QACAC,SAAA;QACAC,OAAA;MACA;MACA,KAAAC,SAAA;MACA,KAAAnD,OAAA;MACA,KAAAC,OAAA;MACA,KAAAC,OAAA;MACA,KAAAK,gBAAA;MACA,KAAAX,QAAA;MACA,KAAAC,OAAA;MACA,KAAAC,cAAA;MACA,KAAAC,SAAA;MACA,KAAAI,aAAA;MACA,KAAAC,eAAA;MACA,KAAAC,cAAA;MACA,KAAAC,SAAA;MACA,KAAAI,QAAA;MACA,KAAAF,gBAAA;MACA,KAAAC,aAAA;MACA,KAAAE,OAAA;MACA,KAAAC,QAAA;MACA,KAAAC,UAAA;MACA,KAAAC,gBAAA;MACA,KAAAC,kBAAA;IACA;IACAqC,SAAA,WAAAA,UAAAC,SAAA,EAAAC,OAAA;MACA,IAAAD,SAAA,IAAAC,OAAA;QACA,IAAAP,OAAA,QAAAQ,MAAA,CAAAD,OAAA,sBAAAE,IAAA,MAAAD,MAAA,CAAAF,SAAA;QACA,IAAAN,OAAA;UACA,YAAAU,MAAA,CAAAV,OAAA,MAAAW,QAAA;QACA;MACA;IACA;IACAtB,aAAA,WAAAA,cAAA;MAAA,IAAAuB,MAAA;MAAA,WAAA1C,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAwC,SAAA;QAAA,IAAAC,KAAA,EAAAC,QAAA;QAAA,WAAA3C,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAyC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvC,IAAA,GAAAuC,SAAA,CAAAtC,IAAA;YAAA;cAAAsC,SAAA,CAAAtC,IAAA;cAAA,OACAiC,MAAA,CAAAzB,SAAA;YAAA;cACA2B,KAAA,GAAAF,MAAA,CAAAM,KAAA,CAAAJ,KAAA;cAAA,KACAA,KAAA;gBAAAG,SAAA,CAAAtC,IAAA;gBAAA;cAAA;cACAoC,QAAA,GAAAD,KAAA,CAAAI,KAAA,CAAAH,QAAA;cAAA,KACAA,QAAA;gBAAAE,SAAA,CAAAtC,IAAA;gBAAA;cAAA;cAAAsC,SAAA,CAAAtC,IAAA;cAAA,OACAoC,QAAA,CAAAI,IAAA,CAAAP,MAAA,CAAArD,SAAA;YAAA;YAAA;cAAA,OAAA0D,SAAA,CAAArC,IAAA;UAAA;QAAA,GAAAiC,QAAA;MAAA;IAGA;IACAzB,eAAA,WAAAA,gBAAA;MAAA,IAAAgC,MAAA;MAAA,WAAAlD,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAgD,SAAA;QAAA,IAAAC,aAAA,EAAAC,SAAA,EAAAC,aAAA;QAAA,WAAApD,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAkD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhD,IAAA,GAAAgD,SAAA,CAAA/C,IAAA;YAAA;cAAA+C,SAAA,CAAA/C,IAAA;cAAA,OACAyC,MAAA,CAAAjC,SAAA;YAAA;cACAmC,aAAA,GAAAF,MAAA,CAAAF,KAAA,CAAAI,aAAA;cAAA,KACAA,aAAA;gBAAAI,SAAA,CAAA/C,IAAA;gBAAA;cAAA;cACA4C,SAAA,GAAAD,aAAA,CAAAJ,KAAA,CAAAK,SAAA;cAAA,KACAA,SAAA;gBAAAG,SAAA,CAAA/C,IAAA;gBAAA;cAAA;cAAA+C,SAAA,CAAA/C,IAAA;cAAA,OACA4C,SAAA,CAAAJ,IAAA,CAAAC,MAAA,CAAA/D,eAAA;YAAA;cAEAmE,aAAA,GAAAF,aAAA,CAAAJ,KAAA,CAAAM,aAAA;cACA,IAAAA,aAAA,GACA;YAAA;YAAA;cAAA,OAAAE,SAAA,CAAA9C,IAAA;UAAA;QAAA,GAAAyC,QAAA;MAAA;IAEA;IACAM,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MAAA,WAAA1D,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAwD,SAAA;QAAA,IAAAtF,IAAA,EAAAmB,aAAA,EAAAoE,MAAA,EAAAlE,OAAA,EAAAmE,SAAA,EAAAC,KAAA,EAAAC,IAAA,EAAAC,SAAA,EAAAC,MAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAxE,QAAA,EAAAC,UAAA,EAAAC,gBAAA,EAAAC,kBAAA,EAAAsE,UAAA,EAAAC,MAAA,EAAAC,CAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,EAAA;QAAA,WAAAvE,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAqE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnE,IAAA,GAAAmE,SAAA,CAAAlE,IAAA;YAAA;cACApC,IAAA,GAAAqF,MAAA,CAAArF,IAAA;cACAmB,aAAA,GAAAkE,MAAA,CAAAlE,aAAA;cAAAmF,SAAA,CAAAlE,IAAA;cAAA,OACA,IAAAmE,oBAAA,EAAAvG,IAAA,CAAAwG,SAAA;YAAA;cAAAjB,MAAA,GAAAe,SAAA,CAAAG,IAAA;cAAA,MACAlB,MAAA,CAAAnC,IAAA;gBAAAkD,SAAA,CAAAlE,IAAA;gBAAA;cAAA;cACAf,OAAA,GAAAkE,MAAA,CAAA3F,IAAA,CAAA8G,MAAA,WAAAC,CAAA;gBAAA,OAAAA,CAAA,CAAAC,KAAA;cAAA;cAAApB,SAAA,OAAAqB,2BAAA,CAAAjF,OAAA,EACAP,OAAA;cAAAiF,SAAA,CAAAnE,IAAA;cAAAqD,SAAA,CAAAsB,CAAA;YAAA;cAAA,KAAArB,KAAA,GAAAD,SAAA,CAAAuB,CAAA,IAAAC,IAAA;gBAAAV,SAAA,CAAAlE,IAAA;gBAAA;cAAA;cAAAsD,IAAA,GAAAD,KAAA,CAAApF,KAAA;cACAsF,SAAA;gBACAsB,KAAA,EAAAvB,IAAA,CAAAuB,KAAA;gBACAC,KAAA,EAAAxB,IAAA,CAAAwB;cACA;cAAAZ,SAAA,CAAAlE,IAAA;cAAA,OACA,IAAA+E,mBAAA,EAAAxB,SAAA;YAAA;cAAAC,MAAA,GAAAU,SAAA,CAAAG,IAAA;cACA,IAAAb,MAAA,CAAAxC,IAAA,YAAAwC,MAAA,CAAAhG,IAAA;gBACAiG,GAAA,GAAAD,MAAA,CAAAhG,IAAA;gBACA8F,IAAA,CAAA0B,GAAA,GAAAvB,GAAA,CAAAuB,GAAA;gBACA1B,IAAA,CAAA2B,GAAA,GAAAxB,GAAA,CAAAwB,GAAA;gBACA3B,IAAA,CAAA4B,GAAA,GAAAzB,GAAA,CAAAyB,GAAA;cACA;YAAA;cAAAhB,SAAA,CAAAlE,IAAA;cAAA;YAAA;cAAAkE,SAAA,CAAAlE,IAAA;cAAA;YAAA;cAAAkE,SAAA,CAAAnE,IAAA;cAAAmE,SAAA,CAAAiB,EAAA,GAAAjB,SAAA;cAAAd,SAAA,CAAAgC,CAAA,CAAAlB,SAAA,CAAAiB,EAAA;YAAA;cAAAjB,SAAA,CAAAnE,IAAA;cAAAqD,SAAA,CAAAiC,CAAA;cAAA,OAAAnB,SAAA,CAAAoB,MAAA;YAAA;cAEArC,MAAA,CAAAhE,OAAA,GAAAA,OAAA;YAAA;cAAAiF,SAAA,CAAAlE,IAAA;cAAA,OAGA,IAAAuF,0BAAA,EAAA3H,IAAA,CAAAmD,EAAA;YAAA;cAAA2C,GAAA,GAAAQ,SAAA,CAAAG,IAAA;cACA,IAAAX,GAAA;gBACAxE,QAAA,GAAAwE,GAAA,CAAAxE,QAAA;gBACAC,UAAA,GAAAuE,GAAA,CAAAvE,UAAA;gBACAC,gBAAA,GAAAsE,GAAA,CAAAtE,gBAAA;gBACAC,kBAAA,GAAAqE,GAAA,CAAArE,kBAAA;gBACAmG,OAAA,CAAAC,GAAA,CAAArG,gBAAA;gBACAoG,OAAA,CAAAC,GAAA,CAAApG,kBAAA;gBACA,IAAAH,QAAA;kBACA+D,MAAA,CAAA/D,QAAA,GAAAA,QAAA;kBAAAyE,UAAA,OAAAc,2BAAA,CAAAjF,OAAA,EACAN,QAAA;kBAAA;oBAAA,KAAAyE,UAAA,CAAAe,CAAA,MAAAd,MAAA,GAAAD,UAAA,CAAAgB,CAAA,IAAAC,IAAA;sBAAAf,CAAA,GAAAD,MAAA,CAAA3F,KAAA;sBACA,KAAAc,aAAA,CAAA2G,GAAA,WAAAnB,CAAA;wBAAA,OAAAA,CAAA,CAAAoB,YAAA;sBAAA,GAAAC,QAAA,CAAA/B,CAAA,CAAA8B,YAAA;wBACA5G,aAAA,CAAA8G,IAAA;0BACAF,YAAA,EAAA9B,CAAA,CAAA8B,YAAA;0BACAG,MAAA;wBACA;sBACA;oBACA;kBAAA,SAAAC,GAAA;oBAAApC,UAAA,CAAAyB,CAAA,CAAAW,GAAA;kBAAA;oBAAApC,UAAA,CAAA0B,CAAA;kBAAA;gBACA;gBACA,IAAAlG,UAAA;kBACA8D,MAAA,CAAA9D,UAAA,GAAAA,UAAA;kBAAA2E,UAAA,OAAAW,2BAAA,CAAAjF,OAAA,EACAL,UAAA;kBAAA;oBAAA,KAAA2E,UAAA,CAAAY,CAAA,MAAAX,MAAA,GAAAD,UAAA,CAAAa,CAAA,IAAAC,IAAA;sBAAAf,EAAA,GAAAE,MAAA,CAAA9F,KAAA;sBACA,KAAAc,aAAA,CAAA2G,GAAA,WAAAnB,CAAA;wBAAA,OAAAA,CAAA,CAAAoB,YAAA;sBAAA,GAAAC,QAAA,CAAA/B,EAAA,CAAA8B,YAAA;wBACA5G,aAAA,CAAA8G,IAAA;0BACAF,YAAA,EAAA9B,EAAA,CAAA8B,YAAA;0BACAG,MAAA;wBACA;sBACA;oBACA;kBAAA,SAAAC,GAAA;oBAAAjC,UAAA,CAAAsB,CAAA,CAAAW,GAAA;kBAAA;oBAAAjC,UAAA,CAAAuB,CAAA;kBAAA;gBACA;gBACA,IAAAjG,gBAAA;kBACA6D,MAAA,CAAA7D,gBAAA,GAAAA,gBAAA;gBACA;gBACA,IAAAC,kBAAA;kBACA4D,MAAA,CAAA5D,kBAAA,GAAAA,kBAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA6E,SAAA,CAAAjE,IAAA;UAAA;QAAA,GAAAiD,QAAA;MAAA;IACA;IACA8C,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MAAA,WAAA1G,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAwG,SAAA;QAAA,IAAAtI,IAAA,EAAAoB,QAAA,EAAAmH,QAAA,EAAAC,YAAA,EAAAC,eAAA,EAAAC,GAAA,EAAAC,UAAA,EAAAC,YAAA,EAAAC,aAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAlB,GAAA,EAAA5G,gBAAA,EAAA+H,UAAA,EAAAC,MAAA,EAAAC,UAAA,EAAAC,QAAA,EAAAC,QAAA,EAAAC,KAAA,EAAAC,YAAA,EAAAC,KAAA,EAAAC,QAAA,EAAAC,OAAA,EAAAC,SAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,CAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,IAAA,EAAAC,UAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,SAAA,EAAAC,UAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,EAAA,EAAAC,WAAA,EAAAC,OAAA,EAAAC,CAAA,EAAAC,YAAA,EAAAC,WAAA,EAAAC,OAAA,EAAAC,KAAA,EAAAC,cAAA,EAAAC,WAAA,EAAAC,OAAA,EAAAC,WAAA,EAAAC,CAAA,EAAAC,WAAA,EAAAC,OAAA,EAAAC,KAAA,EAAAC,OAAA,EAAAC,WAAA,EAAAC,OAAA,EAAAC,EAAA,EAAAC,OAAA,EAAAC,QAAA,EAAAC,UAAA,EAAAC,QAAA,EAAAC,UAAA,EAAAC,WAAA,EAAAC,OAAA,EAAAC,GAAA,EAAAC,WAAA,EAAAC,OAAA,EAAAC,EAAA,EAAAC,KAAA,EAAAC,UAAA,EAAAC,QAAA,EAAAC,IAAA,EAAAC,WAAA;QAAA,WAAA/K,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA6K,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3K,IAAA,GAAA2K,SAAA,CAAA1K,IAAA;YAAA;cACApC,IAAA,GAAAqI,MAAA,CAAArI,IAAA;cACAoB,QAAA,GAAAiH,MAAA,CAAAjH,QAAA;cACAmH,QAAA,GAAAF,MAAA,CAAAlE,MAAA,CAAAnE,IAAA,CAAA+M,WAAA,EAAA3L,QAAA,CAAA4L,QAAA;cAAA,KACAzE,QAAA;gBAAAuE,SAAA,CAAA1K,IAAA;gBAAA;cAAA;cACAoG,YAAA,GAAAxI,IAAA,CAAAwI,YAAA;cAAAsE,SAAA,CAAA1K,IAAA;cAAA,OACA,IAAA6K,yCAAA;gBAAAzE,YAAA,EAAAA,YAAA;gBAAAnF,QAAA,EAAArD,IAAA,CAAAqD;cAAA;YAAA;cAAAoF,eAAA,GAAAqE,SAAA,CAAArG,IAAA;cAAA;cACA4B,MAAA,CAAAI,eAAA,GAAAA,eAAA;cACAC,GAAA,GAAAF,YAAA,CAAA0E,KAAA;cAAAJ,SAAA,CAAA1K,IAAA;cAAA,OACA,IAAA+K,wBAAA;gBAAAC,WAAA,EAAA1E,GAAA;gBAAA2E,eAAA,EAAA3E,GAAA;cAAA;YAAA;cAAAC,UAAA,GAAAmE,SAAA,CAAArG,IAAA;cAEAmC,YAAA,GAAAxH,QAAA,CAAA4L,QAAA;cACAnE,aAAA,OAAAyE,GAAA;cAAAxE,UAAA,OAAAjC,2BAAA,CAAAjF,OAAA,EACA6G,eAAA;cAAA;gBAAA,KAAAK,UAAA,CAAAhC,CAAA,MAAAiC,MAAA,GAAAD,UAAA,CAAA/B,CAAA,IAAAC,IAAA;kBAAAa,GAAA,GAAAkB,MAAA,CAAA1I,KAAA;kBACAwI,aAAA,CAAA0E,GAAA,CAAA1F,GAAA,CAAAqB,UAAA;gBACA;cAAA,SAAAf,GAAA;gBAAAW,UAAA,CAAAtB,CAAA,CAAAW,GAAA;cAAA;gBAAAW,UAAA,CAAArB,CAAA;cAAA;cAEAxG,gBAAA;cAAA+H,UAAA,OAAAnC,2BAAA,CAAAjF,OAAA,EACAiH,aAAA;cAAA;gBAAA,KAAAG,UAAA,CAAAlC,CAAA,MAAAmC,MAAA,GAAAD,UAAA,CAAAjC,CAAA,IAAAC,IAAA;kBAAAkC,UAAA,GAAAD,MAAA,CAAA5I,KAAA;kBACA8I,QAAA,OAAAmE,GAAA;kBACAlE,QAAA;kBACAC,KAAA;kBACAC,YAAA;kBACAC,KAAA;kBACAC,QAAA,GAAAnB,MAAA,CAAAmF,IAAA;kBACA/D,OAAA,GAAApB,MAAA,CAAAmF,IAAA;kBACA9D,SAAA,GAAArB,MAAA,CAAAmF,IAAA;kBAAA7D,UAAA,OAAA9C,2BAAA,CAAAjF,OAAA,EACA+G,UAAA;kBAAA;oBAAA,KAAAgB,UAAA,CAAA7C,CAAA,MAAA8C,MAAA,GAAAD,UAAA,CAAA5C,CAAA,IAAAC,IAAA;sBAAA6C,CAAA,GAAAD,MAAA,CAAAvJ,KAAA;sBACA,IAAAwJ,CAAA,CAAA4D,KAAA,KAAAvE,UAAA;wBACAE,QAAA,GAAAsE,MAAA,CAAA7D,CAAA,CAAAT,QAAA;wBACAC,KAAA,GAAAqE,MAAA,CAAA7D,CAAA,CAAAR,KAAA;wBACAC,YAAA,GAAAO,CAAA,CAAA8D,KAAA;wBACApE,KAAA,GAAAM,CAAA,CAAAN,KAAA;wBAEAC,QAAA,GAAAnB,MAAA,CAAAkF,GAAA,CAAA/D,QAAA,EAAAK,CAAA,CAAA+D,KAAA;wBACAzE,QAAA,CAAAoE,GAAA,CAAA1D,CAAA,CAAAO,KAAA;sBACA;oBACA;kBAAA,SAAAjC,GAAA;oBAAAwB,UAAA,CAAAnC,CAAA,CAAAW,GAAA;kBAAA;oBAAAwB,UAAA,CAAAlC,CAAA;kBAAA;kBAAAqC,UAAA,OAAAjD,2BAAA,CAAAjF,OAAA,EACA6G,eAAA;kBAAA;oBAAA,KAAAqB,UAAA,CAAAhD,CAAA,MAAAiD,MAAA,GAAAD,UAAA,CAAA/C,CAAA,IAAAC,IAAA;sBAAAa,IAAA,GAAAkC,MAAA,CAAA1J,KAAA;sBACA,IAAA6I,UAAA,KAAArB,IAAA,CAAAqB,UAAA;wBACAO,OAAA,GAAApB,MAAA,CAAAkF,GAAA,CAAA9D,OAAA,EAAA5B,IAAA,CAAAgG,IAAA;sBACA;oBACA;kBAAA,SAAA1F,GAAA;oBAAA2B,UAAA,CAAAtC,CAAA,CAAAW,GAAA;kBAAA;oBAAA2B,UAAA,CAAArC,CAAA;kBAAA;kBACAwC,UAAA;kBAAAC,UAAA,OAAArD,2BAAA,CAAAjF,OAAA,EACAuH,QAAA;kBAAA;oBAAA,KAAAe,UAAA,CAAApD,CAAA,MAAAqD,MAAA,GAAAD,UAAA,CAAAnD,CAAA,IAAAC,IAAA;sBAAAoD,KAAA,GAAAD,MAAA,CAAA9J,KAAA;sBACAgK,SAAA;sBACAC,UAAA;sBAAAC,UAAA,OAAA1D,2BAAA,CAAAjF,OAAA,EACAyG,MAAA,CAAA9G,UAAA;sBAAA;wBAAA,KAAAgJ,UAAA,CAAAzD,CAAA,MAAA0D,MAAA,GAAAD,UAAA,CAAAxD,CAAA,IAAAC,IAAA;0BAAAyD,EAAA,GAAAD,MAAA,CAAAnK,KAAA;0BACA,IAAAoK,EAAA,CAAA1C,YAAA,KAAAmB,UAAA;4BAAAwB,WAAA,OAAA7D,2BAAA,CAAAjF,OAAA,EACA6I,EAAA,CAAAR,UAAA;4BAAA;8BAAA,KAAAS,WAAA,CAAA5D,CAAA,MAAA6D,OAAA,GAAAD,WAAA,CAAA3D,CAAA,IAAAC,IAAA;gCAAA4D,CAAA,GAAAD,OAAA,CAAAtK,KAAA;gCACA,IAAAuK,CAAA,CAAAR,KAAA,KAAAA,KAAA;kCACA,IAAAQ,CAAA,CAAAkD,SAAA;oCACAzD,SAAA,GAAAqD,MAAA,CAAA9C,CAAA,CAAAkD,SAAA;oCACAxD,UAAA,GAAAoD,MAAA,CAAA9C,CAAA,CAAAkD,SAAA;kCACA;gCACA;8BACA;4BAAA,SAAA3F,GAAA;8BAAAuC,WAAA,CAAAlD,CAAA,CAAAW,GAAA;4BAAA;8BAAAuC,WAAA,CAAAjD,CAAA;4BAAA;0BACA;wBACA;sBAAA,SAAAU,GAAA;wBAAAoC,UAAA,CAAA/C,CAAA,CAAAW,GAAA;sBAAA;wBAAAoC,UAAA,CAAA9C,CAAA;sBAAA;sBACAoD,YAAA,OAAAyC,GAAA;sBAAAxC,WAAA,OAAAjE,2BAAA,CAAAjF,OAAA,EACA6G,eAAA;sBAAA;wBAAA,KAAAqC,WAAA,CAAAhE,CAAA,MAAAiE,OAAA,GAAAD,WAAA,CAAA/D,CAAA,IAAAC,IAAA;0BAAAa,KAAA,GAAAkD,OAAA,CAAA1K,KAAA;0BACA,IAAA6I,UAAA,KAAArB,KAAA,CAAAqB,UAAA,IAAAkB,KAAA,KAAAvC,KAAA,CAAAkG,aAAA;4BACAlD,YAAA,CAAA0C,GAAA,CAAA1F,KAAA,CAAAuD,WAAA;0BACA;wBACA;sBAAA,SAAAjD,GAAA;wBAAA2C,WAAA,CAAAtD,CAAA,CAAAW,GAAA;sBAAA;wBAAA2C,WAAA,CAAArD,CAAA;sBAAA;sBACAwD,cAAA;sBAAAC,WAAA,OAAArE,2BAAA,CAAAjF,OAAA,EACAiJ,YAAA;sBAAA;wBAAA,KAAAK,WAAA,CAAApE,CAAA,MAAAqE,OAAA,GAAAD,WAAA,CAAAnE,CAAA,IAAAC,IAAA;0BAAAoE,WAAA,GAAAD,OAAA,CAAA9K,KAAA;0BACAgL,CAAA;4BACAD,WAAA,EAAAA;0BACA;0BAAAE,WAAA,OAAAzE,2BAAA,CAAAjF,OAAA,EACA6G,eAAA;0BAAA;4BAAA,KAAA6C,WAAA,CAAAxE,CAAA,MAAAyE,OAAA,GAAAD,WAAA,CAAAvE,CAAA,IAAAC,IAAA;8BAAAa,KAAA,GAAA0D,OAAA,CAAAlL,KAAA;8BACA,IAAA6I,UAAA,KAAArB,KAAA,CAAAqB,UAAA,IAAAkB,KAAA,KAAAvC,KAAA,CAAAkG,aAAA,IAAA3C,WAAA,KAAAvD,KAAA,CAAAuD,WAAA;gCACAC,CAAA,CAAAwC,IAAA,GAAAhG,KAAA,CAAAgG,IAAA;gCACAxC,CAAA,CAAA2C,KAAA,GAAAnG,KAAA,CAAAmG,KAAA;8BACA;4BACA;0BAAA,SAAA7F,GAAA;4BAAAmD,WAAA,CAAA9D,CAAA,CAAAW,GAAA;0BAAA;4BAAAmD,WAAA,CAAA7D,CAAA;0BAAA;0BACAwD,cAAA,CAAAhD,IAAA,CAAAoD,CAAA;wBACA;sBAAA,SAAAlD,GAAA;wBAAA+C,WAAA,CAAA1D,CAAA,CAAAW,GAAA;sBAAA;wBAAA+C,WAAA,CAAAzD,CAAA;sBAAA;sBACAgE,OAAA;sBAAAC,WAAA,OAAA7E,2BAAA,CAAAjF,OAAA,EACA+G,UAAA;sBAAA;wBAAA,KAAA+C,WAAA,CAAA5E,CAAA,MAAA6E,OAAA,GAAAD,WAAA,CAAA3E,CAAA,IAAAC,IAAA;0BAAA6C,EAAA,GAAA8B,OAAA,CAAAtL,KAAA;0BACA,IAAAwJ,EAAA,CAAA4D,KAAA,KAAAvE,UAAA,IAAAW,EAAA,CAAAO,KAAA,KAAAA,KAAA;4BACAyB,OAAA;4BACAC,QAAA;4BACAC,UAAA;4BACAC,QAAA;4BACAC,UAAA;4BAAAC,WAAA,OAAArF,2BAAA,CAAAjF,OAAA,EACAyG,MAAA,CAAA9G,UAAA;4BAAA;8BAAA,KAAA2K,WAAA,CAAApF,CAAA,MAAAqF,OAAA,GAAAD,WAAA,CAAAnF,CAAA,IAAAC,IAAA;gCAAAyD,GAAA,GAAA0B,OAAA,CAAA9L,KAAA;gCACA,IAAAoK,GAAA,CAAA1C,YAAA,KAAAmB,UAAA;kCAAAmD,WAAA,OAAAxF,2BAAA,CAAAjF,OAAA,EACA6I,GAAA,CAAAR,UAAA;kCAAA;oCAAA,KAAAoC,WAAA,CAAAvF,CAAA,MAAAwF,OAAA,GAAAD,WAAA,CAAAtF,CAAA,IAAAC,IAAA;sCAAA4D,EAAA,GAAA0B,OAAA,CAAAjM,KAAA;sCACA,IAAAuK,EAAA,CAAAR,KAAA,KAAAA,KAAA;wCACAyB,OAAA,GAAAjB,EAAA,CAAAiB,OAAA;wCACAC,QAAA,GAAAlB,EAAA,CAAAkB,QAAA;wCACAC,UAAA,GAAAnB,EAAA,CAAAmB,UAAA;wCACAC,QAAA,GAAApB,EAAA,CAAAoB,QAAA;wCACAC,UAAA,GAAArB,EAAA,CAAAqB,UAAA;sCACA;oCACA;kCAAA,SAAA9D,GAAA;oCAAAkE,WAAA,CAAA7E,CAAA,CAAAW,GAAA;kCAAA;oCAAAkE,WAAA,CAAA5E,CAAA;kCAAA;gCACA;8BACA;4BAAA,SAAAU,GAAA;8BAAA+D,WAAA,CAAA1E,CAAA,CAAAW,GAAA;4BAAA;8BAAA+D,WAAA,CAAAzE,CAAA;4BAAA;4BACAgE,OAAA,CAAAxD,IAAA;8BACAgG,KAAA,EAAApE,EAAA,CAAAoE,KAAA;8BAAA;8BACAC,KAAA,EAAArE,EAAA,CAAAqE,KAAA;8BAAA;8BACAC,KAAA,EAAAtE,EAAA,CAAAsE,KAAA;8BAAA;8BACAC,KAAA,EAAAvE,EAAA,CAAAuE,KAAA;8BAAA;8BACAC,KAAA,EAAAxE,EAAA,CAAAwE,KAAA;8BAAA;8BACAxC,OAAA,EAAAA,OAAA;8BAAA;8BACAC,QAAA,EAAAA,QAAA;8BAAA;8BACAC,UAAA,EAAAA,UAAA;8BACAC,QAAA,EAAAA,QAAA;8BAAA;8BACAC,UAAA,EAAAA;4BACA;0BACA;wBACA;sBAAA,SAAA9D,GAAA;wBAAAuD,WAAA,CAAAlE,CAAA,CAAAW,GAAA;sBAAA;wBAAAuD,WAAA,CAAAjE,CAAA;sBAAA;sBACAwC,UAAA,CAAAhC,IAAA;wBACAmC,KAAA,EAAAA,KAAA;wBACAC,SAAA,EAAAA,SAAA;wBAAA;wBACAC,UAAA,EAAAA,UAAA;wBAAA;wBACAW,cAAA,EAAAA,cAAA;wBACAQ,OAAA,EAAAA;sBACA;sBACA/B,SAAA,GAAArB,MAAA,CAAAkF,GAAA,CAAA7D,SAAA,EAAAY,UAAA;oBACA;kBAAA,SAAAnC,GAAA;oBAAA+B,UAAA,CAAA1C,CAAA,CAAAW,GAAA;kBAAA;oBAAA+B,UAAA,CAAAzC,CAAA;kBAAA;kBACA,IAAAmB,YAAA;oBACA4D,KAAA,GAAAnE,MAAA,CAAAiG,QAAA,CAAA1F,YAAA,EAAAQ,QAAA;oBACAqD,UAAA,GAAApE,MAAA,CAAAiG,QAAA,CAAAtO,IAAA,CAAA+M,WAAA,EAAA3D,QAAA;oBACAsD,QAAA,GAAArE,MAAA,CAAAiG,QAAA,CAAAtO,IAAA,CAAA+M,WAAA,EAAA3D,QAAA;oBACAuD,IAAA,GAAAtE,MAAA,CAAAkG,QAAA,CAAA9E,OAAA,EAAAiD,QAAA;oBACAE,WAAA,GAAAvE,MAAA,CAAAiG,QAAA,CAAA5E,SAAA,EAAAnB,QAAA,EAAAnE,QAAA;oBACAnD,gBAAA,CAAAgH,IAAA;sBACAiB,UAAA,EAAAA,UAAA;sBACAI,YAAA,EAAAA,YAAA;sBACAF,QAAA,EAAAA,QAAA;sBACAC,KAAA,EAAAA,KAAA;sBACAE,KAAA,EAAAA,KAAA;sBACAiD,KAAA,EAAAA,KAAA,CAAApI,QAAA;sBACAoF,QAAA,EAAAA,QAAA,CAAApF,QAAA;sBACAoK,QAAA,EAAAnG,MAAA,CAAAlE,MAAA,CAAAkE,MAAA,CAAAkG,QAAA,CAAA/E,QAAA,EAAAgD,KAAA,GAAAA,KAAA,EAAApI,QAAA;sBACAqK,MAAA,EAAAhC,UAAA,CAAArI,QAAA;sBACAsI,QAAA,EAAAA,QAAA,CAAAtI,QAAA;sBACAqF,OAAA,EAAAA,OAAA,CAAArF,QAAA;sBACAuI,IAAA,EAAAA,IAAA,CAAAvI,QAAA;sBACAsK,OAAA,EAAArG,MAAA,CAAAlE,MAAA,CAAAwI,IAAA,EAAAD,QAAA,EAAAtI,QAAA;sBACAsF,SAAA,EAAAA,SAAA;sBACAkD,WAAA,EAAAA,WAAA;sBACA+B,QAAA,EAAAtG,MAAA,CAAAkG,QAAA,CAAA9E,OAAA,EAAAmD,WAAA,EAAAxI,QAAA;sBACA6F,UAAA,EAAAA;oBACA;kBACA;gBACA;cAAA,SAAA9B,GAAA;gBAAAa,UAAA,CAAAxB,CAAA,CAAAW,GAAA;cAAA;gBAAAa,UAAA,CAAAvB,CAAA;cAAA;cAEAY,MAAA,CAAApH,gBAAA,GAAAA,gBAAA;YAAA;YAAA;cAAA,OAAA6L,SAAA,CAAAzK,IAAA;UAAA;QAAA,GAAAiG,QAAA;MAAA;IAEA;IACAsG,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MAAA,WAAAlN,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAgN,SAAA;QAAA,IAAA9O,IAAA,EAAAkB,gBAAA,EAAA6L,WAAA,EAAAgC,WAAA,EAAAC,cAAA,EAAAC,kBAAA,EAAAC,eAAA,EAAArO,aAAA,EAAAsO,aAAA,EAAAC,kBAAA,EAAAC,iBAAA;QAAA,WAAAxN,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAsN,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApN,IAAA,GAAAoN,SAAA,CAAAnN,IAAA;YAAA;cACApC,IAAA,GAAA6O,MAAA,CAAA7O,IAAA;cACAkB,gBAAA,GAAA2N,MAAA,CAAA3N,gBAAA;cAEA6L,WAAA,GAAA/M,IAAA,CAAA+M,WAAA;cACAgC,WAAA,GAAAF,MAAA,CAAA1K,MAAA,CAAAnE,IAAA,CAAAwP,UAAA;cACAR,cAAA,GAAAH,MAAA,CAAA/K,SAAA,CAAA9D,IAAA,CAAA+D,SAAA,EAAA/D,IAAA,CAAAgE,OAAA;cACA,IAAAgL,cAAA,IAAA9N,gBAAA,CAAA2M,IAAA;gBACAoB,kBAAA,GAAAJ,MAAA,CAAA1K,MAAA,CAAA4I,WAAA,EAAAiC,cAAA,EAAA5K,QAAA;gBACA8K,eAAA,GAAAL,MAAA,CAAA1K,MAAA,CAAA4I,WAAA,EAAAgC,WAAA,EAAA3K,QAAA;gBACAvD,aAAA;gBAEAsO,aAAA;gBACAC,kBAAA;gBACAC,iBAAA;gBACA,IAAArP,IAAA,CAAAyP,IAAA;kBAAA;kBACAL,kBAAA,GAAAlO,gBAAA,CAAA2M,IAAA;kBACAwB,iBAAA,GAAAnO,gBAAA,CAAAwO,aAAA;gBACA;kBACAN,kBAAA,GAAAlO,gBAAA,CAAAyO,QAAA;kBACAN,iBAAA,GAAAnO,gBAAA,CAAAwO,aAAA;gBACA;gBACAP,aAAA,GAAAN,MAAA,CAAAP,QAAA,CAAAe,iBAAA,EAAArP,IAAA,CAAA+M,WAAA,EAAA3I,QAAA;gBACAvD,aAAA,CAAAoH,IAAA,QAAA/G,gBAAA,CAAA0O,YAAA,EAAAR,kBAAA,EAAAD,aAAA,EAAAE,iBAAA;gBACAxO,aAAA,CAAAoH,IAAA,QAAAgH,kBAAA,EAAAjP,IAAA,CAAA6P,OAAA,EAAAd,WAAA,EAAAG,eAAA;gBACArO,aAAA,CAAAoH,IAAA,EACA,MACA4G,MAAA,CAAAN,QAAA,CAAAU,kBAAA,EAAA/N,gBAAA,CAAA0O,YAAA,EAAAxL,QAAA,IACAyK,MAAA,CAAAN,QAAA,CAAAvO,IAAA,CAAA6P,OAAA,EAAAT,kBAAA,EAAAhL,QAAA,IACAyK,MAAA,CAAAN,QAAA,CAAAQ,WAAA,EAAAI,aAAA,EAAA/K,QAAA,IACAyK,MAAA,CAAAN,QAAA,CAAAW,eAAA,EAAAG,iBAAA,EAAAjL,QAAA,GACA;gBACAyK,MAAA,CAAAhO,aAAA,GAAAA,aAAA;gBAEAgO,MAAA,CAAA/N,eAAA;kBACAgP,KAAA;oBACAC,IAAA;kBACA;kBACAC,MAAA;oBACApQ,IAAA;kBACA;kBACAqQ,KAAA;oBACAC,SAAA,GACA;sBAAAhR,IAAA;sBAAAkI,GAAA,EAAA+I,IAAA,CAAA/I,GAAA,CAAAlG,gBAAA,CAAA0O,YAAA,EAAAX,kBAAA;oBAAA,GACA;sBAAA/P,IAAA;sBAAAkI,GAAA,EAAA+I,IAAA,CAAA/I,GAAA,CAAAgI,kBAAA,EAAApP,IAAA,CAAA6P,OAAA;oBAAA,GACA;sBAAA3Q,IAAA;sBAAAkI,GAAA,EAAA+I,IAAA,CAAA/I,GAAA,CAAA+H,aAAA,EAAAJ,WAAA;oBAAA,GACA;sBAAA7P,IAAA;sBAAAkI,GAAA,EAAA+I,IAAA,CAAA/I,GAAA,CAAAiI,iBAAA,EAAAH,eAAA;oBAAA;kBAEA;kBACAkB,MAAA,GACA;oBACAlR,IAAA;oBACAmR,IAAA;oBACAzQ,IAAA,GACA;sBACAS,KAAA,EAAAQ,aAAA,IAAAyP,KAAA;sBACApR,IAAA;oBACA,GACA;sBACAmB,KAAA,EAAAQ,aAAA,IAAAyP,KAAA;sBACApR,IAAA;oBACA;kBAEA;gBAEA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAqQ,SAAA,CAAAlN,IAAA;UAAA;QAAA,GAAAyM,QAAA;MAAA;IACA;IACAyB,aAAA,WAAAA,cAAAC,SAAA;MAAA,IAAAC,MAAA;MAAA,WAAA9O,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA4O,SAAA;QAAA,IAAAC,UAAA,EAAAC,OAAA,EAAAC,UAAA,EAAAnQ,OAAA,EAAAoQ,YAAA,EAAAC,IAAA,EAAAC,MAAA,EAAAC,WAAA,EAAAC,OAAA,EAAAxL,IAAA,EAAAyL,WAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,WAAA,EAAAC,OAAA,EAAAC,CAAA;QAAA,WAAA3P,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAyP,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvP,IAAA,GAAAuP,SAAA,CAAAtP,IAAA;YAAA;cAAAsP,SAAA,CAAAtP,IAAA;cAAA,OACA,IAAAuP,mBAAA,EAAAnB,SAAA;YAAA;cAAAG,UAAA,GAAAe,SAAA,CAAAjL,IAAA;cAAA,MACAkK,UAAA,CAAAvN,IAAA,YAAAuN,UAAA,CAAA/Q,IAAA;gBAAA8R,SAAA,CAAAtP,IAAA;gBAAA;cAAA;cACAwO,OAAA,GAAAD,UAAA,CAAA/Q,IAAA;cAAA8R,SAAA,CAAAtP,IAAA;cAAA,OAEA,IAAAwP,mBAAA,EAAAhB,OAAA,CAAAiB,SAAA;YAAA;cAAAhB,UAAA,GAAAa,SAAA,CAAAjL,IAAA;cACA/F,OAAA,GAAAmQ,UAAA,CAAAjR,IAAA;cAEAc,OAAA,CAAA2P,IAAA,GAAAO,OAAA,CAAAP,IAAA;cAEA,oBAAArI,QAAA,CAAA4I,OAAA,CAAAP,IAAA;gBACA3P,OAAA,CAAAoR,WAAA,GAAAlB,OAAA,CAAAkB,WAAA;gBACApR,OAAA,CAAAqR,OAAA,GAAAnB,OAAA,CAAAmB,OAAA;gBACArR,OAAA,CAAAsR,uBAAA,GAAApB,OAAA,CAAAoB,uBAAA;gBACAtR,OAAA,CAAAuR,QAAA,GAAArB,OAAA,CAAAqB,QAAA;gBACAvR,OAAA,CAAAwR,QAAA,GAAAtB,OAAA,CAAAsB,QAAA;gBACAxR,OAAA,CAAAyR,OAAA,GAAAvB,OAAA,CAAAuB,OAAA;gBACA,IAAAvB,OAAA,CAAAE,YAAA;kBACAA,YAAA,GAAAsB,IAAA,CAAAC,KAAA,CAAAzB,OAAA,CAAAE,YAAA;kBACApQ,OAAA,CAAAoQ,YAAA,GAAAA,YAAA;kBAEAC,IAAA;kBACAC,MAAA;kBAAAC,WAAA,OAAApK,2BAAA,CAAAjF,OAAA,EACAkP,YAAA;kBAAA;oBAAA,KAAAG,WAAA,CAAAnK,CAAA,MAAAoK,OAAA,GAAAD,WAAA,CAAAlK,CAAA,IAAAC,IAAA;sBAAAtB,IAAA,GAAAwL,OAAA,CAAA7Q,KAAA;sBAAA8Q,WAAA,OAAAtK,2BAAA,CAAAjF,OAAA,EACA8D,IAAA,CAAA4M,YAAA;sBAAA;wBAAA,KAAAnB,WAAA,CAAArK,CAAA,MAAAsK,OAAA,GAAAD,WAAA,CAAApK,CAAA,IAAAC,IAAA;0BAAAqK,MAAA,GAAAD,OAAA,CAAA/Q,KAAA;0BAAAiR,WAAA,OAAAzK,2BAAA,CAAAjF,OAAA,EACAyP,MAAA,CAAAkB,aAAA;0BAAA;4BAAA,KAAAjB,WAAA,CAAAxK,CAAA,MAAAyK,OAAA,GAAAD,WAAA,CAAAvK,CAAA,IAAAC,IAAA;8BAAAwK,CAAA,GAAAD,OAAA,CAAAlR,KAAA;8BACA,IAAAmR,CAAA,CAAAgB,YAAA;gCACAzB,IAAA,CAAA9I,IAAA,CAAAwK,KAAA,CAAA1B,IAAA,MAAA2B,mBAAA,CAAA9Q,OAAA,EAAA4P,CAAA,CAAAgB,YAAA,CAAAtF,KAAA;8BACA;8BACA,IAAAsE,CAAA,CAAAmB,cAAA,CAAAC,MAAA;gCACA5B,MAAA,CAAA/I,IAAA,CAAAwK,KAAA,CAAAzB,MAAA,MAAA0B,mBAAA,CAAA9Q,OAAA,EAAA4P,CAAA,CAAAmB,cAAA;8BACA;4BACA;0BAAA,SAAAxK,GAAA;4BAAAmJ,WAAA,CAAA9J,CAAA,CAAAW,GAAA;0BAAA;4BAAAmJ,WAAA,CAAA7J,CAAA;0BAAA;wBACA;sBAAA,SAAAU,GAAA;wBAAAgJ,WAAA,CAAA3J,CAAA,CAAAW,GAAA;sBAAA;wBAAAgJ,WAAA,CAAA1J,CAAA;sBAAA;oBACA;kBAAA,SAAAU,GAAA;oBAAA8I,WAAA,CAAAzJ,CAAA,CAAAW,GAAA;kBAAA;oBAAA8I,WAAA,CAAAxJ,CAAA;kBAAA;kBACAgJ,MAAA,CAAAM,IAAA,GAAAA,IAAA;kBACAN,MAAA,CAAAO,MAAA,GAAAA,MAAA;gBACA;kBACAtQ,OAAA,CAAAoQ,YAAA;gBACA;cACA;cACAL,MAAA,CAAA9P,OAAA,GAAAyR,IAAA,CAAAC,KAAA,CAAAzB,OAAA,CAAAsB,QAAA;cACAzB,MAAA,CAAA/P,OAAA,GAAAA,OAAA;YAAA;YAAA;cAAA,OAAAgR,SAAA,CAAArP,IAAA;UAAA;QAAA,GAAAqO,QAAA;MAAA;IAEA;IACAmC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,WAAAnR,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAiR,SAAA;QAAA,IAAA/S,IAAA,EAAA0I,GAAA,EAAA3H,cAAA,EAAAiS,WAAA,EAAAC,OAAA,EAAAvN,IAAA,EAAAwN,SAAA,EAAAC,GAAA,EAAAC,mBAAA,EAAAC,OAAA,EAAAC,SAAA,EAAA3M,CAAA,EAAA4M,GAAA,EAAAC,CAAA,EAAAC,IAAA,EAAArD,MAAA;QAAA,WAAAvO,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA0R,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxR,IAAA,GAAAwR,SAAA,CAAAvR,IAAA;YAAA;cACApC,IAAA,GAAA8S,MAAA,CAAA9S,IAAA;cACA0I,GAAA,GAAA1I,IAAA,CAAAwI,YAAA,CAAA0E,KAAA;cAAAyG,SAAA,CAAAvR,IAAA;cAAA,OACA,IAAAwR,yCAAA;gBAAAC,cAAA,EAAAnL,GAAA;gBAAA0E,WAAA,EAAA1E,GAAA;cAAA;YAAA;cAAA3H,cAAA,GAAA4S,SAAA,CAAAlN,IAAA;cAAA;cAAAuM,WAAA,OAAAnM,2BAAA,CAAAjF,OAAA,EACAb,cAAA;cAAA;gBAAA,KAAAiS,WAAA,CAAAlM,CAAA,MAAAmM,OAAA,GAAAD,WAAA,CAAAjM,CAAA,IAAAC,IAAA;kBAAAtB,IAAA,GAAAuN,OAAA,CAAA5S,KAAA;kBACA6S,SAAA,GAAAxN,IAAA,CAAAwN,SAAA;kBACA,IAAAA,SAAA;oBACAC,GAAA,GAAAf,IAAA,CAAAC,KAAA,CAAAa,SAAA;oBACAxN,IAAA,CAAAoO,KAAA,GAAAX,GAAA,CAAAW,KAAA;kBACA;gBACA;cAAA,SAAA3L,GAAA;gBAAA6K,WAAA,CAAAxL,CAAA,CAAAW,GAAA;cAAA;gBAAA6K,WAAA,CAAAvL,CAAA;cAAA;cACAqL,MAAA,CAAA/R,cAAA,GAAAA,cAAA;cAAA4S,SAAA,CAAAvR,IAAA;cAAA,OAEA,IAAA2R,qDAAA;gBAAAF,cAAA,EAAAnL,GAAA;gBAAA0E,WAAA,EAAA1E,GAAA;cAAA;YAAA;cAAA0K,mBAAA,GAAAO,SAAA,CAAAlN,IAAA;cAEA4M,OAAA,IACAD,mBAAA,CAAAtL,GAAA,WAAAnB,CAAA;gBAAA,OAAAA,CAAA,CAAAqN,SAAA,GAAArN,CAAA,CAAAsN,YAAA;cAAA,IACAb,mBAAA,CAAAtL,GAAA,WAAAnB,CAAA;gBAAA,OAAAA,CAAA,CAAAsN,YAAA;cAAA,GACA;cACAX,SAAA;cACA,KAAA3M,CAAA,MAAAA,CAAA,GAAA0M,OAAA,IAAAT,MAAA,IAAAjM,CAAA;gBACA4M,GAAA;gBACA,KAAAC,CAAA,MAAAA,CAAA,GAAAH,OAAA,CAAAT,MAAA,IAAAY,CAAA;kBACAD,GAAA,IAAAF,OAAA,CAAAG,CAAA,EAAA7M,CAAA;gBACA;gBACA2M,SAAA,CAAArL,IAAA,CAAAsL,GAAA;cACA;cACAE,IAAA;gBACAS,IAAA;gBACAC,KAAA;gBACAC,GAAA;gBACAC,MAAA;cACA;cACAjE,MAAA,IACA,OACA,MACA,CAAAtI,GAAA,WAAA5I,IAAA,EAAAoV,GAAA;gBACA;kBACApV,IAAA,EAAAA,IAAA;kBACAmR,IAAA;kBACAkE,KAAA;kBACAC,QAAA;kBACApU,KAAA;oBACAqU,IAAA;oBACAC,SAAA,WAAAA,UAAAC,MAAA;sBAAA,OAAAxE,IAAA,CAAAyE,KAAA,CAAAD,MAAA,CAAAtU,KAAA;oBAAA;kBACA;kBACAT,IAAA,EAAAyT,OAAA,CAAAiB,GAAA,EAAAxM,GAAA,WAAA+M,CAAA,EAAAC,GAAA;oBAAA,OACAxB,SAAA,CAAAwB,GAAA,aAAAD,CAAA,GAAAvB,SAAA,CAAAwB,GAAA;kBAAA,CACA;gBACA;cACA;cACAhC,MAAA,CAAA9R,SAAA;gBACAgP,MAAA;kBACA+E,YAAA;gBACA;gBACAtB,IAAA,EAAAA,IAAA;gBACAuB,KAAA;kBACA3E,IAAA;gBACA;gBACA4E,KAAA;kBACA5E,IAAA;kBACAzQ,IAAA,EAAAwT,mBAAA,CAAAtL,GAAA,WAAAnB,CAAA;oBAAA,OAAAA,CAAA,CAAAuO,OAAA;kBAAA;gBACA;gBACA9E,MAAA,EAAAA;cACA;YAAA;YAAA;cAAA,OAAAuD,SAAA,CAAAtR,IAAA;UAAA;QAAA,GAAA0Q,QAAA;MAAA;IACA;IACAnO,IAAA,WAAAA,KAAAzB,EAAA;MAAA,IAAAgS,MAAA;MAAA,WAAAxT,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAsT,UAAA;QAAA,IAAAtP,GAAA,EAAA9F,IAAA,EAAAwI,YAAA,EAAAE,GAAA,EAAA2M,WAAA,EAAAjU,QAAA,EAAAkU,mBAAA,EAAApU,gBAAA,EAAAqU,OAAA,EAAA7R,MAAA,EAAAC,SAAA,EAAAC,OAAA,EAAA4R,WAAA,EAAAC,OAAA,EAAA/P,IAAA,EAAAnF,OAAA,EAAAmV,gBAAA,EAAAjV,SAAA,EAAAkV,OAAA,EAAAnG,UAAA,EAAAoG,WAAA,EAAAC,OAAA,EAAAC,QAAA,EAAAC,QAAA,EAAAtS,OAAA,EAAAuS,KAAA,EAAAC,WAAA,EAAAC,OAAA,EAAAC,CAAA;QAAA,WAAAtU,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAoU,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAlU,IAAA,GAAAkU,UAAA,CAAAjU,IAAA;YAAA;cACA+S,MAAA,CAAAtV,OAAA;cAAAwW,UAAA,CAAAjU,IAAA;cAAA,OACA,IAAAkU,iBAAA,EAAAnT,EAAA;YAAA;cAAA2C,GAAA,GAAAuQ,UAAA,CAAA5P,IAAA;cACAzG,IAAA,GAAA8F,GAAA,CAAAlG,IAAA;cACA,IAAAI,IAAA,CAAAmB,aAAA;gBACAgU,MAAA,CAAAhU,aAAA,GAAAiR,IAAA,CAAAC,KAAA,CAAArS,IAAA,CAAAmB,aAAA;cACA;cACAgU,MAAA,CAAAnV,IAAA,GAAAA,IAAA;cAEAwI,YAAA,GAAAxI,IAAA,CAAAwI,YAAA;cACAE,GAAA,GAAAF,YAAA,CAAA0E,KAAA;cAAAmJ,UAAA,CAAAjU,IAAA;cAAA,OACA,IAAAmU,2BAAA,EAAA7N,GAAA,YAAAA,GAAA;YAAA;cAAA2M,WAAA,GAAAgB,UAAA,CAAA5P,IAAA;cACA,IAAA4O,WAAA,CAAAjS,IAAA;gBACAhC,QAAA,GAAAiU,WAAA,CAAAzV,IAAA;gBACAuV,MAAA,CAAA/T,QAAA,GAAAA,QAAA;cACA;cAAAiV,UAAA,CAAAjU,IAAA;cAAA,OAEA,IAAAoU,2CAAA,EAAAxW,IAAA,CAAAwG,SAAA;YAAA;cAAA8O,mBAAA,GAAAe,UAAA,CAAA5P,IAAA;cAAA,MACA6O,mBAAA,CAAAlS,IAAA;gBAAAiT,UAAA,CAAAjU,IAAA;gBAAA;cAAA;cACAlB,gBAAA,GAAAoU,mBAAA,CAAA1V,IAAA;cACAuV,MAAA,CAAAjU,gBAAA,GAAAA,gBAAA;cAAA,KACAA,gBAAA;gBAAAmV,UAAA,CAAAjU,IAAA;gBAAA;cAAA;cAAAiU,UAAA,CAAAjU,IAAA;cAAA,OACA+S,MAAA,CAAA5E,aAAA,CAAArP,gBAAA,CAAAsP,SAAA;YAAA;cAAA6F,UAAA,CAAAjU,IAAA;cAAA,OACA+S,MAAA,CAAAvG,aAAA;YAAA;cAAAyH,UAAA,CAAAjU,IAAA;cAAA,OAIA,IAAAqU,uCAAA;gBACAC,MAAA,EAAA1W,IAAA,CAAAuD,MAAA;gBACAoT,SAAA,EAAA3W,IAAA,CAAAqD,QAAA;gBACAuT,WAAA,EAAA5W,IAAA,CAAAoL,WAAA;gBACA5C,YAAA,EAAAxI,IAAA,CAAAwI;cACA;YAAA;cALA+M,OAAA,GAAAc,UAAA,CAAA5P,IAAA;cAMA/C,MAAA,GAAAyR,MAAA,CAAA3H,IAAA;cACA7J,SAAA,GAAAwR,MAAA,CAAA3H,IAAA;cACA5J,OAAA,GAAAuR,MAAA,CAAA3H,IAAA;cACA2H,MAAA,CAAA7U,QAAA,GAAAiV,OAAA,CAAAzN,GAAA,WAAAnB,CAAA;gBACA;kBACAkQ,KAAA,EAAAlQ,CAAA,CAAAmQ,KAAA;kBACArH,IAAA,EAAA9I,CAAA,CAAAoQ,IAAA;kBACAC,SAAA,EAAArQ,CAAA,CAAAsQ,SAAA;kBACAC,MAAA,EAAAvQ,CAAA,CAAAwQ,MAAA;kBACA5T,MAAA,EAAAoD,CAAA,CAAA+P,MAAA;kBACAhT,MAAA,EAAAiD,CAAA,CAAAyQ,QAAA;kBAAA;kBACAzT,SAAA,EAAAgD,CAAA,CAAA0Q,OAAA;kBAAA;kBACAzT,OAAA,EAAA+C,CAAA,CAAA2Q,QAAA;kBAAA;kBACAlM,WAAA,EAAAzE,CAAA,CAAAiQ,WAAA;kBACAW,QAAA,EAAA5Q,CAAA,CAAA4Q;gBACA;cACA;cAAA/B,WAAA,OAAA3O,2BAAA,CAAAjF,OAAA,EAEAuT,MAAA,CAAA7U,QAAA;cAAA;gBAAA,KAAAkV,WAAA,CAAA1O,CAAA,MAAA2O,OAAA,GAAAD,WAAA,CAAAzO,CAAA,IAAAC,IAAA;kBAAAtB,IAAA,GAAA+P,OAAA,CAAApV,KAAA;kBACAqD,MAAA,GAAAyR,MAAA,CAAA5H,GAAA,CAAA7J,MAAA,EAAAgC,IAAA,CAAAhC,MAAA;kBACAC,SAAA,GAAAwR,MAAA,CAAA5H,GAAA,CAAA5J,SAAA,EAAA+B,IAAA,CAAA/B,SAAA;kBACAC,OAAA,GAAAuR,MAAA,CAAA5H,GAAA,CAAA3J,OAAA,EAAA8B,IAAA,CAAA9B,OAAA;gBACA;cAAA,SAAAuE,GAAA;gBAAAqN,WAAA,CAAAhO,CAAA,CAAAW,GAAA;cAAA;gBAAAqN,WAAA,CAAA/N,CAAA;cAAA;cAEAzH,IAAA,CAAA0D,MAAA,GAAAA,MAAA,CAAAU,QAAA;cACApE,IAAA,CAAA2D,SAAA,GAAAA,SAAA,CAAAS,QAAA;cACApE,IAAA,CAAA4D,OAAA,GAAAA,OAAA,CAAAQ,QAAA;cAAAiS,UAAA,CAAAjU,IAAA;cAAA,OAEA,IAAAoV,qBAAA;gBACAjU,MAAA,EAAAvD,IAAA,CAAAuD,MAAA;gBACAkU,UAAA,EAAAzX,IAAA,CAAAqD,QAAA;gBACA+H,WAAA,EAAApL,IAAA,CAAAoL,WAAA;gBACA5C,YAAA,EAAAxI,IAAA,CAAAwI;cACA;YAAA;cALAjI,OAAA,GAAA8V,UAAA,CAAA5P,IAAA;cAMA0O,MAAA,CAAA5U,OAAA,GAAAA,OAAA;cAAA8V,UAAA,CAAAjU,IAAA;cAAA,OAEA,IAAAsV,+BAAA;gBACAnU,MAAA,EAAAvD,IAAA,CAAAuD,MAAA;gBACA6H,WAAA,EAAApL,IAAA,CAAAoL,WAAA;gBACA/H,QAAA,EAAArD,IAAA,CAAAqD,QAAA;gBACAC,QAAA,EAAAtD,IAAA,CAAAsD,QAAA;gBACAkF,YAAA,EAAAxI,IAAA,CAAAwI;cACA;YAAA;cANAkN,gBAAA,GAAAW,UAAA,CAAA5P,IAAA;cAQAhG,SAAA;cACAkV,OAAA,OAAArI,GAAA,CAAAoI,gBAAA,CAAA5N,GAAA,WAAAnB,CAAA;gBAAA,OAAAA,CAAA,CAAAmP,QAAA;cAAA;cACAtG,UAAA,GAAA2F,MAAA,CAAA3H,IAAA;cAAAoI,WAAA,OAAA/O,2BAAA,CAAAjF,OAAA,EACA+T,OAAA;cAAA;gBAAA,KAAAC,WAAA,CAAA9O,CAAA,MAAA+O,OAAA,GAAAD,WAAA,CAAA7O,CAAA,IAAAC,IAAA;kBAAA8O,QAAA,GAAAD,OAAA,CAAAxV,KAAA;kBACA0V,QAAA;kBACAtS,OAAA,GAAA0R,MAAA,CAAA3H,IAAA;kBACAwI,KAAA;kBAAAC,WAAA,OAAApP,2BAAA,CAAAjF,OAAA,EACA8T,gBAAA;kBAAA;oBAAA,KAAAO,WAAA,CAAAnP,CAAA,MAAAoP,OAAA,GAAAD,WAAA,CAAAlP,CAAA,IAAAC,IAAA;sBAAAmP,CAAA,GAAAD,OAAA,CAAA7V,KAAA;sBACA,IAAAyV,QAAA,KAAAK,CAAA,CAAAL,QAAA;wBACAC,QAAA,GAAAI,CAAA,CAAAJ,QAAA;wBACAC,KAAA,CAAA/N,IAAA,CAAAkO,CAAA;wBACA1S,OAAA,GAAA0R,MAAA,CAAA5H,GAAA,CAAA9J,OAAA,EAAA0S,CAAA,CAAA1S,OAAA;sBACA;oBACA;kBAAA,SAAA0E,GAAA;oBAAA8N,WAAA,CAAAzO,CAAA,CAAAW,GAAA;kBAAA;oBAAA8N,WAAA,CAAAxO,CAAA;kBAAA;kBACAhH,SAAA,CAAAwH,IAAA;oBACA6N,QAAA,EAAAA,QAAA;oBACAC,QAAA,EAAAA,QAAA;oBACAtS,OAAA,EAAAA,OAAA;oBACAuS,KAAA,EAAAA;kBACA;kBACAxG,UAAA,GAAA2F,MAAA,CAAA5H,GAAA,CAAAiC,UAAA,EAAA/L,OAAA;gBACA;cAAA,SAAA0E,GAAA;gBAAAyN,WAAA,CAAApO,CAAA,CAAAW,GAAA;cAAA;gBAAAyN,WAAA,CAAAnO,CAAA;cAAA;cACAzH,IAAA,CAAAyD,OAAA,GAAA+L,UAAA,CAAApL,QAAA;cACA+Q,MAAA,CAAA1U,SAAA,GAAAA,SAAA;cAAA4V,UAAA,CAAAjU,IAAA;cAAA,OAEA+S,MAAA,CAAA/P,kBAAA;YAAA;cAAAiR,UAAA,CAAAjU,IAAA;cAAA,OACA+S,MAAA,CAAA/M,kBAAA;YAAA;cAAAiO,UAAA,CAAAjU,IAAA;cAAA,OACA+S,MAAA,CAAAtC,WAAA;YAAA;cAEAsC,MAAA,CAAAtV,OAAA;YAAA;YAAA;cAAA,OAAAwW,UAAA,CAAAhU,IAAA;UAAA;QAAA,GAAA+S,SAAA;MAAA;IACA;IACAuC,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MAAA,WAAAjW,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA+V,UAAA;QAAA,IAAA7X,IAAA;QAAA,WAAA6B,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA8V,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA5V,IAAA,GAAA4V,UAAA,CAAA3V,IAAA;YAAA;cACApC,IAAA,GAAAgY,MAAA,CAAAC,MAAA,KAAAL,OAAA,CAAA5X,IAAA;cAEAA,IAAA,CAAAmB,aAAA,GAAAiR,IAAA,CAAA8F,SAAA,CAAAN,OAAA,CAAAzW,aAAA;cAAA,MAEAnB,IAAA,CAAAmD,EAAA;gBAAA4U,UAAA,CAAA3V,IAAA;gBAAA;cAAA;cAAA2V,UAAA,CAAA5V,IAAA;cAEAyV,OAAA,CAAA9X,UAAA;cAAAiY,UAAA,CAAA3V,IAAA;cAAA,OACA,IAAA+V,oBAAA,EAAAnY,IAAA;YAAA;cACA4X,OAAA,CAAA9X,UAAA;cACA8X,OAAA,CAAAQ,UAAA;cACAR,OAAA,CAAA5U,OAAA,CAAAA,OAAA,CAAAC,IAAA;cAAA8U,UAAA,CAAA3V,IAAA;cAAA,OACAwV,OAAA,CAAA5U,OAAA,CAAAA,OAAA,CAAAqV,OAAA;YAAA;cAAAN,UAAA,CAAA3V,IAAA;cAAA;YAAA;cAAA2V,UAAA,CAAA5V,IAAA;cAAA4V,UAAA,CAAAxQ,EAAA,GAAAwQ,UAAA;cAEAH,OAAA,CAAA9X,UAAA;YAAA;YAAA;cAAA,OAAAiY,UAAA,CAAA1V,IAAA;UAAA;QAAA,GAAAwV,SAAA;MAAA;IAGA;EACA;AACA", "ignoreList": []}]}