import request from '@/utils/request'

// 查询派工作业列表
export function listWipDispatchState(query) {
  return request({
    url: '/mes/wipDispatchState/list',
    method: 'get',
    params: query
  })
}

// 查询派工作业详细
export function getWipDispatchState(equipmentno) {
  return request({
    url: '/mes/wipDispatchState/' + equipmentno,
    method: 'get'
  })
}

// 新增派工作业
export function addWipDispatchState(data) {
  return request({
    url: '/mes/wipDispatchState',
    method: 'post',
    data: data
  })
}

// 修改派工作业
export function updateWipDispatchState(data) {
  return request({
    url: '/mes/wipDispatchState',
    method: 'put',
    data: data
  })
}

// 删除派工作业
export function delWipDispatchState(equipmentno) {
  return request({
    url: '/mes/wipDispatchState/' + equipmentno,
    method: 'delete'
  })
}

// 导出派工作业
export function exportWipDispatchState(query) {
  return request({
    url: '/mes/wipDispatchState/export',
    method: 'get',
    params: query
  })
}