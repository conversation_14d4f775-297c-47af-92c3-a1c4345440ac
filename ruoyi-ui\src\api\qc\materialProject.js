import request from '@/utils/request'

// 查询包材物料检测项目列表
export function listMaterialProject(query) {
  return request({
    url: '/qc/materialProject/list',
    method: 'get',
    params: query
  })
}

// 查询包材物料检测项目详细
export function getMaterialProject(id) {
  return request({
    url: '/qc/materialProject/' + id,
    method: 'get'
  })
}

// 新增包材物料检测项目
export function addMaterialProject(data) {
  return request({
    url: '/qc/materialProject',
    method: 'post',
    data: data
  })
}

// 修改包材物料检测项目
export function updateMaterialProject(data) {
  return request({
    url: '/qc/materialProject',
    method: 'put',
    data: data
  })
}

// 删除包材物料检测项目
export function delMaterialProject(id) {
  return request({
    url: '/qc/materialProject/' + id,
    method: 'delete'
  })
}

// 导出包材物料检测项目
export function exportMaterialProject(query) {
  return request({
    url: '/qc/materialProject/export',
    method: 'get',
    params: query
  })
}

export function allMaterialProject(query) {
  return request({
    url: '/qc/materialProject/all',
    method: 'get',
    params: query
  })
}

export function revertAllMaterialProject(query) {
  return request({
    url: '/qc/materialProject/revertAll',
    method: 'get',
    params: query
  })
}
