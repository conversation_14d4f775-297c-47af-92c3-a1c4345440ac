import request from '@/utils/request'

// 查询申请列表
export function listDimissionUser(query) {
  return request({
    url: '/hr/dimissionUser/list',
    method: 'get',
    params: query
  })
}
export function auditDimissionUser(query) {
  return request({
    url: '/hr/dimissionUser/audit',
    method: 'get',
    params: query
  })
}
export function logDimissionUser(query) {
  return request({
    url: '/hr/dimissionUser/log',
    method: 'get',
    params: query
  })
}
export function getDimissionUser(id) {
  return request({
    url: '/hr/dimissionUser/' + id,
    method: 'get'
  })
}
export function addDimissionUser(data) {
  return request({
    url: '/hr/dimissionUser',
    method: 'post',
    data: data
  })
}
export function delDimissionUser(id) {
  return request({
    url: '/hr/dimissionUser/' + id,
    method: 'delete'
  })
}
export function submitAudit(data) {
  return request({
    url: '/hr/dimissionUser/submitAudit',
    method: 'put',
    data: data
  })
}
export function cancelAudit(data) {
  return request({
    url: '/hr/dimissionUser/cancelAudit',
    method: 'put',
    data: data
  })
}

export function pigeonholeDimissionUser(data) {
  return request({
    url: '/hr/dimissionUser/pigeonhole',
    method: 'post',
    data: data
  })
}
