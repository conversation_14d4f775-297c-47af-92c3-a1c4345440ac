import request from '@/utils/request'

// 查询香精成分列表
export function listFragrance(query) {
  return request({
    url: '/rd/fragrance/list',
    method: 'get',
    params: query
  })
}

// 查询香精成分详细
export function getFragrance(id) {
  return request({
    url: '/rd/fragrance/' + id,
    method: 'get'
  })
}

// 新增香精成分
export function addFragrance(data) {
  return request({
    url: '/rd/fragrance',
    method: 'post',
    data: data
  })
}

// 修改香精成分
export function updateFragrance(data) {
  return request({
    url: '/rd/fragrance',
    method: 'put',
    data: data
  })
}

// 删除香精成分
export function delFragrance(id) {
  return request({
    url: '/rd/fragrance/' + id,
    method: 'delete'
  })
}

// 导出香精成分
export function exportFragrance(query) {
  return request({
    url: '/rd/fragrance/export',
    method: 'get',
    params: query
  })
}