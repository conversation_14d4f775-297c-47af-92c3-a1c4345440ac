import request from '@/utils/request'

// 查询工艺-灌装量标准列表
export function listGzl(query) {
  return request({
    url: '/sop/gzl/list',
    method: 'get',
    params: query
  })
}

// 查询工艺-灌装量标准详细
export function getGzl(id) {
  return request({
    url: '/sop/gzl/' + id,
    method: 'get'
  })
}

export function getGzlByParams(query) {
  return request({
    url: '/sop/gzl/getByParams',
    method: 'get',
    params: query
  })
}

// 新增工艺-灌装量标准
export function addGzl(data) {
  return request({
    url: '/sop/gzl',
    method: 'post',
    data: data
  })
}

// 修改工艺-灌装量标准
export function updateGzl(data) {
  return request({
    url: '/sop/gzl',
    method: 'put',
    data: data
  })
}

// 删除工艺-灌装量标准
export function delGzl(id) {
  return request({
    url: '/sop/gzl/' + id,
    method: 'delete'
  })
}

// 导出工艺-灌装量标准
export function exportGzl(query) {
  return request({
    url: '/sop/gzl/export',
    method: 'get',
    params: query
  })
}
