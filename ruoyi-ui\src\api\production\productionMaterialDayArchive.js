import request from '@/utils/request'

export function allProductionMaterialDayArchive(query) {
  return request({
    url: '/production/productionMaterialDayArchive/all',
    method: 'get',
    params: query
  })
}

// 查询物料排班日计划存档详细
export function getProductionMaterialDayArchive(id) {
  return request({
    url: '/production/productionMaterialDayArchive/' + id,
    method: 'get'
  })
}

// 新增物料排班日计划存档
export function addProductionMaterialDayArchive(data) {
  return request({
    url: '/production/productionMaterialDayArchive',
    method: 'post',
    data: data
  })
}
