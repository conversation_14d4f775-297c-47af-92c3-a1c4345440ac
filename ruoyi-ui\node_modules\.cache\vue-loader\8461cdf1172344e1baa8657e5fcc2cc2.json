{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\userTable.vue?vue&type=template&id=4f2a111a&scoped=true", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\userTable.vue", "mtime": 1753954679644}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1744596530059}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}