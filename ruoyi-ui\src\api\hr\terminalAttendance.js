import request from '@/utils/request'

// 查询考勤机列表
export function listTerminalAttendance(query) {
  return request({
    url: '/hr/terminalAttendance/list',
    method: 'get',
    params: query
  })
}

// 查询考勤机详细
export function getTerminalAttendance(id) {
  return request({
    url: '/hr/terminalAttendance/' + id,
    method: 'get'
  })
}

// 新增考勤机
export function addTerminalAttendance(data) {
  return request({
    url: '/hr/terminalAttendance',
    method: 'post',
    data: data
  })
}

// 修改考勤机
export function updateTerminalAttendance(data) {
  return request({
    url: '/hr/terminalAttendance',
    method: 'put',
    data: data
  })
}

// 删除考勤机
export function delTerminalAttendance(id) {
  return request({
    url: '/hr/terminalAttendance/' + id,
    method: 'delete'
  })
}

// 导出考勤机
export function exportTerminalAttendance(query) {
  return request({
    url: '/hr/terminalAttendance/export',
    method: 'get',
    params: query
  })
}
