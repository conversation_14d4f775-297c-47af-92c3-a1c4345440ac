import request from '@/utils/request'

// 查询成品价格变化记录列表
export function listFinishedPriceLog(query) {
  return request({
    url: '/resource/finishedPriceLog/list',
    method: 'get',
    params: query
  })
}

// 查询成品价格变化记录详细
export function getFinishedPriceLog(id) {
  return request({
    url: '/resource/finishedPriceLog/' + id,
    method: 'get'
  })
}

// 新增成品价格变化记录
export function addFinishedPriceLog(data) {
  return request({
    url: '/resource/finishedPriceLog',
    method: 'post',
    data: data
  })
}

// 导出成品价格变化记录
export function exportFinishedPriceLog(query) {
  return request({
    url: '/resource/finishedPriceLog/export',
    method: 'get',
    params: query
  })
}

export function allFinishedPriceLog(query) {
  return request({
    url: '/resource/finishedPriceLog/all',
    method: 'get',
    params: query
  })
}
