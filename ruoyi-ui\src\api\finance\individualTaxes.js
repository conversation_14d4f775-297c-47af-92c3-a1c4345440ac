import request from '@/utils/request'

export function calculateListIndividualTaxes(query) {
  return request({
    url: '/finance/individualTaxes/calculateList',
    method: 'get',
    params: query
  })
}
// 查询个税导入记录列表
export function listIndividualTaxes(query) {
  return request({
    url: '/finance/individualTaxes/list',
    method: 'get',
    params: query
  })
}

// 查询个税导入记录详细
export function getIndividualTaxes(id) {
  return request({
    url: '/finance/individualTaxes/' + id,
    method: 'get'
  })
}

// 新增个税导入记录
export function addIndividualTaxes(data) {
  return request({
    url: '/finance/individualTaxes',
    method: 'post',
    data: data
  })
}

// 导出个税导入记录
export function exportIndividualTaxes(query) {
  return request({
    url: '/finance/individualTaxes/export',
    method: 'get',
    params: query
  })
}
