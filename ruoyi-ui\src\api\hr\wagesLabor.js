import request from '@/utils/request'

export function listWagesLabor(query) {
  return request({
    url: '/hr/wagesLabor/list',
    method: 'get',
    params: query
  })
}

export function getWagesLabor(id) {
  return request({
    url: '/hr/wagesLabor/' + id,
    method: 'get'
  })
}
export function statisticsWagesLabor(query) {
  return request({
    url: '/hr/wagesLabor/statistics',
    method: 'get',
    params: query
  })
}

export function addWagesLabor(data) {
  return request({
    url: '/hr/wagesLabor',
    method: 'post',
    data: data
  })
}

export function updateWagesLabor(data) {
  return request({
    url: '/hr/wagesLabor',
    method: 'put',
    data: data
  })
}

export function delWagesLabor(id) {
  return request({
    url: '/hr/wagesLabor/' + id,
    method: 'delete'
  })
}

// 导出日结工工资明细
export function exportLaborCompaniesWages(query) {
  return request({
    url: '/hr/wagesLabor/exportWages',
    method: 'get',
    params: query
  })
}

