import request from '@/utils/request'

// 查询配方释放列表
export function listSoftwareFormulaRelease(query) {
  return request({
    url: '/software/softwareFormulaRelease/list',
    method: 'get',
    params: query
  })
}

//配方审核列表
export function listAuditFormulaRealeaseData(data) {
  return request({
    url: '/software/softwareFormulaRelease/audit',
    method: 'post',
    data: data
  })
}

//获取打样单列表数据
export function querypProjectItemOrderDataList(data) {
  return request({
    url: '/software/softwareFormulaRelease/querypProjectItemOrderDataList',
    method: 'post',
    data: data
  })
}


// 查询配方释放详细
export function getSoftwareFormulaRelease(id) {
  return request({
    url: '/software/softwareFormulaRelease/' + id,
    method: 'get'
  })
}

// 新增配方释放
export function verifySoftwareFormulaRelease(data) {
  return request({
    url: '/software/softwareFormulaRelease/verify',
    method: 'post',
    data: data
  })
}
// 新增配方释放
export function verifyPassSoftwareFormulaRelease(data) {
  return request({
    url: '/software/softwareFormulaRelease/verifyPass',
    method: 'post',
    data: data
  })
}

// 新增配方释放
export function addSoftwareFormulaRelease(data) {
  return request({
    url: '/software/softwareFormulaRelease',
    method: 'post',
    data: data
  })
}

// 修改配方释放
export function updateSoftwareFormulaRelease(data) {
  return request({
    url: '/software/softwareFormulaRelease',
    method: 'put',
    data: data
  })
}

// 删除配方释放
export function delSoftwareFormulaRelease(id) {
  return request({
    url: '/software/softwareFormulaRelease/' + id,
    method: 'delete'
  })
}

// 导出配方释放
export function exportSoftwareFormulaRelease(query) {
  return request({
    url: '/software/softwareFormulaRelease/export',
    method: 'get',
    params: query
  })
}


// 查询配方释放列表
export function querySoftwareFormulaReleaseDataList(query) {
  return request({
    url: '/software/softwareFormulaRelease/querySoftwareFormulaReleaseDataList',
    method: 'get',
    params: query
  })
}

// 查询配方释放
export function querySoftwareFormulaReleaseByIdDataList(query) {
  return request({
    url: '/software/softwareFormulaRelease/querySoftwareFormulaReleaseByIdDataList',
    method: 'get',
    params: query
  })
}


//撤销审核
export function cancelAudit(data) {
  return request({
    url: '/software/softwareFormulaRelease/cancelAudit',
    method: 'put',
    data: data
  })
}


//查询配方分类使用
export function queryUserFormulaCategory(query) {
  return request({
    url: '/software/softwareFormulaRelease/queryUserFormulaCategory',
    method: 'get',
    params: query
  })
}

//查询配方分类使用
export function queryUserUseFormulaCategory(query) {
  return request({
    url: '/software/softwareFormulaRelease/queryUserUseFormulaCategory',
    method: 'get',
    params: query
  })
}

// 新增配方用户分类
export function addSoftwareFormulaCategoryUser(data) {
  return request({
    url: '/software/softwareFormulaRelease/addSoftwareFormulaCategoryUser',
    method: 'post',
    data: data
  })
}

// 取消配方用户分类
export function deleteSoftwareFormulaCategoryUser(data) {
  return request({
    url: '/software/softwareFormulaRelease/deleteSoftwareFormulaCategoryUser',
    method: 'post',
    data: data
  })
}
