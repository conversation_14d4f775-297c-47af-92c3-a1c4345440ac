import request from '@/utils/request'

// 查询并批作业列表
export function listWipMergeContent(query) {
  return request({
    url: '/mes/wipMergeContent/list',
    method: 'get',
    params: query
  })
}

// 查询并批作业详细
export function getWipMergeContent(fromlotno) {
  return request({
    url: '/mes/wipMergeContent/' + fromlotno,
    method: 'get'
  })
}

// 新增并批作业
export function addWipMergeContent(data) {
  return request({
    url: '/mes/wipMergeContent',
    method: 'post',
    data: data
  })
}

// 修改并批作业
export function updateWipMergeContent(data) {
  return request({
    url: '/mes/wipMergeContent',
    method: 'put',
    data: data
  })
}

// 删除并批作业
export function delWipMergeContent(fromlotno) {
  return request({
    url: '/mes/wipMergeContent/' + fromlotno,
    method: 'delete'
  })
}

// 导出并批作业
export function exportWipMergeContent(query) {
  return request({
    url: '/mes/wipMergeContent/export',
    method: 'get',
    params: query
  })
}