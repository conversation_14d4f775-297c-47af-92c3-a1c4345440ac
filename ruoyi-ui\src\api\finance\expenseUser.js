import request from '@/utils/request'

export function listExpenseUserLeadershipSee(query) {
  return request({
    url: '/finance/expenseUser/leadershipSee',
    method: 'get',
    params: query
  })
}
export function listExpenseUser(query) {
  return request({
    url: '/finance/expenseUser/list',
    method: 'get',
    params: query
  })
}
export function listAuditExpenseUser(query) {
  return request({
    url: '/finance/expenseUser/audit',
    method: 'get',
    params: query
  })
}
export function listHistoryExpenseUser(query) {
  return request({
    url: '/finance/expenseUser/history',
    method: 'get',
    params: query
  })
}
export function chooseExpenseUser(query) {
  return request({
    url: '/finance/expenseUser/choose',
    method: 'get',
    params: query
  })
}
export function logExpenseUser(query) {
  return request({
    url: '/finance/expenseUser/log',
    method: 'get',
    params: query
  })
}
export function feeExpenseUser(query) {
  return request({
    url: '/finance/expenseUser/feeList',
    method: 'get',
    params: query
  })
}
export function getExpenseUser(id) {
  return request({
    url: '/finance/expenseUser/' + id,
    method: 'get'
  })
}
export function getExpenseUserApplyType(query) {
  return request({
    url: '/finance/expenseUser/getExpenseUserApplyType',
    method: 'get',
    params: query
  })
}

export function addExpenseUser(data) {
  return request({
    url: '/finance/expenseUser',
    method: 'post',
    data: data
  })
}
export function editExpenseUser(data) {
  return request({
    url: '/finance/expenseUser/edit',
    method: 'post',
    data: data
  })
}

export function submitAudit(data) {
  return request({
    url: '/finance/expenseUser/submitAudit',
    method: 'put',
    data: data
  })
}
export function cancelAudit(data) {
  return request({
    url: '/finance/expenseUser/cancelAudit',
    method: 'put',
    data: data
  })
}

export function pigeonholeExpenseUser(data) {
  return request({
    url: '/finance/expenseUser/pigeonhole',
    method: 'post',
    data: data
  })
}
export function supplementExpenseUser(data) {
  return request({
    url: '/finance/expenseUser/supplement',
    method: 'post',
    data: data
  })
}

export function editPrintNumExpenseUser(data) {
  return request({
    url: '/finance/expenseUser/editPrintNum',
    method: 'post',
    data: data
  })
}

export function nodesExpenseUser(query) {
  return request({
    url: '/finance/expenseUser/nodes',
    method: 'get',
    params: query
  })
}

export function queryPaysumData(query) {
  return request({
    url: '/finance/expenseUser/queryPaysumData',
    method: 'get',
    params: query
  })
}

export function queryPayInfoData(query) {
  return request({
    url: '/finance/expenseUser/queryPayInfoData',
    method: 'get',
    params: query
  })
}

export function sumPayExcel(query) {
  return request({
    url: '/finance/expenseUser/sumPayExcel',
    method: 'get',
    params: query
  })
}


