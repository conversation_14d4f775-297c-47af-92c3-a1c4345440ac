import request from '@/utils/request'

// 查询办公用品规格列表
export function listOfficeGoods(query) {
  return request({
    url: '/resource/officeGoods/list',
    method: 'get',
    params: query
  })
}

// 查询办公用品规格详细
export function getOfficeGoods(id) {
  return request({
    url: '/resource/officeGoods/' + id,
    method: 'get'
  })
}

// 新增办公用品规格
export function addOfficeGoods(data) {
  return request({
    url: '/resource/officeGoods',
    method: 'post',
    data: data
  })
}

// 修改办公用品规格
export function updateOfficeGoods(data) {
  return request({
    url: '/resource/officeGoods',
    method: 'put',
    data: data
  })
}

// 删除办公用品规格
export function delOfficeGoods(id) {
  return request({
    url: '/resource/officeGoods/' + id,
    method: 'delete'
  })
}

export function allOfficeGoods(params) {
  return request({
    url: '/resource/officeGoods/all',
    method: 'get',
    params
  })
}

export function exportTemplateOfficeGoods(query) {
  return request({
    url: '/resource/officeGoods/exportTemplate',
    method: 'get',
    params: query
  })
}

export function rkOfficeGoods(data) {
  return request({
    url: '/resource/officeGoods/rk',
    method: 'put',
    data: data
  })
}

export function exportOfficeGoods(query) {
  return request({
    url: '/resource/officeGoods/export',
    method: 'get',
    params: query
  })
}

export function refreshOfficeGoodsCode(query) {
  return request({
    url: '/resource/officeGoods/refreshGoodsCode',
    method: 'get',
    params: query
  })
}

export function exportOfficeGoodsBase(query) {
  return request({
    url: '/resource/officeGoods/export/base',
    method: 'get',
    params: query
  })
}

export function warnListOfficeGoods(query) {
  return request({
    url: '/resource/officeGoods/warnList',
    method: 'get',
    params: query
  })
}
