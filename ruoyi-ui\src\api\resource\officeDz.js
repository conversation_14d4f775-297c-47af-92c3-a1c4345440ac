import request from '@/utils/request'

// 查询办公用品对账列表
export function listOfficeDz(query) {
  return request({
    url: '/resource/officeDz/list',
    method: 'get',
    params: query
  })
}

export function unFkListOfficeDz(query) {
  return request({
    url: '/resource/officeDz/unFkList',
    method: 'get',
    params: query
  })
}

// 查询办公用品对账详细
export function getOfficeDz(id) {
  return request({
    url: '/resource/officeDz/' + id,
    method: 'get'
  })
}

// 新增办公用品对账
export function addOfficeDz(data) {
  return request({
    url: '/resource/officeDz',
    method: 'post',
    data: data
  })
}

// 修改办公用品对账
export function updateOfficeDz(data) {
  return request({
    url: '/resource/officeDz',
    method: 'put',
    data: data
  })
}

// 删除办公用品对账
export function delOfficeDz(id) {
  return request({
    url: '/resource/officeDz/' + id,
    method: 'delete'
  })
}

// 导出办公用品对账
export function exportOfficeDz(query) {
  return request({
    url: '/resource/officeDz/export',
    method: 'get',
    params: query
  })
}
