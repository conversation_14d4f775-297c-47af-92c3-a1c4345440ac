import request from '@/utils/request'

export function listReservesUserLeadershipSee(query) {
  return request({
    url: '/process/reservesUser/leadershipSee',
    method: 'get',
    params: query
  })
}
export function listReservesUser(query) {
  return request({
    url: '/process/reservesUser/list',
    method: 'get',
    params: query
  })
}
export function listAuditReservesUser(query) {
  return request({
    url: '/process/reservesUser/audit',
    method: 'get',
    params: query
  })
}
export function chooseReservesUser(query) {
  return request({
    url: '/process/reservesUser/choose',
    method: 'get',
    params: query
  })
}
export function logReservesUser(query) {
  return request({
    url: '/process/reservesUser/log',
    method: 'get',
    params: query
  })
}
export function getReservesUser(id) {
  return request({
    url: '/process/reservesUser/' + id,
    method: 'get'
  })
}
export function addReservesUser(data) {
  return request({
    url: '/process/reservesUser',
    method: 'post',
    data: data
  })
}
export function editReservesUser(data) {
  return request({
    url: '/process/reservesUser/edit',
    method: 'post',
    data: data
  })
}

export function submitAudit(data) {
  return request({
    url: '/process/reservesUser/submitAudit',
    method: 'put',
    data: data
  })
}
export function cancelAudit(data) {
  return request({
    url: '/process/reservesUser/cancelAudit',
    method: 'put',
    data: data
  })
}

export function pigeonholeReservesUser(data) {
  return request({
    url: '/process/reservesUser/pigeonhole',
    method: 'post',
    data: data
  })
}
export function supplementReservesUser(data) {
  return request({
    url: '/process/reservesUser/supplement',
    method: 'post',
    data: data
  })
}

export function editPrintNumReservesUser(data) {
  return request({
    url: '/process/reservesUser/editPrintNum',
    method: 'post',
    data: data
  })
}

export function nodesReservesUser(query) {
  return request({
    url: '/process/reservesUser/nodes',
    method: 'get',
    params: query
  })
}
