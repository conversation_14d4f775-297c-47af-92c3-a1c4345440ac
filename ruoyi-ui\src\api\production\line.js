import request from '@/utils/request'

// 查询生产排线列表
export function listLine(query) {
  return request({
    url: '/production/line/list',
    method: 'get',
    params: query
  })
}

// 查询生产排线详细
export function getLine(id) {
  return request({
    url: '/production/line/' + id,
    method: 'get'
  })
}

// 新增生产排线
export function addLine(data) {
  return request({
    url: '/production/line',
    method: 'post',
    data: data
  })
}

// 修改生产排线
export function updateLine(data) {
  return request({
    url: '/production/line',
    method: 'put',
    data: data
  })
}

// 删除生产排线
export function delLine(id) {
  return request({
    url: '/production/line/' + id,
    method: 'delete'
  })
}

// 导出生产排线
export function exportLine(query) {
  return request({
    url: '/production/line/export',
    method: 'get',
    params: query
  })
}

export function allLine(query) {
  return request({
    url: '/production/line/all',
    method: 'get',
    params: query
  })
}
