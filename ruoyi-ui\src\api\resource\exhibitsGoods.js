import request from '@/utils/request'

// 查询展品规格列表
export function listExhibitsGoods(query) {
  return request({
    url: '/resource/exhibitsGoods/list',
    method: 'get',
    params: query
  })
}

// 查询展品规格详细
export function getExhibitsGoods(id) {
  return request({
    url: '/resource/exhibitsGoods/' + id,
    method: 'get'
  })
}

// 新增展品规格
export function addExhibitsGoods(data) {
  return request({
    url: '/resource/exhibitsGoods',
    method: 'post',
    data: data
  })
}

// 修改展品规格
export function updateExhibitsGoods(data) {
  return request({
    url: '/resource/exhibitsGoods',
    method: 'put',
    data: data
  })
}

// 删除展品规格
export function delExhibitsGoods(id) {
  return request({
    url: '/resource/exhibitsGoods/' + id,
    method: 'delete'
  })
}

// 导出展品规格
export function exportExhibitsGoods(query) {
  return request({
    url: '/resource/exhibitsGoods/export',
    method: 'get',
    params: query
  })
}

export function allExhibitsGoods(query) {
  return request({
    url: '/resource/exhibitsGoods/all',
    method: 'get',
    params: query
  })
}
