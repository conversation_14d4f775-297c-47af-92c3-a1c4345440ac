import request from '@/utils/request'

// 查询b代码配制工艺列表
export function listMakeUpB(query) {
  return request({
    url: '/sop/makeUpB/list',
    method: 'get',
    params: query
  })
}

// 查询b代码配制工艺详细
export function getMakeUpB(id) {
  return request({
    url: '/sop/makeUpB/' + id,
    method: 'get'
  })
}

export function getMakeUpBByLabCode(labCode) {
  return request({
    url: '/sop/makeUpB/getByLabCode/' + labCode,
    method: 'get'
  })
}

// 新增b代码配制工艺
export function addMakeUpB(data) {
  return request({
    url: '/sop/makeUpB',
    method: 'post',
    data: data
  })
}

// 修改b代码配制工艺
export function updateMakeUpB(data) {
  return request({
    url: '/sop/makeUpB',
    method: 'put',
    data: data
  })
}

// 删除b代码配制工艺
export function delMakeUpB(id) {
  return request({
    url: '/sop/makeUpB/' + id,
    method: 'delete'
  })
}

// 导出b代码配制工艺
export function exportMakeUpB(query) {
  return request({
    url: '/sop/makeUpB/export',
    method: 'get',
    params: query
  })
}
