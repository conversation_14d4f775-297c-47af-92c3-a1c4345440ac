import request from '@/utils/request'

export function allBillingItem(query) {
  return request({
    url: '/order/billingItem/all',
    method: 'get',
    params: query
  })
}

export function getBillingItem(id) {
  return request({
    url: '/order/billingItem/' + id,
    method: 'get'
  })
}

export function allBillingAllItem(query) {
  return request({
    url: '/order/billingItem/allItem',
    method: 'get',
    params: query
  })
}
