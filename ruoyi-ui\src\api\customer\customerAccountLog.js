import request from '@/utils/request'

// 查询客户账户明细列表
export function listCustomerAccountLog(query) {
  return request({
    url: '/customer/customerAccountLog/list',
    method: 'get',
    params: query
  })
}

// 查询客户账户明细详细
export function getCustomerAccountLog(id) {
  return request({
    url: '/customer/customerAccountLog/' + id,
    method: 'get'
  })
}

// 新增客户账户明细
export function addCustomerAccountLog(data) {
  return request({
    url: '/customer/customerAccountLog',
    method: 'post',
    data: data
  })
}

// 修改客户账户明细
export function updateCustomerAccountLog(data) {
  return request({
    url: '/customer/customerAccountLog',
    method: 'put',
    data: data
  })
}

// 删除客户账户明细
export function delCustomerAccountLog(id) {
  return request({
    url: '/customer/customerAccountLog/' + id,
    method: 'delete'
  })
}

// 导出客户账户明细
export function exportCustomerAccountLog(query) {
  return request({
    url: '/customer/customerAccountLog/export',
    method: 'get',
    params: query
  })
}