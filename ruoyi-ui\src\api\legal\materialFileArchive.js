import request from '@/utils/request'

// 查询法务原料附件备份列表
export function listMaterialFileArchive(query) {
  return request({
    url: '/legal/materialFileArchive/list',
    method: 'get',
    params: query
  })
}

// 查询法务原料附件备份详细
export function getMaterialFileArchive(id) {
  return request({
    url: '/legal/materialFileArchive/' + id,
    method: 'get'
  })
}

// 新增法务原料附件备份
export function addMaterialFileArchive(data) {
  return request({
    url: '/legal/materialFileArchive',
    method: 'post',
    data: data
  })
}

// 修改法务原料附件备份
export function updateMaterialFileArchive(data) {
  return request({
    url: '/legal/materialFileArchive',
    method: 'put',
    data: data
  })
}

// 删除法务原料附件备份
export function delMaterialFileArchive(id) {
  return request({
    url: '/legal/materialFileArchive/' + id,
    method: 'delete'
  })
}

// 导出法务原料附件备份
export function exportMaterialFileArchive(query) {
  return request({
    url: '/legal/materialFileArchive/export',
    method: 'get',
    params: query
  })
}