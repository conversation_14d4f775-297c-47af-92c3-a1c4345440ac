import request from '@/utils/request'

// 查询应聘用户信息列表
export function listSysEntryUser(query) {
  return request({
    url: '/hr/sysEntryUser/list',
    method: 'get',
    params: query
  })
}

// 查询应聘用户信息详细
export function getSysEntryUser(userId) {
  return request({
    url: '/hr/sysEntryUser/' + userId,
    method: 'get'
  })
}

// 新增应聘用户信息
export function addSysEntryUser(data) {
  return request({
    url: '/hr/sysEntryUser',
    method: 'post',
    data: data
  })
}

// 修改应聘用户信息
export function updateSysEntryUser(data) {
  return request({
    url: '/hr/sysEntryUser',
    method: 'put',
    data: data
  })
}

export function updateBaseSysEntryUser(data) {
  return request({
    url: '/hr/sysEntryUser/editBase',
    method: 'put',
    data: data
  })
}

export function updateLogSysEntryUser(data) {
  return request({
    url: '/hr/sysEntryUser/editLog',
    method: 'put',
    data: data
  })
}

// 删除应聘用户信息
export function delSysEntryUser(userId) {
  return request({
    url: '/hr/sysEntryUser/' + userId,
    method: 'delete'
  })
}

// 导出应聘用户信息
export function exportSysEntryUser(id) {
  return request({
    url: '/hr/sysEntryUser/export/' + id,
    method: 'get',
  })
}

export function exportAuditLogSysEntryUser(id) {
  return request({
    url: '/hr/sysEntryUser/exportAuditLog/' + id,
    method: 'get',
  })
}

//提交审核
export function submitAudit(data) {
  return request({
    url: '/hr/sysEntryUser/submitAudit',
    method: 'put',
    data: data
  })
}

//撤销申请
export function cancelAudit(data) {
  return request({
    url: '/hr/sysEntryUser/cancelAudit',
    method: 'put',
    data: data
  })
}

export function exemptEntryUserAudit(instanceId) {
  return request({
    url: '/hr/sysEntryUser/exemptAudit/' + instanceId,
    method: 'put',
  })
}

export function sendEntryUserInterview(data) {
  return request({
    url: '/hr/sysEntryUser/sendEntryInterview',
    method: 'post',
    data,
  })
}

export function sendWorkInterview(data) {
    return request({
        url: '/hr/sysEntryUser/sendWorkInterview',
        method: 'post',
        data,
    })
}

export function sendEntryUserPass(userId) {
  return request({
    url: '/hr/sysEntryUser/sendPass/' + userId,
    method: 'post',
  })
}
