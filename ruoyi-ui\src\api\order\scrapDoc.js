import request from '@/utils/request'

// 查询erp报废数据列表
export function listScrapDoc(query) {
  return request({
    url: '/order/scrapDoc/list',
    method: 'get',
    params: query
  })
}

// 查询erp报废数据详细
export function getScrapDoc(id) {
  return request({
    url: '/order/scrapDoc/' + id,
    method: 'get'
  })
}

// 新增erp报废数据
export function addScrapDoc(data) {
  return request({
    url: '/order/scrapDoc',
    method: 'post',
    data: data
  })
}

// 修改erp报废数据
export function updateScrapDoc(data) {
  return request({
    url: '/order/scrapDoc',
    method: 'put',
    data: data
  })
}

// 删除erp报废数据
export function delScrapDoc(id) {
  return request({
    url: '/order/scrapDoc/' + id,
    method: 'delete'
  })
}

// 导出erp报废数据
export function exportScrapDoc(query) {
  return request({
    url: '/order/scrapDoc/export',
    method: 'get',
    params: query
  })
}