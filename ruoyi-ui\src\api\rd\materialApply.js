import request from '@/utils/request'

// 查询原料准入申请列表
export function listMaterialApply(query) {
  return request({
    url: '/rd/materialApply/list',
    method: 'get',
    params: query
  })
}

export function auditListMaterialApply(query) {
  return request({
    url: '/rd/materialApply/auditList',
    method: 'get',
    params: query
  })
}

// 查询原料准入申请详细
export function getMaterialApply(id) {
  return request({
    url: '/rd/materialApply/' + id,
    method: 'get'
  })
}

// 新增原料准入申请
export function addMaterialApply(data) {
  return request({
    url: '/rd/materialApply',
    method: 'post',
    data: data
  })
}

// 修改原料准入申请
export function updateMaterialApply(data) {
  return request({
    url: '/rd/materialApply',
    method: 'put',
    data: data
  })
}

// 删除原料准入申请
export function delMaterialApply(id) {
  return request({
    url: '/rd/materialApply/' + id,
    method: 'delete'
  })
}

// 导出原料准入申请
export function exportMaterialApply(query) {
  return request({
    url: '/rd/materialApply/export',
    method: 'get',
    params: query
  })
}

export function submitAudit(data) {
  return request({
    url: '/rd/materialApply/submitAudit',
    method: 'put',
    data: data
  })
}

//撤销申请
export function cancelAudit(data) {
  return request({
    url: '/rd/materialApply/cancelAudit',
    method: 'put',
    data: data
  })
}
