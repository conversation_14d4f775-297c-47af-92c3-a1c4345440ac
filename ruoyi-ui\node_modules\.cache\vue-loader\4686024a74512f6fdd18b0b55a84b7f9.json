{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\qc\\audit\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\qc\\audit\\index.vue", "mtime": 1753956861417}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0QXVkaXQsDQogIGdldEF1ZGl0LA0KICBkZWxBdWRpdCwNCiAgYWRkQXVkaXQsDQogIHVwZGF0ZUF1ZGl0LA0KfSBmcm9tICJAL2FwaS9xYy9hdWRpdCI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkF1ZGl0IiwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOS6p+WTgeWHhuWFpeajgOafpeihqOagvOaVsOaNrg0KICAgICAgYXVkaXRMaXN0OiBbXSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICBwcm9kdWN0Q29kZTogbnVsbCwNCiAgICAgICAgbGFib3JhdG9yeUNvZGU6IG51bGwsDQogICAgICAgIG1hbnVmYWN0dXJlcjogbnVsbCwNCiAgICAgICAgcHJvZHVjdE5hbWU6IG51bGwsDQogICAgICAgIHBsYW5uZWRQcm9kdWN0aW9uRGF0ZTogbnVsbCwNCiAgICAgICAgZGVsaXZlcnlEYXRlOiBudWxsLA0KICAgICAgICBwcm9kdWN0VHlwZTogbnVsbCwNCiAgICAgICAgcmVnaXN0cmF0aW9uQ29tcGxldGlvbkhvdmVyVGlwOiBudWxsLA0KICAgICAgICBjYW5Qcm9kdWNlOiBudWxsLA0KICAgICAgICBjYW5EZWxpdmVyOiBudWxsLA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIHByb2R1Y3RDb2RlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS6p+WTgeS7o+eggeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBwcm9kdWN0TmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuqflk4HlkI3np7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgIH0sDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKiDmn6Xor6Lkuqflk4Hlh4blhaXmo4Dmn6XliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3RBdWRpdCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLmF1ZGl0TGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICBwcm9kdWN0SWQ6IG51bGwsDQogICAgICAgIHByb2R1Y3RDb2RlOiBudWxsLA0KICAgICAgICBsYWJvcmF0b3J5Q29kZTogbnVsbCwNCiAgICAgICAgbWFudWZhY3R1cmVyOiBudWxsLA0KICAgICAgICBwcm9kdWN0TmFtZTogbnVsbCwNCiAgICAgICAgc3BlYzogbnVsbCwNCiAgICAgICAgcGxhbm5lZFByb2R1Y3Rpb25EYXRlOiBudWxsLA0KICAgICAgICBvcmRlclF1YW50aXR5OiBudWxsLA0KICAgICAgICBkZWxpdmVyeURhdGU6IG51bGwsDQogICAgICAgIHByb2R1Y3RUeXBlOiBudWxsLA0KICAgICAgICBmb3JtdWxhU3RhYmlsaXR5UmVwb3J0OiBudWxsLA0KICAgICAgICBmb3JtdWxhU3RhYmlsaXR5UmVwb3J0SG92ZXJUaXA6IG51bGwsDQogICAgICAgIGZvcm11bGFGZWFzaWJpbGl0eUFzc2Vzc21lbnQ6IG51bGwsDQogICAgICAgIGZvcm11bGFGZWFzaWJpbGl0eUFzc2Vzc21lbnRIb3ZlclRpcDogbnVsbCwNCiAgICAgICAgc3RhbmRhcmRGb3JtdWxhUHJvY2VzczogbnVsbCwNCiAgICAgICAgbW9sZFRvb2xDb25maXJtYXRpb246IG51bGwsDQogICAgICAgIG1vbGRUb29sQ29uZmlybWF0aW9uSG92ZXJUaXA6IG51bGwsDQogICAgICAgIGZpbGxpbmdQYWNrYWdpbmdGZWFzaWJpbGl0eTogbnVsbCwNCiAgICAgICAgZmlsbGluZ1BhY2thZ2luZ0ZlYXNpYmlsaXR5SG92ZXJUaXA6IG51bGwsDQogICAgICAgIGZpbGxpbmdQYWNrYWdpbmdTb3A6IG51bGwsDQogICAgICAgIGZpbmlzaGVkUHJvZHVjdFN0YW5kYXJkOiBudWxsLA0KICAgICAgICBxdWFsaXR5QWdyZWVtZW50OiBudWxsLA0KICAgICAgICBxdWFsaXR5QWdyZWVtZW50SG92ZXJUaXA6IG51bGwsDQogICAgICAgIHBhY2thZ2luZ01hdGVyaWFsU3RhbmRhcmQ6IG51bGwsDQogICAgICAgIGxpcXVpZFNhbXBsZTogbnVsbCwNCiAgICAgICAgcGFja2FnaW5nTWF0ZXJpYWxTYW1wbGU6IG51bGwsDQogICAgICAgIGZpbmlzaGVkUHJvZHVjdFNhbXBsZTogbnVsbCwNCiAgICAgICAgZXhjZXNzaXZlUGFja2FnaW5nQ29uZmlybWF0aW9uOiBudWxsLA0KICAgICAgICBleGNlc3NpdmVQYWNrYWdpbmdDb25maXJtYXRpb25Ib3ZlclRpcDogbnVsbCwNCiAgICAgICAgcmVnaXN0cmF0aW9uQ29tcGxldGlvbjogbnVsbCwNCiAgICAgICAgcmVnaXN0cmF0aW9uQ29tcGxldGlvbkhvdmVyVGlwOiBudWxsLA0KICAgICAgICBmb3JtdWxhUHJvY2Vzc0NvbnNpc3RlbmN5OiBudWxsLA0KICAgICAgICBmb3JtdWxhUHJvY2Vzc0NvbnNpc3RlbmN5SG92ZXJUaXA6IG51bGwsDQogICAgICAgIGRvY3VtZW50YXRpb25Db25zaXN0ZW5jeTogbnVsbCwNCiAgICAgICAgZG9jdW1lbnRhdGlvbkNvbnNpc3RlbmN5SG92ZXJUaXA6IG51bGwsDQogICAgICAgIGludGVybmFsU3RhbmRhcmRDb21wbGlhbmNlOiBudWxsLA0KICAgICAgICBkZWxGbGFnOiBudWxsLA0KICAgICAgICBjcmVhdGVCeTogbnVsbCwNCiAgICAgICAgY3JlYXRlVGltZTogbnVsbCwNCiAgICAgICAgdXBkYXRlQnk6IG51bGwsDQogICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsDQogICAgICAgIHJlbWFyazogbnVsbCwNCiAgICAgICAgY2FuUHJvZHVjZTogbnVsbCwNCiAgICAgICAgY2FuUHJvZHVjZVJlbWFyazogbnVsbCwNCiAgICAgICAgY2FuRGVsaXZlcjogbnVsbCwNCiAgICAgICAgY2FuRGVsaXZlclJlbWFyazogbnVsbCwNCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoKGl0ZW0pID0+IGl0ZW0uaWQpOw0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOw0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOw0KICAgIH0sDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOS6p+WTgeWHhuWFpeajgOafpSI7DQogICAgfSwNCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgY29uc3QgaWQgPSByb3cuaWQgfHwgdGhpcy5pZHM7DQogICAgICBnZXRBdWRpdChpZCkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnkuqflk4Hlh4blhaXmo4Dmn6UiOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5pZCAhPSBudWxsKSB7DQogICAgICAgICAgICB1cGRhdGVBdWRpdCh0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBhZGRBdWRpdCh0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgaWRzID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5Lqn5ZOB5YeG5YWl5qOA5p+l57yW5Y+35Li6IicgKyBpZHMgKyAnIueahOaVsOaNrumhue+8nycpDQogICAgICAgIC50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICByZXR1cm4gZGVsQXVkaXQoaWRzKTsNCiAgICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgNCiAgICAgICAgInFjL2F1ZGl0L2V4cG9ydCIsDQogICAgICAgIHsNCiAgICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zLA0KICAgICAgICB9LA0KICAgICAgICBgYXVkaXRfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGANCiAgICAgICk7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiqBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/qc/audit", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      size=\"small\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n      label-width=\"68px\"\r\n    >\r\n      <el-form-item label=\"产品代码\" prop=\"productCode\">\r\n        <el-input\r\n          v-model=\"queryParams.productCode\"\r\n          placeholder=\"请输入产品代码\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"实验室编号\" label-width=\"100px\" prop=\"laboratoryCode\">\r\n        <el-select\r\n          v-model=\"queryParams.laboratoryCode\"\r\n          placeholder=\"请选择实验室编号\"\r\n          clearable\r\n        >\r\n          <el-option label=\"请选择字典生成\" value=\"\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"生产企业\" prop=\"manufacturer\">\r\n        <el-select\r\n          v-model=\"queryParams.manufacturer\"\r\n          placeholder=\"请选择生产企业\"\r\n          clearable\r\n        >\r\n          <el-option label=\"请选择字典生成\" value=\"\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"产品名称\" prop=\"productName\">\r\n        <el-input\r\n          v-model=\"queryParams.productName\"\r\n          placeholder=\"请输入产品名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"预计生产日期\" label-width=\"110px\" prop=\"plannedProductionDate\">\r\n        <el-date-picker\r\n          clearable\r\n          v-model=\"queryParams.plannedProductionDate\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择预计生产日期\"\r\n        >\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"产品交期\" prop=\"deliveryDate\">\r\n        <el-date-picker\r\n          clearable\r\n          v-model=\"queryParams.deliveryDate\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择产品交期\"\r\n        >\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"产品类型\" prop=\"productType\">\r\n        <el-select\r\n          v-model=\"queryParams.productType\"\r\n          placeholder=\"请选择产品类型\"\r\n          clearable\r\n        >\r\n          <el-option label=\"请选择字典生成\" value=\"\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"备案号\" prop=\"registrationCompletionHoverTip\">\r\n        <el-input\r\n          v-model=\"queryParams.registrationCompletionHoverTip\"\r\n          placeholder=\"请输入备案号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"是否可生产\" prop=\"canProduce\">\r\n        <el-input\r\n          v-model=\"queryParams.canProduce\"\r\n          placeholder=\"请输入是否可生产\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"是否可出库\" prop=\"canDeliver\">\r\n        <el-input\r\n          v-model=\"queryParams.canDeliver\"\r\n          placeholder=\"请输入是否可出库\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['qc:audit:add']\"\r\n          >新增</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['qc:audit:edit']\"\r\n          >修改</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['qc:audit:remove']\"\r\n          >删除</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['qc:audit:export']\"\r\n          >导出</el-button\r\n        >\r\n      </el-col>\r\n      <right-toolbar\r\n        :showSearch.sync=\"showSearch\"\r\n        @queryTable=\"getList\"\r\n      ></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"auditList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <!-- <el-table-column label=\"主键ID\" align=\"center\" prop=\"id\" /> -->\r\n      <el-table-column label=\"产品ID\" align=\"center\" prop=\"productId\" />\r\n      <el-table-column label=\"产品代码\" align=\"center\" prop=\"productCode\" />\r\n      <el-table-column\r\n        label=\"实验室编号\"\r\n        align=\"center\"\r\n        prop=\"laboratoryCode\"\r\n      />\r\n      <el-table-column label=\"生产企业\" align=\"center\" prop=\"manufacturer\" />\r\n      <el-table-column label=\"产品名称\" align=\"center\" prop=\"productName\" />\r\n      <el-table-column label=\"规格\" align=\"center\" prop=\"spec\" />\r\n      <el-table-column\r\n        label=\"预计生产日期\"\r\n        align=\"center\"\r\n        prop=\"plannedProductionDate\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{\r\n            parseTime(scope.row.plannedProductionDate, \"{y}-{m}-{d}\")\r\n          }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"订单数量\" align=\"center\" prop=\"orderQuantity\" />\r\n      <el-table-column\r\n        label=\"产品交期\"\r\n        align=\"center\"\r\n        prop=\"deliveryDate\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.deliveryDate, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"产品类型\" align=\"center\" prop=\"productType\" />\r\n      <el-table-column\r\n        label=\"配方稳定性报告状态\"\r\n        align=\"center\"\r\n        prop=\"formulaStabilityReport\"\r\n      />\r\n      <el-table-column\r\n        label=\"配方稳定性报告风险内容\"\r\n        align=\"center\"\r\n        prop=\"formulaStabilityReportHoverTip\"\r\n      />\r\n      <el-table-column\r\n        label=\"配制可行性评估状态\"\r\n        align=\"center\"\r\n        prop=\"formulaFeasibilityAssessment\"\r\n      />\r\n      <el-table-column\r\n        label=\"配制可行性评估风险内容\"\r\n        align=\"center\"\r\n        prop=\"formulaFeasibilityAssessmentHoverTip\"\r\n      />\r\n      <el-table-column\r\n        label=\"标准配制工艺单状态\"\r\n        align=\"center\"\r\n        prop=\"standardFormulaProcess\"\r\n      />\r\n      <el-table-column\r\n        label=\"生产模具治具确认状态\"\r\n        align=\"center\"\r\n        prop=\"moldToolConfirmation\"\r\n      />\r\n      <el-table-column\r\n        label=\"生产模具治具确认预计时间\"\r\n        align=\"center\"\r\n        prop=\"moldToolConfirmationHoverTip\"\r\n      />\r\n      <el-table-column\r\n        label=\"灌包可行性评估状态\"\r\n        align=\"center\"\r\n        prop=\"fillingPackagingFeasibility\"\r\n      />\r\n      <el-table-column\r\n        label=\"灌包可行性评估风险内容\"\r\n        align=\"center\"\r\n        prop=\"fillingPackagingFeasibilityHoverTip\"\r\n      />\r\n      <el-table-column\r\n        label=\"灌装/包装SOP状态\"\r\n        align=\"center\"\r\n        prop=\"fillingPackagingSop\"\r\n      />\r\n      <el-table-column\r\n        label=\"成品检验标准状态\"\r\n        align=\"center\"\r\n        prop=\"finishedProductStandard\"\r\n      />\r\n      <el-table-column\r\n        label=\"质量协议状态\"\r\n        align=\"center\"\r\n        prop=\"qualityAgreement\"\r\n      />\r\n      <el-table-column\r\n        label=\"质量协议合同类型是独立还是主框架合同\"\r\n        align=\"center\"\r\n        prop=\"qualityAgreementHoverTip\"\r\n      />\r\n      <el-table-column\r\n        label=\"包材标准状态\"\r\n        align=\"center\"\r\n        prop=\"packagingMaterialStandard\"\r\n      />\r\n      <el-table-column\r\n        label=\"料体标样状态\"\r\n        align=\"center\"\r\n        prop=\"liquidSample\"\r\n      />\r\n      <el-table-column\r\n        label=\"包材标准样状态\"\r\n        align=\"center\"\r\n        prop=\"packagingMaterialSample\"\r\n      />\r\n      <el-table-column\r\n        label=\"成品标样状态\"\r\n        align=\"center\"\r\n        prop=\"finishedProductSample\"\r\n      />\r\n      <el-table-column\r\n        label=\"过度包装确认状态\"\r\n        align=\"center\"\r\n        prop=\"excessivePackagingConfirmation\"\r\n      />\r\n      <el-table-column\r\n        label=\"过度包装风险内容\"\r\n        align=\"center\"\r\n        prop=\"excessivePackagingConfirmationHoverTip\"\r\n      />\r\n      <el-table-column\r\n        label=\"注册备案是否完成状态\"\r\n        align=\"center\"\r\n        prop=\"registrationCompletion\"\r\n      />\r\n      <el-table-column\r\n        label=\"备案号\"\r\n        align=\"center\"\r\n        prop=\"registrationCompletionHoverTip\"\r\n      />\r\n      <el-table-column\r\n        label=\"大货标准配方工艺单与注册/备案一致性状态\"\r\n        align=\"center\"\r\n        prop=\"formulaProcessConsistency\"\r\n      />\r\n      <el-table-column\r\n        label=\"风险内容\"\r\n        align=\"center\"\r\n        prop=\"formulaProcessConsistencyHoverTip\"\r\n      />\r\n      <el-table-column\r\n        label=\"大货文案与备案资料一致性状态\"\r\n        align=\"center\"\r\n        prop=\"documentationConsistency\"\r\n      />\r\n      <el-table-column\r\n        label=\"风险内容\"\r\n        align=\"center\"\r\n        prop=\"documentationConsistencyHoverTip\"\r\n      />\r\n      <el-table-column\r\n        label=\"产品内控标准符合备案执行标准状态\"\r\n        align=\"center\"\r\n        prop=\"internalStandardCompliance\"\r\n      />\r\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" />\r\n      <el-table-column label=\"是否可生产\" align=\"center\" prop=\"canProduce\" />\r\n      <el-table-column\r\n        label=\"是否可生产备注\"\r\n        align=\"center\"\r\n        prop=\"canProduceRemark\"\r\n      />\r\n      <el-table-column label=\"是否可出库\" align=\"center\" prop=\"canDeliver\" />\r\n      <el-table-column\r\n        label=\"是否可出库备注\"\r\n        align=\"center\"\r\n        prop=\"canDeliverRemark\"\r\n      />\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['qc:audit:edit']\"\r\n            >修改</el-button\r\n          >\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['qc:audit:remove']\"\r\n            >删除</el-button\r\n          >\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改产品准入检查对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"产品ID\" prop=\"productId\">\r\n          <el-input v-model=\"form.productId\" placeholder=\"请输入产品ID\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"产品代码\" prop=\"productCode\">\r\n          <el-input v-model=\"form.productCode\" placeholder=\"请输入产品代码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"实验室编号\" prop=\"laboratoryCode\">\r\n          <el-select\r\n            v-model=\"form.laboratoryCode\"\r\n            placeholder=\"请选择实验室编号\"\r\n          >\r\n            <el-option label=\"请选择字典生成\" value=\"\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"生产企业\" prop=\"manufacturer\">\r\n          <el-select v-model=\"form.manufacturer\" placeholder=\"请选择生产企业\">\r\n            <el-option label=\"请选择字典生成\" value=\"\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"产品名称\" prop=\"productName\">\r\n          <el-input v-model=\"form.productName\" placeholder=\"请输入产品名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"规格\" prop=\"spec\">\r\n          <el-input v-model=\"form.spec\" placeholder=\"请输入规格\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"预计生产日期\" prop=\"plannedProductionDate\">\r\n          <el-date-picker\r\n            clearable\r\n            v-model=\"form.plannedProductionDate\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"请选择预计生产日期\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"订单数量\" prop=\"orderQuantity\">\r\n          <el-input v-model=\"form.orderQuantity\" placeholder=\"请输入订单数量\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"产品交期\" prop=\"deliveryDate\">\r\n          <el-date-picker\r\n            clearable\r\n            v-model=\"form.deliveryDate\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"请选择产品交期\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"产品类型\" prop=\"productType\">\r\n          <el-select v-model=\"form.productType\" placeholder=\"请选择产品类型\">\r\n            <el-option label=\"请选择字典生成\" value=\"\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"配方稳定性报告状态\" prop=\"formulaStabilityReport\">\r\n          <el-input\r\n            v-model=\"form.formulaStabilityReport\"\r\n            placeholder=\"请输入配方稳定性报告状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"配方稳定性报告风险内容\"\r\n          prop=\"formulaStabilityReportHoverTip\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.formulaStabilityReportHoverTip\"\r\n            placeholder=\"请输入配方稳定性报告风险内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"配制可行性评估状态\"\r\n          prop=\"formulaFeasibilityAssessment\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.formulaFeasibilityAssessment\"\r\n            placeholder=\"请输入配制可行性评估状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"配制可行性评估风险内容\"\r\n          prop=\"formulaFeasibilityAssessmentHoverTip\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.formulaFeasibilityAssessmentHoverTip\"\r\n            placeholder=\"请输入配制可行性评估风险内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"标准配制工艺单状态\" prop=\"standardFormulaProcess\">\r\n          <el-input\r\n            v-model=\"form.standardFormulaProcess\"\r\n            placeholder=\"请输入标准配制工艺单状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"生产模具治具确认状态\" prop=\"moldToolConfirmation\">\r\n          <el-input\r\n            v-model=\"form.moldToolConfirmation\"\r\n            placeholder=\"请输入生产模具治具确认状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"生产模具治具确认预计时间\"\r\n          prop=\"moldToolConfirmationHoverTip\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.moldToolConfirmationHoverTip\"\r\n            placeholder=\"请输入生产模具治具确认预计时间\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"灌包可行性评估状态\"\r\n          prop=\"fillingPackagingFeasibility\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.fillingPackagingFeasibility\"\r\n            placeholder=\"请输入灌包可行性评估状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"灌包可行性评估风险内容\"\r\n          prop=\"fillingPackagingFeasibilityHoverTip\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.fillingPackagingFeasibilityHoverTip\"\r\n            placeholder=\"请输入灌包可行性评估风险内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"灌装/包装SOP状态\" prop=\"fillingPackagingSop\">\r\n          <el-input\r\n            v-model=\"form.fillingPackagingSop\"\r\n            placeholder=\"请输入灌装/包装SOP状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"成品检验标准状态\" prop=\"finishedProductStandard\">\r\n          <el-input\r\n            v-model=\"form.finishedProductStandard\"\r\n            placeholder=\"请输入成品检验标准状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"质量协议状态\" prop=\"qualityAgreement\">\r\n          <el-input\r\n            v-model=\"form.qualityAgreement\"\r\n            placeholder=\"请输入质量协议状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"质量协议合同类型是独立还是主框架合同\"\r\n          prop=\"qualityAgreementHoverTip\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.qualityAgreementHoverTip\"\r\n            placeholder=\"请输入质量协议合同类型是独立还是主框架合同\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"包材标准状态\" prop=\"packagingMaterialStandard\">\r\n          <el-input\r\n            v-model=\"form.packagingMaterialStandard\"\r\n            placeholder=\"请输入包材标准状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"料体标样状态\" prop=\"liquidSample\">\r\n          <el-input\r\n            v-model=\"form.liquidSample\"\r\n            placeholder=\"请输入料体标样状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"包材标准样状态\" prop=\"packagingMaterialSample\">\r\n          <el-input\r\n            v-model=\"form.packagingMaterialSample\"\r\n            placeholder=\"请输入包材标准样状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"成品标样状态\" prop=\"finishedProductSample\">\r\n          <el-input\r\n            v-model=\"form.finishedProductSample\"\r\n            placeholder=\"请输入成品标样状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"过度包装确认状态\"\r\n          prop=\"excessivePackagingConfirmation\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.excessivePackagingConfirmation\"\r\n            placeholder=\"请输入过度包装确认状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"过度包装风险内容\"\r\n          prop=\"excessivePackagingConfirmationHoverTip\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.excessivePackagingConfirmationHoverTip\"\r\n            placeholder=\"请输入过度包装风险内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"注册备案是否完成状态\"\r\n          prop=\"registrationCompletion\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.registrationCompletion\"\r\n            placeholder=\"请输入注册备案是否完成状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"备案号\" prop=\"registrationCompletionHoverTip\">\r\n          <el-input\r\n            v-model=\"form.registrationCompletionHoverTip\"\r\n            placeholder=\"请输入备案号\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"大货标准配方工艺单与注册/备案一致性状态\"\r\n          prop=\"formulaProcessConsistency\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.formulaProcessConsistency\"\r\n            placeholder=\"请输入大货标准配方工艺单与注册/备案一致性状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"风险内容\" prop=\"formulaProcessConsistencyHoverTip\">\r\n          <el-input\r\n            v-model=\"form.formulaProcessConsistencyHoverTip\"\r\n            placeholder=\"请输入风险内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"大货文案与备案资料一致性状态\"\r\n          prop=\"documentationConsistency\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.documentationConsistency\"\r\n            placeholder=\"请输入大货文案与备案资料一致性状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"风险内容\" prop=\"documentationConsistencyHoverTip\">\r\n          <el-input\r\n            v-model=\"form.documentationConsistencyHoverTip\"\r\n            placeholder=\"请输入风险内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"产品内控标准符合备案执行标准状态\"\r\n          prop=\"internalStandardCompliance\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.internalStandardCompliance\"\r\n            placeholder=\"请输入产品内控标准符合备案执行标准状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"删除标志\" prop=\"delFlag\">\r\n          <el-input v-model=\"form.delFlag\" placeholder=\"请输入删除标志\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input\r\n            v-model=\"form.remark\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"是否可生产\" prop=\"canProduce\">\r\n          <el-input v-model=\"form.canProduce\" placeholder=\"请输入是否可生产\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"是否可生产备注\" prop=\"canProduceRemark\">\r\n          <el-input\r\n            v-model=\"form.canProduceRemark\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"是否可出库\" prop=\"canDeliver\">\r\n          <el-input v-model=\"form.canDeliver\" placeholder=\"请输入是否可出库\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"是否可出库备注\" prop=\"canDeliverRemark\">\r\n          <el-input\r\n            v-model=\"form.canDeliverRemark\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listAudit,\r\n  getAudit,\r\n  delAudit,\r\n  addAudit,\r\n  updateAudit,\r\n} from \"@/api/qc/audit\";\r\n\r\nexport default {\r\n  name: \"Audit\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 产品准入检查表格数据\r\n      auditList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        productCode: null,\r\n        laboratoryCode: null,\r\n        manufacturer: null,\r\n        productName: null,\r\n        plannedProductionDate: null,\r\n        deliveryDate: null,\r\n        productType: null,\r\n        registrationCompletionHoverTip: null,\r\n        canProduce: null,\r\n        canDeliver: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        productCode: [\r\n          { required: true, message: \"产品代码不能为空\", trigger: \"blur\" },\r\n        ],\r\n        productName: [\r\n          { required: true, message: \"产品名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询产品准入检查列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listAudit(this.queryParams).then((response) => {\r\n        this.auditList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        productId: null,\r\n        productCode: null,\r\n        laboratoryCode: null,\r\n        manufacturer: null,\r\n        productName: null,\r\n        spec: null,\r\n        plannedProductionDate: null,\r\n        orderQuantity: null,\r\n        deliveryDate: null,\r\n        productType: null,\r\n        formulaStabilityReport: null,\r\n        formulaStabilityReportHoverTip: null,\r\n        formulaFeasibilityAssessment: null,\r\n        formulaFeasibilityAssessmentHoverTip: null,\r\n        standardFormulaProcess: null,\r\n        moldToolConfirmation: null,\r\n        moldToolConfirmationHoverTip: null,\r\n        fillingPackagingFeasibility: null,\r\n        fillingPackagingFeasibilityHoverTip: null,\r\n        fillingPackagingSop: null,\r\n        finishedProductStandard: null,\r\n        qualityAgreement: null,\r\n        qualityAgreementHoverTip: null,\r\n        packagingMaterialStandard: null,\r\n        liquidSample: null,\r\n        packagingMaterialSample: null,\r\n        finishedProductSample: null,\r\n        excessivePackagingConfirmation: null,\r\n        excessivePackagingConfirmationHoverTip: null,\r\n        registrationCompletion: null,\r\n        registrationCompletionHoverTip: null,\r\n        formulaProcessConsistency: null,\r\n        formulaProcessConsistencyHoverTip: null,\r\n        documentationConsistency: null,\r\n        documentationConsistencyHoverTip: null,\r\n        internalStandardCompliance: null,\r\n        delFlag: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null,\r\n        canProduce: null,\r\n        canProduceRemark: null,\r\n        canDeliver: null,\r\n        canDeliverRemark: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加产品准入检查\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      getAudit(id).then((response) => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改产品准入检查\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateAudit(this.form).then((response) => {\r\n              this.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addAudit(this.form).then((response) => {\r\n              this.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$confirm('是否确认删除产品准入检查编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delAudit(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"qc/audit/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `audit_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n  },\r\n};\r\n</script>\r\n"]}]}