import request from '@/utils/request'

// 查询对账运费订单商品列表
export function listGoodsFreight(query) {
  return request({
    url: '/order/goodsFreight/list',
    method: 'get',
    params: query
  })
}

// 查询对账运费订单商品详细
export function getGoodsFreight(id) {
  return request({
    url: '/order/goodsFreight/' + id,
    method: 'get'
  })
}

// 新增对账运费订单商品
export function addGoodsFreight(data) {
  return request({
    url: '/order/goodsFreight',
    method: 'post',
    data: data
  })
}

// 修改对账运费订单商品
export function updateGoodsFreight(data) {
  return request({
    url: '/order/goodsFreight',
    method: 'put',
    data: data
  })
}

// 删除对账运费订单商品
export function delGoodsFreight(id) {
  return request({
    url: '/order/goodsFreight/' + id,
    method: 'delete'
  })
}

// 导出对账运费订单商品
export function exportGoodsFreight(query) {
  return request({
    url: '/order/goodsFreight/export',
    method: 'get',
    params: query
  })
}

export function allGoodsFreight(query) {
  return request({
    url: '/order/goodsFreight/all',
    method: 'get',
    params: query
  })
}
