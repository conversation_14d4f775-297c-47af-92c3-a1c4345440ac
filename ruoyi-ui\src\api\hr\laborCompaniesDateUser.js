import request from '@/utils/request'

export function listLaborCompaniesDateUser(query) {
  return request({
    url: '/hr/laborCompaniesDateUser/list',
    method: 'get',
    params: query
  })
}

export function getLaborCompaniesDateUser(id) {
  return request({
    url: '/hr/laborCompaniesDateUser/' + id,
    method: 'get'
  })
}

export function addLaborCompaniesDateUser(data) {
  return request({
    url: '/hr/laborCompaniesDateUser',
    method: 'post',
    data: data
  })
}

export function updateLaborCompaniesDateUser(data) {
  return request({
    url: '/hr/laborCompaniesDateUser',
    method: 'put',
    data: data
  })
}

export function delLaborCompaniesDateUser(ids) {
  return request({
    url: '/hr/laborCompaniesDateUser/' + ids,
    method: 'delete'
  })
}

export function allLaborUserByDate(query) {
  return request({
    url: '/hr/laborCompaniesDateUser/allDateUser',
    method: 'get',
    params: query
  })
}

export function addLaborDateUser(data) {
  return request({
    url: '/hr/laborCompaniesDateUser/addDateUser',
    method: 'post',
    data
  })
}

export function exportLaborDateUser(query) {
  return request({
    url: '/hr/laborCompaniesDateUser/export',
    method: 'get',
    params: query
  })
}

export function allLaborCompaniesDateUser(query) {
  return request({
    url: '/hr/laborCompaniesDateUser/all',
    method: 'get',
    params: query
  })
}
