import request from '@/utils/request'

// 查询展品订单商品列表
export function listExhibitsOrderGoods(query) {
  return request({
    url: '/resource/exhibitsOrderGoods/list',
    method: 'get',
    params: query
  })
}

// 查询展品订单商品详细
export function getExhibitsOrderGoods(id) {
  return request({
    url: '/resource/exhibitsOrderGoods/' + id,
    method: 'get'
  })
}

// 新增展品订单商品
export function addExhibitsOrderGoods(data) {
  return request({
    url: '/resource/exhibitsOrderGoods',
    method: 'post',
    data: data
  })
}

// 修改展品订单商品
export function updateExhibitsOrderGoods(data) {
  return request({
    url: '/resource/exhibitsOrderGoods',
    method: 'put',
    data: data
  })
}

// 删除展品订单商品
export function delExhibitsOrderGoods(id) {
  return request({
    url: '/resource/exhibitsOrderGoods/' + id,
    method: 'delete'
  })
}

// 导出展品订单商品
export function exportExhibitsOrderGoods(query) {
  return request({
    url: '/resource/exhibitsOrderGoods/export',
    method: 'get',
    params: query
  })
}

export function allExhibitsOrderGoods(query) {
  return request({
    url: '/resource/exhibitsOrderGoods/all',
    method: 'get',
    params: query
  })
}
