import request from '@/utils/request'

// 查询调研问卷列表
export function listForm(query) {
  return request({
    url: '/gx/form/list',
    method: 'get',
    params: query
  })
}

// 查询调研问卷详细
export function getForm(id) {
  return request({
    url: '/gx/form/' + id,
    method: 'get'
  })
}

// 新增调研问卷
export function addForm(data) {
  return request({
    url: '/gx/form',
    method: 'post',
    data: data
  })
}

// 修改调研问卷
export function updateForm(data) {
  return request({
    url: '/gx/form',
    method: 'put',
    data: data
  })
}

// 删除调研问卷
export function delForm(id) {
  return request({
    url: '/gx/form/' + id,
    method: 'delete'
  })
}

// 导出调研问卷
export function exportForm(query) {
  return request({
    url: '/gx/form/export',
    method: 'get',
    params: query
  })
}

export function sendGxForm(data) {
  return request({
    url: '/gx/form/sendForm',
    method: 'post',
    data,
  })
}

export function allForm(query) {
  return request({
    url: '/gx/form/all',
    method: 'get',
    params: query
  })
}

export function copyForm(id) {
  return request({
    url: '/gx/form/copy/' + id,
    method: 'post'
  })
}
