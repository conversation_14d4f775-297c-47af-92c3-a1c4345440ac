<template>
  <div v-loading="loading" >
    <el-row>
      <el-col :span="8">
        <el-row >
          <el-col :span="12" class="cell-wrapper">
            <div class="label">计划日期</div>
            <div class="content">{{form.workDate}}</div>
          </el-col>
          <el-col :span="12" class="cell-wrapper">
            <div class="label">班次</div>
            <div class="content">{{selectOptionsLabel(sailingsOptions, form.sailings)}}</div>
          </el-col>
          <el-col :span="12" class="cell-wrapper">
            <div class="label">区域</div>
            <div class="content">{{form.areaNo}}</div>
          </el-col>
          <el-col :span="12" class="cell-wrapper">
            <div class="label">设备</div>
            <div class="content">{{form.equipmentNo}}</div>
          </el-col>
          <el-col :span="12" class="cell-wrapper">
            <div class="label" style="width: 100px">线长</div>
            <div class="content">{{ form.lineLeader }}</div>
          </el-col>
          <el-col :span="12" class="cell-wrapper">
            <div class="label">生产数量</div>
            <div class="content">{{form.productNums}}</div>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="8" class="header-wrapper" >
        <div class="header-title">{{form.code}}</div>
        <div class="header-img"></div>
        <div class="header-text">{{form.startTime}}~{{form.endTime}}</div>
      </el-col>
      <el-col :span="8" >
        <el-row >
          <el-col :span="24" class="cell-wrapper">
            <div class="label">品名</div>
            <div class="content">{{form.productName}}</div>
          </el-col>
          <el-col :span="12" class="cell-wrapper">
            <div class="label">品号</div>
            <div class="content">{{form.productNo}}</div>
          </el-col>
          <el-col :span="12" class="cell-wrapper">
            <div class="label" style="width: 100px">客户订单号</div>
            <div class="content">{{ form.customerOrderNo }}</div>
          </el-col>
          <el-col :span="12" class="cell-wrapper">
            <div class="label" style="width: 100px">客户名称</div>
            <div class="content">{{ form.customerOrderNo }}</div>
          </el-col>
          <el-col :span="12" class="cell-wrapper">
            <div class="label">
              产出占比
              <el-tooltip content="良品量 / 工单量" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
            <div class="content" v-if="schedule.planNums">{{toPercent(divide(form.productNums,schedule.planNums))}}</div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>

    <el-tabs v-model="currentTab" type="border-card" style="margin-top: 20px" @tab-click="tabChange" >
      <el-tab-pane label="人员/工时" lazy name="person" key="person" >
        <MesLogArea
          v-if="form.minutes && form.inNums"
          :form="form"
          :mes-lot-wait-list="mesLotWaitList"
          :lot-logs="lotLogs"
          :out-array="outArray"
          :user-array="userArray"
        />
      </el-tab-pane>
      <el-tab-pane label="工艺标准" lazy name="standard" key="standard" >
        <ProcessTable
          :project="project"
          :bom-data="bomData"
          :bom-tree="bomTree"
          :readonly="true" />
      </el-tab-pane>
      <el-tab-pane key="material" label="物料" lazy name="material">
        <MesLogMaterialTabs :form="form" :material-log-list="materialLogArray" />
      </el-tab-pane>
      <el-tab-pane key="charts" label="产线报表" lazy name="charts" >
        <MesLogDataCharts ref="mesDataCharts" :diff-data-array="diffDataArray" />
      </el-tab-pane>
      <el-tab-pane key="equipment" label="数采看板" lazy name="equipment" >

      </el-tab-pane>
      <el-tab-pane key="schedule" label="工单维度分摊" lazy name="schedule" >
        <MesLogScheduleTable
          :plan="form"
          :bcp-list="bcpList"
          :bcp-array="scheduleBcpArray"
          :other-array="scheduleOtherArray"
        />
      </el-tab-pane>
      <el-tab-pane key="report" label="生产记录" lazy name="report" >
        <MesLogProductionLog
          :plan="form"
          :bcp-list="bcpList"
          :bcp-array="bcpArray"
          :other-array="otherArray"
          :material-array="materialArray"
        />
      </el-tab-pane>
      <el-tab-pane key="qc" label="品质检验" lazy name="qc" >
        <MesQc ref="mesQc" :inspection-list="inspectionList" />
      </el-tab-pane>
    </el-tabs>

    <div class="dialog-footer" style="margin-top: 20px" v-if="!readonly" >
      <el-button type="primary" @click="submitForm" size="mini" :loading="btnLoading" >
        确 定
        <el-tooltip content="目前只有物料信息的备注需要保存,其他无需操作" placement="top">
          <i class="el-icon-question"></i>
        </el-tooltip>
      </el-button>
    </div>

  </div>
</template>
<script >

import {
  addMesLog,
  getMesLog,
  updateMesLog
} from "@/api/production/mesLog";
import {allPlanAreaHours} from "@/api/production/planAreaHours";
import {allWipContPartialOut} from "@/api/mes/wipcontPartialout";
import MesLogArea from "@/views/production/mesLog/area.vue";
import {allWipLotLog} from "@/api/mes/mesView";
import ProcessTable from "@/views/production/layout/process/table.vue";
import {getArchive} from "@/api/project/archive";
import {getProject} from "@/api/project/project";
import {getScheduleStandardByCode} from "@/api/order/scheduleStandard";
import MesQc from "@/views/mes/production/qc.vue";
import {allFinishedInspection, allGroupByBatchFinishedInspection} from "@/api/qc/finishedInspection";
import {allGroupByEquipmentNo} from "@/api/mes/wipContMaterialLot";
import {getBomByErpCode, getScheduleMaterial} from "@/api/common/erp";
import MesLogDataCharts from "@/views/production/mesLog/dataCharts.vue";
import {getGzlByParams} from "@/api/sop/gzl";
import {getMaterialLogList} from "@/api/production/mesLog";
import {getScheduleByCode} from "@/api/order/schedule";
import MesLogMaterialTabs from "@/views/production/mesLog/materialTabs.vue";
import MesLogProductionLog from "@/views/production/mesLog/log.vue";
import MesProductionLog from "@/views/mes/production/log.vue";
import MesLogScheduleTable from "@/views/production/mesLog/scheduleTable.vue";

export default {
  name: 'mesLogSave',
  components: {
    MesLogScheduleTable,
    MesProductionLog,
    MesLogProductionLog,
    MesLogMaterialTabs,
    MesLogDataCharts,
    MesQc,
    ProcessTable,
    MesLogArea
  },
  data() {
    return {
      loading: false,
      btnLoading: false,
      readonly: false,
      form: {},
      rules: {},
      currentTab: '',
      sailingsOptions: [
        {label: '白班',value: '0'},
        {label: '晚班',value: '1'},
      ],
      outArray: [],
      lotLogs: [],
      mesLotWaitList: [],
      userArray: [],
      project: {},
      bomData: [],
      bomTree: [],
      diffDataArray: [],
      diffDataOptions: {},
      inspectionList: [],
      qcOptions: {},
      materialLogArray: [],
      scheduleStandard: {},
      materialArray: [],
      schedule: {},
      bcpList: [],
      bcpArray: [],
      otherArray: [],
      scheduleBcpArray: [],
      scheduleOtherArray: [],
    }
  },
  async created() {
  },
  methods: {
    async tabChange() {
      await this.$nextTick()
      if(this.currentTab === 'person') {

      } else if(this.currentTab === 'charts') {
        await this.reduceDiffChart()
      } else if(this.currentTab === 'qc') {
        await this.reduceQcChart()
      }
    },
    cancel() {
      this.$parent.$parent.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        id: null,
        code: null,
        workDate: null,
        sailings: null,
        areaNo: null,
        lineLeader: null,
        minutes: null,
        inNums: null,
        goodsNums: null,
        badNums: null,
      };
      this.resetForm("form")
      this.project = {}
      this.bomData = []
      this.bomTree = []
      this.materialLogArray = []
      this.outArray = []
      this.lotLogs = []
      this.mesLotWaitList = []
      this.userArray = []
      this.diffDataArray = []
      this.diffDataOptions = {}
      this.inspectionList = []
      this.qcOptions = {}
      this.schedule = {}
      this.scheduleStandard = {}
      this.materialArray = []
      this.bcpList = []
      this.bcpArray = []
      this.otherArray = []
      this.scheduleBcpArray = []
      this.scheduleOtherArray = []
    },
    diffHours(startTime,endTime) {
      if(startTime && endTime) {
        let minutes = this.moment(endTime,'YYYY-MM-DD hh:mm').diff(this.moment(startTime,'YYYY-MM-DD hh:mm'), 'minutes')
        if(minutes) {
          return this.divide(minutes,60).toNumber()
        }
      }
    },
    async reduceQcChart() {
      await this.$nextTick()
      const mesQc = this.$refs.mesQc
      if(mesQc) {
        const qcCharts = mesQc.$refs.qcCharts
        if(qcCharts) {
          await qcCharts.init(this.qcOptions)
        }
      }
    },
    async reduceDiffChart() {
      await this.$nextTick()
      const mesDataCharts = this.$refs.mesDataCharts
      if(mesDataCharts) {
        const diffChart = mesDataCharts.$refs.diffChart
        if(diffChart) {
          await diffChart.init(this.diffDataOptions)
        }
        const timeLineChart = mesDataCharts.$refs.timeLineChart
        if(timeLineChart) {
        }
      }
    },
    async buildProductionLog() {
      const form = this.form
      const materialArray = this.materialArray
      const bomRes = await getBomByErpCode(form.productNo)
      if(bomRes.code === 200) {
        let bcpList = bomRes.data.filter(i => i.mb005 === '103')
        for (const item of bcpList) {
          const gzlParams = {
            md003: item.md003,
            md001: item.md001,
          }
          const gzlRes = await getGzlByParams(gzlParams)
          if(gzlRes.code === 200 && gzlRes.data) {
            const gzl = gzlRes.data
            item.max = gzl.max
            item.avg = gzl.avg
            item.min = gzl.min
          }
        }
        this.bcpList = bcpList
      }

      let res = await getMaterialLogList(form.id)
      if(res){
        const bcpArray = res.bcpArray
        const otherArray = res.otherArray
        const scheduleBcpArray = res.scheduleBcpArray
        const scheduleOtherArray = res.scheduleOtherArray
        console.log(scheduleBcpArray)
        console.log(scheduleOtherArray)
        if(bcpArray){
          this.bcpArray = bcpArray
          for (const o of bcpArray) {
            if(!materialArray.map(i=> i.materialCode).includes(o.materialCode)) {
              materialArray.push({
                materialCode: o.materialCode,
                remark: null,
              })
            }
          }
        }
        if(otherArray){
          this.otherArray = otherArray
          for (const o of otherArray) {
            if(!materialArray.map(i=> i.materialCode).includes(o.materialCode)) {
              materialArray.push({
                materialCode: o.materialCode,
                remark: null,
              })
            }
          }
        }
        if(scheduleBcpArray) {
          this.scheduleBcpArray = scheduleBcpArray
        }
        if(scheduleOtherArray) {
          this.scheduleOtherArray = scheduleOtherArray
        }
      }
    },
    async buildMaterialArray() {
      const form = this.form
      const schedule = this.schedule
      const formRate = this.divide(form.productNums,schedule.planNums)//产出占比
      if(formRate) {
        let scheduleCode = form.scheduleCode
        const materialLogList = await allGroupByEquipmentNo({scheduleCode,workDate: form.workDate})//暂时不算班次
        this.materialLogList = materialLogList
        const arr = scheduleCode.split('-')
        let erpBomData = await getScheduleMaterial({workOrderNo: arr[1], workOrderSingle: arr[0],})

        const scheduleNums = schedule.planNums
        const materialNoSet = new Set()
        for (const log of materialLogList) {
          materialNoSet.add(log.materialNo)
        }

        const materialLogArray = []
        for (const materialNo of materialNoSet) {
          const te010Set = new Set()//在品号维度下再排批次
          let sjBomNum = 0 //bom用量对于工单产品是成品的
          let cb010 = 0//erp损耗率
          let materialName
          let te006//erp单位
          let te005Sum = this.$big(0)//工单发料量小计
          let lotSums = this.$big(0)//批次使用量小计
          let erpSylSum = this.$big(0)//erp品号维度使用量小计
          for (const b of erpBomData) {
            if(b.te004 === materialNo) {
              sjBomNum = Number(b.sjBomNum)
              cb010 = Number(b.cb010)
              materialName = b.te017
              te006 = b.te006

              te005Sum = this.add(te005Sum,b.te005)
              te010Set.add(b.te010)//按照e10的批次来
            }
          }
          for (const log of materialLogList) {
            if(materialNo === log.materialNo) {
              lotSums = this.add(lotSums,log.nums)
            }
          }
          const te010Array = []
          for (const te010 of te010Set) {
            let erpLlNums = 0
            let erpSylNums = 0
            for (const ot of this.otherArray) {
              if(ot.materialCode === materialNo ) {
                for (const t of ot.te010Array) {
                  if(t.te010 === te010) {
                    if(t.gdSylNums) {
                      erpLlNums = Number(t.gdSylNums)
                      erpSylNums = Number(t.gdSylNums)
                    }
                  }
                }
              }
            }
            const equipmentSet = new Set()//品号批次内排设备
            for (const log of materialLogList) {
              if(materialNo === log.materialNo && te010 === log.materialLotNo) {
                equipmentSet.add(log.equipmentNo)
              }
            }
            const equipmentArray = []
            for (const equipmentNo of equipmentSet) {
              let l = {
                equipmentNo,
              }
              for (const log of materialLogList) {
                if(materialNo === log.materialNo && te010 === log.materialLotNo && equipmentNo === log.equipmentNo) {
                  l.nums = log.nums//使用量
                  l.times = log.times//领料次数
                }
              }
              equipmentArray.push(l)
            }
            const llArray = []//erp领料单记录
            for (const b of erpBomData) {
              if(b.te004 === materialNo && b.te010 === te010) {
                let sylRate
                let llBlNums
                let llBlReason
                let scBlNums
                let scBlReason
                for (const ot of this.otherArray) {
                  if(ot.materialCode === materialNo ) {
                    for (const t of ot.te010Array) {
                      if(t.te010 === te010) {
                        sylRate = t.sylRate
                        llBlNums = t.llBlNums
                        llBlReason = t.llBlReason
                        scBlNums = t.scBlNums
                        scBlReason = t.scBlReason
                      }
                    }
                  }
                }
                llArray.push({
                  te001: b.te001,//领料通知单别
                  te002: b.te002,//领料通知单号
                  te008: b.te008,//仓库
                  me003: b.me003,//最早入库日期
                  te013: b.te013,//领料说明
                  sylRate,//使用量比例(生产批批次品号使用量/工单品号批次使用量)
                  llBlNums,//来料不良
                  llBlReason,
                  scBlNums,//生产不良
                  scBlReason,
                })
              }
            }
            te010Array.push({
              te010,
              erpLlNums: erpLlNums,//领料量
              erpSylNums: erpSylNums,//使用量
              equipmentArray,
              llArray,
            })
            erpSylSum = this.add(erpSylSum,erpSylNums)
          }
          if(scheduleNums) {
            let gdXql = this.multiply(scheduleNums,sjBomNum)
            let lotPlanXql = this.multiply(form.productNums,sjBomNum)//生产记录实际生产数量 计算 本线需求量
            let lotLlXql = this.multiply(form.productNums,sjBomNum)//批次生产量计算 理论使用量
            let loss = this.subtract(lotSums, lotLlXql)
            const erpRateNums = this.multiply(erpSylSum,formRate).toNumber()
            materialLogArray.push({
              materialNo,
              materialName,
              sjBomNum,
              cb010,
              te006,
              gdXql: gdXql.toNumber(),
              te005Sum: te005Sum.toNumber(),
              xfCyRate: this.divide(this.subtract(te005Sum, gdXql), gdXql).toNumber(),
              lotXql: lotPlanXql.toNumber(),
              lotLlXql: lotLlXql.toNumber(),
              lotSums: lotSums.toNumber(),
              loss: loss.toNumber(),
              lotRate: this.divide(loss, lotLlXql).toNumber(),
              erpSylSum,
              erpRateNums,
              diffNums: this.subtract(lotSums, erpRateNums).toNumber(),
              te010Array,
            })
          }
        }

        this.materialLogArray = materialLogArray
      }
    },
    async buildDiffData() {
      const form = this.form
      const scheduleStandard = this.scheduleStandard

      const productNums = form.productNums
      const actualHours = this.divide(form.sumMinutes,60)
      const actualDuration = this.diffHours(form.startTime,form.endTime)
      if(actualDuration && scheduleStandard.nums) {
        const actualProductivity = this.divide(productNums,actualDuration).toNumber()
        const actualHoursRate = this.divide(productNums, actualHours).toNumber()
        const diffDataArray = []

        let standardHours = 0
        let standardPersonNums = 0
        let standardHoursRate = 0
        if(form.opNo === 'GB') {//一阶 取少的
          standardPersonNums = scheduleStandard.nums
          standardHoursRate = scheduleStandard.costHoursRate
        } else {
          standardPersonNums = scheduleStandard.costNums
          standardHoursRate = scheduleStandard.costHoursRate
        }
        standardHours = this.multiply(standardHoursRate,form.productNums).toNumber()
        diffDataArray.push(['标准',scheduleStandard.productivity,standardPersonNums,standardHours,standardHoursRate])
        diffDataArray.push(['实际',actualProductivity,form.sumNums,actualHours,actualHoursRate])
        diffDataArray.push([
          '差异',
          this.subtract(actualProductivity,scheduleStandard.productivity).toNumber(),
          this.subtract(form.sumNums,standardPersonNums).toNumber(),
          this.subtract(actualHours,standardHours).toNumber(),
          this.subtract(actualHoursRate,standardHoursRate).toNumber(),
        ])
        this.diffDataArray = diffDataArray

        this.diffDataOptions =  {
          title: {
            text: '批次维度数据对比'
          },
          legend: {
            data: ['预估', '实际']
          },
          radar: {
            indicator: [
              { name: '生产产能', max: Math.max(scheduleStandard.productivity, actualProductivity)*1.2 },
              { name: '生产人数', max: Math.max(standardPersonNums, form.sumNums)*1.2 },
              { name: '生产工时', max: Math.max(standardHours, actualHours)*1.2 },
              { name: '工时产出率', max: Math.max(standardHoursRate, actualHoursRate)*1.2 }
            ]
          },
          series: [
            {
              name: '预估 vs 实际',
              type: 'radar',
              data: [
                {
                  value: diffDataArray[0].slice(1),
                  name: '预估'
                },
                {
                  value: diffDataArray[1].slice(1),
                  name: '实际'
                }
              ]
            }
          ]
        }
        // await this.reduceDiffChart()
      }
    },
    async importArchive(archiveId) {
      let archiveRes = await getArchive(archiveId)
      if(archiveRes.code === 200 && archiveRes.data) {
        let archive = archiveRes.data

        const projectRes = await getProject(archive.projectId)
        const project = projectRes.data

        project.type = archive.type

        if(['0','1','3'].includes(archive.type)) {
          project.bomResource = archive.bomResource
          project.bomType = archive.bomType
          project.resourceFinishedGoodsId = archive.resourceFinishedGoodsId
          project.erpPrice = archive.erpPrice
          project.bomArray = archive.bomArray//这里就是字符串
          project.erpCode = archive.erpCode
          if(archive.cubicleArray) {
            const cubicleArray = JSON.parse(archive.cubicleArray)
            project.cubicleArray = cubicleArray

            const imgs = []
            const videos = []
            for (const item of cubicleArray) {
              for (const second of item.sectionArray) {
                for (const w of second.workTypeArray) {
                  if(w.homeworkImgs) {
                    imgs.push(...w.homeworkImgs.split(','))
                  }
                  if(w.homeworkVideos.length) {
                    videos.push(...w.homeworkVideos)
                  }
                }
              }
            }
            this.imgs = imgs
            this.videos = videos
          } else {
            project.cubicleArray = []
          }
        }
        this.bomData = JSON.parse(archive.bomArray)
        this.project = project
      }
    },
    async buildQcLogs() {
      const form = this.form
      const arr = form.scheduleCode.split('-')
      const inspectionList = await allFinishedInspection({singleCategory: arr[0],workOrderNo:arr[1]})//质检
      for(let item of inspectionList){
        let erpColumn = item.erpColumn;
        if(erpColumn){
          let obj = JSON.parse(erpColumn);
          item.tg030 = obj.tg030;
        }
      }
      this.inspectionList = inspectionList

      const groupInspectionList = await allGroupByBatchFinishedInspection({singleCategory: arr[0],workOrderNo:arr[1]})

      const rawData = [
        groupInspectionList.map(i=> i.batchNums - i.samplingNums),
        groupInspectionList.map(i=> i.samplingNums),
      ];
      const totalData = [];
      for (let i = 0; i < rawData[0].length; ++i) {
        let sum = 0;
        for (let j = 0; j < rawData.length; ++j) {
          sum += rawData[j][i];
        }
        totalData.push(sum);
      }
      const grid = {
        left: 100,
        right: 100,
        top: 50,
        bottom: 50
      };
      const series = [
        '未抽样',
        '已抽样',
      ].map((name, sid) => {
        return {
          name,
          type: 'bar',
          stack: 'total',
          barWidth: '60%',
          label: {
            show: true,
            formatter: (params) => Math.round(params.value * 1000) / 10 + '%'
          },
          data: rawData[sid].map((d, did) =>
            totalData[did] <= 0 ? 0 : d / totalData[did]
          )
        }
      })
      this.qcOptions = {
        legend: {
          selectedMode: false
        },
        grid,
        yAxis: {
          type: 'value'
        },
        xAxis: {
          type: 'category',
          data: groupInspectionList.map(i=> i.batchNo)
        },
        series
      }
    },
    async init(id) {
      this.loading = true
      const res = await getMesLog(id)
      const form = res.data
      if(form.materialArray) {
        this.materialArray = JSON.parse(form.materialArray)
      }
      this.form = form

      const scheduleCode = form.scheduleCode
      const arr = scheduleCode.split('-')
      const scheduleRes = await getScheduleByCode(arr[0] + "-" + arr[1])
      if(scheduleRes.code === 200) {
        const schedule = scheduleRes.data
        this.schedule = schedule
      }

      const scheduleStandardRes = await getScheduleStandardByCode(form.productNo)
      if(scheduleStandardRes.code === 200) {
        const scheduleStandard = scheduleStandardRes.data
        this.scheduleStandard = scheduleStandard
        if(scheduleStandard) {
          await this.importArchive(scheduleStandard.archiveId)
          await this.buildDiffData()
        }
      }

      const outList = await allWipContPartialOut({
        areano: form.areaNo,
        eventDate: form.workDate,
        equipmentno: form.equipmentNo,
        scheduleCode: form.scheduleCode,
      })
      let inNums = this.$big(0)
      let goodsNums = this.$big(0)
      let badNums = this.$big(0)
      this.outArray = outList.map(i=> {
        return {
          lotNo: i.lotno,
          opNo: i.opno,
          eventTime: i.eventtime,
          userNo: i.userno,
          areaNo: i.areano,
          inNums: i.inputqty,//产出量
          goodsNums: i.goodqty,//良品量
          badNums: i.scrapqty,//良品量
          equipmentNo: i.equipmentno,
          userName: i.userName,
        }
      })//重命名属性名称,方便理解语义

      for (const item of this.outArray) {
        inNums = this.add(inNums,item.inNums)
        goodsNums = this.add(goodsNums,item.goodsNums)
        badNums = this.add(badNums,item.badNums)
      }

      form.inNums = inNums.toNumber()
      form.goodsNums = goodsNums.toNumber()
      form.badNums = badNums.toNumber()

      const lotLogs = await allWipLotLog({
        areaNo: form.areaNo,
        createTime: form.workDate,
        equipmentNo: form.equipmentNo,
        scheduleCode: form.scheduleCode,
      })
      this.lotLogs = lotLogs

      const planAreaHourList = await allPlanAreaHours({
        areaNo: form.areaNo,
        equipmentNo: form.equipmentNo,
        workDate: form.workDate,
        sailings: form.sailings,
        scheduleCode: form.scheduleCode,
      })

      const userArray = []
      const userSet = new Set(planAreaHourList.map(i=> i.userCode))
      let sumMinutes = this.$big(0)
      for (const userCode of userSet) {
        let nickName
        let minutes = this.$big(0)
        const array = []
        for (const h of planAreaHourList) {
          if(userCode === h.userCode) {
            nickName = h.nickName
            array.push(h)
            minutes = this.add(minutes,h.minutes)
          }
        }
        userArray.push({
          userCode,
          nickName,
          minutes,
          array,
        })
        sumMinutes = this.add(sumMinutes,minutes)
      }
      form.minutes = sumMinutes.toNumber()
      this.userArray = userArray

      await this.buildProductionLog()//这个里面的otherArray,buildMaterialArray用到了
      await this.buildMaterialArray()//这里需要用到 areaList 的数据
      await this.buildQcLogs()

      this.loading = false
    },
    async submitForm() {
      let form = Object.assign({}, this.form)

      form.materialArray = JSON.stringify(this.materialArray)

      if (form.id != null) {
        try {
          this.btnLoading = true
          await updateMesLog(form)
          this.btnLoading = false
          this.msgSuccess("修改成功")
          this.$parent.$parent.open = false
          await this.$parent.$parent.getList()
        } catch (e) {
          this.btnLoading = false
        }
      }
    },
  },
}
</script>
<style scoped lang="scss">
.cell-wrapper {
  .label {
    width: 80px;
  }
}

.header-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .header-title {
    font-size: 16px;
    font-weight: 700;
  }

  .header-img {
    background: url(~@/assets/images/production/plan/layout/head.gif) no-repeat center center;
    background-size: 100%;
    height: 10vh;
    width: 100%;
  }
}

/* el-tabs */
::v-deep .el-tabs__nav-scroll{
  background-color: #fff;
  padding: 20px 0;
}
::v-deep .el-tabs__nav {
  margin: 0 20px;
  /* 使用rpx没有效果 */
}

::v-deep .el-tabs__nav-scroll {
  padding: 10px;
}

::v-deep .el-tabs__content {
  padding-top: 0;
}
</style>
