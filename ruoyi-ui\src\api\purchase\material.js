import request from '@/utils/request'

//查询原料列表
export function materialList(query) {
  return request({
    url: '/material/list',
    method: 'get',
    params: query
  })
}

// 查询原料详细
export function getMaterial(id) {
  return request({
    url: '/material/' + id,
    method: 'get'
  })
}

// 查询原料详细
export function getMaterialPrice(id) {
  return request({
    url: '/material/getMaterialPriceInfo/' + id,
    method: 'get'
  })
}

// 查询原料生产商内容
export function getMaterialProducer(id) {
  return request({
    url: '/material/getMaterialProducerInfo/' + id,
    method: 'get'
  })
}

// 查询原料变动历史记录
export function getMaterialChangeLog(id) {
  return request({
    url: '/material/getMaterialChangeLog/' + id,
    method: 'get'
  })
}

//导入原料价格基础信息
export function importMaterialPrice(id) {
  return request({
    url: '/material/importMaterialPriceInfo',
    method: 'get'
  })
}



// 新增原料
export function addMaterial(data) {
  return request({
    url: '/material',
    method: 'post',
    data: data
  })
}

// 修改原料
export function updateMaterial(data) {
  return request({
    url: '/material',
    method: 'put',
    data: data
  })
}

// 新增原料
export function addPackageMaterial(data) {
  return request({
    url: '/material/addPackageMaterial',
    method: 'post',
    data: data
  })
}

// 修改原料
export function updatePackageMaterial(data) {
  return request({
    url: '/material/updatePackageMaterial',
    method: 'put',
    data: data
  })
}

//查询原料信息
export function getMaterialDataInfo(query) {
  return request({
    url: '/material/getMaterialDataInfo',
    method: 'get',
    params: query
  })
}

// 导出原料价格
export function handleExportBCodePriceInfo(query) {
  return request({
    url: '/material/exportBCodePriceInfo',
    method: 'get',
    params: query
  })
}

// 导出原料价格
export function handleExportMaterialPriceInfo(query) {
  return request({
    url: '/material/exportMaterialPriceInfo',
    method: 'get',
    params: query
  })
}

// 导出包材价格
export function handleExportPackageMaterialPriceInfo(query) {
  return request({
    url: '/material/exportPackageMaterialPriceInfo',
    method: 'get',
    params: query
  })
}
