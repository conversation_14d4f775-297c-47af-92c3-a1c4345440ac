import request from '@/utils/request'

// 查询ERP入库信息(委外入库)列表
export function listErpOutRkMocti(query) {
  return request({
    url: '/order/erpOutRkMocti/list',
    method: 'get',
    params: query
  })
}

// 查询ERP入库信息(委外入库)详细
export function getErpOutRkMocti(id) {
  return request({
    url: '/order/erpOutRkMocti/' + id,
    method: 'get'
  })
}

// 新增ERP入库信息(委外入库)
export function addErpOutRkMocti(data) {
  return request({
    url: '/order/erpOutRkMocti',
    method: 'post',
    data: data
  })
}

// 修改ERP入库信息(委外入库)
export function updateErpOutRkMocti(data) {
  return request({
    url: '/order/erpOutRkMocti',
    method: 'put',
    data: data
  })
}

// 导出ERP入库信息(委外入库)
export function exportErpOutRkMocti(query) {
  return request({
    url: '/order/erpOutRkMocti/export',
    method: 'get',
    params: query
  })
}
