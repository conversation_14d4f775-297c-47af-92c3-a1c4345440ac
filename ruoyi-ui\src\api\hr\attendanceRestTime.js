import request from '@/utils/request'

// 查询工厂标准休息时间列表
export function listAttendanceRestTime(query) {
  return request({
    url: '/hr/attendanceRestTime/list',
    method: 'get',
    params: query
  })
}

// 查询工厂标准休息时间详细
export function getAttendanceRestTime(id) {
  return request({
    url: '/hr/attendanceRestTime/' + id,
    method: 'get'
  })
}

// 新增工厂标准休息时间
export function addAttendanceRestTime(data) {
  return request({
    url: '/hr/attendanceRestTime',
    method: 'post',
    data: data
  })
}

// 修改工厂标准休息时间
export function updateAttendanceRestTime(data) {
  return request({
    url: '/hr/attendanceRestTime',
    method: 'put',
    data: data
  })
}

// 删除工厂标准休息时间
export function delAttendanceRestTime(id) {
  return request({
    url: '/hr/attendanceRestTime/' + id,
    method: 'delete'
  })
}

// 导出工厂标准休息时间
export function exportAttendanceRestTime(query) {
  return request({
    url: '/hr/attendanceRestTime/export',
    method: 'get',
    params: query
  })
}

export function allAttendanceRestTime(query) {
  return request({
    url: '/hr/attendanceRestTime/all',
    method: 'get',
    params: query
  })
}
