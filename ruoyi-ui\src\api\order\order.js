import request from '@/utils/request'

// 查询订单列表
export function listOrder(query) {
  return request({
    url: '/order/list',
    method: 'get',
    params: query
  })
}

// 查询订单详细
export function getOrder(id) {
  return request({
    url: '/order/' + id,
    method: 'get'
  })
}


// 查询订单详细
export function queryOrderDetail(id) {
  return request({
    url: '/order/queryOrderDetail/' + id,
    method: 'get'
  })
}

// 新增订单
export function addOrder(data) {
  return request({
    url: '/order',
    method: 'post',
    data: data
  })
}

// 修改订单
export function updateOrder(data) {
  return request({
    url: '/order',
    method: 'put',
    data: data
  })
}

// 修改订单
export function updateOtherOrder(data) {
  return request({
    url: '/order/other',
    method: 'put',
    data: data
  })
}

// 修改订单
export function updateOrderFile(data) {
  return request({
    url: '/order/updateOrderFile',
    method: 'put',
    data: data
  })
}

// 修改订单状态
export function updateOrderStatus(data) {
  return request({
    url: '/order/updateOrderStatus',
    method: 'put',
    data: data
  })
}

// 修改其它订单状态
export function updateOtherOrderStatus(data) {
  return request({
    url: '/order/updateOtherOrderStatus',
    method: 'put',
    data: data
  })
}

// 修改关联订单
export function updateOtherOrderRelations(data) {
  return request({
    url: '/order/updateOtherOrderRelations',
    method: 'put',
    data: data
  })
}

// 修改订单状态
export function updateOtherOrderCustomerYw(data) {
  return request({
    url: '/order/updateOtherOrderCustomerYw',
    method: 'put',
    data: data
  })
}

// 关联erp订单号
export function updateErpOrderRelationData(data) {
  return request({
    url: '/order/updateErpOrderRelationData',
    method: 'put',
    data: data
  })
}

// 删除订单
export function delOrder(id) {
  return request({
    url: '/order/' + id,
    method: 'delete'
  })
}

// 导出订单
export function exportOrder(query) {
  return request({
    url: '/order/export',
    method: 'get',
    params: query
  })
}

export function updateStatusOrder(data) {
  return request({
    url: '/order/updateStatus',
    method: 'put',
    data: data
  })
}

export function revokeOrder(data) {
  return request({
    url: '/order/revokeOrder',
    method: 'put',
    data: data
  })
}

export function allOrder(query) {
  return request({
    url: '/order/all',
    method: 'get',
    params: query
  })
}

export function allCustomerOrder(query) {
  return request({
    url: '/order/allCustomerAll',
    method: 'get',
    params: query
  })
}

export function allUnRkOrder(query) {
  return request({
    url: '/order/allUnRkOrder',
    method: 'get',
    params: query
  })
}

export function allUnCkOrder(query) {
  return request({
    url: '/order/allUnCkOrder',
    method: 'get',
    params: query
  })
}

export function allUnDzOrder(query) {
  return request({
    url: '/order/allUnDzOrder',
    method: 'get',
    params: query
  })
}

export function queryRelationOrderGoodsData(query) {
  return request({
    url: '/order/relationOrderGoodsData',
    method: 'get',
    params: query
  })
}

export function queryRelationDetailOrderGoodsData(query) {
  return request({
    url: '/order/queryRelationDetailOrderGoodsData',
    method: 'get',
    params: query
  })
}

export function otherListOrder(query) {
  return request({
    url: '/order/otherList',
    method: 'get',
    params: query
  })
}

export function addOtherOrder(data) {
  return request({
    url: '/order/other',
    method: 'post',
    data: data
  })
}

export function orderAll(query) {
  return request({
    url: '/order/allOrder',
    method: 'get',
    params: query
  })
}

export function countOrder(query) {
  return request({
    url: '/order/orderCount',
    method: 'get',
    params: query
  })
}

export function listRemindUnBjOrder(query) {
  return request({
    url: '/order/remindUnBjOrder',
    method: 'get',
    params: query
  })
}

export function listUnSignList(query) {
  return request({
    url: '/order/unSignList',
    method: 'get',
    params: query
  })
}

export function getOrderByErpCode(data) {
  return request({
    url: '/order/getOrderByErpCode',
    method: 'post',
    data
  })
}
