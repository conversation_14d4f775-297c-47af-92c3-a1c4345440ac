import request from '@/utils/request'

// 查询检测仪器列表
export function listJcyq(query) {
  return request({
    url: '/qc/jcyq/list',
    method: 'get',
    params: query
  })
}

// 查询检测仪器详细
export function getJcyq(id) {
  return request({
    url: '/qc/jcyq/' + id,
    method: 'get'
  })
}

// 新增检测仪器
export function addJcyq(data) {
  return request({
    url: '/qc/jcyq',
    method: 'post',
    data: data
  })
}

// 修改检测仪器
export function updateJcyq(data) {
  return request({
    url: '/qc/jcyq',
    method: 'put',
    data: data
  })
}

// 删除检测仪器
export function delJcyq(id) {
  return request({
    url: '/qc/jcyq/' + id,
    method: 'delete'
  })
}

// 导出检测仪器
export function exportJcyq(query) {
  return request({
    url: '/qc/jcyq/export',
    method: 'get',
    params: query
  })
}

export function jcyqAll(query) {
  return request({
    url: '/qc/jcyq/all',
    method: 'get',
    params: query
  })
}
