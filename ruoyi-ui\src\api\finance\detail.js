import request from '@/utils/request'

// 查询财务到账记录明细列表
export function listDetail(query) {
  return request({
    url: '/finance/detail/list',
    method: 'get',
    params: query
  })
}

// 查询财务到账记录明细详细
export function getDetail(id) {
  return request({
    url: '/finance/detail/' + id,
    method: 'get'
  })
}

// 新增财务到账记录明细
export function addDetail(data) {
  return request({
    url: '/finance/detail',
    method: 'post',
    data: data
  })
}

// 修改财务到账记录明细
export function updateDetail(data) {
  return request({
    url: '/finance/detail',
    method: 'put',
    data: data
  })
}

// 删除财务到账记录明细
export function delDetail(id) {
  return request({
    url: '/finance/detail/' + id,
    method: 'delete'
  })
}

// 导出财务到账记录明细
export function exportDetail(query) {
  return request({
    url: '/finance/detail/export',
    method: 'get',
    params: query
  })
}

// 查询财务到账记录明细列表
export function listAllDetail(query) {
  return request({
    url: '/finance/detail/all',
    method: 'get',
    params: query
  })
}
