import request from '@/utils/request'

// 查询模板管理列表
export function listTemplate(query) {
  return request({
    url: '/filemanage/template/list',
    method: 'get',
    params: query
  })
}

export function AlllistTemplate(query) {
  return request({
    url: '/filemanage/template/Alllist',
    method: 'get',
    params: query
  })
}

// 查询模板管理详细
export function getTemplate(id) {
  return request({
    url: '/filemanage/template/' + id,
    method: 'get'
  })
}

// 新增模板管理
export function addTemplate(data) {
  return request({
    url: '/filemanage/template',
    method: 'post',
    data: data
  })
}

// 修改模板管理
export function updateTemplate(data) {
  return request({
    url: '/filemanage/template',
    method: 'put',
    data: data
  })
}

// 删除模板管理
export function delTemplate(id) {
  return request({
    url: '/filemanage/template/' + id,
    method: 'delete'
  })
}

// 导出模板管理
export function exportTemplate(query) {
  return request({
    url: '/filemanage/template/export',
    method: 'get',
    params: query
  })
}
