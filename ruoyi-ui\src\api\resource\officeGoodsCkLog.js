import request from '@/utils/request'

// 查询办公用品出库记录列表
export function listOfficeGoodsCkLog(query) {
  return request({
    url: '/resource/officeGoodsCkLog/list',
    method: 'get',
    params: query
  })
}

// 查询办公用品出库记录详细
export function getOfficeGoodsCkLog(id) {
  return request({
    url: '/resource/officeGoodsCkLog/' + id,
    method: 'get'
  })
}

// 新增办公用品出库记录
export function addOfficeGoodsCkLog(data) {
  return request({
    url: '/resource/officeGoodsCkLog',
    method: 'post',
    data: data
  })
}

// 修改办公用品出库记录
export function updateOfficeGoodsCkLog(data) {
  return request({
    url: '/resource/officeGoodsCkLog',
    method: 'put',
    data: data
  })
}

// 删除办公用品出库记录
export function delOfficeGoodsCkLog(id) {
  return request({
    url: '/resource/officeGoodsCkLog/' + id,
    method: 'delete'
  })
}

// 导出办公用品出库记录
export function exportOfficeGoodsCkLog(query) {
  return request({
    url: '/resource/officeGoodsCkLog/export',
    method: 'get',
    params: query
  })
}

export function officeGoodsCkLogCkNums(params) {
  return request({
    url: '/resource/officeGoodsCkLog/ckNums',
    method: 'get',
    params
  })
}
