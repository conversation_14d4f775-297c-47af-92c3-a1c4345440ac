import request from '@/utils/request'

export function listPurchasePayUser(query) {
  return request({
    url: '/finance/purchasePayUser/list',
    method: 'get',
    params: query
  })
}


export function listPurchasePayUserReconcilia(query) {
  return request({
    url: '/finance/purchasePayUser/reconciliaList',
    method: 'get',
    params: query
  })
}


export function listAuditPurchasePayUser(query) {
  return request({
    url: '/finance/purchasePayUser/audit',
    method: 'get',
    params: query
  })
}
export function getPurchasePayUser(id) {
  return request({
    url: '/finance/purchasePayUser/' + id,
    method: 'get'
  })
}
export function addPurchasePayUser(data) {
  return request({
    url: '/finance/purchasePayUser',
    method: 'post',
    data: data
  })
}
export function editPurchasePayUser(data) {
  return request({
    url: '/finance/purchasePayUser/edit',
    method: 'post',
    data: data
  })
}

export function submitAudit(data) {
  return request({
    url: '/finance/purchasePayUser/submitAudit',
    method: 'put',
    data: data
  })
}
export function cancelAudit(data) {
  return request({
    url: '/finance/purchasePayUser/cancelAudit',
    method: 'put',
    data: data
  })
}

export function pigeonholePurchasePayUser(data) {
  return request({
    url: '/finance/purchasePayUser/pigeonhole',
    method: 'post',
    data: data
  })
}

export function listPurchasePayUserItem(query) {
  return request({
    url: '/finance/purchasePayUser/item/list',
    method: 'get',
    params: query
  })
}

// 导出付款申请数据
export function exportPurchasePay(query) {
  return request({
    url: '/finance/purchasePayUser/exportPurchasePay',
    method: 'get',
    params: query
  })
}

export function listHistoryPurchasePayUserItem(query) {
  return request({
    url: '/finance/purchasePayUser/historyList',
    method: 'get',
    params: query
  })
}

