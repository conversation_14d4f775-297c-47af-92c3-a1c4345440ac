import request from '@/utils/request'

// 查询项目流程进行列表
export function listExecution(query) {
  return request({
    url: '/project/execution/list',
    method: 'get',
    params: query
  })
}

// 查询项目流程进行列表
export function orderExecutionList(query) {
  return request({
    url: '/project/execution/orderExecutionList',
    method: 'get',
    params: query
  })
}

// 查询项目流程进行列表
export function formulaListExecution(query) {
  return request({
    url: '/project/execution/formulaList',
    method: 'get',
    params: query
  })
}

// 查询项目流程进行列表
export function listExecutionShow(query) {
  return request({
    url: '/project/execution/listExecutionShow',
    method: 'get',
    params: query
  })
}

// 查询项目流程进行列表
export function listAllExecution(query) {
  return request({
    url: '/project/execution/allList',
    method: 'get',
    params: query
  })
}

// 查询项目流程进行详细
export function getExecution(id) {
  return request({
    url: '/project/execution/' + id,
    method: 'get'
  })
}


//获取编码是否冲突
export function judgeConfirmCodeUniqueOpr(data) {
  return request({
    url: '/project/execution/judgeConfirmCodeUniqueOpr',
    method: 'post',
    data: data
  })
}


// 查询项目流程进行详细
export function getOrderExecution(id) {
  return request({
    url: '/project/execution/getOrderExecution/' + id,
    method: 'get'
  })
}

// 新增项目流程进行
export function assginExecutionUserInfo(data) {
  return request({
    url: '/project/execution/assginExecutionUserInfo',
    method: 'post',
    data: data
  })
}

// 新增项目流程进行
export function addExecution(data) {
  return request({
    url: '/project/execution',
    method: 'post',
    data: data
  })
}

// 修改项目流程进行
export function updateExecution(data) {
  return request({
    url: '/project/execution',
    method: 'put',
    data: data
  })
}

// 删除项目流程进行
export function delExecution(id) {
  return request({
    url: '/project/execution/' + id,
    method: 'delete'
  })
}

// 导出项目流程进行
export function exportExecution(query) {
  return request({
    url: '/project/execution/export',
    method: 'get',
    params: query
  })
}

export function myExecutionOrderList(query) {
  return request({
    url: '/project/execution/myExecutionOrderList',
    method: 'get',
    params: query
  })
}

export function executionStatistics(query) {
  return request({
    url: '/project/execution/statistics',
    method: 'get',
    params: query
  })
}

export function executionAuditList(query) {
  return request({
    url: '/project/execution/auditList',
    method: 'get',
    params: query
  })
}

export function auditCustomer(query) {
  return request({
    url: '/project/execution/auditCustomer',
    method: 'get',
    params: query
  })
}

export function exportProjectExecution(query) {
  return request({
    url: '/project/execution/exportExecution',
    method: 'get',
    params: query
  })
}

export function exportProjectFlower(query) {
  return request({
    url: '/project/execution/exportFlower',
    method: 'get',
    params: query
  })
}
