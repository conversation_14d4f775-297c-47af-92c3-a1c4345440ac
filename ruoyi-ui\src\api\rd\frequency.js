import request from '@/utils/request'

// 查询化妆品申请频率列表
export function listFrequency(query) {
  return request({
    url: '/rd/frequency/list',
    method: 'get',
    params: query
  })
}

// 查询化妆品申请频率详细
export function getFrequency(id) {
  return request({
    url: '/rd/frequency/' + id,
    method: 'get'
  })
}

// 新增化妆品申请频率
export function addFrequency(data) {
  return request({
    url: '/rd/frequency',
    method: 'post',
    data: data
  })
}

// 修改化妆品申请频率
export function updateFrequency(data) {
  return request({
    url: '/rd/frequency',
    method: 'put',
    data: data
  })
}

// 删除化妆品申请频率
export function delFrequency(id) {
  return request({
    url: '/rd/frequency/' + id,
    method: 'delete'
  })
}

// 导出化妆品申请频率
export function exportFrequency(query) {
  return request({
    url: '/rd/frequency/export',
    method: 'get',
    params: query
  })
}

export function allFrequency(query) {
  return request({
    url: '/rd/frequency/all',
    method: 'get',
    params: query
  })
}
