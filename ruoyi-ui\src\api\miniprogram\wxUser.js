import request from '@/utils/request'

// 查询微信用户列表
export function listWxUser(query) {
  return request({
    url: '/wx/wxUser/list',
    method: 'get',
    params: query
  })
}

// 查询微信用户列表
export function allBusiness(query) {
  return request({
    url: '/wx/wxUser/allBusiness',
    method: 'get',
    params: query
  })
}

// 查询微信用户详细
export function getWxUser(id) {
  return request({
    url: '/wx/wxUser/' + id,
    method: 'get'
  })
}

// 新增微信用户
export function addWxUser(data) {
  return request({
    url: '/wx/wxUser',
    method: 'post',
    data: data
  })
}

// 修改微信用户
export function updateWxUser(data) {
  return request({
    url: '/wx/wxUser',
    method: 'put',
    data: data
  })
}

// 删除微信用户
export function delWxUser(id) {
  return request({
    url: '/wx/wxUser/' + id,
    method: 'delete'
  })
}

// 导出微信用户
export function exportWxUser(query) {
  return request({
    url: '/wx/wxUser/export',
    method: 'get',
    params: query
  })
}
