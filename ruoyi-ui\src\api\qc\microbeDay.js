import request from '@/utils/request'

// 查询微生物检验日记录列表
export function listMicrobeDay(query) {
  return request({
    url: '/qc/microbeDay/list',
    method: 'get',
    params: query
  })
}

// 查询微生物检验日记录详细
export function getMicrobeDay(id) {
  return request({
    url: '/qc/microbeDay/' + id,
    method: 'get'
  })
}

// 新增微生物检验日记录
export function addMicrobeDay(data) {
  return request({
    url: '/qc/microbeDay',
    method: 'post',
    data: data
  })
}

// 修改微生物检验日记录
export function updateMicrobeDay(data) {
  return request({
    url: '/qc/microbeDay',
    method: 'put',
    data: data
  })
}

// 删除微生物检验日记录
export function delMicrobeDay(id) {
  return request({
    url: '/qc/microbeDay/' + id,
    method: 'delete'
  })
}

// 导出微生物检验日记录
export function exportMicrobeDay(id) {
  return request({
    url: '/qc/microbeDay/export/' + id,
    method: 'get',
  })
}

export function microbeDaySubmitAudit(data) {
  return request({
    url: '/qc/microbeDay/submitAudit',
    method: 'put',
    data: data
  })
}

export function microbeDayCancelAudit(data) {
  return request({
    url: '/qc/microbeDay/cancelAudit',
    method: 'put',
    data: data
  })
}

export function microbeDaySubmitChangeAudit(data) {
  return request({
    url: '/qc/microbeDay/submitChangeAudit',
    method: 'put',
    data: data
  })
}

export function microbeDayCancelChangeAudit(data) {
  return request({
    url: '/qc/microbeDay/cancelChangeAudit',
    method: 'put',
    data: data
  })
}

export function getMicrobeDayByDateAndFactory(query) {
  return request({
    url: '/qc/microbeDay/getByDateAndFactory',
    method: 'get',
    params: query,
  })
}
