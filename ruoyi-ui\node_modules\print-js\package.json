{"name": "print-js", "homepage": "http://printjs.crabbly.com", "description": "A tiny javascript library to help printing from the web.", "version": "1.6.0", "main": "dist/print.js", "types": "src/index.d.ts", "repository": "https://github.com/crabbly/Print.js", "license": "MIT", "devDependencies": {"@babel/core": "7.11.6", "@babel/preset-env": "7.11.5", "babel-loader": "8.1.0", "coveralls": "3.1.0", "css-loader": "4.2.2", "istanbul-instrumenter-loader": "3.0.1", "jasmine-core": "3.6.0", "karma": "5.2.1", "karma-chrome-launcher": "3.1.0", "karma-coverage": "2.0.3", "karma-jasmine": "4.0.1", "karma-phantomjs-launcher": "1.0.4", "karma-sourcemap-loader": "0.3.8", "karma-webpack": "4.0.2", "mini-css-extract-plugin": "0.11.0", "node-sass": "4.14.1", "optimize-css-assets-webpack-plugin": "5.0.4", "sass-loader": "10.0.2", "standard": "14.3.4", "terser-webpack-plugin": "4.1.0", "webpack": "4.44.1", "webpack-cli": "3.3.12", "webpack-dev-server": "3.11.0"}, "scripts": {"test": "standard && karma start", "dev": "webpack --mode development --progress --hide-modules --devtool source-map", "watch": "webpack --mode development --watch --progress --hide-modules", "production": "webpack --mode production --progress --hide-modules", "coverage": "open coverage/lcov-report/index.html", "coveralls": "cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js", "start": "webpack-dev-server --public http://localhost:8080/test/manual --open", "standard:fix": "standard --fix"}, "author": "<PERSON> <<EMAIL>>", "standard": {"ignore": ["/dist/print.js"], "env": {"browser": true, "jasmine": true}}, "keywords": ["printjs", "print.js", "print-js"]}