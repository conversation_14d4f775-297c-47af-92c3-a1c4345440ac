import request from '@/utils/request'

// 查询bom变更历史数据列表
export function listBomLog(query) {
  return request({
    url: '/order/bomLog/list',
    method: 'get',
    params: query
  })
}

// 查询bom变更历史数据详细
export function getBomLog(id) {
  return request({
    url: '/order/bomLog/' + id,
    method: 'get'
  })
}

// 新增bom变更历史数据
export function addBomLog(data) {
  return request({
    url: '/order/bomLog',
    method: 'post',
    data: data
  })
}

// 修改bom变更历史数据
export function updateBomLog(data) {
  return request({
    url: '/order/bomLog',
    method: 'put',
    data: data
  })
}

// 删除bom变更历史数据
export function delBomLog(id) {
  return request({
    url: '/order/bomLog/' + id,
    method: 'delete'
  })
}

// 导出bom变更历史数据
export function exportBomLog(query) {
  return request({
    url: '/order/bomLog/export',
    method: 'get',
    params: query
  })
}