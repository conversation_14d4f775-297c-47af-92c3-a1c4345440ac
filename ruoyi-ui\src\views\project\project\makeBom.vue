<template>
  <div>
    <el-table
      :data="bomTree"
      :tree-props="{ children: 'children' }"
      default-expand-all
      row-key="id"
      size="mini"
      :row-style="rowStyle"
    >
      <el-table-column align="center" width="120" >
        <template #header >上级</template>
        <template v-slot="scope" >
<!--          {{parentName(bomTree,scope.row.pid)}}-->
        </template>
      </el-table-column>
      <el-table-column align="center" prop="mb002" >
        <template #header >品名<span style="color: #F56C6C">*</span> </template>
        <template v-slot="scope" >
          <div :class="readonly?'mask':''" style="display: flex;align-items: center" >
            <span v-if="scope.row.mb005 !== '101'">{{scope.row.mb002}}</span>
            <el-input v-else v-model.trim="scope.row.mb002" size="mini" />
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="md003" width="120" >
        <template #header >
          品号
          <el-tooltip content="成品是在bom新建的时候推送过来的,其他类型是物料总表关联过来的" >
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
        <template v-slot="scope" >
          <span v-if="scope.row.mb005 === '101'" >{{erpCode}}</span>
          <span v-else >{{bcFields(scope.row.projectBcId,'erpCode')}}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="类别" prop="mb005"  width="100" >
        <template #header >类别 <span style="color: #F56C6C">*</span> </template>
        <template v-slot="scope" >
          <div :class="readonly?'mask':''" >
<!--            <el-select v-model="scope.row.mb005" size="mini" >-->
<!--              <el-option v-for="item in mb005Options"-->
<!--                         :key="item.value"-->
<!--                         :label="item.label"-->
<!--                         :value="item.value" />-->
<!--            </el-select>-->
            {{mb005Text(scope.row.mb005)}}
          </div>
        </template>
      </el-table-column>
<!--      <el-table-column align="center" label="物料类型"  prop="md017"  width="100" >-->
<!--        <template v-slot="scope" >-->
<!--          <el-input v-model="scope.row.md017" size="mini" />-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column align="center" label="物料规格"  prop="mb003"  width="100" >-->
<!--        <template v-slot="scope" >-->
<!--          <el-input v-model="scope.row.mb003" size="mini" />-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column align="center" label="组成用量/底数" prop="md006" width="200" >
        <template #header >组成用量/底数 <span style="color: #F56C6C">*</span> </template>
        <template v-slot="scope" >
          <div :class="readonly?'mask':''" style="display: flex;align-items: center" >
            <el-input v-model="scope.row.md006" size="mini" style="width: 80px" type="number"/>
            /
            <el-input v-model="scope.row.md007" size="mini" style="width: 80px" type="number" />
          </div>
        </template>
      </el-table-column>
<!--      <el-table-column align="center" label="用量" prop="consumption"  width="100" >-->
<!--        <template v-slot="scope" >-->
<!--          <el-input v-model="scope.row.consumption" size="mini" />-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column align="center" label="标准批量" prop="mc004"  width="100" >-->
<!--        <template v-slot="scope" >-->
<!--          <el-input v-model="scope.row.mc004" size="mini" />-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column align="center" label="损耗" prop="md008"  width="100" >-->
<!--        <template v-slot="scope" >-->
<!--          <el-input v-model="scope.row.md008" size="mini" />-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column align="center" prop="mb008" width="100" >
        <template #header >
          物料属性
          <el-tooltip content="此处用于项目总表中的 主包材属性、辅包材属性" >
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
        <template v-slot="scope" >
          <el-select v-model="scope.row.mb008" size="mini" >
            <el-option
              v-for="d in mb008Options"
              :key="d.value"
              :label="d.label"
              :value="d.value"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作"  width="60" >
        <template #header >
          <div style="display: flex;align-items: center" >
            <el-tooltip v-if="!bomTree.length && !readonly" content="添加成品" >
              <i class="el-icon-plus" @click="addItem()" />
            </el-tooltip>
            <el-tooltip v-if="!readonly" content="生成默认bom" >
              <i class="el-icon-news" @click="initBom()" />
            </el-tooltip>
            <el-tooltip content="树图" >
              <i class="el-icon-zoom-in" @click="initChart()" />
            </el-tooltip>
            <el-tooltip v-if="bomTree.length && !readonly" content="清空" >
              <i class="el-icon-circle-close" @click="emptyTree()" />
            </el-tooltip>
          </div>
        </template>
        <template v-if="!readonly" v-slot="scope" >
          <div style="display: flex;align-items: center" >
<!--            <el-tooltip content="普通添加" >-->
<!--              <i class="el-icon-plus" @click="addItem(scope.row)" />-->
<!--            </el-tooltip>-->
            <el-tooltip content="选自物料总表" >
<!--              <i class="el-icon-circle-plus-outline" @click="selectGoods(scope.row.children,scope.row.id)" />-->
              <i class="el-icon-help" @click="showBcDialog(scope.row)" />
            </el-tooltip>
            <i class="el-icon-delete" @click="delItem(scope.row)" />
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div v-if="!readonly" class="dialog-footer" style="margin-top: 20px">
      <el-button :loading="btnLoading" size="mini" type="primary" @click="submitForm" >保 存</el-button>
    </div>

    <el-dialog :close-on-click-modal="false" :fullscreen="fullscreenFlag" :visible.sync="open" append-to-body width="1200px">
      <div slot="title" class="dialog-title">项目物料
        <el-button :icon="fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'" type="text"
                   @click="fullscreenFlag = !fullscreenFlag"/>
      </div>
      <BcSelectTable v-if="projectId" :project-id="projectId" @checked="checkBc" />
    </el-dialog>

    <el-dialog :close-on-click-modal="false" :fullscreen="fullscreenFlag" :visible.sync="bomOpen" append-to-body width="1200px">
      <div slot="title" class="dialog-title">bom结构
        <el-button :icon="fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'" type="text"
                   @click="fullscreenFlag = !fullscreenFlag"/>
      </div>
      <ProjectBomCharts ref="projectBomCharts" :bom-tree="bomTree"  :erp-code="erpCode" />
    </el-dialog>

  </div>
</template>
<script >
import {allBc} from "@/api/project/bc";
import BcSelectTable from "@/views/project/project/bcSelectTable.vue";
import ProjectBomCharts from "@/views/project/project/bomCharts.vue";
import {updateProductItem} from "@/api/project/productItem";

export default {
  name: "projectMarkBom",
  components: {
    ProjectBomCharts,
    BcSelectTable
  },
  props: {
    projectId: {
      type: Number,
      default: null,
    },
    productName: {
      type: String,
      default: '',
    },
    bomTree: {
      type: Array,
      required: true,
    },
    projectProductItemId: {
      type: Number,
      required: true,
    },
    erpCode: {
      type: String,
      required: true,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    materialList: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      btnLoading: false,
      open: false,
      bomOpen: false,
      fullscreenFlag: false,
      mb005Options: [
        {label: '原料',value: '105'},
        {label: '包材',value: '104'},
        {label: '半成品',value: '103'},
        {label: '裸装品',value: '102'},
        {label: '成品',value: '101'},
      ],
      currentRow: {},
      mb008Options: [
        {label: '自制',value: '0'},
        {label: '外购',value: '1'},
        {label: '客供',value: '405'},
      ],
    }
  },
  async created() {
  },
  methods: {
    bcFields(projectBcId,f) {
      const arr = this.materialList.filter(i=>i.id === projectBcId)
      if(arr && arr[0]) {
        return arr[0][f]
      }
    },
    mb005Text(mb005) {
      const arr = this.mb005Options.filter(i=> mb005 === i.value)
      if(arr && arr[0]) {
        return arr[0].label
      }
    },
    rowStyle(scope) {
      if(scope.row.pid !== this.bomTree[0].id && scope.row.id !== this.bomTree[0].id) {
        return {
          backgroundColor: '#E6A23C'
        }
      }
    },
    mb008Text(mb008) {
      let text = ""
      if(mb008) {
        if(["401","402","0"].includes(mb008)) {
          text = '0'
        }
        if(["403","404","1"].includes(mb008)) {
          text = '1'
        }
        if(["405"].includes(mb008)) {
          text = '405'
        }
      }
      return text
    },
    async submitForm() {
      const tempArray = this.translateTreeToData(this.bomTree)
      const bomArray = []
      for (const item of tempArray) {
        bomArray.push({
          ...item,
          mb008: this.mb008Text(item.mb008),
          children: [],
        })
        if(item.pid && !item.mb002) {
          this.msgError('请输入品名')
          return
        }
        if(!item.mb005) {
          this.msgError('请输入类别')
          return
        }
        if(!item.md006) {
          this.msgError('请输入用量')
          return
        }
        if(!item.md007) {
          this.msgError('请输入底数')
          return
        }
      }

      const bomTree = this.toTree(bomArray,undefined)
      const params = {
        id: this.projectProductItemId,
        bomTree: JSON.stringify(bomTree),
        bomArray: JSON.stringify(bomArray),
      }
      if(bomTree[0] && bomTree[0].mb002) {
        params.bomName = bomTree[0].mb002
      }
      if (this.projectProductItemId != null) {
        try {
          this.btnLoading = true
          await updateProductItem(params)
          this.btnLoading = false
          this.msgSuccess("修改成功")
          this.$emit('saveSuccess')
        } catch (e) {
          this.btnLoading = false
        }
      }
    },
    async initChart() {
      this.bomOpen = true
      await this.$nextTick()
      await this.$refs.projectBomCharts.init()
    },
    async emptyTree() {
      try {
        await this.$confirm('是否确认清空?')
        this.bomTree.splice(0,this.bomTree.length)
      } catch (e) {
      }
    },
    async initLp(pid) {
      const array = []
      const id = this.$nanoid()
      array.push({
        id,
        pid,
        mb002: this.productName + ' 半成品',
        mb005: '103',
        md017: null,
        mb003: null,
        consumption: null,
        md006: null,
        md007: null,
        mc004: null,
        md008: null,
        mb008: null,
        children: [],
      })
      const mb008Array = this.mb008Options.map(i=>i.value)
      for (const item of this.materialList) {
        if(item.type === '0') {//裸品下默认是主包材的
          array.push({
            id: this.$nanoid(),
            pid,
            mb005: '104',
            mb002: item.name,
            md017: null,
            mb003: null,
            consumption: null,
            md006: null,
            md007: null,
            mc004: null,
            md008: null,
            mb008: this.mb008Text(item.mb008),
            children: [],
            projectBcId: item.id,
          })
        }
      }
      return array
    },
    async initCp(pid) {
      const array = []
      const id = this.$nanoid()
      const children = await this.initLp(id)
      array.push({
        id,
        pid,
        mb002: this.productName + '裸品',
        mb005: '102',
        md017: null,
        mb003: null,
        consumption: 1,
        md006: null,
        md007: 1,
        mc004: null,
        md008: null,
        mb008: null,
        children,
      })
      const mb008Array = this.mb008Options.map(i=>i.value)
      for (const item of this.materialList) {
        if(item.type !== '0') {//成品下默认不是主包材的
          array.push({
            id: this.$nanoid(),
            pid,
            mb005: '104',
            mb002: item.name,
            md017: null,
            mb003: null,
            consumption: null,
            md006: null,
            md007: null,
            mc004: null,
            md008: null,
            mb008: this.mb008Text(item.mb008),
            children: [],
            projectBcId: item.id,
          })
        }
      }
      return array
    },
    async initBom() {
      try {
        await this.$confirm('重新生成会覆盖当前子名称下已维护的bom,是否继续?')
        this.bomTree.length = 0
        const id = this.$nanoid()
        const children = await this.initCp(id)
        const o = {
          id,
          pid: undefined,
          mb002: null,
          mb005: '101',
          md017: null,
          mb003: null,
          consumption: 1,
          md006: 1,
          md007: 1,
          mc004: null,
          md008: null,
          mb008: null,
          children,
        }
        this.bomTree.push(o)
      } catch (e) {
      }
    },
    nameStyle(mb005) {
      let color = '';
      switch (mb005) {
        case '101': color = '#409EFF'; break;
        case '102': color = '#67C23A'; break;
        case '103': color = '#E6A23C'; break;
        case '104': color = '#F56C6C'; break;
        case '105': color = '#909399'; break;
      }
      return {
        color: color
      }
    },
    checkBc(bcArray) {
      bcArray.sort((pre,curr)=>pre.mb005 - curr.mb005)
      for (const item of bcArray) {
        const o = {
          id: this.$nanoid(),
          pid: this.currentRow.id,
          mb005: item.mb005,
          mb002: item.name,
          md017: null,
          mb003: null,
          consumption: null,
          md006: null,
          md007: null,
          mc004: null,
          md008: null,
          mb008: this.mb008Text(item.mb008),
          children: [],
          projectBcId: item.id,
        }
        this.currentRow.children.push(o)
      }
      this.open = false
    },
    showBcDialog(row) {
      this.currentRow = row
      this.open = true
    },
    parentName(bomTree,pid) {
      if(pid && bomTree.length) {
        const arr = bomTree.filter(i=>i.id === pid)
        if(arr && arr[0]) {
          return arr[0].mb002
        } else {
          for (const item of bomTree) {
            if(item.children.length) {
              return this.parentName(item.children,pid)
            }
          }
        }
      }
    },
    delItem(row) {
      this.removeItem(this.bomTree,row)
    },
    removeItem(bomTree,row) {
      if(row.pid) {
        for (const b of bomTree) {
          if(b.id === row.pid && b.children && b.children.length) {
            const parentArray = b.children
            const index = parentArray.findIndex(i=> i.id === row.id)
            parentArray.splice(index,1)
          }
          this.removeItem(b.children,row)
        }
      } else {
        const index = bomTree.findIndex(i=> i.id === row.id)
        bomTree.splice(index,1)
      }
    },
    addItem(parentNode) {
      const o = {
        id: this.$nanoid(),
        mb002: null,
        md017: null,
        mb003: null,
        consumption: null,
        md006: null,
        md007: null,
        mc004: null,
        md008: null,
        mb008: null,
        children: [],
      }
      if(parentNode) {
        o.pid = parentNode.id
        if(!parentNode.pid) {
          o.mb002 = this.productName + ' 裸品'
          o.mb005 = '102'
        } else if(parentNode.mb005 === '102') {
          o.mb002 = this.productName + ' 半成品'
          o.mb005 = '103'
        } else {
          o.mb005 = null
        }
        parentNode.children.push(o)
      } else {
        o.mb005 = '101'
        o.md006 = 1
        o.md007 = 1
        o.mb002 = this.productName
        this.bomTree.push(o)
      }
    },
  },
}

</script>

<style lang="scss" scoped>

</style>
