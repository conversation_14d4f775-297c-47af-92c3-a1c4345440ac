import request from '@/utils/request'

// 查询项目产品规格列表
export function listProductItem(query) {
  return request({
    url: '/project/productItem/list',
    method: 'get',
    params: query
  })
}

// 查询项目产品规格详细
export function getProductItem(id) {
  return request({
    url: '/project/productItem/' + id,
    method: 'get'
  })
}

// 新增项目产品规格
export function addProductItem(data) {
  return request({
    url: '/project/productItem',
    method: 'post',
    data: data
  })
}

// 修改项目产品规格
export function updateProductItem(data) {
  return request({
    url: '/project/productItem',
    method: 'put',
    data: data
  })
}

// 删除项目产品规格
export function delProductItem(id) {
  return request({
    url: '/project/productItem/' + id,
    method: 'delete'
  })
}

// 导出项目产品规格
export function exportProductItem(query) {
  return request({
    url: '/project/productItem/export',
    method: 'get',
    params: query
  })
}

export function allProductItem(query) {
  return request({
    url: '/project/productItem/all',
    method: 'get',
    params: query
  })
}

export function asyncProductItemBom(query) {
  return request({
    url: '/project/productItem/asyncBomArray',
    method: 'get',
    params: query
  })
}
