import request from '@/utils/request'

// 查询配方稳定性列表
export function listStability(query) {
  return request({
    url: '/rd/stability/list',
    method: 'get',
    params: query
  })
}

export function listByDeptStability(query) {
  return request({
    url: '/rd/stability/listByDept',
    method: 'get',
    params: query
  })
}

// 查询配方稳定性详细
export function getStability(id) {
  return request({
    url: '/rd/stability/' + id,
    method: 'get'
  })
}

export function getStabilityBy(id) {
  return request({
    url: '/rd/stability/' + id,
    method: 'get'
  })
}

// 新增配方稳定性
export function addStability(data) {
  return request({
    url: '/rd/stability',
    method: 'post',
    data: data
  })
}

// 修改配方稳定性
export function updateStability(data) {
  return request({
    url: '/rd/stability',
    method: 'put',
    data: data
  })
}

// 删除配方稳定性
export function delStability(id) {
  return request({
    url: '/rd/stability/' + id,
    method: 'delete'
  })
}

// 导出配方稳定性
export function exportStability(id) {
  return request({
    url: '/rd/stability/exportStability/' + id,
    method: 'get',
  })
}

export function asyncRdStability() {
  return request({
    url: '/rd/stability/asyncRdStability',
    method: 'get',
  })
}

export function allStability(query) {
  return request({
    url: '/rd/stability/all',
    method: 'get',
    params: query
  })
}

export function refreshTemplateStability(id) {
  return request({
    url: '/rd/stability/refreshTemplate/' + id,
    method: 'put'
  })
}
