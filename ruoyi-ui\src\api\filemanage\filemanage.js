import request from '@/utils/request'

// 查询文件管理列表
export function listFilemanage(query) {
  return request({
    url: '/filemanage/filemanage/list',
    method: 'get',
    params: query
  })
}

export function applyListManage(query) {
  return request({
    url: '/filemanage/filemanage/applyList',
    method: 'get',
    params: query
  })
}

export function selectBadgeByUser(query) {
  return request({
    url: '/filemanage/filemanage/selectBadgeByUser',
    method: 'get',
    params: query
  })
}


// 查询文件管理详细
export function getFilemanage(id) {
  return request({
    url: '/filemanage/filemanage/' + id,
    method: 'get'
  })
}

export function getFileNo(query) {
  return request({
    url: '/filemanage/filemanage/selectFileNo',
    method: 'get',
    params: query
  })
}



// 新增文件管理
export function addFilemanage(data) {
  return request({
    url: '/filemanage/filemanage',
    method: 'post',
    data: data
  })
}

export function qaAudit(data) {
  return request({
    url: '/filemanage/filemanage/qaAudit',
    method: 'post',
    data: data
  })
}

export function submitAuditFilemanage(data) {
  return request({
    url: '/filemanage/filemanage/submitAudit',
    method: 'post',
    data: data
  })
}

export function cancelAuditFilemanage(data) {
  return request({
    url: '/filemanage/filemanage/cancelAudit',
    method: 'post',
    data: data
  })
}



// 修改文件管理
export function updateFilemanage(data) {
  return request({
    url: '/filemanage/filemanage',
    method: 'put',
    data: data
  })
}

// 删除文件管理
export function delFilemanage(id) {
  return request({
    url: '/filemanage/filemanage/' + id,
    method: 'delete'
  })
}

// 导出文件管理
export function exportFilemanage(query) {
  return request({
    url: '/filemanage/filemanage/export',
    method: 'get',
    params: query
  })
}

export function exportPerm(query) {
  return request({
    url: '/filemanage/filemanage/exportPerm',
    method: 'get',
    params: query
  })
}

export function exportRecycle(query) {
  return request({
    url: '/filemanage/filemanage/exportRecycle',
    method: 'get',
    params: query
  })
}

export function auditFilemanage(query) {
  return request({
    url: '/filemanage/filemanage/audit',
    method: 'get',
    params: query
  })
}

export function leadershipSee(query) {
  return request({
    url: '/filemanage/filemanage/leadershipSee',
    method: 'get',
    params: query
  })
}

export function selectCustomer(query) {
  return request({
    url: '/filemanage/filemanage/selectCustomer',
    method: 'get',
    params: query
  })
}

export function exemptFileExternalAudit(instanceId) {
  return request({
    url: '/filemanage/filemanage/exemptFileExternalAudit/' + instanceId,
    method: 'put',
  })
}
