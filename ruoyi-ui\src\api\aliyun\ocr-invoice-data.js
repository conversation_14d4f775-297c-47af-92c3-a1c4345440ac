let invoicesItems = [
  {label:'标题',key:'title',width:'200',items:[
      {key:'title'},
    ]},
  {label:'发票类型',key:'invoiceType',width:'200',items:[
      {key:'invoiceType'},
    ]},
  {label:'发票号码',key:'invoiceNumber',width:'250',items:[
      {key:'invoiceNumber'},
      {key:'agentCode'},
      {key:'ticketNumber'},
      {key:'draftNumber'},
      {key:'memberNumber'},
      {key:'orderNumber'},
      {key:'serviceProvider'},
      {key:'shopName'},
      {key:'cardNumber'},
      {key:'certificateNumber'}
    ]},
  {label:'开票日期',key:'invoiceDate',width:'200',items:[
      {key:'invoiceDate'},
      {key:'issueDate'},
      {key:'date'},
      {key:'receiptDate'},
      {key:'transactionTime'},
      {key:'applicationDate'},
    ]},
  {label:'发票金额',key:'totalAmount',width:'200',items:[
      {key:'totalAmount'},
      {key:'amount'},
      {key:'invoiceAmountCn'},
      {key:'fare'},
      {key:'totalConsumption'}
    ]},
  {label:'发票税额',key:'invoiceTax',width:'200',items:[
      {key:'invoiceTax'},
      {key:'tax'},
    ]},
  {label:'不含税金额',key:'invoiceAmountPreTax',width:'200',items:[
      {key:'invoiceAmountPreTax'},
      {key:'preTaxAmount'},
    ]},
  {label:'销售方名称',key:'sellerName',width:'200',items:[
      {key:'sellerName'},
      {key:'issueCompany'},
      {key:'issuerName'},
      {key:'serviceProvider'},
      {key:'marketName'},
    ]},
  {label:'销售方税号',key:'sellerTaxNumber',width:'200',items:[
      {key:'sellerTaxNumber'},
    ]},
  {label:'受票方名称',key:'purchaserName',width:'200',items:[
      {key:'purchaserName'},
      {key:'payeeName'},
      {key:'acceptorName'},
    ]},
  {label:'受票方税号',key:'purchaserTaxNumber',width:'200',items:[
      {key:'purchaserTaxCode'},
      {key:'purchaserTaxNumber'},
      {key:'acceptorAccountNumber'},
      {key:'payeeAccountNumber'},
      {key:'taxNumbe'},
      {key:'marketTaxNumber'},
    ]},
  {label:'受票方开户行',key:'purchaserBankAccountInfo',width:'200',items:[
      {key:'purchaserBankAccountInfo'},
      {key:'payeeAccountBank'},
      {key:'marketBankAccountInfo'},
      {key:'acceptorAccountBank'},
      {key:'payeeAccountBank'},
    ]},
];
let invoicesItemsName = [
  {key:'type',name:'开票类型',isEdit:true,isShow:true},
  {key:'invoiceType',name:'发票类型',isEdit:true,isShow:true},
  {key:'formType',name:'联次',isEdit:true,isShow:true},
  {key:'title',name:'标题',isEdit:true,isShow:true},

  {key:'totalAmount',name:'发票金额',isEdit:true,isShow:true},
  {key:'amount',name:'发票金额',isEdit:true,isShow:true},
  {key:'amountInWords',name:'大写金额',isEdit:true,isShow:true},
  {key:'invoiceTax',name:'发票税额',isEdit:true,isShow:true},
  {key:'invoiceAmountPreTax',name:'不含税金额',isEdit:true,isShow:true},
  {key:'totalAmountInWords',name:'大写金额',isEdit:true,isShow:true},
  {key:'invoiceAmountCn',name:'发票金额',isEdit:true,isShow:true},
  {key:'invoiceAmount',name:'大写金额',isEdit:true,isShow:true},
  {key:'taxRate',name:'增值税税率',isEdit:true,isShow:true},
  {key:'tax',name:'发票税额',isEdit:true,isShow:true},
  {key:'preTaxAmount',name:'不含税金额',isEdit:true,isShow:true},
  {key:'fare',name:'票价',isEdit:true,isShow:true},
  {key:'insurance',name:'保险费',isEdit:true,isShow:true},
  {key:'fuelSurcharge',name:'燃油附加费',isEdit:true,isShow:true},
  {key:'otherTaxes',name:'其他税费',isEdit:true,isShow:true},
  {key:'totalConsumption',name:'消费总计',isEdit:true,isShow:true},
  {key:'invoiceCode',name:'发票代码',isEdit:true,isShow:true},
  {key:'invoiceNumber',name:'发票号码',isEdit:true,isShow:true},
  {key:'invoiceDate',name:'开票日期',isEdit:true,isShow:true},
  {key:'machineCode',name:'机器编码',isEdit:true,isShow:true},
  {key:'checkCode',name:'校验码',isEdit:true,isShow:true},
  {key:'purchaserName',name:'受票方名称',isEdit:true,isShow:true},
  {key:'purchaserTaxCode',name:'受票方税号',isEdit:true,isShow:true},
  {key:'passwordArea',name:'密码区',isEdit:true,isShow:true},
  {key:'sellerName',name:'销售方名称',isEdit:true,isShow:true},
  {key:'sellerContact',name:'销货单位电话',isEdit:true,isShow:true},
  {key:'sellerTaxNumber',name:'销售方税号',isEdit:true,isShow:true},
  {key:'sellerContactInfo',name:'销售方地址',isEdit:true,isShow:true},
  {key:'sellerBankAccount',name:'销货单位账号',isEdit:true,isShow:true},
  {key:'sellerBankAccountInfo',name:'销售方开户行',isEdit:true,isShow:true},
  {key:'sellerAddress',name:'销货单位地址',isEdit:true,isShow:true},
  {key:'sellerDepositaryBank',name:'销售方开户行',isEdit:true,isShow:true},
  {key:'drawer',name:'开票人',isEdit:true,isShow:true},
  {key:'printedInvoiceCode',name:'机打发票代码',isEdit:true,isShow:true},
  {key:'printedInvoiceNumber',name:'机打发票号码',isEdit:true,isShow:true},
  {key:'purchaserBankAccountInfo',name:'受票方开户行',isEdit:true,isShow:true},
  {key:'purchaserContactInfo',name:'受票方地址',isEdit:true,isShow:true},
  {key:'purchaserTaxNumber',name:'受票方税号',isEdit:true,isShow:true},
  {key:'recipient',name:'收款人',isEdit:true,isShow:true},
  {key:'remarks',name:'备注',isEdit:true,isShow:true},
  {key:'reviewer',name:'复核人',isEdit:true,isShow:true},
  {key:'invoiceDetail',name:'发票详单',isEdit:true,isShow:true},
  {key:'taxCode',name:'税控码',isEdit:true,isShow:true},
  {key:'vehicleType',name:'车辆类型',isEdit:true,isShow:true},
  {key:'brandMode',name:'厂牌型号',isEdit:true,isShow:true},
  {key:'origin',name:'产地',isEdit:true,isShow:true},
  {key:'certificateNumber',name:'证号',isEdit:true,isShow:true},
  {key:'importCertificateNumber',name:'进口证明书号',isEdit:true,isShow:true},
  {key:'commodityInspectionNumber',name:'商检单号',isEdit:true,isShow:true},
  {key:'engineNumber',name:'发动机号码',isEdit:true,isShow:true},
  {key:'vinCode',name:'车辆识别代号',isEdit:true,isShow:true},
  {key:'taxAuthoritiesInfo',name:'主管税务机关',isEdit:true,isShow:true},
  {key:'taxAuthoritiesName',name:'主管税务机关',isEdit:true,isShow:true},
  {key:'taxAuthoritiesCode',name:'主管税务代码',isEdit:true,isShow:true},
  {key:'passengerLimitNumber',name:'限乘人数',isEdit:true,isShow:true},
  {key:'issuer',name:'开票人',isEdit:true,isShow:true},
  {key:'tonnage',name:'吨位',isEdit:true,isShow:true},
  {key:'taxPaymentNumber',name:'完税凭证号码',isEdit:true,isShow:true},
  {key:'agentCode',name:'销售单位代号',isEdit:true,isShow:true},
  {key:'caacDevelopmentFund',name:'民航发展基金',isEdit:true,isShow:true},
  {key:'endorsement',name:'签注',isEdit:true,isShow:true},
  {key:'flights',name:'航班详单',isEdit:true,isShow:true},
  {key:'idCardNumber',name:'身份证号码',isEdit:true,isShow:true},
  {key:'internationalFlightSign',name:'国内国际标签',isEdit:true,isShow:true},
  {key:'issueCompany',name:'填开单位',isEdit:true,isShow:true},
  {key:'issueDate',name:'填开日期',isEdit:true,isShow:true},
  {key:'passengerName',name:'旅客姓名',isEdit:true,isShow:true},
  {key:'pnrCode',name:'PNR码',isEdit:true,isShow:true},
  {key:'promptMessage',name:'提示信息',isEdit:true,isShow:true},
  {key:'serialNumber',name:'印刷序号',isEdit:true,isShow:true},
  {key:'ticketNumber',name:'电子客票号码',isEdit:true,isShow:true},
  {key:'validationCode',name:'验证码',isEdit:true,isShow:true},
  {key:'departureStation',name:'出发站',isEdit:true,isShow:true},
  {key:'arrivalStation',name:'到达站',isEdit:true,isShow:true},
  {key:'trainNumber',name:'车次',isEdit:true,isShow:true},
  {key:'departureTime',name:'开车时间',isEdit:true,isShow:true},
  {key:'seatNumber',name:'座位号',isEdit:true,isShow:true},
  {key:'ticketGate',name:'检票口',isEdit:true,isShow:true},
  {key:'seatType',name:'座位类型',isEdit:true,isShow:true},
  {key:'passengerInfo',name:'旅客信息',isEdit:true,isShow:true},
  {key:'ticketCode',name:'售票码',isEdit:true,isShow:true},
  {key:'saleInfo',name:'售票车站信息',isEdit:true,isShow:true},
  {key:'date',name:'乘车日期',isEdit:true,isShow:true},
  {key:'dropOffTime',name:'下车时间',isEdit:true,isShow:true},
  {key:'licensePlateNumber',name:'车牌号',isEdit:true,isShow:true},
  {key:'mileage',name:'里程',isEdit:true,isShow:true},
  {key:'pickUpTime',name:'上车时间',isEdit:true,isShow:true},
  {key:'iGNORE',name:'机打号码',isEdit:true,isShow:true},
  {key:'cashier',name:'收款员',isEdit:true,isShow:true},
  {key:'validToDate',name:'到期日期',isEdit:true,isShow:true},
  {key:'draftStatus',name:'票据状态',isEdit:true,isShow:true},
  {key:'draftNumber',name:'票据号码',isEdit:true,isShow:true},
  {key:'issuerName',name:'出票人全称',isEdit:true,isShow:true},
  {key:'issuerAccountNumber',name:'出票人账号',isEdit:true,isShow:true},
  {key:'issuerAccountBank',name:'出票人开户行',isEdit:true,isShow:true},
  {key:'payeeName',name:'收票人全称',isEdit:true,isShow:true},
  {key:'payeeAccountNumber',name:'收票人账号',isEdit:true,isShow:true},
  {key:'payeeAccountBank',name:'收票人开户行',isEdit:true,isShow:true},
  {key:'acceptorName',name:'承兑人全称',isEdit:true,isShow:true},
  {key:'acceptorAccountNumber',name:'承兑人账号',isEdit:true,isShow:true},
  {key:'acceptorBankNumber',name:'承兑人行号',isEdit:true,isShow:true},
  {key:'acceptorAccountBank',name:'承兑人行名',isEdit:true,isShow:true},
  {key:'agreementNumber',name:'交易合同号',isEdit:true,isShow:true},
  {key:'assignability',name:'',isEdit:true,isShow:true},
  {key:'assignability',name:'能否转让',isEdit:true,isShow:true},
  {key:'acceptanceDate',name:'承兑日期',isEdit:true,isShow:true},
  {key:'subDraftNumber',name:'子票区间号',isEdit:true,isShow:true},
  {key:'time',name:'时间',isEdit:true,isShow:true},
  {key:'idcardNo',name:'身份证号',isEdit:true,isShow:true},
  {key:'additionalInfo',name:'其他信息',isEdit:true,isShow:true},
  {key:'payerCreditCode',name:'交款人社信代',isEdit:true,isShow:true},
  {key:'payerName',name:'交款人',isEdit:true,isShow:true},
  {key:'fax',name:'传真',isEdit:true,isShow:true},
  {key:'phone',name:'电话',isEdit:true,isShow:true},
  {key:'postCode',name:'邮编',isEdit:true,isShow:true},
  {key:'roomNo',name:'房号',isEdit:true,isShow:true},
  {key:'checkInDate',name:'入住日期',isEdit:true,isShow:true},
  {key:'departureDate',name:'离店日期',isEdit:true,isShow:true},
  {key:'memberNumber',name:'会员号码',isEdit:true,isShow:true},
  {key:'name',name:'姓名',isEdit:true,isShow:true},
  {key:'roomType',name:'房型',isEdit:true,isShow:true},
  {key:'numberOfGuests',name:'住店人数',isEdit:true,isShow:true},
  {key:'roomRate',name:'房费',isEdit:true,isShow:true},
  {key:'address',name:'地址',isEdit:true,isShow:true},
  {key:'description',name:'商品说明',isEdit:true,isShow:true},
  {key:'orderNumber',name:'订单号',isEdit:true,isShow:true},
  {key:'paymentMethod',name:'付款方式',isEdit:true,isShow:true},
  {key:'paymentTime',name:'支付时间',isEdit:true,isShow:true},
  {key:'recipientName',name:'收款方名称',isEdit:true,isShow:true},
  {key:'transactionTime',name:'交易时间',isEdit:true,isShow:true},
  {key:'deliveryInfo',name:'收货信息',isEdit:true,isShow:true},
  {key:'shopName',name:'店铺名称',isEdit:true,isShow:true},
  {key:'serviceProvider',name:'服务商',isEdit:true,isShow:true},
  {key:'applicationDate',name:'申请日期',isEdit:true,isShow:true},
  {key:'startTime',name:'行程开始时间',isEdit:true,isShow:true},
  {key:'endTime',name:'行程结束时间',isEdit:true,isShow:true},
  {key:'phoneNumber',name:'行程人手机号',isEdit:true,isShow:true},
  {key:'receiptDate',name:'开票日期',isEdit:true,isShow:true},
  {key:'receiptTime',name:'开票时间',isEdit:true,isShow:true},
  {key:'contactNumber',name:'联系电话',isEdit:true,isShow:true},
  {key:'shopAddress',name:'地址',isEdit:true,isShow:true},
  {key:'idNumber',name:'社会保障号码',isEdit:true,isShow:true},
  {key:'cardNumber',name:'社保保障卡号',isEdit:true,isShow:true},
  {key:'bankAccount',name:'银行账号',isEdit:true,isShow:true},
  {key:'validPeriod',name:'有效期限',isEdit:true,isShow:true},
  {key:'entranceName',name:'入口',isEdit:true,isShow:true},
  {key:'exitName',name:'出口',isEdit:true,isShow:true},
  {key:'ftype',name:'是否复印件',isEdit:true,isShow:true},//1:是，0:否
  {key:'taxAuthorityName',name:'税务机关',isEdit:true,isShow:true},
  {key:'taxNumbe',name:'纳税人识别号',isEdit:true,isShow:true},
  {key:'purchaserCode',name:'买方单位代码',isEdit:true,isShow:true},
  {key:'purchaserAddress',name:'买方单位地址',isEdit:true,isShow:true},
  {key:'purchaserPhoneNumber',name:'买方电话',isEdit:true,isShow:true},
  {key:'sellerCode',name:'销售单位代码',isEdit:true,isShow:true},
  {key:'sellerPhoneNumber',name:'销售方电话',isEdit:true,isShow:true},
  {key:'licensePlateNumber',name:'车牌照号',isEdit:true,isShow:true},
  {key:'vehicleAdministrationName',name:'车辆管理所',isEdit:true,isShow:true},
  {key:'marketName',name:'车市名称',isEdit:true,isShow:true},
  {key:'marketTaxNumber',name:'纳税人识别号',isEdit:true,isShow:true},
  {key:'marketAddress',name:'车市地址',isEdit:true,isShow:true},
  {key:'marketBankAccountInfo',name:'车市开户行',isEdit:true,isShow:true},
  {key:'marketPhoneNumber',name:'车市电话',isEdit:true,isShow:true},
  {key:'invoiceDetails',name:'发票明细',isEdit:true,isShow:true}
];
export function getInvoicesItems(){
  let invoicesItems_ = [];
  for(let i=0;i<invoicesItems.length;i++){
    let invoicesItem = JSON.parse(JSON.stringify(invoicesItems[i]));
    invoicesItem.items = [];
    invoicesItems_.push(invoicesItem);
  }
  return invoicesItems_;
}
export function getInvoicesItemsKey(key){
  let key_ = null;
  for(let i=0;i<invoicesItems.length;i++){
    let findex = invoicesItems[i].items.map(f => f.key).indexOf(key);
    if(findex >= 0){
      key_ = invoicesItems[i].key;
      break;
    }
  }
  return key_;
}
export function getInvoicesItemsIsLabel(label,key){
  let bool = false;
  for(let i=0;i<invoicesItems.length;i++){
    let findex = invoicesItems[i].items.map(f => f.key).indexOf(key);
    if(findex >= 0 && invoicesItems[i].label == label){
      bool = true;
      break;
    }
  }
  return bool;
}
export function getinvoicesItemName(key) {
  let name = null;
  let findex = invoicesItemsName.map(f => f.key).indexOf(key);
  if(findex >= 0){
    name = invoicesItemsName[findex].name;
  }
  return name;
}


export function getInvoicesData(res){
  let request = [];

}
