import request from '@/utils/request'

// 查询配制TOKEN列表
export function listMesToken(query) {
  return request({
    url: '/mes/mesToken/list',
    method: 'get',
    params: query
  })
}

// 查询配制TOKEN详细
export function getMesToken(id) {
  return request({
    url: '/mes/mesToken/' + id,
    method: 'get'
  })
}

export function getExpireTimeByToken(id) {
  return request({
    url: '/mes/mesToken/getExpireTimeByToken/' + id,
    method: 'get'
  })
}

// 新增配制TOKEN
export function addMesToken(data) {
  return request({
    url: '/mes/mesToken',
    method: 'post',
    data: data
  })
}

// 修改配制TOKEN
export function updateMesToken(data) {
  return request({
    url: '/mes/mesToken',
    method: 'put',
    data: data
  })
}

// 删除配制TOKEN
export function delMesToken(id) {
  return request({
    url: '/mes/mesToken/' + id,
    method: 'delete'
  })
}

// 导出配制TOKEN
export function exportMesToken(query) {
  return request({
    url: '/mes/mesToken/export',
    method: 'get',
    params: query
  })
}
