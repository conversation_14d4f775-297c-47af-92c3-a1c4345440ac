{"name": "signature_pad", "description": "Library for drawing smooth signatures.", "version": "5.0.7", "homepage": "https://github.com/szimek/signature_pad", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/szimek"}, "license": "MIT", "source": "src/signature_pad.ts", "main": "dist/signature_pad.umd.js", "module": "dist/signature_pad.js", "types": "dist/types/signature_pad.d.ts", "exports": {"import": "./dist/signature_pad.js", "require": "./dist/signature_pad.umd.js", "default": "./dist/signature_pad.umd.js", "types": "./dist/types/signature_pad.d.ts"}, "scripts": {"build": "yarn run lint && yarn run clean && rollup --config && yarn run emit-types && yarn run update-docs", "clean": "yarn run del dist", "emit-types": "yarn run del dist/types && yarn run tsc src/signature_pad.ts --lib DOM,ES2015 --declaration --declarationDir dist/types --emitDeclarationOnly", "format": "prettier --write {src,tests}/**/*.{js,ts}", "lint": "eslint {src,tests}/**/*.ts", "prepublishOnly": "yarn run build", "serve": "serve -l 9000 docs", "start": "yarn run build && yarn run serve", "test": "jest --coverage", "update-docs": "yarn run cpy 'dist/signature_pad.umd.min.*' docs/js"}, "repository": {"type": "git", "url": "https://github.com/szimek/signature_pad.git"}, "files": ["src", "dist", "docs"], "devDependencies": {"@eslint/js": "^9.12.0", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.1", "@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.0", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.0", "@semantic-release/npm": "^12.0.1", "@semantic-release/release-notes-generator": "^14.0.1", "@types/jest": "^29.5.13", "@types/node": "^22.7.6", "cpy-cli": "^5.0.0", "del": "^8.0.0", "del-cli": "^6.0.0", "eslint": "^9.12.0", "eslint-config-prettier": "^9.1.0", "globals": "^15.11.0", "jest": "^29.7.0", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.3.3", "rollup": "^4.24.0", "semantic-release": "^24.1.2", "serve": "^14.2.4", "ts-jest": "^29.2.5", "tslib": "^2.8.0", "typescript": "~5.6.3", "typescript-eslint": "^8.9.0"}, "jest": {"moduleFileExtensions": ["ts", "js"], "testEnvironment": "jsdom", "testEnvironmentOptions": {"resources": "usable", "url": "http://localhost:3000/"}, "testMatch": ["<rootDir>/tests/**/*.test.ts"], "transform": {"^.+\\.tsx?$": "ts-jest"}, "setupFiles": ["jest-canvas-mock"]}, "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"]}, "packageManager": "yarn@4.2.1"}