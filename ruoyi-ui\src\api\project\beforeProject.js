import request from '@/utils/request'

// 查询立项前项目列表
export function listBeforeProject(query) {
  return request({
    url: '/project/beforeProject/list',
    method: 'get',
    params: query
  })
}


// 查询立项前项目列表
export function listBeforeProjectProduct(query) {
  return request({
    url: '/project/beforeProject/productList',
    method: 'get',
    params: query
  })
}


// 查询项目子名称
export function queryBeforeProjectSubItemDataList(query) {
  return request({
    url: '/project/beforeProject/queryBeforeProjectSubItemDataList',
    method: 'get',
    params: query
  })
}


// 查询价格变动记录
export function queryProjectPriceChangeLogDataList(query) {
  return request({
    url: '/project/beforeProject/queryProjectPriceChangeLogDataList',
    method: 'get',
    params: query
  })
}

//查询项目价格 配方要求数据
export function queryProjectProductFormulaDetail(query) {
  return request({
    url: '/project/beforeProject/queryProjectProductFormulaDetail',
    method: 'get',
    params: query
  })
}

//获取子名称
export function queryProjectProductSubItemNameInfo(query) {
  return request({
    url: '/project/beforeProject/queryProjectProductSubItemNameInfo',
    method: 'get',
    params: query
  })
}

//查询项目订单数量
export function queryBeforeProjectProductDataInfo(query) {
  return request({
    url: '/project/beforeProject/queryBeforeProjectProductDataInfo',
    method: 'get',
    params: query
  })
}
//查询项目价格 配方要求数据
export function queryProjectFormulaDetail(query) {
  return request({
    url: '/project/beforeProject/queryProjectFormulaDetail',
    method: 'get',
    params: query
  })
}

//查询项目信息
export function queryProjectDataInfoDetail(query) {
  return request({
    url: '/project/beforeProject/queryProjectDataInfoDetail',
    method: 'get',
    params: query
  })
}

// 查询立项前项目详细
export function getBeforeProject(id) {
  return request({
    url: '/project/beforeProject/' + id,
    method: 'get'
  })
}

// 查询立项前项目详细
export function getBeforeProjectDetails(id) {
  return request({
    url: '/project/beforeProject/detail/' + id,
    method: 'get'
  })
}

// 查询立项前项目详细
export function getBeforeProjectDetailsNew(query) {
  return request({
    url: '/project/beforeProject/getBeforeProjectDetailsNew',
    method: 'get',
    params: query
  })
}


// 查询立项前项目详细
export function getBeforeProjectObjDetails(id) {
  return request({
    url: '/project/beforeProject/detailObj/' + id,
    method: 'get'
  })
}


// 查询立项前项目 配方要求列表
export function getBeforeProjectFormulaRequirementsDataList(id) {
  return request({
    url: '/project/beforeProject/formulaRequirements/' + id,
    method: 'get'
  })
}

// 查询立项前项目 配方要求列表
export function getProjectFormulaRequirementsDataList(id) {
  return request({
    url: '/project/beforeProject/projectFormulaRequirements/' + id,
    method: 'get'
  })
}

// 新增立项前项目
export function addBeforeProject(data) {
  return request({
    url: '/project/beforeProject',
    method: 'post',
    data: data
  })
}

// 修改立项前项目
export function updateBeforeProject(data) {
  return request({
    url: '/project/beforeProject',
    method: 'put',
    data: data
  })
}

// 删除立项前项目
export function delBeforeProject(id) {
  return request({
    url: '/project/beforeProject/' + id,
    method: 'delete'
  })
}

// 导出立项前项目
export function exportBeforeProject(query) {
  return request({
    url: '/project/beforeProject/export',
    method: 'get',
    params: query
  })
}
