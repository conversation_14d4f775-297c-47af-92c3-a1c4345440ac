import request from '@/utils/request'

// 查询原料检测项目-仪器设置列表
export function listSetXmYq(query) {
  return request({
    url: '/qc/setXmYq/list',
    method: 'get',
    params: query
  })
}


// 查询原料检测项目-仪器设置列表
export function listSetXmYqNew(query) {
  return request({
    url: '/qc/setXmYq/listNew',
    method: 'get',
    params: query
  })
}

// 查询原料检测项目-仪器设置详细
export function getSetXmYq(id) {
  return request({
    url: '/qc/setXmYq/' + id,
    method: 'get'
  })
}

// 新增原料检测项目-仪器设置
export function addSetXmYq(data) {
  return request({
    url: '/qc/setXmYq',
    method: 'post',
    data: data
  })
}

// 修改原料检测项目-仪器设置
export function updateSetXmYq(data) {
  return request({
    url: '/qc/setXmYq',
    method: 'put',
    data: data
  })
}

// 删除原料检测项目-仪器设置
export function delSetXmYq(id) {
  return request({
    url: '/qc/setXmYq/' + id,
    method: 'delete'
  })
}

// 导出原料检测项目-仪器设置
export function exportSetXmYq(query) {
  return request({
    url: '/qc/setXmYq/export',
    method: 'get',
    params: query
  })
}

export function listMaterialXmYqAll(query) {
  return request({
    url: '/qc/setXmYq/all',
    method: 'get',
    params: query
  })
}

export function confirmYqSetting(data) {
  return request({
    url: '/qc/setXmYq/confirmYqSetting',
    method: 'get',
    params: data
  })
}


// 新增原料检测项目-仪器设置
export function confirmXmYqData(data) {
  return request({
    url: '/qc/setXmYq/confirmXmYqDataNew',
    method: 'post',
    data: data
  })
}
