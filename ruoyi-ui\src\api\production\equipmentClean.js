import request from '@/utils/request'

// 查询设备清洗记录列表
export function listEquipmentClean(query) {
  return request({
    url: '/production/equipmentClean/list',
    method: 'get',
    params: query
  })
}

// 查询设备清洗记录详细
export function getEquipmentClean(query) {
  return request({
    url: '/production/equipmentClean',
    method: 'get',
    params: query
  })
}

// 新增设备清洗记录
export function addEquipmentClean(data) {
  return request({
    url: '/production/equipmentClean',
    method: 'post',
    data: data
  })
}

// 修改设备清洗记录
export function updateEquipmentClean(data) {
  return request({
    url: '/production/equipmentClean',
    method: 'put',
    data: data
  })
}

// 删除设备清洗记录
export function delEquipmentClean(id) {
  return request({
    url: '/production/equipmentClean/' + id,
    method: 'delete'
  })
}

// 导出设备清洗记录
export function exportEquipmentClean(query) {
  return request({
    url: '/production/equipmentClean/export',
    method: 'get',
    params: query
  })
}

export function allEquipmentClean(query) {
  return request({
    url: '/production/equipmentClean/all',
    method: 'get',
    params: query
  })
}
