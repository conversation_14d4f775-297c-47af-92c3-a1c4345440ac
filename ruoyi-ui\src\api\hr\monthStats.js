import request from '@/utils/request'

// 查询考勤月度统计列表
export function listMonthStats(query) {
  return request({
    url: '/hr/monthStats/list',
    method: 'get',
    params: query
  })
}

// 查询考勤月度统计详细
export function getMonthStats(id) {
  return request({
    url: '/hr/monthStats/' + id,
    method: 'get'
  })
}
export  function getMonthStatsByUserId(query){
  return request({
    url: '/hr/monthStats/detail',
    method: 'get',
    params: query
  })
}

// 新增考勤月度统计
export function addMonthStats(data) {
  return request({
    url: '/hr/monthStats',
    method: 'post',
    data: data
  })
}

// 修改考勤月度统计
export function updateMonthStats(data) {
  return request({
    url: '/hr/monthStats',
    method: 'put',
    data: data
  })
}

// 删除考勤月度统计
export function delMonthStats(id) {
  return request({
    url: '/hr/monthStats/' + id,
    method: 'delete'
  })
}

// 导出考勤月度统计
export function export1MonthStats(query) {
  return request({
    url: '/hr/monthStats/export1',
    method: 'get',
    params: query
  })
}
