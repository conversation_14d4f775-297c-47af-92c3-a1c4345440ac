import request from '@/utils/request'

// 查询开票记录列表
export function listBillingApplyLog(query) {
  return request({
    url: '/order/billingApplyLog/list',
    method: 'get',
    params: query
  })
}

// 查询开票记录详细
export function getBillingApplyLog(id) {
  return request({
    url: '/order/billingApplyLog/' + id,
    method: 'get'
  })
}

// 新增开票记录
export function addBillingApplyLog(data) {
  return request({
    url: '/order/billingApplyLog',
    method: 'post',
    data: data
  })
}

// 修改开票记录
export function updateBillingApplyLog(data) {
  return request({
    url: '/order/billingApplyLog',
    method: 'put',
    data: data
  })
}

// 修改开票记录 发货信息
export function updateBillingApplyShipLog(data) {
  return request({
    url: '/order/billingApplyLog/ship',
    method: 'put',
    data: data
  })
}

// 删除开票记录
export function delBillingApplyLog(id) {
  return request({
    url: '/order/billingApplyLog/' + id,
    method: 'delete'
  })
}

// 导出开票记录
export function exportBillingApplyLog(query) {
  return request({
    url: '/order/billingApplyLog/export',
    method: 'get',
    params: query
  })
}

// 查询开票记录列表
export function getBillingApplyInfo(query) {
  return request({
    url: '/order/billingApply/getDetail',
    method: 'get',
    params: query
  })
}

export function allBillingApplyLog(query) {
  return request({
    url: '/order/billingApplyLog/all',
    method: 'get',
    params: query
  })
}
