import request from '@/utils/request'

export function listStockItem(query) {
  return request({
    url: '/order/stockItem/list',
    method: 'get',
    params: query
  })
}

export function listStockItemNew(query) {
  return request({
    url: '/order/stockItem/listNew',
    method: 'get',
    params: query
  })
}

export function allStockItem(query) {
  return request({
    url: '/order/stockItem/all',
    method: 'get',
    params: query
  })
}

export function updateNumsStockItem(data) {
  return request({
    url: '/order/stockItem/editNums',
    method: 'put',
    data: data
  })
}

export function updateBindStockItemInfo(data) {
  return request({
    url: '/order/stockItem/updateBindStockItemInfo',
    method: 'put',
    data: data
  })
}

export function getStockItem(id) {
  return request({
    url: '/order/stockItem/' + id,
    method: 'get'
  })
}

export function ckAllStockItem(query) {
  return request({
    url: '/order/stockItem/ckAll',
    method: 'get',
    params: query
  })
}

export function allGoodsBatchNoDataList(query) {
  return request({
    url: '/order/stockItem/allGoodsBatchNo',
    method: 'get',
    params: query
  })
}

export function undoRkStockItem(data) {
  return request({
    url: '/order/stockItem/undoRk',
    method: 'put',
    data: data
  })
}
