import request from '@/utils/request'

// 查询药食同源列表
export function listYsty(query) {
  return request({
    url: '/rd/ysty/list',
    method: 'get',
    params: query
  })
}

// 查询药食同源详细
export function getYsty(id) {
  return request({
    url: '/rd/ysty/' + id,
    method: 'get'
  })
}

// 新增药食同源
export function addYsty(data) {
  return request({
    url: '/rd/ysty',
    method: 'post',
    data: data
  })
}

// 修改药食同源
export function updateYsty(data) {
  return request({
    url: '/rd/ysty',
    method: 'put',
    data: data
  })
}

// 删除药食同源
export function delYsty(id) {
  return request({
    url: '/rd/ysty/' + id,
    method: 'delete'
  })
}

// 导出药食同源
export function exportYsty(query) {
  return request({
    url: '/rd/ysty/export',
    method: 'get',
    params: query
  })
}

export function importTemplateYsty() {
  return request({
    url: '/rd/ysty/importTemplate',
    method: 'get'
  })
}

export function allRdYsty(query) {
  return request({
    url: '/rd/ysty/all',
    method: 'get',
    params: query
  })
}
