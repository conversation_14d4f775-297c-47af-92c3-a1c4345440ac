import request from '@/utils/request'

// 查询订单商品列表
export function listGoods(query) {
  return request({
    url: '/order/goods/list',
    method: 'get',
    params: query
  })
}

// 查询订单商品统计列表
export function statsOrderGoods(query) {
  return request({
    url: '/order/goods/stats',
    method: 'get',
    params: query
  })
}

// 订单毛利润表
export function grossProfitOrderGoods(query) {
  return request({
    url: '/order/goods/grossProfit',
    method: 'get',
    params: query
  })
}

// 订单毛利润表
export function exportGrossProfitOrderGoods(query) {
  return request({
    url: '/order/goods/exportGrossProfit',
    method: 'get',
    params: query
  })
}


export function refreshGrossProfitOrderGoods(query) {
  return request({
    url: '/order/goods/refresh',
    method: 'get',
    params: query
  })
}

// 查询备货订单
export function stockUpOrderGoods(query) {
  return request({
    url: '/order/goods/stockUp',
    method: 'get',
    params: query
  })
}

// 查询备货订单抵扣明细
export function stockUpDeductionInfoOrderGoods(query) {
  return request({
    url: '/order/goods/stockUpDeductionInfo',
    method: 'get',
    params: query
  })
}

export function statsAllOrderGoods(query) {
  return request({
    url: '/order/goods/statsAll',
    method: 'get',
    params: query
  })
}

// 查询订单商品详细
export function getGoods(id) {
  return request({
    url: '/order/goods/' + id,
    method: 'get'
  })
}

// 查询订单商品详细
export function getTransitGoods(id) {
  return request({
    url: '/order/goods/transit/' + id,
    method: 'get'
  })
}

// 新增订单商品
export function addGoods(data) {
  return request({
    url: '/order/goods',
    method: 'post',
    data: data
  })
}

// 修改订单商品
export function updateGoods(data) {
  return request({
    url: '/order/goods',
    method: 'put',
    data: data
  })
}

// 修改订单商品
export function updateGoodsAnalysis(data) {
  return request({
    url: '/order/goods/updateAnalysis',
    method: 'put',
    data: data
  })
}

// 删除订单商品
export function delGoods(id) {
  return request({
    url: '/order/goods/' + id,
    method: 'delete'
  })
}

// 导出订单商品
export function exportGoods(query) {
  return request({
    url: '/order/goods/export',
    method: 'get',
    params: query
  })
}

export function allGoods(query) {
  return request({
    url: '/order/goods/all',
    method: 'get',
    params: query
  })
}

export function allSubGoods(query) {
  return request({
    url: '/order/goods/subAll',
    method: 'get',
    params: query
  })
}

export function rkListGoods(query) {
  return request({
    url: '/order/goods/rkList',
    method: 'get',
    params: query
  })
}

export function libraryListGoods(query) {
  return request({
    url: '/order/goods/libraryGoodsList',
    method: 'get',
    params: query
  })
}

export function getLibraryGoodsLogList(query) {
  return request({
    url: '/order/goods/libraryGoodsLogList',
    method: 'get',
    params: query
  })
}

export function allUnDzOtherGoods(query) {
  return request({
    url: '/order/goods/allUnDzOther',
    method: 'get',
    params: query
  })
}

export function baseAllGoods(query) {
  return request({
    url: '/order/goods/baseAll',
    method: 'get',
    params: query
  })
}

export function allOtherGoods(query) {
  return request({
    url: '/order/goods/allOtherGoods',
    method: 'get',
    params: query
  })
}

export function listRemindOrderGoodsStock(query) {
  return request({
    url: '/order/goods/remindOrderGoodsStock',
    method: 'get',
    params: query
  })
}

export function listRemindOrderGoodsCk(query) {
  return request({
    url: '/order/goods/remindOrderGoodsCk',
    method: 'get',
    params: query
  })
}

export function exportGoodsDebt(query) {
  return request({
    url: '/order/goods/debt/export',
    method: 'get',
    params: query
  })
}

export function exportGoodsCk(query) {
  return request({
    url: '/order/goods/ck/export',
    method: 'get',
    params: query
  })
}
export function exportGoodsKxdz(query) {
  return request({
    url: '/order/goods/kxdz/export',
    method: 'get',
    params: query
  })
}

export function exportTransitOrder(query) {
  return request({
    url: '/order/goods/transitOrder/export',
    method: 'get',
    params: query
  })
}
