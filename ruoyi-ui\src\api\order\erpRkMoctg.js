import request from '@/utils/request'

// 查询ERP入库信息列表
export function listErpRkMoctg(query) {
  return request({
    url: '/order/erpRkMoctg/list',
    method: 'get',
    params: query
  })
}

// 查询ERP入库信息详细
export function getErpRkMoctg(id) {
  return request({
    url: '/order/erpRkMoctg/' + id,
    method: 'get'
  })
}

// 新增ERP入库信息
export function addErpRkMoctg(data) {
  return request({
    url: '/order/erpRkMoctg',
    method: 'post',
    data: data
  })
}

// 修改ERP入库信息
export function updateErpRkMoctg(data) {
  return request({
    url: '/order/erpRkMoctg',
    method: 'put',
    data: data
  })
}

// 导出ERP入库信息
export function exportErpRkMoctg(query) {
  return request({
    url: '/order/erpRkMoctg/export',
    method: 'get',
    params: query
  })
}
