import request from '@/utils/request'

// 查询功效测试人员列表
export function listUser(query) {
  return request({
    url: '/gx/user/list',
    method: 'get',
    params: query
  })
}

// 查询功效测试人员详细
export function getUser(id) {
  return request({
    url: '/gx/user/' + id,
    method: 'get'
  })
}

// 新增功效测试人员
export function addUser(data) {
  return request({
    url: '/gx/user',
    method: 'post',
    data: data
  })
}

// 修改功效测试人员
export function updateUser(data) {
  return request({
    url: '/gx/user',
    method: 'put',
    data: data
  })
}

// 删除功效测试人员
export function delUser(id) {
  return request({
    url: '/gx/user/' + id,
    method: 'delete'
  })
}

// 导出功效测试人员
export function exportUser(query) {
  return request({
    url: '/gx/user/export',
    method: 'get',
    params: query
  })
}