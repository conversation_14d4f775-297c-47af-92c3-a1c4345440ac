import request from '@/utils/request'

// 查询料体利润率列表
export function listProjectOfferNrwProfit(query) {
  return request({
    url: '/project/projectOfferNrwProfit/list',
    method: 'get',
    params: query
  })
}

// 查询料体利润率详细
export function getProjectOfferNrwProfit(id) {
  return request({
    url: '/project/projectOfferNrwProfit/' + id,
    method: 'get'
  })
}

// 新增料体利润率
export function addProjectOfferNrwProfit(data) {
  return request({
    url: '/project/projectOfferNrwProfit',
    method: 'post',
    data: data
  })
}

// 修改料体利润率
export function updateProjectOfferNrwProfit(data) {
  return request({
    url: '/project/projectOfferNrwProfit',
    method: 'put',
    data: data
  })
}

// 删除料体利润率
export function delProjectOfferNrwProfit(id) {
  return request({
    url: '/project/projectOfferNrwProfit/' + id,
    method: 'delete'
  })
}

// 导出料体利润率
export function exportProjectOfferNrwProfit(query) {
  return request({
    url: '/project/projectOfferNrwProfit/export',
    method: 'get',
    params: query
  })
}

// 查询料体利润率列表
export function itemAll(query) {
  return request({
    url: '/project/projectOfferNrwProfit/itemAll',
    method: 'get',
    params: query
  })
}
