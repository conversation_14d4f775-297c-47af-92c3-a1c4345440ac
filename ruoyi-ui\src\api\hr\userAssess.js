import request from '@/utils/request'

// 查询试用期考核列表
export function listUserAssess(query) {
  return request({
    url: '/hr/userAssess/list',
    method: 'get',
    params: query
  })
}

// 查询试用期考核详细
export function getUserAssess(id) {
  return request({
    url: '/hr/userAssess/' + id,
    method: 'get'
  })
}

// 新增试用期考核
export function addUserAssess(data) {
  return request({
    url: '/hr/userAssess',
    method: 'post',
    data: data
  })
}

// 修改试用期考核
export function updateUserAssess(data) {
  return request({
    url: '/hr/userAssess',
    method: 'put',
    data: data
  })
}

// 删除试用期考核
export function delUserAssess(id) {
  return request({
    url: '/hr/userAssess/' + id,
    method: 'delete'
  })
}

// 导出试用期考核
export function exportUserAssess(query) {
  return request({
    url: '/hr/userAssess/export',
    method: 'get',
    params: query
  })
}

export function allUserAssess(query) {
  return request({
    url: '/hr/userAssess/all',
    method: 'get',
    params: query
  })
}
