{"name": "@logicflow/core", "version": "1.2.28", "description": "LogicFlow core, to quickly build flowchart editor", "main": "dist/entry.js", "module": "dist/logic-flow.js", "unpkg": "dist/logic-flow.min.js", "sideEffects": true, "jsdelivr": "dist/logic-flow.min.js", "license": "Apache-2.0", "homepage": "https://site.logic-flow.cn", "types": "types/index.d.ts", "repository": {"type": "git", "url": "https://github.com/didi/LogicFlow", "directory": "packages/core"}, "scripts": {"dev": "cross-env NODE_ENV=development webpack-dev-server --client-log-level warning --config scripts/webpack.config.dev.js", "clean": "<PERSON><PERSON><PERSON> dist lib esm cjs", "build": "npm run build:umd", "build:umd": "cross-env NODE_ENV=production webpack --config scripts/webpack.config.build.js && npm run copy", "build-analyse": "cross-env analyse=true npm run build", "types": "tsc -d --declarationDir ./types --outDir temp && rimraf temp", "lint": "eslint . --ext .ts,.tsx", "publish-lib": "npm run types & npm run clean && npm run build && npm publish", "publish-next": "npm run types & npm run clean && npm run build && npm publish --tag next", "copy": "node ./scripts/copy.js"}, "files": ["dist", "es", "types", "README.md"], "dependencies": {"@types/mousetrap": "^1.6.4", "mousetrap": "^1.6.5", "preact": "^10.4.8"}, "keywords": ["flowchart", "diagram"], "devDependencies": {"@babel/core": "^7.9.0", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-decorators": "^7.10.5", "@babel/plugin-syntax-jsx": "^7.8.3", "@babel/plugin-transform-react-jsx": "^7.10.4", "@babel/preset-env": "^7.9.5", "@babel/preset-typescript": "^7.9.0", "@types/lodash-es": "^4.17.3", "@typescript-eslint/eslint-plugin": "^3.6.1", "@typescript-eslint/parser": "^3.2.0", "autoprefixer": "^9.7.6", "babel-core": "^7.0.0-bridge.0", "babel-loader": "^8.1.0", "babel-plugin-import": "^1.13.0", "case-sensitive-paths-webpack-plugin": "^2.3.0", "classnames": "^2.2.6", "core-js": "^3.6.5", "cross-env": "^7.0.2", "css-loader": "^4.2.1", "eslint": "^7.0.0", "eslint-config-airbnb-typescript": "^9.0.0", "eslint-loader": "^4.0.2", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.20.6", "eslint-plugin-react-hooks": "^4.1.2", "eslint-plugin-standard": "^4.0.1", "eslint-webpack-plugin": "^2.1.0", "html-webpack-plugin": "^4.2.0", "husky": "^4.2.5", "ids": "^1.0.0", "less-loader": "^6.0.0", "lodash-es": "^4.17.15", "mobx": "^5.15.7", "mobx-react": "^6.3.0", "mobx-utils": "^5.6.1", "prettier": "^2.2.1", "rimraf": "^3.0.2", "standard-version": "^9.0.0", "style-loader": "^1.2.0", "style-resources-loader": "^1.3.3", "terser-webpack-plugin": "^4.2.3", "typescript": "^3.8.3", "url-loader": "^4.1.0", "uuid": "^8.2.0", "webpack": "^4.43.0", "webpack-bundle-analyzer": "^3.7.0", "webpack-cli": "^3.3.11", "webpack-dev-server": "^3.10.3"}, "browserslist": ["> 3%", "last 2 versions", "not ie <= 11"], "gitHead": "ab8cc6126e9dfa5c4fb18037538a374d3a0b0521"}