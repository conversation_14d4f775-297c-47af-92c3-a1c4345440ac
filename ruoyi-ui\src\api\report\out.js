import request from '@/utils/request'

export function listReportOut(query) {
  return request({
    url: '/report/out/list',
    method: 'get',
    params: query
  })
}
export function listReportOutUser(query) {
  return request({
    url: '/report/out/userList',
    method: 'get',
    params: query
  })
}
export function auditReportOut(query) {
  return request({
    url: '/report/out/audit',
    method: 'get',
    params: query
  })
}
export function auditUserReportOut(query) {
  return request({
    url: '/report/out/auditUser',
    method: 'get',
    params: query
  })
}
export function outUserReportOut(query) {
  return request({
    url: '/report/out/outUser',
    method: 'get',
    params: query
  })
}
