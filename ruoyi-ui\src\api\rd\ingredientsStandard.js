import request from '@/utils/request'

// 查询成分标准列表
export function listIngredientsStandard(query) {
  return request({
    url: '/rd/ingredientsStandard/list',
    method: 'get',
    params: query
  })
}

// 查询成分标准详细
export function getIngredientsStandard(id) {
  return request({
    url: '/rd/ingredientsStandard/' + id,
    method: 'get'
  })
}

// 新增成分标准
export function addIngredientsStandard(data) {
  return request({
    url: '/rd/ingredientsStandard',
    method: 'post',
    data: data
  })
}

// 修改成分标准
export function updateIngredientsStandard(data) {
  return request({
    url: '/rd/ingredientsStandard',
    method: 'put',
    data: data
  })
}

// 删除成分标准
export function delIngredientsStandard(id) {
  return request({
    url: '/rd/ingredientsStandard/' + id,
    method: 'delete'
  })
}

// 导出成分标准
export function exportIngredientsStandard(query) {
  return request({
    url: '/rd/ingredientsStandard/export',
    method: 'get',
    params: query
  })
}

export function allIngredientsStandard(query) {
  return request({
    url: '/rd/ingredientsStandard/all',
    method: 'get',
    params: query
  })
}
