import request from '@/utils/request'

// 查询办公用品入库记录列表
export function listOfficeGoodsRkLog(query) {
  return request({
    url: '/resource/officeGoodsRkLog/list',
    method: 'get',
    params: query
  })
}

// 查询办公用品入库记录详细
export function getOfficeGoodsRkLog(id) {
  return request({
    url: '/resource/officeGoodsRkLog/' + id,
    method: 'get'
  })
}

// 新增办公用品入库记录
export function addOfficeGoodsRkLog(data) {
  return request({
    url: '/resource/officeGoodsRkLog',
    method: 'post',
    data: data
  })
}

// 修改办公用品入库记录
export function updateOfficeGoodsRkLog(data) {
  return request({
    url: '/resource/officeGoodsRkLog',
    method: 'put',
    data: data
  })
}

// 删除办公用品入库记录
export function delOfficeGoodsRkLog(id) {
  return request({
    url: '/resource/officeGoodsRkLog/' + id,
    method: 'delete'
  })
}

// 导出办公用品入库记录
export function exportOfficeGoodsRkLog(query) {
  return request({
    url: '/resource/officeGoodsRkLog/export',
    method: 'get',
    params: query
  })
}

export function bpOfficeGoodsRkLog(data) {
  return request({
    url: '/resource/officeGoodsRkLog/bp',
    method: 'put',
    data
  })
}

export function exportOfficeGoodsSfc(query) {
  return request({
    url: '/resource/officeGoodsRkLog/exportSfc',
    method: 'get',
    params: query
  })
}

export function archiveGoods(data) {
  return request({
    url: '/resource/officeGoodsRkLog/archiveGoods',
    method: 'post',
    data,
  })
}

export function allOfficeGoodsRkLog(query) {
  return request({
    url: '/resource/officeGoodsRkLog/all',
    method: 'get',
    params: query
  })
}
