import request from '@/utils/request'

// 查询模具库商品列表
export function listResourceMouldGood(query) {
  return request({
    url: '/resource/resourceMouldGood/list',
    method: 'get',
    params: query
  })
}

// 查询模具库商品详细
export function getResourceMouldGood(id) {
  return request({
    url: '/resource/resourceMouldGood/' + id,
    method: 'get'
  })
}

// 新增模具库商品
export function addResourceMouldGood(data) {
  return request({
    url: '/resource/resourceMouldGood',
    method: 'post',
    data: data
  })
}

// 修改模具库商品
export function updateResourceMouldGood(data) {
  return request({
    url: '/resource/resourceMouldGood',
    method: 'put',
    data: data
  })
}

// 删除模具库商品
export function delResourceMouldGood(id) {
  return request({
    url: '/resource/resourceMouldGood/' + id,
    method: 'delete'
  })
}

// 导出模具库商品
export function exportResourceMouldGood(query) {
  return request({
    url: '/resource/resourceMouldGood/export',
    method: 'get',
    params: query
  })
}
