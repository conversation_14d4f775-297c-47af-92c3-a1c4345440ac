import request from '@/utils/request'

// 查询费用报销付款明细列表
export function listReimbursement(query) {
  return request({
    url: '/finance/reimbursement/list',
    method: 'get',
    params: query
  })
}

// 查询费用报销付款明细详细
export function getReimbursement(id) {
  return request({
    url: '/finance/reimbursement/' + id,
    method: 'get'
  })
}

// 新增费用报销付款明细
export function addReimbursement(data) {
  return request({
    url: '/finance/reimbursement',
    method: 'post',
    data: data
  })
}

export function addBatchReimbursement(data) {
  return request({
    url: '/finance/reimbursement/addBatch',
    method: 'post',
    data: data
  })
}

// 修改费用报销付款明细
export function updateReimbursement(data) {
  return request({
    url: '/finance/reimbursement',
    method: 'put',
    data: data
  })
}

// 删除费用报销付款明细
export function delReimbursement(id) {
  return request({
    url: '/finance/reimbursement/' + id,
    method: 'delete'
  })
}

// 导出费用报销付款明细
export function exportReimbursement(query) {
  return request({
    url: '/finance/reimbursement/export',
    method: 'get',
    params: query
  })
}

export function getReimbursementItem(query) {
  return request({
    url: '/finance/reimbursementitem/getReimbursementItem',
    method: 'get',
    params: query
  })
}

export function batchReimbursementItem(data) {
  return request({
    url: '/finance/reimbursementitem/batchReimbursementItem',
    method: 'post',
    data: data
  })
}

export function listReimbursementItem(query) {
  return request({
    url: '/finance/reimbursementitem/list',
    method: 'get',
    params: query
  })
}

export function listByDept(query) {
  return request({
    url: '/finance/reimbursementitem/listByDept',
    method: 'get',
    params: query
  })
}

export function listByExpenseId(query) {
  return request({
    url: '/finance/reimbursementitem/listByExpenseId',
    method: 'get',
    params: query
  })
}

export function updateItemState(id) {
  return request({
    url: '/finance/reimbursementitem/updateItemState/' + id,
    method: 'put'
  })
}

export function getItemExcel(query) {
  return request({
    url: '/finance/reimbursementitem/getItemExcel',
    method: 'get',
    params: query
  })
}

export function getItemExcelByDept(query) {
  return request({
    url: '/finance/reimbursementitem/getItemExcelByDept',
    method: 'get',
    params: query
  })
}

export function getImport(query) {
  return request({
    url: '/finance/reimbursementitem/getImport',
    method: 'get',
    params: query
  })
}



export function getItemExcelSummary(query) {
  return request({
    url: '/finance/reimbursementitem/getItemExcelSummary',
    method: 'get',
    params: query
  })
}



// 查询费用报销付款明细子详细
export function getReimbursementitem(id) {
  return request({
    url: '/finance/reimbursementitem/' + id,
    method: 'get'
  })
}

// 新增费用报销付款明细子
export function addReimbursementitem(data) {
  return request({
    url: '/finance/reimbursementitem',
    method: 'post',
    data: data
  })
}

// 修改费用报销付款明细子
export function updateReimbursementitem(data) {
  return request({
    url: '/finance/reimbursementitem',
    method: 'put',
    data: data
  })
}

// 删除费用报销付款明细子
export function delReimbursementitem(id) {
  return request({
    url: '/finance/reimbursementitem/' + id,
    method: 'delete'
  })
}

// 导出费用报销付款明细子
export function exportReimbursementitem(query) {
  return request({
    url: '/finance/reimbursementitem/export',
    method: 'get',
    params: query
  })
}

export function getPickerOptions(query) {
  return request({
    url: '/finance/reimbursementitem/getPickerOptions',
    method: 'get',
    params: query
  })
}




