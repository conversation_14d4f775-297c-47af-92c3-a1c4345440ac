import request from '@/utils/request'

// 查询mes生产记录列表
export function listMesLog(query) {
  return request({
    url: '/production/mesLog/list',
    method: 'get',
    params: query
  })
}

// 查询mes生产记录详细
export function getMesLog(id) {
  return request({
    url: '/production/mesLog/' + id,
    method: 'get'
  })
}

// 新增mes生产记录
export function addMesLog(data) {
  return request({
    url: '/production/mesLog',
    method: 'post',
    data: data
  })
}

// 修改mes生产记录
export function updateMesLog(data) {
  return request({
    url: '/production/mesLog',
    method: 'put',
    data: data
  })
}

// 删除mes生产记录
export function delMesLog(id) {
  return request({
    url: '/production/mesLog/' + id,
    method: 'delete'
  })
}

// 导出mes生产记录
export function exportMesLog(query) {
  return request({
    url: '/production/mesLog/export',
    method: 'get',
    params: query
  })
}

export function getMaterialLogList(id) {
  return request({
    url: '/production/mesLog/materialLogList/' + id,
    method: 'get'
  })
}

export function exportMesLogProductionLog(data) {
  return request({
    url: '/production/mesLog/exportProductionLog',
    method: 'post',
    data,
  })
}
