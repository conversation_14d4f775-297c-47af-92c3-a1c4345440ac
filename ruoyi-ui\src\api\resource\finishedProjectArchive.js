import request from '@/utils/request'

// 查询成品sop变更记录列表
export function listFinishedProjectArchive(query) {
  return request({
    url: '/resource/finishedProjectArchive/list',
    method: 'get',
    params: query
  })
}

// 查询成品sop变更记录详细
export function getFinishedProjectArchive(id) {
  return request({
    url: '/resource/finishedProjectArchive/' + id,
    method: 'get'
  })
}

// 新增成品sop变更记录
export function addFinishedProjectArchive(data) {
  return request({
    url: '/resource/finishedProjectArchive',
    method: 'post',
    data: data
  })
}

// 修改成品sop变更记录
export function updateFinishedProjectArchive(data) {
  return request({
    url: '/resource/finishedProjectArchive',
    method: 'put',
    data: data
  })
}

// 导出成品sop变更记录
export function exportFinishedProjectArchive(query) {
  return request({
    url: '/resource/finishedProjectArchive/export',
    method: 'get',
    params: query
  })
}
