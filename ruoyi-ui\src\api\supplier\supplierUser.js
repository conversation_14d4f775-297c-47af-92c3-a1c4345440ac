import request from '@/utils/request'

// 查询供应商账号列表
export function listSupplierUser(query) {
  return request({
    url: '/supplier/user/list',
    method: 'get',
    params: query
  })
}

export function getSelectSupplier(query) {
  return request({
    url: '/supplier/user/getSelectSupplier',
    method: 'get',
    params: query
  })
}

// 查询供应商账号详细
export function getSupplierUser(userId) {
  return request({
    url: '/supplier/user/' + userId,
    method: 'get'
  })
}

// 新增供应商账号
export function addSupplierUser(data) {
  return request({
    url: '/supplier/user',
    method: 'post',
    data: data
  })
}

// 修改供应商账号
export function updateSupplierUser(data) {
  return request({
    url: '/supplier/user',
    method: 'put',
    data: data
  })
}

// 删除供应商账号
export function delSupplierUser(userId) {
  return request({
    url: '/supplier/user/' + userId,
    method: 'delete'
  })
}

// 导出供应商账号
export function exportSupplierUser(query) {
  return request({
    url: '/supplier/user/export',
    method: 'get',
    params: query
  })
}

// 修改用户信息
export function updateSupplierProfile(data) {
  return request({
    url: '/supplier/user/updateProfile',
    method: 'put',
    data: data
  })
}

// 用户密码重置
export function updateSupplierPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: '/supplier/user/updatePwd',
    method: 'put',
    params: data
  })
}

// 用户密码重置
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password
  }
  return request({
    url: '/supplier/user/resetPwd',
    method: 'put',
    data: data
  })
}

export function queryMenus(query) {
  return request({
    url: '/supplier/user/queryMenus',
    method: 'get',
    params: query
  })
}

export function submitUserMenu(data) {
  return request({
    url: '/supplier/user/submitUserMenu',
    method: 'post',
    data: data
  })
}


