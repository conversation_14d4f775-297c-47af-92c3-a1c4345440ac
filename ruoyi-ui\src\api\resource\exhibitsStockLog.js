import request from '@/utils/request'

// 查询展品库存记录列表
export function listExhibitsStockLog(query) {
  return request({
    url: '/resource/exhibitsStockLog/list',
    method: 'get',
    params: query
  })
}

// 查询展品库存记录详细
export function getExhibitsStockLog(id) {
  return request({
    url: '/resource/exhibitsStockLog/' + id,
    method: 'get'
  })
}

// 新增展品库存记录
export function addExhibitsStockLog(data) {
  return request({
    url: '/resource/exhibitsStockLog',
    method: 'post',
    data: data
  })
}

// 修改展品库存记录
export function updateExhibitsStockLog(data) {
  return request({
    url: '/resource/exhibitsStockLog',
    method: 'put',
    data: data
  })
}

// 删除展品库存记录
export function delExhibitsStockLog(id) {
  return request({
    url: '/resource/exhibitsStockLog/' + id,
    method: 'delete'
  })
}

// 导出展品库存记录
export function exportExhibitsStockLog(query) {
  return request({
    url: '/resource/exhibitsStockLog/export',
    method: 'get',
    params: query
  })
}