{"name": "jspdf", "version": "2.5.2", "homepage": "https://github.com/mrrio/jspdf", "description": "PDF Document creation from JavaScript", "main": "dist/jspdf.node.min.js", "module": "dist/jspdf.es.min.js", "browser": "dist/jspdf.es.min.js", "files": ["dist", "types/index.d.ts", "README.md", "LICENSE"], "typings": "types/index.d.ts", "types": "types/index.d.ts", "keywords": ["pdf"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/MrRio/jsPDF.git"}, "dependencies": {"@babel/runtime": "^7.23.2", "atob": "^2.1.2", "btoa": "^1.2.1", "fflate": "^0.8.1"}, "optionalDependencies": {"canvg": "^3.0.6", "core-js": "^3.6.0", "dompurify": "^2.5.4", "html2canvas": "^1.0.0-rc.5"}, "devDependencies": {"@babel/core": "^7.10.4", "@babel/plugin-transform-runtime": "^7.14.5", "@babel/preset-env": "^7.10.4", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-replace": "^2.3.3", "@rollup/plugin-typescript": "^8.0.0", "@types/jasmine": "^3.5.11", "@types/node": "^20.16.5", "@typescript-eslint/eslint-plugin": "^3.6.0", "@typescript-eslint/parser": "^3.6.0", "chalk": "^4.1.0", "codeclimate-test-reporter": "^0.5.1", "core-js": "^3.33.0", "diff": "^4.0.2", "docdash": "^1.2.0", "eslint": "^7.4.0", "eslint-plugin-jasmine": "^4.1.1", "folder-delete": "^1.0.4", "inquirer": "^6.5.2", "jasmine": "3.5.0", "jasmine-core": "3.5.0", "jasmine-expect": "4.0.3", "js-yaml": "3.13.1", "jsdoc": "^3.6.3", "karma": "5.1.0", "karma-babel-preprocessor": "8.0.1", "karma-chrome-launcher": "3.1.0", "karma-coverage": "2.0.2", "karma-firefox-launcher": "1.3.0", "karma-ie-launcher": "1.0.0", "karma-jasmine": "3.3.1", "karma-jasmine-matchers": "4.0.2", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "^7.0.7", "karma-sauce-launcher": "4.1.5", "karma-typescript": "^5.5.4", "karma-verbose-reporter": "0.0.6", "local-web-server": "^4.2.1", "log-utils": "^1.0.0", "markdown": "0.5.0", "preprocess": "^3.2.0", "prettier": "^1.19.1", "regenerator-runtime": "^0.13.5", "requirejs": "^2.3.6", "rollup": "^2.18.2", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-license": "^2.1.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-preprocess": "0.0.4", "rollup-plugin-terser": "^6.1.0", "typescript": "^5.6.2", "yarpm": "^0.2.1"}, "scripts": {"start": "ws", "build": "rollup --config rollup.config.js", "version": "yarpm run build && yarpm run generate-docs && git add -A dist docs", "pretest": "yarpm run build", "test": "yarpm run test-node && yarpm run test-saucelabs", "test-saucelabs": "karma start test/saucelabs/karma.conf.js --single-run --verbose && for a in coverage/*; do codeclimate-test-reporter < \\\"$a/lcov.info\\\"; break; done", "test-local": "yarpm run test-unit && yarpm run test-node && yarpm run test-amd && yarpm run test-esm && yarpm run test-globals && yarpm run test-typescript && yarpm run test-webworker", "test-unit": "karma start test/unit/karma.conf.js --single-run", "test-amd": "karma start test/deployment/amd/karma.conf.js --single-run", "test-esm": "karma start test/deployment/esm/karma.conf.js --single-run", "test-globals": "karma start test/deployment/globals/karma.conf.js --single-run", "test-typescript": "karma start test/deployment/typescript/karma.conf.js --single-run", "test-webworker": "karma start test/deployment/webworker/karma.conf.js --single-run", "test-node": "jasmine --config=test/deployment/node/jasmine.json", "test-training": "node test/utils/reference-server.js", "test-typings": "tsc -p types/tsconfig.json && tsc -p types/tsconfig-node.json", "prettier": "prettier --write \"*.{js,ts,md,css,json}\" \"{spec,examples,src,types}/**/*.{js,ts,md,css,json}\"", "lint": "prettier --check \"*.{js,ts,md,css,json}\" \"{spec,examples,src,types}/**/*.{js,ts,md,css,json}\"", "pregenerate-docs": "node deletedocs.js", "generate-docs": "jsdoc -c jsdoc.json --readme README.md"}, "browserslist": ["last 2 versions", "IE 11"]}