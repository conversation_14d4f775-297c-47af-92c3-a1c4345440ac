import request from '@/utils/request'

// 查询成品检验项目模板明细列表
export function listFinishedProjectModelItem(query) {
  return request({
    url: '/qc/finishedProjectModelItem/list',
    method: 'get',
    params: query
  })
}

// 查询成品检验项目模板明细列表
export function listFinishedProjectModelItemAll(query) {
  return request({
    url: '/qc/finishedProjectModelItem/all',
    method: 'get',
    params: query
  })
}

// 查询成品检验项目模板明细详细
export function getFinishedProjectModelItem(id) {
  return request({
    url: '/qc/finishedProjectModelItem/' + id,
    method: 'get'
  })
}

// 新增成品检验项目模板明细
export function addFinishedProjectModelItem(data) {
  return request({
    url: '/qc/finishedProjectModelItem',
    method: 'post',
    data: data
  })
}

// 修改成品检验项目模板明细
export function updateFinishedProjectModelItem(data) {
  return request({
    url: '/qc/finishedProjectModelItem',
    method: 'put',
    data: data
  })
}

// 删除成品检验项目模板明细
export function delFinishedProjectModelItem(id) {
  return request({
    url: '/qc/finishedProjectModelItem/' + id,
    method: 'delete'
  })
}

// 导出成品检验项目模板明细
export function exportFinishedProjectModelItem(query) {
  return request({
    url: '/qc/finishedProjectModelItem/export',
    method: 'get',
    params: query
  })
}

export function allFinishedProjectModelItem(query) {
  return request({
    url: '/qc/finishedProjectModelItem/all',
    method: 'get',
    params: query
  })
}
