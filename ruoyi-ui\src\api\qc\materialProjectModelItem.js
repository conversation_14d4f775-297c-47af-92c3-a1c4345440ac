import request from '@/utils/request'

// 查询包材检验项目模板明细列表
export function listMaterialProjectModelItem(query) {
  return request({
    url: '/qc/materialProjectModelItem/list',
    method: 'get',
    params: query
  })
}


// 查询包材检验项目模板明细列表
export function listMaterialProjectModelItemAll(query) {
  return request({
    url: '/qc/materialProjectModelItem/all',
    method: 'get',
    params: query
  })
}


// 查询包材检验项目模板明细详细
export function getMaterialProjectModelItem(id) {
  return request({
    url: '/qc/materialProjectModelItem/' + id,
    method: 'get'
  })
}

// 新增包材检验项目模板明细
export function addMaterialProjectModelItem(data) {
  return request({
    url: '/qc/materialProjectModelItem',
    method: 'post',
    data: data
  })
}

// 修改包材检验项目模板明细
export function updateMaterialProjectModelItem(data) {
  return request({
    url: '/qc/materialProjectModelItem',
    method: 'put',
    data: data
  })
}

// 删除包材检验项目模板明细
export function delMaterialProjectModelItem(id) {
  return request({
    url: '/qc/materialProjectModelItem/' + id,
    method: 'delete'
  })
}

// 导出包材检验项目模板明细
export function exportMaterialProjectModelItem(query) {
  return request({
    url: '/qc/materialProjectModelItem/export',
    method: 'get',
    params: query
  })
}
