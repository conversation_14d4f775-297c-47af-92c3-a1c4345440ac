import request from '@/utils/request'

// 查询文件确认列表
export function listConfirm(query) {
  return request({
    url: '/filemanage/confirm/list',
    method: 'get',
    params: query
  })
}

// 查询文件确认详细
export function getConfirm(id) {
  return request({
    url: '/filemanage/confirm/' + id,
    method: 'get'
  })
}

// 新增文件确认
export function addConfirm(data) {
  return request({
    url: '/filemanage/confirm',
    method: 'post',
    data: data
  })
}

// 修改文件确认
export function updateConfirm(data) {
  return request({
    url: '/filemanage/confirm',
    method: 'put',
    data: data
  })
}

// 删除文件确认
export function delConfirm(id) {
  return request({
    url: '/filemanage/confirm/' + id,
    method: 'delete'
  })
}

export function handleConfirm(data) {
  return request({
    url: '/filemanage/confirm/confirmByIds',
    method: 'post',
    data: data
  })
}


// 导出文件确认
export function exportConfirm(query) {
  return request({
    url: '/filemanage/confirm/export',
    method: 'get',
    params: query
  })
}
