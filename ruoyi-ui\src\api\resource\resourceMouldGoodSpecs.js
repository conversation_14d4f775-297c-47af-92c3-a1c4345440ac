import request from '@/utils/request'

// 查询模具库商品规格列表
export function listResourceMouldGoodSpecs(query) {
  return request({
    url: '/resource/resourceMouldGoodSpecs/list',
    method: 'get',
    params: query
  })
}

// 查询模具库商品规格详细
export function getResourceMouldGoodSpecs(id) {
  return request({
    url: '/resource/resourceMouldGoodSpecs/' + id,
    method: 'get'
  })
}

// 新增模具库商品规格
export function addResourceMouldGoodSpecs(data) {
  return request({
    url: '/resource/resourceMouldGoodSpecs',
    method: 'post',
    data: data
  })
}

// 修改模具库商品规格
export function updateResourceMouldGoodSpecs(data) {
  return request({
    url: '/resource/resourceMouldGoodSpecs',
    method: 'put',
    data: data
  })
}

// 删除模具库商品规格
export function delResourceMouldGoodSpecs(id) {
  return request({
    url: '/resource/resourceMouldGoodSpecs/' + id,
    method: 'delete'
  })
}

// 导出模具库商品规格
export function exportResourceMouldGoodSpecs(query) {
  return request({
    url: '/resource/resourceMouldGoodSpecs/export',
    method: 'get',
    params: query
  })
}

export function allResourceMouldGoodSpecs(query) {
  return request({
    url: '/resource/resourceMouldGoodSpecs/all',
    method: 'get',
    params: query
  })
}
