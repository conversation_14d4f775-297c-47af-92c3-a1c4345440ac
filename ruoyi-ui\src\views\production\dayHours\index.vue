<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" v-show="showSearch" size="mini" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="厂区" prop="factory">
            <el-select v-model="queryParams.factory" >
              <el-option
                v-for="item in factoryOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="排班日期" prop="workDate">
            <el-date-picker
              clearable
              v-model="queryParams.workDate"
              type="date"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          :loading="btnLoading"
          @click="handleExport"
          v-hasPermi="['production:dayHours:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="dayHoursList">
      <el-table-column label="排班日期" width="120" align="center" >
        <template v-slot="scope">
          <span style="cursor: pointer;color: #00afff" @click="handleUpdate(scope.row,true)">{{ scope.row.workDate }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工厂" width="120" align="center" prop="factory" >
        <template v-slot="scope" >
          <span style="color: #1c84c6" v-if="scope.row.factory === 'COMPANY_YN'" >宜侬</span>
          <span style="color: #FF99CC" v-if="scope.row.factory === 'COMPANY_YC'" >瀛彩</span>
        </template>
      </el-table-column>
      <el-table-column label="正式工人数" width="120" align="center" prop="userNums"/>
      <el-table-column label="劳务工人数" width="120" align="center" prop="laborNums"/>
      <el-table-column label="包干工人数" width="120" align="center" prop="outerNums"/>
      <el-table-column label="正式工工时" width="120" align="center" >
        <template v-slot="scope" >
          {{minutesToHours(scope.row.userMinutes).toFixed(2)}}
        </template>
      </el-table-column>
      <el-table-column label="劳务工工时" width="120" align="center" >
        <template v-slot="scope" >
          {{minutesToHours(scope.row.laborMinutes).toFixed(2)}}
        </template>
      </el-table-column>
      <el-table-column label="包干工工时" width="120" align="center" prop="outerHours" >
        <template v-slot="scope" >
          {{minutesToHours(scope.row.outerMinutes).toFixed(2)}}
        </template>
      </el-table-column>
      <el-table-column label="总工时" width="120" align="center" prop="sumHours">
        <template v-slot="scope" >
          {{minutesToHours(scope.row.sumMinutes).toFixed(2)}}
        </template>
      </el-table-column>
      <el-table-column label="有效工时" width="120" align="center" prop="effectiveHours">
        <template v-slot="scope" >
          {{minutesToHours(scope.row.effectiveMinutes).toFixed(2)}}
        </template>
      </el-table-column>
      <el-table-column label="无效工时" width="120" align="center" prop="invalidHours">
        <template v-slot="scope" >
          {{minutesToHours(scope.row.invalidMinutes).toFixed(2)}}
        </template>
      </el-table-column>
      <el-table-column label="休息工时" width="120" align="center" prop="restHours">
        <template v-slot="scope" >
          {{minutesToHours(scope.row.restMinutes).toFixed(2)}}
        </template>
      </el-table-column>
      <el-table-column label="间接工时" width="120" align="center" prop="otherHours">
        <template v-slot="scope" >
          {{minutesToHours(scope.row.otherMinutes).toFixed(2)}}
        </template>
      </el-table-column>
      <el-table-column label="管理工时" width="120" align="center" prop="manageHours">
        <template v-slot="scope" >
          {{minutesToHours(scope.row.manageMinutes).toFixed(2)}}
        </template>
      </el-table-column>
      <el-table-column label="工资工时" width="120" align="center" prop="wagesHours">
        <template v-slot="scope" >
          {{minutesToHours(scope.row.wagesMinutes).toFixed(2)}}
        </template>
      </el-table-column>
      <el-table-column label="推送人" width="120" align="center" prop="pushUser"/>
      <el-table-column label="推送时间" width="160" align="center" prop="pushTime"/>
      <el-table-column label="备注" width="120" align="center" prop="remark"/>
      <el-table-column fixed="right" label="操作" width="120" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-tooltip content="修改" placement="top">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['production:dayHours:edit']"
            />
          </el-tooltip>
          <el-tooltip content="导出工资工时" placement="top">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-download"
              :loading="btnLoading"
              @click="exportWagesHours(scope.row.id)"
            />
          </el-tooltip>
          <el-tooltip content="导出异常工时" placement="top">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-download"
              :loading="btnLoading"
              @click="exportAbnormalHours(scope.row.id)"
            />
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['production:dayHours:remove']"
            />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog :fullscreen="fullscreenFlag" :visible.sync="open" width="1200px" :close-on-click-modal="false"
               append-to-body>
      <div class="dialog-title" slot="title">
        <div>
          <span style="color: #1c84c6" v-if="currentRow.factory === 'COMPANY_YN'" >宜侬</span>
          <span style="color: #FF99CC" v-if="currentRow.factory === 'COMPANY_YC'" >瀛彩</span>
          ({{currentRow.workDate}})
        </div>
        <el-button @click="fullscreenFlag = !fullscreenFlag" type="text"
                   :icon="fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'"/>
      </div>
      <DayHoursSave ref="dayHoursSave" />
    </el-dialog>
  </div>
</template>

<script>
import {
  listDayHours,
  delDayHours,
  exportDayHours,
  exportWagesHours, exportAbnormalHours
} from "@/api/production/dayHours";
import DayHoursSave from "@/views/production/dayHours/save.vue";

export default {
  name: "DayHours",
  components: {DayHoursSave},
  data() {
    return {
      loading: false,
      btnLoading: false,
      fullscreenFlag: true,
      single: true,
      showSearch: false,
      total: 0,
      dayHoursList: [],
      title: "",
      open: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        factory: null,
        workDate: null,
      },
      currentRow: {},
      factoryOptions: [
        {label:'宜侬',value: 'COMPANY_YN',},
        {label:'瀛彩',value: 'COMPANY_YC',},
      ],
    };
  },
  async created() {
    await this.getList()
  },
  methods: {
    async getList() {
      let params = Object.assign({}, this.queryParams)
      this.loading = true
      let res = await listDayHours(params)
      this.loading = false
      this.dayHoursList = res.rows
      this.total = res.total
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    async handleUpdate(row) {
      this.currentRow = row
      this.open = true
      await this.$nextTick()
      const dayHoursSave = this.$refs.dayHoursSave
      if(dayHoursSave) {
        dayHoursSave.reset()
        await dayHoursSave.init(row.id)
      }
    },
    async handleDelete(row) {
      try {
        await this.$confirm('是否确认删除生产工时日报?')
        this.btnLoading = true
        const res = await delDayHours(row.id)
        if (res.code === 200) {
          await this.getList()
          this.msgSuccess("删除成功")
        }
        this.btnLoading = false
      } catch (e) {
        this.btnLoading = false
      }
    },
    async handleExport() {
      try {
        const queryParams = this.queryParams;
        await this.$confirm('是否确认导出所有生产工时日报数据项?')
        this.btnLoading = true
        const res = await exportDayHours(queryParams)
        this.btnLoading = false
        this.download(res.msg)
      } catch (e) {
        this.btnLoading = false
      }
    },
    async exportWagesHours(id) {
      try {
        await this.$confirm('是否确认导出?')
        this.btnLoading = true
        let res = await exportWagesHours(id)
        this.btnLoading = false
        this.download(res.msg);
      } catch (e) {
        this.btnLoading = false
      }
    },
    async exportAbnormalHours(id) {
      try {
        await this.$confirm('是否确认导出?')
        this.btnLoading = true
        let res = await exportAbnormalHours(id)
        this.btnLoading = false
        this.download(res.msg);
      } catch (e) {
        this.btnLoading = false
      }
    },
  }
};
</script>
