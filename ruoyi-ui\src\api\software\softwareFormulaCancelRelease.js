import request from '@/utils/request'

// 查询取消配方释放列表
export function listSoftwareFormulaCancelRelease(query) {
  return request({
    url: '/software/softwareFormulaCancelRelease/list',
    method: 'get',
    params: query
  })
}

//配方审核列表
export function listAuditFormulaCancelRealeaseData(data) {
  return request({
    url: '/software/softwareFormulaCancelRelease/audit',
    method: 'post',
    data: data
  })
}


// 查询取消配方释放详细
export function getSoftwareFormulaCancelRelease(id) {
  return request({
    url: '/software/softwareFormulaCancelRelease/' + id,
    method: 'get'
  })
}

// 新增取消配方释放
export function addSoftwareFormulaCancelRelease(data) {
  return request({
    url: '/software/softwareFormulaCancelRelease',
    method: 'post',
    data: data
  })
}

// 修改取消配方释放
export function updateSoftwareFormulaCancelRelease(data) {
  return request({
    url: '/software/softwareFormulaCancelRelease',
    method: 'put',
    data: data
  })
}

// 删除取消配方释放
export function delSoftwareFormulaCancelRelease(id) {
  return request({
    url: '/software/softwareFormulaCancelRelease/' + id,
    method: 'delete'
  })
}

// 导出取消配方释放
export function exportSoftwareFormulaCancelRelease(query) {
  return request({
    url: '/software/softwareFormulaCancelRelease/export',
    method: 'get',
    params: query
  })
}

//撤销审核
export function cancelAudit(data) {
  return request({
    url: '/software/softwareFormulaCancelRelease/cancelAudit',
    method: 'put',
    data: data
  })
}
