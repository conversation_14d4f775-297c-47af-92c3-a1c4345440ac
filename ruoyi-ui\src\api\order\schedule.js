import request from '@/utils/request'

export function listSchedule(query) {
  return request({
    url: '/order/schedule/list',
    method: 'get',
    params: query
  })
}

export function listAuditSchedule(query) {
  return request({
    url: '/order/schedule/listAudit',
    method: 'get',
    params: query
  })
}

export function batchExportOrderBalanceMaterial(data) {
  return request({
    url: '/order/schedule/batchExportOrderBalanceMaterial',
    method: 'post',
    data
  })
}

export function exportMaterialDetail(data) {
  return request({
    url: '/order/schedule/exportMaterialDetail',
    method: 'post',
    data
  })
}

// 查询包材原料价格审核列表
export function listHistory(query) {
  return request({
    url: '/order/schedule/history',
    method: 'get',
    params: query
  })
}


export function listOrder(query) {
  return request({
    url: '/order/schedule/allOrderList',
    method: 'get',
    params: query
  })
}

export function listProductionOrder(query) {
  return request({
    url: '/order/schedule/allProductionOrderList',
    method: 'get',
    params: query
  })
}

export function listOrderGoods(query) {
  return request({
    url: '/order/schedule/allOrderGoodsList',
    method: 'get',
    params: query
  })
}
export function listProductionOrderGoods(query) {
  return request({
    url: '/order/schedule/allProductionOrderGoodsList',
    method: 'get',
    params: query
  })
}

export function orderAllList(query) {
  return request({
    url: '/order/schedule/orderList',
    method: 'get',
    params: query
  })
}

export function orderGoodsAllList(query) {
  return request({
    url: '/order/schedule/orderGoodsList',
    method: 'get',
    params: query
  })
}

export function orderBalanceSheet(query) {
  return request({
    url: '/order/schedule/orderBalanceSheetList',
    method: 'get',
    params: query
  })
}

// 查询排班计划/生产排班详细
export function getSchedule(id) {
  return request({
    url: '/order/schedule/' + id,
    method: 'get'
  })
}

export function getScheduleByCode(code) {
  return request({
    url: '/order/schedule/getByCode/' + code,
    method: 'get'
  })
}

// 查询排班计划/生产排班详细
export function getScheduleDetail(id) {
  return request({
    url: '/order/schedule/detail/' + id,
    method: 'get'
  })
}

export function getScheduleProductionRecordFormData(id) {
  return request({
    url: '/order/schedule/getScheduleProductionRecordFormData/' + id,
    method: 'get'
  })
}



// 新增排班计划/生产排班
export function addScheduleProductionRecordData(data) {
  return request({
    url: '/order/schedule/addScheduleProductionRecordData',
    method: 'post',
    data: data
  })
}



// 新增排班计划/生产排班
export function addSchedule(data) {
  return request({
    url: '/order/schedule',
    method: 'post',
    data: data
  })
}

// 修改排班计划/生产排班 记录日志等等
export function updateSchedule(data) {
  return request({
    url: '/order/schedule',
    method: 'put',
    data: data
  })
}

// 修改排班计划/生产排班 记录日志等等
export function pushOrderSchedule(data) {
  return request({
    url: '/order/schedule/pushOrderSchedule',
    method: 'put',
    data: data
  })
}

//修改基础信息
export function updateScheduleInfo(data) {
  return request({
    url: '/order/schedule/editInfo',
    method: 'put',
    data: data
  })
}

// 修改排班计划/生产排班时间
export function updateScheduleTime(data) {
  return request({
    url: '/order/schedule/editScheduleTime',
    method: 'put',
    data: data
  })
}

// 修改排班计划/生产排班状态
export function updateScheduleStatus(data) {
  return request({
    url: '/order/schedule/editScheduleStatus',
    method: 'put',
    data: data
  })
}

// 刷新排班
export function handleRefreshData(data) {
  return request({
    url: '/order/schedule/handleRefreshData',
    method: 'get',
    data: data
  })
}

// 删除排班计划/生产排班
export function delSchedule(id) {
  return request({
    url: '/order/schedule/' + id,
    method: 'delete'
  })
}

// 导出排班计划/生产排班
export function exportBalanceSchedule(data) {
  return request({
    url: '/order/schedule/exportBalance',
    method: 'post',
    data,
  })
}

export function exportScheduleBalance(data) {
  return request({
    url: '/order/schedule/exportScheduleBalance',
    method: 'post',
    data,
  })
}

export function exportScheduleHoursData(data) {
  return request({
    url: '/order/schedule/exportScheduleHoursData',
    method: 'post',
    data,
  })
}

export function exportScheduleCostData(data) {
  return request({
    url: '/order/schedule/exportScheduleCostData',
    method: 'post',
    data,
  })
}


export function refreshOrderBalanceData(data) {
  return request({
    url: '/order/schedule/refreshOrderBalanceData',
    method: 'post',
    data,
  })
}

export function refreshOrderBalanceMaterialData(data) {
  return request({
    url: '/order/schedule/refreshOrderBalanceMaterialData',
    method: 'post',
    data,
  })
}


export function exportOrderBalance(data) {
  return request({
    url: '/order/schedule/exportOrderBalance',
    method: 'post',
    data,
  })
}

export function importTemplateSchedule() {
  return request({
    url: '/order/schedule/importTemplate',
    method: 'get'
  })
}

//根据工单获取平衡表数据
export function balanceSheetData(query) {
  return request({
    url: '/order/schedule/queryBalanceSheetData',
    method: 'get',
    params: query
  })
}

//根据工单获取生产物料平衡表数据
export function productionBalanceSheetData(query) {
  return request({
    url: '/order/schedule/queryProductionBalanceSheetData',
    method: 'get',
    params: query
  })
}

//根据工单获取生产物料平衡表数据
export function productionPzBalanceSheetData(query) {
  return request({
    url: '/order/schedule/queryProductionPzBalanceSheetData',
    method: 'get',
    params: query
  })
}

//根据工单产线 工时数据
export function queryProductionHoursDataList(query) {
  return request({
    url: '/order/schedule/queryProductionHoursDataList',
    method: 'get',
    params: query
  })
}

//根据工单获取产线退料数据
export function queryReturnMaterialData(query) {
  return request({
    url: '/order/schedule/queryReturnMaterialData',
    method: 'get',
    params: query
  })
}

//根据配置工单获取产线退料数据
export function queryReturnPzMaterialData(query) {
  return request({
    url: '/order/schedule/queryReturnPzMaterialData',
    method: 'get',
    params: query
  })
}

//根据用户id获取用户数据
export function queryUserDataInfo(query) {
  return request({
    url: '/order/schedule/queryUserDataInfo',
    method: 'get',
    params: query
  })
}

//根据工单获取平衡表数据(成本)
export function balanceSheetDetailData(query) {
  return request({
    url: '/order/schedule/queryBalanceSheetDetailData',
    method: 'get',
    params: query
  })
}

//根据工单获取平衡表(按照批号)数据
export function balanceBatchSheetData(query) {
  return request({
    url: '/order/schedule/queryBalanceBatchSheetData',
    method: 'get',
    params: query
  })
}

//根据工单获取入库记录数据
export function warehouseData(query) {
  return request({
    url: '/order/schedule/queryWarehouseData',
    method: 'get',
    params: query
  })
}

//根据工单获取委外进货记录数据
export function outWarehouseData(query) {
  return request({
    url: '/order/schedule/queryOutWarehouseData',
    method: 'get',
    params: query
  })
}

//根据工单获取委外退货记录数据
export function outReturnWarehouseData(query) {
  return request({
    url: '/order/schedule/queryOutReturnWarehouseData',
    method: 'get',
    params: query
  })
}

//根据订单号和erp编码获取erp订单 从而获取工单内容
export function queryErpOrderDetailDataNewInfo(query) {
  return request({
    url: '/order/schedule/queryErpOrderDetailDataNewInfo',
    method: 'get',
    params: query
  })
}

//订单平衡表 查找订单对应工单信息
export function queryErpOrderBalanceDataInfo(query) {
  return request({
    url: '/order/schedule/queryErpOrderBalanceDataInfo',
    method: 'get',
    params: query
  })
}

//查找订单商品运费金额
export function queryOrderGoodsFreightData(query) {
  return request({
    url: '/order/schedule/queryOrderGoodsFreightData',
    method: 'get',
    params: query
  })
}

//根据品号获取标准信息(标准工时 人数)
export function queryProductionStandardInfo(query) {
  return request({
    url: '/order/schedule/queryProductionStandardInfo',
    method: 'get',
    params: query
  })
}

//根据工单品号 获取订单内容
export function queryOrderGoodsDetailDataInfo(query) {
  return request({
    url: '/order/schedule/queryOrderGoodsDetailDataInfo',
    method: 'get',
    params: query
  })
}

//根据工单品号 获取订单内容
export function queryOrderGoodsDetailByIdDataInfo(query) {
  return request({
    url: '/order/schedule/queryOrderGoodsDetailByIdDataInfo',
    method: 'get',
    params: query
  })
}


//工单提交生产部审核
export function submitProductionAudit(data) {
  return request({
    url: '/order/schedule/submitProductionAudit',
    method: 'put',
    data: data
  })
}

//工单撤销生产部审核
export function cancelProductionAudit(data) {
  return request({
    url: '/order/schedule/cancelProductionAudit',
    method: 'put',
    data: data
  })
}

//生产模块工单列表
export function productionListSchedule(query) {
  return request({
    url: '/order/schedule/productionList',
    method: 'get',
    params: query
  })
}

export function allSchedule(query) {
  return request({
    url: '/order/schedule/all',
    method: 'get',
    params: query
  })
}


//提交审核
export function submitAudit(data) {
  return request({
    url: '/order/schedule/submitAudit',
    method: 'put',
    data: data
  })
}

//提交获取数据
export function judgeOrderSchedule(data) {
  return request({
    url: '/order/schedule/judgeOrderSchedule',
    method: 'put',
    data: data
  })
}
//判断成品品号是否满足要求
export function judgeOrderScheduleGoodsInfo(data) {
  return request({
    url: '/order/schedule/judgeOrderScheduleGoodsInfo',
    method: 'put',
    data: data
  })
}

export function cancelAudit(data) {
  return request({
    url: '/order/schedule/cancelAudit',
    method: 'put',
    data: data
  })
}

export function recentAllSchedule(query) {
  return request({
    url: '/order/schedule/recentAll',
    method: 'get',
    params: query
  })
}

export function standardListSchedule(query) {
  return request({
    url: '/order/schedule/standardList',
    method: 'get',
    params: query
  })
}

export function exportScheduleDiffHours(query) {
  return request({
    url: '/order/schedule/exportDiffHours',
    method: 'get',
    params: query
  })
}
