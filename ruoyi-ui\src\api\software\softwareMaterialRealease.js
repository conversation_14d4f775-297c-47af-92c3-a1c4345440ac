import request from '@/utils/request'

// 查询原料释放列表
export function listSoftwareMaterialRealease(query) {
  return request({
    url: '/software/softwareMaterialRealease/list',
    method: 'get',
    params: query
  })
}

// 查询原料释放详细
export function getSoftwareMaterialRealease(id) {
  return request({
    url: '/software/softwareMaterialRealease/' + id,
    method: 'get'
  })
}

// 新增原料释放
export function addSoftwareMaterialRealease(data) {
  return request({
    url: '/software/softwareMaterialRealease',
    method: 'post',
    data: data
  })
}

// 修改原料释放
export function updateSoftwareMaterialRealease(data) {
  return request({
    url: '/software/softwareMaterialRealease',
    method: 'put',
    data: data
  })
}

// 删除原料释放
export function delSoftwareMaterialRealease(id) {
  return request({
    url: '/software/softwareMaterialRealease/' + id,
    method: 'delete'
  })
}

// 导出原料释放
export function exportSoftwareMaterialRealease(query) {
  return request({
    url: '/software/softwareMaterialRealease/export',
    method: 'get',
    params: query
  })
}


//查看原料代码列表
export function queryChooseMaterialCodeDataList(query) {
  return request({
    url: '/software/softwareMaterialRealease/queryChooseMaterialCodeDataList',
    method: 'get',
    params: query
  })
}

//查看原料代码列表
export function queryChooseMaterialCodeByCodeDataList(query) {
  return request({
    url: '/software/softwareMaterialRealease/queryChooseMaterialCodeByCodeDataList',
    method: 'get',
    params: query
  })
}


//查看原料spec标准内容
export function queryMaterialLxzbDetailData(query) {
  return request({
    url: '/software/softwareMaterialRealease/queryMaterialLxzbDetailData',
    method: 'get',
    params: query
  })
}

export function listAuditMaterialRealeaseData(data) {
  return request({
    url: '/software/softwareMaterialRealease/audit',
    method: 'post',
    data: data
  })
}

//撤销审核
export function cancelAudit(data) {
  return request({
    url: '/software/softwareMaterialRealease/cancelAudit',
    method: 'put',
    data: data
  })
}
