import request from '@/utils/request'

// 查询方案人员签到记录列表
export function listCaseUserSign(query) {
  return request({
    url: '/gx/caseUserSign/list',
    method: 'get',
    params: query
  })
}

// 查询方案人员签到记录详细
export function getCaseUserSign(id) {
  return request({
    url: '/gx/caseUserSign/' + id,
    method: 'get'
  })
}

// 新增方案人员签到记录
export function addCaseUserSign(data) {
  return request({
    url: '/gx/caseUserSign',
    method: 'post',
    data: data
  })
}

// 修改方案人员签到记录
export function updateCaseUserSign(data) {
  return request({
    url: '/gx/caseUserSign',
    method: 'put',
    data: data
  })
}

// 删除方案人员签到记录
export function delCaseUserSign(id) {
  return request({
    url: '/gx/caseUserSign/' + id,
    method: 'delete'
  })
}

// 导出方案人员签到记录
export function exportCaseUserSign(query) {
  return request({
    url: '/gx/caseUserSign/export',
    method: 'get',
    params: query
  })
}

export function allCaseUserSign(query) {
  return request({
    url: '/gx/caseUserSign/all',
    method: 'get',
    params: query
  })
}
