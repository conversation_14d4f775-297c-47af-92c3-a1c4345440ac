{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\save.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\save.vue", "mtime": 1753954679644}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\babel.config.js", "mtime": 1743382537964}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_dayHours", "require", "_userTabs", "_interopRequireDefault", "_planAreaHours", "_baseCharts", "_user", "_attendanceRestTime", "_otherMinutes", "_mesHours", "_attendanceLog", "_productionGroup", "_time", "_index", "_attendance", "name", "components", "BaseChart", "DayHoursUserTabs", "props", "readonly", "type", "Boolean", "default", "data", "loading", "btnLoading", "form", "rules", "tabOptions", "label", "value", "sumMinutes", "userMinutes", "laborMinutes", "outerMinutes", "weightMinutes", "otherMinutes", "manageMinutes", "qcMinutes", "sumNums", "userNums", "laborNums", "outerNums", "weightNums", "otherNums", "manageNums", "qcNums", "currentTab", "userNumsOptions", "userMinutesOptions", "hoursTypeOptions", "hoursComposeOptions", "dayUserList", "attendanceLogList", "userList", "restList", "weightMinutesList", "otherMinutesList", "manageMinutesList", "qcMinutesList", "mesHoursList", "exceptionOptions", "created", "_this", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "userAll", "sent", "stop", "methods", "sailingsChange", "userCode", "_this2", "_callee2", "_callee2$", "_context2", "refreshAreaHours", "factory", "workDate", "buildCharts", "_this3", "_callee3", "userNumsChart", "userMinutesChart", "hoursT<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_callee3$", "_context3", "buildOptions", "minutesToHours", "toFixed", "effectiveMinutes", "invalidMinutes", "restMinutes", "$nextTick", "$refs", "init", "_this4", "_callee4", "oldArray", "_iterator", "_step", "_loop", "_iterator2", "_step2", "u", "exceptionArray", "flag", "i", "_callee4$", "_context5", "map", "sailings", "finalMinutes", "remark", "allPlanAreaUserHours", "_createForOfIteratorHelper2", "item", "attendanceArr", "timesArray", "upTime", "downTime", "startTimeArray", "endTimeArray", "attendanceMinutes", "minTime", "maxTime", "startTime", "timeArray", "_iterator3", "_step3", "o", "_loop$", "_context4", "filter", "userId", "sort", "a", "b", "userCheckTime", "moment", "format", "findClosestTimeString", "mesMinTime", "mesMaxTime", "sapMinTime", "sapMaxTime", "diff", "attendanceStartTime", "attendanceEndTime", "attendanceArray", "endTime", "push", "reduce", "min", "current", "Date", "max", "add", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "s", "n", "done", "err", "e", "f", "<PERSON><PERSON><PERSON>", "t1", "finish", "sapArray", "length", "wagesMinutes", "divide", "title", "text", "left", "legend", "orient", "series", "radius", "show", "formatter", "cancel", "$parent", "open", "reset", "id", "mesMinutes", "mesNums", "resetForm", "_this5", "_callee5", "res", "searchDateArray", "_iterator4", "_step4", "l", "productionGroup", "_iterator5", "_step5", "_iterator6", "_step6", "_item", "_iterator7", "_step7", "_item2", "_iterator8", "_step8", "_item3", "_callee5$", "_context6", "getDayHours", "allAttendanceRestTime", "companyCode", "allAttendanceLog", "Number", "allMesHours", "workdate", "userMinutesList", "allProductionGroup", "addOtherUser", "computeItemData", "computeDayData", "_this6", "_callee6", "_iterator9", "_step9", "_callee6$", "_context7", "$big", "toNumber", "_this7", "_callee7", "_iterator10", "_step10", "_loop2", "_callee7$", "_context9", "t", "dayUserSailingsList", "_loop2$", "_context8", "sumOfArray", "userWagesMinutes", "_toConsumableArray2", "Set", "userType", "array", "nums", "_iterator11", "_step11", "row", "_this8", "includes", "startDate", "upStandTime", "downStandTime", "minutes", "workPeriods", "start", "end", "calculateIntersectionMinutes", "subtract", "nick<PERSON><PERSON>", "roundDownToHalfHour", "undefined", "pushHrDate", "_this9", "_callee8", "_form", "params", "_callee8$", "_context10", "validate", "Object", "assign", "concat", "pushHrUserAnDate", "code", "msgSuccess", "submitForm", "_this10", "_callee9", "_iterator12", "_step12", "_loop3", "_callee9$", "_context12", "_loop3$", "_context11", "exceptionTips", "join", "updateDayHours", "t2"], "sources": ["src/views/production/dayHours/save.vue"], "sourcesContent": ["<template>\r\n  <div v-loading=\"loading\" >\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"6\" >\r\n        <el-statistic\r\n          group-separator=\",\"\r\n          :precision=\"2\"\r\n          :value=\"form.sumNums\"\r\n          title=\"今日上工总人数\"\r\n        />\r\n      </el-col>\r\n      <el-col :span=\"6\" >\r\n        <el-statistic\r\n          group-separator=\",\"\r\n          :precision=\"2\"\r\n          :value=\"minutesToHours(form.sumMinutes)\"\r\n          title=\"今日上工总工时\"\r\n        />\r\n      </el-col>\r\n      <el-col :span=\"6\" >\r\n        <el-statistic\r\n          group-separator=\",\"\r\n          :precision=\"2\"\r\n          :value=\"minutesToHours(form.wagesMinutes)\"\r\n          title=\"今日工资总工时\"\r\n        >\r\n          <template #suffix >\r\n            <el-tooltip content=\"质检除外\" >\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </template>\r\n        </el-statistic>\r\n      </el-col>\r\n      <el-col :span=\"6\" >\r\n        <el-statistic\r\n          group-separator=\",\"\r\n          :precision=\"2\"\r\n          :value=\"minutesToHours(form.mesMinutes)\"\r\n          title=\"mes上工总工时\"\r\n        >\r\n          <template #suffix >\r\n            <el-tooltip content=\"质检除外\" >\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </template>\r\n        </el-statistic>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-row :gutter=\"20\" style=\"margin-top: 20px\" >\r\n      <el-col :span=\"6\" >\r\n        <div style=\"height: 420px;width: 420px;\">\r\n          <BaseChart ref=\"userNumsChart\" :styleObj=\"{height: '400px',width: '400px'}\" />\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"6\" >\r\n        <div style=\"height: 420px;width: 420px;\">\r\n          <BaseChart ref=\"userMinutesChart\" :styleObj=\"{height: '400px',width: '400px'}\" />\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"6\" >\r\n        <div style=\"height: 420px;width: 420px;\">\r\n          <BaseChart ref=\"hoursTypeCharts\" :styleObj=\"{height: '400px',width: '400px'}\" />\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"6\" >\r\n        <div style=\"height: 420px;width: 420px;\">\r\n          <BaseChart ref=\"hoursComposeCharts\" :styleObj=\"{height: '400px',width: '400px'}\" />\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-tooltip content=\"刷新产线工时\" >\r\n      <el-button :loading=\"btnLoading\" icon=\"el-icon-refresh\" size=\"mini\" type=\"text\" @click=\"refreshAreaHours(form.factory,form.workDate)\" />\r\n    </el-tooltip>\r\n\r\n    <el-tabs v-model=\"currentTab\" >\r\n      <el-tab-pane v-for=\"tab in tabOptions\" :key=\"tab.value\" :label=\"tab.label\" :name=\"tab.value\" >\r\n        <div class=\"table-wrapper\">\r\n          <table class=\"base-table small-table\" >\r\n            <tr >\r\n              <th style=\"width: 120px\" >维度</th>\r\n              <th style=\"width: 120px\" >总数</th>\r\n              <th style=\"width: 120px\" >正式工</th>\r\n              <th style=\"width: 120px\" >称量</th>\r\n              <th style=\"width: 120px\" >间接</th>\r\n              <th style=\"width: 120px\" >管理</th>\r\n              <th style=\"width: 120px\" >质检</th>\r\n              <th style=\"width: 120px\" >劳务</th>\r\n              <th style=\"width: 120px\" >包干</th>\r\n            </tr>\r\n            <tr>\r\n              <th>\r\n                工资工时\r\n                <el-tooltip content=\"有修正工时时,以修正工时为准(不包含质检)\" >\r\n                  <i class=\"el-icon-question\" />\r\n                </el-tooltip>\r\n              </th>\r\n              <td>{{minutesToHours(tab.wagesMinutes).toFixed(2)}}</td>\r\n              <td>{{minutesToHours(tab.userWagesMinutes).toFixed(2)}}</td>\r\n              <td>{{minutesToHours(tab.weightMinutes).toFixed(2)}}</td>\r\n              <td>{{minutesToHours(tab.otherMinutes).toFixed(2)}}</td>\r\n              <td>{{minutesToHours(tab.manageMinutes).toFixed(2)}}</td>\r\n              <td>{{minutesToHours(tab.qcMinutes).toFixed(2)}}</td>\r\n              <td>{{minutesToHours(tab.laborMinutes).toFixed(2)}}</td>\r\n              <td>{{minutesToHours(tab.outerMinutes).toFixed(2)}}</td>\r\n            </tr>\r\n            <tr>\r\n              <th>人数</th>\r\n              <td>{{tab.sumNums}}</td>\r\n              <td>{{tab.userNums}}</td>\r\n              <td>{{tab.weightNums}}</td>\r\n              <td>{{tab.otherNums}}</td>\r\n              <td>{{tab.manageNums}}</td>\r\n              <td>{{tab.qcNums}}</td>\r\n              <td>{{tab.laborNums}}</td>\r\n              <td>{{tab.outerNums}}</td>\r\n            </tr>\r\n          </table>\r\n        </div>\r\n        <DayHoursUserTabs\r\n          :day-hours=\"form\"\r\n          :attendance-log-list=\"attendanceLogList\"\r\n          :mes-hours-list=\"mesHoursList\"\r\n          :day-user-list=\"dayUserList.filter(i=>i.sailings === tab.value)\"\r\n          :weight-minutes-list=\"weightMinutesList\"\r\n          :other-minutes-list=\"otherMinutesList\"\r\n          :manage-minutes-list=\"manageMinutesList\"\r\n          :qc-minutes-list=\"qcMinutesList\"\r\n          :user-list=\"userList\"\r\n          :rest-list=\"restList\"\r\n          :sailings=\"tab.value\"\r\n          @computeItemData=\"computeItemData\"\r\n          @sailingsChange=\"sailingsChange\"\r\n        />\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" size=\"mini\" style=\"margin-top: 20px\" label-width=\"80px\">\r\n      <el-form-item label=\"备注\" prop=\"remark\">\r\n        <el-input v-model=\"form.remark\" type=\"textarea\"/>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div slot=\"footer\" class=\"dialog-footer\" v-has-permi=\"['mes:production:hours:push']\" >\r\n      <el-button type=\"primary\" @click=\"submitForm\" size=\"mini\" :loading=\"btnLoading\">确 定</el-button>\r\n      <el-button type=\"primary\" @click=\"pushHrDate\" size=\"mini\" :loading=\"btnLoading\">推送人事(班别)</el-button>\r\n      <el-button @click=\"cancel\" size=\"mini\">取 消</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script >\r\nimport {getDayHours, updateDayHours} from \"@/api/production/dayHours\";\r\nimport DayHoursUserTabs from \"@/views/production/dayHours/userTabs.vue\";\r\nimport {allPlanAreaUserHours, getDayAreaHours} from \"@/api/production/planAreaHours\";\r\nimport BaseChart from \"../../../../baseCharts.vue\";\r\nimport {userAll} from \"@/api/system/user\";\r\nimport {allAttendanceRestTime} from \"@/api/hr/attendanceRestTime\";\r\nimport {getOtherDayMinutesByParams, } from \"@/api/production/otherMinutes\";\r\nimport {allMesHours} from \"@/api/production/mesHours\";\r\nimport {allAttendanceLog} from \"@/api/hr/attendanceLog\";\r\nimport {allProductionGroup} from \"@/api/production/productionGroup\";\r\nimport {\r\n  calculateIntersectionMinutes,\r\n  findClosestTimeString,\r\n  findIntersection,\r\n  roundDownToHalfHour\r\n} from \"@/utils/production/time\";\r\nimport form from \"@/views/gx/form/index.vue\";\r\nimport {pushHrUserAnDate} from \"@/api/hr/attendance\";\r\n\r\nexport default {\r\n  name: 'dayHoursSave',\r\n  components: {BaseChart, DayHoursUserTabs},\r\n  props: {\r\n    readonly: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      btnLoading: false,\r\n      form: {},\r\n      rules: {},\r\n      tabOptions: [\r\n        {\r\n          label: '白班',\r\n          value: '0',\r\n          sumMinutes: 0,\r\n          userMinutes: 0,\r\n          laborMinutes: 0,\r\n          outerMinutes: 0,\r\n          weightMinutes: 0,\r\n          otherMinutes: 0,\r\n          manageMinutes: 0,\r\n          qcMinutes: 0,\r\n          sumNums: 0,\r\n          userNums: 0,\r\n          laborNums: 0,\r\n          outerNums: 0,\r\n          weightNums: 0,\r\n          otherNums: 0,\r\n          manageNums: 0,\r\n          qcNums: 0,\r\n        },\r\n        {\r\n          label: '晚班',\r\n          value: '1',\r\n          sumMinutes: 0,\r\n          userMinutes: 0,\r\n          laborMinutes: 0,\r\n          outerMinutes: 0,\r\n          weightMinutes: 0,\r\n          otherMinutes: 0,\r\n          manageMinutes: 0,\r\n          qcMinutes: 0,\r\n          sumNums: 0,\r\n          userNums: 0,\r\n          laborNums: 0,\r\n          outerNums: 0,\r\n          weightNums: 0,\r\n          otherNums: 0,\r\n          manageNums: 0,\r\n          qcNums: 0,\r\n        },\r\n      ],\r\n      currentTab: '0',\r\n      userNumsOptions: {},\r\n      userMinutesOptions: {},\r\n      hoursTypeOptions: {},\r\n      hoursComposeOptions: {},\r\n      dayUserList: [],\r\n      attendanceLogList: [],\r\n      userList: [],\r\n      restList: [],\r\n      weightMinutesList: [],\r\n      otherMinutesList: [],\r\n      manageMinutesList: [],\r\n      qcMinutesList: [],\r\n      mesHoursList: [],\r\n      exceptionOptions: [\r\n        {label: '上工考勤异常',value: 1},\r\n        {label: '下工考勤异常',value: 2},\r\n        {label: 'mes上工异常',value: 3},\r\n        {label: 'mes下工异常',value: 4},\r\n        {label: '转场异常',value: 5},\r\n        {label: '有效工时异常',value: 6},\r\n        {label: 'sap上工异常',value: 7},\r\n        {label: 'sap下工异常',value: 8},\r\n      ],\r\n    }\r\n  },\r\n  async created() {\r\n    this.userList = await userAll()\r\n  },\r\n  methods: {\r\n    async sailingsChange(userCode){//mes班别变更,刷新mes工时\r\n      await this.refreshAreaHours(this.form.factory,this.form.workDate)\r\n    },\r\n    async buildCharts() {\r\n      const form = this.form\r\n\r\n      this.userNumsOptions = this.buildOptions(\"上工人员分布\",[\r\n        {name: '正式工',value: form.userNums},\r\n        {name: '称量',value: form.weightNums},\r\n        {name: '间接',value: form.otherNums},\r\n        {name: '管理',value: form.manageNums},\r\n        {name: '质检',value: form.qcNums},\r\n        {name: '劳务工',value: form.laborNums},\r\n        {name: '外包工',value: form.outerNums},\r\n      ])\r\n      this.userMinutesOptions = this.buildOptions(\"产线上工工时分布\",[\r\n        {name: '正式工',value: this.minutesToHours(form.userMinutes).toFixed(2) },\r\n        {name: '劳务工',value: this.minutesToHours(form.laborMinutes).toFixed(2) },\r\n        {name: '外包工',value: this.minutesToHours(form.outerMinutes).toFixed(2) },\r\n      ])\r\n      this.hoursTypeOptions = this.buildOptions(\"产线工时性质分布\",[\r\n        {name: '有效',value: this.minutesToHours(form.effectiveMinutes).toFixed(2)},\r\n        {name: '无效',value: this.minutesToHours(form.invalidMinutes).toFixed(2)},\r\n        {name: '休息',value: this.minutesToHours(form.restMinutes).toFixed(2)},\r\n      ])\r\n      this.hoursComposeOptions = this.buildOptions(\"工时组成分布\",[\r\n        {name: '产线',value: this.minutesToHours(form.sumMinutes).toFixed(2)},\r\n        {name: '称量',value: this.minutesToHours(form.weightMinutes).toFixed(2)},\r\n        {name: '间接',value: this.minutesToHours(form.otherMinutes).toFixed(2)},\r\n        {name: '管理',value: this.minutesToHours(form.manageMinutes).toFixed(2)},\r\n        // {name: '质检',value: this.minutesToHours(form.qcMinutes).toFixed(2)},\r\n      ])\r\n\r\n      await this.$nextTick()\r\n      const userNumsChart = this.$refs.userNumsChart\r\n      if(userNumsChart && this.userNumsOptions) {\r\n        await userNumsChart.init(this.userNumsOptions)\r\n      }\r\n\r\n      const userMinutesChart = this.$refs.userMinutesChart\r\n      if(userMinutesChart && this.userMinutesOptions) {\r\n        await userMinutesChart.init(this.userMinutesOptions)\r\n      }\r\n\r\n      const hoursTypeCharts = this.$refs.hoursTypeCharts\r\n      if(hoursTypeCharts && this.hoursTypeOptions) {\r\n        await hoursTypeCharts.init(this.hoursTypeOptions)\r\n      }\r\n\r\n      const hoursComposeCharts = this.$refs.hoursComposeCharts\r\n      if(hoursComposeCharts && this.hoursComposeOptions) {\r\n        await hoursComposeCharts.init(this.hoursComposeOptions)\r\n      }\r\n    },\r\n    async refreshAreaHours(factory,workDate) {\r\n      const oldArray = this.dayUserList.map(i=> {//保存原数组的备注\r\n        return {\r\n          userCode: i.userCode,\r\n          sailings: i.sailings,\r\n          finalMinutes: i.finalMinutes,\r\n          remark: i.remark,\r\n        }\r\n      })\r\n      this.btnLoading = true\r\n      const dayUserList = await allPlanAreaUserHours({factory,workDate,})\r\n      for (const item of dayUserList) {\r\n        const attendanceArr = this.attendanceLogList.filter(i=> i.userId === item.userId).sort((a,b)=> a.userCheckTime - b.userCheckTime)\r\n        const timesArray = attendanceArr.map(i=> this.moment(i.userCheckTime).format('YYYY-MM-DD HH:mm:ss'))\r\n        const upTime = findClosestTimeString(timesArray,item.mesMinTime)\r\n        const downTime = findClosestTimeString(timesArray,item.mesMaxTime)\r\n        const startTimeArray = [\r\n          item.sapMinTime,\r\n          item.mesMinTime,\r\n        ]\r\n        const endTimeArray = [\r\n          item.sapMaxTime,\r\n          item.mesMaxTime,\r\n        ]\r\n        if(upTime && downTime) {\r\n          const attendanceMinutes = this.moment(downTime).diff(upTime,'minutes')\r\n\r\n          item.attendanceStartTime = upTime\r\n          item.attendanceEndTime = downTime\r\n          item.attendanceMinutes = attendanceMinutes\r\n          item.attendanceArray = [{startTime: upTime,endTime: downTime}]\r\n          startTimeArray.push(upTime)\r\n          endTimeArray.push(downTime)\r\n        } else {\r\n          item.attendanceArray = []\r\n        }\r\n\r\n        const minTime = startTimeArray.reduce((min, current) => {\r\n          return new Date(current) < new Date(min) ? current : min;\r\n        })\r\n\r\n        const maxTime = endTimeArray.reduce((max, current) => {\r\n          return new Date(current) > new Date(max) ? current : max;\r\n        })\r\n        item.minTime = minTime\r\n        item.maxTime = maxTime\r\n\r\n        let startTime = minTime\r\n        let timeArray = []\r\n        while (startTime <= maxTime) {\r\n          timeArray.push(startTime)\r\n          startTime = this.moment(startTime,'YYYY-MM-DD HH:mm:ss').add(0.25,'hours').format('YYYY-MM-DD HH:mm:ss')\r\n        }\r\n        item.timeArray = timeArray\r\n\r\n        item.mesArray = this.mesHoursList.filter(i=>i.userCode === item.userCode).sort((a,b)=> a.startTime - b.startTime)\r\n        // if(item.userCode==='HR23001938') {\r\n        //   console.log(item)\r\n        // }\r\n        for (const o of oldArray) {//匹配原来的备注\r\n          if(o.userCode === item.userCode && o.sailings === item.sailings) {\r\n            item.remark = o.remark\r\n            item.finalMinutes = o.finalMinutes\r\n          }\r\n        }\r\n      }\r\n      //异常提醒\r\n      for (const u of dayUserList) {\r\n        // if(u.userCode === 'HR18000095') {\r\n        //   console.log(u.attendanceEndTime,u.mesMaxTime)\r\n        //   console.log(this.moment(u.attendanceEndTime).diff(u.mesMaxTime,'minutes'))\r\n        // }\r\n        const exceptionArray = []\r\n        if(u.mesMinTime < u.attendanceStartTime) {\r\n          exceptionArray.push(1)//上工考勤异常\r\n        } else if(this.moment(u.mesMinTime).diff(u.attendanceStartTime,'minutes') > 30) {\r\n          exceptionArray.push(3)//上工异常\r\n        }\r\n\r\n        if(u.mesMaxTime > u.attendanceEndTime) {\r\n          exceptionArray.push(2)//下工考勤异常\r\n        } else if(this.moment(u.attendanceEndTime).diff(u.mesMaxTime,'minutes') > 15) {\r\n          exceptionArray.push(4)//下工异常\r\n        }\r\n        if(this.moment(u.sapMinTime).diff(u.mesMinTime,'minutes') > 30) {\r\n          exceptionArray.push(7)\r\n        }\r\n        if(this.moment(u.mesMaxTime).diff(u.sapMaxTime,'minutes') > 15) {\r\n          exceptionArray.push(8)\r\n        }\r\n        let flag = false\r\n        for (let i = 0; i < u.sapArray.length; i++) {\r\n          if(i>0) {\r\n            if(this.moment(u.sapArray[i].startTime).diff(u.sapArray[i-1].endTime,'minutes') > 30) {\r\n              flag = true\r\n            }\r\n          }\r\n        }\r\n        if(flag) {\r\n          exceptionArray.push(5)//转场异常\r\n        }\r\n        if(u.wagesMinutes && this.divide(u.effectiveMinutes,u.wagesMinutes) < 0.8) {\r\n          exceptionArray.push(6)//有效工时异常\r\n        }\r\n\r\n        u.exceptionArray = exceptionArray\r\n      }\r\n\r\n      this.dayUserList = dayUserList\r\n\r\n      this.btnLoading = false\r\n    },\r\n    buildOptions(title,data) {\r\n      return   {\r\n        title: {\r\n          text: title,\r\n          left: 'center'\r\n        },\r\n        legend: {\r\n          orient: 'vertical',\r\n          left: 'left'\r\n        },\r\n        series: [\r\n          {\r\n            type: 'pie',\r\n            radius: '50%',\r\n            data,\r\n          }\r\n        ],\r\n        label: {\r\n          show: true,\r\n          formatter: '{b}: {c}' // {b}表示名称，{c}表示数值\r\n        },\r\n      }\r\n    },\r\n    cancel() {\r\n      this.$parent.$parent.open = false;\r\n      this.reset();\r\n    },\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        factory: null,\r\n        workDate: null,\r\n        sumNums: null,\r\n        userNums: null,\r\n        laborNums: null,\r\n        outerNums: null,\r\n        userMinutes: null,\r\n        laborMinutes: null,\r\n        outerMinutes: null,\r\n        sumMinutes: null,\r\n        effectiveMinutes: null,\r\n        invalidMinutes: null,\r\n        restMinutes: null,\r\n        otherMinutes: null,\r\n        manageMinutes: null,\r\n        wagesMinutes: null,\r\n        mesMinutes: null,\r\n        mesNums: null,\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n      this.dayUserList = []\r\n      this.weightMinutesList = []\r\n      this.otherMinutesList = []\r\n      this.manageMinutesList = []\r\n      this.qcMinutesList = []\r\n      this.tabOptions = [\r\n        {\r\n          label: '白班',\r\n          value: '0',\r\n          sumMinutes: 0,\r\n          userMinutes: 0,\r\n          laborMinutes: 0,\r\n          outerMinutes: 0,\r\n          weightMinutes: 0,\r\n          otherMinutes: 0,\r\n          manageMinutes: 0,\r\n          qcMinutes: 0,\r\n          sumNums: 0,\r\n          userNums: 0,\r\n          laborNums: 0,\r\n          outerNums: 0,\r\n          weightNums: 0,\r\n          otherNums: 0,\r\n          manageNums: 0,\r\n          qcNums: 0,\r\n        },\r\n        {\r\n          label: '晚班',\r\n          value: '1',\r\n          sumMinutes: 0,\r\n          userMinutes: 0,\r\n          laborMinutes: 0,\r\n          outerMinutes: 0,\r\n          weightMinutes: 0,\r\n          otherMinutes: 0,\r\n          manageMinutes: 0,\r\n          qcMinutes: 0,\r\n          sumNums: 0,\r\n          userNums: 0,\r\n          laborNums: 0,\r\n          outerNums: 0,\r\n          weightNums: 0,\r\n          otherNums: 0,\r\n          manageNums: 0,\r\n          qcNums: 0,\r\n        },\r\n      ]\r\n    },\r\n    async init(id) {\r\n      this.loading = true\r\n      const res = await getDayHours(id)\r\n      const form = res.data\r\n\r\n      this.restList = await allAttendanceRestTime({companyCode: form.factory})\r\n      const workDate = form.workDate\r\n      if(workDate) {\r\n        const searchDateArray = [workDate,]\r\n        searchDateArray.push(this.moment(workDate).add(1, 'days').format('YYYY-MM-DD'))\r\n        const attendanceLogList = await allAttendanceLog({searchDateArray})\r\n        for (const l of attendanceLogList) {\r\n          l.userId = Number(l.userId)\r\n        }\r\n        this.attendanceLogList = attendanceLogList\r\n      }\r\n\r\n      const mesHoursList = await allMesHours({workdate: workDate,})\r\n      this.mesHoursList = mesHoursList\r\n\r\n      this.form = form\r\n\r\n      if(form.userMinutesList && form.userMinutesList.length) {\r\n        this.dayUserList = form.userMinutesList\r\n      }\r\n      await this.refreshAreaHours(form.factory,form.workDate)\r\n\r\n      if(form.weightMinutesList) {\r\n        this.weightMinutesList = form.weightMinutesList\r\n      }\r\n\r\n      if(form.otherMinutesList) {\r\n        this.otherMinutesList = form.otherMinutesList\r\n      }\r\n\r\n      if(form.manageMinutesList) {\r\n        this.manageMinutesList = form.manageMinutesList\r\n      }\r\n\r\n      if(form.qcMinutesList) {\r\n        this.qcMinutesList = form.qcMinutesList\r\n      }\r\n\r\n      const productionGroup = await allProductionGroup({factory: form.factory})//读取默认名单\r\n      if(!this.weightMinutesList.length) {\r\n        for (const item of productionGroup) {\r\n          if(item.type === 'weight') {\r\n            this.addOtherUser(this.weightMinutesList,item)\r\n          }\r\n        }\r\n        await this.computeItemData('weight')\r\n      }\r\n\r\n      if(!this.manageMinutesList.length) {\r\n        for (const item of productionGroup) {\r\n          if(item.type === 'manage') {\r\n            this.addOtherUser(this.manageMinutesList,item)\r\n          }\r\n        }\r\n        await this.computeItemData('manage')\r\n      }\r\n\r\n      if(!this.otherMinutesList.length) {\r\n        for (const item of productionGroup) {\r\n          if(item.type === 'other') {\r\n            this.addOtherUser(this.otherMinutesList,item)\r\n          }\r\n        }\r\n        await this.computeItemData('other')\r\n      }\r\n\r\n      if(!this.qcMinutesList.length) {\r\n        for (const item of productionGroup) {\r\n          if(item.type === 'qc') {\r\n            this.addOtherUser(this.qcMinutesList,item)\r\n          }\r\n        }\r\n        await this.computeItemData('qc')\r\n      }\r\n\r\n      await this.computeDayData()\r\n      await this.buildCharts()\r\n\r\n      this.loading = false\r\n    },\r\n    async computeItemData(type) {\r\n      if(type) {\r\n        let wagesMinutes = this.$big(0)\r\n        for (const item of this[type + 'MinutesList']) {\r\n          wagesMinutes = this.add(wagesMinutes,item.wagesMinutes)\r\n        }\r\n        this.form[type + 'Minutes'] = wagesMinutes.toNumber()\r\n        this.form[type + 'Nums'] = this[type + 'MinutesList'].length\r\n      }\r\n\r\n      await this.computeDayData()\r\n      await this.$nextTick()\r\n      await this.buildCharts()\r\n    },\r\n    async computeDayData() {\r\n      const tabOptions = this.tabOptions\r\n      const dayUserList = this.dayUserList\r\n      for (const t of tabOptions) {\r\n        const dayUserSailingsList = dayUserList.filter(i=>i.sailings === t.value)\r\n        t.restMinutes= this.sumOfArray(dayUserSailingsList.map(i=>i.restMinutes))\r\n        t.effectiveMinutes= this.sumOfArray(dayUserSailingsList.map(i=>i.effectiveMinutes))\r\n        t.invalidMinutes= this.sumOfArray(dayUserSailingsList.map(i=>i.invalidMinutes))\r\n        t.weightMinutes = this.sumOfArray(this.weightMinutesList.filter(i=>i.sailings === t.value).map(i=>i.finalMinutes ? i.finalMinutes * 60 : i.wagesMinutes))\r\n        t.otherMinutes = this.sumOfArray(this.otherMinutesList.filter(i=>i.sailings === t.value).map(i=>i.finalMinutes ? i.finalMinutes * 60 : i.wagesMinutes))\r\n        t.manageMinutes = this.sumOfArray(this.manageMinutesList.filter(i=>i.sailings === t.value).map(i=>i.finalMinutes ? i.finalMinutes * 60 : i.wagesMinutes))\r\n        t.qcMinutes = this.sumOfArray(this.qcMinutesList.filter(i=>i.sailings === t.value).map(i=>i.finalMinutes ? i.finalMinutes * 60 : i.wagesMinutes))\r\n        const wagesMinutes = this.sumOfArray(dayUserSailingsList.map(i=>i.finalMinutes ? i.finalMinutes * 60 : i.wagesMinutes))\r\n\r\n        t.userWagesMinutes = wagesMinutes\r\n        t.wagesMinutes = this.sumOfArray([wagesMinutes,t.weightMinutes,t.otherMinutes,t.manageMinutes])//工资工时去除质检\r\n        t.mesMinutes = this.sumOfArray(dayUserSailingsList.filter(i=>i.sailings === t.value).map(i=>i.mesMinutes))\r\n        t.userMinutes = t.mesMinutes\r\n        t.sumMinutes = this.sumOfArray([t.mesMinutes,t.weightMinutes,t.otherMinutes,t.manageMinutes])//总工时去除质检\r\n\r\n        t.userNums = [...new Set(dayUserSailingsList.filter(i=>i.userType === 'user').map(i=>i.userCode))].length\r\n        t.laborNums = [...new Set(dayUserSailingsList.filter(i=>i.userType === 'labor').map(i=>i.userCode))].length\r\n        t.outerNums = [...new Set(dayUserSailingsList.filter(i=>i.userType === 'outer').map(i=>i.userCode))].length\r\n        const sumNums = [...new Set(dayUserSailingsList.map(i=>i.userCode))].length\r\n        t.weightNums = this.weightMinutesList.filter(i=>i.sailings === t.value).length\r\n        t.otherNums = this.otherMinutesList.filter(i=>i.sailings === t.value).length\r\n        t.manageNums = this.manageMinutesList.filter(i=>i.sailings === t.value).length\r\n        t.qcNums = this.qcMinutesList.filter(i=>i.sailings === t.value).length\r\n        t.sumNums = this.sumOfArray([sumNums,t.weightNums,t.otherNums,t.manageNums])//总人数去除质检\r\n      }\r\n\r\n      const form = this.form\r\n      form.wagesMinutes = this.sumOfArray(tabOptions.map(i=>i.wagesMinutes))\r\n      form.restMinutes = this.sumOfArray(tabOptions.map(i=>i.restMinutes))\r\n      form.effectiveMinutes = this.sumOfArray(tabOptions.map(i=>i.effectiveMinutes))\r\n      form.invalidMinutes = this.sumOfArray(tabOptions.map(i=>i.invalidMinutes))\r\n      form.weightMinutes = this.sumOfArray(tabOptions.map(i=>i.weightMinutes))\r\n      form.otherMinutes = this.sumOfArray(tabOptions.map(i=>i.otherMinutes))\r\n      form.manageMinutes = this.sumOfArray(tabOptions.map(i=>i.manageMinutes))\r\n      form.qcMinutes = this.sumOfArray(tabOptions.map(i=>i.qcMinutes))\r\n      form.mesMinutes = this.sumOfArray(tabOptions.map(i=>i.mesMinutes))\r\n      form.sumMinutes = this.sumOfArray(tabOptions.map(i=>i.sumMinutes))\r\n\r\n      form.userNums = this.sumOfArray(tabOptions.map(i=>i.userNums))\r\n      form.laborNums = this.sumOfArray(tabOptions.map(i=>i.laborNums))\r\n      form.outerNums = this.sumOfArray(tabOptions.map(i=>i.outerNums))\r\n      form.sumNums = this.sumOfArray(tabOptions.map(i=>i.sumNums))\r\n      form.weightNums = this.sumOfArray(tabOptions.map(i=>i.weightNums))\r\n      form.otherNums = this.sumOfArray(tabOptions.map(i=>i.otherNums))\r\n      form.manageNums = this.sumOfArray(tabOptions.map(i=>i.manageNums))\r\n      form.qcNums = this.sumOfArray(tabOptions.map(i=>i.qcNums))\r\n\r\n    },\r\n    sumOfArray(array) {\r\n      let nums = this.$big(0)\r\n      for (const n of array) {\r\n        nums = this.add(nums,n)\r\n      }\r\n      return nums.toNumber()\r\n    },\r\n    addOtherUser(array,row) {\r\n      const attendanceArr = this.attendanceLogList.filter(i=> i.userId === row.userId)\r\n      if(attendanceArr && attendanceArr[0]) {\r\n        if(!array.map(i=>i.userId).includes(row.userId)) {\r\n          const attendanceArr = this.attendanceLogList.filter(i=> i.userId === row.userId).sort((a,b)=> a.userCheckTime - b.userCheckTime)\r\n          if(attendanceArr && attendanceArr[1]){ //至少有两个\r\n            const timesArray = attendanceArr.map(i=> this.moment(i.userCheckTime).format('YYYY-MM-DD HH:mm:ss'))\r\n            const startDate = this.moment(this.form.workDate).format('YYYY-MM-DD')\r\n            let upStandTime = startDate + ' 08:30:00'\r\n            let downStandTime =  startDate + ' 20:30:00'\r\n            if(row.sailings === '1') {\r\n              upStandTime = startDate + ' 20:30:00'\r\n              downStandTime = this.moment(startDate).add(1, 'days').format('YYYY-MM-DD') + ' 08:30:00'\r\n            }\r\n            let upTime = findClosestTimeString(timesArray,upStandTime)\r\n            const downTime = findClosestTimeString(timesArray,downStandTime)\r\n            if(upTime && downTime) {\r\n              if(upTime < upStandTime ) {//如果早于8点半,按8点半算\r\n                upTime = upStandTime\r\n              }\r\n              const minutes = this.moment(downTime).diff(upTime,'minutes')\r\n              const workPeriods = [\r\n                {\r\n                  start: this.moment(upTime).format('HH:mm'),\r\n                  end: this.moment(downTime).format('HH:mm'),\r\n                },\r\n              ]\r\n              const restMinutes = calculateIntersectionMinutes(workPeriods,this.restList)\r\n              const wagesMinutes = this.subtract(minutes,restMinutes).toNumber()\r\n              if(minutes > 0) {\r\n                array.push({\r\n                  userId: row.userId,\r\n                  userCode: row.userCode,\r\n                  nickName: row.nickName,\r\n                  startTime: upTime,\r\n                  endTime: downTime,\r\n                  sailings: row.sailings,\r\n                  minutes,\r\n                  restMinutes,\r\n                  wagesMinutes: row.type === 'manage' ? 480 : roundDownToHalfHour(wagesMinutes),//如果是管理工时默认是8小时\r\n                  finalMinutes: undefined,\r\n                })\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    async pushHrDate() {\r\n      await this.$refs[\"form\"].validate()\r\n      if(this.dayUserList.length) {\r\n        this.btnLoading = true\r\n        let form = Object.assign({}, this.form)\r\n        let params = {\r\n          workDate: form.workDate,\r\n          factory: form.factory,\r\n          userMinutesList: this.dayUserList,\r\n          otherMinutesList: [\r\n            ...this.weightMinutesList,\r\n            ...this.otherMinutesList,\r\n            ...this.manageMinutesList,\r\n            ...this.qcMinutesList,\r\n          ],\r\n        }\r\n        const res = await pushHrUserAnDate(params)\r\n        if(res.code === 200) {\r\n          this.msgSuccess('推送成功!')\r\n        }\r\n      }\r\n      this.btnLoading = false\r\n    },\r\n    async submitForm() {\r\n      let form = Object.assign({}, this.form)\r\n\r\n      const dayUserList = this.dayUserList\r\n      for (const item of dayUserList) {\r\n        item.exceptionTips = this.exceptionOptions.filter(i=> item.exceptionArray.includes(i.value)).map(i=>i.label).join('|')\r\n      }\r\n      form.userMinutesList = dayUserList\r\n\r\n      form.weightMinutesList = this.weightMinutesList\r\n      form.otherMinutesList = this.otherMinutesList\r\n      form.manageMinutesList = this.manageMinutesList\r\n      form.qcMinutesList = this.qcMinutesList\r\n\r\n      if (form.id != null) {\r\n        try {\r\n          this.btnLoading = true\r\n          await updateDayHours(form)\r\n          this.btnLoading = false\r\n          this.msgSuccess(\"修改成功\")\r\n          // this.$parent.$parent.open = false\r\n          // await this.$parent.$parent.getList()\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n  },\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuJA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,cAAA,GAAAH,OAAA;AACA,IAAAI,WAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,mBAAA,GAAAN,OAAA;AACA,IAAAO,aAAA,GAAAP,OAAA;AACA,IAAAQ,SAAA,GAAAR,OAAA;AACA,IAAAS,cAAA,GAAAT,OAAA;AACA,IAAAU,gBAAA,GAAAV,OAAA;AACA,IAAAW,KAAA,GAAAX,OAAA;AAMA,IAAAY,MAAA,GAAAV,sBAAA,CAAAF,OAAA;AACA,IAAAa,WAAA,GAAAb,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAc,IAAA;EACAC,UAAA;IAAAC,SAAA,EAAAA,mBAAA;IAAAC,gBAAA,EAAAA;EAAA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACAC,IAAA;MACAC,KAAA;MACAC,UAAA,GACA;QACAC,KAAA;QACAC,KAAA;QACAC,UAAA;QACAC,WAAA;QACAC,YAAA;QACAC,YAAA;QACAC,aAAA;QACAC,YAAA;QACAC,aAAA;QACAC,SAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA;QACAC,SAAA;QACAC,UAAA;QACAC,SAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAjB,KAAA;QACAC,KAAA;QACAC,UAAA;QACAC,WAAA;QACAC,YAAA;QACAC,YAAA;QACAC,aAAA;QACAC,YAAA;QACAC,aAAA;QACAC,SAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA;QACAC,SAAA;QACAC,UAAA;QACAC,SAAA;QACAC,UAAA;QACAC,MAAA;MACA,EACA;MACAC,UAAA;MACAC,eAAA;MACAC,kBAAA;MACAC,gBAAA;MACAC,mBAAA;MACAC,WAAA;MACAC,iBAAA;MACAC,QAAA;MACAC,QAAA;MACAC,iBAAA;MACAC,gBAAA;MACAC,iBAAA;MACAC,aAAA;MACAC,YAAA;MACAC,gBAAA,GACA;QAAAhC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACAgC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,WAAAC,kBAAA,CAAA1C,OAAA,mBAAA2C,oBAAA,CAAA3C,OAAA,IAAA4C,IAAA,UAAAC,QAAA;MAAA,WAAAF,oBAAA,CAAA3C,OAAA,IAAA8C,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACA,IAAAC,aAAA;UAAA;YAAAV,KAAA,CAAAT,QAAA,GAAAgB,QAAA,CAAAI,IAAA;UAAA;UAAA;YAAA,OAAAJ,QAAA,CAAAK,IAAA;QAAA;MAAA,GAAAR,OAAA;IAAA;EACA;EACAS,OAAA;IACAC,cAAA,WAAAA,eAAAC,QAAA;MAAA,IAAAC,MAAA;MAAA,WAAAf,kBAAA,CAAA1C,OAAA,mBAAA2C,oBAAA,CAAA3C,OAAA,IAAA4C,IAAA,UAAAc,SAAA;QAAA,WAAAf,oBAAA,CAAA3C,OAAA,IAAA8C,IAAA,UAAAa,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAX,IAAA,GAAAW,SAAA,CAAAV,IAAA;YAAA;cAAAU,SAAA,CAAAV,IAAA;cAAA,OACAO,MAAA,CAAAI,gBAAA,CAAAJ,MAAA,CAAArD,IAAA,CAAA0D,OAAA,EAAAL,MAAA,CAAArD,IAAA,CAAA2D,QAAA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAP,IAAA;UAAA;QAAA,GAAAK,QAAA;MAAA;IACA;IACAM,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,WAAAvB,kBAAA,CAAA1C,OAAA,mBAAA2C,oBAAA,CAAA3C,OAAA,IAAA4C,IAAA,UAAAsB,SAAA;QAAA,IAAA9D,IAAA,EAAA+D,aAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAC,kBAAA;QAAA,WAAA3B,oBAAA,CAAA3C,OAAA,IAAA8C,IAAA,UAAAyB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvB,IAAA,GAAAuB,SAAA,CAAAtB,IAAA;YAAA;cACA9C,IAAA,GAAA6D,MAAA,CAAA7D,IAAA;cAEA6D,MAAA,CAAAvC,eAAA,GAAAuC,MAAA,CAAAQ,YAAA,YACA;gBAAAjF,IAAA;gBAAAgB,KAAA,EAAAJ,IAAA,CAAAc;cAAA,GACA;gBAAA1B,IAAA;gBAAAgB,KAAA,EAAAJ,IAAA,CAAAiB;cAAA,GACA;gBAAA7B,IAAA;gBAAAgB,KAAA,EAAAJ,IAAA,CAAAkB;cAAA,GACA;gBAAA9B,IAAA;gBAAAgB,KAAA,EAAAJ,IAAA,CAAAmB;cAAA,GACA;gBAAA/B,IAAA;gBAAAgB,KAAA,EAAAJ,IAAA,CAAAoB;cAAA,GACA;gBAAAhC,IAAA;gBAAAgB,KAAA,EAAAJ,IAAA,CAAAe;cAAA,GACA;gBAAA3B,IAAA;gBAAAgB,KAAA,EAAAJ,IAAA,CAAAgB;cAAA,EACA;cACA6C,MAAA,CAAAtC,kBAAA,GAAAsC,MAAA,CAAAQ,YAAA,cACA;gBAAAjF,IAAA;gBAAAgB,KAAA,EAAAyD,MAAA,CAAAS,cAAA,CAAAtE,IAAA,CAAAM,WAAA,EAAAiE,OAAA;cAAA,GACA;gBAAAnF,IAAA;gBAAAgB,KAAA,EAAAyD,MAAA,CAAAS,cAAA,CAAAtE,IAAA,CAAAO,YAAA,EAAAgE,OAAA;cAAA,GACA;gBAAAnF,IAAA;gBAAAgB,KAAA,EAAAyD,MAAA,CAAAS,cAAA,CAAAtE,IAAA,CAAAQ,YAAA,EAAA+D,OAAA;cAAA,EACA;cACAV,MAAA,CAAArC,gBAAA,GAAAqC,MAAA,CAAAQ,YAAA,cACA;gBAAAjF,IAAA;gBAAAgB,KAAA,EAAAyD,MAAA,CAAAS,cAAA,CAAAtE,IAAA,CAAAwE,gBAAA,EAAAD,OAAA;cAAA,GACA;gBAAAnF,IAAA;gBAAAgB,KAAA,EAAAyD,MAAA,CAAAS,cAAA,CAAAtE,IAAA,CAAAyE,cAAA,EAAAF,OAAA;cAAA,GACA;gBAAAnF,IAAA;gBAAAgB,KAAA,EAAAyD,MAAA,CAAAS,cAAA,CAAAtE,IAAA,CAAA0E,WAAA,EAAAH,OAAA;cAAA,EACA;cACAV,MAAA,CAAApC,mBAAA,GAAAoC,MAAA,CAAAQ,YAAA,YACA;gBAAAjF,IAAA;gBAAAgB,KAAA,EAAAyD,MAAA,CAAAS,cAAA,CAAAtE,IAAA,CAAAK,UAAA,EAAAkE,OAAA;cAAA,GACA;gBAAAnF,IAAA;gBAAAgB,KAAA,EAAAyD,MAAA,CAAAS,cAAA,CAAAtE,IAAA,CAAAS,aAAA,EAAA8D,OAAA;cAAA,GACA;gBAAAnF,IAAA;gBAAAgB,KAAA,EAAAyD,MAAA,CAAAS,cAAA,CAAAtE,IAAA,CAAAU,YAAA,EAAA6D,OAAA;cAAA,GACA;gBAAAnF,IAAA;gBAAAgB,KAAA,EAAAyD,MAAA,CAAAS,cAAA,CAAAtE,IAAA,CAAAW,aAAA,EAAA4D,OAAA;cAAA;cACA;cAAA,CACA;cAAAH,SAAA,CAAAtB,IAAA;cAAA,OAEAe,MAAA,CAAAc,SAAA;YAAA;cACAZ,aAAA,GAAAF,MAAA,CAAAe,KAAA,CAAAb,aAAA;cAAA,MACAA,aAAA,IAAAF,MAAA,CAAAvC,eAAA;gBAAA8C,SAAA,CAAAtB,IAAA;gBAAA;cAAA;cAAAsB,SAAA,CAAAtB,IAAA;cAAA,OACAiB,aAAA,CAAAc,IAAA,CAAAhB,MAAA,CAAAvC,eAAA;YAAA;cAGA0C,gBAAA,GAAAH,MAAA,CAAAe,KAAA,CAAAZ,gBAAA;cAAA,MACAA,gBAAA,IAAAH,MAAA,CAAAtC,kBAAA;gBAAA6C,SAAA,CAAAtB,IAAA;gBAAA;cAAA;cAAAsB,SAAA,CAAAtB,IAAA;cAAA,OACAkB,gBAAA,CAAAa,IAAA,CAAAhB,MAAA,CAAAtC,kBAAA;YAAA;cAGA0C,eAAA,GAAAJ,MAAA,CAAAe,KAAA,CAAAX,eAAA;cAAA,MACAA,eAAA,IAAAJ,MAAA,CAAArC,gBAAA;gBAAA4C,SAAA,CAAAtB,IAAA;gBAAA;cAAA;cAAAsB,SAAA,CAAAtB,IAAA;cAAA,OACAmB,eAAA,CAAAY,IAAA,CAAAhB,MAAA,CAAArC,gBAAA;YAAA;cAGA0C,kBAAA,GAAAL,MAAA,CAAAe,KAAA,CAAAV,kBAAA;cAAA,MACAA,kBAAA,IAAAL,MAAA,CAAApC,mBAAA;gBAAA2C,SAAA,CAAAtB,IAAA;gBAAA;cAAA;cAAAsB,SAAA,CAAAtB,IAAA;cAAA,OACAoB,kBAAA,CAAAW,IAAA,CAAAhB,MAAA,CAAApC,mBAAA;YAAA;YAAA;cAAA,OAAA2C,SAAA,CAAAnB,IAAA;UAAA;QAAA,GAAAa,QAAA;MAAA;IAEA;IACAL,gBAAA,WAAAA,iBAAAC,OAAA,EAAAC,QAAA;MAAA,IAAAmB,MAAA;MAAA,WAAAxC,kBAAA,CAAA1C,OAAA,mBAAA2C,oBAAA,CAAA3C,OAAA,IAAA4C,IAAA,UAAAuC,SAAA;QAAA,IAAAC,QAAA,EAAAtD,WAAA,EAAAuD,SAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,CAAA,EAAAC,cAAA,EAAAC,IAAA,EAAAC,CAAA;QAAA,WAAAlD,oBAAA,CAAA3C,OAAA,IAAA8C,IAAA,UAAAgD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9C,IAAA,GAAA8C,SAAA,CAAA7C,IAAA;YAAA;cACAkC,QAAA,GAAAF,MAAA,CAAApD,WAAA,CAAAkE,GAAA,WAAAH,CAAA;gBAAA;gBACA;kBACArC,QAAA,EAAAqC,CAAA,CAAArC,QAAA;kBACAyC,QAAA,EAAAJ,CAAA,CAAAI,QAAA;kBACAC,YAAA,EAAAL,CAAA,CAAAK,YAAA;kBACAC,MAAA,EAAAN,CAAA,CAAAM;gBACA;cACA;cACAjB,MAAA,CAAA/E,UAAA;cAAA4F,SAAA,CAAA7C,IAAA;cAAA,OACA,IAAAkD,mCAAA;gBAAAtC,OAAA,EAAAA,OAAA;gBAAAC,QAAA,EAAAA;cAAA;YAAA;cAAAjC,WAAA,GAAAiE,SAAA,CAAA3C,IAAA;cAAAiC,SAAA,OAAAgB,2BAAA,CAAArG,OAAA,EACA8B,WAAA;cAAAiE,SAAA,CAAA9C,IAAA;cAAAsC,KAAA,oBAAA5C,oBAAA,CAAA3C,OAAA,IAAA4C,IAAA,UAAA2C,MAAA;gBAAA,IAAAe,IAAA,EAAAC,aAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,QAAA,EAAAC,cAAA,EAAAC,YAAA,EAAAC,iBAAA,EAAAC,OAAA,EAAAC,OAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,CAAA;gBAAA,WAAAzE,oBAAA,CAAA3C,OAAA,IAAA8C,IAAA,UAAAuE,OAAAC,SAAA;kBAAA,kBAAAA,SAAA,CAAArE,IAAA,GAAAqE,SAAA,CAAApE,IAAA;oBAAA;sBAAAoD,IAAA,GAAAhB,KAAA,CAAA9E,KAAA;sBACA+F,aAAA,GAAArB,MAAA,CAAAnD,iBAAA,CAAAwF,MAAA,WAAA1B,CAAA;wBAAA,OAAAA,CAAA,CAAA2B,MAAA,KAAAlB,IAAA,CAAAkB,MAAA;sBAAA,GAAAC,IAAA,WAAAC,CAAA,EAAAC,CAAA;wBAAA,OAAAD,CAAA,CAAAE,aAAA,GAAAD,CAAA,CAAAC,aAAA;sBAAA;sBACApB,UAAA,GAAAD,aAAA,CAAAP,GAAA,WAAAH,CAAA;wBAAA,OAAAX,MAAA,CAAA2C,MAAA,CAAAhC,CAAA,CAAA+B,aAAA,EAAAE,MAAA;sBAAA;sBACArB,MAAA,OAAAsB,2BAAA,EAAAvB,UAAA,EAAAF,IAAA,CAAA0B,UAAA;sBACAtB,QAAA,OAAAqB,2BAAA,EAAAvB,UAAA,EAAAF,IAAA,CAAA2B,UAAA;sBACAtB,cAAA,IACAL,IAAA,CAAA4B,UAAA,EACA5B,IAAA,CAAA0B,UAAA,CACA;sBACApB,YAAA,IACAN,IAAA,CAAA6B,UAAA,EACA7B,IAAA,CAAA2B,UAAA,CACA;sBACA,IAAAxB,MAAA,IAAAC,QAAA;wBACAG,iBAAA,GAAA3B,MAAA,CAAA2C,MAAA,CAAAnB,QAAA,EAAA0B,IAAA,CAAA3B,MAAA;wBAEAH,IAAA,CAAA+B,mBAAA,GAAA5B,MAAA;wBACAH,IAAA,CAAAgC,iBAAA,GAAA5B,QAAA;wBACAJ,IAAA,CAAAO,iBAAA,GAAAA,iBAAA;wBACAP,IAAA,CAAAiC,eAAA;0BAAAvB,SAAA,EAAAP,MAAA;0BAAA+B,OAAA,EAAA9B;wBAAA;wBACAC,cAAA,CAAA8B,IAAA,CAAAhC,MAAA;wBACAG,YAAA,CAAA6B,IAAA,CAAA/B,QAAA;sBACA;wBACAJ,IAAA,CAAAiC,eAAA;sBACA;sBAEAzB,OAAA,GAAAH,cAAA,CAAA+B,MAAA,WAAAC,GAAA,EAAAC,OAAA;wBACA,WAAAC,IAAA,CAAAD,OAAA,QAAAC,IAAA,CAAAF,GAAA,IAAAC,OAAA,GAAAD,GAAA;sBACA;sBAEA5B,OAAA,GAAAH,YAAA,CAAA8B,MAAA,WAAAI,GAAA,EAAAF,OAAA;wBACA,WAAAC,IAAA,CAAAD,OAAA,QAAAC,IAAA,CAAAC,GAAA,IAAAF,OAAA,GAAAE,GAAA;sBACA;sBACAxC,IAAA,CAAAQ,OAAA,GAAAA,OAAA;sBACAR,IAAA,CAAAS,OAAA,GAAAA,OAAA;sBAEAC,SAAA,GAAAF,OAAA;sBACAG,SAAA;sBACA,OAAAD,SAAA,IAAAD,OAAA;wBACAE,SAAA,CAAAwB,IAAA,CAAAzB,SAAA;wBACAA,SAAA,GAAA9B,MAAA,CAAA2C,MAAA,CAAAb,SAAA,yBAAA+B,GAAA,gBAAAjB,MAAA;sBACA;sBACAxB,IAAA,CAAAW,SAAA,GAAAA,SAAA;sBAEAX,IAAA,CAAA0C,QAAA,GAAA9D,MAAA,CAAA5C,YAAA,CAAAiF,MAAA,WAAA1B,CAAA;wBAAA,OAAAA,CAAA,CAAArC,QAAA,KAAA8C,IAAA,CAAA9C,QAAA;sBAAA,GAAAiE,IAAA,WAAAC,CAAA,EAAAC,CAAA;wBAAA,OAAAD,CAAA,CAAAV,SAAA,GAAAW,CAAA,CAAAX,SAAA;sBAAA;sBACA;sBACA;sBACA;sBAAAE,UAAA,OAAAb,2BAAA,CAAArG,OAAA,EACAoF,QAAA;sBAAA;wBAAA,KAAA8B,UAAA,CAAA+B,CAAA,MAAA9B,MAAA,GAAAD,UAAA,CAAAgC,CAAA,IAAAC,IAAA;0BAAA/B,CAAA,GAAAD,MAAA,CAAA3G,KAAA;0BAAA;0BACA,IAAA4G,CAAA,CAAA5D,QAAA,KAAA8C,IAAA,CAAA9C,QAAA,IAAA4D,CAAA,CAAAnB,QAAA,KAAAK,IAAA,CAAAL,QAAA;4BACAK,IAAA,CAAAH,MAAA,GAAAiB,CAAA,CAAAjB,MAAA;4BACAG,IAAA,CAAAJ,YAAA,GAAAkB,CAAA,CAAAlB,YAAA;0BACA;wBACA;sBAAA,SAAAkD,GAAA;wBAAAlC,UAAA,CAAAmC,CAAA,CAAAD,GAAA;sBAAA;wBAAAlC,UAAA,CAAAoC,CAAA;sBAAA;oBAAA;oBAAA;sBAAA,OAAAhC,SAAA,CAAAjE,IAAA;kBAAA;gBAAA,GAAAkC,KAAA;cAAA;cAAAF,SAAA,CAAA4D,CAAA;YAAA;cAAA,KAAA3D,KAAA,GAAAD,SAAA,CAAA6D,CAAA,IAAAC,IAAA;gBAAApD,SAAA,CAAA7C,IAAA;gBAAA;cAAA;cAAA,OAAA6C,SAAA,CAAAwD,aAAA,CAAAhE,KAAA;YAAA;cAAAQ,SAAA,CAAA7C,IAAA;cAAA;YAAA;cAAA6C,SAAA,CAAA7C,IAAA;cAAA;YAAA;cAAA6C,SAAA,CAAA9C,IAAA;cAAA8C,SAAA,CAAAyD,EAAA,GAAAzD,SAAA;cAAAV,SAAA,CAAAgE,CAAA,CAAAtD,SAAA,CAAAyD,EAAA;YAAA;cAAAzD,SAAA,CAAA9C,IAAA;cAAAoC,SAAA,CAAAiE,CAAA;cAAA,OAAAvD,SAAA,CAAA0D,MAAA;YAAA;cAEA;cAAAjE,UAAA,OAAAa,2BAAA,CAAArG,OAAA,EACA8B,WAAA;cAAA;gBAAA,KAAA0D,UAAA,CAAAyD,CAAA,MAAAxD,MAAA,GAAAD,UAAA,CAAA0D,CAAA,IAAAC,IAAA;kBAAAzD,CAAA,GAAAD,MAAA,CAAAjF,KAAA;kBACA;kBACA;kBACA;kBACA;kBACAmF,cAAA;kBACA,IAAAD,CAAA,CAAAsC,UAAA,GAAAtC,CAAA,CAAA2C,mBAAA;oBACA1C,cAAA,CAAA8C,IAAA;kBACA,WAAAvD,MAAA,CAAA2C,MAAA,CAAAnC,CAAA,CAAAsC,UAAA,EAAAI,IAAA,CAAA1C,CAAA,CAAA2C,mBAAA;oBACA1C,cAAA,CAAA8C,IAAA;kBACA;kBAEA,IAAA/C,CAAA,CAAAuC,UAAA,GAAAvC,CAAA,CAAA4C,iBAAA;oBACA3C,cAAA,CAAA8C,IAAA;kBACA,WAAAvD,MAAA,CAAA2C,MAAA,CAAAnC,CAAA,CAAA4C,iBAAA,EAAAF,IAAA,CAAA1C,CAAA,CAAAuC,UAAA;oBACAtC,cAAA,CAAA8C,IAAA;kBACA;kBACA,IAAAvD,MAAA,CAAA2C,MAAA,CAAAnC,CAAA,CAAAwC,UAAA,EAAAE,IAAA,CAAA1C,CAAA,CAAAsC,UAAA;oBACArC,cAAA,CAAA8C,IAAA;kBACA;kBACA,IAAAvD,MAAA,CAAA2C,MAAA,CAAAnC,CAAA,CAAAuC,UAAA,EAAAG,IAAA,CAAA1C,CAAA,CAAAyC,UAAA;oBACAxC,cAAA,CAAA8C,IAAA;kBACA;kBACA7C,IAAA;kBACA,KAAAC,CAAA,MAAAA,CAAA,GAAAH,CAAA,CAAAgE,QAAA,CAAAC,MAAA,EAAA9D,CAAA;oBACA,IAAAA,CAAA;sBACA,IAAAX,MAAA,CAAA2C,MAAA,CAAAnC,CAAA,CAAAgE,QAAA,CAAA7D,CAAA,EAAAmB,SAAA,EAAAoB,IAAA,CAAA1C,CAAA,CAAAgE,QAAA,CAAA7D,CAAA,MAAA2C,OAAA;wBACA5C,IAAA;sBACA;oBACA;kBACA;kBACA,IAAAA,IAAA;oBACAD,cAAA,CAAA8C,IAAA;kBACA;kBACA,IAAA/C,CAAA,CAAAkE,YAAA,IAAA1E,MAAA,CAAA2E,MAAA,CAAAnE,CAAA,CAAAd,gBAAA,EAAAc,CAAA,CAAAkE,YAAA;oBACAjE,cAAA,CAAA8C,IAAA;kBACA;kBAEA/C,CAAA,CAAAC,cAAA,GAAAA,cAAA;gBACA;cAAA,SAAAyD,GAAA;gBAAA5D,UAAA,CAAA6D,CAAA,CAAAD,GAAA;cAAA;gBAAA5D,UAAA,CAAA8D,CAAA;cAAA;cAEApE,MAAA,CAAApD,WAAA,GAAAA,WAAA;cAEAoD,MAAA,CAAA/E,UAAA;YAAA;YAAA;cAAA,OAAA4F,SAAA,CAAA1C,IAAA;UAAA;QAAA,GAAA8B,QAAA;MAAA;IACA;IACAV,YAAA,WAAAA,aAAAqF,KAAA,EAAA7J,IAAA;MACA;QACA6J,KAAA;UACAC,IAAA,EAAAD,KAAA;UACAE,IAAA;QACA;QACAC,MAAA;UACAC,MAAA;UACAF,IAAA;QACA;QACAG,MAAA,GACA;UACArK,IAAA;UACAsK,MAAA;UACAnK,IAAA,EAAAA;QACA,EACA;QACAM,KAAA;UACA8J,IAAA;UACAC,SAAA;QACA;MACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA,CAAAA,OAAA,CAAAC,IAAA;MACA,KAAAC,KAAA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAtK,IAAA;QACAuK,EAAA;QACA7G,OAAA;QACAC,QAAA;QACA9C,OAAA;QACAC,QAAA;QACAC,SAAA;QACAC,SAAA;QACAV,WAAA;QACAC,YAAA;QACAC,YAAA;QACAH,UAAA;QACAmE,gBAAA;QACAC,cAAA;QACAC,WAAA;QACAhE,YAAA;QACAC,aAAA;QACA6I,YAAA;QACAgB,UAAA;QACAC,OAAA;QACA1E,MAAA;MACA;MACA,KAAA2E,SAAA;MACA,KAAAhJ,WAAA;MACA,KAAAI,iBAAA;MACA,KAAAC,gBAAA;MACA,KAAAC,iBAAA;MACA,KAAAC,aAAA;MACA,KAAA/B,UAAA,IACA;QACAC,KAAA;QACAC,KAAA;QACAC,UAAA;QACAC,WAAA;QACAC,YAAA;QACAC,YAAA;QACAC,aAAA;QACAC,YAAA;QACAC,aAAA;QACAC,SAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA;QACAC,SAAA;QACAC,UAAA;QACAC,SAAA;QACAC,UAAA;QACAC,MAAA;MACA,GACA;QACAjB,KAAA;QACAC,KAAA;QACAC,UAAA;QACAC,WAAA;QACAC,YAAA;QACAC,YAAA;QACAC,aAAA;QACAC,YAAA;QACAC,aAAA;QACAC,SAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA;QACAC,SAAA;QACAC,UAAA;QACAC,SAAA;QACAC,UAAA;QACAC,MAAA;MACA,EACA;IACA;IACAyD,IAAA,WAAAA,KAAA0F,EAAA;MAAA,IAAAI,MAAA;MAAA,WAAArI,kBAAA,CAAA1C,OAAA,mBAAA2C,oBAAA,CAAA3C,OAAA,IAAA4C,IAAA,UAAAoI,SAAA;QAAA,IAAAC,GAAA,EAAA7K,IAAA,EAAA2D,QAAA,EAAAmH,eAAA,EAAAnJ,iBAAA,EAAAoJ,UAAA,EAAAC,MAAA,EAAAC,CAAA,EAAA/I,YAAA,EAAAgJ,eAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAlF,IAAA,EAAAmF,UAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,MAAA;QAAA,WAAAtJ,oBAAA,CAAA3C,OAAA,IAAA8C,IAAA,UAAAoJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlJ,IAAA,GAAAkJ,SAAA,CAAAjJ,IAAA;YAAA;cACA6H,MAAA,CAAA7K,OAAA;cAAAiM,SAAA,CAAAjJ,IAAA;cAAA,OACA,IAAAkJ,qBAAA,EAAAzB,EAAA;YAAA;cAAAM,GAAA,GAAAkB,SAAA,CAAA/I,IAAA;cACAhD,IAAA,GAAA6K,GAAA,CAAAhL,IAAA;cAAAkM,SAAA,CAAAjJ,IAAA;cAAA,OAEA,IAAAmJ,yCAAA;gBAAAC,WAAA,EAAAlM,IAAA,CAAA0D;cAAA;YAAA;cAAAiH,MAAA,CAAA9I,QAAA,GAAAkK,SAAA,CAAA/I,IAAA;cACAW,QAAA,GAAA3D,IAAA,CAAA2D,QAAA;cAAA,KACAA,QAAA;gBAAAoI,SAAA,CAAAjJ,IAAA;gBAAA;cAAA;cACAgI,eAAA,IAAAnH,QAAA;cACAmH,eAAA,CAAAzC,IAAA,CAAAsC,MAAA,CAAAlD,MAAA,CAAA9D,QAAA,EAAAgF,GAAA,YAAAjB,MAAA;cAAAqE,SAAA,CAAAjJ,IAAA;cAAA,OACA,IAAAqJ,+BAAA;gBAAArB,eAAA,EAAAA;cAAA;YAAA;cAAAnJ,iBAAA,GAAAoK,SAAA,CAAA/I,IAAA;cAAA+H,UAAA,OAAA9E,2BAAA,CAAArG,OAAA,EACA+B,iBAAA;cAAA;gBAAA,KAAAoJ,UAAA,CAAAlC,CAAA,MAAAmC,MAAA,GAAAD,UAAA,CAAAjC,CAAA,IAAAC,IAAA;kBAAAkC,CAAA,GAAAD,MAAA,CAAA5K,KAAA;kBACA6K,CAAA,CAAA7D,MAAA,GAAAgF,MAAA,CAAAnB,CAAA,CAAA7D,MAAA;gBACA;cAAA,SAAA4B,GAAA;gBAAA+B,UAAA,CAAA9B,CAAA,CAAAD,GAAA;cAAA;gBAAA+B,UAAA,CAAA7B,CAAA;cAAA;cACAyB,MAAA,CAAAhJ,iBAAA,GAAAA,iBAAA;YAAA;cAAAoK,SAAA,CAAAjJ,IAAA;cAAA,OAGA,IAAAuJ,qBAAA;gBAAAC,QAAA,EAAA3I;cAAA;YAAA;cAAAzB,YAAA,GAAA6J,SAAA,CAAA/I,IAAA;cACA2H,MAAA,CAAAzI,YAAA,GAAAA,YAAA;cAEAyI,MAAA,CAAA3K,IAAA,GAAAA,IAAA;cAEA,IAAAA,IAAA,CAAAuM,eAAA,IAAAvM,IAAA,CAAAuM,eAAA,CAAAhD,MAAA;gBACAoB,MAAA,CAAAjJ,WAAA,GAAA1B,IAAA,CAAAuM,eAAA;cACA;cAAAR,SAAA,CAAAjJ,IAAA;cAAA,OACA6H,MAAA,CAAAlH,gBAAA,CAAAzD,IAAA,CAAA0D,OAAA,EAAA1D,IAAA,CAAA2D,QAAA;YAAA;cAEA,IAAA3D,IAAA,CAAA8B,iBAAA;gBACA6I,MAAA,CAAA7I,iBAAA,GAAA9B,IAAA,CAAA8B,iBAAA;cACA;cAEA,IAAA9B,IAAA,CAAA+B,gBAAA;gBACA4I,MAAA,CAAA5I,gBAAA,GAAA/B,IAAA,CAAA+B,gBAAA;cACA;cAEA,IAAA/B,IAAA,CAAAgC,iBAAA;gBACA2I,MAAA,CAAA3I,iBAAA,GAAAhC,IAAA,CAAAgC,iBAAA;cACA;cAEA,IAAAhC,IAAA,CAAAiC,aAAA;gBACA0I,MAAA,CAAA1I,aAAA,GAAAjC,IAAA,CAAAiC,aAAA;cACA;cAAA8J,SAAA,CAAAjJ,IAAA;cAAA,OAEA,IAAA0J,mCAAA;gBAAA9I,OAAA,EAAA1D,IAAA,CAAA0D;cAAA;YAAA;cAAAwH,eAAA,GAAAa,SAAA,CAAA/I,IAAA;cAAA,IACA2H,MAAA,CAAA7I,iBAAA,CAAAyH,MAAA;gBAAAwC,SAAA,CAAAjJ,IAAA;gBAAA;cAAA;cAAAqI,UAAA,OAAAlF,2BAAA,CAAArG,OAAA,EACAsL,eAAA;cAAA;gBAAA,KAAAC,UAAA,CAAAtC,CAAA,MAAAuC,MAAA,GAAAD,UAAA,CAAArC,CAAA,IAAAC,IAAA;kBAAA7C,IAAA,GAAAkF,MAAA,CAAAhL,KAAA;kBACA,IAAA8F,IAAA,CAAAxG,IAAA;oBACAiL,MAAA,CAAA8B,YAAA,CAAA9B,MAAA,CAAA7I,iBAAA,EAAAoE,IAAA;kBACA;gBACA;cAAA,SAAA8C,GAAA;gBAAAmC,UAAA,CAAAlC,CAAA,CAAAD,GAAA;cAAA;gBAAAmC,UAAA,CAAAjC,CAAA;cAAA;cAAA6C,SAAA,CAAAjJ,IAAA;cAAA,OACA6H,MAAA,CAAA+B,eAAA;YAAA;cAAA,IAGA/B,MAAA,CAAA3I,iBAAA,CAAAuH,MAAA;gBAAAwC,SAAA,CAAAjJ,IAAA;gBAAA;cAAA;cAAAuI,UAAA,OAAApF,2BAAA,CAAArG,OAAA,EACAsL,eAAA;cAAA;gBAAA,KAAAG,UAAA,CAAAxC,CAAA,MAAAyC,MAAA,GAAAD,UAAA,CAAAvC,CAAA,IAAAC,IAAA;kBAAA7C,KAAA,GAAAoF,MAAA,CAAAlL,KAAA;kBACA,IAAA8F,KAAA,CAAAxG,IAAA;oBACAiL,MAAA,CAAA8B,YAAA,CAAA9B,MAAA,CAAA3I,iBAAA,EAAAkE,KAAA;kBACA;gBACA;cAAA,SAAA8C,GAAA;gBAAAqC,UAAA,CAAApC,CAAA,CAAAD,GAAA;cAAA;gBAAAqC,UAAA,CAAAnC,CAAA;cAAA;cAAA6C,SAAA,CAAAjJ,IAAA;cAAA,OACA6H,MAAA,CAAA+B,eAAA;YAAA;cAAA,IAGA/B,MAAA,CAAA5I,gBAAA,CAAAwH,MAAA;gBAAAwC,SAAA,CAAAjJ,IAAA;gBAAA;cAAA;cAAA0I,UAAA,OAAAvF,2BAAA,CAAArG,OAAA,EACAsL,eAAA;cAAA;gBAAA,KAAAM,UAAA,CAAA3C,CAAA,MAAA4C,MAAA,GAAAD,UAAA,CAAA1C,CAAA,IAAAC,IAAA;kBAAA7C,MAAA,GAAAuF,MAAA,CAAArL,KAAA;kBACA,IAAA8F,MAAA,CAAAxG,IAAA;oBACAiL,MAAA,CAAA8B,YAAA,CAAA9B,MAAA,CAAA5I,gBAAA,EAAAmE,MAAA;kBACA;gBACA;cAAA,SAAA8C,GAAA;gBAAAwC,UAAA,CAAAvC,CAAA,CAAAD,GAAA;cAAA;gBAAAwC,UAAA,CAAAtC,CAAA;cAAA;cAAA6C,SAAA,CAAAjJ,IAAA;cAAA,OACA6H,MAAA,CAAA+B,eAAA;YAAA;cAAA,IAGA/B,MAAA,CAAA1I,aAAA,CAAAsH,MAAA;gBAAAwC,SAAA,CAAAjJ,IAAA;gBAAA;cAAA;cAAA6I,UAAA,OAAA1F,2BAAA,CAAArG,OAAA,EACAsL,eAAA;cAAA;gBAAA,KAAAS,UAAA,CAAA9C,CAAA,MAAA+C,MAAA,GAAAD,UAAA,CAAA7C,CAAA,IAAAC,IAAA;kBAAA7C,MAAA,GAAA0F,MAAA,CAAAxL,KAAA;kBACA,IAAA8F,MAAA,CAAAxG,IAAA;oBACAiL,MAAA,CAAA8B,YAAA,CAAA9B,MAAA,CAAA1I,aAAA,EAAAiE,MAAA;kBACA;gBACA;cAAA,SAAA8C,GAAA;gBAAA2C,UAAA,CAAA1C,CAAA,CAAAD,GAAA;cAAA;gBAAA2C,UAAA,CAAAzC,CAAA;cAAA;cAAA6C,SAAA,CAAAjJ,IAAA;cAAA,OACA6H,MAAA,CAAA+B,eAAA;YAAA;cAAAX,SAAA,CAAAjJ,IAAA;cAAA,OAGA6H,MAAA,CAAAgC,cAAA;YAAA;cAAAZ,SAAA,CAAAjJ,IAAA;cAAA,OACA6H,MAAA,CAAA/G,WAAA;YAAA;cAEA+G,MAAA,CAAA7K,OAAA;YAAA;YAAA;cAAA,OAAAiM,SAAA,CAAA9I,IAAA;UAAA;QAAA,GAAA2H,QAAA;MAAA;IACA;IACA8B,eAAA,WAAAA,gBAAAhN,IAAA;MAAA,IAAAkN,MAAA;MAAA,WAAAtK,kBAAA,CAAA1C,OAAA,mBAAA2C,oBAAA,CAAA3C,OAAA,IAAA4C,IAAA,UAAAqK,SAAA;QAAA,IAAArD,YAAA,EAAAsD,UAAA,EAAAC,MAAA,EAAA7G,IAAA;QAAA,WAAA3D,oBAAA,CAAA3C,OAAA,IAAA8C,IAAA,UAAAsK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApK,IAAA,GAAAoK,SAAA,CAAAnK,IAAA;YAAA;cACA,IAAApD,IAAA;gBACA8J,YAAA,GAAAoD,MAAA,CAAAM,IAAA;gBAAAJ,UAAA,OAAA7G,2BAAA,CAAArG,OAAA,EACAgN,MAAA,CAAAlN,IAAA;gBAAA;kBAAA,KAAAoN,UAAA,CAAAjE,CAAA,MAAAkE,MAAA,GAAAD,UAAA,CAAAhE,CAAA,IAAAC,IAAA;oBAAA7C,IAAA,GAAA6G,MAAA,CAAA3M,KAAA;oBACAoJ,YAAA,GAAAoD,MAAA,CAAAjE,GAAA,CAAAa,YAAA,EAAAtD,IAAA,CAAAsD,YAAA;kBACA;gBAAA,SAAAR,GAAA;kBAAA8D,UAAA,CAAA7D,CAAA,CAAAD,GAAA;gBAAA;kBAAA8D,UAAA,CAAA5D,CAAA;gBAAA;gBACA0D,MAAA,CAAA5M,IAAA,CAAAN,IAAA,gBAAA8J,YAAA,CAAA2D,QAAA;gBACAP,MAAA,CAAA5M,IAAA,CAAAN,IAAA,aAAAkN,MAAA,CAAAlN,IAAA,kBAAA6J,MAAA;cACA;cAAA0D,SAAA,CAAAnK,IAAA;cAAA,OAEA8J,MAAA,CAAAD,cAAA;YAAA;cAAAM,SAAA,CAAAnK,IAAA;cAAA,OACA8J,MAAA,CAAAjI,SAAA;YAAA;cAAAsI,SAAA,CAAAnK,IAAA;cAAA,OACA8J,MAAA,CAAAhJ,WAAA;YAAA;YAAA;cAAA,OAAAqJ,SAAA,CAAAhK,IAAA;UAAA;QAAA,GAAA4J,QAAA;MAAA;IACA;IACAF,cAAA,WAAAA,eAAA;MAAA,IAAAS,MAAA;MAAA,WAAA9K,kBAAA,CAAA1C,OAAA,mBAAA2C,oBAAA,CAAA3C,OAAA,IAAA4C,IAAA,UAAA6K,SAAA;QAAA,IAAAnN,UAAA,EAAAwB,WAAA,EAAA4L,WAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAxN,IAAA;QAAA,WAAAuC,oBAAA,CAAA3C,OAAA,IAAA8C,IAAA,UAAA+K,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7K,IAAA,GAAA6K,SAAA,CAAA5K,IAAA;YAAA;cACA5C,UAAA,GAAAkN,MAAA,CAAAlN,UAAA;cACAwB,WAAA,GAAA0L,MAAA,CAAA1L,WAAA;cAAA4L,WAAA,OAAArH,2BAAA,CAAArG,OAAA,EACAM,UAAA;cAAAwN,SAAA,CAAA7K,IAAA;cAAA2K,MAAA,oBAAAjL,oBAAA,CAAA3C,OAAA,IAAA4C,IAAA,UAAAgL,OAAA;gBAAA,IAAAG,CAAA,EAAAC,mBAAA,EAAApE,YAAA,EAAA3I,OAAA;gBAAA,WAAA0B,oBAAA,CAAA3C,OAAA,IAAA8C,IAAA,UAAAmL,QAAAC,SAAA;kBAAA,kBAAAA,SAAA,CAAAjL,IAAA,GAAAiL,SAAA,CAAAhL,IAAA;oBAAA;sBAAA6K,CAAA,GAAAJ,OAAA,CAAAnN,KAAA;sBACAwN,mBAAA,GAAAlM,WAAA,CAAAyF,MAAA,WAAA1B,CAAA;wBAAA,OAAAA,CAAA,CAAAI,QAAA,KAAA8H,CAAA,CAAAvN,KAAA;sBAAA;sBACAuN,CAAA,CAAAjJ,WAAA,GAAA0I,MAAA,CAAAW,UAAA,CAAAH,mBAAA,CAAAhI,GAAA,WAAAH,CAAA;wBAAA,OAAAA,CAAA,CAAAf,WAAA;sBAAA;sBACAiJ,CAAA,CAAAnJ,gBAAA,GAAA4I,MAAA,CAAAW,UAAA,CAAAH,mBAAA,CAAAhI,GAAA,WAAAH,CAAA;wBAAA,OAAAA,CAAA,CAAAjB,gBAAA;sBAAA;sBACAmJ,CAAA,CAAAlJ,cAAA,GAAA2I,MAAA,CAAAW,UAAA,CAAAH,mBAAA,CAAAhI,GAAA,WAAAH,CAAA;wBAAA,OAAAA,CAAA,CAAAhB,cAAA;sBAAA;sBACAkJ,CAAA,CAAAlN,aAAA,GAAA2M,MAAA,CAAAW,UAAA,CAAAX,MAAA,CAAAtL,iBAAA,CAAAqF,MAAA,WAAA1B,CAAA;wBAAA,OAAAA,CAAA,CAAAI,QAAA,KAAA8H,CAAA,CAAAvN,KAAA;sBAAA,GAAAwF,GAAA,WAAAH,CAAA;wBAAA,OAAAA,CAAA,CAAAK,YAAA,GAAAL,CAAA,CAAAK,YAAA,QAAAL,CAAA,CAAA+D,YAAA;sBAAA;sBACAmE,CAAA,CAAAjN,YAAA,GAAA0M,MAAA,CAAAW,UAAA,CAAAX,MAAA,CAAArL,gBAAA,CAAAoF,MAAA,WAAA1B,CAAA;wBAAA,OAAAA,CAAA,CAAAI,QAAA,KAAA8H,CAAA,CAAAvN,KAAA;sBAAA,GAAAwF,GAAA,WAAAH,CAAA;wBAAA,OAAAA,CAAA,CAAAK,YAAA,GAAAL,CAAA,CAAAK,YAAA,QAAAL,CAAA,CAAA+D,YAAA;sBAAA;sBACAmE,CAAA,CAAAhN,aAAA,GAAAyM,MAAA,CAAAW,UAAA,CAAAX,MAAA,CAAApL,iBAAA,CAAAmF,MAAA,WAAA1B,CAAA;wBAAA,OAAAA,CAAA,CAAAI,QAAA,KAAA8H,CAAA,CAAAvN,KAAA;sBAAA,GAAAwF,GAAA,WAAAH,CAAA;wBAAA,OAAAA,CAAA,CAAAK,YAAA,GAAAL,CAAA,CAAAK,YAAA,QAAAL,CAAA,CAAA+D,YAAA;sBAAA;sBACAmE,CAAA,CAAA/M,SAAA,GAAAwM,MAAA,CAAAW,UAAA,CAAAX,MAAA,CAAAnL,aAAA,CAAAkF,MAAA,WAAA1B,CAAA;wBAAA,OAAAA,CAAA,CAAAI,QAAA,KAAA8H,CAAA,CAAAvN,KAAA;sBAAA,GAAAwF,GAAA,WAAAH,CAAA;wBAAA,OAAAA,CAAA,CAAAK,YAAA,GAAAL,CAAA,CAAAK,YAAA,QAAAL,CAAA,CAAA+D,YAAA;sBAAA;sBACAA,YAAA,GAAA4D,MAAA,CAAAW,UAAA,CAAAH,mBAAA,CAAAhI,GAAA,WAAAH,CAAA;wBAAA,OAAAA,CAAA,CAAAK,YAAA,GAAAL,CAAA,CAAAK,YAAA,QAAAL,CAAA,CAAA+D,YAAA;sBAAA;sBAEAmE,CAAA,CAAAK,gBAAA,GAAAxE,YAAA;sBACAmE,CAAA,CAAAnE,YAAA,GAAA4D,MAAA,CAAAW,UAAA,EAAAvE,YAAA,EAAAmE,CAAA,CAAAlN,aAAA,EAAAkN,CAAA,CAAAjN,YAAA,EAAAiN,CAAA,CAAAhN,aAAA;sBACAgN,CAAA,CAAAnD,UAAA,GAAA4C,MAAA,CAAAW,UAAA,CAAAH,mBAAA,CAAAzG,MAAA,WAAA1B,CAAA;wBAAA,OAAAA,CAAA,CAAAI,QAAA,KAAA8H,CAAA,CAAAvN,KAAA;sBAAA,GAAAwF,GAAA,WAAAH,CAAA;wBAAA,OAAAA,CAAA,CAAA+E,UAAA;sBAAA;sBACAmD,CAAA,CAAArN,WAAA,GAAAqN,CAAA,CAAAnD,UAAA;sBACAmD,CAAA,CAAAtN,UAAA,GAAA+M,MAAA,CAAAW,UAAA,EAAAJ,CAAA,CAAAnD,UAAA,EAAAmD,CAAA,CAAAlN,aAAA,EAAAkN,CAAA,CAAAjN,YAAA,EAAAiN,CAAA,CAAAhN,aAAA;;sBAEAgN,CAAA,CAAA7M,QAAA,OAAAmN,mBAAA,CAAArO,OAAA,MAAAsO,GAAA,CAAAN,mBAAA,CAAAzG,MAAA,WAAA1B,CAAA;wBAAA,OAAAA,CAAA,CAAA0I,QAAA;sBAAA,GAAAvI,GAAA,WAAAH,CAAA;wBAAA,OAAAA,CAAA,CAAArC,QAAA;sBAAA,KAAAmG,MAAA;sBACAoE,CAAA,CAAA5M,SAAA,OAAAkN,mBAAA,CAAArO,OAAA,MAAAsO,GAAA,CAAAN,mBAAA,CAAAzG,MAAA,WAAA1B,CAAA;wBAAA,OAAAA,CAAA,CAAA0I,QAAA;sBAAA,GAAAvI,GAAA,WAAAH,CAAA;wBAAA,OAAAA,CAAA,CAAArC,QAAA;sBAAA,KAAAmG,MAAA;sBACAoE,CAAA,CAAA3M,SAAA,OAAAiN,mBAAA,CAAArO,OAAA,MAAAsO,GAAA,CAAAN,mBAAA,CAAAzG,MAAA,WAAA1B,CAAA;wBAAA,OAAAA,CAAA,CAAA0I,QAAA;sBAAA,GAAAvI,GAAA,WAAAH,CAAA;wBAAA,OAAAA,CAAA,CAAArC,QAAA;sBAAA,KAAAmG,MAAA;sBACA1I,OAAA,OAAAoN,mBAAA,CAAArO,OAAA,MAAAsO,GAAA,CAAAN,mBAAA,CAAAhI,GAAA,WAAAH,CAAA;wBAAA,OAAAA,CAAA,CAAArC,QAAA;sBAAA,KAAAmG,MAAA;sBACAoE,CAAA,CAAA1M,UAAA,GAAAmM,MAAA,CAAAtL,iBAAA,CAAAqF,MAAA,WAAA1B,CAAA;wBAAA,OAAAA,CAAA,CAAAI,QAAA,KAAA8H,CAAA,CAAAvN,KAAA;sBAAA,GAAAmJ,MAAA;sBACAoE,CAAA,CAAAzM,SAAA,GAAAkM,MAAA,CAAArL,gBAAA,CAAAoF,MAAA,WAAA1B,CAAA;wBAAA,OAAAA,CAAA,CAAAI,QAAA,KAAA8H,CAAA,CAAAvN,KAAA;sBAAA,GAAAmJ,MAAA;sBACAoE,CAAA,CAAAxM,UAAA,GAAAiM,MAAA,CAAApL,iBAAA,CAAAmF,MAAA,WAAA1B,CAAA;wBAAA,OAAAA,CAAA,CAAAI,QAAA,KAAA8H,CAAA,CAAAvN,KAAA;sBAAA,GAAAmJ,MAAA;sBACAoE,CAAA,CAAAvM,MAAA,GAAAgM,MAAA,CAAAnL,aAAA,CAAAkF,MAAA,WAAA1B,CAAA;wBAAA,OAAAA,CAAA,CAAAI,QAAA,KAAA8H,CAAA,CAAAvN,KAAA;sBAAA,GAAAmJ,MAAA;sBACAoE,CAAA,CAAA9M,OAAA,GAAAuM,MAAA,CAAAW,UAAA,EAAAlN,OAAA,EAAA8M,CAAA,CAAA1M,UAAA,EAAA0M,CAAA,CAAAzM,SAAA,EAAAyM,CAAA,CAAAxM,UAAA;oBAAA;oBAAA;sBAAA,OAAA2M,SAAA,CAAA7K,IAAA;kBAAA;gBAAA,GAAAuK,MAAA;cAAA;cAAAF,WAAA,CAAAzE,CAAA;YAAA;cAAA,KAAA0E,OAAA,GAAAD,WAAA,CAAAxE,CAAA,IAAAC,IAAA;gBAAA2E,SAAA,CAAA5K,IAAA;gBAAA;cAAA;cAAA,OAAA4K,SAAA,CAAAvE,aAAA,CAAAqE,MAAA;YAAA;cAAAE,SAAA,CAAA5K,IAAA;cAAA;YAAA;cAAA4K,SAAA,CAAA5K,IAAA;cAAA;YAAA;cAAA4K,SAAA,CAAA7K,IAAA;cAAA6K,SAAA,CAAAtE,EAAA,GAAAsE,SAAA;cAAAJ,WAAA,CAAArE,CAAA,CAAAyE,SAAA,CAAAtE,EAAA;YAAA;cAAAsE,SAAA,CAAA7K,IAAA;cAAAyK,WAAA,CAAApE,CAAA;cAAA,OAAAwE,SAAA,CAAArE,MAAA;YAAA;cAGArJ,IAAA,GAAAoN,MAAA,CAAApN,IAAA;cACAA,IAAA,CAAAwJ,YAAA,GAAA4D,MAAA,CAAAW,UAAA,CAAA7N,UAAA,CAAA0F,GAAA,WAAAH,CAAA;gBAAA,OAAAA,CAAA,CAAA+D,YAAA;cAAA;cACAxJ,IAAA,CAAA0E,WAAA,GAAA0I,MAAA,CAAAW,UAAA,CAAA7N,UAAA,CAAA0F,GAAA,WAAAH,CAAA;gBAAA,OAAAA,CAAA,CAAAf,WAAA;cAAA;cACA1E,IAAA,CAAAwE,gBAAA,GAAA4I,MAAA,CAAAW,UAAA,CAAA7N,UAAA,CAAA0F,GAAA,WAAAH,CAAA;gBAAA,OAAAA,CAAA,CAAAjB,gBAAA;cAAA;cACAxE,IAAA,CAAAyE,cAAA,GAAA2I,MAAA,CAAAW,UAAA,CAAA7N,UAAA,CAAA0F,GAAA,WAAAH,CAAA;gBAAA,OAAAA,CAAA,CAAAhB,cAAA;cAAA;cACAzE,IAAA,CAAAS,aAAA,GAAA2M,MAAA,CAAAW,UAAA,CAAA7N,UAAA,CAAA0F,GAAA,WAAAH,CAAA;gBAAA,OAAAA,CAAA,CAAAhF,aAAA;cAAA;cACAT,IAAA,CAAAU,YAAA,GAAA0M,MAAA,CAAAW,UAAA,CAAA7N,UAAA,CAAA0F,GAAA,WAAAH,CAAA;gBAAA,OAAAA,CAAA,CAAA/E,YAAA;cAAA;cACAV,IAAA,CAAAW,aAAA,GAAAyM,MAAA,CAAAW,UAAA,CAAA7N,UAAA,CAAA0F,GAAA,WAAAH,CAAA;gBAAA,OAAAA,CAAA,CAAA9E,aAAA;cAAA;cACAX,IAAA,CAAAY,SAAA,GAAAwM,MAAA,CAAAW,UAAA,CAAA7N,UAAA,CAAA0F,GAAA,WAAAH,CAAA;gBAAA,OAAAA,CAAA,CAAA7E,SAAA;cAAA;cACAZ,IAAA,CAAAwK,UAAA,GAAA4C,MAAA,CAAAW,UAAA,CAAA7N,UAAA,CAAA0F,GAAA,WAAAH,CAAA;gBAAA,OAAAA,CAAA,CAAA+E,UAAA;cAAA;cACAxK,IAAA,CAAAK,UAAA,GAAA+M,MAAA,CAAAW,UAAA,CAAA7N,UAAA,CAAA0F,GAAA,WAAAH,CAAA;gBAAA,OAAAA,CAAA,CAAApF,UAAA;cAAA;cAEAL,IAAA,CAAAc,QAAA,GAAAsM,MAAA,CAAAW,UAAA,CAAA7N,UAAA,CAAA0F,GAAA,WAAAH,CAAA;gBAAA,OAAAA,CAAA,CAAA3E,QAAA;cAAA;cACAd,IAAA,CAAAe,SAAA,GAAAqM,MAAA,CAAAW,UAAA,CAAA7N,UAAA,CAAA0F,GAAA,WAAAH,CAAA;gBAAA,OAAAA,CAAA,CAAA1E,SAAA;cAAA;cACAf,IAAA,CAAAgB,SAAA,GAAAoM,MAAA,CAAAW,UAAA,CAAA7N,UAAA,CAAA0F,GAAA,WAAAH,CAAA;gBAAA,OAAAA,CAAA,CAAAzE,SAAA;cAAA;cACAhB,IAAA,CAAAa,OAAA,GAAAuM,MAAA,CAAAW,UAAA,CAAA7N,UAAA,CAAA0F,GAAA,WAAAH,CAAA;gBAAA,OAAAA,CAAA,CAAA5E,OAAA;cAAA;cACAb,IAAA,CAAAiB,UAAA,GAAAmM,MAAA,CAAAW,UAAA,CAAA7N,UAAA,CAAA0F,GAAA,WAAAH,CAAA;gBAAA,OAAAA,CAAA,CAAAxE,UAAA;cAAA;cACAjB,IAAA,CAAAkB,SAAA,GAAAkM,MAAA,CAAAW,UAAA,CAAA7N,UAAA,CAAA0F,GAAA,WAAAH,CAAA;gBAAA,OAAAA,CAAA,CAAAvE,SAAA;cAAA;cACAlB,IAAA,CAAAmB,UAAA,GAAAiM,MAAA,CAAAW,UAAA,CAAA7N,UAAA,CAAA0F,GAAA,WAAAH,CAAA;gBAAA,OAAAA,CAAA,CAAAtE,UAAA;cAAA;cACAnB,IAAA,CAAAoB,MAAA,GAAAgM,MAAA,CAAAW,UAAA,CAAA7N,UAAA,CAAA0F,GAAA,WAAAH,CAAA;gBAAA,OAAAA,CAAA,CAAArE,MAAA;cAAA;YAAA;YAAA;cAAA,OAAAsM,SAAA,CAAAzK,IAAA;UAAA;QAAA,GAAAoK,QAAA;MAAA;IAEA;IACAU,UAAA,WAAAA,WAAAK,KAAA;MACA,IAAAC,IAAA,QAAAnB,IAAA;MAAA,IAAAoB,WAAA,OAAArI,2BAAA,CAAArG,OAAA,EACAwO,KAAA;QAAAG,OAAA;MAAA;QAAA,KAAAD,WAAA,CAAAzF,CAAA,MAAA0F,OAAA,GAAAD,WAAA,CAAAxF,CAAA,IAAAC,IAAA;UAAA,IAAAD,CAAA,GAAAyF,OAAA,CAAAnO,KAAA;UACAiO,IAAA,QAAA1F,GAAA,CAAA0F,IAAA,EAAAvF,CAAA;QACA;MAAA,SAAAE,GAAA;QAAAsF,WAAA,CAAArF,CAAA,CAAAD,GAAA;MAAA;QAAAsF,WAAA,CAAApF,CAAA;MAAA;MACA,OAAAmF,IAAA,CAAAlB,QAAA;IACA;IACAV,YAAA,WAAAA,aAAA2B,KAAA,EAAAI,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAtI,aAAA,QAAAxE,iBAAA,CAAAwF,MAAA,WAAA1B,CAAA;QAAA,OAAAA,CAAA,CAAA2B,MAAA,KAAAoH,GAAA,CAAApH,MAAA;MAAA;MACA,IAAAjB,aAAA,IAAAA,aAAA;QACA,KAAAiI,KAAA,CAAAxI,GAAA,WAAAH,CAAA;UAAA,OAAAA,CAAA,CAAA2B,MAAA;QAAA,GAAAsH,QAAA,CAAAF,GAAA,CAAApH,MAAA;UACA,IAAAjB,cAAA,QAAAxE,iBAAA,CAAAwF,MAAA,WAAA1B,CAAA;YAAA,OAAAA,CAAA,CAAA2B,MAAA,KAAAoH,GAAA,CAAApH,MAAA;UAAA,GAAAC,IAAA,WAAAC,CAAA,EAAAC,CAAA;YAAA,OAAAD,CAAA,CAAAE,aAAA,GAAAD,CAAA,CAAAC,aAAA;UAAA;UACA,IAAArB,cAAA,IAAAA,cAAA;YAAA;YACA,IAAAC,UAAA,GAAAD,cAAA,CAAAP,GAAA,WAAAH,CAAA;cAAA,OAAAgJ,MAAA,CAAAhH,MAAA,CAAAhC,CAAA,CAAA+B,aAAA,EAAAE,MAAA;YAAA;YACA,IAAAiH,SAAA,QAAAlH,MAAA,MAAAzH,IAAA,CAAA2D,QAAA,EAAA+D,MAAA;YACA,IAAAkH,WAAA,GAAAD,SAAA;YACA,IAAAE,aAAA,GAAAF,SAAA;YACA,IAAAH,GAAA,CAAA3I,QAAA;cACA+I,WAAA,GAAAD,SAAA;cACAE,aAAA,QAAApH,MAAA,CAAAkH,SAAA,EAAAhG,GAAA,YAAAjB,MAAA;YACA;YACA,IAAArB,MAAA,OAAAsB,2BAAA,EAAAvB,UAAA,EAAAwI,WAAA;YACA,IAAAtI,QAAA,OAAAqB,2BAAA,EAAAvB,UAAA,EAAAyI,aAAA;YACA,IAAAxI,MAAA,IAAAC,QAAA;cACA,IAAAD,MAAA,GAAAuI,WAAA;gBAAA;gBACAvI,MAAA,GAAAuI,WAAA;cACA;cACA,IAAAE,OAAA,QAAArH,MAAA,CAAAnB,QAAA,EAAA0B,IAAA,CAAA3B,MAAA;cACA,IAAA0I,WAAA,IACA;gBACAC,KAAA,OAAAvH,MAAA,CAAApB,MAAA,EAAAqB,MAAA;gBACAuH,GAAA,OAAAxH,MAAA,CAAAnB,QAAA,EAAAoB,MAAA;cACA,EACA;cACA,IAAAhD,WAAA,OAAAwK,kCAAA,EAAAH,WAAA,OAAAlN,QAAA;cACA,IAAA2H,YAAA,QAAA2F,QAAA,CAAAL,OAAA,EAAApK,WAAA,EAAAyI,QAAA;cACA,IAAA2B,OAAA;gBACAV,KAAA,CAAA/F,IAAA;kBACAjB,MAAA,EAAAoH,GAAA,CAAApH,MAAA;kBACAhE,QAAA,EAAAoL,GAAA,CAAApL,QAAA;kBACAgM,QAAA,EAAAZ,GAAA,CAAAY,QAAA;kBACAxI,SAAA,EAAAP,MAAA;kBACA+B,OAAA,EAAA9B,QAAA;kBACAT,QAAA,EAAA2I,GAAA,CAAA3I,QAAA;kBACAiJ,OAAA,EAAAA,OAAA;kBACApK,WAAA,EAAAA,WAAA;kBACA8E,YAAA,EAAAgF,GAAA,CAAA9O,IAAA,0BAAA2P,yBAAA,EAAA7F,YAAA;kBAAA;kBACA1D,YAAA,EAAAwJ;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MAAA,WAAAlN,kBAAA,CAAA1C,OAAA,mBAAA2C,oBAAA,CAAA3C,OAAA,IAAA4C,IAAA,UAAAiN,SAAA;QAAA,IAAAC,KAAA,EAAAC,MAAA,EAAA9E,GAAA;QAAA,WAAAtI,oBAAA,CAAA3C,OAAA,IAAA8C,IAAA,UAAAkN,UAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAhN,IAAA,GAAAgN,UAAA,CAAA/M,IAAA;YAAA;cAAA+M,UAAA,CAAA/M,IAAA;cAAA,OACA0M,MAAA,CAAA5K,KAAA,SAAAkL,QAAA;YAAA;cAAA,KACAN,MAAA,CAAA9N,WAAA,CAAA6H,MAAA;gBAAAsG,UAAA,CAAA/M,IAAA;gBAAA;cAAA;cACA0M,MAAA,CAAAzP,UAAA;cACAC,KAAA,GAAA+P,MAAA,CAAAC,MAAA,KAAAR,MAAA,CAAAxP,IAAA;cACA2P,MAAA;gBACAhM,QAAA,EAAA3D,KAAA,CAAA2D,QAAA;gBACAD,OAAA,EAAA1D,KAAA,CAAA0D,OAAA;gBACA6I,eAAA,EAAAiD,MAAA,CAAA9N,WAAA;gBACAK,gBAAA,KAAAkO,MAAA,KAAAhC,mBAAA,CAAArO,OAAA,EACA4P,MAAA,CAAA1N,iBAAA,OAAAmM,mBAAA,CAAArO,OAAA,EACA4P,MAAA,CAAAzN,gBAAA,OAAAkM,mBAAA,CAAArO,OAAA,EACA4P,MAAA,CAAAxN,iBAAA,OAAAiM,mBAAA,CAAArO,OAAA,EACA4P,MAAA,CAAAvN,aAAA;cAEA;cAAA4N,UAAA,CAAA/M,IAAA;cAAA,OACA,IAAAoN,4BAAA,EAAAP,MAAA;YAAA;cAAA9E,GAAA,GAAAgF,UAAA,CAAA7M,IAAA;cACA,IAAA6H,GAAA,CAAAsF,IAAA;gBACAX,MAAA,CAAAY,UAAA;cACA;YAAA;cAEAZ,MAAA,CAAAzP,UAAA;YAAA;YAAA;cAAA,OAAA8P,UAAA,CAAA5M,IAAA;UAAA;QAAA,GAAAwM,QAAA;MAAA;IACA;IACAY,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MAAA,WAAAhO,kBAAA,CAAA1C,OAAA,mBAAA2C,oBAAA,CAAA3C,OAAA,IAAA4C,IAAA,UAAA+N,SAAA;QAAA,IAAAvQ,IAAA,EAAA0B,WAAA,EAAA8O,WAAA,EAAAC,OAAA,EAAAC,MAAA;QAAA,WAAAnO,oBAAA,CAAA3C,OAAA,IAAA8C,IAAA,UAAAiO,UAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA/N,IAAA,GAAA+N,UAAA,CAAA9N,IAAA;YAAA;cACA9C,IAAA,GAAA+P,MAAA,CAAAC,MAAA,KAAAM,OAAA,CAAAtQ,IAAA;cAEA0B,WAAA,GAAA4O,OAAA,CAAA5O,WAAA;cAAA8O,WAAA,OAAAvK,2BAAA,CAAArG,OAAA,EACA8B,WAAA;cAAAkP,UAAA,CAAA/N,IAAA;cAAA6N,MAAA,oBAAAnO,oBAAA,CAAA3C,OAAA,IAAA4C,IAAA,UAAAkO,OAAA;gBAAA,IAAAxK,IAAA;gBAAA,WAAA3D,oBAAA,CAAA3C,OAAA,IAAA8C,IAAA,UAAAmO,QAAAC,UAAA;kBAAA,kBAAAA,UAAA,CAAAjO,IAAA,GAAAiO,UAAA,CAAAhO,IAAA;oBAAA;sBAAAoD,IAAA,GAAAuK,OAAA,CAAArQ,KAAA;sBACA8F,IAAA,CAAA6K,aAAA,GAAAT,OAAA,CAAAnO,gBAAA,CAAAgF,MAAA,WAAA1B,CAAA;wBAAA,OAAAS,IAAA,CAAAX,cAAA,CAAAmJ,QAAA,CAAAjJ,CAAA,CAAArF,KAAA;sBAAA,GAAAwF,GAAA,WAAAH,CAAA;wBAAA,OAAAA,CAAA,CAAAtF,KAAA;sBAAA,GAAA6Q,IAAA;oBAAA;oBAAA;sBAAA,OAAAF,UAAA,CAAA7N,IAAA;kBAAA;gBAAA,GAAAyN,MAAA;cAAA;cAAAF,WAAA,CAAA3H,CAAA;YAAA;cAAA,KAAA4H,OAAA,GAAAD,WAAA,CAAA1H,CAAA,IAAAC,IAAA;gBAAA6H,UAAA,CAAA9N,IAAA;gBAAA;cAAA;cAAA,OAAA8N,UAAA,CAAAzH,aAAA,CAAAuH,MAAA;YAAA;cAAAE,UAAA,CAAA9N,IAAA;cAAA;YAAA;cAAA8N,UAAA,CAAA9N,IAAA;cAAA;YAAA;cAAA8N,UAAA,CAAA/N,IAAA;cAAA+N,UAAA,CAAAxH,EAAA,GAAAwH,UAAA;cAAAJ,WAAA,CAAAvH,CAAA,CAAA2H,UAAA,CAAAxH,EAAA;YAAA;cAAAwH,UAAA,CAAA/N,IAAA;cAAA2N,WAAA,CAAAtH,CAAA;cAAA,OAAA0H,UAAA,CAAAvH,MAAA;YAAA;cAEArJ,IAAA,CAAAuM,eAAA,GAAA7K,WAAA;cAEA1B,IAAA,CAAA8B,iBAAA,GAAAwO,OAAA,CAAAxO,iBAAA;cACA9B,IAAA,CAAA+B,gBAAA,GAAAuO,OAAA,CAAAvO,gBAAA;cACA/B,IAAA,CAAAgC,iBAAA,GAAAsO,OAAA,CAAAtO,iBAAA;cACAhC,IAAA,CAAAiC,aAAA,GAAAqO,OAAA,CAAArO,aAAA;cAAA,MAEAjC,IAAA,CAAAuK,EAAA;gBAAAqG,UAAA,CAAA9N,IAAA;gBAAA;cAAA;cAAA8N,UAAA,CAAA/N,IAAA;cAEAyN,OAAA,CAAAvQ,UAAA;cAAA6Q,UAAA,CAAA9N,IAAA;cAAA,OACA,IAAAmO,wBAAA,EAAAjR,IAAA;YAAA;cACAsQ,OAAA,CAAAvQ,UAAA;cACAuQ,OAAA,CAAAF,UAAA;cACA;cACA;cAAAQ,UAAA,CAAA9N,IAAA;cAAA;YAAA;cAAA8N,UAAA,CAAA/N,IAAA;cAAA+N,UAAA,CAAAM,EAAA,GAAAN,UAAA;cAEAN,OAAA,CAAAvQ,UAAA;YAAA;YAAA;cAAA,OAAA6Q,UAAA,CAAA3N,IAAA;UAAA;QAAA,GAAAsN,QAAA;MAAA;IAGA;EACA;AACA", "ignoreList": []}]}