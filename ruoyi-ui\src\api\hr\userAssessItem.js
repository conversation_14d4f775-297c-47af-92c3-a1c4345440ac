import request from '@/utils/request'

// 查询试用期考核明细列表
export function listUserAssessItem(query) {
  return request({
    url: '/hr/userAssessItem/list',
    method: 'get',
    params: query
  })
}

// 查询试用期考核明细详细
export function getUserAssessItem(id) {
  return request({
    url: '/hr/userAssessItem/' + id,
    method: 'get'
  })
}

// 新增试用期考核明细
export function addUserAssessItem(data) {
  return request({
    url: '/hr/userAssessItem',
    method: 'post',
    data: data
  })
}

// 修改试用期考核明细
export function updateUserAssessItem(data) {
  return request({
    url: '/hr/userAssessItem',
    method: 'put',
    data: data
  })
}

// 删除试用期考核明细
export function delUserAssessItem(id) {
  return request({
    url: '/hr/userAssessItem/' + id,
    method: 'delete'
  })
}

// 导出试用期考核明细
export function exportUserAssessItem(data) {
  return request({
    url: '/hr/userAssessItem/export',
    method: 'post',
    data,
  })
}

export function allUserAssessItem(query) {
  return request({
    url: '/hr/userAssessItem/all',
    method: 'get',
    params: query
  })
}
