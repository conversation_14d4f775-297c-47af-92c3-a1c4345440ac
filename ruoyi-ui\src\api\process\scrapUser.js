import request from '@/utils/request'

// 查询报废申请列表
export function listScrapUser(query) {
  return request({
    url: '/process/scrapUser/list',
    method: 'get',
    params: query
  })
}
// 查询报废申请审批列表
export function listAuditScrapUser(query) {
  return request({
    url: '/process/scrapUser/audit',
    method: 'get',
    params: query
  })
}
// 查询请假申请列表
export function logScrapUser(query) {
  return request({
    url: '/process/scrapUser/log',
    method: 'get',
    params: query
  })
}

// 查询请假申请详情
export function getScrapUser(id) {
  return request({
    url: '/process/scrapUser/' + id,
    method: 'get'
  })
}

export function chooseScrapUser(query) {
  return request({
    url: '/process/scrapUser/choose',
    method: 'get',
    params: query
  })
}

// 新增报废申请
export function addScrapUser(data) {
  return request({
    url: '/process/scrapUser',
    method: 'post',
    data: data
  })
}

// 报废审批
export function auditScrapUser(data) {
  return request({
    url: '/process/scrapUser/audit',
    method: 'post',
    data: data
  })
}
export function editScrapUser(data) {
  return request({
    url: '/process/scrapUser/edit',
    method: 'post',
    data: data
  })
}
export function submitAudit(data) {
  return request({
    url: '/process/scrapUser/submitAudit',
    method: 'put',
    data: data
  })
}
export function cancelAudit(data) {
  return request({
    url: '/process/scrapUser/cancelAudit',
    method: 'put',
    data: data
  })
}

export function pigeonholeScrapUser(data) {
  return request({
    url: '/process/scrapUser/pigeonhole',
    method: 'post',
    data: data
  })
}
export function editPrintNumScrapUser(data) {
  return request({
    url: '/process/scrapUser/editPrintNum',
    method: 'post',
    data: data
  })
}
