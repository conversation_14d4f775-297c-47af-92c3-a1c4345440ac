import request from '@/utils/request'

// 查询BOM变更单身档列表
export function listBomChange(query) {
  return request({
    url: '/order/bomChange/list',
    method: 'get',
    params: query
  })
}

// 查询BOM变更单身档详细
export function getBomChange(id) {
  return request({
    url: '/order/bomChange/' + id,
    method: 'get'
  })
}

// 新增BOM变更单身档
export function addBomChange(data) {
  return request({
    url: '/order/bomChange',
    method: 'post',
    data: data
  })
}

// 修改BOM变更单身档
export function updateBomChange(data) {
  return request({
    url: '/order/bomChange',
    method: 'put',
    data: data
  })
}

// 删除BOM变更单身档
export function delBomChange(id) {
  return request({
    url: '/order/bomChange/' + id,
    method: 'delete'
  })
}

// 导出BOM变更单身档
export function exportBomChange(query) {
  return request({
    url: '/order/bomChange/export',
    method: 'get',
    params: query
  })
}