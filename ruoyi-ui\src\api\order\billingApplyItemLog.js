import request from '@/utils/request'

// 查询开票记录明细列表
export function listBillingApplyItemLog(query) {
  return request({
    url: '/order/billingApplyItemLog/list',
    method: 'get',
    params: query
  })
}

// 查询开票记录明细详细
export function getBillingApplyItemLog(id) {
  return request({
    url: '/order/billingApplyItemLog/' + id,
    method: 'get'
  })
}

// 新增开票记录明细
export function addBillingApplyItemLog(data) {
  return request({
    url: '/order/billingApplyItemLog',
    method: 'post',
    data: data
  })
}

// 修改开票记录明细
export function updateBillingApplyItemLog(data) {
  return request({
    url: '/order/billingApplyItemLog',
    method: 'put',
    data: data
  })
}

// 删除开票记录明细
export function delBillingApplyItemLog(id) {
  return request({
    url: '/order/billingApplyItemLog/' + id,
    method: 'delete'
  })
}

// 导出开票记录明细
export function exportBillingApplyItemLog(query) {
  return request({
    url: '/order/billingApplyItemLog/export',
    method: 'get',
    params: query
  })
}

export function allBillingApplyItemLog(query) {
  return request({
    url: '/order/billingApplyItemLog/all',
    method: 'get',
    params: query
  })
}
