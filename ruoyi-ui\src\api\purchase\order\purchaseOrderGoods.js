import request from "@/utils/request";

export function allPurchaseOrderGoods(data){
  return request({
    url: 'purchase/orderGoods/all',
    method: 'post',
    data,
  })
}

export function allPurchaseOrderGoodsErpData(data){
  return request({
    url: 'purchase/orderGoods/allOrderErpData',
    method: 'post',
    data,
  })
}
export function listPurchaseOrderGoods(query) {
  return request({
    url: '/purchase/orderGoods/list',
    method: 'get',
    params: query
  })
}

export function listPurchaseOrderGoodsStatistics(query) {
  return request({
    url: '/purchase/orderGoods/goodsStatistics',
    method: 'get',
    params: query
  })
}

export function listPurchaseOrderGoodsStatisticsDataList(query) {
  return request({
    url: '/purchase/orderGoods/goodsStatisticsDataList',
    method: 'get',
    params: query
  })
}

export function listPurchaseOrderGoodsByOrderId(query) {
  return request({
    url: '/purchase/orderGoods/queryOrderGoodsStatisticsById',
    method: 'get',
    params: query
  })
}


export function listPurchaseOrderGoodsDetails(query) {
  return request({
    url: '/purchase/orderGoods/queryOrderGoodsStatisticsDetails',
    method: 'get',
    params: query
  })
}

export function listPurchaseReconciliaStatistics(query) {
  return request({
    url: '/purchase/orderGoods/reconciliaStatistics',
    method: 'get',
    params: query
  })
}

//降价统计
export function listPurchasePriceReductionStatistics(query) {
  return request({
    url: '/purchase/orderGoods/priceReductionStatistics',
    method: 'get',
    params: query
  })
}

//降价统计
export function listPurchasePriceReductionStatisticsDataList(query) {
  return request({
    url: '/purchase/orderGoods/priceReductionStatisticsDataList',
    method: 'get',
    params: query
  })
}

export function getPurchaseOrderGoods(id) {
  return request({
    url: '/purchase/orderGoods/' + id,
    method: 'get'
  })
}


// 导出订单商品数据
export function exportPurchaseOrderGodosData(query) {
  return request({
    url: '/purchase/orderGoods/exportPurchaseOrderGodosData',
    method: 'get',
    params: query
  })
}


// 导出对账商品数据
export function exportPurchaseReconciliaData(query) {
  return request({
    url: '/purchase/orderGoods/exportPurchaseReconciliaData',
    method: 'get',
    params: query
  })
}

// 导出降价商品数据
export function exportPurchasePriceReductionData(query) {
  return request({
    url: '/purchase/orderGoods/exportPurchasePriceReductionData',
    method: 'get',
    params: query
  })
}


export function goodsStatisticsBySupplier(query) {
  return request({
    url: '/purchase/orderGoods/goodsStatisticsBySupplier',
    method: 'get',
    params: query
  })
}

export function exportPurchaseOrderGodosDataBySupplier(query) {
  return request({
    url: '/purchase/orderGoods/exportPurchaseOrderGodosDataBySupplier',
    method: 'get',
    params: query
  })
}

export function purchasePrintLable(data) {
  return request({
    url: '/purchase/orderGoods/printLable',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

export function printLableDetail(data) {
  return request({
    url: '/purchase/orderGoods/printLableDetail',
    method: 'post',
    data,
    responseType: 'blob'
  })
}


export function goodsDetailList(query) {
  return request({
    url: '/purchase/delivery/detailList',
    method: 'get',
    params: query
  })
}
