import request from '@/utils/request'

// 查询半成品检验记录-详情列表
export function listBcpRecordDetail(query) {
  return request({
    url: '/qc/bcpRecordDetail/list',
    method: 'get',
    params: query
  })
}

// 查询半成品检验记录-详情详细
export function getBcpRecordDetail(id) {
  return request({
    url: '/qc/bcpRecordDetail/' + id,
    method: 'get'
  })
}

// 新增半成品检验记录-详情
export function addBcpRecordDetail(data) {
  return request({
    url: '/qc/bcpRecordDetail',
    method: 'post',
    data: data
  })
}

// 修改半成品检验记录-详情
export function updateBcpRecordDetail(data) {
  return request({
    url: '/qc/bcpRecordDetail',
    method: 'put',
    data: data
  })
}

// 删除半成品检验记录-详情
export function delBcpRecordDetail(id) {
  return request({
    url: '/qc/bcpRecordDetail/' + id,
    method: 'delete'
  })
}

// 导出半成品检验记录-详情
export function exportBcpRecordDetail(query) {
  return request({
    url: '/qc/bcpRecordDetail/export',
    method: 'get',
    params: query
  })
}

export function bcpRecordDetailAll(query) {
  return request({
    url: '/qc/bcpRecordDetail/all',
    method: 'get',
    params: query
  })
}
