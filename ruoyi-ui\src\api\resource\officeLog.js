import request from '@/utils/request'

// 查询库存记录列表
export function listOfficeLog(query) {
  return request({
    url: '/resource/officeLog/list',
    method: 'get',
    params: query
  })
}

// 查询库存记录详细
export function getOfficeLog(id) {
  return request({
    url: '/resource/officeLog/' + id,
    method: 'get'
  })
}

// 新增库存记录
export function addOfficeLog(data) {
  return request({
    url: '/resource/officeLog',
    method: 'post',
    data: data
  })
}

// 修改库存记录
export function updateOfficeLog(data) {
  return request({
    url: '/resource/officeLog',
    method: 'put',
    data: data
  })
}

// 删除库存记录
export function delOfficeLog(id) {
  return request({
    url: '/resource/officeLog/' + id,
    method: 'delete'
  })
}

// 导出库存记录
export function exportOfficeLog(query) {
  return request({
    url: '/resource/officeLog/export',
    method: 'get',
    params: query
  })
}