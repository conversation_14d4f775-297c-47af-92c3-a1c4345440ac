import request from '@/utils/request'

// 查询成品BOM列表
export function listOrderErpBom(query) {
  return request({
    url: '/order/orderErpBom/list',
    method: 'get',
    params: query
  })
}

// 查询成品BOM列表
export function listMaterialErpBom(query) {
  return request({
    url: '/order/orderErpBom/materialList',
    method: 'get',
    params: query
  })
}

// 查询成品BOM详细
export function getOrderErpBom(id) {
  return request({
    url: '/order/orderErpBom/' + id,
    method: 'get'
  })
}

// 新增成品BOM
export function addOrderErpBom(data) {
  return request({
    url: '/order/orderErpBom',
    method: 'post',
    data: data
  })
}

// 修改成品BOM
export function updateOrderErpBom(data) {
  return request({
    url: '/order/orderErpBom',
    method: 'put',
    data: data
  })
}

// 删除成品BOM
export function delOrderErpBom(id) {
  return request({
    url: '/order/orderErpBom/' + id,
    method: 'delete'
  })
}

// 导出成品BOM
export function exportOrderErpBom(query) {
  return request({
    url: '/order/orderErpBom/export',
    method: 'get',
    params: query
  })
}


//导入包材价格基础信息
export function importPackageMaterialPrice(id) {
  return request({
    url: '/order/orderErpBom/importPackageMaterialPriceInfo',
    method: 'get'
  })
}
