import request from '@/utils/request'

// 查询客户跟进(小程序)列表
export function listMiniprogramFollow(query) {
  return request({
    url: '/miniprogram/miniprogramFollow/list',
    method: 'get',
    params: query
  })
}

// 查询推荐商品
export function queryRecommendedSamplesGoods(query) {
  return request({
    url: '/miniprogram/miniprogramFollow/queryRecommendedSamplesGoods',
    method: 'get',
    params: query
  })
}

// 查询推荐商品
export function queryExhibitsGoodsInfo(query) {
  return request({
    url: '/miniprogram/miniprogramFollow/queryExhibitsGoodsInfo',
    method: 'get',
    params: query
  })
}

// 查询客户跟进(小程序)详细
export function getMiniprogramFollow(id) {
  return request({
    url: '/miniprogram/miniprogramFollow/' + id,
    method: 'get'
  })
}

// 新增客户跟进(小程序)
export function addMiniprogramFollow(data) {
  return request({
    url: '/miniprogram/miniprogramFollow',
    method: 'post',
    data: data
  })
}

// 修改客户跟进(小程序)
export function updateMiniprogramFollow(data) {
  return request({
    url: '/miniprogram/miniprogramFollow',
    method: 'put',
    data: data
  })
}

// 删除客户跟进(小程序)
export function delMiniprogramFollow(id) {
  return request({
    url: '/miniprogram/miniprogramFollow/' + id,
    method: 'delete'
  })
}

// 导出客户跟进(小程序)
export function exportMiniprogramFollow(query) {
  return request({
    url: '/miniprogram/miniprogramFollow/export',
    method: 'get',
    params: query
  })
}
