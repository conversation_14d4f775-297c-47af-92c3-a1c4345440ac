import request from '@/utils/request'

// 查询检测项目列表
export function listDetectItem(query) {
  return request({
    url: '/resource/detectItem/list',
    method: 'get',
    params: query
  })
}

// 查询检测项目详细
export function getDetectItem(id) {
  return request({
    url: '/resource/detectItem/' + id,
    method: 'get'
  })
}

// 新增检测项目
export function addDetectItem(data) {
  return request({
    url: '/resource/detectItem',
    method: 'post',
    data: data
  })
}

// 修改检测项目
export function updateDetectItem(data) {
  return request({
    url: '/resource/detectItem',
    method: 'put',
    data: data
  })
}

// 删除检测项目
export function delDetectItem(id) {
  return request({
    url: '/resource/detectItem/' + id,
    method: 'delete'
  })
}

// 导出检测项目
export function exportDetectItem(query) {
  return request({
    url: '/resource/detectItem/export',
    method: 'get',
    params: query
  })
}

export function allDetectItem(query) {
  return request({
    url: '/resource/detectItem/all',
    method: 'get',
    params: query
  })
}
