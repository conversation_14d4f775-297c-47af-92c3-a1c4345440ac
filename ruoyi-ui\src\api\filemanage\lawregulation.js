import request from '@/utils/request'

// 查询法律法规列表
export function listLawregulation(query) {
  return request({
    url: '/filemanage/lawregulation/list',
    method: 'get',
    params: query
  })
}

// 查询法律法规详细
export function getLawregulation(id) {
  return request({
    url: '/filemanage/lawregulation/' + id,
    method: 'get'
  })
}

// 新增法律法规
export function addLawregulation(data) {
  return request({
    url: '/filemanage/lawregulation',
    method: 'post',
    data: data
  })
}

// 修改法律法规
export function updateLawregulation(data) {
  return request({
    url: '/filemanage/lawregulation',
    method: 'put',
    data: data
  })
}

// 删除法律法规
export function delLawregulation(id) {
  return request({
    url: '/filemanage/lawregulation/' + id,
    method: 'delete'
  })
}


export function refreshLawregulation(data) {
  return request({
    url: '/filemanage/lawregulation/refreshLawregulation',
    method: 'post',
    data: data
  })
}

// 导出法律法规
export function exportLawregulation(query) {
  return request({
    url: '/filemanage/lawregulation/export',
    method: 'get',
    params: query
  })
}

export function replaceUpload(data) {
  return request({
    url: '/filemanage/lawregulation/replaceUpload',
    method: 'post',
    data: data
  })
}
