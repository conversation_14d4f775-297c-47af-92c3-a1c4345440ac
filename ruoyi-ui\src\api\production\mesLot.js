import request from '@/utils/request'

// 查询mes生产批列表
export function listMesLot(query) {
  return request({
    url: '/production/mesLot/list',
    method: 'get',
    params: query
  })
}

export function gbListMesLot(query) {
  return request({
    url: '/production/mesLot/gbList',
    method: 'get',
    params: query
  })
}

// 查询mes生产批详细
export function getMesLot(id) {
  return request({
    url: '/production/mesLot/' + id,
    method: 'get'
  })
}

export function getMaterialLogList(id) {
  return request({
    url: '/production/mesLot/materialLogList/' + id,
    method: 'get'
  })
}

// 新增mes生产批
export function addMesLot(data) {
  return request({
    url: '/production/mesLot',
    method: 'post',
    data: data
  })
}

// 修改mes生产批
export function updateMesLot(data) {
  return request({
    url: '/production/mesLot',
    method: 'put',
    data: data
  })
}

// 删除mes生产批
export function delMesLot(id) {
  return request({
    url: '/production/mesLot/' + id,
    method: 'delete'
  })
}

// 导出mes生产批
export function exportMesLot(query) {
  return request({
    url: '/production/mesLot/export',
    method: 'get',
    params: query
  })
}

export function exportMesLotProductionLog(data) {
  return request({
    url: '/production/mesLot/exportProductionLog',
    method: 'post',
    data,
  })
}
