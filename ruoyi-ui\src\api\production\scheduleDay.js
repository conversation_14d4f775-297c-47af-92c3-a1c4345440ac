import request from '@/utils/request'

export function toBeAllScheduleDay(query) {
  return request({
    url: '/production/scheduleDay/toBeAll',
    method: 'get',
    params: query
  })
}

export function liteAllScheduleDay(query) {
  return request({
    url: '/production/scheduleDay/liteAll',
    method: 'get',
    params: query
  })
}

// 查询人员排班日计划列表
export function listScheduleDay(query) {
  return request({
    url: '/production/scheduleDay/list',
    method: 'get',
    params: query
  })
}

export function hoursListScheduleDay(query) {
  return request({
    url: '/production/scheduleDay/hoursList',
    method: 'get',
    params: query
  })
}

// 查询人员排班日计划详细
export function getScheduleDay(id) {
  return request({
    url: '/production/scheduleDay/' + id,
    method: 'get'
  })
}

export function getScheduleDayByParams(query) {
  return request({
    url: '/production/scheduleDay/scheduleDayByParams',
    method: 'get',
    params: query,
  })
}

// 新增人员排班日计划
export function addScheduleDay(data) {
  return request({
    url: '/production/scheduleDay',
    method: 'post',
    data: data
  })
}

// 修改人员排班日计划
export function updateScheduleDay(data) {
  return request({
    url: '/production/scheduleDay',
    method: 'put',
    data: data
  })
}

export function updateScheduleDayWithBeforeToday(data) {
  return request({
    url: '/production/scheduleDay/editWithBeforeToday',
    method: 'put',
    data: data
  })
}

export function updateScheduleDayGroup(data) {
  return request({
    url: '/production/scheduleDay/editGroup',
    method: 'put',
    data: data
  })
}

export function updateScheduleDayAndWorkTypeMaterialArray(data) {
  return request({
    url: '/production/scheduleDay/editScheduleDayAndWorkTypeMaterialArray',
    method: 'put',
    data: data
  })
}

export function updateScheduleDayAndWeightActualJson(data) {
  return request({
    url: '/production/scheduleDay/editScheduleDayAndWeightActualJson',
    method: 'put',
    data: data
  })
}

export function savePlanHours(data) {
  return request({
    url: '/production/scheduleDay/savePlanHours',
    method: 'put',
    data: data
  })
}

export function saveActualHours(data) {
  return request({
    url: '/production/scheduleDay/saveActualHours',
    method: 'put',
    data: data
  })
}

// 删除人员排班日计划
export function delScheduleDay(id) {
  return request({
    url: '/production/scheduleDay/' + id,
    method: 'delete'
  })
}

export function exportPlanHours(id) {
  return request({
    url: '/production/scheduleDay/exportPlanHours/' + id,
    method: 'get',
  })
}

export function exportActualList(query) {
  return request({
    url: '/production/scheduleDay/exportActualList',
    method: 'get',
    params: query
  })
}

export function allScheduleDay(query) {
  return request({
    url: '/production/scheduleDay/all',
    method: 'get',
    params: query
  })
}

export function leaveUserDateList(query) {
  return request({
    url: '/production/scheduleDay/leaveUserDateList',
    method: 'get',
    params: query
  })
}

//提交审核
export function submitAudit(data) {
  return request({
    url: '/production/scheduleDay/submitAudit',
    method: 'put',
    data: data
  })
}

//撤销申请
export function cancelAudit(data) {
  return request({
    url: '/production/scheduleDay/cancelAudit',
    method: 'put',
    data: data
  })
}

export function submitChangeAudit(data) {
  return request({
    url: '/production/scheduleDay/submitChangeAudit',
    method: 'put',
    data: data
  })
}

export function cancelChangeAudit(data) {
  return request({
    url: '/production/scheduleDay/cancelChangeAudit',
    method: 'put',
    data: data
  })
}

export function personHours(query) {
  return request({
    url: '/production/scheduleDay/personHours',
    method: 'get',
    params: query
  })
}

export function exportScheduleDayProduction(data) {
  return request({
    url: '/production/scheduleDay/exportScheduleDayProduction',
    method: 'post',
    data,
  })
}

export function listStatsScheduleDay(query) {
  return request({
    url: '/production/scheduleDay/listStats',
    method: 'get',
    params: query
  })
}

export function microbeInspectionArrayScheduleDay(query) {
  return request({
    url: '/production/scheduleDay/microbeInspectionArray',
    method: 'get',
    params: query
  })
}

export function exportProductionTable(data) {
  return request({
    url: '/production/scheduleDay/exportProductionTable',
    method: 'post',
    data,
  })
}

export function exportMakeUpTable(data) {
  return request({
    url: '/production/scheduleDay/exportMakeUpTable',
    method: 'post',
    data,
  })
}

export function exportMaterialTable(data) {
  return request({
    url: '/production/scheduleDay/exportMaterialTable',
    method: 'post',
    data,
  })
}

export function exportWeightTable(data) {
  return request({
    url: '/production/scheduleDay/exportWeightTable',
    method: 'post',
    data,
  })
}

export function exportYcTable(id) {
  return request({
    url: '/production/scheduleDay/exportYcTable/' + id,
    method: 'get',
  })
}

export function saveQcMicrobeInspectionArray(data) {
  return request({
    url: '/production/scheduleDay/saveQcMicrobeInspectionArray',
    method: 'put',
    data: data
  })
}

export function exportMicrobeInspection(id) {
  return request({
    url: '/production/scheduleDay/exportMicrobeInspection/' + id,
    method: 'get',
  })
}

export function asyncOtherAndManageUserNums() {
  return request({
    url: '/production/scheduleDay/asyncOtherAndManageUserNums',
    method: 'get',
  })
}

export function exportScheduleDayDiffHours(query) {
  return request({
    url: '/production/scheduleDay/exportDiffHours',
    method: 'get',
    params: query,
  })
}

export function exportWageHours(id) {
  return request({
    url: '/production/scheduleDay/exportWageHours/' + id,
    method: 'get',
  })
}

export function productionDayWeightArray(query) {
  return request({
    url: '/production/scheduleDay/productionDayWeightArray',
    method: 'get',
    params: query
  })
}

export function productionDayWorkTypeMaterialArray(query) {
  return request({
    url: '/production/scheduleDay/productionDayWorkTypeMaterialArray',
    method: 'get',
    params: query
  })
}
