import request from '@/utils/request'

// 查询生产人员技能评分列表
export function listUserScore(query) {
  return request({
    url: '/production/userScore/list',
    method: 'get',
    params: query
  })
}

// 查询生产人员技能评分详细
export function getUserScore(id) {
  return request({
    url: '/production/userScore/' + id,
    method: 'get'
  })
}

// 新增生产人员技能评分
export function addUserScore(data) {
  return request({
    url: '/production/userScore',
    method: 'post',
    data: data
  })
}

// 修改生产人员技能评分
export function updateUserScore(data) {
  return request({
    url: '/production/userScore',
    method: 'put',
    data: data
  })
}

// 删除生产人员技能评分
export function delUserScore(id) {
  return request({
    url: '/production/userScore/' + id,
    method: 'delete'
  })
}

// 导出生产人员技能评分
export function exportUserScore(query) {
  return request({
    url: '/production/userScore/export',
    method: 'get',
    params: query
  })
}

export function productionUserList(query) {
  return request({
    url: '/production/userScore/productionUserList',
    method: 'get',
    params: query
  })
}
