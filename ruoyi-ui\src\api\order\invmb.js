import request from '@/utils/request'

// 查询erp品号信息列表
export function listInvmb(query) {
  return request({
    url: '/order/invmb/list',
    method: 'get',
    params: query
  })
}

// 查询erp品号信息详细
export function getInvmb(id) {
  return request({
    url: '/order/invmb/' + id,
    method: 'get'
  })
}

// 新增erp品号信息
export function addInvmb(data) {
  return request({
    url: '/order/invmb',
    method: 'post',
    data: data
  })
}

// 修改erp品号信息
export function updateInvmb(data) {
  return request({
    url: '/order/invmb',
    method: 'put',
    data: data
  })
}

// 删除erp品号信息
export function delInvmb(id) {
  return request({
    url: '/order/invmb/' + id,
    method: 'delete'
  })
}

// 导出erp品号信息
export function exportInvmb(query) {
  return request({
    url: '/order/invmb/export',
    method: 'get',
    params: query
  })
}