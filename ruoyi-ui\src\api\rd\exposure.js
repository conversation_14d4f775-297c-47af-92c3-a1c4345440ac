import request from '@/utils/request'

// 查询化妆品暴露类型列表
export function listExposure(query) {
  return request({
    url: '/rd/exposure/list',
    method: 'get',
    params: query
  })
}

// 查询化妆品暴露类型详细
export function getExposure(id) {
  return request({
    url: '/rd/exposure/' + id,
    method: 'get'
  })
}

// 新增化妆品暴露类型
export function addExposure(data) {
  return request({
    url: '/rd/exposure',
    method: 'post',
    data: data
  })
}

// 修改化妆品暴露类型
export function updateExposure(data) {
  return request({
    url: '/rd/exposure',
    method: 'put',
    data: data
  })
}

// 删除化妆品暴露类型
export function delExposure(id) {
  return request({
    url: '/rd/exposure/' + id,
    method: 'delete'
  })
}

// 导出化妆品暴露类型
export function exportExposure(query) {
  return request({
    url: '/rd/exposure/export',
    method: 'get',
    params: query
  })
}

export function allExposure(query) {
  return request({
    url: '/rd/exposure/all',
    method: 'get',
    params: query
  })
}
