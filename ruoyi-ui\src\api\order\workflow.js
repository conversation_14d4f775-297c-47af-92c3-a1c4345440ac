import request from '@/utils/request'

// 查询订单流程列表
export function allWorkflow(query) {
  return request({
    url: '/order/workflow/all',
    method: 'get',
    params: query
  })
}

// 查询订单流程详细
export function getWorkflow(id) {
  return request({
    url: '/order/workflow/' + id,
    method: 'get'
  })
}

// 导出订单流程
export function exportWorkflow(query) {
  return request({
    url: '/order/workflow/export',
    method: 'get',
    params: query
  })
}
