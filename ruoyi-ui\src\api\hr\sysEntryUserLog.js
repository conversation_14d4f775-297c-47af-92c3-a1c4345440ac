import request from '@/utils/request'

// 查询应聘用户评价记录列表
export function listSysEntryUserLog(query) {
  return request({
    url: '/hr/sysEntryUserLog/list',
    method: 'get',
    params: query
  })
}

export function allSysEntryUserLog(query) {
  return request({
    url: '/hr/sysEntryUserLog/all',
    method: 'get',
    params: query
  })
}

// 查询应聘用户评价记录详细
export function getSysEntryUserLog(id) {
  return request({
    url: '/hr/sysEntryUserLog/' + id,
    method: 'get'
  })
}

// 新增应聘用户评价记录
export function addSysEntryUserLog(data) {
  return request({
    url: '/hr/sysEntryUserLog',
    method: 'post',
    data: data
  })
}

// 修改应聘用户评价记录
export function updateSysEntryUserLog(data) {
  return request({
    url: '/hr/sysEntryUserLog',
    method: 'put',
    data: data
  })
}

// 删除应聘用户评价记录
export function delSysEntryUserLog(id) {
  return request({
    url: '/hr/sysEntryUserLog/' + id,
    method: 'delete'
  })
}

// 导出应聘用户评价记录
export function exportSysEntryUserLog(query) {
  return request({
    url: '/hr/sysEntryUserLog/export',
    method: 'get',
    params: query
  })
}
