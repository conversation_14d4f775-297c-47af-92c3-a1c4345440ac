import request from '@/utils/request'

// 查询方案人员打卡记录列表
export function listCaseUserDk(query) {
  return request({
    url: '/gx/caseUserDk/list',
    method: 'get',
    params: query
  })
}

// 查询方案人员打卡记录详细
export function getCaseUserDk(id) {
  return request({
    url: '/gx/caseUserDk/' + id,
    method: 'get'
  })
}

// 新增方案人员打卡记录
export function addCaseUserDk(data) {
  return request({
    url: '/gx/caseUserDk',
    method: 'post',
    data: data
  })
}

// 修改方案人员打卡记录
export function updateCaseUserDk(data) {
  return request({
    url: '/gx/caseUserDk',
    method: 'put',
    data: data
  })
}

// 删除方案人员打卡记录
export function delCaseUserDk(id) {
  return request({
    url: '/gx/caseUserDk/' + id,
    method: 'delete'
  })
}

// 导出方案人员打卡记录
export function exportCaseUserDk(query) {
  return request({
    url: '/gx/caseUserDk/export',
    method: 'get',
    params: query
  })
}

export function allCaseUserDk(query) {
  return request({
    url: '/gx/caseUserDk/all',
    method: 'get',
    params: query
  })
}
