{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\userTabs.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\userTabs.vue", "mtime": 1753954679645}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["userTabs.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "userTabs.vue", "sourceRoot": "src/views/production/dayHours", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-tabs v-model=\"currentTab\" >\r\n      <el-tab-pane v-for=\"tab in tabOptions\" :key=\"tab.value\" :name=\"tab.value\" :label=\"tab.label\" >\r\n\r\n        <DayHoursUserTable\r\n          :day-hours=\"dayHours\"\r\n          :mes-hours-list=\"mesHoursList\"\r\n          :user-array=\"dayUserList.filter(i=>i.userType === tab.value)\"\r\n          :sum-hours=\"getSumHours(tab.value)\"\r\n          @sailingsChange=\"sailingsChange\"\r\n          @computeItemData=\"computeItemData\"\r\n        />\r\n\r\n        <template v-if=\"tab.value === 'user'\" >\r\n          <DayHoursBaseTable\r\n            title=\"称量\"\r\n            :attendance-log-list=\"attendanceLogList\"\r\n            :rest-list=\"restList\"\r\n            :user-list=\"userList\"\r\n            :user-array=\"weightMinutesList\"\r\n            :day-hours=\"dayHours\"\r\n            :sailings=\"sailings\"\r\n            @computeItemData=\"computeItemData('weight')\"\r\n          />\r\n\r\n          <DayHoursBaseTable\r\n            title=\"间接\"\r\n            :attendance-log-list=\"attendanceLogList\"\r\n            :rest-list=\"restList\"\r\n            :user-list=\"userList\"\r\n            :user-array=\"otherMinutesList\"\r\n            :day-hours=\"dayHours\"\r\n            :sailings=\"sailings\"\r\n            @computeItemData=\"computeItemData('other')\"\r\n          />\r\n\r\n          <DayHoursBaseTable\r\n            title=\"管理\"\r\n            :attendance-log-list=\"attendanceLogList\"\r\n            :rest-list=\"restList\"\r\n            :user-list=\"userList\"\r\n            :user-array=\"manageMinutesList\"\r\n            :day-hours=\"dayHours\"\r\n            :sailings=\"sailings\"\r\n            @computeItemData=\"computeItemData('manage')\"\r\n          />\r\n\r\n          <DayHoursBaseTable\r\n            title=\"质检\"\r\n            :attendance-log-list=\"attendanceLogList\"\r\n            :rest-list=\"restList\"\r\n            :user-list=\"userList\"\r\n            :user-array=\"qcMinutesList\"\r\n            :day-hours=\"dayHours\"\r\n            :sailings=\"sailings\"\r\n            @computeItemData=\"computeItemData('qc')\"\r\n          />\r\n\r\n        </template>\r\n\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n  </div>\r\n</template>\r\n<script >\r\nimport DayHoursUserTable from \"@/views/production/dayHours/userTable.vue\";\r\nimport DayHoursBaseTable from \"@/views/production/dayHours/baseTable.vue\";\r\n\r\nexport default {\r\n  name: 'dayHoursUserTabs',\r\n  components: {DayHoursBaseTable, DayHoursUserTable},\r\n  props: {\r\n    sailings: {\r\n      type: String,\r\n      required: true,\r\n    },\r\n    dayUserList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    weightMinutesList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    otherMinutesList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    manageMinutesList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    qcMinutesList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    attendanceLogList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    mesHoursList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    dayHours: {\r\n      type: Object,\r\n      required: true,\r\n    },\r\n    userList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    restList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      currentTab: 'user',\r\n      tabOptions: [\r\n        {label: '正式工',value: 'user'},\r\n        {label: '劳务工',value: 'labor'},\r\n        {label: '包干工',value: 'outer'},\r\n      ],\r\n    }\r\n  },\r\n  async created() {\r\n  },\r\n  methods: {\r\n    sailingsChange(userCode) {\r\n      this.$emit('sailingsChange',userCode)\r\n    },\r\n    computeItemData(type) {\r\n      if(type) {\r\n        this.$emit('computeItemData',type)\r\n      } else {\r\n        this.$emit('computeItemData')\r\n      }\r\n    },\r\n    getSumHours(userType) {\r\n      if(userType === 'user') {\r\n        return this.dayHours.userHours\r\n      } else if(userType === 'labor') {\r\n        return this.dayHours.laborHours\r\n      } else if(userType === 'outer') {\r\n        return this.dayHours.outerHours\r\n      }\r\n    }\r\n  },\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n::v-deep .el-divider {\r\n  .el-divider__text {\r\n    font-size: 18px;\r\n    font-weight: 650;\r\n  }\r\n}\r\n</style>\r\n"]}]}