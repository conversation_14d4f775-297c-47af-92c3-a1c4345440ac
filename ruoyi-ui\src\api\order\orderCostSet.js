import request from '@/utils/request'

// 查询成本设置列表
export function listOrderCostSet(query) {
  return request({
    url: '/order/orderCostSet/list',
    method: 'get',
    params: query
  })
}

// 查询成本设置详细
export function getOrderCostSet(id) {
  return request({
    url: '/order/orderCostSet/' + id,
    method: 'get'
  })
}

// 新增成本设置
export function addOrderCostSet(data) {
  return request({
    url: '/order/orderCostSet',
    method: 'post',
    data: data
  })
}

// 修改成本设置
export function updateOrderCostSet(data) {
  return request({
    url: '/order/orderCostSet',
    method: 'put',
    data: data
  })
}

// 删除成本设置
export function delOrderCostSet(id) {
  return request({
    url: '/order/orderCostSet/' + id,
    method: 'delete'
  })
}

// 导出成本设置
export function exportOrderCostSet(query) {
  return request({
    url: '/order/orderCostSet/export',
    method: 'get',
    params: query
  })
}