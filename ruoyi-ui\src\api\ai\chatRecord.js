import request from '@/utils/request'

// 查询智能体列表
export function getAgentList() {
  return request({
    url: '/ai/chatRecord/agentList',
    method: 'get'
  })
}

// 查询聊天记录列表
export function getConversationList(query) {
  return request({
    url: '/ai/chatRecord/conversationList',
    method: 'get',
    params: query
  })
}

// 查询聊天记录详细
export function getChatRecord(agentId,sessionId) {
  return request({
    url: '/ai/chatRecord/' + agentId + '/' + sessionId,
    method: 'get'
  })
}
