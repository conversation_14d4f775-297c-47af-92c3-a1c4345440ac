import request from '@/utils/request'

// 查询供应商采购列表
export function listPurchase(query) {
  return request({
    url: '/supplier/purchase/list',
    method: 'get',
    params: query
  })
}

export function unDzListPurchase(query) {
  return request({
    url: '/supplier/purchase/unDzList',
    method: 'get',
    params: query
  })
}

// 查询供应商采购详细
export function getPurchase(id) {
  return request({
    url: '/supplier/purchase/' + id,
    method: 'get'
  })
}

// 新增供应商采购
export function addPurchase(data) {
  return request({
    url: '/supplier/purchase',
    method: 'post',
    data: data
  })
}

// 修改供应商采购
export function updatePurchase(data) {
  return request({
    url: '/supplier/purchase',
    method: 'put',
    data: data
  })
}

// 删除供应商采购
export function delPurchase(id) {
  return request({
    url: '/supplier/purchase/' + id,
    method: 'delete'
  })
}

// 导出供应商采购
export function exportPurchase(query) {
  return request({
    url: '/supplier/purchase/export',
    method: 'get',
    params: query
  })
}

export function rkOffice(data) {
  return request({
    url: '/supplier/purchase/rkOffice',
    method: 'put',
    data,
  })
}

export function allPurchase(query) {
  return request({
    url: '/supplier/purchase/all',
    method: 'get',
    params: query
  })
}

export function allRkPurchase(query) {
  return request({
    url: '/supplier/purchase/allRk',
    method: 'get',
    params: query
  })
}
