import request from '@/utils/request'

// 查询整包装申请单列表
export function listOrderErpPurtbWholePackage(query) {
  return request({
    url: '/order/orderErpPurtbWholePackage/list',
    method: 'get',
    params: query
  })
}

export function listAuditOrderPurtbWholePackage(query) {
  return request({
    url: '/order/orderErpPurtbWholePackage/audit',
    method: 'get',
    params: query
  })
}

// 查询整包装申请单详细
export function getOrderErpPurtbWholePackage(id) {
  return request({
    url: '/order/orderErpPurtbWholePackage/' + id,
    method: 'get'
  })
}

// 新增整包装申请单
export function addOrderErpPurtbWholePackage(data) {
  return request({
    url: '/order/orderErpPurtbWholePackage',
    method: 'post',
    data: data
  })
}

// 修改整包装申请单
export function updateOrderErpPurtbWholePackage(data) {
  return request({
    url: '/order/orderErpPurtbWholePackage',
    method: 'put',
    data: data
  })
}

// 删除整包装申请单
export function delOrderErpPurtbWholePackage(id) {
  return request({
    url: '/order/orderErpPurtbWholePackage/' + id,
    method: 'delete'
  })
}

// 导出整包装申请单
export function exportOrderErpPurtbWholePackage(query) {
  return request({
    url: '/order/orderErpPurtbWholePackage/export',
    method: 'get',
    params: query
  })
}

//获取待申请整包装数据
export function applyOrderErpPurtbWholePackage(query) {
  return request({
    url: '/order/orderErpPurtbWholePackage/applyList',
    method: 'get',
    params: query
  })
}


//查看详情 待申请整包装数据
export function applyOrderErpPurtbWholePackageItem(query) {
  return request({
    url: '/order/orderErpPurtbWholePackage/applyItemList',
    method: 'get',
    params: query
  })
}

//编辑 获取待申请整包装数据
export function applyOrderErpPurtbWholePackageEditItem(query) {
  return request({
    url: '/order/orderErpPurtbWholePackage/applyItemEditList',
    method: 'get',
    params: query
  })
}

//撤销审核
export function cancelAudit(data) {
  return request({
    url: '/order/orderErpPurtbWholePackage/cancelAudit',
    method: 'put',
    data: data
  })
}


export function revokeAudit(data) {
  return request({
    url: '/order/orderErpPurtbWholePackage/revokeAudit',
    method: 'put',
    data: data
  })
}


//撤销审核
export function editApplyTypeInfo(data) {
  return request({
    url: '/order/orderErpPurtbWholePackage/editApplyTypeInfo',
    method: 'post',
    data: data
  })
}

export function purchaseAllType(query) {
  return request({
    url: '/order/orderErpPurtbWholePackage/purchaseAllType',
    method: 'get',
    params: query
  })
}
