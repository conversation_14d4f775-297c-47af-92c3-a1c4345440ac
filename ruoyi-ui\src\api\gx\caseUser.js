import request from '@/utils/request'

// 查询功效测试方案参与人列表
export function listCaseUser(query) {
  return request({
    url: '/gx/caseUser/list',
    method: 'get',
    params: query
  })
}

export function includesSignAndDkListCaseUser(query) {
  return request({
    url: '/gx/caseUser/includesSignAndDkList',
    method: 'get',
    params: query
  })
}

// 查询功效测试方案参与人详细
export function getCaseUser(id) {
  return request({
    url: '/gx/caseUser/' + id,
    method: 'get'
  })
}

// 新增功效测试方案参与人
export function addCaseUser(data) {
  return request({
    url: '/gx/caseUser',
    method: 'post',
    data: data
  })
}

// 修改功效测试方案参与人
export function updateCaseUser(data) {
  return request({
    url: '/gx/caseUser',
    method: 'put',
    data: data
  })
}

// 删除功效测试方案参与人
export function delCaseUser(id) {
  return request({
    url: '/gx/caseUser/' + id,
    method: 'delete'
  })
}

// 导出功效测试方案参与人
export function exportCaseUserDkLog(caseId) {
  return request({
    url: '/gx/caseUser/exportCaseUserDkLog/' + caseId,
    method: 'get',
  })
}

export function passCaseUser(data) {
  return request({
    url: '/gx/caseUser/pass',
    method: 'put',
    data,
  })
}

export function rejectCaseUser(data) {
  return request({
    url: '/gx/caseUser/reject',
    method: 'put',
    data,
  })
}

export function quitCaseUser(data) {
  return request({
    url: '/gx/caseUser/quit',
    method: 'put',
    data,
  })
}

export function allCaseUser(query) {
  return request({
    url: '/gx/caseUser/all',
    method: 'get',
    params: query
  })
}
