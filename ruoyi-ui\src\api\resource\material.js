import request from '@/utils/request'

// 查询包材列表
export function listMaterial(query) {
  return request({
    url: '/resource/material/list',
    method: 'get',
    params: query
  })
}

export function listIncludeGoodsMaterial(query) {
  return request({
    url: '/resource/material/listIncludeGoods',
    method: 'get',
    params: query
  })
}

// 查询包材详细
export function getMaterial(id) {
  return request({
    url: '/resource/material/' + id,
    method: 'get'
  })
}

// 新增包材
export function addMaterial(data) {
  return request({
    url: '/resource/material',
    method: 'post',
    data: data
  })
}

// 修改包材
export function updateMaterial(data) {
  return request({
    url: '/resource/material',
    method: 'put',
    data: data
  })
}

// 删除包材
export function delMaterial(id) {
  return request({
    url: '/resource/material/' + id,
    method: 'delete'
  })
}

// 导出包材
export function exportMaterial(query) {
  return request({
    url: '/resource/material/export',
    method: 'get',
    params: query
  })
}

export function asyncMaterialGoodsList() {
  return request({
    url: '/resource/material/asyncGoodsList',
    method: 'get'
  })
}

export function allMaterial(query) {
  return request({
    url: '/resource/material/all',
    method: 'get',
    params: query
  })
}

export function getMaterialInfo(query) {
  return request({
    url: '/resource/material/info',
    method: 'get',
    params: query
  })
}
