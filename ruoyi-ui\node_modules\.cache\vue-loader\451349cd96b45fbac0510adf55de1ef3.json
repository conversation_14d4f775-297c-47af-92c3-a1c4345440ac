{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\sop\\makeUp\\save.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\sop\\makeUp\\save.vue", "mtime": 1753954679648}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBhZGRNYWtlVXAsIGdldE1ha2VVcCwgdXBkYXRlTWFrZVVwLGNvdW50TWFrZVVwQnlMYWJDb2RlIH0gZnJvbSAiQC9hcGkvc29wL21ha2VVcCI7DQppbXBvcnQgeyBnZXRQcm9qZWN0QnlObywgfSBmcm9tICJAL2FwaS9wcm9qZWN0L3Byb2plY3QiOw0KaW1wb3J0IHthbGxGb3JtdWxhLCBnZXRGb3JtdWxhfSBmcm9tICJAL2FwaS9zb2Z0d2FyZS9mb3JtdWxhIjsNCmltcG9ydCB7IGFsbE1hdGVyaWFsRm9ybXVsYSB9IGZyb20gIkAvYXBpL3NvZnR3YXJlL21hdGVyaWFsRm9ybXVsYSI7DQppbXBvcnQgeyBhbGxFcXVpcG1lbnQgfSBmcm9tICJAL2FwaS9wcm9kdWN0aW9uL2VxdWlwbWVudCI7DQppbXBvcnQgUHJvamVjdEJhc2VWaWV3IGZyb20gIkAvdmlld3MvcHJvamVjdC9wcm9qZWN0L2Jhc2UudnVlIjsNCmltcG9ydCBQcm9kdWN0aW9uUGcgZnJvbSAiQC92aWV3cy9zb3AvbWFrZVVwL3Byb2R1Y3Rpb25QZy52dWUiOw0KaW1wb3J0IFN0YWJpbGl0eUxpc3QgZnJvbSAiQC92aWV3cy9yZC9zdGFiaWxpdHkvbGlzdC52dWUiOw0KaW1wb3J0IEJjcFByb2R1Y3Rpdml0eUFycmF5IGZyb20gIkAvdmlld3Mvc29wL21ha2VVcC9iY3BQcm9kdWN0aXZpdHlBcnJheS52dWUiOw0KaW1wb3J0IE1ha2VVcEJTYXZlIGZyb20gIkAvdmlld3Mvc29wL21ha2VVcEIvc2F2ZS52dWUiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJtYWtlVXBTYXZlIiwNCiAgY29tcG9uZW50czogew0KICAgIE1ha2VVcEJTYXZlLA0KICAgIEJjcFByb2R1Y3Rpdml0eUFycmF5LA0KICAgIFN0YWJpbGl0eUxpc3QsDQogICAgUHJvZHVjdGlvblBnLA0KICAgIFByb2plY3RCYXNlVmlldw0KICB9LA0KICBwcm9wczogew0KICAgIHJlYWRvbmx5OiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UsDQogICAgfSwNCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBzdW1QZXJjZW50YWdlKCkgew0KICAgICAgbGV0IHN1bSA9IHRoaXMuJGJpZygwKQ0KICAgICAgZm9yIChjb25zdCBpdGVtIG9mIHRoaXMubWF0ZXJpYWxBcnJheSkgew0KICAgICAgICBpZihpdGVtLnBlcmNlbnRhZ2UpIHsNCiAgICAgICAgICBzdW0gPSB0aGlzLmFkZChzdW0saXRlbS5wZXJjZW50YWdlKQ0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gc3VtLnRvTnVtYmVyKCkNCiAgICB9DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgYnRuTG9hZGluZzogZmFsc2UsDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIHRleHRPcGVuOiBmYWxzZSwNCiAgICAgIGZ1bGxzY3JlZW5GbGFnOiBmYWxzZSwNCiAgICAgIHRpdGxlOiAnJywNCiAgICAgIGZvcm06IHt9LA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgcHJvamVjdE5vOiBbDQogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLG1lc3NhZ2U6ICfor7fovpPlhaXmraPnoa7nmoTpobnnm67nvJblj7cnfSwNCiAgICAgICAgXSwNCiAgICAgICAgbnVtczogWw0KICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSxtZXNzYWdlOiAn6K+36L6T5YWl6YWN572u6YePJ30sDQogICAgICAgIF0sDQogICAgICAgIGZvcm11bGFJZDogWw0KICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSxtZXNzYWdlOiAn6K+36YCJ5oup6YWN5pa557yW56CBJ30sDQogICAgICAgIF0sDQogICAgICAgIHR5cGU6IFsNCiAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsbWVzc2FnZTogJ+ivt+mAieaLqeW3peiJuuexu+Weiyd9LA0KICAgICAgICBdLA0KICAgICAgfSwNCiAgICAgIHByb2plY3RMaXN0OiBbXSwNCiAgICAgIGZvcm11bGFMaXN0OiBbXSwNCiAgICAgIGN1cnJlbnRQcm9qZWN0OiB7fSwNCiAgICAgIGN1cnJlbnRUYWI6ICdzcGxpdHRpbmcnLA0KICAgICAgZXF1aXBtZW50TGlzdDogW10sDQogICAgICBhaWRlZEVxdWlwbWVudExpc3Q6IFtdLA0KICAgICAgbWF0ZXJpYWxBcnJheTogW10sDQogICAgICBwcm9jZXNzQXJyYXk6IFtdLA0KICAgICAgcHJvZHVjdGl2aXR5QXJyYXk6IFtdLA0KICAgICAgZmluZGluZ0FycmF5OiBbXSwNCiAgICAgIGVxdWlwbWVudFR5cGVPcHRpb25zOiBbXSwNCiAgICAgIGN5Y2xlT3B0aW9uczogW10sDQogICAgICByaXNrTGV2ZWxPcHRpb25zOiBbXSwNCiAgICAgIGJjcENvZGU6IG51bGwsDQogICAgICBjdXJyZW50RGlhZ3JhbToge30sDQogICAgICBiY3BBcnJheTogW10sDQogICAgICBjb25jbHVzaW9uT3B0aW9uczogWw0KICAgICAgICB7bGFiZWw6ICfpgJrov4cnLHZhbHVlOiAnMCd9LA0KICAgICAgICB7bGFiZWw6ICfkuI3pgJrov4cnLHZhbHVlOiAnMSd9LA0KICAgICAgXSwNCiAgICAgIGVycERhdGFMaXN0OiBbXSwNCiAgICAgIHR5cGVPcHRpb25zOiBbDQogICAgICAgIHtsYWJlbDogJ+agh+WHhuW3peiJuicsdmFsdWU6ICcwJ30sDQogICAgICAgIHtsYWJlbDogJ+Wkp+i0p+W3peiJuicsdmFsdWU6ICcxJ30sDQogICAgICBdLA0KICAgICAgY3VycmVudFJvdzoge30sDQogICAgICBweEpzb246IHt9LA0KICAgICAgcHhBcnJheTogW10sDQogICAgICB3aGV0aGVyT3B0aW9uczogWw0KICAgICAgICB7bGFiZWw6ICfmmK8nLHZhbHVlOiAnMSd9LA0KICAgICAgICB7bGFiZWw6ICflkKYnLHZhbHVlOiAnMCd9LA0KICAgICAgXSwNCiAgICAgIGZpbGVzOiBbXSwNCiAgICAgIHN0ZXBGb3JtOiB7fSwNCiAgICAgIHN0ZXBSdWxlczogew0KICAgICAgICBwcm9jZXNzVGV4dDogWw0KICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSxtZXNzYWdlOiAn6K+36L6T5YWl5bel6Im6J30sDQogICAgICAgIF0sDQogICAgICAgIGVxdWlwbWVudElkOiBbDQogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLG1lc3NhZ2U6ICfor7fpgInmi6norr7lpIcnfSwNCiAgICAgICAgXSwNCiAgICAgIH0sDQogICAgfQ0KICB9LA0KICBhc3luYyBjcmVhdGVkKCkgew0KICAgIHRoaXMuZXF1aXBtZW50TGlzdCA9IGF3YWl0IGFsbEVxdWlwbWVudCh7dHlwZUlkOiA1OX0pDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICByZXNldFN0ZXBGb3JtKCkgew0KICAgICAgdGhpcy5zdGVwRm9ybSA9IHsNCiAgICAgICAgcHJvY2Vzc1RleHQ6IG51bGwsDQogICAgICAgIGVxdWlwbWVudElkOiBudWxsLA0KICAgICAgfQ0KICAgICAgdGhpcy5yZXNldEZvcm0oInN0ZXBGb3JtIikNCiAgICB9LA0KICAgIHNob3dEaWFsb2cocm93KSB7DQogICAgICB0aGlzLmN1cnJlbnRSb3cgPSByb3cNCiAgICAgIHRoaXMucmVzZXRTdGVwRm9ybSgpDQogICAgICB0aGlzLnRleHRPcGVuID0gdHJ1ZQ0KICAgIH0sDQogICAgYnVpbGRTdGVwQXJyYXkoKSB7DQogICAgICBjb25zdCByb3cgPSB0aGlzLmN1cnJlbnRSb3cNCiAgICAgIGNvbnN0IGZvcm0gPSBPYmplY3QuYXNzaWduKHt9LHRoaXMuc3RlcEZvcm0pDQoNCiAgICAgIGNvbnN0IGFycmF5ID0gdGhpcy5zcGxpdFN0ZXBzKGZvcm0ucHJvY2Vzc1RleHQpDQogICAgICBmb3IgKGNvbnN0IGl0ZW0gb2YgYXJyYXkpIHsNCiAgICAgICAgdGhpcy5hZGRBcmdzSXRlbShyb3cuZXF1aXBtZW50QXJncyxyb3cubWF0ZXJpYWxBcnJheSxpdGVtLGZvcm0uZXF1aXBtZW50SWQpDQogICAgICB9DQogICAgICB0aGlzLnRleHRPcGVuID0gZmFsc2UNCiAgICB9LA0KICAgIC8v5YiH5YiG5q2l6aqkDQogICAgc3BsaXRTdGVwcyh0ZXh0KSB7DQogICAgICByZXR1cm4gdGV4dC5zcGxpdCgnXG4nKS5yZWR1Y2UoKGFjYywgbGluZSkgPT4gew0KICAgICAgICBjb25zdCB0cmltbWVkTGluZSA9IGxpbmUudHJpbSgpOw0KICAgICAgICBpZiAoIXRyaW1tZWRMaW5lKSByZXR1cm4gYWNjOyAvLyDot7Pov4fnqbrooYwNCg0KICAgICAgICBpZiAoL14oXGQrXC584piFXGQrXC4pLy50ZXN0KHRyaW1tZWRMaW5lKSkgew0KICAgICAgICAgIC8vIOaWsOatpemqpOW8gOWni++8iOS7peaVsOWtl+aIluKYheaVsOWtl+W8gOWktO+8iQ0KICAgICAgICAgIGFjYy5wdXNoKHRyaW1tZWRMaW5lKTsNCiAgICAgICAgfSBlbHNlIGlmIChhY2MubGVuZ3RoID4gMCkgew0KICAgICAgICAgIC8vIOW7tue7reS4iuS4gOatpemqpOeahOWGheWuuQ0KICAgICAgICAgIGFjY1thY2MubGVuZ3RoIC0gMV0gKz0gJ1xuJyArIHRyaW1tZWRMaW5lOw0KICAgICAgICB9DQogICAgICAgIHJldHVybiBhY2M7DQogICAgICB9LCBbXSk7DQogICAgfSwNCiAgICBhc3luYyBjbG9zZShpZCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICB9LA0KICAgIGFzeW5jIHNob3dCUHJvY2Vzcyhyb3cpIHsNCiAgICAgIHRoaXMuY3VycmVudFJvdyA9IHJvdw0KICAgICAgdGhpcy50aXRsZSA9IHJvdy5tYXRlcmlhbENvZGUNCiAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICAgIGF3YWl0IHRoaXMuJG5leHRUaWNrKCkNCiAgICAgIGNvbnN0IG1ha2VVcEJTYXZlID0gdGhpcy4kcmVmcy5tYWtlVXBCU2F2ZQ0KICAgICAgaWYobWFrZVVwQlNhdmUpIHsNCiAgICAgICAgbWFrZVVwQlNhdmUucmVzZXQoKQ0KICAgICAgICBtYWtlVXBCU2F2ZS5mb3JtLm1ha2VVcElkID0gdGhpcy5mb3JtLmlkDQogICAgICAgIG1ha2VVcEJTYXZlLmZvcm0ubGFiQ29kZSA9IHJvdy5tYXRlcmlhbENvZGUNCiAgICAgICAgYXdhaXQgbWFrZVVwQlNhdmUuaW5pdChyb3cubWF0ZXJpYWxDb2RlKQ0KICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgcHJvY2Vzc1RvRXF1aXBtZW50KCkgew0KICAgICAgY29uc3QgcHJvY2Vzc0FycmF5ID0gdGhpcy5wcm9jZXNzQXJyYXkNCiAgICAgIGNvbnN0IHByb2R1Y3Rpdml0eUFycmF5ID0gdGhpcy5wcm9kdWN0aXZpdHlBcnJheQ0KICAgICAgY29uc3QgZXF1aXBtZW50SWRTZXQgPSBuZXcgU2V0KCkNCiAgICAgIGZvciAoY29uc3QgcCBvZiBwcm9jZXNzQXJyYXkpIHsNCiAgICAgICAgZm9yIChjb25zdCBlIG9mIHAuZXF1aXBtZW50QXJncykgew0KICAgICAgICAgIGlmKGUuZXF1aXBtZW50SWQpIHsNCiAgICAgICAgICAgIGVxdWlwbWVudElkU2V0LmFkZChlLmVxdWlwbWVudElkKQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgZm9yIChjb25zdCBlcXVpcG1lbnRJZCBvZiBlcXVpcG1lbnRJZFNldCkgew0KICAgICAgICBpZighcHJvZHVjdGl2aXR5QXJyYXkubWFwKGk9PmkuZXF1aXBtZW50SWQpLmluY2x1ZGVzKGVxdWlwbWVudElkKSkgew0KICAgICAgICAgIHByb2R1Y3Rpdml0eUFycmF5LnB1c2goew0KICAgICAgICAgICAgZXF1aXBtZW50SWQsDQogICAgICAgICAgICB3ZWlnaHQ6IHVuZGVmaW5lZCwNCiAgICAgICAgICAgIG1heDogdW5kZWZpbmVkLA0KICAgICAgICAgICAgbWluOiB1bmRlZmluZWQsDQogICAgICAgICAgICBwZXJzb25OdW1zOiB1bmRlZmluZWQsDQogICAgICAgICAgICBob3VyczogdW5kZWZpbmVkLA0KICAgICAgICAgICAgcHJvZHVjdGl2aXR5OiAwLA0KICAgICAgICAgICAgaG91clJhdGU6IDAsDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgc2VhcmNoRm9ybXVsYUxpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICBjb25zdCBsYWJvcmF0b3J5Q29kZSA9IHRoaXMuZm9ybS5sYWJDb2RlDQogICAgICBjb25zdCBmb3JtdWxhTGlzdCA9IGF3YWl0IGFsbEZvcm11bGEoe2xhYm9yYXRvcnlDb2RlfSkNCiAgICAgIHRoaXMuZm9ybXVsYUxpc3QgPSBmb3JtdWxhTGlzdA0KDQogICAgICBjb25zdCByZXMgPSBhd2FpdCBjb3VudE1ha2VVcEJ5TGFiQ29kZSh7bGFiQ29kZTogbGFib3JhdG9yeUNvZGV9KQ0KICAgICAgaWYocmVzLmNvZGUgPT09IDIwMCAmJiByZXMuZGF0YSkgew0KICAgICAgICB0aGlzLm1zZ0Vycm9yKCflrp7pqozlrqTnvJbnoIHlt7LlrZjlnKghJykNCiAgICAgIH0NCiAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgfSwNCiAgICBjb21wdXRlTnVtcygpIHsNCiAgICAgIGlmKHRoaXMuZm9ybS5udW1zKSB7DQogICAgICAgIGZvciAoY29uc3QgbyBvZiB0aGlzLm1hdGVyaWFsQXJyYXkpIHsNCiAgICAgICAgICBpZih0aGlzLmZvcm0ubnVtcyAmJiBvLnBlcmNlbnRhZ2UpIHsNCiAgICAgICAgICAgIG8ubnVtcyA9IHRoaXMubXVsdGlwbHkodGhpcy5mb3JtLm51bXMsdGhpcy5kaXZpZGUoby5wZXJjZW50YWdlLDEwMCkpLnRvTnVtYmVyKCkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgby5udW1zID0gMA0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgYWRkQXJnc0l0ZW0oYXJyYXksbWF0ZXJpYWxBcnJheSxwcm9jZXNzLGVxdWlwbWVudElkKSB7DQogICAgICBhcnJheS5wdXNoKHsNCiAgICAgICAgc3RlcDogYXJyYXkubGVuZ3RoKzEsDQogICAgICAgIG1hdGVyaWFsSWRzOiBtYXRlcmlhbEFycmF5Lm1hcChpPT5pLm1hdGVyaWFsSWQpLA0KICAgICAgICBlcXVpcG1lbnRJZCwNCiAgICAgICAgcHJvY2VzcywNCiAgICAgICAga2V5UG9pbnRzOiBudWxsLA0KICAgICAgICB0ZW1wZXJhdHVyZTogbnVsbCwNCiAgICAgICAgbXBhOiBudWxsLA0KICAgICAgICBqejogbnVsbCwNCiAgICAgICAganpUaW1lOiBudWxsLA0KICAgICAgICBuajogbnVsbCwNCiAgICAgICAgbmpUaW1lOiBudWxsLA0KICAgICAgICB3ajogbnVsbCwNCiAgICAgICAgems6IG51bGwsDQogICAgICAgIHRpbWU6IG51bGwsDQogICAgICB9KQ0KICAgIH0sDQogICAgZGVsSXRlbShhcnJheSxpKSB7DQogICAgICBhcnJheS5zcGxpY2UoaSwxKQ0KICAgIH0sDQogICAgYWRkUHhJdGVtKCkgew0KICAgICAgdGhpcy5weEFycmF5LnB1c2goew0KICAgICAgICBkZXB0OiBudWxsLA0KICAgICAgICBuYW1lOiBudWxsLA0KICAgICAgICBpc0xqOiBudWxsLA0KICAgICAgICBzdWdnZXN0aW9uOiBudWxsLA0KICAgICAgICBwZzogbnVsbCwNCiAgICAgIH0pDQogICAgfSwNCiAgICBhc3luYyBnZW5lcmF0ZVByb2Nlc3NBcnJheSgpIHsNCiAgICAgIHRoaXMuYnRuTG9hZGluZyA9IHRydWUNCiAgICAgIGNvbnN0IHRlbXBBcnJheSA9IFtdDQogICAgICBmb3IgKGNvbnN0IG0gb2YgdGhpcy5tYXRlcmlhbEFycmF5KSB7DQogICAgICAgIGZvciAoY29uc3Qgc3ViIG9mIG0uYXJyYXkpIHsNCiAgICAgICAgICB0ZW1wQXJyYXkucHVzaCh7DQogICAgICAgICAgICBtYXRlcmlhbElkOiBtLm1hdGVyaWFsSWQsDQogICAgICAgICAgICBlcnBDb2RlOiBtLmVycENvZGUsDQogICAgICAgICAgICBtYXRlcmlhbENvZGU6IG0ubWF0ZXJpYWxDb2RlLA0KICAgICAgICAgICAgcGVyY2VudGFnZTogc3ViLnBlcmNlbnRhZ2UsDQogICAgICAgICAgICBzdWJJdGVtOiBzdWIuc3ViSXRlbSwNCiAgICAgICAgICAgIGNhdGVnb3J5OiBzdWIuc3ViSXRlbS5yZXBsYWNlKC9ccyovZywiIikuc3Vic3RyaW5nKDAsIDEpLA0KICAgICAgICAgICAgZmlyc3RQZXJjZW50YWdlOiBudWxsLA0KICAgICAgICAgICAgcHJvY2Vzc0FycmF5OiBbXSwNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9DQogICAgICBjb25zdCBjYXRlZ29yeVNldCA9IG5ldyBTZXQoKQ0KICAgICAgZm9yIChjb25zdCB0ZW1wIG9mIHRlbXBBcnJheSkgew0KICAgICAgICBjYXRlZ29yeVNldC5hZGQodGVtcC5jYXRlZ29yeSkNCiAgICAgIH0NCiAgICAgIGNvbnN0IHByb2Nlc3NBcnJheSA9IFtdDQogICAgICBmb3IgKGNvbnN0IGNhdGVnb3J5IG9mIGNhdGVnb3J5U2V0KSB7DQogICAgICAgIGNvbnN0IG1hdGVyaWFsQXJyYXkgPSB0ZW1wQXJyYXkuZmlsdGVyKGk9PmkuY2F0ZWdvcnkgPT09IGNhdGVnb3J5KS5zb3J0KChhLGIpPT4gYS5zdWJJdGVtLnJlcGxhY2UoL1thLXpBLVpdL2csICcnKSAtIGIuc3ViSXRlbS5yZXBsYWNlKC9bYS16QS1aXS9nLCAnJykpDQogICAgICAgIGNvbnN0IGVxdWlwbWVudEFyZ3MgPSBbXQ0KICAgICAgICB0aGlzLmFkZEFyZ3NJdGVtKGVxdWlwbWVudEFyZ3MsbWF0ZXJpYWxBcnJheSkNCiAgICAgICAgcHJvY2Vzc0FycmF5LnB1c2goew0KICAgICAgICAgIGNhdGVnb3J5LA0KICAgICAgICAgIG1hdGVyaWFsQXJyYXksDQogICAgICAgICAgZXF1aXBtZW50QXJncywNCiAgICAgICAgICBwcm9jZXNzRGVzYzogbnVsbCwNCiAgICAgICAgICBrZXlQb2ludHM6IG51bGwsDQogICAgICAgICAgYWlkZWRBcnJheTogW10sDQogICAgICAgICAgZmlsZXM6W10sDQogICAgICAgICAgbWF0ZXJpYWxJZHM6IFtdLC8v5bel6Im65Zu+5Lit6YCJ5Lit55qE54mp5paZDQogICAgICAgIH0pDQogICAgICB9DQogICAgICB0aGlzLnByb2Nlc3NBcnJheSA9IHByb2Nlc3NBcnJheS5zb3J0KChhLGIpPT5hLmNhdGVnb3J5LmxvY2FsZUNvbXBhcmUoYi5jYXRlZ29yeSkpDQogICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQ0KICAgIH0sDQogICAgcGVyY2VudGFnZVN0eWxlKGl0ZW0pIHsNCiAgICAgIGxldCBwZXJjZW50YWdlID0gaXRlbS5hcnJheS5maWx0ZXIoaT0+aS5wZXJjZW50YWdlKS5tYXAoaT0+aS5wZXJjZW50YWdlKS5yZWR1Y2UoKGEsYik9PiB0aGlzLmFkZChhLCBiKSx0aGlzLiRiaWcoMCkpDQogICAgICBpZihwZXJjZW50YWdlLnRvTnVtYmVyKCkgPT09IGl0ZW0ucGVyY2VudGFnZSkgew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIGNvbG9yOiAnIzY3QzIzQScNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICBjb2xvcjogJyNGNTZDNkMnDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIGFkZFN1Ykl0ZW0oaXRlbSkgew0KICAgICAgaXRlbS5hcnJheS5wdXNoKHsNCiAgICAgICAgcGVyY2VudGFnZTogbnVsbCwNCiAgICAgICAgc3ViSXRlbTogbnVsbCwNCiAgICAgIH0pDQogICAgfSwNCiAgICBkZWxTdWJJdGVtKGl0ZW0saSkgew0KICAgICAgaXRlbS5hcnJheS5zcGxpY2UoaSwxKQ0KICAgIH0sDQogICAgYXN5bmMgZm9ybXVsYUNoYW5nZSgpIHsNCiAgICAgIGNvbnN0IGZvcm0gPSB0aGlzLmZvcm0NCiAgICAgIGNvbnN0IGZvcm11bGFJZCA9IGZvcm0uZm9ybXVsYUlkDQogICAgICBpZihmb3JtdWxhSWQpIHsNCiAgICAgICAgY29uc3QgZm9ybXVsYVJlcyA9IGF3YWl0IGdldEZvcm11bGEoZm9ybXVsYUlkKQ0KICAgICAgICBpZihmb3JtdWxhUmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIGNvbnN0IGRhdGEgPSBmb3JtdWxhUmVzLmRhdGENCg0KICAgICAgICAgIGZvcm0uZm9ybXVsYUNvZGUgPSBkYXRhLmZvcm11bGFDb2RlDQogICAgICAgICAgZm9ybS5sYWJDb2RlID0gZGF0YS5sYWJvcmF0b3J5Q29kZQ0KICAgICAgICAgIGZvcm0uY3VzdG9tZXJOYW1lID0gZGF0YS5jdXN0b21lck5hbWUNCiAgICAgICAgICBmb3JtLnByb2R1Y3ROYW1lID0gZGF0YS5wcm9kdWN0TmFtZQ0KICAgICAgICAgIGZvcm0ucHJvamVjdE5vID0gZGF0YS5wcm9qZWN0Tm8NCg0KICAgICAgICAgIGlmKGRhdGEuamNYbUpzb24pIHsNCiAgICAgICAgICAgIGNvbnN0IGJjcEFycmF5ID0gSlNPTi5wYXJzZShkYXRhLmpjWG1Kc29uKQ0KICAgICAgICAgICAgdGhpcy5iY3BBcnJheSA9IHRoaXMuYmNwQXJyYXkuZmlsdGVyKGk9PmJjcEFycmF5Lm1hcChiPT5iLmlkKS5pbmNsdWRlcyhpLmlkKSkvL+i/h+a7pOeglOWPkeS4reW3sue7j+WIoOmZpOeahOWNiuaIkOWTgQ0KICAgICAgICAgICAgZm9yIChjb25zdCBiY3Agb2YgYmNwQXJyYXkpIHsNCiAgICAgICAgICAgICAgaWYoIXRoaXMuYmNwQXJyYXkubWFwKGk9PmkuaWQpLmluY2x1ZGVzKGJjcC5pZCkpIHsNCiAgICAgICAgICAgICAgICBiY3AuenNTdGFuZGFyZCA9IG51bGwNCiAgICAgICAgICAgICAgICBiY3AuaW5zcGVjdGlvbkFycmF5ID0gW10NCiAgICAgICAgICAgICAgICB0aGlzLmJjcEFycmF5LnB1c2goYmNwKS8v6KGl5YWF56CU5Y+R5paw5aKe55qEDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgY29uc3QgYXJyYXkgPSBhd2FpdCBhbGxNYXRlcmlhbEZvcm11bGEoe2Zvcm11bGFJZH0pDQogICAgICAgIGNvbnN0IG1hdGVyaWFsSWRTZXQgPSBuZXcgU2V0KCkNCiAgICAgICAgZm9yIChjb25zdCBpdGVtIG9mIGFycmF5KSB7DQogICAgICAgICAgbWF0ZXJpYWxJZFNldC5hZGQoaXRlbS5tYXRlcmlhbElkKQ0KICAgICAgICB9DQogICAgICAgIGNvbnN0IG1hdGVyaWFsQXJyYXkgPSBbXQ0KICAgICAgICBmb3IgKGNvbnN0IG1hdGVyaWFsSWQgb2YgbWF0ZXJpYWxJZFNldCkgew0KICAgICAgICAgIGNvbnN0IGFyciA9IGFycmF5LmZpbHRlcihpPT4gaS5tYXRlcmlhbElkID09PSBtYXRlcmlhbElkKQ0KICAgICAgICAgIGlmKGFyciAmJiBhcnJbMF0pIHsNCiAgICAgICAgICAgIGNvbnN0IG8gPSB7DQogICAgICAgICAgICAgIG1hdGVyaWFsSWQ6IGFyclswXS5tYXRlcmlhbElkLA0KICAgICAgICAgICAgICBlcnBDb2RlOiBhcnJbMF0uZXJwQ29kZSwNCiAgICAgICAgICAgICAgbWF0ZXJpYWxDb2RlOiBhcnJbMF0ubWF0ZXJpYWxDb2RlLA0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgY29uc3Qgc3ViQXJyYXkgPSBhcnIubWFwKGk9PiB7DQogICAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgICAgcGVyY2VudGFnZTogaS5wZXJjZW50YWdlLA0KICAgICAgICAgICAgICAgIHN1Ykl0ZW06IGkuc3ViSXRlbSwNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIG8uYXJyYXkgPSBzdWJBcnJheQ0KICAgICAgICAgICAgby5wZXJjZW50YWdlID0gc3ViQXJyYXkubWFwKGk9PmkucGVyY2VudGFnZSkucmVkdWNlKChhLGIpPT4gdGhpcy5hZGQoYSwgYiksMCkudG9OdW1iZXIoKQ0KICAgICAgICAgICAgbWF0ZXJpYWxBcnJheS5wdXNoKG8pDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHRoaXMubWF0ZXJpYWxBcnJheSA9IG1hdGVyaWFsQXJyYXkNCg0KICAgICAgICB0aGlzLmNvbXB1dGVOdW1zKCkNCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIHNlYXJjaFByb2plY3QoKSB7DQogICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlDQogICAgICBsZXQgcHJvamVjdE5vID0gdGhpcy5mb3JtLnByb2plY3RObw0KICAgICAgaWYocHJvamVjdE5vKSB7DQogICAgICAgIHByb2plY3RObyA9IHByb2plY3ROby5yZXBsYWNlKC9ccyovZywiIikNCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgZ2V0UHJvamVjdEJ5Tm8ocHJvamVjdE5vKQ0KICAgICAgICBpZihyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgY29uc3QgcHJvamVjdCA9IHJlcy5kYXRhDQogICAgICAgICAgaWYocHJvamVjdCkgew0KICAgICAgICAgICAgdGhpcy5jdXJyZW50UHJvamVjdCA9IHByb2plY3QNCiAgICAgICAgICAgIHRoaXMuZm9ybS5wcm9qZWN0SWQgPSBwcm9qZWN0LmlkDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQ0KICAgIH0sDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy4kcGFyZW50LiRwYXJlbnQub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICBwcm9qZWN0SWQ6IG51bGwsDQogICAgICAgIGVycENvZGU6IG51bGwsDQogICAgICAgIGZvcm11bGFJZDogbnVsbCwNCiAgICAgICAgZm9ybXVsYUNvZGU6IG51bGwsDQogICAgICAgIGRpYWdyYW1JZDogbnVsbCwNCiAgICAgICAgcHJvamVjdE5vOiBudWxsLA0KICAgICAgICBudW1zOiAxMDAsDQogICAgICAgIHR5cGU6ICcwJywNCiAgICAgICAgdGVtcGVyYXR1cmU6IG51bGwsDQogICAgICAgIG1lc2hlc051bXM6IG51bGwsDQogICAgICAgIGxhYkNvZGU6IG51bGwsDQogICAgICAgIHB4VXNlcjogbnVsbCwNCiAgICAgICAgcHhEYXRlOiBudWxsLA0KICAgICAgICBwelVzZXI6IG51bGwsDQogICAgICAgIHB6RGF0ZTogbnVsbCwNCiAgICAgICAgcWNDb2RlOiBudWxsLA0KICAgICAgfQ0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKQ0KICAgICAgdGhpcy5jdXJyZW50UHJvamVjdCA9IHt9DQogICAgICB0aGlzLmZvcm11bGFMaXN0ID0gW10NCiAgICAgIHRoaXMubWF0ZXJpYWxBcnJheSA9IFtdDQogICAgICB0aGlzLnByb2Nlc3NBcnJheSA9IFtdDQogICAgICB0aGlzLnByb2R1Y3Rpdml0eUFycmF5ID0gW10NCiAgICAgIHRoaXMuZmluZGluZ0FycmF5ID0gW10NCiAgICAgIHRoaXMuYmNwQXJyYXkgPSBbXQ0KICAgICAgdGhpcy5iY3BDb2RlID0gbnVsbA0KICAgICAgdGhpcy5lcnBEYXRhTGlzdCA9IFtdDQogICAgICB0aGlzLnB4SnNvbiA9IHsNCiAgICAgICAgYnlRcjogbnVsbCwNCiAgICAgICAgYnlTdGF0dXM6IG51bGwsDQogICAgICAgIGp5VGltZTogbnVsbCwNCiAgICAgICAgemxKeTogbnVsbCwNCiAgICAgICAgY2xGYTogbnVsbCwNCiAgICAgICAgY3BKczogbnVsbCwNCiAgICAgICAgY3BUeDogbnVsbCwNCiAgICAgICAgdGJZbDogbnVsbCwNCiAgICAgICAgenlTeDogbnVsbCwNCiAgICAgICAgank6IG51bGwsDQogICAgICAgIGd4czogbnVsbCwNCiAgICAgICAgc3VtbWFyeTogbnVsbCwNCiAgICAgIH0NCiAgICAgIHRoaXMucHhBcnJheSA9IFtdDQogICAgICB0aGlzLmZpbGVzID0gW10NCiAgICB9LA0KICAgIGFzeW5jIGluaXQoaWQpIHsNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGdldE1ha2VVcChpZCkNCiAgICAgIGNvbnN0IGZvcm0gPSByZXMuZGF0YQ0KDQogICAgICBpZihmb3JtLm1hdGVyaWFsQXJyYXkpIHsNCiAgICAgICAgdGhpcy5tYXRlcmlhbEFycmF5ID0gSlNPTi5wYXJzZShmb3JtLm1hdGVyaWFsQXJyYXkpDQogICAgICB9DQoNCiAgICAgIGlmKGZvcm0ucHJvY2Vzc0FycmF5KSB7DQogICAgICAgIHRoaXMucHJvY2Vzc0FycmF5ID0gSlNPTi5wYXJzZShmb3JtLnByb2Nlc3NBcnJheSkNCiAgICAgIH0NCg0KICAgICAgaWYoZm9ybS5wcm9kdWN0aXZpdHlBcnJheSkgew0KICAgICAgICB0aGlzLnByb2R1Y3Rpdml0eUFycmF5ID0gSlNPTi5wYXJzZShmb3JtLnByb2R1Y3Rpdml0eUFycmF5KQ0KICAgICAgfQ0KDQogICAgICBpZihmb3JtLmJjcEFycmF5KSB7DQogICAgICAgIHRoaXMuYmNwQXJyYXkgPSBKU09OLnBhcnNlKGZvcm0uYmNwQXJyYXkpDQogICAgICB9DQoNCiAgICAgIGlmKGZvcm0uZmluZGluZ0FycmF5KSB7DQogICAgICAgIHRoaXMuZmluZGluZ0FycmF5ID0gSlNPTi5wYXJzZShmb3JtLmZpbmRpbmdBcnJheSkNCiAgICAgIH0NCg0KICAgICAgaWYoZm9ybS5weEpzb24pIHsNCiAgICAgICAgdGhpcy5weEpzb24gPSBKU09OLnBhcnNlKGZvcm0ucHhKc29uKQ0KICAgICAgfQ0KDQogICAgICBpZihmb3JtLnB4QXJyYXkpIHsNCiAgICAgICAgdGhpcy5weEFycmF5ID0gSlNPTi5wYXJzZShmb3JtLnB4QXJyYXkpDQogICAgICB9DQoNCiAgICAgIGlmKGZvcm0uZmlsZXMpIHsNCiAgICAgICAgdGhpcy5maWxlcyA9IEpTT04ucGFyc2UoZm9ybS5maWxlcykNCiAgICAgIH0NCg0KICAgICAgdGhpcy5mb3JtID0gZm9ybQ0KICAgICAgYXdhaXQgdGhpcy5zZWFyY2hQcm9qZWN0KCkNCg0KICAgIH0sDQogICAgYXN5bmMgc3VibWl0Rm9ybSgpIHsNCiAgICAgIGF3YWl0IHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSgpDQogICAgICBsZXQgZm9ybSA9IE9iamVjdC5hc3NpZ24oe30sdGhpcy5mb3JtKQ0KDQogICAgICBmb3IgKGNvbnN0IG0gb2YgdGhpcy5tYXRlcmlhbEFycmF5KSB7DQogICAgICAgIGxldCBwZXJjZW50YWdlID0gbS5hcnJheS5maWx0ZXIoaT0+aS5wZXJjZW50YWdlKS5tYXAoaT0+aS5wZXJjZW50YWdlKS5yZWR1Y2UoKGEsYik9PiB0aGlzLmFkZChhLCBiKSwwKQ0KICAgICAgICBpZihwZXJjZW50YWdlLnRvTnVtYmVyKCkgIT09IG0ucGVyY2VudGFnZSkgew0KICAgICAgICAgIHRoaXMubXNnRXJyb3IoJ+mFjeaWueavlOS+i+S4jeS4gOiHtCzor7fkv67mlLnlkI7mj5DkuqQnKQ0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIGZvciAoY29uc3QgaXRlbSBvZiB0aGlzLnByb2R1Y3Rpdml0eUFycmF5KSB7DQogICAgICAgIGlmKCFpdGVtLmVxdWlwbWVudElkKSB7DQogICAgICAgICAgdGhpcy5tc2dFcnJvcign6K+36YCJ5oup6K6+5aSHJykNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KICAgICAgICBpZighaXRlbS53ZWlnaHQpIHsNCiAgICAgICAgICB0aGlzLm1zZ0Vycm9yKCfor7fovpPlhaXmoIflh4bphY3liLbph48nKQ0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIGlmKCFpdGVtLnBlcnNvbk51bXMpIHsNCiAgICAgICAgICB0aGlzLm1zZ0Vycm9yKCfor7fovpPlhaXmoIflh4bkurrmlbAnKQ0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIGlmKCFpdGVtLmhvdXJzKSB7DQogICAgICAgICAgdGhpcy5tc2dFcnJvcign6K+36L6T5YWl5qCH5YeG5bel5pe2JykNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICBmb3JtLm1hdGVyaWFsQXJyYXkgPSBKU09OLnN0cmluZ2lmeSh0aGlzLm1hdGVyaWFsQXJyYXkpDQogICAgICBmb3JtLnByb2Nlc3NBcnJheSA9IEpTT04uc3RyaW5naWZ5KHRoaXMucHJvY2Vzc0FycmF5KQ0KICAgICAgZm9ybS5wcm9kdWN0aXZpdHlBcnJheSA9IEpTT04uc3RyaW5naWZ5KHRoaXMucHJvZHVjdGl2aXR5QXJyYXkpDQogICAgICBmb3JtLmJjcEFycmF5ID0gSlNPTi5zdHJpbmdpZnkodGhpcy5iY3BBcnJheSkNCiAgICAgIGZvcm0uZmluZGluZ0FycmF5ID0gSlNPTi5zdHJpbmdpZnkodGhpcy5maW5kaW5nQXJyYXkpDQogICAgICBmb3JtLnB4SnNvbiA9IEpTT04uc3RyaW5naWZ5KHRoaXMucHhKc29uKQ0KICAgICAgZm9ybS5weEFycmF5ID0gSlNPTi5zdHJpbmdpZnkodGhpcy5weEFycmF5KQ0KICAgICAgZm9ybS5maWxlcyA9IEpTT04uc3RyaW5naWZ5KHRoaXMuZmlsZXMpDQoNCiAgICAgIGlmIChmb3JtLmlkICE9IG51bGwpIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlDQogICAgICAgICAgYXdhaXQgdXBkYXRlTWFrZVVwKGZvcm0pDQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICBpZihyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpDQogICAgICAgICAgfQ0KICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlDQogICAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgYWRkTWFrZVVwKGZvcm0pDQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICBpZihyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpDQogICAgICAgICAgICBhd2FpdCB0aGlzLmluaXQocmVzLmRhdGEuaWQpDQogICAgICAgICAgfQ0KICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogIH0NCn0NCg=="}, {"version": 3, "sources": ["save.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAig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file": "save.vue", "sourceRoot": "src/views/sop/makeUp", "sourcesContent": ["<template>\r\n  <div v-loading=\"loading\" >\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" size=\"mini\" label-width=\"100px\">\r\n      <div :class=\"readonly?'mask':''\" >\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <el-form-item prop=\"labCode\" label=\"实验室编码\" >\r\n              <el-input v-model=\"form.labCode\" >\r\n                <template #append >\r\n                  <el-tooltip content=\"根据实验室编码 带出 配方编码\" >\r\n                    <el-button icon=\"el-icon-search\" @click=\"searchFormulaList\" />\r\n                  </el-tooltip>\r\n                </template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item prop=\"formulaId\" >\r\n              <template #label>\r\n                配方编码\r\n                <el-tooltip content=\"根据配方编码 带出 称量记录、配方分相、半成品检验记录\" >\r\n                  <i class=\"el-icon-question\" />\r\n                </el-tooltip>\r\n              </template>\r\n              <el-select v-model=\"form.formulaId\" filterable @change=\"formulaChange\" >\r\n                <el-option\r\n                  v-for=\"item in formulaList\"\r\n                  :key=\"item.id\"\r\n                  :label=\"item.formulaCode\"\r\n                  :value=\"item.id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item prop=\"type\">\r\n              <template #label>\r\n                工艺类型\r\n              </template>\r\n              <el-select v-model=\"form.type\" >\r\n                <el-option\r\n                  v-for=\"item in typeOptions\"\r\n                  :key=\"item.value\"\r\n                  :value=\"item.value\"\r\n                  :label=\"item.label\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row >\r\n        <el-row :gutter=\"20\" >\r\n          <el-col :span=\"8\">\r\n            <el-form-item prop=\"nums\">\r\n              <template #label>\r\n                配制量\r\n                <el-tooltip content=\"根据 此处的配制量 和 称量记录中的配制比例 计算 称量记录中 单个物料的配制量\" >\r\n                  <i class=\"el-icon-question\" />\r\n                </el-tooltip>\r\n              </template>\r\n              <el-input v-model=\"form.nums\" @input=\"computeNums\" >\r\n                <template #append>kg</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"客户\" prop=\"customerName\">\r\n              {{form.customerName}}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"产品名称\" prop=\"productName\">\r\n              {{form.productName}}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\" >\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"项目\" prop=\"projectNo\">\r\n              {{form.projectNo}}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"培训人\" prop=\"pxUser\">\r\n              <el-input v-model=\"form.pxUser\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"培训日期\" prop=\"pxDate\">\r\n              <el-date-picker\r\n                clearable\r\n                v-model=\"form.pxDate\"\r\n                type=\"date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\" >\r\n<!--          <el-col :span=\"8\">-->\r\n<!--            <el-form-item label=\"配制人\" prop=\"pzUser\">-->\r\n<!--              <el-input v-model=\"form.pzUser\" />-->\r\n<!--            </el-form-item>-->\r\n<!--          </el-col>-->\r\n<!--          <el-col :span=\"8\">-->\r\n<!--            <el-form-item label=\"配制日期\" prop=\"pzDate\">-->\r\n<!--              <el-date-picker-->\r\n<!--                clearable-->\r\n<!--                v-model=\"form.pzDate\"-->\r\n<!--                type=\"date\"-->\r\n<!--                value-format=\"yyyy-MM-dd\"-->\r\n<!--              />-->\r\n<!--            </el-form-item>-->\r\n<!--          </el-col>-->\r\n          <el-col :span=\"8\" v-if=\"form.qcCode\" >\r\n            <el-form-item prop=\"qcCode\" label=\"文件受控编号\" >\r\n              {{form.qcCode}}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <el-divider content-position=\"left\" >工艺详情</el-divider>\r\n\r\n      <el-tabs v-model=\"currentTab\" >\r\n        <el-tab-pane key=\"record\" label=\"称量记录\" lazy name=\"record\" >\r\n          <div class=\"table-wrapper\">\r\n            <table class=\"base-table small-table\">\r\n              <tr>\r\n                <th style=\"width: 60px\" >序号</th>\r\n                <th style=\"width: 100px\" >物料代码</th>\r\n                <th style=\"width: 180px\" >物料名称</th>\r\n                <th style=\"width: 180px\" >配制比例</th>\r\n                <th style=\"width: 180px\" >配制量(kg)</th>\r\n              </tr>\r\n              <tr v-for=\"(item,i) in materialArray\" :key=\"i\" >\r\n                <td >{{i+1}}</td>\r\n                <td>{{item.erpCode}}</td>\r\n                <td>{{item.materialCode}}</td>\r\n                <td><span :style=\"percentageStyle(item)\">{{item.percentage}}%</span></td>\r\n                <td>{{item.nums}}</td>\r\n              </tr>\r\n            </table>\r\n          </div>\r\n        </el-tab-pane>\r\n        <el-tab-pane key=\"splitting\" label=\"配方分相\" lazy name=\"splitting\" >\r\n          <div class=\"table-wrapper\">\r\n            <table class=\"base-table small-table\">\r\n              <tr>\r\n                <th style=\"width: 100px\" >物料代码</th>\r\n                <th style=\"width: 180px\" >物料名称</th>\r\n                <th style=\"width: 180px\" >比例</th>\r\n                <th >研发分相</th>\r\n              </tr>\r\n              <tr v-for=\"(item,i) in materialArray\" :key=\"i\" :class=\"readonly?'mask':''\">\r\n                <td>{{item.erpCode}}</td>\r\n                <td>\r\n                  {{item.materialCode}}\r\n                  (<span :style=\"percentageStyle(item)\">{{item.percentage}}%</span>)\r\n                  <i class=\"el-icon-circle-plus-outline\" @click=\"addSubItem(item)\" />\r\n                </td>\r\n                <td :colspan=\"2\" style=\"padding: 0\" >\r\n                  <table class=\"base-table\" style=\"margin: 0\">\r\n                    <tr v-for=\"(sub,i) in item.array\" :key=\"i\" >\r\n                      <td style=\"width: 180px\" >\r\n                        <div style=\"display: flex;align-items: center\">\r\n                          <i class=\"el-icon-remove-outline\" @click=\"delSubItem(item,i)\" />\r\n                          <el-input v-model=\"sub.percentage\" size=\"mini\" >\r\n                            <template #append >%</template>\r\n                          </el-input>\r\n                        </div>\r\n                      </td>\r\n                      <td>\r\n                        <el-input v-model=\"sub.subItem\" size=\"mini\" v-input.num_alp />\r\n                      </td>\r\n                    </tr>\r\n                  </table>\r\n                </td>\r\n              </tr>\r\n              <tr>\r\n                <th >合计</th>\r\n                <td >{{sumPercentage}}<span v-if=\"sumPercentage\">%</span></td>\r\n                <td ></td>\r\n                <td ></td>\r\n              </tr>\r\n            </table>\r\n          </div>\r\n        </el-tab-pane>\r\n        <el-tab-pane key=\"base\" label=\"工艺详情\" lazy name=\"base\" >\r\n          <el-tooltip v-if=\"!readonly\" content=\"重新生成工艺详情\" >\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" :loading=\"btnLoading\" @click=\"generateProcessArray\" />\r\n          </el-tooltip>\r\n\r\n          <div class=\"table-wrapper\">\r\n            <table class=\"base-table small-table\">\r\n              <thead >\r\n                <tr>\r\n                  <th style=\"width: 120px\" class=\"nth1\" >工段</th>\r\n                  <th style=\"width: 120px\" class=\"nth2\" >主分相</th>\r\n                  <th style=\"width: 120px\" class=\"nth3\" >子分相</th>\r\n                  <th style=\"width: 120px\" class=\"nth4\" >物料代码</th>\r\n                  <th style=\"width: 120px\" class=\"nth5\" >物料名称</th>\r\n                  <th style=\"width: 120px\" class=\"nth6\" >比例</th>\r\n                  <th style=\"width: 120px\" class=\"nth7\" >首次投料比例</th>\r\n                  <th style=\"width: 1560px;\" >设备参数</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                <tr v-for=\"(item,index) in processArray\" :key=\"index\" >\r\n                  <td v-if=\"index === 0\" :rowspan=\"processArray.length\" class=\"nth1\" >配制段</td>\r\n                  <td class=\"nth2\" >{{item.category}}</td>\r\n                  <td class=\"nth3\" :colspan=\"5\" style=\"padding: 0\">\r\n                    <table class=\"base-table small-table\" style=\"margin: 0\" >\r\n                      <tr v-for=\"(m,z) in item.materialArray\" :key=\"z\" >\r\n                        <td>{{m.subItem}}</td>\r\n                        <td>{{m.erpCode}}</td>\r\n                        <td>\r\n                          <span v-if=\"m.materialCode.startsWith('B') && form.id\"\r\n                                style=\"color: #00afff;cursor: pointer\"\r\n                                @click=\"showBProcess(m)\">{{m.materialCode}}</span>\r\n                          <span v-else >{{m.materialCode}}</span>\r\n                        </td>\r\n                        <td>{{m.percentage}}%</td>\r\n                        <td>\r\n                          <span v-if=\"readonly\" >{{m.firstPercentage}}%</span>\r\n                          <el-input v-else v-model=\"m.firstPercentage\" size=\"mini\" type=\"number\" >\r\n                            <template #append>%</template>\r\n                          </el-input>\r\n                        </td>\r\n                      </tr>\r\n                    </table>\r\n                  </td>\r\n                  <td style=\"padding: 0\">\r\n                    <table class=\"base-table small-table\" style=\"margin: 0\" :class=\"readonly?'mask':''\" >\r\n                      <tr>\r\n                        <th style=\"width: 50px\" >\r\n                          <i class=\"el-icon-circle-plus-outline\" v-if=\"!readonly\" @click=\"addArgsItem(item.equipmentArgs,item.materialArray)\" />\r\n                        </th>\r\n                        <th style=\"width: 80px\" >\r\n                          步骤\r\n                          <i class=\"el-icon-s-help\" v-if=\"!readonly\" @click=\"showDialog(item)\" />\r\n                        </th>\r\n                        <th style=\"width: 120px\" >原料</th>\r\n                        <th style=\"width: 300px\" >设备</th>\r\n                        <th style=\"width: 180px\" >工艺描述</th>\r\n                        <th style=\"width: 100px\" >温度(℃)</th>\r\n                        <th style=\"width: 100px\" >均质(rpm/min)</th>\r\n                        <th style=\"width: 100px\" >均质时间(min)</th>\r\n                        <th style=\"width: 100px\" >搅拌(转/min)</th>\r\n                        <th style=\"width: 100px\" >搅拌时间(min)</th>\r\n                        <th style=\"width: 100px\" >时长(min)</th>\r\n                        <th style=\"width: 100px\" >真空mpa</th>\r\n                      </tr>\r\n                      <tr v-for=\"(d,di) in item.equipmentArgs\" :key=\"di\">\r\n                        <td>\r\n                          <i class=\"el-icon-remove-outline\" @click=\"delItem(item.equipmentArgs,di)\" />\r\n                        </td>\r\n                        <td >\r\n                          <el-input v-model=\"d.step\" size=\"mini\" type=\"number\" />\r\n                        </td>\r\n                        <td >\r\n                          <el-select v-model=\"d.materialIds\" filterable multiple size=\"mini\" >\r\n                            <el-option\r\n                              v-for=\"m in item.materialArray\"\r\n                              :key=\"m.materialId\"\r\n                              :label=\"m.materialCode + '|' + m.subItem\"\r\n                              :value=\"m.materialId\" />\r\n                          </el-select>\r\n                        </td>\r\n                        <td>\r\n                          <el-select v-model=\"d.equipmentId\" filterable size=\"mini\" style=\"width: 280px\" >\r\n                            <el-option\r\n                              v-for=\"e in equipmentList\"\r\n                              :key=\"e.id\"\r\n                              :value=\"e.id\"\r\n                              :label=\"e.name\" />\r\n                          </el-select>\r\n                        </td>\r\n                        <td >\r\n                          <span v-if=\"readonly\">{{d.process}}</span>\r\n                          <el-input\r\n                            v-else\r\n                            v-model=\"d.process\"\r\n                            autosize\r\n                            size=\"mini\"\r\n                            type=\"textarea\"\r\n                          />\r\n                        </td>\r\n                        <td ><el-input v-model=\"d.temperature\" size=\"mini\" /></td>\r\n\r\n                        <td ><el-input v-model=\"d.jz\" size=\"mini\" /></td>\r\n                        <td ><el-input v-model=\"d.jzTime\" size=\"mini\" /></td>\r\n                        <td ><el-input v-model=\"d.nj\" size=\"mini\" /></td>\r\n                        <td ><el-input v-model=\"d.njTime\" size=\"mini\" /></td>\r\n                        <td ><el-input v-model=\"d.time\" size=\"mini\" /></td>\r\n                        <td ><el-input v-model=\"d.mpa\" size=\"mini\" /></td>\r\n                      </tr>\r\n                    </table>\r\n                  </td>\r\n                </tr>\r\n                <tr >\r\n                  <td >出料</td>\r\n                  <td :colspan=\"9\" >\r\n                    <el-row :gutter=\"20\" >\r\n                      <el-col :span=\"4\" >\r\n                        <div class=\"cell-wrapper\">\r\n                          <div class=\"label\">温度</div>\r\n                          <div class=\"content\">\r\n                            <el-input v-model=\"form.temperature\" size=\"mini\" />\r\n                          </div>\r\n                        </div>\r\n                      </el-col>\r\n                      <el-col :span=\"4\" >\r\n                        <div class=\"cell-wrapper\">\r\n                          <div class=\"label\">滤网目数</div>\r\n                          <div class=\"content\">\r\n                            <el-input v-model=\"form.meshesNums\" size=\"mini\" />\r\n                          </div>\r\n                        </div>\r\n                      </el-col>\r\n                      <el-col :span=\"4\" >\r\n                        <span style=\"color: #F56C6C\">加*号的为关键工艺控制点</span>\r\n                      </el-col>\r\n                      <el-col :span=\"12\" ></el-col>\r\n                    </el-row>\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-tab-pane>\r\n        <el-tab-pane key=\"productivity\" label=\"人工/产能\" lazy name=\"productivity\">\r\n          <el-tooltip v-if=\"!readonly\" content=\"工艺带入设备\" >\r\n            <i @click=\"processToEquipment\" style=\"margin-right: 5px\" class=\"el-icon-refresh\" />\r\n          </el-tooltip>\r\n          <BcpProductivityArray :data-array=\"productivityArray\" :equipment-list=\"equipmentList\" />\r\n        </el-tab-pane>\r\n        <el-tab-pane key=\"bcp\" label=\"半成品检验标准\" lazy name=\"bcp\" >\r\n\r\n          <div class=\"table-wrapper\" >\r\n            <table class=\"base-table small-table\">\r\n              <tr>\r\n                <th style=\"width: 120px\">类型</th>\r\n                <th style=\"width: 120px\">检测项目</th>\r\n                <th style=\"width: 240px\">研发标准</th>\r\n                <th style=\"width: 320px\">标准值</th>\r\n              </tr>\r\n              <tr v-for=\"(item,index) in bcpArray\" :key=\"item.id\" :class=\"readonly?'mask':''\" >\r\n                <td>{{item.type}}</td>\r\n                <td>{{item.label}}</td>\r\n                <td>{{item.standard}}</td>\r\n                <td>{{item.standardVal}}</td>\r\n              </tr>\r\n            </table>\r\n          </div>\r\n        </el-tab-pane>\r\n        <el-tab-pane key=\"assess\" label=\"配制可行性评估\" lazy name=\"assess\" >\r\n          <ProductionPg :pg-tabs=\"findingArray\" :form=\"form\" />\r\n        </el-tab-pane>\r\n        <el-tab-pane key=\"stability\" label=\"产品稳定性报告\" lazy name=\"stability\" >\r\n          <StabilityList v-if=\"form.labCode\" :lab-no=\"form.labCode\" />\r\n        </el-tab-pane>\r\n        <el-tab-pane key=\"new\" label=\"新品培训纪要\" lazy name=\"new\" >\r\n          <el-row :gutter=\"20\" >\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"标样确认\" prop=\"byQr\">\r\n                <el-input v-model=\"pxJson.byQr\" />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"标样状态\" prop=\"byStatus\">\r\n                <el-input v-model=\"pxJson.byStatus\" />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\" >\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"工程师\" prop=\"gxs\" >\r\n                <el-input v-model=\"pxJson.gxs\" />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"签样时间\" prop=\"jyTime\">\r\n                <el-date-picker\r\n                  clearable\r\n                  v-model=\"pxJson.jyTime\"\r\n                  type=\"datetime\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-form-item label=\"质量建议\" prop=\"zlJy\">\r\n            <el-input v-model=\"pxJson.zlJy\" type=\"textarea\" autosize />\r\n          </el-form-item>\r\n          <el-form-item label=\"中试结果及处理方案\" label-width=\"150px\" prop=\"clFa\">\r\n            <el-input v-model=\"pxJson.clFa\" type=\"textarea\" autosize />\r\n          </el-form-item>\r\n          <el-form-item label=\"产品介绍\" prop=\"cpJs\">\r\n            <el-input v-model=\"pxJson.cpJs\" type=\"textarea\" autosize />\r\n          </el-form-item>\r\n          <el-form-item label=\"产品特性\" prop=\"cpTx\">\r\n            <el-input v-model=\"pxJson.cpTx\" type=\"textarea\" autosize />\r\n          </el-form-item>\r\n          <el-form-item label=\"特别原料\" prop=\"tbYl\">\r\n            <el-input v-model=\"pxJson.tbYl\" type=\"textarea\" autosize />\r\n          </el-form-item>\r\n          <el-form-item label=\"重点工艺注意事项\" label-width=\"150px\" prop=\"zySx\">\r\n            <el-input v-model=\"pxJson.zySx\" type=\"textarea\" autosize />\r\n          </el-form-item>\r\n          <el-form-item label=\"工程师建议\" prop=\"jy\">\r\n            <el-input v-model=\"pxJson.jy\" type=\"textarea\" autosize />\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"兼容性报告\" prop=\"files\">\r\n            <FileUpload v-model=\"files\" :readonly=\"readonly\" />\r\n          </el-form-item>\r\n\r\n          <el-divider content-position=\"left\" >参与培训人员</el-divider>\r\n\r\n          <div class=\"table-wrapper\" >\r\n            <table class=\"base-table small-table\" >\r\n              <tr>\r\n                <th style=\"width: 50px\" >\r\n                  <i class=\"el-icon-circle-plus-outline\" @click=\"addPxItem()\" />\r\n                </th>\r\n                <th style=\"width: 120px\" >部门</th>\r\n                <th style=\"width: 120px\" >姓名</th>\r\n                <th style=\"width: 120px\" >是否理解</th>\r\n                <th style=\"width: 180px\" >合理建议</th>\r\n                <th >评估改善</th>\r\n              </tr>\r\n              <tr v-for=\"(item,index) in pxArray\" :key=\"index\" >\r\n                <td>\r\n                  <i class=\"el-icon-remove-outline\" @click=\"delItem(pxArray,index)\" />\r\n                </td>\r\n                <td>\r\n                  <el-input v-model=\"item.dept\" size=\"mini\" />\r\n                </td>\r\n                <td>\r\n                  <el-input v-model=\"item.name\" size=\"mini\" />\r\n                </td>\r\n                <td>\r\n                  <el-select v-model=\"item.isLj\" size=\"mini\" >\r\n                    <el-option\r\n                      v-for=\"dict in whetherOptions\"\r\n                      :key=\"dict.value\"\r\n                      :label=\"dict.label\"\r\n                      :value=\"dict.value\"\r\n                    />\r\n                  </el-select>\r\n                </td>\r\n                <td>\r\n                  <el-input v-model=\"item.suggestion\" size=\"mini\" type=\"textArea\" autosize />\r\n                </td>\r\n                <td>\r\n                  <el-input v-model=\"item.pg\" size=\"mini\" type=\"textArea\" autosize />\r\n                </td>\r\n              </tr>\r\n            </table>\r\n          </div>\r\n\r\n          <el-form-item label=\"培训总结\" prop=\"summary\">\r\n            <el-input v-model=\"pxJson.summary\" type=\"textarea\" autosize />\r\n          </el-form-item>\r\n\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n\r\n    </el-form>\r\n    <div v-if=\"!readonly\" class=\"dialog-footer\" style=\"margin-top: 20px\">\r\n      <el-button type=\"primary\" @click=\"submitForm\" size=\"mini\" :loading=\"btnLoading\" >确 定</el-button>\r\n      <el-button @click=\"cancel\" size=\"mini\" >取 消</el-button>\r\n    </div>\r\n\r\n    <el-dialog :fullscreen=\"fullscreenFlag\" :visible.sync=\"open\" width=\"1200px\" :close-on-click-modal=\"false\" append-to-body>\r\n      <div class=\"dialog-title\" slot=\"title\">\r\n        <span>{{ title }}</span>\r\n        <el-button @click=\"fullscreenFlag = !fullscreenFlag\" type=\"text\"\r\n                   :icon=\"fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'\"/>\r\n      </div>\r\n      <MakeUpBSave\r\n        ref=\"makeUpBSave\"\r\n        :readonly=\"readonly\"\r\n        :equipment-list=\"equipmentList\"\r\n        @close=\"close\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog :visible.sync=\"textOpen\" width=\"600px\" :close-on-click-modal=\"false\" append-to-body >\r\n      <el-form ref=\"stepForm\" :model=\"stepForm\" :rules=\"stepRules\" size=\"mini\" label-width=\"100px\" >\r\n        <el-form-item label=\"设备\" prop=\"equipmentId\" >\r\n          <el-select v-model=\"stepForm.equipmentId\" filterable size=\"mini\" style=\"width: 280px\" >\r\n            <el-option\r\n              v-for=\"e in equipmentList\"\r\n              :key=\"e.id\"\r\n              :value=\"e.id\"\r\n              :label=\"e.name\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"培训总结\" prop=\"processText\" >\r\n          <el-input v-model=\"stepForm.processText\" type=\"textarea\" autosize />\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div class=\"dialog-footer\" style=\"margin-top: 20px\" >\r\n        <el-button type=\"primary\" @click=\"buildStepArray\" size=\"mini\" >确 定</el-button>\r\n        <el-button @click=\"textOpen = false\" size=\"mini\" >取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n<script>\r\nimport { addMakeUp, getMakeUp, updateMakeUp,countMakeUpByLabCode } from \"@/api/sop/makeUp\";\r\nimport { getProjectByNo, } from \"@/api/project/project\";\r\nimport {allFormula, getFormula} from \"@/api/software/formula\";\r\nimport { allMaterialFormula } from \"@/api/software/materialFormula\";\r\nimport { allEquipment } from \"@/api/production/equipment\";\r\nimport ProjectBaseView from \"@/views/project/project/base.vue\";\r\nimport ProductionPg from \"@/views/sop/makeUp/productionPg.vue\";\r\nimport StabilityList from \"@/views/rd/stability/list.vue\";\r\nimport BcpProductivityArray from \"@/views/sop/makeUp/bcpProductivityArray.vue\";\r\nimport MakeUpBSave from \"@/views/sop/makeUpB/save.vue\";\r\n\r\nexport default {\r\n  name: \"makeUpSave\",\r\n  components: {\r\n    MakeUpBSave,\r\n    BcpProductivityArray,\r\n    StabilityList,\r\n    ProductionPg,\r\n    ProjectBaseView\r\n  },\r\n  props: {\r\n    readonly: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  computed: {\r\n    sumPercentage() {\r\n      let sum = this.$big(0)\r\n      for (const item of this.materialArray) {\r\n        if(item.percentage) {\r\n          sum = this.add(sum,item.percentage)\r\n        }\r\n      }\r\n      return sum.toNumber()\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      btnLoading: false,\r\n      open: false,\r\n      textOpen: false,\r\n      fullscreenFlag: false,\r\n      title: '',\r\n      form: {},\r\n      rules: {\r\n        projectNo: [\r\n          {required: true,message: '请输入正确的项目编号'},\r\n        ],\r\n        nums: [\r\n          {required: true,message: '请输入配置量'},\r\n        ],\r\n        formulaId: [\r\n          {required: true,message: '请选择配方编码'},\r\n        ],\r\n        type: [\r\n          {required: true,message: '请选择工艺类型'},\r\n        ],\r\n      },\r\n      projectList: [],\r\n      formulaList: [],\r\n      currentProject: {},\r\n      currentTab: 'splitting',\r\n      equipmentList: [],\r\n      aidedEquipmentList: [],\r\n      materialArray: [],\r\n      processArray: [],\r\n      productivityArray: [],\r\n      findingArray: [],\r\n      equipmentTypeOptions: [],\r\n      cycleOptions: [],\r\n      riskLevelOptions: [],\r\n      bcpCode: null,\r\n      currentDiagram: {},\r\n      bcpArray: [],\r\n      conclusionOptions: [\r\n        {label: '通过',value: '0'},\r\n        {label: '不通过',value: '1'},\r\n      ],\r\n      erpDataList: [],\r\n      typeOptions: [\r\n        {label: '标准工艺',value: '0'},\r\n        {label: '大货工艺',value: '1'},\r\n      ],\r\n      currentRow: {},\r\n      pxJson: {},\r\n      pxArray: [],\r\n      whetherOptions: [\r\n        {label: '是',value: '1'},\r\n        {label: '否',value: '0'},\r\n      ],\r\n      files: [],\r\n      stepForm: {},\r\n      stepRules: {\r\n        processText: [\r\n          {required: true,message: '请输入工艺'},\r\n        ],\r\n        equipmentId: [\r\n          {required: true,message: '请选择设备'},\r\n        ],\r\n      },\r\n    }\r\n  },\r\n  async created() {\r\n    this.equipmentList = await allEquipment({typeId: 59})\r\n  },\r\n  methods: {\r\n    resetStepForm() {\r\n      this.stepForm = {\r\n        processText: null,\r\n        equipmentId: null,\r\n      }\r\n      this.resetForm(\"stepForm\")\r\n    },\r\n    showDialog(row) {\r\n      this.currentRow = row\r\n      this.resetStepForm()\r\n      this.textOpen = true\r\n    },\r\n    buildStepArray() {\r\n      const row = this.currentRow\r\n      const form = Object.assign({},this.stepForm)\r\n\r\n      const array = this.splitSteps(form.processText)\r\n      for (const item of array) {\r\n        this.addArgsItem(row.equipmentArgs,row.materialArray,item,form.equipmentId)\r\n      }\r\n      this.textOpen = false\r\n    },\r\n    //切分步骤\r\n    splitSteps(text) {\r\n      return text.split('\\n').reduce((acc, line) => {\r\n        const trimmedLine = line.trim();\r\n        if (!trimmedLine) return acc; // 跳过空行\r\n\r\n        if (/^(\\d+\\.|★\\d+\\.)/.test(trimmedLine)) {\r\n          // 新步骤开始（以数字或★数字开头）\r\n          acc.push(trimmedLine);\r\n        } else if (acc.length > 0) {\r\n          // 延续上一步骤的内容\r\n          acc[acc.length - 1] += '\\n' + trimmedLine;\r\n        }\r\n        return acc;\r\n      }, []);\r\n    },\r\n    async close(id) {\r\n      this.open = false\r\n    },\r\n    async showBProcess(row) {\r\n      this.currentRow = row\r\n      this.title = row.materialCode\r\n      this.open = true\r\n      await this.$nextTick()\r\n      const makeUpBSave = this.$refs.makeUpBSave\r\n      if(makeUpBSave) {\r\n        makeUpBSave.reset()\r\n        makeUpBSave.form.makeUpId = this.form.id\r\n        makeUpBSave.form.labCode = row.materialCode\r\n        await makeUpBSave.init(row.materialCode)\r\n      }\r\n    },\r\n    async processToEquipment() {\r\n      const processArray = this.processArray\r\n      const productivityArray = this.productivityArray\r\n      const equipmentIdSet = new Set()\r\n      for (const p of processArray) {\r\n        for (const e of p.equipmentArgs) {\r\n          if(e.equipmentId) {\r\n            equipmentIdSet.add(e.equipmentId)\r\n          }\r\n        }\r\n      }\r\n      for (const equipmentId of equipmentIdSet) {\r\n        if(!productivityArray.map(i=>i.equipmentId).includes(equipmentId)) {\r\n          productivityArray.push({\r\n            equipmentId,\r\n            weight: undefined,\r\n            max: undefined,\r\n            min: undefined,\r\n            personNums: undefined,\r\n            hours: undefined,\r\n            productivity: 0,\r\n            hourRate: 0,\r\n          })\r\n        }\r\n      }\r\n    },\r\n    async searchFormulaList() {\r\n      this.loading = true\r\n      const laboratoryCode = this.form.labCode\r\n      const formulaList = await allFormula({laboratoryCode})\r\n      this.formulaList = formulaList\r\n\r\n      const res = await countMakeUpByLabCode({labCode: laboratoryCode})\r\n      if(res.code === 200 && res.data) {\r\n        this.msgError('实验室编码已存在!')\r\n      }\r\n      this.loading = false\r\n    },\r\n    computeNums() {\r\n      if(this.form.nums) {\r\n        for (const o of this.materialArray) {\r\n          if(this.form.nums && o.percentage) {\r\n            o.nums = this.multiply(this.form.nums,this.divide(o.percentage,100)).toNumber()\r\n          } else {\r\n            o.nums = 0\r\n          }\r\n        }\r\n      }\r\n    },\r\n    addArgsItem(array,materialArray,process,equipmentId) {\r\n      array.push({\r\n        step: array.length+1,\r\n        materialIds: materialArray.map(i=>i.materialId),\r\n        equipmentId,\r\n        process,\r\n        keyPoints: null,\r\n        temperature: null,\r\n        mpa: null,\r\n        jz: null,\r\n        jzTime: null,\r\n        nj: null,\r\n        njTime: null,\r\n        wj: null,\r\n        zk: null,\r\n        time: null,\r\n      })\r\n    },\r\n    delItem(array,i) {\r\n      array.splice(i,1)\r\n    },\r\n    addPxItem() {\r\n      this.pxArray.push({\r\n        dept: null,\r\n        name: null,\r\n        isLj: null,\r\n        suggestion: null,\r\n        pg: null,\r\n      })\r\n    },\r\n    async generateProcessArray() {\r\n      this.btnLoading = true\r\n      const tempArray = []\r\n      for (const m of this.materialArray) {\r\n        for (const sub of m.array) {\r\n          tempArray.push({\r\n            materialId: m.materialId,\r\n            erpCode: m.erpCode,\r\n            materialCode: m.materialCode,\r\n            percentage: sub.percentage,\r\n            subItem: sub.subItem,\r\n            category: sub.subItem.replace(/\\s*/g,\"\").substring(0, 1),\r\n            firstPercentage: null,\r\n            processArray: [],\r\n          })\r\n        }\r\n      }\r\n      const categorySet = new Set()\r\n      for (const temp of tempArray) {\r\n        categorySet.add(temp.category)\r\n      }\r\n      const processArray = []\r\n      for (const category of categorySet) {\r\n        const materialArray = tempArray.filter(i=>i.category === category).sort((a,b)=> a.subItem.replace(/[a-zA-Z]/g, '') - b.subItem.replace(/[a-zA-Z]/g, ''))\r\n        const equipmentArgs = []\r\n        this.addArgsItem(equipmentArgs,materialArray)\r\n        processArray.push({\r\n          category,\r\n          materialArray,\r\n          equipmentArgs,\r\n          processDesc: null,\r\n          keyPoints: null,\r\n          aidedArray: [],\r\n          files:[],\r\n          materialIds: [],//工艺图中选中的物料\r\n        })\r\n      }\r\n      this.processArray = processArray.sort((a,b)=>a.category.localeCompare(b.category))\r\n      this.btnLoading = false\r\n    },\r\n    percentageStyle(item) {\r\n      let percentage = item.array.filter(i=>i.percentage).map(i=>i.percentage).reduce((a,b)=> this.add(a, b),this.$big(0))\r\n      if(percentage.toNumber() === item.percentage) {\r\n        return {\r\n          color: '#67C23A'\r\n        }\r\n      } else {\r\n        return {\r\n          color: '#F56C6C'\r\n        }\r\n      }\r\n    },\r\n    addSubItem(item) {\r\n      item.array.push({\r\n        percentage: null,\r\n        subItem: null,\r\n      })\r\n    },\r\n    delSubItem(item,i) {\r\n      item.array.splice(i,1)\r\n    },\r\n    async formulaChange() {\r\n      const form = this.form\r\n      const formulaId = form.formulaId\r\n      if(formulaId) {\r\n        const formulaRes = await getFormula(formulaId)\r\n        if(formulaRes.code === 200) {\r\n          const data = formulaRes.data\r\n\r\n          form.formulaCode = data.formulaCode\r\n          form.labCode = data.laboratoryCode\r\n          form.customerName = data.customerName\r\n          form.productName = data.productName\r\n          form.projectNo = data.projectNo\r\n\r\n          if(data.jcXmJson) {\r\n            const bcpArray = JSON.parse(data.jcXmJson)\r\n            this.bcpArray = this.bcpArray.filter(i=>bcpArray.map(b=>b.id).includes(i.id))//过滤研发中已经删除的半成品\r\n            for (const bcp of bcpArray) {\r\n              if(!this.bcpArray.map(i=>i.id).includes(bcp.id)) {\r\n                bcp.zsStandard = null\r\n                bcp.inspectionArray = []\r\n                this.bcpArray.push(bcp)//补充研发新增的\r\n              }\r\n            }\r\n          }\r\n        }\r\n        const array = await allMaterialFormula({formulaId})\r\n        const materialIdSet = new Set()\r\n        for (const item of array) {\r\n          materialIdSet.add(item.materialId)\r\n        }\r\n        const materialArray = []\r\n        for (const materialId of materialIdSet) {\r\n          const arr = array.filter(i=> i.materialId === materialId)\r\n          if(arr && arr[0]) {\r\n            const o = {\r\n              materialId: arr[0].materialId,\r\n              erpCode: arr[0].erpCode,\r\n              materialCode: arr[0].materialCode,\r\n            }\r\n            const subArray = arr.map(i=> {\r\n              return {\r\n                percentage: i.percentage,\r\n                subItem: i.subItem,\r\n              }\r\n            })\r\n            o.array = subArray\r\n            o.percentage = subArray.map(i=>i.percentage).reduce((a,b)=> this.add(a, b),0).toNumber()\r\n            materialArray.push(o)\r\n          }\r\n        }\r\n        this.materialArray = materialArray\r\n\r\n        this.computeNums()\r\n      }\r\n    },\r\n    async searchProject() {\r\n      this.btnLoading = true\r\n      let projectNo = this.form.projectNo\r\n      if(projectNo) {\r\n        projectNo = projectNo.replace(/\\s*/g,\"\")\r\n        const res = await getProjectByNo(projectNo)\r\n        if(res.code === 200) {\r\n          const project = res.data\r\n          if(project) {\r\n            this.currentProject = project\r\n            this.form.projectId = project.id\r\n          }\r\n        }\r\n      }\r\n      this.btnLoading = false\r\n    },\r\n    cancel() {\r\n      this.$parent.$parent.open = false;\r\n      this.reset();\r\n    },\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        projectId: null,\r\n        erpCode: null,\r\n        formulaId: null,\r\n        formulaCode: null,\r\n        diagramId: null,\r\n        projectNo: null,\r\n        nums: 100,\r\n        type: '0',\r\n        temperature: null,\r\n        meshesNums: null,\r\n        labCode: null,\r\n        pxUser: null,\r\n        pxDate: null,\r\n        pzUser: null,\r\n        pzDate: null,\r\n        qcCode: null,\r\n      }\r\n      this.resetForm(\"form\")\r\n      this.currentProject = {}\r\n      this.formulaList = []\r\n      this.materialArray = []\r\n      this.processArray = []\r\n      this.productivityArray = []\r\n      this.findingArray = []\r\n      this.bcpArray = []\r\n      this.bcpCode = null\r\n      this.erpDataList = []\r\n      this.pxJson = {\r\n        byQr: null,\r\n        byStatus: null,\r\n        jyTime: null,\r\n        zlJy: null,\r\n        clFa: null,\r\n        cpJs: null,\r\n        cpTx: null,\r\n        tbYl: null,\r\n        zySx: null,\r\n        jy: null,\r\n        gxs: null,\r\n        summary: null,\r\n      }\r\n      this.pxArray = []\r\n      this.files = []\r\n    },\r\n    async init(id) {\r\n      const res = await getMakeUp(id)\r\n      const form = res.data\r\n\r\n      if(form.materialArray) {\r\n        this.materialArray = JSON.parse(form.materialArray)\r\n      }\r\n\r\n      if(form.processArray) {\r\n        this.processArray = JSON.parse(form.processArray)\r\n      }\r\n\r\n      if(form.productivityArray) {\r\n        this.productivityArray = JSON.parse(form.productivityArray)\r\n      }\r\n\r\n      if(form.bcpArray) {\r\n        this.bcpArray = JSON.parse(form.bcpArray)\r\n      }\r\n\r\n      if(form.findingArray) {\r\n        this.findingArray = JSON.parse(form.findingArray)\r\n      }\r\n\r\n      if(form.pxJson) {\r\n        this.pxJson = JSON.parse(form.pxJson)\r\n      }\r\n\r\n      if(form.pxArray) {\r\n        this.pxArray = JSON.parse(form.pxArray)\r\n      }\r\n\r\n      if(form.files) {\r\n        this.files = JSON.parse(form.files)\r\n      }\r\n\r\n      this.form = form\r\n      await this.searchProject()\r\n\r\n    },\r\n    async submitForm() {\r\n      await this.$refs[\"form\"].validate()\r\n      let form = Object.assign({},this.form)\r\n\r\n      for (const m of this.materialArray) {\r\n        let percentage = m.array.filter(i=>i.percentage).map(i=>i.percentage).reduce((a,b)=> this.add(a, b),0)\r\n        if(percentage.toNumber() !== m.percentage) {\r\n          this.msgError('配方比例不一致,请修改后提交')\r\n          return\r\n        }\r\n      }\r\n\r\n      for (const item of this.productivityArray) {\r\n        if(!item.equipmentId) {\r\n          this.msgError('请选择设备')\r\n          return\r\n        }\r\n        if(!item.weight) {\r\n          this.msgError('请输入标准配制量')\r\n          return\r\n        }\r\n        if(!item.personNums) {\r\n          this.msgError('请输入标准人数')\r\n          return\r\n        }\r\n        if(!item.hours) {\r\n          this.msgError('请输入标准工时')\r\n          return\r\n        }\r\n      }\r\n\r\n      form.materialArray = JSON.stringify(this.materialArray)\r\n      form.processArray = JSON.stringify(this.processArray)\r\n      form.productivityArray = JSON.stringify(this.productivityArray)\r\n      form.bcpArray = JSON.stringify(this.bcpArray)\r\n      form.findingArray = JSON.stringify(this.findingArray)\r\n      form.pxJson = JSON.stringify(this.pxJson)\r\n      form.pxArray = JSON.stringify(this.pxArray)\r\n      form.files = JSON.stringify(this.files)\r\n\r\n      if (form.id != null) {\r\n        try {\r\n          this.btnLoading = true\r\n          await updateMakeUp(form)\r\n          this.btnLoading = false\r\n          if(res.code === 200) {\r\n            this.msgSuccess(\"操作成功\")\r\n          }\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      } else {\r\n        try {\r\n          this.btnLoading = true\r\n          const res = await addMakeUp(form)\r\n          this.btnLoading = false\r\n          if(res.code === 200) {\r\n            this.msgSuccess(\"操作成功\")\r\n            await this.init(res.data.id)\r\n          }\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n\r\n.diagram-wrapper {\r\n  height: 0;\r\n  padding-top: 100%;\r\n  background-size: 100% auto;\r\n  background-position: center center;\r\n  background-repeat: no-repeat;\r\n  position: relative;\r\n\r\n  .pointer {\r\n    position: absolute;\r\n    width: 20px;\r\n    height: 20px;\r\n    border-radius: 50%;\r\n    border: 1px solid #DCDFE6;\r\n    cursor: pointer;\r\n    text-align: center;\r\n    line-height: 20px;\r\n    font-size: 12px;\r\n    white-space: nowrap;\r\n  }\r\n\r\n}\r\n\r\n.table-wrapper {\r\n  max-height: 90vh;\r\n\r\n  .base-table {\r\n    thead {\r\n      position: sticky;\r\n      top: 0;\r\n      z-index: 3;\r\n    }\r\n\r\n    .nth1 {\r\n      position: sticky;\r\n      left: 0px;\r\n      z-index: 1;\r\n    }\r\n\r\n    .nth2 {\r\n      position: sticky;\r\n      left: 120px;\r\n      z-index: 1;\r\n    }\r\n\r\n    .nth3 {\r\n      position: sticky;\r\n      left: 240px;\r\n      z-index: 1;\r\n    }\r\n\r\n    .nth4 {\r\n      position: sticky;\r\n      left: 360px;\r\n      z-index: 1;\r\n    }\r\n\r\n    .nth5 {\r\n      position: sticky;\r\n      left: 480px;\r\n      z-index: 1;\r\n    }\r\n\r\n    .nth6 {\r\n      position: sticky;\r\n      left: 600px;\r\n      z-index: 1;\r\n    }\r\n\r\n    .nth7 {\r\n      position: sticky;\r\n      left: 720px;\r\n      z-index: 1;\r\n    }\r\n\r\n    th:nth-child(-n+7) {\r\n      background: #f8f8f9;\r\n      color: #515a6e;\r\n    }\r\n\r\n    td:nth-child(-n+7) {\r\n      background: #fff;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}