import request from '@/utils/request'

// 查询微生物检验记录列表
export function listMicrobeInspection(query) {
  return request({
    url: '/qc/microbeInspection/list',
    method: 'get',
    params: query
  })
}

// 查询微生物检验记录详细
export function getMicrobeInspection(id) {
  return request({
    url: '/qc/microbeInspection/' + id,
    method: 'get'
  })
}

// 新增微生物检验记录
export function addMicrobeInspection(data) {
  return request({
    url: '/qc/microbeInspection',
    method: 'post',
    data: data
  })
}

// 修改微生物检验记录
export function updateMicrobeInspection(data) {
  return request({
    url: '/qc/microbeInspection',
    method: 'put',
    data: data
  })
}

// 删除微生物检验记录
export function delMicrobeInspection(id) {
  return request({
    url: '/qc/microbeInspection/' + id,
    method: 'delete'
  })
}

// 导出微生物检验记录
export function exportMicrobeInspection(query) {
  return request({
    url: '/qc/microbeInspection/export',
    method: 'get',
    params: query
  })
}

export function allMicrobeInspection(query) {
  return request({
    url: '/qc/microbeInspection/all',
    method: 'get',
    params: query
  })
}

export function generateMicrobeInspection(query) {
  return request({
    url: '/qc/microbeInspection/generateInspection',
    method: 'get',
    params: query
  })
}

export function updateBatchMicrobeInspection(data) {
  return request({
    url: '/qc/microbeInspection/editBatch',
    method: 'put',
    data: data
  })
}

export function delMicrobeInspectionByParams(data) {
  return request({
    url: '/qc/microbeInspection/delMicrobeInspection',
    method: 'delete',
    data: data
  })
}
