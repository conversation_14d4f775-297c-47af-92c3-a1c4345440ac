import request from '@/utils/request'

// 查询项目列表
export function listProject(query) {
  return request({
    url: '/project/project/list',
    method: 'get',
    params: query
  })
}

// 查询项目列表
export function listAllProject(query) {
  return request({
    url: '/project/project/listAll',
    method: 'get',
    params: query
  })
}

// 查询项目详细
export function getProject(id) {
  return request({
    url: '/project/project/' + id,
    method: 'get'
  })
}

export function getProjectDetailsById(id) {
  return request({
    url: '/project/project/detail/' + id,
    method: 'get'
  })
}

// 查询项目详细
export function getReloadProject(query) {
  return request({
    url: '/project/beforeProject/getReloadProjectDataList',
    method: 'get',
    params: query
  })
}


export function getProjectSubitFormInspectionAndFilingData(query) {
  return request({
    url: '/project/beforeProject/getProjectSubitFormInspectionAndFilingData',
    method: 'get',
    params: query
  })
}


// 查询立项前项目产品列表信息
export function getBeforeProjectDataList(query) {
  return request({
    url: '/project/beforeProject/getBeforeProjectDataList',
    method: 'get',
    params: query
  })
}
// 查询立项前项目子名称信息
export function getBeforeProjectItemNameDataList(query) {
  return request({
    url: '/project/beforeProject/getBeforeProjectItemNameDataList',
    method: 'get',
    params: query
  })
}

// 查询备案信息
export function getProjectIcpDataInfo(id) {
  return request({
    url: '/project/project/getProjectIcpDataInfo/' + id,
    method: 'get'
  })
}

// 新增项目
export function addProject(data) {
  return request({
    url: '/project/project',
    method: 'post',
    data: data
  })
}

// 修改项目
export function updateProject(data) {
  return request({
    url: '/project/project',
    method: 'put',
    data: data
  })
}

// 修改项目
export function updateProjectStatus(data) {
  return request({
    url: '/project/project/editStatus',
    method: 'put',
    data: data
  })
}

//重启项目
export function reloadProject(data) {
  return request({
    url: '/project/project/reloadProject',
    method: 'put',
    data: data
  })
}
// 修改变更项目
export function updateProjectExchangeStatus(data) {
  return request({
    url: '/project/project/editExchangeStatus',
    method: 'put',
    data: data
  })
}

// 修改项目
export function changeProjectApplayInfo(data) {
  return request({
    url: '/project/project/changeProjectApplayInfo',
    method: 'post',
    data: data
  })
}

// 修改项目客户
export function changeProjectCustomerInfo(data) {
  return request({
    url: '/project/project/changeProjectCustomerInfo',
    method: 'post',
    data: data
  })
}

// 删除项目
export function delProject(id) {
  return request({
    url: '/project/project/' + id,
    method: 'delete'
  })
}

// 导出项目
export function exportProject(query) {
  return request({
    url: '/project/project/export',
    method: 'get',
    params: query
  })
}

// 导出项目
export function handleExportProject(query) {
  return request({
    url: '/project/project/exportproject',
    method: 'get',
    params: query
  })
}

// 导出项目时效表
export function handleExportProjectXmsxb(query) {
  return request({
    url: '/project/project/exportProjectXmsxb',
    method: 'get',
    params: query
  })
}

// 导出项目时效表
export function handleExportProjectOffer(query) {
  return request({
    url: '/project/project/exportProjectOffer',
    method: 'get',
    params: query
  })
}

export function projectAll(query) {
  return request({
    url: '/project/project/baseAll',
    method: 'get',
    params: query
  })
}

export function liteListProject(query) {
  return request({
    url: '/project/project/liteList',
    method: 'get',
    params: query
  })
}

export function allByItemType(query) {
  return request({
    url: '/project/project/allByItemType',
    method: 'get',
    params: query
  })
}

export function itemNames(id) {
  return request({
    url: '/project/project/itemNames/' + id,
    method: 'get'
  })
}

export function charts(id) {
  return request({
    url: '/project/project/charts/' + id,
    method: 'get'
  })
}

export function editItemStatus(data) {
  return request({
    url: '/project/project/editItemStatus',
    method: 'put',
    data: data
  })
}

export function projectBaseAll(query) {
  return request({
    url: '/project/project/baseAll',
    method: 'get',
    params: query
  })
}

export function projectAllByOrderType(query) {
  return request({
    url: '/project/project/allByOrderType',
    method: 'get',
    params: query
  })
}

export function groupByStatusAndMonthProject(query) {
  return request({
    url: '/project/project/groupByStatusAndMonth',
    method: 'get',
    params: query
  })
}

export function progressCountProject(query) {
  return request({
    url: '/project/project/progressCount',
    method: 'get',
    params: query
  })
}

export function allWorkTypeEquipmentProject(query) {
  return request({
    url: '/project/project/allWorkTypeEquipmentProject',
    method: 'get',
    params: query
  })
}

export function listByCustomerOrderNo(query) {
  return request({
    url: '/project/project/listByCustomerOrderNo',
    method: 'get',
    params: query
  })
}

//查询项目下的二级类型数量
export function itemCount(query) {
  return request({
    url: '/project/project/itemCount',
    method: 'get',
    params: query
  })
}

export function listProjectByItemTypes(query) {
  return request({
    url: '/project/project/listByItemTypes',
    method: 'get',
    params: query
  })
}

// 导出项目/客户状态更改记录
export function exportChange(query) {
  return request({
    url: '/project/change/export',
    method: 'get',
    params: query
  })
}

export function currentErpPrice(query) {
  return request({
    url: '/project/project/currentErpPrice',
    method: 'get',
    params: query
  })
}

// 配方-查询项目列表 进行中
export function formualList(query) {
  return request({
    url: '/project/project/formualProjectList',
    method: 'get',
    params: query
  })
}

// 配方-查询项目详情
export function formualProjectDetail(query) {
  return request({
    url: '/project/project/formualProjectDetail',
    method: 'get',
    params: query
  })
}

export function getProjectByNo(projectNo) {
  return request({
    url: '/project/project/getByProjectNo/' + projectNo,
    method: 'get'
  })
}

export function listProjectByTypes(query) {
  return request({
    url: '/project/project/listByTypes',
    method: 'get',
    params: query
  })
}

export function getProductsByProjectId(id) {
  return request({
    url: '/project/project/productsByProjectId/' + id,
    method: 'get'
  })
}

export function saveProduct(data) {
  return request({
    url: '/project/project/saveProduct',
    method: 'put',
    data: data
  })
}

export function refreshItemName() {
  return request({
    url: '/project/project/refreshItemName',
    method: 'get',
  })
}
