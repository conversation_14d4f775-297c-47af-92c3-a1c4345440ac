import request from '@/utils/request'

// 查询检测项目列表
export function listQcJcxm(query) {
  return request({
    url: '/qc/qcJcxm/list',
    method: 'get',
    params: query
  })
}

// 查询检测项目详细
export function getQcJcxm(id) {
  return request({
    url: '/qc/qcJcxm/' + id,
    method: 'get'
  })
}

// 新增检测项目
export function addQcJcxm(data) {
  return request({
    url: '/qc/qcJcxm',
    method: 'post',
    data: data
  })
}

// 修改检测项目
export function updateQcJcxm(data) {
  return request({
    url: '/qc/qcJcxm',
    method: 'put',
    data: data
  })
}

// 删除检测项目
export function delQcJcxm(id) {
  return request({
    url: '/qc/qcJcxm/' + id,
    method: 'delete'
  })
}

// 导出检测项目
export function exportQcJcxm(query) {
  return request({
    url: '/qc/qcJcxm/export',
    method: 'get',
    params: query
  })
}

export function allJcxm(query) {
  return request({
    url: '/qc/qcJcxm/all',
    method: 'get',
    params: query
  })
}

export function queryInspectionItems(query) {
  return request({
    url: '/qc/qcJcxm/queryInspectionItems',
    method: 'get',
    params: query
  })
}


export function queryInvmbDetail(code,erpType) {
  return request({
    url: '/qc/qcJcxm/queryInvmbDetail/'+code+'/'+erpType,
    method: 'get'
  })
}

// 查询项目包材内容列表
export function listQcJcxmMaterialItem(query) {
  return request({
    url: '/qc/qcJcxm/materialList',
    method: 'get',
    params: query
  })
}
