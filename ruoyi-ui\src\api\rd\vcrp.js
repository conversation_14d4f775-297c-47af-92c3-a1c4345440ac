import request from '@/utils/request'

// 查询VCRP编码列表
export function listVcrp(query) {
  return request({
    url: '/rd/vcrp/list',
    method: 'get',
    params: query
  })
}

// 查询VCRP编码详细
export function getVcrp(id) {
  return request({
    url: '/rd/vcrp/' + id,
    method: 'get'
  })
}

// 新增VCRP编码
export function addVcrp(data) {
  return request({
    url: '/rd/vcrp',
    method: 'post',
    data: data
  })
}

// 修改VCRP编码
export function updateVcrp(data) {
  return request({
    url: '/rd/vcrp',
    method: 'put',
    data: data
  })
}

// 删除VCRP编码
export function delVcrp(id) {
  return request({
    url: '/rd/vcrp/' + id,
    method: 'delete'
  })
}

// 导出VCRP编码
export function exportVcrp(query) {
  return request({
    url: '/rd/vcrp/export',
    method: 'get',
    params: query
  })
}

export function allVcrp(query) {
  return request({
    url: '/rd/vcrp/all',
    method: 'get',
    params: query
  })
}

export function allCascadeVcrp(query) {
  return request({
    url: '/rd/vcrp/allCascade',
    method: 'get',
    params: query
  })
}
