import request from '@/utils/request'

// HR发送工资条列表
export function listHrPayStubs(query) {
  return request({
    url: '/hr/payStubs/hrList',
    method: 'get',
    params: query
  })
}
// 查询工资条列表
export function listPayStubs(query) {
  return request({
    url: '/hr/payStubs/list',
    method: 'get',
    params: query
  })
}


// 查询福利信息
export function queryBenefitInfo() {
  return request({
    url: '/hr/payStubs/queryBenefitInfo',
    method: 'get'
  })
}

// 查询工资条详细
export function getPayStubs(id) {
  return request({
    url: '/hr/payStubs/' + id,
    method: 'get'
  })
}
export function addPayStubs(data) {
  return request({
    url: '/hr/payStubs',
    method: 'post',
    data: data
  })
}

// 导出工资条
export function exportPayStubs(query) {
  return request({
    url: '/hr/payStubs/export',
    method: 'get',
    params: query
  })
}

export function getColumns1(query) {
  return request({
    url: '/index/getColumns',
    method: 'get',
    params: query
  })
}
