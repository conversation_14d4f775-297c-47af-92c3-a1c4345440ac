import request from '@/utils/request'

export function allProjectBeforeProduct(query) {
  return request({
    url: '/project/before/product/all',
    method: 'get',
    params: query
  })
}

export function allUnAuditedProjectBeforeProduct(query) {
  return request({
    url: '/project/before/product/allUnAudited',
    method: 'get',
    params: query
  })
}

export function getBeforeProjectProduct(id) {
  return request({
    url: '/project/before/product/' + id,
    method: 'get'
  })
}

export function updateBeforeProjectProduct(data) {
  return request({
    url: '/project/before/product',
    method: 'put',
    data: data
  })
}
