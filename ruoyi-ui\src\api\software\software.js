import request from '@/utils/request'

//获取配方列表
export function allFormula(query) {
  return request({
    url: '/software/allFormula',
    method: 'get',
    params: query
  })
}

//获取配方执行标准
export function allFormulaZxbz(query) {
  return request({
    url: '/software/allFormulaZxbz',
    method: 'get',
    params: query
  })
}

/** 获取送检配方表数据 */
export function queryInspectionFormulaData(query) {
  return request({
    url: '/software/queryInspectionFormulaData',
    method: 'get',
    params: query
  })
}

/** 获取配方含量信息 */
export function queryInspectionFormulaContentData(query) {
  return request({
    url: '/software/queryInspectionFormulaContentData',
    method: 'get',
    params: query
  })
}

/** 获取配方含量信息 */
export function queryInspectionFormulaDetailData(query) {
  return request({
    url: '/software/queryInspectionFormulaDetailData',
    method: 'get',
    params: query
  })
}

/** 获取配方文案花盒信息 */
export function queryInspectionFormulaWaBoxDetailData(query) {
  return request({
    url: '/software/queryInspectionFormulaWaBoxDetailData',
    method: 'get',
    params: query
  })
}

/** 获取功效列表数据 */
export function queryFormulaMaterialGxDataList(query) {
  return request({
    url: '/software/queryFormulaMaterialGxDataList',
    method: 'get',
    params: query
  })
}


/** 获取配方文案花盒信息 */
export function exportFormulaWaxcData(query) {
  return request({
    url: '/software/exportFormulaWaxcData',
    method: 'get',
    params: query
  })
}

/** 导出配方称量单信息 */
export function exportFormulaWeightData(query) {
  return request({
    url: '/software/exportFormulaWeightData',
    method: 'get',
    params: query
  })
}

/** 导出配方比较信息 */
export function exportFormulaCompareData(query) {
  return request({
    url: '/software/exportFormulaCompareData',
    method: 'get',
    params: query
  })
}

/** 导出配方价格信息 */
export function exportFormulaPriceData(query) {
  return request({
    url: '/software/exportFormulaPriceData',
    method: 'get',
    params: query
  })
}

/** 获取配方附件信息 */
export function queryInspectionFormulaFileData(query) {
  return request({
    url: '/software/queryInspectionFormulaFileData',
    method: 'get',
    params: query
  })
}

export function exportFormulaAqpgbg(data) {
  return request({
    url: '/software/exportFormulaAqpgbg',
    method: 'post',
    data
  })
}
export function exportFormulaAqpgbgDetail(data) {
  return request({
    url: '/software/exportFormulaAqpgbgDetail',
    method: 'post',
    data
  })
}

export function exportFormulaZxbz(data) {
  return request({
    url: '/software/exportFormulaZxbz',
    method: 'post',
    data
  })
}

export function queryInspectionFormulaContentByLabNoData(query) {
  return request({
    url: '/software/queryInspectionFormulaContentByLabNoData',
    method: 'get',
    params: query
  })
}
