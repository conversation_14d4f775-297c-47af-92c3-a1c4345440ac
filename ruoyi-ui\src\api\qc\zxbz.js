import request from '@/utils/request'

// 查询QC执行标准列表
export function listZxbz(query) {
  return request({
    url: '/qc/zxbz/list',
    method: 'get',
    params: query
  })
}

// 查询QC执行标准详细
export function getZxbz(id) {
  return request({
    url: '/qc/zxbz/' + id,
    method: 'get'
  })
}

// 新增QC执行标准
export function addZxbz(data) {
  return request({
    url: '/qc/zxbz',
    method: 'post',
    data: data
  })
}

// 修改QC执行标准
export function updateZxbz(data) {
  return request({
    url: '/qc/zxbz',
    method: 'put',
    data: data
  })
}

// 删除QC执行标准
export function delZxbz(id) {
  return request({
    url: '/qc/zxbz/' + id,
    method: 'delete'
  })
}

// 导出QC执行标准
export function exportZxbz(query) {
  return request({
    url: '/qc/zxbz/export',
    method: 'get',
    params: query
  })
}

export function allZxbz(query) {
  return request({
    url: '/qc/zxbz/all',
    method: 'get',
    params: query
  })
}
