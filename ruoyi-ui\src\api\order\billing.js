import request from '@/utils/request'

// 查询开票申请列表
export function listBilling(query) {
  return request({
    url: '/order/billing/list',
    method: 'get',
    params: query
  })
}

// 查询开票申请列表
export function listFinanceBilling(query) {
  return request({
    url: '/order/billing/financeList',
    method: 'get',
    params: query
  })
}

// 查询开票申请详细
export function getBilling(id) {
  return request({
    url: '/order/billing/' + id,
    method: 'get'
  })
}

// 新增开票申请
export function addBilling(data) {
  return request({
    url: '/order/billing',
    method: 'post',
    data: data
  })
}

// 修改开票申请
export function updateBilling(data) {
  return request({
    url: '/order/billing',
    method: 'put',
    data: data
  })
}

// 删除开票申请
export function delBilling(id) {
  return request({
    url: '/order/billing/' + id,
    method: 'delete'
  })
}

// 导出开票申请
export function exportBilling(query) {
  return request({
    url: '/order/billing/export',
    method: 'get',
    params: query
  })
}

export function allBilling(query) {
  return request({
    url: '/order/billing/all',
    method: 'get',
    params: query
  })
}

export function revokeBilling(data) {
  return request({
    url: '/order/billing/revoke',
    method: 'put',
    data: data
  })
}

export function revokeStockLogBilling(data) {
  return request({
    url: '/order/billing/revokeStockLog',
    method: 'put',
    data: data
  })
}
