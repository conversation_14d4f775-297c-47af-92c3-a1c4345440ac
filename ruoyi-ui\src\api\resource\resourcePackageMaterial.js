import request from '@/utils/request'

// 查询包材列表
export function listResourcePackageMaterial(query) {
  return request({
    url: '/resource/resourcePackageMaterial/list',
    method: 'get',
    params: query
  })
}

// 查询包材详细
export function getResourcePackageMaterial(id) {
  return request({
    url: '/resource/resourcePackageMaterial/' + id,
    method: 'get'
  })
}

// 查询原料详细
export function getResourceMaterialPrice(code) {
  return request({
    url: '/resource/resourcePackageMaterial/getMaterialPriceInfo/' + code,
    method: 'get'
  })
}
// 新增包材
export function addResourcePackageMaterial(data) {
  return request({
    url: '/resource/resourcePackageMaterial',
    method: 'post',
    data: data
  })
}

// 修改包材
export function updateResourcePackageMaterial(data) {
  return request({
    url: '/resource/resourcePackageMaterial',
    method: 'put',
    data: data
  })
}

// 删除包材
export function delResourcePackageMaterial(id) {
  return request({
    url: '/resource/resourcePackageMaterial/' + id,
    method: 'delete'
  })
}

// 导出包材
export function exportResourcePackageMaterial(query) {
  return request({
    url: '/resource/resourcePackageMaterial/export',
    method: 'get',
    params: query
  })
}
