import request from '@/utils/request'

// 查询文件清单列表
export function listCir(query) {
  return request({
    url: '/rd/cir/list',
    method: 'get',
    params: query
  })
}

// 查询文件清单详细
export function getCir(id) {
  return request({
    url: '/rd/cir/' + id,
    method: 'get'
  })
}

// 新增文件清单
export function addCir(data) {
  return request({
    url: '/rd/cir',
    method: 'post',
    data: data
  })
}

// 修改文件清单
export function updateCir(data) {
  return request({
    url: '/rd/cir',
    method: 'put',
    data: data
  })
}

// 删除文件清单
export function delCir(id) {
  return request({
    url: '/rd/cir/' + id,
    method: 'delete'
  })
}

// 导出文件清单
export function exportCirHistory(query) {
  return request({
    url: '/rd/cir/exportHistory',
    method: 'get',
    params: query
  })
}

export function allCir(query) {
  return request({
    url: '/rd/cir/all',
    method: 'get',
    params: query
  })
}

export function updateCirStatus(data) {
  return request({
    url: '/rd/cir/editStatus',
    method: 'put',
    data
  })
}

export function exportCirFilesInclude(query) {
  return request({
    url: '/rd/cir/exportFiles',
    method: 'get',
    params: query
  })
}
