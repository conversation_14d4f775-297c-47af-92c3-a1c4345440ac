import request from '@/utils/request'

// 查询料体配制损耗列表
export function listProjectOfferNrwPzLoss(query) {
  return request({
    url: '/project/projectOfferNrwPzLoss/list',
    method: 'get',
    params: query
  })
}


// 查询料体配制损耗列表
export function allItemList(query) {
  return request({
    url: '/project/projectOfferNrwPzLoss/allItemList',
    method: 'get',
    params: query
  })
}


// 查询料体配制损耗详细
export function getProjectOfferNrwPzLoss(id) {
  return request({
    url: '/project/projectOfferNrwPzLoss/' + id,
    method: 'get'
  })
}

// 新增料体配制损耗
export function addProjectOfferNrwPzLoss(data) {
  return request({
    url: '/project/projectOfferNrwPzLoss',
    method: 'post',
    data: data
  })
}

// 修改料体配制损耗
export function updateProjectOfferNrwPzLoss(data) {
  return request({
    url: '/project/projectOfferNrwPzLoss',
    method: 'put',
    data: data
  })
}

// 删除料体配制损耗
export function delProjectOfferNrwPzLoss(id) {
  return request({
    url: '/project/projectOfferNrwPzLoss/' + id,
    method: 'delete'
  })
}

// 导出料体配制损耗
export function exportProjectOfferNrwPzLoss(query) {
  return request({
    url: '/project/projectOfferNrwPzLoss/export',
    method: 'get',
    params: query
  })
}
