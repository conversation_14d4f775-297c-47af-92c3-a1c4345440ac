import request from '@/utils/request'

// 查询生产批基本数据列表
export function listWiplotBasis(query) {
  return request({
    url: '/mes/wiplotBasis/list',
    method: 'get',
    params: query
  })
}

// 查询生产批基本数据详细
export function getWiplotBasis(baselotno) {
  return request({
    url: '/mes/wiplotBasis/' + baselotno,
    method: 'get'
  })
}

// 新增生产批基本数据
export function addWiplotBasis(data) {
  return request({
    url: '/mes/wiplotBasis',
    method: 'post',
    data: data
  })
}

// 修改生产批基本数据
export function updateWiplotBasis(data) {
  return request({
    url: '/mes/wiplotBasis',
    method: 'put',
    data: data
  })
}

// 删除生产批基本数据
export function delWiplotBasis(baselotno) {
  return request({
    url: '/mes/wiplotBasis/' + baselotno,
    method: 'delete'
  })
}

// 导出生产批基本数据
export function exportWiplotBasis(query) {
  return request({
    url: '/mes/wiplotBasis/export',
    method: 'get',
    params: query
  })
}