{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\project\\project\\makeBom.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\project\\project\\makeBom.vue", "mtime": 1753954679647}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQge2FsbEJjfSBmcm9tICJAL2FwaS9wcm9qZWN0L2JjIjsNCmltcG9ydCBCY1NlbGVjdFRhYmxlIGZyb20gIkAvdmlld3MvcHJvamVjdC9wcm9qZWN0L2JjU2VsZWN0VGFibGUudnVlIjsNCmltcG9ydCBQcm9qZWN0Qm9tQ2hhcnRzIGZyb20gIkAvdmlld3MvcHJvamVjdC9wcm9qZWN0L2JvbUNoYXJ0cy52dWUiOw0KaW1wb3J0IHt1cGRhdGVQcm9kdWN0SXRlbX0gZnJvbSAiQC9hcGkvcHJvamVjdC9wcm9kdWN0SXRlbSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogInByb2plY3RNYXJrQm9tIiwNCiAgY29tcG9uZW50czogew0KICAgIFByb2plY3RCb21DaGFydHMsDQogICAgQmNTZWxlY3RUYWJsZQ0KICB9LA0KICBwcm9wczogew0KICAgIHByb2plY3RJZDogew0KICAgICAgdHlwZTogTnVtYmVyLA0KICAgICAgZGVmYXVsdDogbnVsbCwNCiAgICB9LA0KICAgIHByb2R1Y3ROYW1lOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnJywNCiAgICB9LA0KICAgIGJvbVRyZWU6IHsNCiAgICAgIHR5cGU6IEFycmF5LA0KICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgfSwNCiAgICBwcm9qZWN0UHJvZHVjdEl0ZW1JZDogew0KICAgICAgdHlwZTogTnVtYmVyLA0KICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgfSwNCiAgICBlcnBDb2RlOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICB9LA0KICAgIHJlYWRvbmx5OiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UsDQogICAgfSwNCiAgICBtYXRlcmlhbExpc3Q6IHsNCiAgICAgIHR5cGU6IEFycmF5LA0KICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgfSwNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgYnRuTG9hZGluZzogZmFsc2UsDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIGJvbU9wZW46IGZhbHNlLA0KICAgICAgZnVsbHNjcmVlbkZsYWc6IGZhbHNlLA0KICAgICAgbWIwMDVPcHRpb25zOiBbDQogICAgICAgIHtsYWJlbDogJ+WOn+aWmScsdmFsdWU6ICcxMDUnfSwNCiAgICAgICAge2xhYmVsOiAn5YyF5p2QJyx2YWx1ZTogJzEwNCd9LA0KICAgICAgICB7bGFiZWw6ICfljYrmiJDlk4EnLHZhbHVlOiAnMTAzJ30sDQogICAgICAgIHtsYWJlbDogJ+ijuOijheWTgScsdmFsdWU6ICcxMDInfSwNCiAgICAgICAge2xhYmVsOiAn5oiQ5ZOBJyx2YWx1ZTogJzEwMSd9LA0KICAgICAgXSwNCiAgICAgIGN1cnJlbnRSb3c6IHt9LA0KICAgICAgbWIwMDhPcHRpb25zOiBbDQogICAgICAgIHtsYWJlbDogJ+iHquWIticsdmFsdWU6ICcwJ30sDQogICAgICAgIHtsYWJlbDogJ+Wklui0rScsdmFsdWU6ICcxJ30sDQogICAgICAgIHtsYWJlbDogJ+WuouS+mycsdmFsdWU6ICc0MDUnfSwNCiAgICAgIF0sDQogICAgfQ0KICB9LA0KICBhc3luYyBjcmVhdGVkKCkgew0KICB9LA0KICBtZXRob2RzOiB7DQogICAgYmNGaWVsZHMocHJvamVjdEJjSWQsZikgew0KICAgICAgY29uc3QgYXJyID0gdGhpcy5tYXRlcmlhbExpc3QuZmlsdGVyKGk9PmkuaWQgPT09IHByb2plY3RCY0lkKQ0KICAgICAgaWYoYXJyICYmIGFyclswXSkgew0KICAgICAgICByZXR1cm4gYXJyWzBdW2ZdDQogICAgICB9DQogICAgfSwNCiAgICBtYjAwNVRleHQobWIwMDUpIHsNCiAgICAgIGNvbnN0IGFyciA9IHRoaXMubWIwMDVPcHRpb25zLmZpbHRlcihpPT4gbWIwMDUgPT09IGkudmFsdWUpDQogICAgICBpZihhcnIgJiYgYXJyWzBdKSB7DQogICAgICAgIHJldHVybiBhcnJbMF0ubGFiZWwNCiAgICAgIH0NCiAgICB9LA0KICAgIHJvd1N0eWxlKHNjb3BlKSB7DQogICAgICBpZihzY29wZS5yb3cucGlkICE9PSB0aGlzLmJvbVRyZWVbMF0uaWQgJiYgc2NvcGUucm93LmlkICE9PSB0aGlzLmJvbVRyZWVbMF0uaWQpIHsNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjRTZBMjNDJw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBtYjAwOFRleHQobWIwMDgpIHsNCiAgICAgIGxldCB0ZXh0ID0gIiINCiAgICAgIGlmKG1iMDA4KSB7DQogICAgICAgIGlmKFsiNDAxIiwiNDAyIiwiMCJdLmluY2x1ZGVzKG1iMDA4KSkgew0KICAgICAgICAgIHRleHQgPSAnMCcNCiAgICAgICAgfQ0KICAgICAgICBpZihbIjQwMyIsIjQwNCIsIjEiXS5pbmNsdWRlcyhtYjAwOCkpIHsNCiAgICAgICAgICB0ZXh0ID0gJzEnDQogICAgICAgIH0NCiAgICAgICAgaWYoWyI0MDUiXS5pbmNsdWRlcyhtYjAwOCkpIHsNCiAgICAgICAgICB0ZXh0ID0gJzQwNScNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIHRleHQNCiAgICB9LA0KICAgIGFzeW5jIHN1Ym1pdEZvcm0oKSB7DQogICAgICBjb25zdCB0ZW1wQXJyYXkgPSB0aGlzLnRyYW5zbGF0ZVRyZWVUb0RhdGEodGhpcy5ib21UcmVlKQ0KICAgICAgY29uc3QgYm9tQXJyYXkgPSBbXQ0KICAgICAgZm9yIChjb25zdCBpdGVtIG9mIHRlbXBBcnJheSkgew0KICAgICAgICBib21BcnJheS5wdXNoKHsNCiAgICAgICAgICAuLi5pdGVtLA0KICAgICAgICAgIG1iMDA4OiB0aGlzLm1iMDA4VGV4dChpdGVtLm1iMDA4KSwNCiAgICAgICAgICBjaGlsZHJlbjogW10sDQogICAgICAgIH0pDQogICAgICAgIGlmKGl0ZW0ucGlkICYmICFpdGVtLm1iMDAyKSB7DQogICAgICAgICAgdGhpcy5tc2dFcnJvcign6K+36L6T5YWl5ZOB5ZCNJykNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KICAgICAgICBpZighaXRlbS5tYjAwNSkgew0KICAgICAgICAgIHRoaXMubXNnRXJyb3IoJ+ivt+i+k+WFpeexu+WIqycpDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCiAgICAgICAgaWYoIWl0ZW0ubWQwMDYpIHsNCiAgICAgICAgICB0aGlzLm1zZ0Vycm9yKCfor7fovpPlhaXnlKjph48nKQ0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIGlmKCFpdGVtLm1kMDA3KSB7DQogICAgICAgICAgdGhpcy5tc2dFcnJvcign6K+36L6T5YWl5bqV5pWwJykNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICBjb25zdCBib21UcmVlID0gdGhpcy50b1RyZWUoYm9tQXJyYXksdW5kZWZpbmVkKQ0KICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICBpZDogdGhpcy5wcm9qZWN0UHJvZHVjdEl0ZW1JZCwNCiAgICAgICAgYm9tVHJlZTogSlNPTi5zdHJpbmdpZnkoYm9tVHJlZSksDQogICAgICAgIGJvbUFycmF5OiBKU09OLnN0cmluZ2lmeShib21BcnJheSksDQogICAgICB9DQogICAgICBpZihib21UcmVlWzBdICYmIGJvbVRyZWVbMF0ubWIwMDIpIHsNCiAgICAgICAgcGFyYW1zLmJvbU5hbWUgPSBib21UcmVlWzBdLm1iMDAyDQogICAgICB9DQogICAgICBpZiAodGhpcy5wcm9qZWN0UHJvZHVjdEl0ZW1JZCAhPSBudWxsKSB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gdHJ1ZQ0KICAgICAgICAgIGF3YWl0IHVwZGF0ZVByb2R1Y3RJdGVtKHBhcmFtcykNCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIikNCiAgICAgICAgICB0aGlzLiRlbWl0KCdzYXZlU3VjY2VzcycpDQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBpbml0Q2hhcnQoKSB7DQogICAgICB0aGlzLmJvbU9wZW4gPSB0cnVlDQogICAgICBhd2FpdCB0aGlzLiRuZXh0VGljaygpDQogICAgICBhd2FpdCB0aGlzLiRyZWZzLnByb2plY3RCb21DaGFydHMuaW5pdCgpDQogICAgfSwNCiAgICBhc3luYyBlbXB0eVRyZWUoKSB7DQogICAgICB0cnkgew0KICAgICAgICBhd2FpdCB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTmuIXnqbo/JykNCiAgICAgICAgdGhpcy5ib21UcmVlLnNwbGljZSgwLHRoaXMuYm9tVHJlZS5sZW5ndGgpDQogICAgICB9IGNhdGNoIChlKSB7DQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBpbml0THAocGlkKSB7DQogICAgICBjb25zdCBhcnJheSA9IFtdDQogICAgICBjb25zdCBpZCA9IHRoaXMuJG5hbm9pZCgpDQogICAgICBhcnJheS5wdXNoKHsNCiAgICAgICAgaWQsDQogICAgICAgIHBpZCwNCiAgICAgICAgbWIwMDI6IHRoaXMucHJvZHVjdE5hbWUgKyAnIOWNiuaIkOWTgScsDQogICAgICAgIG1iMDA1OiAnMTAzJywNCiAgICAgICAgbWQwMTc6IG51bGwsDQogICAgICAgIG1iMDAzOiBudWxsLA0KICAgICAgICBjb25zdW1wdGlvbjogbnVsbCwNCiAgICAgICAgbWQwMDY6IG51bGwsDQogICAgICAgIG1kMDA3OiBudWxsLA0KICAgICAgICBtYzAwNDogbnVsbCwNCiAgICAgICAgbWQwMDg6IG51bGwsDQogICAgICAgIG1iMDA4OiBudWxsLA0KICAgICAgICBjaGlsZHJlbjogW10sDQogICAgICB9KQ0KICAgICAgY29uc3QgbWIwMDhBcnJheSA9IHRoaXMubWIwMDhPcHRpb25zLm1hcChpPT5pLnZhbHVlKQ0KICAgICAgZm9yIChjb25zdCBpdGVtIG9mIHRoaXMubWF0ZXJpYWxMaXN0KSB7DQogICAgICAgIGlmKGl0ZW0udHlwZSA9PT0gJzAnKSB7Ly/oo7jlk4HkuIvpu5jorqTmmK/kuLvljIXmnZDnmoQNCiAgICAgICAgICBhcnJheS5wdXNoKHsNCiAgICAgICAgICAgIGlkOiB0aGlzLiRuYW5vaWQoKSwNCiAgICAgICAgICAgIHBpZCwNCiAgICAgICAgICAgIG1iMDA1OiAnMTA0JywNCiAgICAgICAgICAgIG1iMDAyOiBpdGVtLm5hbWUsDQogICAgICAgICAgICBtZDAxNzogbnVsbCwNCiAgICAgICAgICAgIG1iMDAzOiBudWxsLA0KICAgICAgICAgICAgY29uc3VtcHRpb246IG51bGwsDQogICAgICAgICAgICBtZDAwNjogbnVsbCwNCiAgICAgICAgICAgIG1kMDA3OiBudWxsLA0KICAgICAgICAgICAgbWMwMDQ6IG51bGwsDQogICAgICAgICAgICBtZDAwODogbnVsbCwNCiAgICAgICAgICAgIG1iMDA4OiB0aGlzLm1iMDA4VGV4dChpdGVtLm1iMDA4KSwNCiAgICAgICAgICAgIGNoaWxkcmVuOiBbXSwNCiAgICAgICAgICAgIHByb2plY3RCY0lkOiBpdGVtLmlkLA0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiBhcnJheQ0KICAgIH0sDQogICAgYXN5bmMgaW5pdENwKHBpZCkgew0KICAgICAgY29uc3QgYXJyYXkgPSBbXQ0KICAgICAgY29uc3QgaWQgPSB0aGlzLiRuYW5vaWQoKQ0KICAgICAgY29uc3QgY2hpbGRyZW4gPSBhd2FpdCB0aGlzLmluaXRMcChpZCkNCiAgICAgIGFycmF5LnB1c2goew0KICAgICAgICBpZCwNCiAgICAgICAgcGlkLA0KICAgICAgICBtYjAwMjogdGhpcy5wcm9kdWN0TmFtZSArICfoo7jlk4EnLA0KICAgICAgICBtYjAwNTogJzEwMicsDQogICAgICAgIG1kMDE3OiBudWxsLA0KICAgICAgICBtYjAwMzogbnVsbCwNCiAgICAgICAgY29uc3VtcHRpb246IDEsDQogICAgICAgIG1kMDA2OiBudWxsLA0KICAgICAgICBtZDAwNzogMSwNCiAgICAgICAgbWMwMDQ6IG51bGwsDQogICAgICAgIG1kMDA4OiBudWxsLA0KICAgICAgICBtYjAwODogbnVsbCwNCiAgICAgICAgY2hpbGRyZW4sDQogICAgICB9KQ0KICAgICAgY29uc3QgbWIwMDhBcnJheSA9IHRoaXMubWIwMDhPcHRpb25zLm1hcChpPT5pLnZhbHVlKQ0KICAgICAgZm9yIChjb25zdCBpdGVtIG9mIHRoaXMubWF0ZXJpYWxMaXN0KSB7DQogICAgICAgIGlmKGl0ZW0udHlwZSAhPT0gJzAnKSB7Ly/miJDlk4HkuIvpu5jorqTkuI3mmK/kuLvljIXmnZDnmoQNCiAgICAgICAgICBhcnJheS5wdXNoKHsNCiAgICAgICAgICAgIGlkOiB0aGlzLiRuYW5vaWQoKSwNCiAgICAgICAgICAgIHBpZCwNCiAgICAgICAgICAgIG1iMDA1OiAnMTA0JywNCiAgICAgICAgICAgIG1iMDAyOiBpdGVtLm5hbWUsDQogICAgICAgICAgICBtZDAxNzogbnVsbCwNCiAgICAgICAgICAgIG1iMDAzOiBudWxsLA0KICAgICAgICAgICAgY29uc3VtcHRpb246IG51bGwsDQogICAgICAgICAgICBtZDAwNjogbnVsbCwNCiAgICAgICAgICAgIG1kMDA3OiBudWxsLA0KICAgICAgICAgICAgbWMwMDQ6IG51bGwsDQogICAgICAgICAgICBtZDAwODogbnVsbCwNCiAgICAgICAgICAgIG1iMDA4OiB0aGlzLm1iMDA4VGV4dChpdGVtLm1iMDA4KSwNCiAgICAgICAgICAgIGNoaWxkcmVuOiBbXSwNCiAgICAgICAgICAgIHByb2plY3RCY0lkOiBpdGVtLmlkLA0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiBhcnJheQ0KICAgIH0sDQogICAgYXN5bmMgaW5pdEJvbSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGF3YWl0IHRoaXMuJGNvbmZpcm0oJ+mHjeaWsOeUn+aIkOS8muimhuebluW9k+WJjeWtkOWQjeensOS4i+W3sue7tOaKpOeahGJvbSzmmK/lkKbnu6fnu60/JykNCiAgICAgICAgdGhpcy5ib21UcmVlLmxlbmd0aCA9IDANCiAgICAgICAgY29uc3QgaWQgPSB0aGlzLiRuYW5vaWQoKQ0KICAgICAgICBjb25zdCBjaGlsZHJlbiA9IGF3YWl0IHRoaXMuaW5pdENwKGlkKQ0KICAgICAgICBjb25zdCBvID0gew0KICAgICAgICAgIGlkLA0KICAgICAgICAgIHBpZDogdW5kZWZpbmVkLA0KICAgICAgICAgIG1iMDAyOiBudWxsLA0KICAgICAgICAgIG1iMDA1OiAnMTAxJywNCiAgICAgICAgICBtZDAxNzogbnVsbCwNCiAgICAgICAgICBtYjAwMzogbnVsbCwNCiAgICAgICAgICBjb25zdW1wdGlvbjogMSwNCiAgICAgICAgICBtZDAwNjogMSwNCiAgICAgICAgICBtZDAwNzogMSwNCiAgICAgICAgICBtYzAwNDogbnVsbCwNCiAgICAgICAgICBtZDAwODogbnVsbCwNCiAgICAgICAgICBtYjAwODogbnVsbCwNCiAgICAgICAgICBjaGlsZHJlbiwNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmJvbVRyZWUucHVzaChvKQ0KICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgfQ0KICAgIH0sDQogICAgbmFtZVN0eWxlKG1iMDA1KSB7DQogICAgICBsZXQgY29sb3IgPSAnJzsNCiAgICAgIHN3aXRjaCAobWIwMDUpIHsNCiAgICAgICAgY2FzZSAnMTAxJzogY29sb3IgPSAnIzQwOUVGRic7IGJyZWFrOw0KICAgICAgICBjYXNlICcxMDInOiBjb2xvciA9ICcjNjdDMjNBJzsgYnJlYWs7DQogICAgICAgIGNhc2UgJzEwMyc6IGNvbG9yID0gJyNFNkEyM0MnOyBicmVhazsNCiAgICAgICAgY2FzZSAnMTA0JzogY29sb3IgPSAnI0Y1NkM2Qyc7IGJyZWFrOw0KICAgICAgICBjYXNlICcxMDUnOiBjb2xvciA9ICcjOTA5Mzk5JzsgYnJlYWs7DQogICAgICB9DQogICAgICByZXR1cm4gew0KICAgICAgICBjb2xvcjogY29sb3INCiAgICAgIH0NCiAgICB9LA0KICAgIGNoZWNrQmMoYmNBcnJheSkgew0KICAgICAgYmNBcnJheS5zb3J0KChwcmUsY3Vycik9PnByZS5tYjAwNSAtIGN1cnIubWIwMDUpDQogICAgICBmb3IgKGNvbnN0IGl0ZW0gb2YgYmNBcnJheSkgew0KICAgICAgICBjb25zdCBvID0gew0KICAgICAgICAgIGlkOiB0aGlzLiRuYW5vaWQoKSwNCiAgICAgICAgICBwaWQ6IHRoaXMuY3VycmVudFJvdy5pZCwNCiAgICAgICAgICBtYjAwNTogaXRlbS5tYjAwNSwNCiAgICAgICAgICBtYjAwMjogaXRlbS5uYW1lLA0KICAgICAgICAgIG1kMDE3OiBudWxsLA0KICAgICAgICAgIG1iMDAzOiBudWxsLA0KICAgICAgICAgIGNvbnN1bXB0aW9uOiBudWxsLA0KICAgICAgICAgIG1kMDA2OiBudWxsLA0KICAgICAgICAgIG1kMDA3OiBudWxsLA0KICAgICAgICAgIG1jMDA0OiBudWxsLA0KICAgICAgICAgIG1kMDA4OiBudWxsLA0KICAgICAgICAgIG1iMDA4OiB0aGlzLm1iMDA4VGV4dChpdGVtLm1iMDA4KSwNCiAgICAgICAgICBjaGlsZHJlbjogW10sDQogICAgICAgICAgcHJvamVjdEJjSWQ6IGl0ZW0uaWQsDQogICAgICAgIH0NCiAgICAgICAgdGhpcy5jdXJyZW50Um93LmNoaWxkcmVuLnB1c2gobykNCiAgICAgIH0NCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlDQogICAgfSwNCiAgICBzaG93QmNEaWFsb2cocm93KSB7DQogICAgICB0aGlzLmN1cnJlbnRSb3cgPSByb3cNCiAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICB9LA0KICAgIHBhcmVudE5hbWUoYm9tVHJlZSxwaWQpIHsNCiAgICAgIGlmKHBpZCAmJiBib21UcmVlLmxlbmd0aCkgew0KICAgICAgICBjb25zdCBhcnIgPSBib21UcmVlLmZpbHRlcihpPT5pLmlkID09PSBwaWQpDQogICAgICAgIGlmKGFyciAmJiBhcnJbMF0pIHsNCiAgICAgICAgICByZXR1cm4gYXJyWzBdLm1iMDAyDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgZm9yIChjb25zdCBpdGVtIG9mIGJvbVRyZWUpIHsNCiAgICAgICAgICAgIGlmKGl0ZW0uY2hpbGRyZW4ubGVuZ3RoKSB7DQogICAgICAgICAgICAgIHJldHVybiB0aGlzLnBhcmVudE5hbWUoaXRlbS5jaGlsZHJlbixwaWQpDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBkZWxJdGVtKHJvdykgew0KICAgICAgdGhpcy5yZW1vdmVJdGVtKHRoaXMuYm9tVHJlZSxyb3cpDQogICAgfSwNCiAgICByZW1vdmVJdGVtKGJvbVRyZWUscm93KSB7DQogICAgICBpZihyb3cucGlkKSB7DQogICAgICAgIGZvciAoY29uc3QgYiBvZiBib21UcmVlKSB7DQogICAgICAgICAgaWYoYi5pZCA9PT0gcm93LnBpZCAmJiBiLmNoaWxkcmVuICYmIGIuY2hpbGRyZW4ubGVuZ3RoKSB7DQogICAgICAgICAgICBjb25zdCBwYXJlbnRBcnJheSA9IGIuY2hpbGRyZW4NCiAgICAgICAgICAgIGNvbnN0IGluZGV4ID0gcGFyZW50QXJyYXkuZmluZEluZGV4KGk9PiBpLmlkID09PSByb3cuaWQpDQogICAgICAgICAgICBwYXJlbnRBcnJheS5zcGxpY2UoaW5kZXgsMSkNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5yZW1vdmVJdGVtKGIuY2hpbGRyZW4scm93KQ0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjb25zdCBpbmRleCA9IGJvbVRyZWUuZmluZEluZGV4KGk9PiBpLmlkID09PSByb3cuaWQpDQogICAgICAgIGJvbVRyZWUuc3BsaWNlKGluZGV4LDEpDQogICAgICB9DQogICAgfSwNCiAgICBhZGRJdGVtKHBhcmVudE5vZGUpIHsNCiAgICAgIGNvbnN0IG8gPSB7DQogICAgICAgIGlkOiB0aGlzLiRuYW5vaWQoKSwNCiAgICAgICAgbWIwMDI6IG51bGwsDQogICAgICAgIG1kMDE3OiBudWxsLA0KICAgICAgICBtYjAwMzogbnVsbCwNCiAgICAgICAgY29uc3VtcHRpb246IG51bGwsDQogICAgICAgIG1kMDA2OiBudWxsLA0KICAgICAgICBtZDAwNzogbnVsbCwNCiAgICAgICAgbWMwMDQ6IG51bGwsDQogICAgICAgIG1kMDA4OiBudWxsLA0KICAgICAgICBtYjAwODogbnVsbCwNCiAgICAgICAgY2hpbGRyZW46IFtdLA0KICAgICAgfQ0KICAgICAgaWYocGFyZW50Tm9kZSkgew0KICAgICAgICBvLnBpZCA9IHBhcmVudE5vZGUuaWQNCiAgICAgICAgaWYoIXBhcmVudE5vZGUucGlkKSB7DQogICAgICAgICAgby5tYjAwMiA9IHRoaXMucHJvZHVjdE5hbWUgKyAnIOijuOWTgScNCiAgICAgICAgICBvLm1iMDA1ID0gJzEwMicNCiAgICAgICAgfSBlbHNlIGlmKHBhcmVudE5vZGUubWIwMDUgPT09ICcxMDInKSB7DQogICAgICAgICAgby5tYjAwMiA9IHRoaXMucHJvZHVjdE5hbWUgKyAnIOWNiuaIkOWTgScNCiAgICAgICAgICBvLm1iMDA1ID0gJzEwMycNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBvLm1iMDA1ID0gbnVsbA0KICAgICAgICB9DQogICAgICAgIHBhcmVudE5vZGUuY2hpbGRyZW4ucHVzaChvKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgby5tYjAwNSA9ICcxMDEnDQogICAgICAgIG8ubWQwMDYgPSAxDQogICAgICAgIG8ubWQwMDcgPSAxDQogICAgICAgIG8ubWIwMDIgPSB0aGlzLnByb2R1Y3ROYW1lDQogICAgICAgIHRoaXMuYm9tVHJlZS5wdXNoKG8pDQogICAgICB9DQogICAgfSwNCiAgfSwNCn0NCg0K"}, {"version": 3, "sources": ["makeBom.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+JA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "makeBom.vue", "sourceRoot": "src/views/project/project", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-table\r\n      :data=\"bomTree\"\r\n      :tree-props=\"{ children: 'children' }\"\r\n      default-expand-all\r\n      row-key=\"id\"\r\n      size=\"mini\"\r\n      :row-style=\"rowStyle\"\r\n    >\r\n      <el-table-column align=\"center\" width=\"120\" >\r\n        <template #header >上级</template>\r\n        <template v-slot=\"scope\" >\r\n<!--          {{parentName(bomTree,scope.row.pid)}}-->\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"mb002\" >\r\n        <template #header >品名<span style=\"color: #F56C6C\">*</span> </template>\r\n        <template v-slot=\"scope\" >\r\n          <div :class=\"readonly?'mask':''\" style=\"display: flex;align-items: center\" >\r\n            <span v-if=\"scope.row.mb005 !== '101'\">{{scope.row.mb002}}</span>\r\n            <el-input v-else v-model.trim=\"scope.row.mb002\" size=\"mini\" />\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"md003\" width=\"120\" >\r\n        <template #header >\r\n          品号\r\n          <el-tooltip content=\"成品是在bom新建的时候推送过来的,其他类型是物料总表关联过来的\" >\r\n            <i class=\"el-icon-question\" />\r\n          </el-tooltip>\r\n        </template>\r\n        <template v-slot=\"scope\" >\r\n          <span v-if=\"scope.row.mb005 === '101'\" >{{erpCode}}</span>\r\n          <span v-else >{{bcFields(scope.row.projectBcId,'erpCode')}}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" label=\"类别\" prop=\"mb005\"  width=\"100\" >\r\n        <template #header >类别 <span style=\"color: #F56C6C\">*</span> </template>\r\n        <template v-slot=\"scope\" >\r\n          <div :class=\"readonly?'mask':''\" >\r\n<!--            <el-select v-model=\"scope.row.mb005\" size=\"mini\" >-->\r\n<!--              <el-option v-for=\"item in mb005Options\"-->\r\n<!--                         :key=\"item.value\"-->\r\n<!--                         :label=\"item.label\"-->\r\n<!--                         :value=\"item.value\" />-->\r\n<!--            </el-select>-->\r\n            {{mb005Text(scope.row.mb005)}}\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n<!--      <el-table-column align=\"center\" label=\"物料类型\"  prop=\"md017\"  width=\"100\" >-->\r\n<!--        <template v-slot=\"scope\" >-->\r\n<!--          <el-input v-model=\"scope.row.md017\" size=\"mini\" />-->\r\n<!--        </template>-->\r\n<!--      </el-table-column>-->\r\n<!--      <el-table-column align=\"center\" label=\"物料规格\"  prop=\"mb003\"  width=\"100\" >-->\r\n<!--        <template v-slot=\"scope\" >-->\r\n<!--          <el-input v-model=\"scope.row.mb003\" size=\"mini\" />-->\r\n<!--        </template>-->\r\n<!--      </el-table-column>-->\r\n      <el-table-column align=\"center\" label=\"组成用量/底数\" prop=\"md006\" width=\"200\" >\r\n        <template #header >组成用量/底数 <span style=\"color: #F56C6C\">*</span> </template>\r\n        <template v-slot=\"scope\" >\r\n          <div :class=\"readonly?'mask':''\" style=\"display: flex;align-items: center\" >\r\n            <el-input v-model=\"scope.row.md006\" size=\"mini\" style=\"width: 80px\" type=\"number\"/>\r\n            /\r\n            <el-input v-model=\"scope.row.md007\" size=\"mini\" style=\"width: 80px\" type=\"number\" />\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n<!--      <el-table-column align=\"center\" label=\"用量\" prop=\"consumption\"  width=\"100\" >-->\r\n<!--        <template v-slot=\"scope\" >-->\r\n<!--          <el-input v-model=\"scope.row.consumption\" size=\"mini\" />-->\r\n<!--        </template>-->\r\n<!--      </el-table-column>-->\r\n<!--      <el-table-column align=\"center\" label=\"标准批量\" prop=\"mc004\"  width=\"100\" >-->\r\n<!--        <template v-slot=\"scope\" >-->\r\n<!--          <el-input v-model=\"scope.row.mc004\" size=\"mini\" />-->\r\n<!--        </template>-->\r\n<!--      </el-table-column>-->\r\n<!--      <el-table-column align=\"center\" label=\"损耗\" prop=\"md008\"  width=\"100\" >-->\r\n<!--        <template v-slot=\"scope\" >-->\r\n<!--          <el-input v-model=\"scope.row.md008\" size=\"mini\" />-->\r\n<!--        </template>-->\r\n<!--      </el-table-column>-->\r\n      <el-table-column align=\"center\" prop=\"mb008\" width=\"100\" >\r\n        <template #header >\r\n          物料属性\r\n          <el-tooltip content=\"此处用于项目总表中的 主包材属性、辅包材属性\" >\r\n            <i class=\"el-icon-question\" />\r\n          </el-tooltip>\r\n        </template>\r\n        <template v-slot=\"scope\" >\r\n          <el-select v-model=\"scope.row.mb008\" size=\"mini\" >\r\n            <el-option\r\n              v-for=\"d in mb008Options\"\r\n              :key=\"d.value\"\r\n              :label=\"d.label\"\r\n              :value=\"d.value\"\r\n            />\r\n          </el-select>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" label=\"操作\"  width=\"60\" >\r\n        <template #header >\r\n          <div style=\"display: flex;align-items: center\" >\r\n            <el-tooltip v-if=\"!bomTree.length && !readonly\" content=\"添加成品\" >\r\n              <i class=\"el-icon-plus\" @click=\"addItem()\" />\r\n            </el-tooltip>\r\n            <el-tooltip v-if=\"!readonly\" content=\"生成默认bom\" >\r\n              <i class=\"el-icon-news\" @click=\"initBom()\" />\r\n            </el-tooltip>\r\n            <el-tooltip content=\"树图\" >\r\n              <i class=\"el-icon-zoom-in\" @click=\"initChart()\" />\r\n            </el-tooltip>\r\n            <el-tooltip v-if=\"bomTree.length && !readonly\" content=\"清空\" >\r\n              <i class=\"el-icon-circle-close\" @click=\"emptyTree()\" />\r\n            </el-tooltip>\r\n          </div>\r\n        </template>\r\n        <template v-if=\"!readonly\" v-slot=\"scope\" >\r\n          <div style=\"display: flex;align-items: center\" >\r\n<!--            <el-tooltip content=\"普通添加\" >-->\r\n<!--              <i class=\"el-icon-plus\" @click=\"addItem(scope.row)\" />-->\r\n<!--            </el-tooltip>-->\r\n            <el-tooltip content=\"选自物料总表\" >\r\n<!--              <i class=\"el-icon-circle-plus-outline\" @click=\"selectGoods(scope.row.children,scope.row.id)\" />-->\r\n              <i class=\"el-icon-help\" @click=\"showBcDialog(scope.row)\" />\r\n            </el-tooltip>\r\n            <i class=\"el-icon-delete\" @click=\"delItem(scope.row)\" />\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <div v-if=\"!readonly\" class=\"dialog-footer\" style=\"margin-top: 20px\">\r\n      <el-button :loading=\"btnLoading\" size=\"mini\" type=\"primary\" @click=\"submitForm\" >保 存</el-button>\r\n    </div>\r\n\r\n    <el-dialog :close-on-click-modal=\"false\" :fullscreen=\"fullscreenFlag\" :visible.sync=\"open\" append-to-body width=\"1200px\">\r\n      <div slot=\"title\" class=\"dialog-title\">项目物料\r\n        <el-button :icon=\"fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'\" type=\"text\"\r\n                   @click=\"fullscreenFlag = !fullscreenFlag\"/>\r\n      </div>\r\n      <BcSelectTable v-if=\"projectId\" :project-id=\"projectId\" @checked=\"checkBc\" />\r\n    </el-dialog>\r\n\r\n    <el-dialog :close-on-click-modal=\"false\" :fullscreen=\"fullscreenFlag\" :visible.sync=\"bomOpen\" append-to-body width=\"1200px\">\r\n      <div slot=\"title\" class=\"dialog-title\">bom结构\r\n        <el-button :icon=\"fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'\" type=\"text\"\r\n                   @click=\"fullscreenFlag = !fullscreenFlag\"/>\r\n      </div>\r\n      <ProjectBomCharts ref=\"projectBomCharts\" :bom-tree=\"bomTree\"  :erp-code=\"erpCode\" />\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n<script >\r\nimport {allBc} from \"@/api/project/bc\";\r\nimport BcSelectTable from \"@/views/project/project/bcSelectTable.vue\";\r\nimport ProjectBomCharts from \"@/views/project/project/bomCharts.vue\";\r\nimport {updateProductItem} from \"@/api/project/productItem\";\r\n\r\nexport default {\r\n  name: \"projectMarkBom\",\r\n  components: {\r\n    ProjectBomCharts,\r\n    BcSelectTable\r\n  },\r\n  props: {\r\n    projectId: {\r\n      type: Number,\r\n      default: null,\r\n    },\r\n    productName: {\r\n      type: String,\r\n      default: '',\r\n    },\r\n    bomTree: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    projectProductItemId: {\r\n      type: Number,\r\n      required: true,\r\n    },\r\n    erpCode: {\r\n      type: String,\r\n      required: true,\r\n    },\r\n    readonly: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    materialList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      open: false,\r\n      bomOpen: false,\r\n      fullscreenFlag: false,\r\n      mb005Options: [\r\n        {label: '原料',value: '105'},\r\n        {label: '包材',value: '104'},\r\n        {label: '半成品',value: '103'},\r\n        {label: '裸装品',value: '102'},\r\n        {label: '成品',value: '101'},\r\n      ],\r\n      currentRow: {},\r\n      mb008Options: [\r\n        {label: '自制',value: '0'},\r\n        {label: '外购',value: '1'},\r\n        {label: '客供',value: '405'},\r\n      ],\r\n    }\r\n  },\r\n  async created() {\r\n  },\r\n  methods: {\r\n    bcFields(projectBcId,f) {\r\n      const arr = this.materialList.filter(i=>i.id === projectBcId)\r\n      if(arr && arr[0]) {\r\n        return arr[0][f]\r\n      }\r\n    },\r\n    mb005Text(mb005) {\r\n      const arr = this.mb005Options.filter(i=> mb005 === i.value)\r\n      if(arr && arr[0]) {\r\n        return arr[0].label\r\n      }\r\n    },\r\n    rowStyle(scope) {\r\n      if(scope.row.pid !== this.bomTree[0].id && scope.row.id !== this.bomTree[0].id) {\r\n        return {\r\n          backgroundColor: '#E6A23C'\r\n        }\r\n      }\r\n    },\r\n    mb008Text(mb008) {\r\n      let text = \"\"\r\n      if(mb008) {\r\n        if([\"401\",\"402\",\"0\"].includes(mb008)) {\r\n          text = '0'\r\n        }\r\n        if([\"403\",\"404\",\"1\"].includes(mb008)) {\r\n          text = '1'\r\n        }\r\n        if([\"405\"].includes(mb008)) {\r\n          text = '405'\r\n        }\r\n      }\r\n      return text\r\n    },\r\n    async submitForm() {\r\n      const tempArray = this.translateTreeToData(this.bomTree)\r\n      const bomArray = []\r\n      for (const item of tempArray) {\r\n        bomArray.push({\r\n          ...item,\r\n          mb008: this.mb008Text(item.mb008),\r\n          children: [],\r\n        })\r\n        if(item.pid && !item.mb002) {\r\n          this.msgError('请输入品名')\r\n          return\r\n        }\r\n        if(!item.mb005) {\r\n          this.msgError('请输入类别')\r\n          return\r\n        }\r\n        if(!item.md006) {\r\n          this.msgError('请输入用量')\r\n          return\r\n        }\r\n        if(!item.md007) {\r\n          this.msgError('请输入底数')\r\n          return\r\n        }\r\n      }\r\n\r\n      const bomTree = this.toTree(bomArray,undefined)\r\n      const params = {\r\n        id: this.projectProductItemId,\r\n        bomTree: JSON.stringify(bomTree),\r\n        bomArray: JSON.stringify(bomArray),\r\n      }\r\n      if(bomTree[0] && bomTree[0].mb002) {\r\n        params.bomName = bomTree[0].mb002\r\n      }\r\n      if (this.projectProductItemId != null) {\r\n        try {\r\n          this.btnLoading = true\r\n          await updateProductItem(params)\r\n          this.btnLoading = false\r\n          this.msgSuccess(\"修改成功\")\r\n          this.$emit('saveSuccess')\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n    async initChart() {\r\n      this.bomOpen = true\r\n      await this.$nextTick()\r\n      await this.$refs.projectBomCharts.init()\r\n    },\r\n    async emptyTree() {\r\n      try {\r\n        await this.$confirm('是否确认清空?')\r\n        this.bomTree.splice(0,this.bomTree.length)\r\n      } catch (e) {\r\n      }\r\n    },\r\n    async initLp(pid) {\r\n      const array = []\r\n      const id = this.$nanoid()\r\n      array.push({\r\n        id,\r\n        pid,\r\n        mb002: this.productName + ' 半成品',\r\n        mb005: '103',\r\n        md017: null,\r\n        mb003: null,\r\n        consumption: null,\r\n        md006: null,\r\n        md007: null,\r\n        mc004: null,\r\n        md008: null,\r\n        mb008: null,\r\n        children: [],\r\n      })\r\n      const mb008Array = this.mb008Options.map(i=>i.value)\r\n      for (const item of this.materialList) {\r\n        if(item.type === '0') {//裸品下默认是主包材的\r\n          array.push({\r\n            id: this.$nanoid(),\r\n            pid,\r\n            mb005: '104',\r\n            mb002: item.name,\r\n            md017: null,\r\n            mb003: null,\r\n            consumption: null,\r\n            md006: null,\r\n            md007: null,\r\n            mc004: null,\r\n            md008: null,\r\n            mb008: this.mb008Text(item.mb008),\r\n            children: [],\r\n            projectBcId: item.id,\r\n          })\r\n        }\r\n      }\r\n      return array\r\n    },\r\n    async initCp(pid) {\r\n      const array = []\r\n      const id = this.$nanoid()\r\n      const children = await this.initLp(id)\r\n      array.push({\r\n        id,\r\n        pid,\r\n        mb002: this.productName + '裸品',\r\n        mb005: '102',\r\n        md017: null,\r\n        mb003: null,\r\n        consumption: 1,\r\n        md006: null,\r\n        md007: 1,\r\n        mc004: null,\r\n        md008: null,\r\n        mb008: null,\r\n        children,\r\n      })\r\n      const mb008Array = this.mb008Options.map(i=>i.value)\r\n      for (const item of this.materialList) {\r\n        if(item.type !== '0') {//成品下默认不是主包材的\r\n          array.push({\r\n            id: this.$nanoid(),\r\n            pid,\r\n            mb005: '104',\r\n            mb002: item.name,\r\n            md017: null,\r\n            mb003: null,\r\n            consumption: null,\r\n            md006: null,\r\n            md007: null,\r\n            mc004: null,\r\n            md008: null,\r\n            mb008: this.mb008Text(item.mb008),\r\n            children: [],\r\n            projectBcId: item.id,\r\n          })\r\n        }\r\n      }\r\n      return array\r\n    },\r\n    async initBom() {\r\n      try {\r\n        await this.$confirm('重新生成会覆盖当前子名称下已维护的bom,是否继续?')\r\n        this.bomTree.length = 0\r\n        const id = this.$nanoid()\r\n        const children = await this.initCp(id)\r\n        const o = {\r\n          id,\r\n          pid: undefined,\r\n          mb002: null,\r\n          mb005: '101',\r\n          md017: null,\r\n          mb003: null,\r\n          consumption: 1,\r\n          md006: 1,\r\n          md007: 1,\r\n          mc004: null,\r\n          md008: null,\r\n          mb008: null,\r\n          children,\r\n        }\r\n        this.bomTree.push(o)\r\n      } catch (e) {\r\n      }\r\n    },\r\n    nameStyle(mb005) {\r\n      let color = '';\r\n      switch (mb005) {\r\n        case '101': color = '#409EFF'; break;\r\n        case '102': color = '#67C23A'; break;\r\n        case '103': color = '#E6A23C'; break;\r\n        case '104': color = '#F56C6C'; break;\r\n        case '105': color = '#909399'; break;\r\n      }\r\n      return {\r\n        color: color\r\n      }\r\n    },\r\n    checkBc(bcArray) {\r\n      bcArray.sort((pre,curr)=>pre.mb005 - curr.mb005)\r\n      for (const item of bcArray) {\r\n        const o = {\r\n          id: this.$nanoid(),\r\n          pid: this.currentRow.id,\r\n          mb005: item.mb005,\r\n          mb002: item.name,\r\n          md017: null,\r\n          mb003: null,\r\n          consumption: null,\r\n          md006: null,\r\n          md007: null,\r\n          mc004: null,\r\n          md008: null,\r\n          mb008: this.mb008Text(item.mb008),\r\n          children: [],\r\n          projectBcId: item.id,\r\n        }\r\n        this.currentRow.children.push(o)\r\n      }\r\n      this.open = false\r\n    },\r\n    showBcDialog(row) {\r\n      this.currentRow = row\r\n      this.open = true\r\n    },\r\n    parentName(bomTree,pid) {\r\n      if(pid && bomTree.length) {\r\n        const arr = bomTree.filter(i=>i.id === pid)\r\n        if(arr && arr[0]) {\r\n          return arr[0].mb002\r\n        } else {\r\n          for (const item of bomTree) {\r\n            if(item.children.length) {\r\n              return this.parentName(item.children,pid)\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    delItem(row) {\r\n      this.removeItem(this.bomTree,row)\r\n    },\r\n    removeItem(bomTree,row) {\r\n      if(row.pid) {\r\n        for (const b of bomTree) {\r\n          if(b.id === row.pid && b.children && b.children.length) {\r\n            const parentArray = b.children\r\n            const index = parentArray.findIndex(i=> i.id === row.id)\r\n            parentArray.splice(index,1)\r\n          }\r\n          this.removeItem(b.children,row)\r\n        }\r\n      } else {\r\n        const index = bomTree.findIndex(i=> i.id === row.id)\r\n        bomTree.splice(index,1)\r\n      }\r\n    },\r\n    addItem(parentNode) {\r\n      const o = {\r\n        id: this.$nanoid(),\r\n        mb002: null,\r\n        md017: null,\r\n        mb003: null,\r\n        consumption: null,\r\n        md006: null,\r\n        md007: null,\r\n        mc004: null,\r\n        md008: null,\r\n        mb008: null,\r\n        children: [],\r\n      }\r\n      if(parentNode) {\r\n        o.pid = parentNode.id\r\n        if(!parentNode.pid) {\r\n          o.mb002 = this.productName + ' 裸品'\r\n          o.mb005 = '102'\r\n        } else if(parentNode.mb005 === '102') {\r\n          o.mb002 = this.productName + ' 半成品'\r\n          o.mb005 = '103'\r\n        } else {\r\n          o.mb005 = null\r\n        }\r\n        parentNode.children.push(o)\r\n      } else {\r\n        o.mb005 = '101'\r\n        o.md006 = 1\r\n        o.md007 = 1\r\n        o.mb002 = this.productName\r\n        this.bomTree.push(o)\r\n      }\r\n    },\r\n  },\r\n}\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n</style>\r\n"]}]}