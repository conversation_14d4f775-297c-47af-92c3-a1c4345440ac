import request from '@/utils/request'

// 查询成品检验标准列表
export function listFinishedStandard(query) {
  return request({
    url: '/qc/finishedStandard/list',
    method: 'get',
    params: query
  })
}

// 查询成品检验标准详细
export function getFinishedStandard(id) {
  return request({
    url: '/qc/finishedStandard/' + id,
    method: 'get'
  })
}

// 新增成品检验标准
export function addFinishedStandard(data) {
  return request({
    url: '/qc/finishedStandard',
    method: 'post',
    data: data
  })
}

// 修改成品检验标准
export function updateFinishedStandard(data) {
  return request({
    url: '/qc/finishedStandard',
    method: 'put',
    data: data
  })
}

// 删除成品检验标准
export function delFinishedStandard(id) {
  return request({
    url: '/qc/finishedStandard/' + id,
    method: 'delete'
  })
}

// 导出成品检验标准
export function exportFinishedStandard(query) {
  return request({
    url: '/qc/finishedStandard/export',
    method: 'get',
    params: query
  })
}

export function allFinishedStandard(query) {
  return request({
    url: '/qc/finishedStandard/all',
    method: 'get',
    params: query
  })
}


// 导出包材物料检验标准
export function importFinishedStandardRecord(query) {
  return request({
    url: '/qc/finishedStandard/import',
    method: 'get',
    params: query
  })
}
// 导出包材物料检验标准
export function importFinishedStandardRecord2(query) {
  return request({
    url: '/qc/finishedStandard/import2',
    method: 'get',
    params: query
  })
}

export function getFinishedStandardByCode(code) {
  return request({
    url: '/qc/finishedStandard/getByCode/' + code,
    method: 'get'
  })
}
