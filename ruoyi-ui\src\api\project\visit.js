import request from '@/utils/request'

// 查询用户项目访问统计列表
export function listVisit(query) {
  return request({
    url: '/project/visit/list',
    method: 'get',
    params: query
  })
}

// 查询用户项目访问统计详细
export function getVisit(id) {
  return request({
    url: '/project/visit/' + id,
    method: 'get'
  })
}

// 新增用户项目访问统计
export function addVisit(data) {
  return request({
    url: '/project/visit',
    method: 'post',
    data: data
  })
}
