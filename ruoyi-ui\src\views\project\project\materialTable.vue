<template>
  <div>
    <el-row :gutter="20">
      <el-tooltip class="item" content="自定义列" effect="dark">
        <el-button
          circle
          icon="el-icon-menu"
          size="mini"
          @click="showCol"/>
      </el-tooltip>
    </el-row>

<!--    <el-form :model="queryParams" ref="queryForm" size="mini" label-width="120px">-->
<!--      <el-row>-->
<!--        <el-col :span="8">-->
<!--          <el-form-item label="品名" prop="name">-->
<!--            <el-input-->
<!--              v-model="queryParams.name"-->
<!--              clearable-->
<!--            />-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="8">-->
<!--          <el-form-item>-->
<!--            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>-->
<!--            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--      </el-row>-->
<!--    </el-form>-->

    <div class="table-wrapper small-table">
      <table :class="readonly?'mask':''" class="base-table bc-table">
        <thead>
        <tr>
          <th v-if="!readonly" :rowspan="2" class="nth0" style="width: 80px" >
            <el-tooltip content="普通添加" >
              <i class="el-icon-circle-plus-outline" @click="addItem" />
            </el-tooltip>
<!--            <el-tooltip content="通过erp编码添加(单个物料)" >-->
<!--              <i class="el-icon-circle-plus-outline" @click="showErp" />-->
<!--            </el-tooltip>-->
            <el-tooltip content="通过bom结构添加" >
              <i class="el-icon-circle-plus-outline" @click="showBom" />
            </el-tooltip>
            <el-tooltip content="选择包材库" >
              <i class="el-icon-circle-plus-outline" @click="selectBc" />
            </el-tooltip>
          </th>
          <th v-if="columnsFlag('项目包材编码')" :rowspan="2" style="width: 120px" >
            <span style="color: #F56C6C">*</span>
            项目包材编码
          </th>
          <th v-if="columnsFlag('开发类型')" :rowspan="2" style="width: 120px" >
            <span style="color: #F56C6C">*</span>
            开发类型
          </th>
          <th v-if="columnsFlag('名称')" :rowspan="2" style="width: 500px" >
            <span style="color: #F56C6C">*</span>
            名称
          </th>
          <th v-if="columnsFlag('类别')" :rowspan="2" style="width: 150px" >
            <span style="color: #F56C6C">*</span>
            类别
          </th>
          <th v-if="columnsFlag('物料属性')" :rowspan="2" style="width: 150px" >
            <span style="color: #F56C6C">*</span>
            物料属性
          </th>
          <th v-if="columnsFlag('包装材料')" :rowspan="2" style="width: 150px" >包装材料</th>
<!--          <th v-if="columnsFlag('包材库编码')" :rowspan="2" style="width: 150px" >包材库编码</th>-->
          <th v-if="columnsFlag('包材ERP编码')" :rowspan="2" style="width: 150px" >包材ERP编码</th>
          <th v-if="columnsFlag('规格')" :rowspan="2" style="width: 150px" >规格</th>
          <th v-if="columnsFlag('尺寸')" :rowspan="2" style="width: 250px" >尺寸</th>
          <th v-if="columnsFlag('型号')" :rowspan="2" style="width: 150px" >型号</th>
          <th v-if="columnsFlag('供应商')" :rowspan="2" style="width: 150px" >供应商</th>
          <th v-if="columnsFlag('图片')" :rowspan="2" style="width: 300px" >图片</th>
          <th v-if="columnsFlag('COA/SPEC')" :rowspan="2" style="width: 300px" >COA/SPEC</th>
          <th v-if="columnsFlag('备注') && devStatus === '0'" :rowspan="2" style="width: 300px" >备注</th>
          <th v-if="devStatus === '1'" :rowspan="2" style="width: 120px" >报价日期</th>
          <th v-if="devStatus === '1'" :rowspan="2" style="width: 120px" >阶段</th>
          <th v-if="devStatus === '1'" :rowspan="2" class="nth0" style="width: 120px" >价格</th>
          <th v-for="log in logArray" :key="log.value" :colspan="log.array.length" :style="{width: log.array.length * 80 + 'px'}">{{log.label}}</th>
        </tr>
        <tr>
          <template v-for="log in logArray" >
            <th v-for="(l,index) in log.array" :key="log.value  + '_' + index " style="width: 80px" >{{l.projectItemOrderCode.substring(l.projectItemOrderCode.indexOf("-")+1)}}</th>
          </template>
        </tr>
        </thead>
        <tbody>
        <tr v-for="(item,index) in materialList.slice((currentPage-1) * pageSize, currentPage * pageSize)" :key="index">
          <td v-if="!readonly" class="nth0" >
            <i class="el-icon-remove-outline" @click="delItem(item)" ></i>
          </td>
          <td v-if="columnsFlag('项目包材编码')" >{{item.code}}</td>
          <td v-if="columnsFlag('开发类型')" >
            <el-select v-model="item.opType" size="mini" >
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :disabled="item.value==2"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </td>
          <td v-if="columnsFlag('名称')" >
            <el-input v-model.trim="item.name" size="mini" />
          </td>
          <td v-if="columnsFlag('类别')" >
            <span style="color: #00afff;cursor: pointer" @click="showType(item)" >{{materialText(item)}}</span>
<!--            <el-select v-model="item.type" size="mini" >-->
<!--              <el-option-->
<!--                v-for="dict in bcTypeOptions"-->
<!--                :key="dict.dictValue"-->
<!--                :label="dict.dictLabel"-->
<!--                :value="dict.dictValue"-->
<!--              />-->
<!--            </el-select>-->
<!--            <template v-if="item.type === '0'" >-->
<!--              <el-select v-model="item.zrqType" placeholder="主容器" size="mini" >-->
<!--                <el-option-->
<!--                  v-for="dict in zrqOptions"-->
<!--                  :key="dict.dictValue"-->
<!--                  :label="dict.dictLabel"-->
<!--                  :value="dict.dictValue"-->
<!--                />-->
<!--              </el-select>-->
<!--              <el-select v-model="item.allocator" clearable multiple placeholder="分配器" size="mini" >-->
<!--                <el-option-->
<!--                  v-for="dict in allocatorOptions"-->
<!--                  :key="dict.dictValue"-->
<!--                  :label="dict.dictLabel"-->
<!--                  :value="dict.dictValue"-->
<!--                />-->
<!--              </el-select>-->
<!--            </template>-->
          </td>
          <td v-if="columnsFlag('物料属性')" >
            <el-select v-model="item.mb008" size="mini" >
              <el-option
                  v-for="d in attrOptions"
                  :key="d.dictValue"
                  :label="d.dictLabel"
                  :value="d.dictValue"
              />
            </el-select>
          </td>
          <td v-if="columnsFlag('包装材料')" >
            <el-select v-model="item.materialType" size="mini" >
              <el-option
                v-for="dict in materialTypeOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </td>
<!--          <td v-if="columnsFlag('包材库编码')" >{{item.bcCode}}</td>-->
          <td v-if="columnsFlag('包材ERP编码')" >
            <el-input v-model.trim="item.erpCode" size="mini" />
          </td>
          <td v-if="columnsFlag('规格')" ><el-input v-model="item.spec" size="mini" /></td>
          <td v-if="columnsFlag('尺寸')" >
            <div style="display: flex;align-items: center;" >
              <el-input v-model="item.length" placeholder="长" size="mini" style="width: 80px" type="number" />
              *
              <el-input v-model="item.width" placeholder="宽" size="mini" style="width: 80px" type="number" />
              *
              <el-input v-model="item.height" placeholder="高" size="mini" style="width: 80px" type="number" />
            </div>
          </td>
          <td v-if="columnsFlag('型号')" ><el-input v-model="item.model" size="mini" /></td>
          <td v-if="columnsFlag('供应商')" >
            <el-select v-if="devStatus === '1'" v-model="item.supplierId" clearable  filterable size="mini" >
              <el-option
                v-for="item in supplierList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </td>
          <td v-if="columnsFlag('图片')" ><ImageUpload v-model="item.imgs" :is-show-tip="false" /></td>
          <td v-if="columnsFlag('COA/SPEC')" ><FileUpload :id="item.id?item.id:item.key" v-model="item.files" :is-show-tip="false" :view-type="1" @change="fileChange" /></td>
          <td v-if="columnsFlag('备注') && devStatus === '0'" >
            <el-input v-model="item.remark" autosize placeholder="客户指定供应商信息、联系方式,以及其它特殊要求" size="mini" type="textarea" />
          </td>
          <td v-if="devStatus === '1'" >{{item.priceDate}}</td>
          <td v-if="devStatus === '1'" >{{valueToLabel(stageOptions,item.stage)}}</td>
          <td v-if="devStatus === '1'" class="nth0" >
            <div v-if="item.id" style="color: #00afff;cursor: pointer" @click="handlePrice(item)"  >
                <span v-if="item.price">
                  {{item.price}}
                </span>
              <el-tooltip v-else content="阶梯价" placement="top">
                <span class="el-icon-edit" />
              </el-tooltip>
            </div>
          </td>
          <template v-for="log in logArray" >
            <td v-for="(l,z) in log.array" :key="log.value  + '_' + z " style="width: 80px" >
              <span :class="tdClass(l.projectBcIds,item.id)"></span>
            </td>
          </template>
        </tr>
        </tbody>
      </table>
    </div>

    <el-pagination
      layout="prev, pager, next"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-size="pageSize"
      :total="materialList.length">
    </el-pagination>

    <el-dialog :visible.sync="bomOpen" append-to-body title="选择bom" width="1200px">
      <el-input v-model="erpCode" size="mini" >
        <template slot="append" >
          <el-button :loading="btnLoading" icon="el-icon-search" @click="getErpInfo" />
        </template>
      </el-input>
      <el-tree
        ref="bomTree"
        :data="bomTree"
        check-strictly
        default-expand-all
        node-key="id"
        show-checkbox
      >
        <span slot-scope="{ node, data }" class="custom-tree-node" >
          <span>{{ data.mb002 }}</span>
          <span>{{ data.md003 }}</span>
        </span>
      </el-tree>
      <div class="dialog-footer" style="margin-top: 20px">
        <el-button :loading="btnLoading" size="mini" type="primary" @click="confirmBom" >确 定</el-button>
        <el-button size="mini" @click="bomOpen = false" >取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="bcOpen" append-to-body title="选择包材" width="1200px">
      <MaterialGoodsSelectTable @change="bcChange" />
      <div class="dialog-footer" style="margin-top: 20px">
        <el-button :loading="btnLoading" size="mini" type="primary" @click="confirmBc" >确 定</el-button>
        <el-button size="mini" @click="bcOpen = false" >取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :close-on-click-modal="false" :fullscreen="fullscreenFlag" :visible.sync="open" append-to-body width="1200px">
      <div slot="title" class="dialog-title">
        阶梯价
        <el-button :icon="fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'" type="text"
                   @click="fullscreenFlag = !fullscreenFlag"/>
      </div>
      <BcPriceTable
        v-if="currentRow.id"
        :config-array="configArray"
        :files="files"
        :form="currentRow"
        :price-array="priceArray"
        :project-bc-id="currentRow.id"
        @saveSuccess="saveSuccess"
      />
    </el-dialog>

    <CustomCols
      ref="customCols"
      :default-columns="columns"
      name="projectBc"
      @success="colSuccess"
    />

    <el-dialog :visible.sync="typeOpen" append-to-body title="选择类别" width="1200px">
      <el-form ref="form" :model="currentRow" :rules="rules" label-width="120px" size="mini" >
        <el-form-item prop="mb005">
          <template #label>
            物料类型
          </template>
          <el-radio-group v-model="currentRow.mb005" style="width: 90%;" @input="mb005Change" >
            <el-row :gutter="20" >
              <el-col v-for="dict in mb005Options" :key="dict.value" :span="3" >
                <el-radio :label="dict.value" style="padding-bottom: 10px" >
                  {{dict.label}}
                </el-radio>
              </el-col>
            </el-row>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="currentRow.mb005 === '104'" prop="type">
          <template #label>
            <span style="color: #F56C6C">*</span>
            包材类别
          </template>
          <el-radio-group v-model="currentRow.type" @input="typeChange" >
            <el-row :gutter="20" >
              <el-col v-for="dict in bcTypeOptions" :key="dict.dictValue" :span="3" >
                <el-radio :label="dict.dictValue" style="padding-bottom: 10px">
                  {{dict.dictLabel}}
                </el-radio>
              </el-col>
            </el-row>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="currentRow.mb005 === '104' && currentRow.type === '0'" >
          <template #label>
            <span style="color: #F56C6C">*</span>
            主容器-类别
          </template>
          <el-radio-group v-model="currentRow.zrqType" >
            <el-row :gutter="20" >
              <el-col v-for="dict in zrqOptions" :key="dict.dictValue" :span="3" >
                <el-radio :label="dict.dictValue" style="padding-bottom: 10px">
                  {{dict.dictLabel}}
                </el-radio>
              </el-col>
            </el-row>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="currentRow.mb005 === '104' && currentRow.allocator && currentRow.type === '0'" label="分配器" >
          <el-checkbox-group v-model="currentRow.allocator">
            <el-row :gutter="20" >
              <el-col v-for="dict in allocatorOptions" :key="dict.dictValue" :span="3" >
                <el-checkbox
                  :label="dict.dictValue">
                  {{dict.dictLabel}}
                </el-checkbox>
              </el-col>
            </el-row>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div class="dialog-footer" style="margin-top: 20px">
        <el-button :loading="btnLoading" size="mini" type="primary" @click="confirmMaterial" >确 定</el-button>
        <el-button size="mini" @click="typeOpen = false" >取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="treeOpen" append-to-body width="1200px">
      <template #title>
        选择类别
        <el-button
          icon="el-icon-question"
          size="mini"
          type="text"
          @click="openDoc"
        >包材分类</el-button>
      </template>

      <el-radio-group v-model="currentRow.mb005" style="width: 90%;" @input="mb005Change" >
        <el-row :gutter="20" >
          <el-col v-for="dict in mb005Options.filter(i=>i.value !== '104')" :key="dict.value" :span="3" >
            <el-radio :label="dict.value" style="font-size: 18px;font-weight: 700;margin-bottom: 10px" >
              {{dict.label}}
            </el-radio>
          </el-col>
        </el-row>
      </el-radio-group>
      <div style="display: flex;margin-bottom: 10px" >
        <div v-for="dict in mb005Options.filter(i=>i.value === '104')" :key="dict.value" class="label"  style="font-size: 15px;font-weight: 700;width: 100px" >
          {{dict.label}}
        </div>
        <div >
          <div>
            <div class="row-wrapper">
              <div class="label" >主包材类别</div>
              <div class="content">
                <el-radio-group v-model="currentRow.zrqType" @input="zrqTypeChange" >
                  <el-row :gutter="20" >
                    <el-col v-for="dict in zrqOptions" :key="dict.dictValue" :span="4" >
                      <el-radio :label="dict.dictValue" style="padding-bottom: 10px">
                        {{dict.dictLabel}}
                      </el-radio>
                    </el-col>
                  </el-row>
                </el-radio-group>
              </div>
            </div>
            <div class="row-wrapper">
              <div class="label">分配器</div>
              <div class="content">
                <el-checkbox-group v-model="currentRow.allocator" @input="allocatorChange" >
                  <el-row :gutter="20" >
                    <el-col v-for="dict in allocatorOptions" :key="dict.dictValue" :span="4" >
                      <el-checkbox
                        :label="dict.dictValue">
                        {{dict.dictLabel}}
                      </el-checkbox>
                    </el-col>
                  </el-row>
                </el-checkbox-group>
              </div>
            </div>
          </div>
          <div style="margin-top: 10px">
            <el-radio-group v-model="currentRow.type" @input="typeChange" >
              <el-row :gutter="20" >
                <el-col v-for="dict in bcTypeOptions.filter(i=>i.dictValue !== '0')" :key="dict.dictValue" :span="4" >
                  <el-radio :label="dict.dictValue" style="padding-bottom: 10px">
                    {{dict.dictLabel}}
                    <el-tooltip v-if="dict.dictLabel === '辅助工具'" content="(例如:粉扑、粉刷、勺子等)" >
                      <i class="el-icon-question" />
                    </el-tooltip>
                  </el-radio>
                </el-col>
              </el-row>
            </el-radio-group>
          </div>
        </div>
      </div>
      <div class="dialog-footer" style="margin-top: 20px">
        <el-button :loading="btnLoading" size="mini" type="primary" @click="confirmTree" >确 定</el-button>
        <el-button size="mini" @click="treeOpen = false" >取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import {getBomByErpCode, getProductByMd003} from "@/api/common/erp";
import MaterialGoodsSelectTable from "@/views/resource/materialGoods/selectTable.vue";
import {supplierAll} from "@/api/supplier/supplier";
import {allBcLog} from "@/api/project/bcLog";
import BcPriceTable from "@/views/sop/bc/bcPriceTable.vue";
import CustomCols from "@/components/customCols.vue";
import {allTreeData} from "@/api/system/treeData";
import {erpBom} from "@/api/production/schedulePlan";
import {delBc, updateBcForce} from "@/api/project/bc";

export default {
  name: 'projectBcTable',
  components: {
    CustomCols,
    BcPriceTable,
    MaterialGoodsSelectTable,
  },
  props: {
    form: {
      type: Object,
      required: true,
    },
    materialList: {
      type: Array,
      required: true,
    },
    devStatus: {
      type: String,
      default: '0',
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    'form.id': {
      async handler(v) {
        this.resetQuery()
        if(v && this.readonly) {//如果是视图模式
          let bcLogList = await allBcLog({projectId: v, status: '3'})
          let logArray = []
          for (let item of this.logOptions) {
            let bcLog = bcLogList.filter(i => i.projectId === v && i.type === item.dictValue)
            if (bcLog && bcLog.length > 0) {
              let array = []
              let keys = []
              for (let bc of bcLog) {
                if (!keys.includes(bc.projectItemOrderCode)) {
                  keys.push(bc.projectItemOrderCode)
                }
              }
              for (let k of keys) {
                let projectBcIds = bcLog.filter(i => i.projectItemOrderCode === k).map(i => i.projectBcId)
                array.push({
                  projectItemOrderCode: k,
                  projectBcIds,
                })
              }
              logArray.push({
                label: item.dictLabel,
                value: item.dictValue,
                array,
              })
            }
          }
          this.logArray = logArray
          this.bcLogList = bcLogList
        }
      },
      immediate: true,
    }
  },
  data() {
    return {
      queryParams: {
        name: null,
      },
      currentPage: 1,
      pageSize: 10,
      loading: false,
      btnLoading: false,
      bomOpen: false,
      bcOpen: false,
      open: false,
      fullscreenFlag: false,
      bcTypeOptions: [],
      bomData: [],
      bomTree: [],
      currentBcArray: [],
      typeOptions: [
        {label: '包材开发',value: '0'},
        {label: '客户开发',value: '1'},
        {label: '采购开发',value: '2'},
        {label: '自制',value: '3'},  //20241122研发开发改为自制
      ],
      resourceTypeOptions: [
        {label: '普通添加',value: 'customer'},
        {label: 'bom',value: 'bom'},
        {label: '包材库',value: 'bc'},
        {label: '包材开发',value: 'dev'},
      ],
      attrOptions: [],
      zrqOptions: [],
      materialTypeOptions: [],
      supplierList: [],
      title: null,
      columns: [
        {label: '开发类型',visible: true},
        {label: '项目包材编码',visible: true},
        {label: '类别',visible: true},
        {label: '包装材料',visible: true},
        {label: '包材库编码',visible: true},
        {label: '包材ERP编码',visible: true},
        {label: '物料属性',visible: true},
        {label: '名称',visible: true},
        {label: '规格',visible: true},
        {label: '尺寸',visible: true},
        {label: '型号',visible: true},
        {label: '供应商',visible: true},
        {label: '图片',visible: false},
        {label: 'COA/SPEC',visible: false},
        {label: '备注',visible: true},
      ],
      logArray: [],
      bcLogList: [],
      logOptions: [],
      priceArray: [],
      configArray: [],
      files: [],
      currentRow: {},
      gradedTypeOptions: [
        {label: '订单价',value: '0'},
        {label: 'MOQ价',value: '1'},
        {label: '梯度价(一档)',value: '2'},
        {label: '梯度价(二档)',value: '3'},
        {label: '梯度价(三档)',value: '4'},
        {label: '梯度价(四档)',value: '5'},
        {label: '梯度价(五档)',value: '6'},
        {label: '梯度价(六档)',value: '7'},
        {label: '梯度价(七档)',value: '8'},
        {label: '梯度价(八档)',value: '9'},
        {label: '梯度价(九档)',value: '10'},
      ],
      stageOptions: [
        {label:'裸包价',value: '0'},
        {label:'寻样阶段',value: '1'},
        {label:'打样阶段',value: '2'},
        {label:'订单阶段',value: '3'},
      ],
      allocatorOptions: [],
      erpCode: [],
      typeOpen: false,
      treeOpen: false,
      mb005Options: [
        {label: '包材',value: '104'},
        {label: '半成品',value: '103'},
        {label: '裸装品',value: '102'},
        {label: '成品',value: '101'},
      ],
      rules: {
        mb005: [
          {required: true,msg: '请选择物料类型'}
        ]
      },
      materialArray: [],
    }
  },
  async created() {
    this.getDicts("PRODUCT_PROPERTIES").then(response => {
      const attrOptions = response.data
      attrOptions.push({dictLabel: '自制',dictValue: '0'})
      attrOptions.push({dictLabel: '外购',dictValue: '1'})
      attrOptions.push({dictLabel: '客指代采',dictValue: '3'})
      this.attrOptions = attrOptions
    })
    let logRes = await this.getDicts("project_bc_log")
    this.logOptions = logRes.data;
    let bcTypeRes = await this.getDicts("project_bc_type")
    this.bcTypeOptions = bcTypeRes.data;
    this.getDicts("bc-zrq").then(response => {
      this.zrqOptions = response.data;
    })
    this.getDicts("BZCL").then(response => {
      this.materialTypeOptions = response.data;
    })
    this.getDicts("bc-fpq").then(response => {
      this.allocatorOptions = response.data;
    })
    let supplierList = await supplierAll({supplierType: '1',reqType:1});
    this.supplierList = supplierList;
  },
  methods: {
    getList() {
      let materialArray = this.materialList.filter(i => i.mb005 !== '103')
      let params = Object.assign({}, this.queryParams)
      if (params.name) {
        materialArray = materialArray.filter(i => i.name == params.name)
      }
      this.materialArray = materialArray.slice((this.currentPage-1) * this.pageSize, this.currentPage * this.pageSize)
    },
    handleQuery() {
      this.currentPage = 1
      this.getList()
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleCurrentChange(val) {
      this.currentPage = val
    },
    openDoc() {
      window.open('https://view.officeapps.live.com/op/view.aspx?src=https://enow.oss-cn-beijing.aliyuncs.com/images/20240909/1725847832537.pptx')
    },
    async confirmTree() {
      const params = {
        id: this.currentRow.id,
        mb005: this.currentRow.mb005,
        zrqType: this.currentRow.zrqType,
        allocator: this.currentRow.allocator.join(','),
        type: this.currentRow.type,
      }
      const res = await updateBcForce(params)
      this.treeOpen = false
    },
    mb005Change() {
      this.currentRow.type = null
      this.currentRow.zrqType = null
      this.currentRow.allocator = []
    },
    typeChange() {
      this.currentRow.mb005 = '104'
      this.currentRow.zrqType = null
      this.currentRow.allocator = []
    },
    zrqTypeChange() {
      this.currentRow.mb005 = '104'
      this.currentRow.type = '0'
    },
    allocatorChange() {
      this.currentRow.mb005 = '104'
      this.currentRow.type = '0'
    },
    async confirmMaterial() {
      await this.$refs["form"].validate()
      const form = this.currentRow
      if(form.mb005 === '104') {
        if(!form.type) {
          this.msgError('请选择包材类型')
          return
        }
        if(form.type==='0') { // 如果是主包材,没有选择主包材类型
          if(!form.zrqType) {
            this.msgError('请选择主包材类型')
            return
          }
        }
      }
      this.typeOpen = false
    },
    allocatorText(allocator) {
      const arr = this.allocatorOptions.filter(i=> allocator.includes(i.dictValue))
      if(arr && arr[0]) {
        return arr.map(i=> i.dictLabel).join(',')
      }
    },
    mb005Text(mb005) {
      const arr = this.mb005Options.filter(i=> mb005 === i.value)
      if(arr && arr[0]) {
        return arr[0].label
      }
    },
    showType(row) {
      this.currentRow = row
      this.treeOpen = true
    },
    materialText(item) {
      const array = []
      if(item.mb005) {
        array.push(this.mb005Text(item.mb005))
        if(item.mb005 === '104') {
          if(item.type) {
            array.push(this.selectDictLabel(this.bcTypeOptions,item.type))
          }
          if(item.zrqType) {
            array.push(this.selectDictLabel(this.zrqOptions,item.zrqType))
          }
          if(item.allocator.length) {
            array.push(this.allocatorText(item.allocator))
          }
        }
        return array.join('/')
      } else {
        return "请选择"
      }
    },
    async showCol() {
      await this.$nextTick()
      this.$refs.customCols.columnsOpen = true
    },
    colSuccess() {

    },
    valueToLabel(options,value) {
      const arr = options.filter(i=> i.value === value)
      if(arr && arr[0]) {
        return arr[0].label
      }
    },
    priceText(row) {
      if(row.priceArray) {
        const o = row.priceArray[0]
        const array = []
        if(o.stage) {
          array.push(this.valueToLabel(this.stageOptions,o.stage))
        }
        array.push("(")
        array.push(o.createDate)
        array.push(")")
        if(o.array && o.array[0]) {
          const sub = o.array[0]
          if(sub.gradedType) {
            array.push(this.valueToLabel(this.gradedTypeOptions,sub.gradedType))
          }
          if(sub.price) {
            array.push(sub.price)
          }
          if(sub.moq) {
            array.push('起订量:' + sub.moq)
          }
        }
        return array.join('')
      }
    },
    validateTable() {
      for (const item of this.materialList) {
        if(!item.opType) {
          throw new Error('请选择开发类型!')
          return
        }
        if(!item.name) {
          throw new Error('请输入名称!')
          return
        }
        if(!item.mb005) {
          throw new Error('请输入物料类型!')
          return
        }
        if(!item.mb008) {
          throw new Error('请输入物料属性!')
          return
        }
        if(item.mb005 === '104') {
          if(!item.type) {
            throw new Error('请选择类别!')
            return
          }
          if(item.type==='0' && (!item.zrqType && !item.allocator.length)) {//主容器或分配器二选一
            throw new Error('请选择主容器类别或分配器!')
            return
          }
        }
      }
    },
    async saveSuccess() {
      this.$emit("saveSuccess")
      this.open = false
    },
    resetPriceArray() {
      this.configArray = [
        '1'
      ]
      this.priceArray = []
      this.files = []
    },
    async handlePrice(row) {
      this.currentRow = row
      this.resetPriceArray()
      if(row.priceArray) {
        this.priceArray = row.priceArray
      }
      if(row.configArray) {
        this.configArray = row.configArray
      }
      if(row.files) {
        this.files = row.files
      }
      this.open = true
    },
    resourceText(resourceType) {
      const arr = this.resourceTypeOptions.filter(i=> i.value === resourceType)
      if(arr && arr[0]) {
        return arr[0].label
      }
    },
    fileChange(fileList,id) {
      for(let item of this.materialList) {
        if(item.key === id || item.id === id) {
          item.files = fileList
        }
      }
    },
    confirmBc() {
      for (const item of this.currentBcArray) {
        let o = {
          resourceType: 'bc',
          opType: null,
          code: null,
          bcCode: item.materialCode,
          erpCode: item.erpCode,
          name: item.materialName,
          spec: item.capacity,
          type: null,
          zrqType: null,
          allocator: [],
          materialType: null,
          length: item.length,
          width: item.width,
          height: item.height,
          model: null,
          vendor: null,
          ecgy: null,
          remark: null,
          imgs: null,
          files: [],
          priceArray: [],
          configArray: [],
          supplierId: null,
          devStatus: this.devStatus,
        }
        this.materialList.push(o)
      }
      this.bcOpen = false
    },
    bcChange(bcArray) {
      this.currentBcArray = bcArray
    },
    selectBc() {
      this.currentBcArray = []
      this.bcOpen = true
    },
    async confirmBom() {
      await this.$nextTick()
      const ids = this.$refs.bomTree.getCheckedNodes().map(i => i.id)
      const arr = this.bomData.filter(i=>ids.includes(i.id))
      if(arr && arr[0]) {
        for (const item of arr) {
          let o = {
            resourceType: 'bom',
            opType: null,
            code: null,
            bcCode: null,
            erpCode: item.md003,
            name: item.mb002,
            mb005: item.mb005,
            mb008: item.mb008,
            spec: null,
            type: null,
            zrqType: null,
            allocator: [],
            materialType: null,
            length: 0,
            width: 0,
            height: 0,
            model: null,
            vendor: null,
            ecgy: null,
            remark: null,
            imgs: null,
            files: [],
            priceArray: [],
            configArray: [],
            supplierId: null,
            devStatus: this.devStatus,
          }
          this.materialList.push(o)
        }
      }
      this.bomOpen = false
    },
    toBomTree(list, md001) {
      return list.filter(item => {
        if(md001) {
          if (item.md001 === md001) {
            let children = this.toBomTree(list, item.md003)
            if(children && children.length > 0) {
              item.children = children
            }
            return true
          }
        } else {
          if ([undefined,null,''].includes(item.md001)) {
            let children = this.toBomTree(list, item.md003)
            if(children && children.length > 0) {
              item.children = children
            }
            return true
          }
        }
        return false
      })
    },
    async getErpInfo() {
      const erpNo = this.erpCode
      if (erpNo) {
        this.btnLoading = true
        this.loading = true
        let erpRes = await getProductByMd003(erpNo);
        if(erpRes && erpRes.data) {
          let data = erpRes.data
          let form = this.form
          let mb002 = data.MB002
          if(mb002){
            form.ma003 = data.MA003
            form.mb002 = mb002
            form.mb005 = data.MB005

            let bomList = await erpBom({erpNo})
            bomList = bomList.filter(i => i.mb005 !== '105')
            bomList.push({
              id: erpNo,
              mb002: mb002,
              md003: erpNo,
              mb005: data.MB005,
              mb008: data.mb008,
            })
            let bomTree = this.toBomTree(JSON.parse(JSON.stringify(bomList)), undefined)
            this.bomTree = bomTree
            this.bomData = bomList
          }else{
            this.msgError('erp代码输入有误');
            form.ma003 = null;
            form.mb002 = null;
          }
        } else {
          this.msgError('erp代码输入有误');
        }
        this.btnLoading = false
        this.loading = false
      }
    },
    async showBom() {
      this.bomOpen = true
    },
    addItem() {
      let o = {
        rid: this.$nanoid(),
        resourceType: 'customer',
        opType: null,
        type: null,
        code: null,
        bcCode: null,
        erpCode: null,
        name: null,
        spec: null,
        zrqType: null,
        allocator: [],
        materialType: null,
        length: 0,
        width: 0,
        height: 0,
        model: null,
        vendor: null,
        ecgy: null,
        remark: null,
        imgs: null,
        files: [],
        priceArray: [],
        configArray: [],
        supplierId: null,
        devStatus: this.devStatus,
      }
      this.materialList.push(o)
    },
    async delItem(row){
      let index;
      if(row.id) {
        index = this.materialList.findIndex(i=> i.id === row.id)
      } else if(row.rid) {
        index = this.materialList.findIndex(i=> i.rid === row.rid)
      }
      this.materialList.splice(index,1)
      if(row.id) {
        await delBc(row.id)
      }
    },
    tdClass(projectBcIds,projectBcId) {
      return projectBcIds.includes(projectBcId) ? 'el-icon-check' : ''
    },
  }
}
</script>
<style lang="scss" scoped>
.row-wrapper {
  display: flex;

  .label {
    width: 150px;
    font-size: 13px;
    font-weight: 600;
  }

  .content {

  }
}
.table-wrapper {

  .base-table {
    thead {
      position: sticky;
      top: 0;
      z-index: 3;

      .nth0 {
        background-color: rgba(248,248,249,1);
      }

    }

    tbody {
      .nth0 {
        background-color: rgba(255,255,255,1);
      }
    }

    .nth0 {
      position: sticky;
      left: 0;
      z-index: 1;
    }

  }

}
</style>
