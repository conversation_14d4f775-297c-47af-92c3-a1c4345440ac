import request from '@/utils/request'

// 查询功效存档列表
export function listArchive(query) {
  return request({
    url: '/gx/archive/list',
    method: 'get',
    params: query
  })
}

// 查询功效存档详细
export function getArchive(id) {
  return request({
    url: '/gx/archive/' + id,
    method: 'get'
  })
}

// 新增功效存档
export function addArchive(data) {
  return request({
    url: '/gx/archive',
    method: 'post',
    data: data
  })
}

// 修改功效存档
export function updateArchive(data) {
  return request({
    url: '/gx/archive',
    method: 'put',
    data: data
  })
}

// 导出功效存档
export function exportArchive(query) {
  return request({
    url: '/gx/archive/export',
    method: 'get',
    params: query
  })
}
