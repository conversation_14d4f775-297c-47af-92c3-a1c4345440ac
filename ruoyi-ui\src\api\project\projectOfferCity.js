import request from '@/utils/request'

// 查询报价-运费-城市列表
export function listProjectOfferCity(query) {
  return request({
    url: '/project/projectOfferCity/list',
    method: 'get',
    params: query
  })
}

// 查询报价-运费-城市列表
export function allDelivreyFeeList(query) {
  return request({
    url: '/project/projectOfferCity/allDelivreyFeeList',
    method: 'get',
    params: query
  })
}

// 查询报价-运费-城市 物流公司费用
export function allSupplierFeeList(query) {
  return request({
    url: '/project/projectOfferCity/allSupplierFeeList',
    method: 'get',
    params: query
  })
}

// 查询报价-运费-城市  所有物流公司
export function allOfferSupplierList(query) {
  return request({
    url: '/project/projectOfferCity/allOfferSupplierList',
    method: 'get',
    params: query
  })
}

// 查询报价-运费-城市详细
export function getProjectOfferCity(id) {
  return request({
    url: '/project/projectOfferCity/' + id,
    method: 'get'
  })
}

// 新增报价-运费-城市
export function addProjectOfferCity(data) {
  return request({
    url: '/project/projectOfferCity',
    method: 'post',
    data: data
  })
}

// 修改报价-运费-城市
export function updateProjectOfferCity(data) {
  return request({
    url: '/project/projectOfferCity',
    method: 'put',
    data: data
  })
}

// 删除报价-运费-城市
export function delProjectOfferCity(id) {
  return request({
    url: '/project/projectOfferCity/' + id,
    method: 'delete'
  })
}

// 导出报价-运费-城市
export function exportProjectOfferCity(query) {
  return request({
    url: '/project/projectOfferCity/export',
    method: 'get',
    params: query
  })
}
