import request from '@/utils/request'

// 查询财务到账记录列表
export function listRecord(query) {
  return request({
    url: '/finance/record/list',
    method: 'get',
    params: query
  })
}
// 查询财务到账记录列表
export function listRecordBusiness(query) {
  return request({
    url: '/finance/record/listBusiness',
    method: 'get',
    params: query
  })
}

// 查询财务到账记录详细
export function getRecord(id) {
  return request({
    url: '/finance/record/' + id,
    method: 'get'
  })
}

// 新增财务到账记录
export function addRecord(data) {
  return request({
    url: '/finance/record',
    method: 'post',
    data: data
  })
}

// 修改财务到账记录
export function updateRecord(data) {
  return request({
    url: '/finance/record',
    method: 'put',
    data: data
  })
}

// 删除财务到账记录
export function delRecord(id) {
  return request({
    url: '/finance/record/' + id,
    method: 'delete'
  })
}

// 导出财务到账记录
export function exportRecord(query) {
  return request({
    url: '/finance/record/export',
    method: 'get',
    params: query
  })
}


export function allCustomerFinanceRecordDataList(query) {
  return request({
    url: '/finance/record/allCustomerFinanceRecordDataList',
    method: 'get',
    params: query
  })
}

export function accountCompanyDataList(query) {
  return request({
    url: '/finance/accountInfo/allAccountCompany',
    method: 'get',
    params: query
  })
}


// 用户状态修改
export function changeFinancesTips(id, isTips) {
  const data = {
    id,
    isTips
  }
  return request({
    url: '/finance/record/changeFinancesTips',
    method: 'put',
    data: data
  })
}
