import request from '@/utils/request'

// 查询供应商管理列表
export function listSupplier(query) {
  return request({
    url: '/supplier/supplier/list',
    method: 'get',
    params: query
  })
}

// 查询供应商管理详细
export function getSupplier(id) {
  return request({
    url: '/supplier/supplier/' + id,
    method: 'get'
  })
}

// 新增供应商管理
export function addSupplier(data) {
  return request({
    url: '/supplier/supplier',
    method: 'post',
    data: data
  })
}

// 修改供应商管理
export function updateSupplier(data) {
  return request({
    url: '/supplier/supplier',
    method: 'put',
    data: data
  })
}

// 删除供应商管理
export function delSupplier(id) {
  return request({
    url: '/supplier/supplier/' + id,
    method: 'delete'
  })
}

// 导出供应商管理
export function exportSupplier(query) {
  return request({
    url: '/supplier/supplier/export',
    method: 'get',
    params: query
  })
}

// 查询供应商联系人列表
export function listSupplierContracts(query) {
  return request({
    url: '/supplier/supplier/contractsListAll',
    method: 'get',
    params: query
  })
}

// 查询供应商联系人列表
export function listSupplierBillingDetail(query) {
  return request({
    url: '/supplier/supplier/contractsListDetail',
    method: 'get',
    params: query
  })
}
// 查询供应商财务
export function listSupplierBillingAll(query) {
  return request({
    url: '/supplier/supplier/financeListAll',
    method: 'get',
    params: query
  })
}
// 查询供应商品牌 列表
export function listSupplierCustomerBrand(query) {
  return request({
    url: '/supplier/supplier/brandListAll',
    method: 'get',
    params: query
  })
}

// 查询供应商账期 列表
export function listSupplierPay(query) {
  return request({
    url: '/supplier/supplier/payListAll',
    method: 'get',
    params: query
  })
}

// 查询供应商列表
export function supplierAll(query) {
  return request({
    url: '/supplier/supplier/supplierAll',
    method: 'get',
    params: query
  })
}

export function allSupplier(query) {
  return request({
    url: '/supplier/supplier/all',
    method: 'get',
    params: query
  })
}
