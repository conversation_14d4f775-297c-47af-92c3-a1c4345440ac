{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\userTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\userTable.vue", "mtime": 1753954679644}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\babel.config.js", "mtime": 1743382537964}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_attendanceLog", "require", "_list", "_interopRequireDefault", "_save", "_schedulePlan", "_dispositionPlan", "_mesTimeLine", "_mes<PERSON><PERSON>w", "_time", "name", "components", "MesTimeLine", "MesProductPlanSave", "MesHoursList", "props", "userArray", "type", "Array", "required", "mesHoursList", "sumHours", "Number", "dayHours", "Object", "watch", "handler", "_this", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "filterUser", "stop", "immediate", "deep", "data", "btnLoading", "loading", "title", "fullscreenFlag", "attendanceLogOpen", "selectAttendanceLogOpen", "open", "planOpen", "queryParams", "userCode", "nick<PERSON><PERSON>", "statusOptions", "filterUserArray", "attendanceLogList", "currentRow", "logOpen", "mesLogArray", "opTypeOptions", "label", "value", "exceptionOptions", "currentType", "created", "_this2", "_callee2", "_callee2$", "_context2", "getDicts", "then", "response", "methods", "sailingsChange", "$emit", "mesLotLogs", "row", "_this3", "_callee3", "waitList", "lotLogs", "_iterator", "_step", "_loop", "_callee3$", "_context4", "lotNo", "allMesLotWaitVo", "sent", "allWipLotLog", "_createForOfIteratorHelper2", "lotLog", "arr", "_loop$", "_context3", "opType", "filter", "i", "waitDate", "createTime", "reasonName", "s", "n", "done", "<PERSON><PERSON><PERSON>", "t1", "e", "f", "finish", "planView", "_this4", "_callee4", "mesProductPlanSave", "res", "_callee4$", "_context5", "$nextTick", "$refs", "reset", "planType", "getSchedulePlanByCode", "planCode", "getDispositionPlan", "init", "id", "mesLog", "_this5", "_callee5", "_callee5$", "_context6", "getList", "selectUserTime", "time", "_this6", "_callee6", "_callee6$", "_context7", "attendanceMinutes", "diffMinutes", "attendanceEndTime", "attendanceStartTime", "attendanceArray", "startTime", "endTime", "selectAttendanceLog", "user", "_this7", "_callee7", "workDate", "searchDateArray", "params", "_iterator2", "_step2", "item", "_callee7$", "_context8", "push", "moment", "add", "format", "userId", "allAttendanceLog", "userCheckTime", "err", "t0", "attendanceLog", "_this8", "_callee8", "_iterator3", "_step3", "_callee8$", "_context9", "reset<PERSON><PERSON>y", "resetForm"], "sources": ["src/views/production/dayHours/userTable.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-divider content-position=\"left\" >产线</el-divider>\r\n\r\n    <el-form ref=\"queryForm\" :model=\"queryParams\" label-width=\"80px\" size=\"mini\" >\r\n      <el-row>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"工号\" prop=\"userCode\">\r\n            <el-input v-model=\"queryParams.userCode\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"姓名\" prop=\"nickName\">\r\n            <el-input v-model=\"queryParams.nickName\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"filterUser\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <div class=\"table-wrapper\">\r\n      <table class=\"base-table small-table\" >\r\n        <colgroup>\r\n          <col style=\"width: 50px\" /><!-- 序号 -->\r\n          <col style=\"width: 100px\" /><!-- 员工姓名 -->\r\n          <col style=\"width: 120px\" /><!-- 工号 -->\r\n          <col style=\"width: 100px\" /><!-- 类型 -->\r\n          <col style=\"width: 160px\" /><!-- 开始时间 -->\r\n          <col style=\"width: 160px\" /><!-- 结束时间 -->\r\n          <col style=\"width: 100px\" /><!-- 时长 -->\r\n          <col style=\"width: 100px\" /><!-- 异常状态 -->\r\n          <col style=\"width: 120px\" /><!-- 设备编号 -->\r\n          <col style=\"width: 160px\" /><!-- 批次号 -->\r\n          <col style=\"width: 120px\" /><!-- 工作日期 -->\r\n          <col style=\"width: 160px\" /><!-- 进站时间 -->\r\n          <col style=\"width: 160px\" /><!-- 出站时间 -->\r\n          <col style=\"width: 100px\" /><!-- 工时 -->\r\n          <col style=\"width: 100px\" /><!-- sap工时 -->\r\n          <col style=\"width: 100px\" /><!-- 休息工时 -->\r\n          <col style=\"width: 500px\" /><!-- 有效时段 -->\r\n          <col style=\"width: 100px\" /><!-- 有效工时 -->\r\n          <col style=\"width: 100px\" /><!-- 无效工时 -->\r\n          <col style=\"width: 100px\" /><!-- 工资工时 -->\r\n          <col style=\"width: 100px\" /><!-- 修正工时 -->\r\n          <col style=\"width: 120px\" /><!-- 异常状态 -->\r\n          <col style=\"width: 240px\" /><!-- 备注 -->\r\n        </colgroup>\r\n        <thead>\r\n          <tr >\r\n            <th :rowspan=\"2\" >序号</th>\r\n            <th :rowspan=\"2\" >员工姓名</th>\r\n            <th :rowspan=\"2\" >工号</th>\r\n            <th :colspan=\"5\" >工时对比</th>\r\n            <th :colspan=\"7\" >sap根据mes设备进出站记录拆解后的记录</th>\r\n            <th :rowspan=\"2\" >休息工时\r\n              <el-tooltip content=\"工厂标准休息时间与mes时间的交集半点向下取整\" >\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th :rowspan=\"2\" >\r\n              有效时段\r\n            </th>\r\n            <th :rowspan=\"2\" >\r\n              有效工时\r\n              <el-tooltip content=\"sap工时\" >\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th :rowspan=\"2\" >\r\n              无效工时\r\n              <el-tooltip content=\"工资工时-有效工时\" >\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th :rowspan=\"2\" >\r\n              工资工时\r\n              <el-tooltip >\r\n                <div slot=\"content\">\r\n                  mes时长半点向下取整\r\n                </div>\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th :rowspan=\"2\" >\r\n              修正工时\r\n              <el-tooltip >\r\n                <div slot=\"content\">\r\n                  修正过后以修正的工时为准\r\n                </div>\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th :rowspan=\"2\" >\r\n              异常状态\r\n              <el-tooltip >\r\n                <div slot=\"content\">\r\n                  <div>1.上工时间早于考勤打卡时间:上工考勤异常(代上工风险)</div>\r\n                  <div>2.下工时间晚于考勤打卡时间:下工考勤异常(代下工风险)</div>\r\n                  <div>3.下工 20分钟后未打卡:下班考勤异常</div>\r\n                  <div>4.末道产线记录出站,15分钟内员工未下工:下工异常</div>\r\n                  <div>5.上工后半小时没有匹配上产线:上工异常</div>\r\n                  <div>6.转场超过半小时:转场异常 (扣除标准休息时间)</div>\r\n                  <div>7.有效工时低于工资工时 85%,有效工时异常.</div>\r\n                  <div>8.末道产线记录出站后,超出 15 分钟后才下工,或没有下工数据的,且考勤时间晚于末道产线记录 20 分钟之后的,工资工时以末道产线记录出站/暂停时间为准.</div>\r\n                  <div>9.没有上下班考勤记录的,工资工时统一记录为 0,需人工核实,修改工资工时需审批</div>\r\n                </div>\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th :rowspan=\"2\" >备注</th>\r\n          </tr>\r\n          <tr>\r\n            <th >类型</th>\r\n            <th >开始时间</th>\r\n            <th >结束时间</th>\r\n            <th >时长</th>\r\n            <th >\r\n              异常状态\r\n              <el-tooltip >\r\n                <div slot=\"content\">\r\n                  <div>1.上工时间早于考勤打卡时间:上工考勤异常(代上工风险)</div>\r\n                  <div>2.下工时间晚于考勤打卡时间:下工考勤异常(代下工风险)</div>\r\n                  <div>3.下工 20分钟后未打卡:下班考勤异常</div>\r\n                  <div>4.末道产线记录出站,15分钟内员工未下工:下工异常</div>\r\n                  <div>5.上工后半小时没有匹配上产线:上工异常</div>\r\n                  <div>6.转场超过半小时:转场异常 (扣除标准休息时间)</div>\r\n                  <div>7.有效工时低于工资工时 85%,有效工时异常.</div>\r\n                  <div>8.末道产线记录出站后,超出 15 分钟后才下工,或没有下工数据的,且考勤时间晚于末道产线记录 20 分钟之后的,工资工时以末道产线记录出站/暂停时间为准.</div>\r\n                  <div>9.没有上下班考勤记录的,工资工时统一记录为 0,需人工核实,修改工资工时需审批</div>\r\n                </div>\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th >设备编号</th>\r\n            <th >批次号</th>\r\n            <th >工作日期</th>\r\n            <th >进站时间</th>\r\n            <th >出站时间</th>\r\n            <th >工时</th>\r\n            <th >sap工时</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr v-for=\"(u,i) in filterUserArray\" :key=\"u.userId\">\r\n            <td >{{i+1}}</td>\r\n            <td >{{u.nickName}}</td>\r\n            <td >{{u.userCode}}</td>\r\n            <td :colspan=\"5\" style=\"width: 620px;padding: 0\" >\r\n              <table class=\"base-table small-table\" >\r\n                <tr>\r\n                  <th style=\"width: 100px\" >考勤</th>\r\n                  <td style=\"width: 160px\" >\r\n                    <span style=\"color: #1c84c6;cursor: pointer\" @click=\"selectAttendanceLog(u,'attendanceStart')\">\r\n                      {{u.attendanceStartTime}}\r\n                    </span>\r\n                  </td>\r\n                  <td style=\"width: 160px\" >\r\n                    <span style=\"color: #1c84c6;cursor: pointer\" @click=\"selectAttendanceLog(u,'attendanceEnd')\">\r\n                      {{u.attendanceEndTime}}\r\n                    </span>\r\n                  </td>\r\n                  <td style=\"width: 100px\" >\r\n                    <span style=\"color: #1c84c6;cursor: pointer\" @click=\"attendanceLog(u.userId)\">\r\n                      {{minutesToHours(u.attendanceMinutes).toFixed(2)}}\r\n                    </span>\r\n                  </td>\r\n                  <td :rowspan=\"3\" style=\"width: 100px\" >\r\n                    <div v-for=\"e in u.exceptionArray\" :key=\"e\" style=\"color: #F56C6C\" >\r\n                      {{selectOptionsLabel(exceptionOptions,e)}}\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n                <tr>\r\n                  <th style=\"width: 100px\" >mes</th>\r\n                  <td style=\"width: 160px\" >{{u.mesMinTime}}</td>\r\n                  <td style=\"width: 160px\" >{{u.mesMaxTime}}</td>\r\n                  <td style=\"width: 100px\" >\r\n                    <span style=\"color: #1c84c6;cursor: pointer\" @click=\"mesLog(u)\">\r\n                      {{minutesToHours(u.mesMinutes).toFixed(2)}}\r\n                    </span>\r\n                  </td>\r\n                </tr>\r\n                <tr>\r\n                  <th style=\"width: 100px\" >sap</th>\r\n                  <td style=\"width: 160px\" >{{u.sapMinTime}}</td>\r\n                  <td style=\"width: 160px\" >{{u.sapMaxTime}}</td>\r\n                  <td style=\"width: 100px\" >{{minutesToHours(u.sapMinutes).toFixed(2)}}</td>\r\n                </tr>\r\n              </table>\r\n            </td>\r\n            <td :colspan=\"6\" style=\"width: 820px;padding: 0\" >\r\n              <table class=\"base-table small-table\" >\r\n                <tr v-for=\"h in u.sapArray\" :key=\"h.id\">\r\n                  <td style=\"width: 120px\" >{{h.equipmentNo}}</td>\r\n                  <td style=\"width: 160px\" >\r\n                    <span style=\"color: #1c84c6;cursor: pointer\" @click=\"mesLotLogs(h)\">\r\n                      {{h.lotNo}}\r\n                    </span>\r\n                  </td>\r\n                  <td style=\"width: 120px\" >{{h.workDate}}</td>\r\n                  <td style=\"width: 160px\" >{{h.startTime}}</td>\r\n                  <td style=\"width: 160px\" >{{h.endTime}}</td>\r\n                  <td style=\"width: 100px\" >{{minutesToHours(h.minutes).toFixed(2)}}</td>\r\n                </tr>\r\n              </table>\r\n            </td>\r\n            <td >{{minutesToHours(u.sapSumMinutes).toFixed(2)}}</td>\r\n            <td >{{minutesToHours(u.restMinutes).toFixed(2)}}</td>\r\n            <td >\r\n              <MesTimeLine\r\n                :time-array=\"u.timeArray\"\r\n                :attendance-array=\"u.attendanceArray\"\r\n                :mes-array=\"u.mesArray\"\r\n                :sap-array=\"u.sapArray\"\r\n              />\r\n            </td>\r\n            <td >{{minutesToHours(u.effectiveMinutes).toFixed(2)}}</td>\r\n            <td >{{minutesToHours(u.invalidMinutes).toFixed(2)}}</td>\r\n            <td >\r\n              {{minutesToHours(u.wagesMinutes).toFixed(2)}}\r\n            </td>\r\n            <td >\r\n              <!-- v-if=\"u.exceptionArray && u.exceptionArray.length\" -->\r\n              <el-input v-model=\"u.finalMinutes\" autosize size=\"mini\" @input=\"$emit('computeItemData')\" />\r\n            </td>\r\n            <td >\r\n              <div v-for=\"e in u.exceptionArray\" :key=\"e\" style=\"color: #F56C6C\" >\r\n                {{selectOptionsLabel(exceptionOptions,e)}}\r\n              </div>\r\n            </td>\r\n            <td >\r\n              <el-input v-model=\"u.remark\" autosize size=\"mini\" />\r\n            </td>\r\n          </tr>\r\n          <tr>\r\n            <th>合计</th>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td>{{sumHours}}</td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <el-dialog :close-on-click-modal=\"false\" :visible.sync=\"selectAttendanceLogOpen\" append-to-body width=\"600px\">\r\n      <table class=\"base-table small-table\">\r\n        <tr>\r\n          <th style=\"width: 320px\">打卡地址</th>\r\n          <th style=\"width: 180px\">打卡时间</th>\r\n        </tr>\r\n        <tr v-for=\"item in attendanceLogList\" :key=\"item.id\" >\r\n          <td>{{item.userAddress}}</td>\r\n          <td>\r\n            <span style=\"color: #00afff;cursor: pointer\" @click=\"selectUserTime(item.userCheckTime)\" >\r\n              {{item.userCheckTime}}\r\n            </span>\r\n          </td>\r\n        </tr>\r\n      </table>\r\n    </el-dialog>\r\n\r\n    <el-dialog :close-on-click-modal=\"false\" :visible.sync=\"attendanceLogOpen\" append-to-body width=\"600px\">\r\n      <table class=\"base-table small-table\">\r\n        <tr>\r\n          <th style=\"width: 320px\">打卡地址</th>\r\n          <th style=\"width: 180px\">打卡时间</th>\r\n        </tr>\r\n        <tr v-for=\"item in attendanceLogList\" :key=\"item.id\" >\r\n          <td>{{item.userAddress}}</td>\r\n          <td>{{item.userCheckTime}}</td>\r\n        </tr>\r\n      </table>\r\n    </el-dialog>\r\n\r\n    <el-dialog :fullscreen=\"fullscreenFlag\" :visible.sync=\"open\" width=\"1200px\" :close-on-click-modal=\"false\" append-to-body>\r\n      <div class=\"dialog-title\" slot=\"title\">{{ title }}\r\n        <el-button @click=\"fullscreenFlag = !fullscreenFlag\" type=\"text\"\r\n                   :icon=\"fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'\"/>\r\n      </div>\r\n      <MesHoursList ref=\"mesHoursList\" :work-date=\"dayHours.workDate\" :user-code=\"currentRow.userCode\" @sailingsChange=\"sailingsChange\" />\r\n    </el-dialog>\r\n\r\n    <el-dialog :title=\"title\" :fullscreen=\"fullscreenFlag\" :visible.sync=\"planOpen\" width=\"1200px\" :close-on-click-modal=\"false\"  append-to-body>\r\n      <div class=\"dialog-title\" slot=\"title\">\r\n        {{title}}\r\n        <el-button @click=\"fullscreenFlag = !fullscreenFlag\" type=\"text\"\r\n                   :icon=\"fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'\" />\r\n      </div>\r\n      <MesProductPlanSave ref=\"mesProductPlanSave\" :readonly=\"true\" :plan-type=\"currentRow.planType\" />\r\n    </el-dialog>\r\n\r\n    <el-dialog :fullscreen=\"fullscreenFlag\" :visible.sync=\"logOpen\" width=\"1200px\" :close-on-click-modal=\"false\"\r\n               append-to-body>\r\n      <div class=\"dialog-title\" slot=\"title\">{{ title }}\r\n        <el-button @click=\"fullscreenFlag = !fullscreenFlag\" type=\"text\"\r\n                   :icon=\"fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'\"/>\r\n      </div>\r\n      <div class=\"table-wrapper\">\r\n        <table class=\"base-table small-table\" >\r\n          <tr>\r\n            <th style=\"width: 80px\" >作业站</th>\r\n            <th style=\"width: 120px\" >类型</th>\r\n            <th style=\"width: 120px\" >时间</th>\r\n            <th style=\"width: 180px\" >暂停原因</th>\r\n            <th style=\"width: 100px\" >数量</th>\r\n            <th style=\"width: 120px\" >人员编号</th>\r\n            <th style=\"width: 80px\" >人员名称</th>\r\n          </tr>\r\n          <tr v-for=\"item in mesLogArray\" :key=\"item.lotNo\" >\r\n            <td style=\"width: 80px\" >{{item.opNo}}</td>\r\n            <td style=\"width: 120px\" >{{ selectOptionsLabel(opTypeOptions, item.opType)}}</td>\r\n            <td style=\"width: 120px\" >{{item.createTime}}</td>\r\n            <td style=\"width: 180px\" >{{item.reasonName}}</td>\r\n            <td style=\"width: 100px\" >{{item.qty}}</td>\r\n            <td style=\"width: 120px\" >{{item.userNo}}</td>\r\n            <td style=\"width: 80px\" >{{item.userName}}</td>\r\n          </tr>\r\n        </table>\r\n      </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n<script >\r\nimport {allAttendanceLog} from \"@/api/hr/attendanceLog\";\r\nimport MesHoursList from \"@/views/production/mesHours/list.vue\";\r\nimport MesProductPlanSave from \"@/views/mes/production/production/save.vue\";\r\nimport {getSchedulePlanByCode} from \"@/api/production/schedulePlan\";\r\nimport {getDispositionPlan} from \"@/api/production/dispositionPlan\";\r\nimport MesTimeLine from \"@/views/production/dayHours/mesTimeLine.vue\";\r\nimport {allMesLotWaitVo, allWipLotLog} from \"@/api/mes/mesView\";\r\nimport {diffMinutes} from \"@/utils/production/time\";\r\n\r\nexport default {\r\n  name: 'dayHoursUserTable',\r\n  components: {MesTimeLine, MesProductPlanSave, MesHoursList},\r\n  props: {\r\n    userArray: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    mesHoursList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    sumHours: {\r\n      type: Number,\r\n      required: true,\r\n    },\r\n    dayHours: {\r\n      type: Object,\r\n      required: true,\r\n    },\r\n  },\r\n  watch: {\r\n    userArray: {\r\n      async handler() {\r\n        await this.filterUser()\r\n      },\r\n      immediate: true,\r\n      deep: true,\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      loading: false,\r\n      title: '',\r\n      fullscreenFlag: true,\r\n      attendanceLogOpen: false,\r\n      selectAttendanceLogOpen: false,\r\n      open: false,\r\n      planOpen: false,\r\n      queryParams: {\r\n        userCode: null,\r\n        nickName: null,\r\n      },\r\n      statusOptions: [],\r\n      filterUserArray: [],\r\n      attendanceLogList: [],\r\n      currentRow: {},\r\n      logOpen: false,\r\n      mesLogArray: [],\r\n      opTypeOptions: [\r\n        {label: '进站',value: 'CHECKIN'},\r\n        {label: '暂停',value: 'WAITDISPOSITION'},\r\n        {label: '解除暂停-继续生产',value: 'RELEASE-GO'},\r\n        {label: '出站',value: 'CHECKOUT'},\r\n        {label: '解除暂停-结束生产',value: 'RELEASE-Inventory'},\r\n        {label: '设备变更',value: 'EQPCHANGE'},\r\n        {label: '开批',value: 'LOTCREATE'},\r\n      ],\r\n      exceptionOptions: [\r\n        {label: '上工考勤异常',value: 1},\r\n        {label: '下工考勤异常',value: 2},\r\n        {label: 'mes上工异常',value: 3},\r\n        {label: 'mes下工异常',value: 4},\r\n        {label: '转场异常',value: 5},\r\n        {label: '有效工时异常',value: 6},\r\n        {label: 'sap上工异常',value: 7},\r\n        {label: 'sap下工异常',value: 8},\r\n      ],\r\n      currentType: null,\r\n    }\r\n  },\r\n  async created() {\r\n    this.getDicts(\"production_status\").then(response => {\r\n      this.statusOptions = response.data\r\n    })\r\n  },\r\n  methods: {\r\n    sailingsChange(userCode){\r\n      this.$emit('sailingsChange',userCode)\r\n    },\r\n    async mesLotLogs(row) {\r\n      this.logOpen = true\r\n      this.title = row.lotNo + \"关联的生产批操作记录\";\r\n      const waitList = await allMesLotWaitVo({lotNo: row.lotNo})\r\n      const lotLogs = await allWipLotLog({lotNo: row.lotNo})\r\n      for (const lotLog of lotLogs) {\r\n        if(lotLog.opType === 'WAITDISPOSITION') {\r\n          const arr = waitList.filter(i=> i.waitDate === lotLog.createTime)\r\n          if(arr && arr[0]) {\r\n            lotLog.reasonName = arr[0].reasonName\r\n          }\r\n        }\r\n      }\r\n      this.mesLogArray = lotLogs\r\n    },\r\n    async planView(row) {\r\n      this.currentRow = row\r\n      this.planOpen = true\r\n      this.title = '计划详情'\r\n      await this.$nextTick()\r\n      const mesProductPlanSave = this.$refs.mesProductPlanSave\r\n      if(mesProductPlanSave) {\r\n        mesProductPlanSave.reset()\r\n        let res\r\n        if(row.planType === 'production') {\r\n          res = await getSchedulePlanByCode(row.planCode)\r\n        } else {\r\n          res = await getDispositionPlan(row.planCode)\r\n        }\r\n        await mesProductPlanSave.init(res.data.id)\r\n      }\r\n    },\r\n    async mesLog(row) {\r\n      this.currentRow = row\r\n      this.open = true\r\n      this.title = 'mes工时明细'\r\n      await this.$nextTick()\r\n      await this.$refs.mesHoursList.getList()\r\n    },\r\n    async selectUserTime(time) {\r\n      this.currentRow[this.currentType + 'Time'] = time\r\n      this.currentRow.attendanceMinutes = diffMinutes(this.currentRow.attendanceEndTime,this.currentRow.attendanceStartTime)\r\n      this.currentRow.attendanceArray = [{startTime: this.currentRow.attendanceStartTime,endTime: this.currentRow.attendanceEndTime}]\r\n      this.selectAttendanceLogOpen = false\r\n    },\r\n    async selectAttendanceLog(user,type) {\r\n      const workDate = this.dayHours.workDate\r\n      if(workDate) {\r\n        const searchDateArray = [workDate,]\r\n        searchDateArray.push(this.moment(workDate).add(1, 'days').format('YYYY-MM-DD'))\r\n        const params = {\r\n          userId: user.userId,\r\n          searchDateArray\r\n        }\r\n        try {\r\n          this.btnLoading = true\r\n          const attendanceLogList = await allAttendanceLog(params)\r\n          for (const item of attendanceLogList) {\r\n            if(item.userCheckTime) {\r\n              item.userCheckTime = this.moment(item.userCheckTime).format('YYYY-MM-DD HH:mm:ss')\r\n            }\r\n          }\r\n          this.attendanceLogList = attendanceLogList\r\n          this.btnLoading = false\r\n          this.currentRow = user\r\n          this.currentType = type\r\n          this.selectAttendanceLogOpen = true\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n    async attendanceLog(userId) {\r\n      const workDate = this.dayHours.workDate\r\n      if(workDate) {\r\n        const searchDateArray = [workDate,]\r\n        searchDateArray.push(this.moment(workDate).add(1, 'days').format('YYYY-MM-DD'))\r\n        const params = {\r\n          userId,\r\n          searchDateArray\r\n        }\r\n        try {\r\n          this.btnLoading = true\r\n          const attendanceLogList = await allAttendanceLog(params)\r\n          for (const item of attendanceLogList) {\r\n            if(item.userCheckTime) {\r\n              item.userCheckTime = this.moment(item.userCheckTime).format('YYYY-MM-DD HH:mm:ss')\r\n            }\r\n          }\r\n          this.attendanceLogList = attendanceLogList\r\n          this.btnLoading = false\r\n          this.attendanceLogOpen = true\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.filterUser()\r\n    },\r\n    filterUser() {\r\n      let filterUserArray = this.userArray\r\n      const queryParams = this.queryParams\r\n      if (queryParams.userCode) {\r\n        filterUserArray = filterUserArray.filter(i => i.userCode === queryParams.userCode)\r\n      }\r\n      if (queryParams.nickName) {\r\n        filterUserArray = filterUserArray.filter(i => i.nickName === queryParams.nickName)\r\n      }\r\n      this.filterUserArray = filterUserArray\r\n    },\r\n  },\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.table-wrapper {\r\n  max-height: 80vh;\r\n\r\n  .base-table {\r\n\r\n    thead {\r\n      position: sticky;\r\n      top: 0;\r\n      z-index: 3;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAqVA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,KAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,aAAA,GAAAJ,OAAA;AACA,IAAAK,gBAAA,GAAAL,OAAA;AACA,IAAAM,YAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,QAAA,GAAAP,OAAA;AACA,IAAAQ,KAAA,GAAAR,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAS,IAAA;EACAC,UAAA;IAAAC,WAAA,EAAAA,oBAAA;IAAAC,kBAAA,EAAAA,aAAA;IAAAC,YAAA,EAAAA;EAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,QAAA;IACA;IACAC,YAAA;MACAH,IAAA,EAAAC,KAAA;MACAC,QAAA;IACA;IACAE,QAAA;MACAJ,IAAA,EAAAK,MAAA;MACAH,QAAA;IACA;IACAI,QAAA;MACAN,IAAA,EAAAO,MAAA;MACAL,QAAA;IACA;EACA;EACAM,KAAA;IACAT,SAAA;MACAU,OAAA,WAAAA,QAAA;QAAA,IAAAC,KAAA;QAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;UAAA,WAAAF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAC,SAAAC,QAAA;YAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;cAAA;gBAAAF,QAAA,CAAAE,IAAA;gBAAA,OACAV,KAAA,CAAAW,UAAA;cAAA;cAAA;gBAAA,OAAAH,QAAA,CAAAI,IAAA;YAAA;UAAA,GAAAP,OAAA;QAAA;MACA;MACAQ,SAAA;MACAC,IAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,OAAA;MACAC,KAAA;MACAC,cAAA;MACAC,iBAAA;MACAC,uBAAA;MACAC,IAAA;MACAC,QAAA;MACAC,WAAA;QACAC,QAAA;QACAC,QAAA;MACA;MACAC,aAAA;MACAC,eAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,OAAA;MACAC,WAAA;MACAC,aAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,gBAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAE,WAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,WAAAtC,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAoC,SAAA;MAAA,WAAArC,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAmC,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAAjC,IAAA,GAAAiC,SAAA,CAAAhC,IAAA;UAAA;YACA6B,MAAA,CAAAI,QAAA,sBAAAC,IAAA,WAAAC,QAAA;cACAN,MAAA,CAAAZ,aAAA,GAAAkB,QAAA,CAAA9B,IAAA;YACA;UAAA;UAAA;YAAA,OAAA2B,SAAA,CAAA9B,IAAA;QAAA;MAAA,GAAA4B,QAAA;IAAA;EACA;EACAM,OAAA;IACAC,cAAA,WAAAA,eAAAtB,QAAA;MACA,KAAAuB,KAAA,mBAAAvB,QAAA;IACA;IACAwB,UAAA,WAAAA,WAAAC,GAAA;MAAA,IAAAC,MAAA;MAAA,WAAAlD,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAgD,SAAA;QAAA,IAAAC,QAAA,EAAAC,OAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,KAAA;QAAA,WAAAtD,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAoD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlD,IAAA,GAAAkD,SAAA,CAAAjD,IAAA;YAAA;cACAyC,MAAA,CAAApB,OAAA;cACAoB,MAAA,CAAAjC,KAAA,GAAAgC,GAAA,CAAAU,KAAA;cAAAD,SAAA,CAAAjD,IAAA;cAAA,OACA,IAAAmD,wBAAA;gBAAAD,KAAA,EAAAV,GAAA,CAAAU;cAAA;YAAA;cAAAP,QAAA,GAAAM,SAAA,CAAAG,IAAA;cAAAH,SAAA,CAAAjD,IAAA;cAAA,OACA,IAAAqD,qBAAA;gBAAAH,KAAA,EAAAV,GAAA,CAAAU;cAAA;YAAA;cAAAN,OAAA,GAAAK,SAAA,CAAAG,IAAA;cAAAP,SAAA,OAAAS,2BAAA,CAAA9D,OAAA,EACAoD,OAAA;cAAAK,SAAA,CAAAlD,IAAA;cAAAgD,KAAA,oBAAAtD,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAqD,MAAA;gBAAA,IAAAQ,MAAA,EAAAC,GAAA;gBAAA,WAAA/D,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA6D,OAAAC,SAAA;kBAAA,kBAAAA,SAAA,CAAA3D,IAAA,GAAA2D,SAAA,CAAA1D,IAAA;oBAAA;sBAAAuD,MAAA,GAAAT,KAAA,CAAArB,KAAA;sBACA,IAAA8B,MAAA,CAAAI,MAAA;wBACAH,GAAA,GAAAb,QAAA,CAAAiB,MAAA,WAAAC,CAAA;0BAAA,OAAAA,CAAA,CAAAC,QAAA,KAAAP,MAAA,CAAAQ,UAAA;wBAAA;wBACA,IAAAP,GAAA,IAAAA,GAAA;0BACAD,MAAA,CAAAS,UAAA,GAAAR,GAAA,IAAAQ,UAAA;wBACA;sBACA;oBAAA;oBAAA;sBAAA,OAAAN,SAAA,CAAAxD,IAAA;kBAAA;gBAAA,GAAA6C,KAAA;cAAA;cAAAF,SAAA,CAAAoB,CAAA;YAAA;cAAA,KAAAnB,KAAA,GAAAD,SAAA,CAAAqB,CAAA,IAAAC,IAAA;gBAAAlB,SAAA,CAAAjD,IAAA;gBAAA;cAAA;cAAA,OAAAiD,SAAA,CAAAmB,aAAA,CAAArB,KAAA;YAAA;cAAAE,SAAA,CAAAjD,IAAA;cAAA;YAAA;cAAAiD,SAAA,CAAAjD,IAAA;cAAA;YAAA;cAAAiD,SAAA,CAAAlD,IAAA;cAAAkD,SAAA,CAAAoB,EAAA,GAAApB,SAAA;cAAAJ,SAAA,CAAAyB,CAAA,CAAArB,SAAA,CAAAoB,EAAA;YAAA;cAAApB,SAAA,CAAAlD,IAAA;cAAA8C,SAAA,CAAA0B,CAAA;cAAA,OAAAtB,SAAA,CAAAuB,MAAA;YAAA;cAEA/B,MAAA,CAAAnB,WAAA,GAAAsB,OAAA;YAAA;YAAA;cAAA,OAAAK,SAAA,CAAA/C,IAAA;UAAA;QAAA,GAAAwC,QAAA;MAAA;IACA;IACA+B,QAAA,WAAAA,SAAAjC,GAAA;MAAA,IAAAkC,MAAA;MAAA,WAAAnF,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAiF,SAAA;QAAA,IAAAC,kBAAA,EAAAC,GAAA;QAAA,WAAApF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAkF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhF,IAAA,GAAAgF,SAAA,CAAA/E,IAAA;YAAA;cACA0E,MAAA,CAAAtD,UAAA,GAAAoB,GAAA;cACAkC,MAAA,CAAA7D,QAAA;cACA6D,MAAA,CAAAlE,KAAA;cAAAuE,SAAA,CAAA/E,IAAA;cAAA,OACA0E,MAAA,CAAAM,SAAA;YAAA;cACAJ,kBAAA,GAAAF,MAAA,CAAAO,KAAA,CAAAL,kBAAA;cAAA,KACAA,kBAAA;gBAAAG,SAAA,CAAA/E,IAAA;gBAAA;cAAA;cACA4E,kBAAA,CAAAM,KAAA;cAAA,MAEA1C,GAAA,CAAA2C,QAAA;gBAAAJ,SAAA,CAAA/E,IAAA;gBAAA;cAAA;cAAA+E,SAAA,CAAA/E,IAAA;cAAA,OACA,IAAAoF,mCAAA,EAAA5C,GAAA,CAAA6C,QAAA;YAAA;cAAAR,GAAA,GAAAE,SAAA,CAAA3B,IAAA;cAAA2B,SAAA,CAAA/E,IAAA;cAAA;YAAA;cAAA+E,SAAA,CAAA/E,IAAA;cAAA,OAEA,IAAAsF,mCAAA,EAAA9C,GAAA,CAAA6C,QAAA;YAAA;cAAAR,GAAA,GAAAE,SAAA,CAAA3B,IAAA;YAAA;cAAA2B,SAAA,CAAA/E,IAAA;cAAA,OAEA4E,kBAAA,CAAAW,IAAA,CAAAV,GAAA,CAAAxE,IAAA,CAAAmF,EAAA;YAAA;YAAA;cAAA,OAAAT,SAAA,CAAA7E,IAAA;UAAA;QAAA,GAAAyE,QAAA;MAAA;IAEA;IACAc,MAAA,WAAAA,OAAAjD,GAAA;MAAA,IAAAkD,MAAA;MAAA,WAAAnG,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAiG,SAAA;QAAA,WAAAlG,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAgG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9F,IAAA,GAAA8F,SAAA,CAAA7F,IAAA;YAAA;cACA0F,MAAA,CAAAtE,UAAA,GAAAoB,GAAA;cACAkD,MAAA,CAAA9E,IAAA;cACA8E,MAAA,CAAAlF,KAAA;cAAAqF,SAAA,CAAA7F,IAAA;cAAA,OACA0F,MAAA,CAAAV,SAAA;YAAA;cAAAa,SAAA,CAAA7F,IAAA;cAAA,OACA0F,MAAA,CAAAT,KAAA,CAAAlG,YAAA,CAAA+G,OAAA;YAAA;YAAA;cAAA,OAAAD,SAAA,CAAA3F,IAAA;UAAA;QAAA,GAAAyF,QAAA;MAAA;IACA;IACAI,cAAA,WAAAA,eAAAC,IAAA;MAAA,IAAAC,MAAA;MAAA,WAAA1G,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAwG,SAAA;QAAA,WAAAzG,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAuG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArG,IAAA,GAAAqG,SAAA,CAAApG,IAAA;YAAA;cACAiG,MAAA,CAAA7E,UAAA,CAAA6E,MAAA,CAAAtE,WAAA,aAAAqE,IAAA;cACAC,MAAA,CAAA7E,UAAA,CAAAiF,iBAAA,OAAAC,iBAAA,EAAAL,MAAA,CAAA7E,UAAA,CAAAmF,iBAAA,EAAAN,MAAA,CAAA7E,UAAA,CAAAoF,mBAAA;cACAP,MAAA,CAAA7E,UAAA,CAAAqF,eAAA;gBAAAC,SAAA,EAAAT,MAAA,CAAA7E,UAAA,CAAAoF,mBAAA;gBAAAG,OAAA,EAAAV,MAAA,CAAA7E,UAAA,CAAAmF;cAAA;cACAN,MAAA,CAAAtF,uBAAA;YAAA;YAAA;cAAA,OAAAyF,SAAA,CAAAlG,IAAA;UAAA;QAAA,GAAAgG,QAAA;MAAA;IACA;IACAU,mBAAA,WAAAA,oBAAAC,IAAA,EAAAjI,IAAA;MAAA,IAAAkI,MAAA;MAAA,WAAAvH,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAqH,SAAA;QAAA,IAAAC,QAAA,EAAAC,eAAA,EAAAC,MAAA,EAAA/F,iBAAA,EAAAgG,UAAA,EAAAC,MAAA,EAAAC,IAAA;QAAA,WAAA5H,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA0H,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxH,IAAA,GAAAwH,SAAA,CAAAvH,IAAA;YAAA;cACAgH,QAAA,GAAAF,MAAA,CAAA5H,QAAA,CAAA8H,QAAA;cAAA,KACAA,QAAA;gBAAAO,SAAA,CAAAvH,IAAA;gBAAA;cAAA;cACAiH,eAAA,IAAAD,QAAA;cACAC,eAAA,CAAAO,IAAA,CAAAV,MAAA,CAAAW,MAAA,CAAAT,QAAA,EAAAU,GAAA,YAAAC,MAAA;cACAT,MAAA;gBACAU,MAAA,EAAAf,IAAA,CAAAe,MAAA;gBACAX,eAAA,EAAAA;cACA;cAAAM,SAAA,CAAAxH,IAAA;cAEA+G,MAAA,CAAAxG,UAAA;cAAAiH,SAAA,CAAAvH,IAAA;cAAA,OACA,IAAA6H,+BAAA,EAAAX,MAAA;YAAA;cAAA/F,iBAAA,GAAAoG,SAAA,CAAAnE,IAAA;cAAA+D,UAAA,OAAA7D,2BAAA,CAAA9D,OAAA,EACA2B,iBAAA;cAAA;gBAAA,KAAAgG,UAAA,CAAAlD,CAAA,MAAAmD,MAAA,GAAAD,UAAA,CAAAjD,CAAA,IAAAC,IAAA;kBAAAkD,IAAA,GAAAD,MAAA,CAAA3F,KAAA;kBACA,IAAA4F,IAAA,CAAAS,aAAA;oBACAT,IAAA,CAAAS,aAAA,GAAAhB,MAAA,CAAAW,MAAA,CAAAJ,IAAA,CAAAS,aAAA,EAAAH,MAAA;kBACA;gBACA;cAAA,SAAAI,GAAA;gBAAAZ,UAAA,CAAA7C,CAAA,CAAAyD,GAAA;cAAA;gBAAAZ,UAAA,CAAA5C,CAAA;cAAA;cACAuC,MAAA,CAAA3F,iBAAA,GAAAA,iBAAA;cACA2F,MAAA,CAAAxG,UAAA;cACAwG,MAAA,CAAA1F,UAAA,GAAAyF,IAAA;cACAC,MAAA,CAAAnF,WAAA,GAAA/C,IAAA;cACAkI,MAAA,CAAAnG,uBAAA;cAAA4G,SAAA,CAAAvH,IAAA;cAAA;YAAA;cAAAuH,SAAA,CAAAxH,IAAA;cAAAwH,SAAA,CAAAS,EAAA,GAAAT,SAAA;cAEAT,MAAA,CAAAxG,UAAA;YAAA;YAAA;cAAA,OAAAiH,SAAA,CAAArH,IAAA;UAAA;QAAA,GAAA6G,QAAA;MAAA;IAGA;IACAkB,aAAA,WAAAA,cAAAL,MAAA;MAAA,IAAAM,MAAA;MAAA,WAAA3I,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAyI,SAAA;QAAA,IAAAnB,QAAA,EAAAC,eAAA,EAAAC,MAAA,EAAA/F,iBAAA,EAAAiH,UAAA,EAAAC,MAAA,EAAAhB,IAAA;QAAA,WAAA5H,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA0I,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxI,IAAA,GAAAwI,SAAA,CAAAvI,IAAA;YAAA;cACAgH,QAAA,GAAAkB,MAAA,CAAAhJ,QAAA,CAAA8H,QAAA;cAAA,KACAA,QAAA;gBAAAuB,SAAA,CAAAvI,IAAA;gBAAA;cAAA;cACAiH,eAAA,IAAAD,QAAA;cACAC,eAAA,CAAAO,IAAA,CAAAU,MAAA,CAAAT,MAAA,CAAAT,QAAA,EAAAU,GAAA,YAAAC,MAAA;cACAT,MAAA;gBACAU,MAAA,EAAAA,MAAA;gBACAX,eAAA,EAAAA;cACA;cAAAsB,SAAA,CAAAxI,IAAA;cAEAmI,MAAA,CAAA5H,UAAA;cAAAiI,SAAA,CAAAvI,IAAA;cAAA,OACA,IAAA6H,+BAAA,EAAAX,MAAA;YAAA;cAAA/F,iBAAA,GAAAoH,SAAA,CAAAnF,IAAA;cAAAgF,UAAA,OAAA9E,2BAAA,CAAA9D,OAAA,EACA2B,iBAAA;cAAA;gBAAA,KAAAiH,UAAA,CAAAnE,CAAA,MAAAoE,MAAA,GAAAD,UAAA,CAAAlE,CAAA,IAAAC,IAAA;kBAAAkD,IAAA,GAAAgB,MAAA,CAAA5G,KAAA;kBACA,IAAA4F,IAAA,CAAAS,aAAA;oBACAT,IAAA,CAAAS,aAAA,GAAAI,MAAA,CAAAT,MAAA,CAAAJ,IAAA,CAAAS,aAAA,EAAAH,MAAA;kBACA;gBACA;cAAA,SAAAI,GAAA;gBAAAK,UAAA,CAAA9D,CAAA,CAAAyD,GAAA;cAAA;gBAAAK,UAAA,CAAA7D,CAAA;cAAA;cACA2D,MAAA,CAAA/G,iBAAA,GAAAA,iBAAA;cACA+G,MAAA,CAAA5H,UAAA;cACA4H,MAAA,CAAAxH,iBAAA;cAAA6H,SAAA,CAAAvI,IAAA;cAAA;YAAA;cAAAuI,SAAA,CAAAxI,IAAA;cAAAwI,SAAA,CAAAP,EAAA,GAAAO,SAAA;cAEAL,MAAA,CAAA5H,UAAA;YAAA;YAAA;cAAA,OAAAiI,SAAA,CAAArI,IAAA;UAAA;QAAA,GAAAiI,QAAA;MAAA;IAGA;IACAK,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAxI,UAAA;IACA;IACAA,UAAA,WAAAA,WAAA;MACA,IAAAiB,eAAA,QAAAvC,SAAA;MACA,IAAAmC,WAAA,QAAAA,WAAA;MACA,IAAAA,WAAA,CAAAC,QAAA;QACAG,eAAA,GAAAA,eAAA,CAAA0C,MAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAA9C,QAAA,KAAAD,WAAA,CAAAC,QAAA;QAAA;MACA;MACA,IAAAD,WAAA,CAAAE,QAAA;QACAE,eAAA,GAAAA,eAAA,CAAA0C,MAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAA7C,QAAA,KAAAF,WAAA,CAAAE,QAAA;QAAA;MACA;MACA,KAAAE,eAAA,GAAAA,eAAA;IACA;EACA;AACA", "ignoreList": []}]}