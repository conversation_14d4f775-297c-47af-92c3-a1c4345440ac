import request from '@/utils/request'

// 查询原料检验记录列表
export function listMaterialRecord(query) {
  return request({
    url: '/qc/materialRecord/list',
    method: 'get',
    params: query
  })
}

// 查询原料检验记录详细
export function getMaterialRecord(id) {
  return request({
    url: '/qc/materialRecord/' + id,
    method: 'get'
  })
}

// 查询原料检验记录详细
export function getMaterialRecordDetail(id) {
  return request({
    url: '/qc/materialRecord/detail/' + id,
    method: 'get'
  })
}

// 新增原料检验记录
export function addMaterialRecord(data) {
  return request({
    url: '/qc/materialRecord',
    method: 'post',
    data: data
  })
}

// 修改原料检验记录
export function updateMaterialRecord(data) {
  return request({
    url: '/qc/materialRecord',
    method: 'put',
    data: data
  })
}

// 删除原料检验记录
export function delMaterialRecord(id) {
  return request({
    url: '/qc/materialRecord/' + id,
    method: 'delete'
  })
}

// 导出原料检验记录
export function exportMaterialRecord(query) {
  return request({
    url: '/qc/materialRecord/export',
    method: 'get',
    params: query
  })
}

export function exportB(query) {
  return request({
    url: '/qc/materialRecord/exportB',
    method: 'get',
    params: query
  })
}

export function exportQcMaterialRecord(query) {
  return request({
    url: '/qc/materialRecord/exportMaterialRecord',
    method: 'get',
    params: query
  })
}

export function exportQrCode(query) {
  return request({
    url: '/qc/materialRecord/exportQrCode',
    method: 'get',
    params: query
  })
}

export function allMaterialRecord(query) {
  return request({
    url: '/qc/materialRecord/all',
    method: 'get',
    params: query
  })
}

export function microbeMaterialRecord(query) {
  return request({
    url: '/qc/materialRecord/microbeArray',
    method: 'get',
    params: query
  })
}

export function recentAllMaterialRecord(query) {
  return request({
    url: '/qc/materialRecord/recentAll',
    method: 'get',
    params: query
  })
}

export function exportMaterialBg(id) {
  return request({
    url: '/qc/materialRecord/exportMaterialBg/' + id,
    method: 'get',
  })
}

export function exportMaterialJl(id) {
  return request({
    url: '/qc/materialRecord/exportMaterialJl/' + id,
    method: 'get',
  })
}


export function printLableDetail(data) {
  return request({
    url: '/qc/materialRecord/printLableDetail',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

//取样标签
export function printLableDetail2(data) {
  return request({
    url: '/qc/materialRecord/printLableDetail2',
    method: 'post',
    data,
    responseType: 'blob'
  })
}
