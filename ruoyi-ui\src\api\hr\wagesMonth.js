import request from '@/utils/request'

// 查询月度工资计算列表
export function listWagesMonth(query) {
  return request({
    url: '/hr/wagesMonth/list',
    method: 'get',
    params: query
  })
}
export function listAuditWagesMonth(query) {
  return request({
    url: '/hr/wagesMonth/audit',
    method: 'get',
    params: query
  })
}
export function getWagesMonth(id) {
  return request({
    url: '/hr/wagesMonth/' + id,
    method: 'get'
  })
}
export function getWagesMonthDetail(id) {
  return request({
    url: '/hr/wagesMonth/wagesDetail/' + id,
    method: 'get'
  })
}

export function getWagesMonthDepartDetail(id) {
  return request({
    url: '/hr/wagesMonth/departDetail/' + id,
    method: 'get'
  })
}
export function statisticsWagesMonth(query) {
  return request({
    url: '/hr/wagesMonth/statistics',
    method: 'get',
    params: query
  })
}
export function updateWagesMonthTrend(data) {
  return request({
    url: '/hr/wagesMonth/trend',
    method: 'post',
    data: data
  })
}
export function taxesFinishWagesMonth(data) {
  return request({
    url: '/hr/wagesMonth/taxes_finish',
    method: 'post',
    data: data
  })
}
export function processWagesPayUser(data) {
  return request({
    url: '/hr/wagesMonth/wagesPayUserProcess',
    method: 'post',
    data: data
  })
}
export function roleWagesPayUser(data) {
  return request({
    url: '/hr/wagesMonth/wagesPayUserRole',
    method: 'post',
    data: data
  })
}
export function queryWagesData(data) {
  return request({
    url: '/hr/wagesMonth/queryWagesData',
    method: 'post',
    data: data
  })
}
export function submitAudit(data) {
  return request({
    url: '/hr/wagesMonth/submitAudit',
    method: 'put',
    data: data
  })
}
export function cancelAudit(data) {
  return request({
    url: '/hr/wagesMonth/cancelAudit',
    method: 'put',
    data: data
  })
}
