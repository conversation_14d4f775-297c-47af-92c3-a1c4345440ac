{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\qc\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\qc\\index.vue", "mtime": 1753956861403}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\babel.config.js", "mtime": 1743382537964}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_finishedInspection", "require", "name", "data", "badge", "created", "_this", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getStatisticsData", "stop", "methods", "_this2", "_callee2", "res", "_callee2$", "_context2", "getQualityStatisticsData", "sent", "openWindow", "path", "newpage", "$router", "resolve", "window", "open", "href"], "sources": ["src/views/qc/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"page-wrapper\">\r\n\r\n    <div class=\"second-wrapper\" >\r\n      <div class=\"cell\">\r\n        <div class=\"press\">审核</div>\r\n        <div class=\"row\" v-has-permi=\"['qc:materia:materialStandardAudit']\" >\r\n          <el-badge :value=\"badge.materialStandardAuditCount\" :max=\"99\" :hidden=\"badge.materialStandardAuditCount?false:true\">\r\n            <router-link to='/qc/materialStandardAudit'>\r\n              <span class=\"link\">原料标准审核</span>\r\n            </router-link>\r\n          </el-badge>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:process:audit:microbe']\" >\r\n          <router-link to='/qc/process/audit/microbe'>\r\n            <span class=\"link\">微生物日记录审核</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:process:audit:materialTest']\" >\r\n          <router-link to='/qc/process/audit/materialTest'>\r\n            <span class=\"link\">物料测试审核</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:process:audit:cpfx']\" >\r\n          <router-link to='/qc/process/audit/cpfx'>\r\n            <span class=\"link\">成品放行审核</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\">\r\n          <router-link to='/process/instance?type=2'>\r\n            <span class=\"link\">流程列表</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['process:errorReportSystem:list']\">\r\n          <router-link to='/process/errorReportSystem'>\r\n            <span class=\"link\">异常报告（体系）申请</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['process:errorReportSystem:log']\">\r\n          <router-link to='/process/errorReportSystemLog'>\r\n            <span class=\"link\">异常报告（体系）记录</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['filemanage:filemanage:list']\" >\r\n          <router-link to='/filemanage/filemanage'>\r\n            <span class=\"link\" >文件申请</span>\r\n          </router-link>\r\n        </div>\r\n      </div>\r\n      <div class=\"cell\">\r\n        <div class=\"press\">成品</div>\r\n        <div class=\"row\" v-has-permi=\"['qc:finishedCategory:list']\">\r\n          <router-link to='/qc/finishedCategory'>\r\n            <span class=\"link\">成品物料类型</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:finishedType:list']\">\r\n          <router-link to='/qc/finishedType'>\r\n            <span class=\"link\" >成品检验类型</span>\r\n          </router-link>\r\n        </div>\r\n<!--        <div class=\"row\" v-has-permi=\"['qc:finishedProject:list']\">-->\r\n<!--          <router-link to='/qc/finishedProject'>-->\r\n<!--            <span class=\"link\" >成品检验项目</span>-->\r\n<!--          </router-link>-->\r\n<!--        </div>-->\r\n        <div class=\"row\" v-has-permi=\"['qc:finishedProjectModel:list']\">\r\n          <router-link to='/qc/finishedProjectModel'>\r\n            <span class=\"link\" >成品检验模板</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:finishedStandard:list']\">\r\n          <el-badge :value=\"badge.finishedStandardCount\" :max=\"99\" :hidden=\"badge.finishedStandardCount?false:true\">\r\n            <router-link to='/qc/finishedStandard'>\r\n              <span class=\"link\" >成品检验标准</span>\r\n            </router-link>\r\n          </el-badge>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:finishedInspection:list']\">\r\n          <el-badge :value=\"badge.finishedStandardInspectionCount\" :max=\"99\" :hidden=\"badge.finishedStandardInspectionCount?false:true\">\r\n            <router-link to='/qc/finishedInspection'>\r\n              <span class=\"link\" >成品检验记录</span>\r\n            </router-link>\r\n          </el-badge>\r\n        </div>\r\n      </div>\r\n      <div class=\"cell\">\r\n        <div class=\"press\">包材</div>\r\n        <div class=\"row\" v-has-permi=\"['qc:materialType:list']\">\r\n          <router-link to='/qc/materialType'>\r\n            <span class=\"link\" >包材物料类型</span>\r\n          </router-link>\r\n        </div>\r\n<!--        <div class=\"row\" v-has-permi=\"['qc:materialProject:list']\">-->\r\n<!--          <router-link to='/qc/materialProject'>-->\r\n<!--            <span class=\"link\" >包材检测项目</span>-->\r\n<!--          </router-link>-->\r\n<!--        </div>-->\r\n        <div class=\"row\" v-has-permi=\"['qc:materialProjectModel:list']\">\r\n          <router-link to='/qc/materialProjectModel'>\r\n            <span class=\"link\" >包材检验模板</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:packaging:inspection:standard:list']\">\r\n          <el-badge :value=\"badge.packageStandardCount\" :max=\"99\" :hidden=\"badge.packageStandardCount?false:true\">\r\n            <router-link to='/qc/packagingInspectionStandard'>\r\n              <span class=\"link\" >包材检验标准</span>\r\n            </router-link>\r\n          </el-badge>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:packagingInspectionRecord:list']\">\r\n          <el-badge :value=\"badge.packageStandardInspectionCount\" :max=\"99\" :hidden=\"badge.packageStandardInspectionCount?false:true\">\r\n            <router-link to='/qc/packagingInspectionRecord'>\r\n              <span class=\"link\" >包材检验记录</span>\r\n            </router-link>\r\n          </el-badge>\r\n        </div>\r\n      </div>\r\n      <div class=\"cell\">\r\n        <div class=\"press\">半成品</div>\r\n        <div class=\"row\" v-has-permi=\"['qc:bcpTemplate:list']\">\r\n            <router-link to='/qc/bcpTemplate'>\r\n              <span class=\"link\" >半成品检验模板</span>\r\n            </router-link>\r\n         </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:bcpStandard:list']\">\r\n          <el-badge :value=\"badge.semimanufacturesStandardCount\" :max=\"99\" :hidden=\"badge.semimanufacturesStandardCount?false:true\">\r\n            <router-link to='/qc/bcpStandard'>\r\n              <span class=\"link\" >半成品检验标准</span>\r\n            </router-link>\r\n          </el-badge>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:bcpInspection:list']\">\r\n          <el-badge :value=\"badge.semimanufacturesStandardInspectionCount\" :max=\"99\" :hidden=\"badge.semimanufacturesStandardInspectionCount?false:true\">\r\n            <router-link to='/qc/bcpInspection'>\r\n              <span class=\"link\" >半成品检验记录</span>\r\n            </router-link>\r\n          </el-badge>\r\n        </div>\r\n      </div>\r\n      <div class=\"cell\">\r\n        <div class=\"press\">原料</div>\r\n        <div class=\"row\" v-has-permi=\"['qc:qcMaterialTest:list']\">\r\n          <el-badge :value=\"badge.materialStandardCount\" :max=\"99\" :hidden=\"badge.materialStandardCount?false:true\">\r\n            <router-link to='/qc/qcMaterialTest'>\r\n              <span class=\"link\" >原料检测标准</span>\r\n            </router-link>\r\n          </el-badge>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:materialRecord:list']\">\r\n          <el-badge :value=\"badge.materialStandardInspectionCount\" :max=\"99\" :hidden=\"badge.materialStandardInspectionCount?false:true\">\r\n            <router-link to='/qc/materialRecordAll'>\r\n              <span class=\"link\" >原料检验记录</span>\r\n            </router-link>\r\n          </el-badge>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:materialRecord:blist']\">\r\n          <router-link to='/qc/materialRecordBAll'>\r\n            <span class=\"link\" >B代码检验记录</span>\r\n          </router-link>\r\n        </div>\r\n      </div>\r\n      <div class=\"cell\">\r\n        <div class=\"press\">微生物</div>\r\n        <div class=\"row\" v-has-permi=\"['qc:microbeStandard:list']\">\r\n          <i class=\"ali-icon ali-manufacturing\"></i>\r\n          <router-link to='/qc/microbeStandard'>\r\n            <span class=\"link\" >微生物检验标准</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:microbeInspection:tabs']\">\r\n          <i class=\"ali-icon ali-manufacturing\"></i>\r\n          <router-link to='/qc/microbeInspection/tabs'>\r\n            <span class=\"link\" >微生物检验台账</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:microbeDay:list']\">\r\n          <i class=\"ali-icon ali-manufacturing\"></i>\r\n          <router-link to='/qc/microbeDay'>\r\n            <span class=\"link\" >微生物检验日记录</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:microbeInspection:list']\">\r\n          <i class=\"ali-icon ali-manufacturing\"></i>\r\n          <router-link to='/qc/microbeInspection'>\r\n            <span class=\"link\" >微生物检验记录</span>\r\n          </router-link>\r\n        </div>\r\n      </div>\r\n      <div class=\"cell\">\r\n        <div class=\"press\">其它</div>\r\n        <div class=\"row\" v-has-permi=\"['qc:audit:list']\">\r\n          <router-link to='/qc/audit'>\r\n            <span class=\"link\" >准入检查</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:jcxm:list']\">\r\n          <router-link to='/qc/jcxm'>\r\n            <span class=\"link\" >检测项目(原料/半成品/包材/成品)</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:jcyq:list']\">\r\n          <router-link to='/qc/jcyq'>\r\n            <span class=\"link\" >检测仪器</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:zxbz:list']\">\r\n          <router-link to='/qc/zxbz'>\r\n            <span class=\"link\" >QC执行标准</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:tz:list']\">\r\n          <el-badge :value=\"badge.tzCount\" :max=\"99\" :hidden=\"badge.tzCount?false:true\">\r\n            <router-link to='/qc/tz'>\r\n              <span class=\"link\" >标准品台账</span>\r\n            </router-link>\r\n          </el-badge>\r\n        </div>\r\n        <div v-has-permi=\"['sop:bomChange:all']\" class=\"row\" >\r\n          <router-link to='/sop/bomChange/all'>\r\n            <span class=\"link\" >bom变更全部</span>\r\n          </router-link>\r\n        </div>\r\n        <div v-has-permi=\"['sop:bomChange:all']\" class=\"row\" >\r\n          <router-link to='/sop/bomChange/all'>\r\n            <span class=\"link\" >bom变更全部</span>\r\n          </router-link>\r\n        </div>\r\n        <div v-has-permi=\"['quantity:qc:order']\" class=\"row\">\r\n          <router-link to='/qc/quantityOrderData'>\r\n            <span class=\"link\">订单列表</span>\r\n          </router-link>\r\n        </div>\r\n<!--        <div class=\"row\" v-has-permi=\"['qc:detectCode:list']\">-->\r\n<!--          <router-link to='/qc/detectCode'>-->\r\n<!--            <span class=\"link\" >检测编码</span>-->\r\n<!--          </router-link>-->\r\n<!--        </div>-->\r\n<!--        <div class=\"row\" v-has-permi=\"['qc:jcff:list']\">-->\r\n<!--          <router-link to='/qc/jcff'>-->\r\n<!--            <span class=\"link\" >检测方法</span>-->\r\n<!--          </router-link>-->\r\n<!--        </div>-->\r\n<!--        <div class=\"row\" v-has-permi=\"['qc:defectiveItems:list']\">-->\r\n<!--          <router-link to='/qc/defectiveItems'>-->\r\n<!--            <span class=\"link\" >缺陷项目</span>-->\r\n<!--          </router-link>-->\r\n<!--        </div>-->\r\n      </div>\r\n<!--      <div class=\"cell\">-->\r\n<!--        <div class=\"press\">内容物</div>-->\r\n<!--        <div class=\"row\" v-has-permi=\"['qc:semimanufactures:list']\">-->\r\n<!--             <router-link to='/qc/semimanufactures'>-->\r\n<!--              <span class=\"link\" >内容物检验标准</span>-->\r\n<!--            </router-link>-->\r\n<!--         </div>-->\r\n<!--        <div class=\"row\" v-has-permi=\"['qc:bcpRecord:list']\">-->\r\n<!--            <router-link to='/qc/bcpRecord'>-->\r\n<!--              <span class=\"link\" >内容物检验记录</span>-->\r\n<!--            </router-link>-->\r\n<!--         </div>-->\r\n<!--      </div>-->\r\n      <div class=\"cell\">\r\n        <div class=\"press\">文件管理</div>\r\n        <div class=\"row\" v-has-permi=\"['filemanage:template:list']\" >\r\n          <router-link to='/filemanage/template'>\r\n            <span class=\"link\" >文件模板</span>\r\n          </router-link>\r\n        </div>\r\n        <!-- <div class=\"row\" v-has-permi=\"['filemanage:filemanage:list']\" >\r\n          <router-link to='/filemanage/filemanage'>\r\n            <span class=\"link\" >文件申请</span>\r\n          </router-link>\r\n        </div> -->\r\n        <div class=\"row\" v-has-permi=\"['filemanage:archive:list']\" >\r\n          <router-link to='/filemanage/archive'>\r\n            <span class=\"link\" >档案库</span>\r\n          </router-link>\r\n        </div>\r\n        <!-- <div class=\"row\" v-has-permi=\"['filemanage:archive:list']\" >\r\n          <router-link to='/filemanage/archive/indexOld'>\r\n            <span class=\"link\" >旧档案库</span>\r\n          </router-link>\r\n        </div> -->\r\n        <div class=\"row\" v-has-permi=\"['filemanage:lawregulation:list']\" >\r\n          <router-link to='/filemanage/lawregulation'>\r\n            <span class=\"link\" >法律法规</span>\r\n          </router-link>\r\n        </div>\r\n      </div>\r\n\r\n\r\n\r\n      <div class=\"cell\">\r\n        <div class=\"press\">物料测试</div>\r\n        <div class=\"row\" v-has-permi=\"['qc:materialTesting:list']\">\r\n          <span class=\"link\" @click=\"openWindow('/qc/layout')\" >项目视图</span>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:materialTesting:list']\" >\r\n          <router-link to='/qc/materialTesting'>\r\n            <span class=\"link\" >物料测试存档</span>\r\n          </router-link>\r\n        </div>\r\n        <div v-has-permi=\"['rd:stability:list']\" class=\"row\" >\r\n          <router-link to='/rd/stability/all'>\r\n            <span class=\"link\" >配方稳定性</span>\r\n          </router-link>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {getQualityStatisticsData} from \"@/api/qc/finishedInspection\";\r\n\r\nexport default {\r\n  name: \"qcIndex\",\r\n  data() {\r\n    return {\r\n      badge:{\r\n\r\n      }\r\n    }\r\n  },\r\n  async created() {\r\n    await this.getStatisticsData();\r\n  },\r\n  methods: {\r\n    async getStatisticsData() {\r\n      let res = await getQualityStatisticsData();\r\n      this.badge = res;\r\n    },\r\n    openWindow(path) {\r\n      let newpage = this.$router.resolve({\r\n        path\r\n      })\r\n      window.open(newpage.href, '_blank');\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-wrapper {\r\n  padding: 2vh 1vw;\r\n  background: #fafbfe;\r\n  overflow: auto;\r\n\r\n  .first-wrapper {\r\n    display: grid;\r\n    grid-template-columns: 1fr 1fr 1fr 1fr;\r\n    grid-gap: 2vh 1vw;\r\n    height: 26vh;\r\n\r\n    .cell {\r\n      background: #fff;\r\n      box-shadow: 0 0 35px 0 rgb(154 161 171 / 15%);\r\n      transition: all 250ms cubic-bezier(.02, .01, .47, 1);\r\n      border-radius: 5px;\r\n      padding: 1vh .5vw;\r\n      height: 24vh;\r\n      overflow: auto;\r\n\r\n      .press {\r\n        margin-bottom: 1vh;\r\n      }\r\n\r\n      .row {\r\n        display: flex;\r\n        align-items: center;\r\n        height: 4vh;\r\n        border-bottom: 1px solid #eee;\r\n        padding: .5vh 1vw;\r\n\r\n        .link {\r\n          margin-left: 10px;\r\n          font-size: 12px;\r\n          cursor:pointer;\r\n          color: #00afff;\r\n          transition: all 250ms cubic-bezier(.02, .01, .47, 1);\r\n        }\r\n\r\n        .link:hover {\r\n          box-shadow: 0px 1rem 2rem 0px rgba(48, 55, 66, 0.15);\r\n          transform: translate(0,-5px);\r\n          transition-delay: 0s !important;\r\n        }\r\n      }\r\n\r\n    }\r\n\r\n    .cell:hover {\r\n      box-shadow: 0px 1rem 2rem 0px rgba(48, 55, 66, 0.15);\r\n      transform: translate(0,-5px);\r\n      transition-delay: 0s !important;\r\n    }\r\n\r\n    .row {\r\n      display: flex;\r\n      align-items: center;\r\n      height: 4vh;\r\n      border-bottom: 1px solid #eee;\r\n      padding: .5vh 1vw;\r\n\r\n      .link {\r\n        margin-left: 10px;\r\n        font-size: 12px;\r\n        cursor:pointer;\r\n        color: #00afff;\r\n        transition: all 250ms cubic-bezier(.02, .01, .47, 1);\r\n      }\r\n\r\n      .link:hover {\r\n        box-shadow: 0px 1rem 2rem 0px rgba(48, 55, 66, 0.15);\r\n        transform: translate(0,-5px);\r\n        transition-delay: 0s !important;\r\n      }\r\n    }\r\n  }\r\n\r\n  .second-wrapper {\r\n    display: grid;\r\n    grid-template-columns: 1fr 1fr 1fr;\r\n    grid-gap: 2vh 1vw;\r\n    height: 112vh;\r\n\r\n    .cell {\r\n      background: #fff;\r\n      box-shadow: 0 0 35px 0 rgb(154 161 171 / 15%);\r\n      transition: all 250ms cubic-bezier(.02, .01, .47, 1);\r\n      border-radius: 5px;\r\n      padding: 1vh .5vw;\r\n      height: 36vh;\r\n      overflow: auto;\r\n\r\n      .press {\r\n        margin-bottom: 1vh;\r\n      }\r\n    }\r\n\r\n    .cell:hover {\r\n      box-shadow: 0px 1rem 2rem 0px rgba(48, 55, 66, 0.15);\r\n      transform: translate(0,-5px);\r\n      transition-delay: 0s !important;\r\n    }\r\n\r\n    .row {\r\n      display: flex;\r\n      align-items: center;\r\n      height: 4vh;\r\n      border-bottom: 1px solid #eee;\r\n      padding: .5vh 1vw;\r\n\r\n      .link {\r\n        margin-left: 10px;\r\n        font-size: 12px;\r\n        cursor:pointer;\r\n        color: #00afff;\r\n        transition: all 250ms cubic-bezier(.02, .01, .47, 1);\r\n      }\r\n\r\n      .link:hover {\r\n        box-shadow: 0px 1rem 2rem 0px rgba(48, 55, 66, 0.15);\r\n        transform: translate(0,-5px);\r\n        transition-delay: 0s !important;\r\n      }\r\n\r\n      .disabled {\r\n        color: #909399;\r\n        font-size: 12px;\r\n      }\r\n    }\r\n  }\r\n\r\n}\r\n\r\n::v-deep .el-pagination {\r\n  width: 100%;\r\n  overflow: hidden;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;AA2TA,IAAAA,mBAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA,GAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;MAAA,WAAAF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAV,KAAA,CAAAW,iBAAA;UAAA;UAAA;YAAA,OAAAH,QAAA,CAAAI,IAAA;QAAA;MAAA,GAAAP,OAAA;IAAA;EACA;EACAQ,OAAA;IACAF,iBAAA,WAAAA,kBAAA;MAAA,IAAAG,MAAA;MAAA,WAAAb,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAW,SAAA;QAAA,IAAAC,GAAA;QAAA,WAAAb,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAW,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAT,IAAA,GAAAS,SAAA,CAAAR,IAAA;YAAA;cAAAQ,SAAA,CAAAR,IAAA;cAAA,OACA,IAAAS,4CAAA;YAAA;cAAAH,GAAA,GAAAE,SAAA,CAAAE,IAAA;cACAN,MAAA,CAAAhB,KAAA,GAAAkB,GAAA;YAAA;YAAA;cAAA,OAAAE,SAAA,CAAAN,IAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IACAM,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAC,OAAA,QAAAC,OAAA,CAAAC,OAAA;QACAH,IAAA,EAAAA;MACA;MACAI,MAAA,CAAAC,IAAA,CAAAJ,OAAA,CAAAK,IAAA;IACA;EACA;AACA", "ignoreList": []}]}