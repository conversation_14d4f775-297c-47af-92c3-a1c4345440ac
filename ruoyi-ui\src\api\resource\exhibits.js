import request from '@/utils/request'

// 查询展厅管理列表
export function listExhibits(query) {
  return request({
    url: '/resource/exhibits/list',
    method: 'get',
    params: query
  })
}

// 查询展厅管理详细
export function getExhibits(id) {
  return request({
    url: '/resource/exhibits/' + id,
    method: 'get'
  })
}

// 新增展厅管理
export function addExhibits(data) {
  return request({
    url: '/resource/exhibits',
    method: 'post',
    data: data
  })
}

// 修改展厅管理
export function updateExhibits(data) {
  return request({
    url: '/resource/exhibits',
    method: 'put',
    data: data
  })
}

// 删除展厅管理
export function delExhibits(id) {
  return request({
    url: '/resource/exhibits/' + id,
    method: 'delete'
  })
}

// 导出展厅管理
export function exportExhibits(query) {
  return request({
    url: '/resource/exhibits/export',
    method: 'get',
    params: query
  })
}

//生成太阳码
export function generateQrCode(query) {
  return request({
    url: '/resource/exhibits/generateQrCode',
    method: 'get',
    params: query
  })
}

export function allExhibits(query) {
  return request({
    url: '/resource/exhibits/all',
    method: 'get',
    params: query
  })
}

export function exportTemplateExhibits(query) {
  return request({
    url: '/resource/exhibits/exportTemplate',
    method: 'get',
    params: query
  })
}

