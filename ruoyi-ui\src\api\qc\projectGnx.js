import request from '@/utils/request'

// 查询包材功能性列表
export function listProjectGnx(query) {
  return request({
    url: '/qc/projectGnx/list',
    method: 'get',
    params: query
  })
}

// 查询包材功能性详细
export function getProjectGnx(id) {
  return request({
    url: '/qc/projectGnx/' + id,
    method: 'get'
  })
}

// 新增包材功能性
export function addProjectGnx(data) {
  return request({
    url: '/qc/projectGnx',
    method: 'post',
    data: data
  })
}

// 修改包材功能性
export function updateProjectGnx(data) {
  return request({
    url: '/qc/projectGnx',
    method: 'put',
    data: data
  })
}

// 删除包材功能性
export function delProjectGnx(id) {
  return request({
    url: '/qc/projectGnx/' + id,
    method: 'delete'
  })
}

// 导出包材功能性
export function exportProjectGnx(query) {
  return request({
    url: '/qc/projectGnx/export',
    method: 'get',
    params: query
  })
}