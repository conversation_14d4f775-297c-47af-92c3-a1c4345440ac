import request from '@/utils/request'

// 查询AI简历评估列表
export function listEvaluation(query) {
  return request({
    url: '/hr/evaluation/list',
    method: 'get',
    params: query
  })
}

// 查询AI简历评估详细
export function getEvaluation(id) {
  return request({
    url: '/hr/evaluation/' + id,
    method: 'get'
  })
}

// 新增AI简历评估
export function addEvaluation(data) {
  return request({
    url: '/hr/evaluation',
    method: 'post',
    data: data
  })
}

// 修改AI简历评估
export function updateEvaluation(data) {
  return request({
    url: '/hr/evaluation',
    method: 'put',
    data: data
  })
}

// 删除AI简历评估
export function delEvaluation(id) {
  return request({
    url: '/hr/evaluation/' + id,
    method: 'delete'
  })
}

// 导出AI简历评估
export function exportEvaluation(query) {
  return request({
    url: '/hr/evaluation/export',
    method: 'get',
    params: query
  })
}

// 发送简历评估信息给指定用户
export function sendEvaluation(data) {
  return request({
    url: '/hr/evaluation/sendEvaluation',
    method: 'post',
    data: data
  })
}
