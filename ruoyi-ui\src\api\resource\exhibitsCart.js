import request from '@/utils/request'

// 查询展厅购物车推荐列表
export function listExhibitsCart(query) {
  return request({
    url: '/resource/exhibitsCart/list',
    method: 'get',
    params: query
  })
}

// 查询展厅购物车推荐详细
export function getExhibitsCart(id) {
  return request({
    url: '/resource/exhibitsCart/' + id,
    method: 'get'
  })
}

// 新增展厅购物车推荐
export function addExhibitsCart(data) {
  return request({
    url: '/resource/exhibitsCart',
    method: 'post',
    data: data
  })
}

// 修改展厅购物车推荐
export function updateExhibitsCart(data) {
  return request({
    url: '/resource/exhibitsCart',
    method: 'put',
    data: data
  })
}

// 删除展厅购物车推荐
export function delExhibitsCart(id) {
  return request({
    url: '/resource/exhibitsCart/' + id,
    method: 'delete'
  })
}

// 导出展厅购物车推荐
export function exportExhibitsCart(query) {
  return request({
    url: '/resource/exhibitsCart/export',
    method: 'get',
    params: query
  })
}

export function allExhibitsCart(query) {
  return request({
    url: '/resource/exhibitsCart/all',
    method: 'get',
    params: query
  })
}
