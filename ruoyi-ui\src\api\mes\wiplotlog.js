import request from '@/utils/request'

// 查询生产批历史纪录列表
export function listWiplotlog(query) {
  return request({
    url: '/mes/wiplotlog/list',
    method: 'get',
    params: query
  })
}

// 查询生产批历史纪录详细
export function getWiplotlog(lotserial) {
  return request({
    url: '/mes/wiplotlog/' + lotserial,
    method: 'get'
  })
}

// 新增生产批历史纪录
export function addWiplotlog(data) {
  return request({
    url: '/mes/wiplotlog',
    method: 'post',
    data: data
  })
}

// 修改生产批历史纪录
export function updateWiplotlog(data) {
  return request({
    url: '/mes/wiplotlog',
    method: 'put',
    data: data
  })
}

// 删除生产批历史纪录
export function delWiplotlog(lotserial) {
  return request({
    url: '/mes/wiplotlog/' + lotserial,
    method: 'delete'
  })
}

// 导出生产批历史纪录
export function exportWiplotlog(query) {
  return request({
    url: '/mes/wiplotlog/export',
    method: 'get',
    params: query
  })
}