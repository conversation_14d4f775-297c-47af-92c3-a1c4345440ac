import request from '@/utils/request'

// 查询包材库存记录列表
export function listMaterialStockLog(query) {
  return request({
    url: '/resource/materialStockLog/list',
    method: 'get',
    params: query
  })
}

// 查询包材库存记录详细
export function getMaterialStockLog(id) {
  return request({
    url: '/resource/materialStockLog/' + id,
    method: 'get'
  })
}

// 删除包材库存记录
export function delMaterialStockLog(id) {
  return request({
    url: '/resource/materialStockLog/' + id,
    method: 'delete'
  })
}

// 导出包材库存记录
export function exportMaterialStockLog(query) {
  return request({
    url: '/resource/materialStockLog/export',
    method: 'get',
    params: query
  })
}
