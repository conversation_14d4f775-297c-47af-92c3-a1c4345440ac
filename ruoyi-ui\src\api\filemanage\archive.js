import request from '@/utils/request'

// 查询文件归档列表
export function listArchive(query) {
  return request({
    url: '/filemanage/archive/list',
    method: 'get',
    params: query
  })
}

// 查询文件归档详细
export function getArchive(id) {
  return request({
    url: '/filemanage/archive/' + id,
    method: 'get'
  })
}

// 新增文件归档
export function addArchive(data) {
  return request({
    url: '/filemanage/archive',
    method: 'post',
    data: data
  })
}

// 修改文件归档
export function updateArchive(data) {
  return request({
    url: '/filemanage/archive',
    method: 'put',
    data: data
  })
}

// 删除文件归档
export function delArchive(id) {
  return request({
    url: '/filemanage/archive/' + id,
    method: 'delete'
  })
}

// 导出文件归档
export function exportArchive(query) {
  return request({
    url: '/filemanage/archive/export',
    method: 'get',
    params: query
  })
}
