import request from '@/utils/request'

// 查询立项申请列表
export function listApply(query) {
  return request({
    url: '/project/apply/list',
    method: 'get',
    params: query
  })
}

// 查询立项申请详细
export function getApply(id) {
  return request({
    url: '/project/apply/' + id,
    method: 'get'
  })
}

// 新增立项申请
export function addApply(data) {
  return request({
    url: '/project/apply',
    method: 'post',
    data: data
  })
}

// 修改立项申请
export function updateApply(data) {
  return request({
    url: '/project/apply',
    method: 'put',
    data: data
  })
}

// 删除立项申请
export function delApply(id) {
  return request({
    url: '/project/apply/' + id,
    method: 'delete'
  })
}

// 导出立项申请
export function exportApply(query) {
  return request({
    url: '/project/apply/export',
    method: 'get',
    params: query
  })
}

export function submitAudit(data) {
  return request({
    url: '/project/apply/submitAudit',
    method: 'put',
    data: data
  })
}

//撤销申请
export function cancelAudit(data) {
  return request({
    url: '/project/apply/cancelAudit',
    method: 'put',
    data: data
  })
}

export function exemptApplyAudit(instanceId) {
  return request({
    url: '/project/apply/exemptAudit/' + instanceId,
    method: 'put',
  })
}
