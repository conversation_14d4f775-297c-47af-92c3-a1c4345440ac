import request from '@/utils/request'

// 查询公共流程进行列表
export function listCommonExecution(query) {
  return request({
    url: '/common/commonExecution/list',
    method: 'get',
    params: query
  })
}

// 查询公共流程进行详细
export function getCommonExecution(id) {
  return request({
    url: '/common/commonExecution/' + id,
    method: 'get'
  })
}

// 新增公共流程进行
export function addCommonExecution(data) {
  return request({
    url: '/common/commonExecution',
    method: 'post',
    data: data
  })
}

// 修改公共流程进行
export function updateCommonExecution(data) {
  return request({
    url: '/common/commonExecution',
    method: 'put',
    data: data
  })
}

// 删除公共流程进行
export function delCommonExecution(id) {
  return request({
    url: '/common/commonExecution/' + id,
    method: 'delete'
  })
}

// 导出公共流程进行
export function exportCommonExecution(query) {
  return request({
    url: '/common/commonExecution/export',
    method: 'get',
    params: query
  })
}

// 查询通用订单流程进行列表
export function allCommonOrdreExecutionDataList(query) {
  return request({
    url: '/common/commonExecution/allCommonOrdreExecutionDataList',
    method: 'get',
    params: query
  })
}
