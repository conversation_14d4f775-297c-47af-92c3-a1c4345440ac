import request from '@/utils/request'

// 查询包材相容性列表
export function listProjectXrx(query) {
  return request({
    url: '/qc/projectXrx/list',
    method: 'get',
    params: query
  })
}

// 查询包材相容性详细
export function getProjectXrx(id) {
  return request({
    url: '/qc/projectXrx/' + id,
    method: 'get'
  })
}

// 新增包材相容性
export function addProjectXrx(data) {
  return request({
    url: '/qc/projectXrx',
    method: 'post',
    data: data
  })
}

// 修改包材相容性
export function updateProjectXrx(data) {
  return request({
    url: '/qc/projectXrx',
    method: 'put',
    data: data
  })
}

// 删除包材相容性
export function delProjectXrx(id) {
  return request({
    url: '/qc/projectXrx/' + id,
    method: 'delete'
  })
}

// 导出包材相容性
export function exportProjectXrx(query) {
  return request({
    url: '/qc/projectXrx/export',
    method: 'get',
    params: query
  })
}