import request from '@/utils/request'

// 查询报价包材损耗条目列表
export function listProjectOfferBcLossItem(query) {
  return request({
    url: '/project/projectOfferBcLossItem/list',
    method: 'get',
    params: query
  })
}

// 查询报价包材损耗条目详细
export function getProjectOfferBcLossItem(id) {
  return request({
    url: '/project/projectOfferBcLossItem/' + id,
    method: 'get'
  })
}

// 新增报价包材损耗条目
export function addProjectOfferBcLossItem(data) {
  return request({
    url: '/project/projectOfferBcLossItem',
    method: 'post',
    data: data
  })
}

// 修改报价包材损耗条目
export function updateProjectOfferBcLossItem(data) {
  return request({
    url: '/project/projectOfferBcLossItem',
    method: 'put',
    data: data
  })
}

// 删除报价包材损耗条目
export function delProjectOfferBcLossItem(id) {
  return request({
    url: '/project/projectOfferBcLossItem/' + id,
    method: 'delete'
  })
}

// 导出报价包材损耗条目
export function exportProjectOfferBcLossItem(query) {
  return request({
    url: '/project/projectOfferBcLossItem/export',
    method: 'get',
    params: query
  })
}

export function getBcLoss(data) {
  return request({
    url: '/project/projectOfferBcLossItem/bcLoss',
    method: 'post',
    data,
  })
}
