import request from '@/utils/request'

export function statsOuterHours(query) {
  return request({
    url: '/production/outerHours/stats',
    method: 'get',
    params: query
  })
}


// 查询生产包干工工时列表
export function listOuterHours(query) {
  return request({
    url: '/production/outerHours/list',
    method: 'get',
    params: query
  })
}

// 查询生产包干工工时详细
export function getOuterHours(id) {
  return request({
    url: '/production/outerHours/' + id,
    method: 'get'
  })
}

// 新增生产包干工工时
export function addOuterHours(data) {
  return request({
    url: '/production/outerHours',
    method: 'post',
    data: data
  })
}

// 修改生产包干工工时
export function updateOuterHours(data) {
  return request({
    url: '/production/outerHours',
    method: 'put',
    data: data
  })
}

// 删除生产包干工工时
export function delOuterHours(id) {
  return request({
    url: '/production/outerHours/' + id,
    method: 'delete'
  })
}

// 导出生产包干工工时
export function exportOuterHours(query) {
  return request({
    url: '/production/outerHours/export',
    method: 'get',
    params: query
  })
}
