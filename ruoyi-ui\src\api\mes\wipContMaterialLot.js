import request from '@/utils/request'

// 查询生产批用料记录(料批)列表
export function listWipContMaterialLot(query) {
  return request({
    url: '/mes/wipContMaterialLot/list',
    method: 'get',
    params: query
  })
}

// 查询生产批用料记录(料批)详细
export function getWipContMaterialLot(lotserial) {
  return request({
    url: '/mes/wipContMaterialLot/' + lotserial,
    method: 'get'
  })
}

// 新增生产批用料记录(料批)
export function addWipContMaterialLot(data) {
  return request({
    url: '/mes/wipContMaterialLot',
    method: 'post',
    data: data
  })
}

// 修改生产批用料记录(料批)
export function updateWipContMaterialLot(data) {
  return request({
    url: '/mes/wipContMaterialLot',
    method: 'put',
    data: data
  })
}

// 删除生产批用料记录(料批)
export function delWipContMaterialLot(lotserial) {
  return request({
    url: '/mes/wipContMaterialLot/' + lotserial,
    method: 'delete'
  })
}

// 导出生产批用料记录(料批)
export function exportWipContMaterialLot(query) {
  return request({
    url: '/mes/wipContMaterialLot/export',
    method: 'get',
    params: query
  })
}

export function allWipContMaterialLot(query) {
  return request({
    url: '/mes/wipContMaterialLot/all',
    method: 'get',
    params: query
  })
}

export function allGroupByEquipmentNo(query) {
  return request({
    url: '/mes/wipContMaterialLot/allGroupByEquipmentNo',
    method: 'get',
    params: query
  })
}
