import request from '@/utils/request'

// 查询产线亏损-天纬度列表
export function listProductionDay(query) {
  return request({
    url: '/production/productionDay/list',
    method: 'get',
    params: query
  })
}

// 查询产线亏损-天纬度详细
export function getProductionDay(id) {
  return request({
    url: '/production/productionDay/' + id,
    method: 'get'
  })
}

// 新增产线亏损-天纬度
export function addProductionDay(data) {
  return request({
    url: '/production/productionDay',
    method: 'post',
    data: data
  })
}

// 修改产线亏损-天纬度
export function updateProductionDay(data) {
  return request({
    url: '/production/productionDay',
    method: 'put',
    data: data
  })
}

// 删除产线亏损-天纬度
export function delProductionDay(id) {
  return request({
    url: '/production/productionDay/' + id,
    method: 'delete'
  })
}

// 导出产线亏损-天纬度
export function exportProductionDay(query) {
  return request({
    url: '/production/productionDay/export',
    method: 'get',
    params: query
  })
}