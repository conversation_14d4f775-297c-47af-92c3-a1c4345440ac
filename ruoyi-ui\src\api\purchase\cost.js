import request from '@/utils/request'

// 查询物料基础价格列表
export function listCost(query) {
  return request({
    url: '/purchase/cost/list',
    method: 'get',
    params: query
  })
}

// 查询物料基础价格详细
export function getCost(id) {
  return request({
    url: '/purchase/cost/' + id,
    method: 'get'
  })
}

// 新增物料基础价格
export function addCost(data) {
  return request({
    url: '/purchase/cost',
    method: 'post',
    data: data
  })
}

// 修改物料基础价格
export function updateCost(data) {
  return request({
    url: '/purchase/cost',
    method: 'put',
    data: data
  })
}

// 删除物料基础价格
export function delCost(id) {
  return request({
    url: '/purchase/cost/' + id,
    method: 'delete'
  })
}

// 导出物料基础价格
export function exportCost(query) {
  return request({
    url: '/purchase/cost/export',
    method: 'get',
    params: query
  })
}