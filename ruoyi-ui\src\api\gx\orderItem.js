import request from '@/utils/request'

// 查询功效订单明细列表
export function listOrderItem(query) {
  return request({
    url: '/gx/orderItem/list',
    method: 'get',
    params: query
  })
}

export function unAssociationListOrderItem(query) {
  return request({
    url: '/gx/orderItem/unAssociationList',
    method: 'get',
    params: query
  })
}

export function todoConfirmListOrderItem(query) {
  return request({
    url: '/gx/orderItem/todoConfirmList',
    method: 'get',
    params: query,
  })
}

// 查询功效订单明细详细
export function getOrderItem(id) {
  return request({
    url: '/gx/orderItem/' + id,
    method: 'get'
  })
}

// 新增功效订单明细
export function addOrderItem(data) {
  return request({
    url: '/gx/orderItem',
    method: 'post',
    data: data
  })
}

// 修改功效订单明细
export function updateOrderItem(data) {
  return request({
    url: '/gx/orderItem',
    method: 'put',
    data: data
  })
}

// 删除功效订单明细
export function delOrderItem(id) {
  return request({
    url: '/gx/orderItem/' + id,
    method: 'delete'
  })
}

// 导出功效订单明细
export function exportOrderItem(id) {
  return request({
    url: '/gx/orderItem/exportOrderItem/' + id,
    method: 'get',
  })
}

export function allOrderItem(query) {
  return request({
    url: '/gx/orderItem/all',
    method: 'get',
    params: query
  })
}

export function allFullOrderItem(query) {
  return request({
    url: '/gx/orderItem/allFull',
    method: 'get',
    params: query
  })
}

export function editOrderItemCaseId(data) {
  return request({
    url: '/gx/orderItem/editCaseId',
    method: 'put',
    data: data
  })
}

export function removeOrderItemCaseId(id) {
  return request({
    url: '/gx/orderItem/removeCaseId/' + id,
    method: 'put',
  })
}

export function finishOrderItemCaseId(id) {
  return request({
    url: '/gx/orderItem/finish/' + id,
    method: 'put',
  })
}

export function confirmOrderItemCaseId(id) {
  return request({
    url: '/gx/orderItem/confirm/' + id,
    method: 'put',
  })
}

export function pairedTTest(data) {
  return request({
    url: '/gx/orderItem/pairedTTest',
    method: 'post',
    data,
  })
}

export function computePValue(data) {
  return request({
    url: '/gx/orderItem/computePValue',
    method: 'post',
    data,
  })
}
