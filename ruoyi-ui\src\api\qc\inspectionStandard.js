import request from '@/utils/request'

// 查询包材物料检验标准列表
export function listInspectionStandard(query) {
  return request({
    url: '/qc/inspectionStandard/list',
    method: 'get',
    params: query
  })
}

// 查询包材物料检验标准详细
export function getInspectionStandard(id) {
  return request({
    url: '/qc/inspectionStandard/' + id,
    method: 'get'
  })
}

// 新增包材物料检验标准
export function addInspectionStandard(data) {
  return request({
    url: '/qc/inspectionStandard',
    method: 'post',
    data: data
  })
}

// 修改包材物料检验标准
export function updateInspectionStandard(data) {
  return request({
    url: '/qc/inspectionStandard',
    method: 'put',
    data: data
  })
}

// 删除包材物料检验标准
export function delInspectionStandard(id) {
  return request({
    url: '/qc/inspectionStandard/' + id,
    method: 'delete'
  })
}

// 导出包材物料检验标准
export function exportInspectionStandard(query) {
  return request({
    url: '/qc/inspectionStandard/export',
    method: 'get',
    params: query
  })
}

// 导出包材物料检验标准
export function importInspectionStandardRecord(query) {
  return request({
    url: '/qc/inspectionStandard/import',
    method: 'get',
    params: query
  })
}

export function allInspectionStandard(query) {
  return request({
    url: '/qc/inspectionStandard/all',
    method: 'get',
    params: query
  })
}

export function getInspectionStandardByCode(code) {
  return request({
    url: '/qc/inspectionStandard/getByCode/' + code,
    method: 'get'
  })
}
