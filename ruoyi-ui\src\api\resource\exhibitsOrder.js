import request from '@/utils/request'

// 查询展品订单列表
export function listExhibitsOrder(query) {
  return request({
    url: '/resource/exhibitsOrder/list',
    method: 'get',
    params: query
  })
}

// 查询展品订单详细
export function getExhibitsOrder(id) {
  return request({
    url: '/resource/exhibitsOrder/' + id,
    method: 'get'
  })
}

// 新增展品订单
export function addExhibitsOrder(data) {
  return request({
    url: '/resource/exhibitsOrder',
    method: 'post',
    data: data
  })
}

// 修改展品订单
export function updateExhibitsOrder(data) {
  return request({
    url: '/resource/exhibitsOrder',
    method: 'put',
    data: data
  })
}

// 删除展品订单
export function delExhibitsOrder(id) {
  return request({
    url: '/resource/exhibitsOrder/' + id,
    method: 'delete'
  })
}

// 导出展品订单
export function exportExhibitsOrder(query) {
  return request({
    url: '/resource/exhibitsOrder/export',
    method: 'get',
    params: query
  })
}

export function shipmentExhibitsOrder(data) {
  return request({
    url: '/resource/exhibitsOrder/shipment',
    method: 'put',
    data: data
  })
}

export function allExhibitsOrder(query) {
  return request({
    url: '/resource/exhibitsOrder/all',
    method: 'get',
    params: query
  })
}
