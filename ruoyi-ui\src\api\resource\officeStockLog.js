import request from '@/utils/request'

// 查询办公用品库存记录列表
export function listOfficeStockLog(query) {
  return request({
    url: '/resource/officeStockLog/list',
    method: 'get',
    params: query
  })
}

// 查询办公用品库存记录详细
export function getOfficeStockLog(id) {
  return request({
    url: '/resource/officeStockLog/' + id,
    method: 'get'
  })
}

// 新增办公用品库存记录
export function addOfficeStockLog(data) {
  return request({
    url: '/resource/officeStockLog',
    method: 'post',
    data: data
  })
}

// 修改办公用品库存记录
export function updateOfficeStockLog(data) {
  return request({
    url: '/resource/officeStockLog',
    method: 'put',
    data: data
  })
}

// 删除办公用品库存记录
export function delOfficeStockLog(id) {
  return request({
    url: '/resource/officeStockLog/' + id,
    method: 'delete'
  })
}

// 导出办公用品库存记录
export function exportOfficeStockLog(query) {
  return request({
    url: '/resource/officeStockLog/export',
    method: 'get',
    params: query
  })
}

export function exportOfficeCkLog(params) {
  return request({
    url: '/resource/officeStockLog/exportCkLog',
    method: 'get',
    params,
  })
}
