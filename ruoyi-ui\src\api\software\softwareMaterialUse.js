import request from '@/utils/request'

// 查询原料使用记录列表
export function listSoftwareMaterialUse(query) {
  return request({
    url: '/software/softwareMaterialUse/list',
    method: 'get',
    params: query
  })
}

// 查询原料使用记录详细
export function getSoftwareMaterialUse(id) {
  return request({
    url: '/software/softwareMaterialUse/' + id,
    method: 'get'
  })
}

// 新增原料使用记录
export function addSoftwareMaterialUse(data) {
  return request({
    url: '/software/softwareMaterialUse',
    method: 'post',
    data: data
  })
}

// 修改原料使用记录
export function updateSoftwareMaterialUse(data) {
  return request({
    url: '/software/softwareMaterialUse',
    method: 'put',
    data: data
  })
}

// 删除原料使用记录
export function delSoftwareMaterialUse(id) {
  return request({
    url: '/software/softwareMaterialUse/' + id,
    method: 'delete'
  })
}

// 导出原料使用记录
export function exportSoftwareMaterialUse(query) {
  return request({
    url: '/software/softwareMaterialUse/export',
    method: 'get',
    params: query
  })
}