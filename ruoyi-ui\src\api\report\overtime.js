import request from '@/utils/request'

export function listReportOvertime(query) {
  return request({
    url: '/report/overtime/list',
    method: 'get',
    params: query
  })
}

export function listReportOvertimeUser(query) {
  return request({
    url: '/report/overtime/userList',
    method: 'get',
    params: query
  })
}
export function auditReportOvertime(query) {
  return request({
    url: '/report/overtime/audit',
    method: 'get',
    params: query
  })
}
export function auditUserReportOvertime(query) {
  return request({
    url: '/report/overtime/auditUser',
    method: 'get',
    params: query
  })
}
export function overtimeUserReportOvertime(query) {
  return request({
    url: '/report/overtime/overtimeUser',
    method: 'get',
    params: query
  })
}
