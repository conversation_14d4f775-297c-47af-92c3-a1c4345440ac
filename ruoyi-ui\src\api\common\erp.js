import request from '@/utils/request'

export function getErpScheduleList(query) {
  return request({
    url: '/erp/scheduleList',
    method: 'get',
    params: query,
  })
}

export function getBatchOptions(query) {
  return request({
    url: '/erp/batchOptions',
    method: 'get',
    params: query,
  })
}

export function getProductByMd003(md003) {
  return request({
    url: '/erp/' + md003,
    method: 'get',
  })
}

export function getProductIncludesNumsByMd003(md003) {
  return request({
    url: '/erp/getInfoIncludesNums/' + md003,
    method: 'get',
  })
}

export function getScheduleListByWorkOrderNos(query) {
  return request({
    url: '/erp/scheduleListByWorkOrderNos',
    method: 'get',
    params: query,
  })
}

export function getBomByErpCode(erpCode) {
  return request({
    url: '/erp/getBomByErpCode/' + erpCode,
    method: 'get',
  })
}

export function getBomIncludeUnAuditByCode(erpCode) {
  return request({
    url: '/erp/getBomIncludeUnAuditByCode/' + erpCode,
    method: 'get',
  })
}
export function getBomIncludesNumsByErpCode(erpCode) {
  return request({
    url: '/erp/getBomIncludesNumsByErpCode/' + erpCode,
    method: 'get',
  })
}

export function getErpInspectionRecord(query) {
  return request({
    url: '/erp/inspectionRecord',
    method: 'get',
    params: query,
  })
}

export function getErpMaterialBomData(query) {
  return request({
    url: '/erp/materialBomData',
    method: 'get',
    params: query,
  })
}

export function getScheduleMaterial(query) {
  return request({
    url: '/erp/getScheduleMaterial',
    method: 'get',
    params: query,
  })
}
