import request from '@/utils/request'

// 查询生产设备列表
export function listEquipment(query) {
  return request({
    url: '/production/equipment/list',
    method: 'get',
    params: query
  })
}

// 查询生产设备详细
export function getEquipment(id) {
  return request({
    url: '/production/equipment/' + id,
    method: 'get'
  })
}

// 新增生产设备
export function addEquipment(data) {
  return request({
    url: '/production/equipment',
    method: 'post',
    data: data
  })
}

// 修改生产设备
export function updateEquipment(data) {
  return request({
    url: '/production/equipment',
    method: 'put',
    data: data
  })
}

// 删除生产设备
export function delEquipment(id) {
  return request({
    url: '/production/equipment/' + id,
    method: 'delete'
  })
}

// 导出生产设备
export function exportEquipment(query) {
  return request({
    url: '/production/equipment/export',
    method: 'get',
    params: query
  })
}

export function allEquipment(query) {
  return request({
    url: '/production/equipment/all',
    method: 'get',
    params: query
  })
}

export function importEquipment() {
  return request({
    url: '/production/equipment/importEquipment',
    method: 'get'
  })
}
