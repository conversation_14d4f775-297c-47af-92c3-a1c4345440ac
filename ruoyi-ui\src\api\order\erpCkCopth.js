import request from '@/utils/request'

// 查询ERP出库信息列表
export function listErpCkCopth(query) {
  return request({
    url: '/order/erpCkCopth/list',
    method: 'get',
    params: query
  })
}

// 查询ERP出库信息详细
export function getErpCkCopth(id) {
  return request({
    url: '/order/erpCkCopth/' + id,
    method: 'get'
  })
}

// 新增ERP出库信息
export function addErpCkCopth(data) {
  return request({
    url: '/order/erpCkCopth',
    method: 'post',
    data: data
  })
}

// 修改ERP出库信息
export function updateErpCkCopth(data) {
  return request({
    url: '/order/erpCkCopth',
    method: 'put',
    data: data
  })
}

// 修改ERP出库信息
export function updateErpCkCopthRevoke(data) {
  return request({
    url: '/order/erpCkCopth/updateErpCkCopthRevoke',
    method: 'put',
    data: data
  })
}
// 拆分ERP出库数量信息
export function updateErpCkCopthSplit(data) {
  return request({
    url: '/order/erpCkCopth/updateErpCkCopthSplit',
    method: 'put',
    data: data
  })
}

// 导出ERP出库信息
export function exportErpCkCopth(query) {
  return request({
    url: '/order/erpCkCopth/export',
    method: 'get',
    params: query
  })
}

export function allCkLogItem(query) {
  return request({
    url: '/order/erpCkCopth/allCkLogItem',
    method: 'get',
    params: query
  })
}

export function listRemindErpCkCopth(query) {
  return request({
    url: '/order/erpCkCopth/remindErpCkCopthList',
    method: 'get',
    params: query
  })
}

// 查询ERP出库信息列表
export function allList(query) {
  return request({
    url: '/order/erpCkCopth/all',
    method: 'get',
    params: query
  })
}
