import request from '@/utils/request'

// 查询使用资源历史明细列表
export function listWipcontResource(query) {
  return request({
    url: '/mes/wipcontResource/list',
    method: 'get',
    params: query
  })
}

// 查询使用资源历史明细详细
export function getWipcontResource(loggroupserial) {
  return request({
    url: '/mes/wipcontResource/' + loggroupserial,
    method: 'get'
  })
}

// 新增使用资源历史明细
export function addWipcontResource(data) {
  return request({
    url: '/mes/wipcontResource',
    method: 'post',
    data: data
  })
}

// 修改使用资源历史明细
export function updateWipcontResource(data) {
  return request({
    url: '/mes/wipcontResource',
    method: 'put',
    data: data
  })
}

// 删除使用资源历史明细
export function delWipcontResource(loggroupserial) {
  return request({
    url: '/mes/wipcontResource/' + loggroupserial,
    method: 'delete'
  })
}

// 导出使用资源历史明细
export function exportWipcontResource(query) {
  return request({
    url: '/mes/wipcontResource/export',
    method: 'get',
    params: query
  })
}

export function allWipContResource(query) {
  return request({
    url: '/mes/wipcontResource/all',
    method: 'get',
    params: query
  })
}
