import request from '@/utils/request'

// 查询用户数据列表
export function listMesUser(query) {
  return request({
    url: '/mes/mesUser/list',
    method: 'get',
    params: query
  })
}

// 查询用户数据详细
export function getMesUser(userno) {
  return request({
    url: '/mes/mesUser/' + userno,
    method: 'get'
  })
}

// 新增用户数据
export function addMesUser(data) {
  return request({
    url: '/mes/mesUser',
    method: 'post',
    data: data
  })
}

// 修改用户数据
export function updateMesUser(data) {
  return request({
    url: '/mes/mesUser',
    method: 'put',
    data: data
  })
}

// 删除用户数据
export function delMesUser(userno) {
  return request({
    url: '/mes/mesUser/' + userno,
    method: 'delete'
  })
}

// 导出用户数据
export function exportMesUser(query) {
  return request({
    url: '/mes/mesUser/export',
    method: 'get',
    params: query
  })
}
