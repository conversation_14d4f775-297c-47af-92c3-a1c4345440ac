import request from '@/utils/request'

// 查询订单每日盈亏列表
export function listDailyProfitAndLoss(query) {
  return request({
    url: '/order/dailyProfitAndLoss/list',
    method: 'get',
    params: query
  })
}

// 查询工单盈亏列表
export function listWorkOrderProfitAndLosslist(query) {
  return request({
    url: '/order/dailyProfitAndLoss/workOrderProfitAndLosslist',
    method: 'get',
    params: query
  })
}

// 查询产线盈亏列表
export function listProductionLineProfitAndLoss(query) {
  return request({
    url: '/order/dailyProfitAndLoss/productionLineProfitAndLosslist',
    method: 'get',
    params: query
  })
}

// 查询产线盈亏列表
export function queryProductionLineProfitAndLossCostlist(query) {
  return request({
    url: '/order/dailyProfitAndLoss/queryProductionLineProfitAndLossCostlist',
    method: 'get',
    params: query
  })
}

// 查询订单每日盈亏详细
export function getDailyProfitAndLoss(id) {
  return request({
    url: '/order/dailyProfitAndLoss/' + id,
    method: 'get'
  })
}

// 新增订单每日盈亏
export function addDailyProfitAndLoss(data) {
  return request({
    url: '/order/dailyProfitAndLoss',
    method: 'post',
    data: data
  })
}

// 修改订单每日盈亏
export function updateDailyProfitAndLoss(data) {
  return request({
    url: '/order/dailyProfitAndLoss',
    method: 'put',
    data: data
  })
}

// 删除订单每日盈亏
export function delDailyProfitAndLoss(id) {
  return request({
    url: '/order/dailyProfitAndLoss/' + id,
    method: 'delete'
  })
}

// 导出订单每日盈亏
export function exportDailyProfitAndLoss(query) {
  return request({
    url: '/order/dailyProfitAndLoss/export',
    method: 'get',
    params: query
  })
}



export function refreshDailyProfitAndLoss(query) {
  return request({
    url: '/order/dailyProfitAndLoss/refresh',
    method: 'get',
    params: query
  })
}

export function refreshLineDailyProfitAndLoss(query) {
  return request({
    url: '/order/dailyProfitAndLoss/refreshLine',
    method: 'get',
    params: query
  })
}


// 导出产线每日盈亏
export function exportProductionLine(query) {
  return request({
    url: '/order/dailyProfitAndLoss/exportProductionLine',
    method: 'get',
    params: query
  })
}

// 导出产线每日盈亏
export function exportProductionCostLine(query) {
  return request({
    url: '/order/dailyProfitAndLoss/exportProductionCostLine',
    method: 'get',
    params: query
  })
}

// 刷新人员工资成本
export function handleProductionLineRefreshData(query) {
  return request({
    url: '/order/dailyProfitAndLoss/handleRefreshData',
    method: 'get',
    params: query
  })
}
