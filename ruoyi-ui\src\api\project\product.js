import request from '@/utils/request'

// 查询项目产品列表
export function listProduct(query) {
  return request({
    url: '/project/product/list',
    method: 'get',
    params: query
  })
}

// 查询项目产品详细
export function getProduct(id) {
  return request({
    url: '/project/product/' + id,
    method: 'get'
  })
}

// 新增项目产品
export function addProduct(data) {
  return request({
    url: '/project/product',
    method: 'post',
    data: data
  })
}

// 修改项目产品
export function updateProduct(data) {
  return request({
    url: '/project/product',
    method: 'put',
    data: data
  })
}

// 导出项目产品
export function exportProduct(query) {
  return request({
    url: '/project/product/export',
    method: 'get',
    params: query
  })
}

export function delProduct(ids) {
  return request({
    url: '/project/product/' + ids,
    method: 'delete'
  })
}

export function asyncProjectProduct() {
  return request({
    url: '/project/product/asyncProjectProduct',
    method: 'put',
  })
}

export function asyncProjectProductDkNums(query) {
  return request({
    url: '/project/product/asyncProjectProductDkNums',
    method: 'get',
    params: query
  })
}

export function asyncProjectProductItemId(query) {
  return request({
    url: '/project/product/asyncProjectProductItemId',
    method: 'get',
    params: query
  })
}
