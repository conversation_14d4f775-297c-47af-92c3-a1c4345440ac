import request from '@/utils/request'

// 查询物料预处理日计划列表
export function listMaterialDay(query) {
  return request({
    url: '/production/materialDay/list',
    method: 'get',
    params: query
  })
}

// 查询物料预处理日计划详细
export function getMaterialDay(id) {
  return request({
    url: '/production/materialDay/' + id,
    method: 'get'
  })
}

export function materialDaySubmitAudit(data) {
  return request({
    url: '/production/materialDay/submitAudit',
    method: 'put',
    data,
  })
}

export function materialDayCancelAudit(data) {
  return request({
    url: '/production/materialDay/cancelAudit',
    method: 'put',
    data,
  })
}

export function materialDaySubmitChangeAudit(data) {
  return request({
    url: '/production/materialDay/submitChangeAudit',
    method: 'put',
    data,
  })
}

export function materialDayCancelChangeAudit(data) {
  return request({
    url: '/production/materialDay/cancelChangeAudit',
    method: 'put',
    data,
  })
}

export function finishMaterialDay(data) {
  return request({
    url: '/production/materialDay/finish',
    method: 'put',
    data,
  })
}

export function updateMaterialDay(data) {
  return request({
    url: '/production/materialDay',
    method: 'put',
    data,
  })
}

export function asyncMaterialDayHours(data) {
  return request({
    url: '/production/materialDay/asyncHours',
    method: 'put',
    data,
  })
}
