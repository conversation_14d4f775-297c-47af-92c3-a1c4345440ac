{"name": "@types/mousetrap", "version": "1.6.15", "description": "TypeScript definitions for mousetrap", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mousetrap", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "qcz", "url": "https://github.com/qcz"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/alanhchoi"}, {"name": "<PERSON><PERSON>", "githubUsername": "nic<PERSON>er", "url": "https://github.com/nicbarker"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mousetrap"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "c181de473322f4914ce1e00175050eeb1bc1c795d246b263fd03abc1bf94b165", "typeScriptVersion": "4.5"}