import request from '@/utils/request'

// 查询展厅订单商品分配记录列表
export function listExhibitsOrderGoodsAssignLog(query) {
  return request({
    url: '/resource/exhibitsOrderGoodsAssignLog/list',
    method: 'get',
    params: query
  })
}

// 查询展厅订单商品分配记录详细
export function getExhibitsOrderGoodsAssignLog(id) {
  return request({
    url: '/resource/exhibitsOrderGoodsAssignLog/' + id,
    method: 'get'
  })
}

// 导出展厅订单商品分配记录
export function exportExhibitsOrderGoodsAssignLog(query) {
  return request({
    url: '/resource/exhibitsOrderGoodsAssignLog/export',
    method: 'get',
    params: query
  })
}

export function allExhibitsOrderGoodsAssignLog(query) {
  return request({
    url: '/resource/exhibitsOrderGoodsAssignLog/all',
    method: 'get',
    params: query
  })
}
