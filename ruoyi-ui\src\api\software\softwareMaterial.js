import request from '@/utils/request'

export function listSoftwareMaterial(query) {
  return request({
    url: '/software/softwareMaterial/list',
    method: 'get',
    params: query
  })
}

export function getSoftwareMaterial(id) {
  return request({
    url: '/software/softwareMaterial/' + id,
    method: 'get'
  })
}

export function getSoftwareMaterialDetail(id) {
  return request({
    url: '/software/softwareMaterial/detail/' + id,
    method: 'get'
  })
}

//查询原料可查看tab数据
export function queryLookMaterialTabs(query) {
  return request({
    url: '/software/softwareMaterial/queryLookMaterialTabs',
    method: 'get',
    params: query
  })
}


//查询原料可关联数据
export function queryMaterialRelationDatas(query) {
  return request({
    url: '/software/softwareMaterial/queryMaterialRelationDatas',
    method: 'get',
    params: query
  })
}

//查询原料可查看tab数据
export function queryLookMaterialCodeTips(query) {
  return request({
    url: '/software/softwareMaterial/queryLookMaterialCodeTips',
    method: 'get',
    params: query
  })
}

export function queryMaterialSupplierData(id) {
  return request({
    url: '/software/softwareMaterial/materialSupplier/' + id,
    method: 'get'
  })
}

export function queryMaterialUserSpecData(id) {
  return request({
    url: '/software/softwareMaterial/getUserSpec/' + id,
    method: 'get'
  })
}

export function addSoftwareMaterial(data) {
  return request({
    url: '/software/softwareMaterial',
    method: 'post',
    data: data
  })
}

export function addSoftwareMaterialLhzb(data) {
  return request({
    url: '/software/softwareMaterial/addLhzb',
    method: 'post',
    data: data
  })
}

export function addSoftwareMaterialFragranceDatas(data) {
  return request({
    url: '/software/softwareMaterial/saveMaterialFraganceData',
    method: 'post',
    data: data
  })
}

export function updateSoftwareMaterial(data) {
  return request({
    url: '/software/softwareMaterial',
    method: 'put',
    data: data
  })
}

export function delSoftwareMaterial(id) {
  return request({
    url: '/software/softwareMaterial/' + id,
    method: 'delete'
  })
}

export function exportSoftwareMaterial(query) {
  return request({
    url: '/software/softwareMaterial/export',
    method: 'get',
    params: query
  })
}

export function exportSoftwareMaterialExcel(query) {
  return request({
    url: '/software/softwareMaterial/exportExcel',
    method: 'get',
    params: query
  })
}

// 查询原料组分数据
export function listSoftwareRawMaterial(query) {
  return request({
    url: '/software/softwareMaterial/queryRawMaterialData',
    method: 'get',
    params: query
  })
}

export function getRawMaterialInfo(id) {
  return request({
    url: '/software/softwareMaterial/rawMaterial/' + id,
    method: 'get'
  })
}

// 查询原料供应商-理化指标 安全信息 功效 详细
export function queryMaterialProducerData(query) {
  return request({
    url: '/software/softwareMaterial/queryMaterialProducerData',
    method: 'get',
    params: query
  })
}

// 查询原料ifra香精成分清单数据
export function queryFragranceDataList(query) {
  return request({
    url: '/software/softwareMaterial/queryFragranceDataList',
    method: 'get',
    params: query
  })
}

// 查询原料ifra香精成分数据
export function queryMaterialFragranceDataList(query) {
  return request({
    url: '/software/softwareMaterial/queryMaterialFragranceDataList',
    method: 'get',
    params: query
  })
}

// 查询原料原料报送码变更记录
export function queryMaterialYlbsmDataList(query) {
  return request({
    url: '/software/softwareMaterial/queryMaterialYlbsmDataList',
    method: 'get',
    params: query
  })
}

// 查询原料ifra香精成分详情数据
export function queryFragranceDataDetail(query) {
  return request({
    url: '/software/softwareMaterial/queryFragranceDataDetail',
    method: 'get',
    params: query
  })
}

// 查询原料详细-根据原料代码
export function getRawMaterialInfoByCode(query) {
  return request({
    url: '/software/softwareMaterial/getRawMaterialInfoByCode',
    method: 'get',
    params:query
  })
}

// 查询原料详细-根据原料代码
export function getFormulaInfoByCode(query) {
  return request({
    url: '/software/softwareMaterial/getFormulaInfoByCode',
    method: 'get',
    params:query
  })
}

//替换原料
export function replaceSoftwareMaterial(data) {
  return request({
    url: '/software/softwareMaterial/replace',
    method: 'post',
    data: data
  })
}

export function listPriceMaterial(query) {
  return request({
    url: '/software/softwareMaterial/priceList',
    method: 'get',
    params: query
  })
}

export function listRecycleMaterial(query) {
  return request({
    url: '/software/softwareMaterial/recycleList',
    method: 'get',
    params: query
  })
}

export function recoverMaterial(data) {
  return request({
    url: '/software/softwareMaterial/recover',
    method: 'put',
    data,
  })
}

export function similarityListSoftwareMaterial(data) {
  return request({
    url: '/software/softwareMaterial/similarityList',
    method: 'post',
    data,
  })
}


// 查询原料原料报送码变更记录
export function querySimilarRawMaterialDataList(query) {
  return request({
    url: '/software/softwareMaterial/querySimilarRawMaterialDataList',
    method: 'get',
    params: query
  })
}

// 查询原料原料报送码变更记录
export function querySimilarRawMaterialData(query) {
  return request({
    url: '/software/softwareMaterial/querySimilarRawMaterialData',
    method: 'get',
    params: query
  })
}
