import request from '@/utils/request'

export function allSocialSecurity(query) {
  return request({
    url: '/hr/socialSecurity/all',
    method: 'get',
    params: query
  })
}
// 查询社保方案列表
export function listSocialSecurity(query) {
  return request({
    url: '/hr/socialSecurity/list',
    method: 'get',
    params: query
  })
}

// 查询社保方案详细
export function getSocialSecurity(id) {
  return request({
    url: '/hr/socialSecurity/' + id,
    method: 'get'
  })
}

// 新增社保方案
export function addSocialSecurity(data) {
  return request({
    url: '/hr/socialSecurity',
    method: 'post',
    data: data
  })
}

// 修改社保方案
export function updateSocialSecurity(data) {
  return request({
    url: '/hr/socialSecurity',
    method: 'put',
    data: data
  })
}

// 删除社保方案
export function delSocialSecurity(id) {
  return request({
    url: '/hr/socialSecurity/' + id,
    method: 'delete'
  })
}

// 导出社保方案
export function exportSocialSecurity(query) {
  return request({
    url: '/hr/socialSecurity/export',
    method: 'get',
    params: query
  })
}
