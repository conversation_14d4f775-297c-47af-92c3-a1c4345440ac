{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\engineerSampleOrder\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\engineerSampleOrder\\index.vue", "mtime": 1753954184863}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\babel.config.js", "mtime": 1743382537964}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9zZWFuL3dvcmtzcGFjZS9lbm93X3Byb2plY3QvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfcmVnZW5lcmF0b3JSdW50aW1lMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQzovc2Vhbi93b3Jrc3BhY2UvZW5vd19wcm9qZWN0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL3JlZ2VuZXJhdG9yUnVudGltZS5qcyIpKTsKdmFyIF9hc3luY1RvR2VuZXJhdG9yMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQzovc2Vhbi93b3Jrc3BhY2UvZW5vd19wcm9qZWN0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2FzeW5jVG9HZW5lcmF0b3IuanMiKSk7CnZhciBfb2JqZWN0U3ByZWFkMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQzovc2Vhbi93b3Jrc3BhY2UvZW5vd19wcm9qZWN0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL29iamVjdFNwcmVhZDIuanMiKSk7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZpbHRlci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmpvaW4uanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmZpbHRlci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IubWFwLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaW5jbHVkZXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5wYWQtc3RhcnQuanMiKTsKdmFyIF9lbmdpbmVlclNhbXBsZU9yZGVyID0gcmVxdWlyZSgiQC9hcGkvc29mdHdhcmUvZW5naW5lZXJTYW1wbGVPcmRlciIpOwp2YXIgX3Byb2plY3RJdGVtT3JkZXIgPSByZXF1aXJlKCJAL2FwaS9wcm9qZWN0L3Byb2plY3RJdGVtT3JkZXIiKTsKdmFyIF92dWVUcmVlc2VsZWN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdCIpKTsKcmVxdWlyZSgiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QvZGlzdC92dWUtdHJlZXNlbGVjdC5jc3MiKTsKdmFyIF9jdXN0b21lciA9IHJlcXVpcmUoIkAvYXBpL2N1c3RvbWVyL2N1c3RvbWVyIik7CnZhciBfdmFsaWRhdGUgPSByZXF1aXJlKCJAL3V0aWxzL3ZhbGlkYXRlIik7CnZhciBfZXhlY3V0aW9uQWRkT3JFZGl0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL2NvbXBvbmVudHMvUHJvamVjdC9jb21wb25lbnRzL2V4ZWN1dGlvbkFkZE9yRWRpdC52dWUiKSk7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAiRW5naW5lZXJTYW1wbGVPcmRlciIsCiAgY29tcG9uZW50czogewogICAgVHJlZXNlbGVjdDogX3Z1ZVRyZWVzZWxlY3QuZGVmYXVsdCwKICAgIGV4ZWN1dGlvbkFkZE9yRWRpdDogX2V4ZWN1dGlvbkFkZE9yRWRpdC5kZWZhdWx0CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOmAieS4reaVsOe7hAogICAgICBpZHM6IFtdLAogICAgICAvLyDpgInkuK3nmoTlrozmlbTooYzmlbDmja4KICAgICAgc2VsZWN0ZWRSb3dzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDlt6XnqIvluIjmiZPmoLfljZXlhbPogZTooajmoLzmlbDmja4KICAgICAgZW5naW5lZXJTYW1wbGVPcmRlckxpc3Q6IFtdLAogICAgICAvLyDnoJTlj5Hpg6jpg6jpl6jmoJHliJfooagKICAgICAgcmVzZWFyY2hEZXB0RGF0YXM6IFtdLAogICAgICAvLyDmiZPmoLfnsbvliKvlrZflhbgKICAgICAgZHlsYk9wdGlvbnM6IFtdLAogICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHVzZXJJZDogbnVsbCwKICAgICAgICBuaWNrTmFtZTogbnVsbCwKICAgICAgICBzYW1wbGVPcmRlckNvZGU6IG51bGwsCiAgICAgICAgY29tcGxldGlvblN0YXR1czogbnVsbCwKICAgICAgICBzY2hlZHVsZWREYXRlOiBudWxsLAogICAgICAgIHN0YXJ0RGF0ZTogbnVsbCwKICAgICAgICBhY3R1YWxTdGFydFRpbWU6IG51bGwsCiAgICAgICAgYWN0dWFsRmluaXNoVGltZTogbnVsbCwKICAgICAgICBkZXB0SWQ6IG51bGwsCiAgICAgICAgZGVwdElkczogW10sCiAgICAgICAgYXNzb2NpYXRpb25TdGF0dXM6IG51bGwsCiAgICAgICAgY3VzdG9tZXJJZDogbnVsbCwKICAgICAgICBwcm9kdWN0TmFtZTogbnVsbCwKICAgICAgICBjb25maXJtQ29kZTogbnVsbCwKICAgICAgICBpc092ZXJkdWU6IG51bGwsCiAgICAgICAgLy8g5aKe6YC+5pyf5Lu75Yqh6L+H5ruk5Y+C5pWwCiAgICAgICAgbGFib3JhdG9yeTogbnVsbCAvLyDlrp7pqozlrqTnrZvpgInlj4LmlbAKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICB1c2VySWQ6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLlt6XnqIvluIhJROS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBzYW1wbGVPcmRlckNvZGU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLmiZPmoLfljZXnvJblj7fkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgY29tcGxldGlvblN0YXR1czogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuWujOaIkOaDheWGteS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlIgogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIHN0YXR1c09wdGlvbnM6IFtdLAogICAgICBzZXJ2aWNlTW9kZU9wdGlvbnM6IFtdLAogICAgICBkYXRlUmFuZ2U6IFtdLAogICAgICAvLyDmlrDlop7nmoTml6XmnJ/ojIPlm7TnrZvpgIkKICAgICAgc2NoZWR1bGVkRGF0ZVJhbmdlOiBbXSwKICAgICAgc3RhcnREYXRlUmFuZ2U6IFtdLAogICAgICBhY3R1YWxTdGFydFRpbWVSYW5nZTogW10sCiAgICAgIGFjdHVhbEZpbmlzaFRpbWVSYW5nZTogW10sCiAgICAgIC8vIOaXpeacn+mAieaLqeWZqOmFjee9rgogICAgICBkYXRhUGlja2VyT3B0aW9uczogewogICAgICAgIHNob3J0Y3V0czogW3sKICAgICAgICAgIHRleHQ6ICfku4rlpKknLAogICAgICAgICAgb25DbGljazogZnVuY3Rpb24gb25DbGljayhwaWNrZXIpIHsKICAgICAgICAgICAgdmFyIHRvZGF5ID0gbmV3IERhdGUoKTsKICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgW3RvZGF5LCB0b2RheV0pOwogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIHRleHQ6ICfmmKjlpKknLAogICAgICAgICAgb25DbGljazogZnVuY3Rpb24gb25DbGljayhwaWNrZXIpIHsKICAgICAgICAgICAgdmFyIHllc3RlcmRheSA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgIHllc3RlcmRheS5zZXRUaW1lKHllc3RlcmRheS5nZXRUaW1lKCkgLSAzNjAwICogMTAwMCAqIDI0KTsKICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgW3llc3RlcmRheSwgeWVzdGVyZGF5XSk7CiAgICAgICAgICB9CiAgICAgICAgfSwgewogICAgICAgICAgdGV4dDogJ+acgOi/keS4gOWRqCcsCiAgICAgICAgICBvbkNsaWNrOiBmdW5jdGlvbiBvbkNsaWNrKHBpY2tlcikgewogICAgICAgICAgICB2YXIgZW5kID0gbmV3IERhdGUoKTsKICAgICAgICAgICAgdmFyIHN0YXJ0ID0gbmV3IERhdGUoKTsKICAgICAgICAgICAgc3RhcnQuc2V0VGltZShzdGFydC5nZXRUaW1lKCkgLSAzNjAwICogMTAwMCAqIDI0ICogNyk7CiAgICAgICAgICAgIHBpY2tlci4kZW1pdCgncGljaycsIFtzdGFydCwgZW5kXSk7CiAgICAgICAgICB9CiAgICAgICAgfSwgewogICAgICAgICAgdGV4dDogJ+acgOi/keS4gOS4quaciCcsCiAgICAgICAgICBvbkNsaWNrOiBmdW5jdGlvbiBvbkNsaWNrKHBpY2tlcikgewogICAgICAgICAgICB2YXIgZW5kID0gbmV3IERhdGUoKTsKICAgICAgICAgICAgdmFyIHN0YXJ0ID0gbmV3IERhdGUoKTsKICAgICAgICAgICAgc3RhcnQuc2V0VGltZShzdGFydC5nZXRUaW1lKCkgLSAzNjAwICogMTAwMCAqIDI0ICogMzApOwogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbc3RhcnQsIGVuZF0pOwogICAgICAgICAgfQogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIC8vIOeKtuaAgeabtOaWsOWvueivneahhgogICAgICBzdGF0dXNPcGVuOiBmYWxzZSwKICAgICAgZGFzaGJvYXJkU3RhdHM6IHsKICAgICAgICAidG90YWwiOiAwLAogICAgICAgICJjb21wbGV0ZWQiOiAwLAogICAgICAgICJpblByb2dyZXNzIjogMCwKICAgICAgICAib3ZlcmR1ZSI6IDAKICAgICAgfSwKICAgICAgLy8g5pu05pS55bel56iL5biI5a+56K+d5qGGCiAgICAgIGNoYW5nZUVuZ2luZWVyT3BlbjogZmFsc2UsCiAgICAgIC8vIOabtOaUueW3peeoi+W4iOihqOWNlQogICAgICBjaGFuZ2VFbmdpbmVlckZvcm06IHsKICAgICAgICBpZDogbnVsbCwKICAgICAgICBzYW1wbGVPcmRlckNvZGU6IG51bGwsCiAgICAgICAgY3VycmVudEVuZ2luZWVyTmFtZTogbnVsbCwKICAgICAgICBvbGRFbmdpbmVlcklkOiBudWxsLAogICAgICAgIG5ld0VuZ2luZWVySWQ6IG51bGwsCiAgICAgICAgc2NoZWR1bGVkRGF0ZTogbnVsbCwKICAgICAgICBhZGp1c3RXb3JrU2NoZWR1bGU6IDAKICAgICAgfSwKICAgICAgLy8g5pu05pS55bel56iL5biI6KGo5Y2V5qCh6aqMCiAgICAgIGNoYW5nZUVuZ2luZWVyUnVsZXM6IHsKICAgICAgICBuZXdFbmdpbmVlcklkOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6K+36YCJ5oup5bel56iL5biIIiwKICAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UiCiAgICAgICAgfV0sCiAgICAgICAgc2NoZWR1bGVkRGF0ZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuivt+mAieaLqeaXpeacnyIsCiAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlIgogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIC8vIOW3peeoi+W4iOmAiemhuQogICAgICBlbmdpbmVlck9wdGlvbnM6IFtdLAogICAgICAvLyDmibnmrKHnrqHnkIbnm7jlhbMKICAgICAgYmF0Y2hNYW5hZ2VtZW50T3BlbjogZmFsc2UsCiAgICAgIGN1cnJlbnRCYXRjaDogbnVsbCwKICAgICAgaGlzdG9yeUJhdGNoZXM6IFtdLAogICAgICBjdXJyZW50QmF0Y2hFeHBlcmltZW50czogW10sCiAgICAgIHNlbGVjdGVkT3JkZXJGb3JCYXRjaDogbnVsbCwKICAgICAgY3VycmVudEJhdGNoUm93OiBudWxsLAogICAgICAvLyDlvZPliY3mibnmrKHnrqHnkIbnmoTooYzmlbDmja4KICAgICAgLy8g5om55qyh6K+m5oOF5a+56K+d5qGGCiAgICAgIGJhdGNoRGV0YWlsT3BlbjogZmFsc2UsCiAgICAgIGJhdGNoRGV0YWlsRGF0YTogbnVsbCwKICAgICAgLy8g5re75Yqg5a6e6aqM5a+56K+d5qGGCiAgICAgIGFkZEV4cGVyaW1lbnRPcGVuOiBmYWxzZSwKICAgICAgZXhwZXJpbWVudEZvcm06IHsKICAgICAgICBleHBlcmltZW50Q29kZTogJycsCiAgICAgICAgZXhwZXJpbWVudE5vdGU6ICcnCiAgICAgIH0sCiAgICAgIGV4cGVyaW1lbnRSdWxlczogewogICAgICAgIGV4cGVyaW1lbnRDb2RlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5a6e6aqM57yW5Y+35LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIC8vIOe7k+adn+aJueasoeWvueivneahhgogICAgICBmaW5pc2hCYXRjaE9wZW46IGZhbHNlLAogICAgICBmaW5pc2hCYXRjaEZvcm06IHsKICAgICAgICBxdWFsaXR5RXZhbHVhdGlvbjogJycsCiAgICAgICAgcmVtYXJrOiAnJwogICAgICB9LAogICAgICAvLyDmnKrmnaXml6XmnJ/pgInmi6nlmajphY3nva4KICAgICAgZnV0dXJlRGF0ZVBpY2tlck9wdGlvbnM6IHsKICAgICAgICBkaXNhYmxlZERhdGU6IGZ1bmN0aW9uIGRpc2FibGVkRGF0ZSh0aW1lKSB7CiAgICAgICAgICByZXR1cm4gdGltZS5nZXRUaW1lKCkgPCBEYXRlLm5vdygpIC0gOC42NGU3OyAvLyDnpoHnlKjku4rlpKnkuYvliY3nmoTml6XmnJ8KICAgICAgICB9CiAgICAgIH0sCiAgICAgIGVuZ2luZWVyU2VsZWN0VHlwZTogJ3NwZWNpZmllZCcsCiAgICAgIHNlYXJjaGVkRW5naW5lZXJzOiBbXSwKICAgICAgLy8g5pyq5YiG6YWN5omT5qC35Y2V5ZGK6K2mCiAgICAgIHVuYXNzaWduZWRPcmRlcnM6IFtdLAogICAgICB1bmFzc2lnbmVkUXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiA4CiAgICAgIH0sCiAgICAgIHVuYXNzaWduZWRUb3RhbDogMCwKICAgICAgLy8g5pyq5YiG6YWN6Z2i5p2/5oqY5Y+g54q25oCBCiAgICAgIGlzVW5hc3NpZ25lZFBhbmVsQ29sbGFwc2VkOiB0cnVlLAogICAgICAvLyDlrozmiJDku7vliqHlr7nor53moYYKICAgICAgZmluaXNoVGFza09wZW46IGZhbHNlLAogICAgICBmaW5pc2hUYXNrTG9hZGluZzogZmFsc2UsCiAgICAgIGZpbmlzaFRhc2tGb3JtOiB7CiAgICAgICAgbGFib3JhdG9yeUNvZGU6ICcnLAogICAgICAgIHJlbWFyazogJycKICAgICAgfSwKICAgICAgZXhwZXJpbWVudENvZGVMaXN0OiBbXSwKICAgICAgLy8g5a6e6aqM5a6k57yW56CB5YiX6KGoCiAgICAgIGZpbmlzaFRhc2tSdWxlczogewogICAgICAgIGxhYm9yYXRvcnlDb2RlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5a6e6aqM5a6k57yW56CB5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIGN1cnJlbnRGaW5pc2hSb3c6IG51bGwsCiAgICAgIC8vIOabtOaWsOWunumqjOWupOe8lueggeWvueivneahhgogICAgICB1cGRhdGVMYWJvcmF0b3J5Q29kZU9wZW46IGZhbHNlLAogICAgICB1cGRhdGVMYWJvcmF0b3J5Q29kZUxvYWRpbmc6IGZhbHNlLAogICAgICB1cGRhdGVMYWJvcmF0b3J5Q29kZUZvcm06IHsKICAgICAgICBsYWJvcmF0b3J5Q29kZTogJycsCiAgICAgICAgcmVtYXJrOiAnJwogICAgICB9LAogICAgICB1cGRhdGVMYWJvcmF0b3J5Q29kZVJ1bGVzOiB7CiAgICAgICAgbGFib3JhdG9yeUNvZGU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLlrp7pqozlrqTnvJbnoIHkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgY3VycmVudFVwZGF0ZUxhYm9yYXRvcnlDb2RlUm93OiBudWxsLAogICAgICAvLyDlr7zlh7rpgInmi6nlr7nor53moYYKICAgICAgZXhwb3J0RGlhbG9nT3BlbjogZmFsc2UsCiAgICAgIGV4cG9ydE9wdGlvbnM6IFtdLAogICAgICBleHBvcnRMb2FkaW5nOiBmYWxzZSwKICAgICAgcmVhZG9ubHk6IHRydWUsCiAgICAgIC8vIOmhueebruivpuaDheebuOWFswogICAgICBjdXJyZW50UHJvamVjdFR5cGU6IG51bGwsCiAgICAgIGNvbmZpcm1JdGVtQ29kZXM6IFtdLAogICAgICBjdXN0b21lck9wdGlvbnM6IFtdLAogICAgICBpdGVtTmFtZXM6IFtdLAogICAgICAvLyDpqbPlm57lr7nor53moYYKICAgICAgcmVqZWN0RGlhbG9nT3BlbjogZmFsc2UsCiAgICAgIHJlamVjdExvYWRpbmc6IGZhbHNlLAogICAgICByZWplY3RGb3JtOiB7CiAgICAgICAgc2FtcGxlT3JkZXJDb2RlOiAnJywKICAgICAgICByZWplY3RSZWFzb246ICcnCiAgICAgIH0sCiAgICAgIC8vIOmAvuacn+aTjeS9nOWvueivneahhgogICAgICBvdmVyZHVlT3BlcmF0aW9uT3BlbjogZmFsc2UsCiAgICAgIG92ZXJkdWVPcGVyYXRpb25Mb2FkaW5nOiBmYWxzZSwKICAgICAgb3ZlcmR1ZU9wZXJhdGlvbkZvcm06IHsKICAgICAgICBzYW1wbGVPcmRlckNvZGU6ICcnLAogICAgICAgIGV4cGVjdGVkU2FtcGxlVGltZTogJycsCiAgICAgICAgcmVhc29uRm9yTm9TYW1wbGU6ICcnLAogICAgICAgIHNvbHV0aW9uOiAnJwogICAgICB9LAogICAgICBjdXJyZW50T3ZlcmR1ZVJvdzogbnVsbCwKICAgICAgcmVqZWN0UnVsZXM6IHsKICAgICAgICByZWplY3RSZWFzb246IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLpqbPlm57nkIbnlLHkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgY3VycmVudFJlamVjdFJvdzogbnVsbAogICAgfTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICAvKiog6K6h566X5piv5ZCm5Y+v5Lul57yW6L6R5om55qyhIC0g5Y+q5pyJ54q25oCB5Li6JzEnKOi/m+ihjOS4rSnnmoTmiZPmoLfljZXmiY3lj6/ku6XnvJbovpEgKi9jYW5FZGl0QmF0Y2g6IGZ1bmN0aW9uIGNhbkVkaXRCYXRjaCgpIHsKICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0ZWRPcmRlckZvckJhdGNoICYmIHRoaXMuc2VsZWN0ZWRPcmRlckZvckJhdGNoLmNvbXBsZXRpb25TdGF0dXMgPT09IDE7CiAgICB9CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdmFyIF90aGlzID0gdGhpczsKICAgIC8vIOiuvue9rum7mOiupOaXpeacn+iMg+WbtOS4uuW9k+WkqQogICAgdmFyIHRvZGF5ID0gbmV3IERhdGUoKTsKICAgIHZhciB0b2RheVN0ciA9IHRvZGF5LmdldEZ1bGxZZWFyKCkgKyAnLScgKyBTdHJpbmcodG9kYXkuZ2V0TW9udGgoKSArIDEpLnBhZFN0YXJ0KDIsICcwJykgKyAnLScgKyBTdHJpbmcodG9kYXkuZ2V0RGF0ZSgpKS5wYWRTdGFydCgyLCAnMCcpOwogICAgdGhpcy5kYXRlUmFuZ2UgPSBbdG9kYXlTdHIsIHRvZGF5U3RyXTsKICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgKDAsIF9jdXN0b21lci5jdXN0b21lckJhc2VBbGwpKCkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgIHJldHVybiBfdGhpcy5jdXN0b21lck9wdGlvbnMgPSByZXM7CiAgICB9KTsKICAgIHRoaXMuZ2V0RGljdHMoIkRZRF9HQ1NaVCIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzLnN0YXR1c09wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgfSk7CiAgICB0aGlzLmdldERpY3RzKCJDVVNUT01FUl9TRVJWSUNFX01PREUiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy5zZXJ2aWNlTW9kZU9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgfSk7CiAgICB0aGlzLmdldERpY3RzKCJwcm9qZWN0X25yd19keWxiIikudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgX3RoaXMuZHlsYk9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgfSk7CiAgICB0aGlzLmxvYWREYXNoYm9hcmRTdGF0cygpOwogICAgLy8g6I635Y+W56CU5Y+R6YOo6YOo6Zeo5YiX6KGoCiAgICAoMCwgX2VuZ2luZWVyU2FtcGxlT3JkZXIuZ2V0UmVzZWFyY2hEZXBhcnRtZW50cykoKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy5yZXNlYXJjaERlcHREYXRhcyA9IF90aGlzLmhhbmRsZVRyZWUocmVzcG9uc2UuZGF0YSwgImRlcHRJZCIpOwogICAgfSk7CiAgICAvLyDojrflj5bmnKrliIbphY3miZPmoLfljZUKICAgIHRoaXMuaGFuZGxlVW5hc3NpZ25lZE9yZGVycygpOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOiuoeeul+S6p+WTgS/pobnnm67nrYnnuqcgKi9nZXRQcm9qZWN0TGV2ZWw6IGZ1bmN0aW9uIGdldFByb2plY3RMZXZlbChyb3cpIHsKICAgICAgdmFyIGN1c3RvbWVyTGV2ZWwgPSByb3cuY3VzdG9tZXJMZXZlbCB8fCAnJzsKICAgICAgdmFyIHByb2plY3RMZXZlbCA9IHJvdy5wcm9qZWN0TGV2ZWwgfHwgJyc7CgogICAgICAvLyDlpoLmnpwgcHJvamVjdExldmVsIOaYr+epuuWtl+espuS4suaIliAiLyIg5bCx5LiN55u45YqgCiAgICAgIGlmIChwcm9qZWN0TGV2ZWwgPT09ICcnIHx8IHByb2plY3RMZXZlbCA9PT0gJy8nKSB7CiAgICAgICAgcmV0dXJuIGN1c3RvbWVyTGV2ZWw7CiAgICAgIH0KICAgICAgcmV0dXJuIGN1c3RvbWVyTGV2ZWwgKyBwcm9qZWN0TGV2ZWw7CiAgICB9LAogICAgLyoqIOafpeivouW3peeoi+W4iOaJk+agt+WNleWFs+iBlOWIl+ihqCAqL2dldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB2YXIgcGFyYW1zID0gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCB0aGlzLnF1ZXJ5UGFyYW1zKTsKICAgICAgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UocGFyYW1zLCB0aGlzLnNjaGVkdWxlZERhdGVSYW5nZSwgJ1NjaGVkdWxlZERhdGUnKTsKICAgICAgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UocGFyYW1zLCB0aGlzLnN0YXJ0RGF0ZVJhbmdlLCAnU3RhcnREYXRlJyk7CiAgICAgIHBhcmFtcyA9IHRoaXMuYWRkRGF0ZVJhbmdlKHBhcmFtcywgdGhpcy5hY3R1YWxTdGFydFRpbWVSYW5nZSwgJ0FjdHVhbFN0YXJ0VGltZScpOwogICAgICBwYXJhbXMgPSB0aGlzLmFkZERhdGVSYW5nZShwYXJhbXMsIHRoaXMuYWN0dWFsRmluaXNoVGltZVJhbmdlLCAnQWN0dWFsRmluaXNoVGltZScpOwogICAgICAvLyDmuIXnqbrkuYvliY3nmoTml6XmnJ/ojIPlm7Tlj4LmlbDvvIzmoLnmja7lvZPliY3nirbmgIHph43mlrDorr7nva4KICAgICAgZGVsZXRlIHBhcmFtcy5iZWdpbkRhdGVSYW5nZTsKICAgICAgZGVsZXRlIHBhcmFtcy5lbmREYXRlUmFuZ2U7CiAgICAgIC8vIOa3u+WKoOaXpeacn+iMg+WbtOetm+mAieWPguaVsAogICAgICBpZiAodGhpcy5kYXRlUmFuZ2UgJiYgdGhpcy5kYXRlUmFuZ2UubGVuZ3RoID09PSAyKSB7CiAgICAgICAgcGFyYW1zLmJlZ2luRGF0ZVJhbmdlID0gdGhpcy5kYXRlUmFuZ2VbMF07CiAgICAgICAgcGFyYW1zLmVuZERhdGVSYW5nZSA9IHRoaXMuZGF0ZVJhbmdlWzFdOwogICAgICB9CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIHBhcmFtcy5hc3NvY2lhdGlvblN0YXR1cyA9IDE7CiAgICAgICgwLCBfZW5naW5lZXJTYW1wbGVPcmRlci5saXN0RW5naW5lZXJTYW1wbGVPcmRlcikocGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMi5lbmdpbmVlclNhbXBsZU9yZGVyTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgX3RoaXMyLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgX3RoaXMyLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWw6IGZ1bmN0aW9uIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0OiBmdW5jdGlvbiByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGlkOiBudWxsLAogICAgICAgIHVzZXJJZDogbnVsbCwKICAgICAgICBzYW1wbGVPcmRlckNvZGU6IG51bGwsCiAgICAgICAgc2VydmljZU1vZGU6IG51bGwsCiAgICAgICAgZGlmZmljdWx0eUxldmVsSWQ6IG51bGwsCiAgICAgICAgY29tcGxldGlvblN0YXR1czogbnVsbCwKICAgICAgICBzY2hlZHVsZWREYXRlOiBudWxsLAogICAgICAgIGFjdHVhbFN0YXJ0VGltZTogbnVsbCwKICAgICAgICBhY3R1YWxGaW5pc2hUaW1lOiBudWxsLAogICAgICAgIGFjdHVhbE1hbkhvdXJzOiBudWxsLAogICAgICAgIGVzdGltYXRlZE1hbkhvdXJzOiBudWxsLAogICAgICAgIHN0YW5kYXJkTWFuSG91cnM6IG51bGwsCiAgICAgICAgcXVhbGl0eUV2YWx1YXRpb246IG51bGwsCiAgICAgICAgaXNMb2NrZWQ6IDAsCiAgICAgICAgc3RhcnREYXRlOiBudWxsLAogICAgICAgIGVuZERhdGU6IG51bGwsCiAgICAgICAgY2hlY2tUeXBlOiBudWxsLAogICAgICAgIHNhbXBsZU9yZGVyUmVtYXJrOiBudWxsLAogICAgICAgIGNyZWF0ZUJ5OiBudWxsLAogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsCiAgICAgICAgdXBkYXRlQnk6IG51bGwsCiAgICAgICAgdXBkYXRlVGltZTogbnVsbCwKICAgICAgICByZW1hcms6IG51bGwKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovaGFuZGxlUXVlcnk6IGZ1bmN0aW9uIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLnVuYXNzaWduZWRRdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgIHRoaXMubG9hZERhc2hib2FyZFN0YXRzKCk7CiAgICAgIHRoaXMuaGFuZGxlVW5hc3NpZ25lZE9yZGVycygpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi9yZXNldFF1ZXJ5OiBmdW5jdGlvbiByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107IC8vIOmHjee9ruaXpeacn+iMg+WbtAogICAgICB0aGlzLnNjaGVkdWxlZERhdGVSYW5nZSA9IFtdOwogICAgICB0aGlzLnN0YXJ0RGF0ZVJhbmdlID0gW107CiAgICAgIHRoaXMuYWN0dWFsU3RhcnRUaW1lUmFuZ2UgPSBbXTsKICAgICAgdGhpcy5hY3R1YWxGaW5pc2hUaW1lUmFuZ2UgPSBbXTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLmlkOwogICAgICB9KTsKICAgICAgdGhpcy5zZWxlY3RlZFJvd3MgPSBzZWxlY3Rpb247CiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOwogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi9oYW5kbGVBZGQ6IGZ1bmN0aW9uIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOW3peeoi+W4iOaJk+agt+WNleWFs+iBlCI7CiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqL2hhbmRsZVVwZGF0ZTogZnVuY3Rpb24gaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB2YXIgaWQgPSByb3cuaWQgfHwgdGhpcy5pZHM7CiAgICAgICgwLCBfZW5naW5lZXJTYW1wbGVPcmRlci5nZXRFbmdpbmVlclNhbXBsZU9yZGVyKShpZCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgX3RoaXMzLm9wZW4gPSB0cnVlOwogICAgICAgIF90aGlzMy50aXRsZSA9ICLkv67mlLnlt6XnqIvluIjmiZPmoLfljZXlhbPogZQiOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovc3VibWl0Rm9ybTogZnVuY3Rpb24gc3VibWl0Rm9ybSgpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmIChfdGhpczQuZm9ybS5pZCAhPSBudWxsKSB7CiAgICAgICAgICAgICgwLCBfZW5naW5lZXJTYW1wbGVPcmRlci51cGRhdGVFbmdpbmVlclNhbXBsZU9yZGVyKShfdGhpczQuZm9ybSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgICBfdGhpczQubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgX3RoaXM0Lm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICBfdGhpczQuZ2V0TGlzdCgpOwogICAgICAgICAgICAgIF90aGlzNC5sb2FkRGFzaGJvYXJkU3RhdHMoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAoMCwgX2VuZ2luZWVyU2FtcGxlT3JkZXIuYWRkRW5naW5lZXJTYW1wbGVPcmRlcikoX3RoaXM0LmZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgX3RoaXM0Lm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIF90aGlzNC5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXM0LmdldExpc3QoKTsKICAgICAgICAgICAgICBfdGhpczQubG9hZERhc2hib2FyZFN0YXRzKCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqL2hhbmRsZURlbGV0ZTogZnVuY3Rpb24gaGFuZGxlRGVsZXRlKHJvdykgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgdmFyIGlkcyA9IHJvdy5pZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5bel56iL5biI5omT5qC35Y2V5YWz6IGU57yW5Y+35Li6IicgKyByb3cuc2FtcGxlT3JkZXJDb2RlICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gKDAsIF9lbmdpbmVlclNhbXBsZU9yZGVyLmRlbEVuZ2luZWVyU2FtcGxlT3JkZXIpKGlkcyk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNS5nZXRMaXN0KCk7CiAgICAgICAgX3RoaXM1Lm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICAgIF90aGlzNS5sb2FkRGFzaGJvYXJkU3RhdHMoKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkge30pOwogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi9oYW5kbGVFeHBvcnQ6IGZ1bmN0aW9uIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgLy8g6YeN572u5a+85Ye66YCJ6aG55bm25pi+56S66YCJ5oup5a+56K+d5qGGCiAgICAgIHRoaXMuZXhwb3J0T3B0aW9ucyA9IFtdOwogICAgICB0aGlzLmV4cG9ydERpYWxvZ09wZW4gPSB0cnVlOwogICAgfSwKICAgIC8qKiDnoa7orqTlr7zlh7rmk43kvZwgKi9jb25maXJtRXhwb3J0OiBmdW5jdGlvbiBjb25maXJtRXhwb3J0KCkgewogICAgICBpZiAodGhpcy5leHBvcnRPcHRpb25zLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36Iez5bCR6YCJ5oup5LiA56eN5a+85Ye657G75Z6LJyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuZXhwb3J0TG9hZGluZyA9IHRydWU7CiAgICAgIHRoaXMuZXhlY3V0ZUV4cG9ydHMoMCk7CiAgICB9LAogICAgLyoqIOS4suihjOaJp+ihjOWvvOWHuuaTjeS9nCAqL2V4ZWN1dGVFeHBvcnRzOiBmdW5jdGlvbiBleGVjdXRlRXhwb3J0cyhpbmRleCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgaWYgKGluZGV4ID49IHRoaXMuZXhwb3J0T3B0aW9ucy5sZW5ndGgpIHsKICAgICAgICAvLyDmiYDmnInlr7zlh7rlrozmiJAKICAgICAgICB0aGlzLmV4cG9ydExvYWRpbmcgPSBmYWxzZTsKICAgICAgICB0aGlzLmV4cG9ydERpYWxvZ09wZW4gPSBmYWxzZTsKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIlx1NUJGQ1x1NTFGQVx1NUI4Q1x1NjIxMFx1RkYwQ1x1NTE3MVx1NUJGQ1x1NTFGQSIuY29uY2F0KHRoaXMuZXhwb3J0T3B0aW9ucy5sZW5ndGgsICJcdTRFMkFcdTY1ODdcdTRFRjYiKSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHZhciBvcHRpb24gPSB0aGlzLmV4cG9ydE9wdGlvbnNbaW5kZXhdOwogICAgICB2YXIgYXNzb2NpYXRpb25TdGF0dXMgPSBvcHRpb24gPT09ICdhc3NpZ25lZCcgPyAxIDogMDsKICAgICAgdmFyIHR5cGVOYW1lID0gb3B0aW9uID09PSAnYXNzaWduZWQnID8gJ+W3suWIhumFjScgOiAn5pyq5YiG6YWNJzsKICAgICAgdGhpcy5kb0V4cG9ydChhc3NvY2lhdGlvblN0YXR1cywgb3B0aW9uKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczYuJG1lc3NhZ2Uuc3VjY2VzcygiIi5jb25jYXQodHlwZU5hbWUsICJcdTYyNTNcdTY4MzdcdTUzNTVcdTVCRkNcdTUxRkFcdTYyMTBcdTUyOUYiKSk7CiAgICAgICAgLy8g57un57ut5a+85Ye65LiL5LiA5LiqCiAgICAgICAgX3RoaXM2LmV4ZWN1dGVFeHBvcnRzKGluZGV4ICsgMSk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgIF90aGlzNi5leHBvcnRMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgX3RoaXM2LiRtZXNzYWdlLmVycm9yKCIiLmNvbmNhdCh0eXBlTmFtZSwgIlx1NjI1M1x1NjgzN1x1NTM1NVx1NUJGQ1x1NTFGQVx1NTkzMVx1OEQyNTogIikuY29uY2F0KGVycm9yLm1lc3NhZ2UgfHwgZXJyb3IpKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaJp+ihjOWvvOWHuuaTjeS9nCAqL2RvRXhwb3J0OiBmdW5jdGlvbiBkb0V4cG9ydChhc3NvY2lhdGlvblN0YXR1cywgZXhwb3J0VHlwZSkgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgICAgdmFyIHBhcmFtcyA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgdGhpcy5xdWVyeVBhcmFtcyk7CiAgICAgIHBhcmFtcyA9IHRoaXMuYWRkRGF0ZVJhbmdlKHBhcmFtcywgdGhpcy5zY2hlZHVsZWREYXRlUmFuZ2UsICdTY2hlZHVsZWREYXRlJyk7CiAgICAgIHBhcmFtcyA9IHRoaXMuYWRkRGF0ZVJhbmdlKHBhcmFtcywgdGhpcy5zdGFydERhdGVSYW5nZSwgJ1N0YXJ0RGF0ZScpOwogICAgICBwYXJhbXMgPSB0aGlzLmFkZERhdGVSYW5nZShwYXJhbXMsIHRoaXMuYWN0dWFsU3RhcnRUaW1lUmFuZ2UsICdBY3R1YWxTdGFydFRpbWUnKTsKICAgICAgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UocGFyYW1zLCB0aGlzLmFjdHVhbEZpbmlzaFRpbWVSYW5nZSwgJ0FjdHVhbEZpbmlzaFRpbWUnKTsKICAgICAgLy8g5riF56m65LmL5YmN55qE5pel5pyf6IyD5Zu05Y+C5pWw77yM5qC55o2u5b2T5YmN54q25oCB6YeN5paw6K6+572uCiAgICAgIGRlbGV0ZSBwYXJhbXMuYmVnaW5EYXRlUmFuZ2U7CiAgICAgIGRlbGV0ZSBwYXJhbXMuZW5kRGF0ZVJhbmdlOwogICAgICAvLyDmt7vliqDml6XmnJ/ojIPlm7TnrZvpgInlj4LmlbAKICAgICAgaWYgKHRoaXMuZGF0ZVJhbmdlICYmIHRoaXMuZGF0ZVJhbmdlLmxlbmd0aCA9PT0gMikgewogICAgICAgIHBhcmFtcy5iZWdpbkRhdGVSYW5nZSA9IHRoaXMuZGF0ZVJhbmdlWzBdOwogICAgICAgIHBhcmFtcy5lbmREYXRlUmFuZ2UgPSB0aGlzLmRhdGVSYW5nZVsxXTsKICAgICAgfQogICAgICBwYXJhbXMuYXNzb2NpYXRpb25TdGF0dXMgPSBhc3NvY2lhdGlvblN0YXR1czsKICAgICAgcGFyYW1zLmV4cG9ydFR5cGUgPSBleHBvcnRUeXBlOwogICAgICByZXR1cm4gKDAsIF9lbmdpbmVlclNhbXBsZU9yZGVyLmV4cG9ydEVuZ2luZWVyU2FtcGxlT3JkZXIpKHBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczcuZG93bmxvYWQocmVzcG9uc2UubXNnKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaJp+ihjOWvvOWHuuaJk+agt+WNle+8iOW3suWIhumFjS/mnKrliIbphY3vvIkgKi9kb0V4cG9ydFNhbXBsZU9yZGVyOiBmdW5jdGlvbiBkb0V4cG9ydFNhbXBsZU9yZGVyKCkgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKICAgICAgdmFyIHBhcmFtcyA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgdGhpcy5xdWVyeVBhcmFtcyk7CiAgICAgIHBhcmFtcyA9IHRoaXMuYWRkRGF0ZVJhbmdlKHBhcmFtcywgdGhpcy5zY2hlZHVsZWREYXRlUmFuZ2UsICdTY2hlZHVsZWREYXRlJyk7CiAgICAgIHBhcmFtcyA9IHRoaXMuYWRkRGF0ZVJhbmdlKHBhcmFtcywgdGhpcy5zdGFydERhdGVSYW5nZSwgJ1N0YXJ0RGF0ZScpOwogICAgICBwYXJhbXMgPSB0aGlzLmFkZERhdGVSYW5nZShwYXJhbXMsIHRoaXMuYWN0dWFsU3RhcnRUaW1lUmFuZ2UsICdBY3R1YWxTdGFydFRpbWUnKTsKICAgICAgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UocGFyYW1zLCB0aGlzLmFjdHVhbEZpbmlzaFRpbWVSYW5nZSwgJ0FjdHVhbEZpbmlzaFRpbWUnKTsKICAgICAgLy8g5riF56m65LmL5YmN55qE5pel5pyf6IyD5Zu05Y+C5pWw77yM5qC55o2u5b2T5YmN54q25oCB6YeN5paw6K6+572uCiAgICAgIGRlbGV0ZSBwYXJhbXMuYmVnaW5EYXRlUmFuZ2U7CiAgICAgIGRlbGV0ZSBwYXJhbXMuZW5kRGF0ZVJhbmdlOwogICAgICAvLyDmt7vliqDml6XmnJ/ojIPlm7TnrZvpgInlj4LmlbAKICAgICAgaWYgKHRoaXMuZGF0ZVJhbmdlICYmIHRoaXMuZGF0ZVJhbmdlLmxlbmd0aCA9PT0gMikgewogICAgICAgIHBhcmFtcy5iZWdpbkRhdGVSYW5nZSA9IHRoaXMuZGF0ZVJhbmdlWzBdOwogICAgICAgIHBhcmFtcy5lbmREYXRlUmFuZ2UgPSB0aGlzLmRhdGVSYW5nZVsxXTsKICAgICAgfQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTlr7zlh7rmiYDmnInvvIjlt7LliIbphY0v5pyq5YiG6YWN77yJ5omT5qC35Y2V5YWz6IGU5pWw5o2u77yfJywgIuaPkOekuiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzOC5leHBvcnRMb2FkaW5nID0gdHJ1ZTsKICAgICAgICByZXR1cm4gKDAsIF9lbmdpbmVlclNhbXBsZU9yZGVyLmV4cG9ydEVuZ2luZWVyU2FtcGxlT3JkZXIpKHBhcmFtcyk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM4LmRvd25sb2FkKHJlc3BvbnNlLm1zZyk7CiAgICAgICAgX3RoaXM4LmV4cG9ydExvYWRpbmcgPSBmYWxzZTsKICAgICAgICBfdGhpczguJG1lc3NhZ2Uuc3VjY2Vzcygi5a+85Ye65oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczguZXhwb3J0TG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog54K55Ye7IuW8gOWniyLmjInpkq7mk43kvZwgKi9oYW5kbGVTdGFydDogZnVuY3Rpb24gaGFuZGxlU3RhcnQocm93KSB7CiAgICAgIHZhciBfdGhpczkgPSB0aGlzOwogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHlsIbmiZPmoLfljZXvvJonICsgcm93LnNhbXBsZU9yZGVyQ29kZSArICfmoIforrDkuLrov5vooYzkuK3lkJfvvJ8nLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgKDAsIF9lbmdpbmVlclNhbXBsZU9yZGVyLnVwZGF0ZVNhbXBsZU9yZGVyU3RhdHVzKSh7CiAgICAgICAgICBpZDogcm93LmlkLAogICAgICAgICAgc3RhdHVzOiAnMScKICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgX3RoaXM5Lm1zZ1N1Y2Nlc3MoJ+aJk+agt+WNleW3suiuvue9ruS4uuW3suW8gOWniycpOwogICAgICAgICAgX3RoaXM5LmdldExpc3QoKTsKICAgICAgICAgIF90aGlzOS5sb2FkRGFzaGJvYXJkU3RhdHMoKTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOS4i+i9veaJk+agt+WNleaTjeS9nCAqL2hhbmRsZURvd25sb2FkU2FtcGxlT3JkZXI6IGZ1bmN0aW9uIGhhbmRsZURvd25sb2FkU2FtcGxlT3JkZXIocm93KSB7CiAgICAgIHZhciBfdGhpczEwID0gdGhpczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omT5qC35Y2VPycsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczEwLmV4cG9ydExvYWRpbmcgPSB0cnVlOwogICAgICAgIHJldHVybiAoMCwgX3Byb2plY3RJdGVtT3JkZXIuZXhwb3J0TnJ3SXRlbSkoewogICAgICAgICAgaXRlbUlkOiByb3cuaXRlbUlkLAogICAgICAgICAgcHJvamVjdElkOiByb3cucHJvamVjdElkCiAgICAgICAgfSk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMxMC5kb3dubG9hZChyZXNwb25zZS5tc2cpOwogICAgICAgIF90aGlzMTAuZXhwb3J0TG9hZGluZyA9IGZhbHNlOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICB9LAogICAgLyoqIOaJuemHj+WvvOWHuuaJk+agt+WNleS7u+WKoeaTjeS9nCAqL2hhbmRsZUJhdGNoRXhwb3J0TnJ3OiBmdW5jdGlvbiBoYW5kbGVCYXRjaEV4cG9ydE5ydygpIHsKICAgICAgdmFyIF90aGlzMTEgPSB0aGlzOwogICAgICBpZiAodGhpcy5zZWxlY3RlZFJvd3MubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+mAieaLqeimgeWvvOWHuueahOaJk+agt+WNleS7u+WKoSIpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgLy8g5p6E6YCg5om56YeP5a+85Ye65pWw5o2u77yM5Lyg6YCSaXRlbUlk5ZKMcHJvamVjdElkCiAgICAgIHZhciBleHBvcnREYXRhID0gdGhpcy5zZWxlY3RlZFJvd3MubWFwKGZ1bmN0aW9uIChyb3cpIHsKICAgICAgICByZXR1cm4gewogICAgICAgICAgaXRlbUlkOiByb3cuaXRlbUlkLAogICAgICAgICAgcHJvamVjdElkOiByb3cucHJvamVjdElkCiAgICAgICAgfTsKICAgICAgfSk7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWvvOWHuuaJgOmAieaJk+agt+WNleS7u+WKoeeahOWGheWuueeJqeaVsOaNru+8nycsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczExLmV4cG9ydExvYWRpbmcgPSB0cnVlOwogICAgICAgIHJldHVybiAoMCwgX3Byb2plY3RJdGVtT3JkZXIuZXhwb3J0TXVsdGlwbGVOcncpKGV4cG9ydERhdGEpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMTEuZG93bmxvYWQocmVzcG9uc2UubXNnKTsKICAgICAgICBfdGhpczExLmV4cG9ydExvYWRpbmcgPSBmYWxzZTsKICAgICAgICBfdGhpczExLiRtZXNzYWdlLnN1Y2Nlc3MoIuWvvOWHuuaIkOWKnyIpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMxMS5leHBvcnRMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDngrnlh7si5a6M5oiQIuaMiemSruaTjeS9nCAqL2hhbmRsZUZpbmlzaDogZnVuY3Rpb24gaGFuZGxlRmluaXNoKHJvdykgewogICAgICB2YXIgX3RoaXMxMiA9IHRoaXM7CiAgICAgIHRoaXMuY3VycmVudEZpbmlzaFJvdyA9IHJvdzsKICAgICAgdGhpcy5maW5pc2hUYXNrRm9ybS5sYWJvcmF0b3J5Q29kZSA9ICcnOwogICAgICB0aGlzLmZpbmlzaFRhc2tGb3JtLnJlbWFyayA9ICcnOwogICAgICB0aGlzLmZpbmlzaFRhc2tPcGVuID0gdHJ1ZTsKICAgICAgLy8g5Yqg6L295a6e6aqM5a6k57yW56CB5YiX6KGoCiAgICAgIHRoaXMubG9hZEV4cGVyaW1lbnRDb2RlTGlzdChyb3cuaWQpOwogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMxMi4kcmVmcy5maW5pc2hUYXNrRm9ybS5jbGVhclZhbGlkYXRlKCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDnoa7orqTlrozmiJDku7vliqEgKi9jb25maXJtRmluaXNoVGFzazogZnVuY3Rpb24gY29uZmlybUZpbmlzaFRhc2soKSB7CiAgICAgIHZhciBfdGhpczEzID0gdGhpczsKICAgICAgdGhpcy4kcmVmcy5maW5pc2hUYXNrRm9ybS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICAvLyDlpITnkIblrp7pqozlrqTnvJblj7flrZfmrrUKICAgICAgICB2YXIgbGFib3JhdG9yeUNvZGUgPSBfdGhpczEzLmZpbmlzaFRhc2tGb3JtLmxhYm9yYXRvcnlDb2RlOwoKICAgICAgICAvLyDlpoLmnpzmmK/mlbDnu4TnsbvlnovvvIzkvb/nlKjpgJflj7fmi7zmjqUKICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShsYWJvcmF0b3J5Q29kZSkpIHsKICAgICAgICAgIGxhYm9yYXRvcnlDb2RlID0gbGFib3JhdG9yeUNvZGUuam9pbigiLCIpOwogICAgICAgIH0KICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIF90aGlzMTMuZmluaXNoVGFza0xvYWRpbmcgPSB0cnVlOwogICAgICAgICAgKDAsIF9lbmdpbmVlclNhbXBsZU9yZGVyLnVwZGF0ZVNhbXBsZU9yZGVyU3RhdHVzKSh7CiAgICAgICAgICAgIGlkOiBfdGhpczEzLmN1cnJlbnRGaW5pc2hSb3cuaWQsCiAgICAgICAgICAgIHN0YXR1czogJzInLAogICAgICAgICAgICBsYWJvcmF0b3J5Q29kZTogbGFib3JhdG9yeUNvZGUsCiAgICAgICAgICAgIHJlbWFyazogX3RoaXMxMy5maW5pc2hUYXNrRm9ybS5yZW1hcmsKICAgICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgIF90aGlzMTMuZmluaXNoVGFza0xvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgX3RoaXMxMy5maW5pc2hUYXNrT3BlbiA9IGZhbHNlOwogICAgICAgICAgICBfdGhpczEzLm1zZ1N1Y2Nlc3MoJ+aJk+agt+WNleW3suiuvue9ruS4uuW3suWujOaIkCcpOwogICAgICAgICAgICBfdGhpczEzLmdldExpc3QoKTsKICAgICAgICAgICAgX3RoaXMxMy5sb2FkRGFzaGJvYXJkU3RhdHMoKTsKICAgICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgX3RoaXMxMy5maW5pc2hUYXNrTG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5LuO5om55qyh566h55CG5a+56K+d5qGG5a6M5oiQ5Lu75YqhICovaGFuZGxlRmluaXNoRnJvbUJhdGNoOiBmdW5jdGlvbiBoYW5kbGVGaW5pc2hGcm9tQmF0Y2goKSB7CiAgICAgIHZhciBfdGhpczE0ID0gdGhpczsKICAgICAgdGhpcy5jdXJyZW50RmluaXNoUm93ID0gdGhpcy5jdXJyZW50QmF0Y2hSb3c7CiAgICAgIHRoaXMuZmluaXNoVGFza0Zvcm0ubGFib3JhdG9yeUNvZGUgPSAnJzsKICAgICAgdGhpcy5maW5pc2hUYXNrRm9ybS5yZW1hcmsgPSAnJzsKICAgICAgdGhpcy5maW5pc2hUYXNrT3BlbiA9IHRydWU7CiAgICAgIC8vIOWKoOi9veWunumqjOWupOe8lueggeWIl+ihqAogICAgICB0aGlzLmxvYWRFeHBlcmltZW50Q29kZUxpc3QodGhpcy5jdXJyZW50QmF0Y2hSb3cuaWQpOwogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMxNC4kcmVmcy5maW5pc2hUYXNrRm9ybS5jbGVhclZhbGlkYXRlKCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDngrnlh7si5pu05paw5a6e6aqM5a6k57yW56CBIuaMiemSruaTjeS9nCAqL2hhbmRsZVVwZGF0ZUxhYm9yYXRvcnlDb2RlOiBmdW5jdGlvbiBoYW5kbGVVcGRhdGVMYWJvcmF0b3J5Q29kZShyb3cpIHsKICAgICAgdmFyIF90aGlzMTUgPSB0aGlzOwogICAgICB0aGlzLmN1cnJlbnRVcGRhdGVMYWJvcmF0b3J5Q29kZVJvdyA9IHJvdzsKICAgICAgLy8g5Zue5pi+5b2T5YmN55qE5a6e6aqM5a6k57yW56CB5ZKM5aSH5rOoCiAgICAgIHRoaXMudXBkYXRlTGFib3JhdG9yeUNvZGVGb3JtLmxhYm9yYXRvcnlDb2RlID0gcm93LmxhYm9yYXRvcnlDb2RlIHx8ICcnOwogICAgICB0aGlzLnVwZGF0ZUxhYm9yYXRvcnlDb2RlRm9ybS5yZW1hcmsgPSByb3cucmVtYXJrIHx8ICcnOwogICAgICB0aGlzLnVwZGF0ZUxhYm9yYXRvcnlDb2RlT3BlbiA9IHRydWU7CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczE1LiRyZWZzLnVwZGF0ZUxhYm9yYXRvcnlDb2RlRm9ybS5jbGVhclZhbGlkYXRlKCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDnoa7orqTmm7TmlrDlrp7pqozlrqTnvJbnoIEgKi9jb25maXJtVXBkYXRlTGFib3JhdG9yeUNvZGU6IGZ1bmN0aW9uIGNvbmZpcm1VcGRhdGVMYWJvcmF0b3J5Q29kZSgpIHsKICAgICAgdmFyIF90aGlzMTYgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzLnVwZGF0ZUxhYm9yYXRvcnlDb2RlRm9ybS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIF90aGlzMTYudXBkYXRlTGFib3JhdG9yeUNvZGVMb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgICgwLCBfZW5naW5lZXJTYW1wbGVPcmRlci51cGRhdGVTYW1wbGVPcmRlclN0YXR1cykoewogICAgICAgICAgICBpZDogX3RoaXMxNi5jdXJyZW50VXBkYXRlTGFib3JhdG9yeUNvZGVSb3cuaWQsCiAgICAgICAgICAgIHN0YXR1czogX3RoaXMxNi5jdXJyZW50VXBkYXRlTGFib3JhdG9yeUNvZGVSb3cuY29tcGxldGlvblN0YXR1cywKICAgICAgICAgICAgLy8g5L+d5oyB5b2T5YmN54q25oCBCiAgICAgICAgICAgIGxhYm9yYXRvcnlDb2RlOiBfdGhpczE2LnVwZGF0ZUxhYm9yYXRvcnlDb2RlRm9ybS5sYWJvcmF0b3J5Q29kZSwKICAgICAgICAgICAgcmVtYXJrOiBfdGhpczE2LnVwZGF0ZUxhYm9yYXRvcnlDb2RlRm9ybS5yZW1hcmsKICAgICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgIF90aGlzMTYudXBkYXRlTGFib3JhdG9yeUNvZGVMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgIF90aGlzMTYudXBkYXRlTGFib3JhdG9yeUNvZGVPcGVuID0gZmFsc2U7CiAgICAgICAgICAgIF90aGlzMTYubXNnU3VjY2Vzcygn5a6e6aqM5a6k57yW56CB5pu05paw5oiQ5YqfJyk7CiAgICAgICAgICAgIF90aGlzMTYuZ2V0TGlzdCgpOwogICAgICAgICAgICBfdGhpczE2LmxvYWREYXNoYm9hcmRTdGF0cygpOwogICAgICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgICAgICBfdGhpczE2LnVwZGF0ZUxhYm9yYXRvcnlDb2RlTG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog54K55Ye7Iumps+WbniLmjInpkq7mk43kvZwgKi9oYW5kbGVSZWplY3Q6IGZ1bmN0aW9uIGhhbmRsZVJlamVjdChyb3cpIHsKICAgICAgdmFyIF90aGlzMTcgPSB0aGlzOwogICAgICB0aGlzLmN1cnJlbnRSZWplY3RSb3cgPSByb3c7CiAgICAgIHRoaXMucmVqZWN0Rm9ybS5zYW1wbGVPcmRlckNvZGUgPSByb3cuc2FtcGxlT3JkZXJDb2RlOwogICAgICB0aGlzLnJlamVjdEZvcm0ucmVqZWN0UmVhc29uID0gJyc7CiAgICAgIHRoaXMucmVqZWN0RGlhbG9nT3BlbiA9IHRydWU7CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczE3LiRyZWZzLnJlamVjdEZvcm0uY2xlYXJWYWxpZGF0ZSgpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog56Gu6K6k6amz5ZueICovY29uZmlybVJlamVjdDogZnVuY3Rpb24gY29uZmlybVJlamVjdCgpIHsKICAgICAgdmFyIF90aGlzMTggPSB0aGlzOwogICAgICB0aGlzLiRyZWZzLnJlamVjdEZvcm0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBfdGhpczE4LnJlamVjdExvYWRpbmcgPSB0cnVlOwogICAgICAgICAgKDAsIF9lbmdpbmVlclNhbXBsZU9yZGVyLnVwZGF0ZVNhbXBsZU9yZGVyU3RhdHVzKSh7CiAgICAgICAgICAgIGlkOiBfdGhpczE4LmN1cnJlbnRSZWplY3RSb3cuaWQsCiAgICAgICAgICAgIHN0YXR1czogJzMnLAogICAgICAgICAgICByZWplY3RSZWFzb246IF90aGlzMTgucmVqZWN0Rm9ybS5yZWplY3RSZWFzb24KICAgICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgIF90aGlzMTgucmVqZWN0TG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICBfdGhpczE4LnJlamVjdERpYWxvZ09wZW4gPSBmYWxzZTsKICAgICAgICAgICAgX3RoaXMxOC5tc2dTdWNjZXNzKCfmiZPmoLfljZXlt7LpqbPlm54nKTsKICAgICAgICAgICAgX3RoaXMxOC5nZXRMaXN0KCk7CiAgICAgICAgICAgIF90aGlzMTgubG9hZERhc2hib2FyZFN0YXRzKCk7CiAgICAgICAgICAgIC8vIOWmguaenOaYr+acquWIhumFjeaJk+agt+WNleeahOmps+Wbnu+8jOS5n+mcgOimgeWIt+aWsOacquWIhumFjeWIl+ihqAogICAgICAgICAgICBfdGhpczE4LmhhbmRsZVVuYXNzaWduZWRPcmRlcnMoKTsKICAgICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgX3RoaXMxOC5yZWplY3RMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDngrnlh7si6YC+5pyf5pON5L2cIuaMiemSruaTjeS9nCAqL2hhbmRsZU92ZXJkdWVPcGVyYXRpb246IGZ1bmN0aW9uIGhhbmRsZU92ZXJkdWVPcGVyYXRpb24ocm93KSB7CiAgICAgIHZhciBfdGhpczE5ID0gdGhpczsKICAgICAgdGhpcy5jdXJyZW50T3ZlcmR1ZVJvdyA9IHJvdzsKICAgICAgdGhpcy5vdmVyZHVlT3BlcmF0aW9uRm9ybS5zYW1wbGVPcmRlckNvZGUgPSByb3cuc2FtcGxlT3JkZXJDb2RlOwogICAgICB0aGlzLm92ZXJkdWVPcGVyYXRpb25Gb3JtLmV4cGVjdGVkU2FtcGxlVGltZSA9IHJvdy5leHBlY3RlZFNhbXBsZVRpbWU7CiAgICAgIHRoaXMub3ZlcmR1ZU9wZXJhdGlvbkZvcm0ucmVhc29uRm9yTm9TYW1wbGUgPSByb3cucmVhc29uRm9yTm9TYW1wbGU7CiAgICAgIHRoaXMub3ZlcmR1ZU9wZXJhdGlvbkZvcm0uc29sdXRpb24gPSByb3cuc29sdXRpb247CiAgICAgIHRoaXMub3ZlcmR1ZU9wZXJhdGlvbk9wZW4gPSB0cnVlOwogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMxOS4kcmVmcy5vdmVyZHVlT3BlcmF0aW9uRm9ybS5jbGVhclZhbGlkYXRlKCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDnoa7orqTpgL7mnJ/mk43kvZwgKi9jb25maXJtT3ZlcmR1ZU9wZXJhdGlvbjogZnVuY3Rpb24gY29uZmlybU92ZXJkdWVPcGVyYXRpb24oKSB7CiAgICAgIHZhciBfdGhpczIwID0gdGhpczsKICAgICAgdGhpcy4kcmVmcy5vdmVyZHVlT3BlcmF0aW9uRm9ybS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIF90aGlzMjAub3ZlcmR1ZU9wZXJhdGlvbkxvYWRpbmcgPSB0cnVlOwogICAgICAgICAgKDAsIF9lbmdpbmVlclNhbXBsZU9yZGVyLnVwZGF0ZVNhbXBsZU9yZGVyU3RhdHVzKSh7CiAgICAgICAgICAgIGlkOiBfdGhpczIwLmN1cnJlbnRPdmVyZHVlUm93LmlkLAogICAgICAgICAgICBzdGF0dXM6ICIxMSIsCiAgICAgICAgICAgIC8vIOeJueauiuWkhOeQhu+8jOWPquabtOaWsOKAnOmAvuacn+aDheWGteKAneWhq+WGmeeahOWtl+autQogICAgICAgICAgICBleHBlY3RlZFNhbXBsZVRpbWU6IF90aGlzMjAub3ZlcmR1ZU9wZXJhdGlvbkZvcm0uZXhwZWN0ZWRTYW1wbGVUaW1lLAogICAgICAgICAgICByZWFzb25Gb3JOb1NhbXBsZTogX3RoaXMyMC5vdmVyZHVlT3BlcmF0aW9uRm9ybS5yZWFzb25Gb3JOb1NhbXBsZSwKICAgICAgICAgICAgc29sdXRpb246IF90aGlzMjAub3ZlcmR1ZU9wZXJhdGlvbkZvcm0uc29sdXRpb24KICAgICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgIF90aGlzMjAub3ZlcmR1ZU9wZXJhdGlvbkxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgX3RoaXMyMC5vdmVyZHVlT3BlcmF0aW9uT3BlbiA9IGZhbHNlOwogICAgICAgICAgICBfdGhpczIwLm1zZ1N1Y2Nlc3MoJ+mAvuacn+aTjeS9nOW3suWujOaIkCcpOwogICAgICAgICAgICBfdGhpczIwLmdldExpc3QoKTsKICAgICAgICAgICAgX3RoaXMyMC5sb2FkRGFzaGJvYXJkU3RhdHMoKTsKICAgICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgX3RoaXMyMC5vdmVyZHVlT3BlcmF0aW9uTG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5p+l55yL5omT5qC35Y2V6K+m5oOFICovb3JkZXJEZXRhaWw6IGZ1bmN0aW9uIG9yZGVyRGV0YWlsKHJvdykgewogICAgICB2YXIgX3RoaXMyMSA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgICAgdmFyIG9yZGVySWQsIGRhdGEsIGlkOwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQucHJldiA9IDA7CiAgICAgICAgICAgICAgLy/ojrflj5bmiafooYxJRAogICAgICAgICAgICAgIG9yZGVySWQgPSByb3cucHJvamVjdE9yZGVySWQ7CiAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDQ7CiAgICAgICAgICAgICAgcmV0dXJuICgwLCBfZW5naW5lZXJTYW1wbGVPcmRlci5nZXRFeGVjdXRpb25CeU9yZGVySW5mbykob3JkZXJJZCk7CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICBkYXRhID0gX2NvbnRleHQuc2VudDsKICAgICAgICAgICAgICBpZiAoIShkYXRhICE9IG51bGwgJiYgISgwLCBfdmFsaWRhdGUuaXNOdWxsKShkYXRhLmlkKSkpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxNDsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBpZCA9IGRhdGEuaWQ7CiAgICAgICAgICAgICAgX3RoaXMyMS4kcmVmcy5leGVjdXRpb25BZGRPckVkaXQub3BlbiA9IHRydWU7CiAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDEwOwogICAgICAgICAgICAgIHJldHVybiBfdGhpczIxLiRuZXh0VGljaygpOwogICAgICAgICAgICBjYXNlIDEwOgogICAgICAgICAgICAgIF90aGlzMjEuJHJlZnMuZXhlY3V0aW9uQWRkT3JFZGl0LnJlc2V0KCk7CiAgICAgICAgICAgICAgX3RoaXMyMS4kcmVmcy5leGVjdXRpb25BZGRPckVkaXQuc2hvdyhpZCwgMSk7CiAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDE1OwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDE0OgogICAgICAgICAgICAgIF90aGlzMjEuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluaVsOaNruWksei0pScpOwogICAgICAgICAgICBjYXNlIDE1OgogICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAyMTsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSAxNzoKICAgICAgICAgICAgICBfY29udGV4dC5wcmV2ID0gMTc7CiAgICAgICAgICAgICAgX2NvbnRleHQudDAgPSBfY29udGV4dFsiY2F0Y2giXSgwKTsKICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfmn6XnnIvpobnnm67or6bmg4XlpLHotKU6JywgX2NvbnRleHQudDApOwogICAgICAgICAgICAgIF90aGlzMjEuJG1lc3NhZ2UuZXJyb3IoJ+afpeeci+mhueebruivpuaDheWksei0pTogJyArIChfY29udGV4dC50MC5tZXNzYWdlIHx8ICfmnKrnn6XplJnor68nKSk7CiAgICAgICAgICAgIGNhc2UgMjE6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlLCBudWxsLCBbWzAsIDE3XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvKiog6ZSB5a6a54q25oCB5pS55Y+Y5aSE55CGICovaGFuZGxlTG9ja0NoYW5nZTogZnVuY3Rpb24gaGFuZGxlTG9ja0NoYW5nZSh2YWx1ZSkgewogICAgICB2YXIgX3RoaXMyMiA9IHRoaXM7CiAgICAgIGlmICh2YWx1ZSA9PT0gMSkgewogICAgICAgIHRoaXMuJGNvbmZpcm0oJ+mUgeWumuWQjuWwhuaXoOazleiHquWKqOiwg+aVtOatpOWNleeahOaOkuWNleaXpeacn++8jOaYr+WQpuehruiupOmUgeWumu+8nycsICfmj5DnpLonLCB7CiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgICAgX3RoaXMyMi5mb3JtLmlzTG9ja2VkID0gMTsKICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgICBfdGhpczIyLmZvcm0uaXNMb2NrZWQgPSAwOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLyoqIOihqOagvOS4remUgeWumueKtuaAgeaUueWPmOWkhOeQhiAqL2hhbmRsZVRhYmxlTG9ja0NoYW5nZTogZnVuY3Rpb24gaGFuZGxlVGFibGVMb2NrQ2hhbmdlKHZhbHVlLCByb3cpIHsKICAgICAgdmFyIF90aGlzMjMgPSB0aGlzOwogICAgICBpZiAodmFsdWUgPT09IDEpIHsKICAgICAgICB0aGlzLiRjb25maXJtKCfplIHlrprlkI7lsIbml6Dms5Xoh6rliqjosIPmlbTmraTljZXnmoTmjpLljZXml6XmnJ/vvIzmmK/lkKbnoa7orqTplIHlrprvvJ8nLCAn5o+Q56S6JywgewogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIC8vIOiwg+eUqOabtOaWsOaOpeWPowogICAgICAgICAgKDAsIF9lbmdpbmVlclNhbXBsZU9yZGVyLnVwZGF0ZUVuZ2luZWVyU2FtcGxlT3JkZXIpKHsKICAgICAgICAgICAgaWQ6IHJvdy5pZCwKICAgICAgICAgICAgaXNMb2NrZWQ6IDEKICAgICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgIF90aGlzMjMubXNnU3VjY2Vzcygi6ZSB5a6a5oiQ5YqfIik7CiAgICAgICAgICAgIF90aGlzMjMuZ2V0TGlzdCgpOwogICAgICAgICAgICBfdGhpczIzLmxvYWREYXNoYm9hcmRTdGF0cygpOwogICAgICAgICAgfSk7CiAgICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgICAgLy8g5Y+W5raI5pON5L2c77yM5oGi5aSN5Y6f5YC8CiAgICAgICAgICByb3cuaXNMb2NrZWQgPSAwOwogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIC8vIOino+mUgeaTjeS9nAogICAgICAgICgwLCBfZW5naW5lZXJTYW1wbGVPcmRlci51cGRhdGVFbmdpbmVlclNhbXBsZU9yZGVyKSh7CiAgICAgICAgICBpZDogcm93LmlkLAogICAgICAgICAgaXNMb2NrZWQ6IDAKICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgX3RoaXMyMy5tc2dTdWNjZXNzKCLop6PplIHmiJDlip8iKTsKICAgICAgICAgIF90aGlzMjMuZ2V0TGlzdCgpOwogICAgICAgICAgX3RoaXMyMy5sb2FkRGFzaGJvYXJkU3RhdHMoKTsKICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIC8qKiDliqDovb3nu5/orqHmpoLop4jmlbDmja4gKi9sb2FkRGFzaGJvYXJkU3RhdHM6IGZ1bmN0aW9uIGxvYWREYXNoYm9hcmRTdGF0cygpIHsKICAgICAgdmFyIF90aGlzMjQgPSB0aGlzOwogICAgICB2YXIgcGFyYW1zID0gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCB0aGlzLnF1ZXJ5UGFyYW1zKTsKICAgICAgLy8g5re75Yqg5pel5pyf6IyD5Zu0562b6YCJ5Y+C5pWwCiAgICAgIGlmICh0aGlzLmRhdGVSYW5nZSAmJiB0aGlzLmRhdGVSYW5nZS5sZW5ndGggPT09IDIpIHsKICAgICAgICBwYXJhbXMuYmVnaW5EYXRlUmFuZ2UgPSB0aGlzLmRhdGVSYW5nZVswXTsKICAgICAgICBwYXJhbXMuZW5kRGF0ZVJhbmdlID0gdGhpcy5kYXRlUmFuZ2VbMV07CiAgICAgIH0KICAgICAgKDAsIF9lbmdpbmVlclNhbXBsZU9yZGVyLmdldERhc2hib2FyZFN0YXRzKShwYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMyNC5kYXNoYm9hcmRTdGF0cyA9IHJlc3BvbnNlLmRhdGEgfHwge307CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmm7TmlLnlt6XnqIvluIjmjInpkq7mk43kvZwgKi9oYW5kbGVDaGFuZ2VFbmdpbmVlcjogZnVuY3Rpb24gaGFuZGxlQ2hhbmdlRW5naW5lZXIocm93KSB7CiAgICAgIHZhciBfdGhpczI1ID0gdGhpczsKICAgICAgdGhpcy5jaGFuZ2VFbmdpbmVlckZvcm0gPSB7CiAgICAgICAgaWQ6IHJvdy5pZCwKICAgICAgICBzYW1wbGVPcmRlckNvZGU6IHJvdy5zYW1wbGVPcmRlckNvZGUsCiAgICAgICAgY3VycmVudEVuZ2luZWVyTmFtZTogcm93Lm5pY2tOYW1lLAogICAgICAgIG9sZEVuZ2luZWVySWQ6IHJvdy51c2VySWQsCiAgICAgICAgbmV3RW5naW5lZXJJZDogbnVsbCwKICAgICAgICBzY2hlZHVsZWREYXRlOiBudWxsLAogICAgICAgIGFkanVzdFdvcmtTY2hlZHVsZTogMAogICAgICB9OwoKICAgICAgLy8g6I635Y+W5Y+v55So5bel56iL5biI5YiX6KGoCiAgICAgICgwLCBfZW5naW5lZXJTYW1wbGVPcmRlci5nZXRFbmdpbmVlcnNCeURpZmZpY3VsdHlMZXZlbCkoewogICAgICAgIGRpZmZpY3VsdHlMZXZlbElkOiByb3cuZGlmZmljdWx0eUxldmVsSWQsCiAgICAgICAgY2F0ZWdvcnlJZDogcm93LmNhdGVnb3J5SWQKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczI1LmVuZ2luZWVyT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGEgfHwgW107CiAgICAgIH0pOwogICAgICAvLyDojrflj5bnoJTlj5HmiYDmnInlt6XnqIvluIjliJfooagKICAgICAgKDAsIF9lbmdpbmVlclNhbXBsZU9yZGVyLmdldFJlc2VhcmNoRGVwYXJ0bWVudHNVc2VyKSgpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMyNS5zZWFyY2hlZEVuZ2luZWVycyA9IHJlc3BvbnNlLmRhdGEgfHwgW107CiAgICAgIH0pOwogICAgICB0aGlzLmNoYW5nZUVuZ2luZWVyT3BlbiA9IHRydWU7CiAgICB9LAogICAgLyoqIOaPkOS6pOabtOaUueW3peeoi+W4iCAqL3N1Ym1pdENoYW5nZUVuZ2luZWVyOiBmdW5jdGlvbiBzdWJtaXRDaGFuZ2VFbmdpbmVlcigpIHsKICAgICAgdmFyIF90aGlzMjYgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWyJjaGFuZ2VFbmdpbmVlckZvcm0iXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHZhciBkYXRhID0gewogICAgICAgICAgICBzYW1wbGVPcmRlcklkOiBfdGhpczI2LmNoYW5nZUVuZ2luZWVyRm9ybS5pZCwKICAgICAgICAgICAgb2xkRW5naW5lZXJJZDogX3RoaXMyNi5jaGFuZ2VFbmdpbmVlckZvcm0ub2xkRW5naW5lZXJJZCwKICAgICAgICAgICAgbmV3RW5naW5lZXJJZDogX3RoaXMyNi5jaGFuZ2VFbmdpbmVlckZvcm0ubmV3RW5naW5lZXJJZCwKICAgICAgICAgICAgc2NoZWR1bGVkRGF0ZTogX3RoaXMyNi5jaGFuZ2VFbmdpbmVlckZvcm0uc2NoZWR1bGVkRGF0ZSwKICAgICAgICAgICAgYWRqdXN0V29ya1NjaGVkdWxlOiBfdGhpczI2LmNoYW5nZUVuZ2luZWVyRm9ybS5hZGp1c3RXb3JrU2NoZWR1bGUKICAgICAgICAgIH07CgogICAgICAgICAgLy8g6LCD55So5pu05pS55bel56iL5biI55qEQVBJ5o6l5Y+jCiAgICAgICAgICAoMCwgX2VuZ2luZWVyU2FtcGxlT3JkZXIuY2hhbmdlRW5naW5lZXIpKGRhdGEpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgIF90aGlzMjYubXNnU3VjY2Vzcygi5pu05pS55bel56iL5biI5oiQ5YqfIik7CiAgICAgICAgICAgIF90aGlzMjYuY2hhbmdlRW5naW5lZXJPcGVuID0gZmFsc2U7CiAgICAgICAgICAgIF90aGlzMjYuZ2V0TGlzdCgpOwogICAgICAgICAgICAvLyDojrflj5bmnKrliIbphY3miZPmoLfljZUKICAgICAgICAgICAgX3RoaXMyNi5oYW5kbGVVbmFzc2lnbmVkT3JkZXJzKCk7CiAgICAgICAgICAgIF90aGlzMjYubG9hZERhc2hib2FyZFN0YXRzKCk7CiAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgIF90aGlzMjYubXNnRXJyb3IoIuabtOaUueW3peeoi+W4iOWksei0pSIpOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog6I635Y+W5pyq5YiG6YWN5omT5qC35Y2V5YiX6KGoICovaGFuZGxlVW5hc3NpZ25lZE9yZGVyczogZnVuY3Rpb24gaGFuZGxlVW5hc3NpZ25lZE9yZGVycygpIHsKICAgICAgdmFyIF90aGlzMjcgPSB0aGlzOwogICAgICB2YXIgcGFyYW1zID0gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCB0aGlzLnF1ZXJ5UGFyYW1zKTsKICAgICAgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UocGFyYW1zLCB0aGlzLnNjaGVkdWxlZERhdGVSYW5nZSwgJ1NjaGVkdWxlZERhdGUnKTsKICAgICAgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UocGFyYW1zLCB0aGlzLnN0YXJ0RGF0ZVJhbmdlLCAnU3RhcnREYXRlJyk7CiAgICAgIHBhcmFtcyA9IHRoaXMuYWRkRGF0ZVJhbmdlKHBhcmFtcywgdGhpcy5hY3R1YWxTdGFydFRpbWVSYW5nZSwgJ0FjdHVhbFN0YXJ0VGltZScpOwogICAgICBwYXJhbXMgPSB0aGlzLmFkZERhdGVSYW5nZShwYXJhbXMsIHRoaXMuYWN0dWFsRmluaXNoVGltZVJhbmdlLCAnQWN0dWFsRmluaXNoVGltZScpOwogICAgICAvLyDmuIXnqbrkuYvliY3nmoTml6XmnJ/ojIPlm7Tlj4LmlbDvvIzmoLnmja7lvZPliY3nirbmgIHph43mlrDorr7nva4KICAgICAgZGVsZXRlIHBhcmFtcy5iZWdpbkRhdGVSYW5nZTsKICAgICAgZGVsZXRlIHBhcmFtcy5lbmREYXRlUmFuZ2U7CiAgICAgIC8vIOa3u+WKoOaXpeacn+iMg+WbtOetm+mAieWPguaVsAogICAgICBpZiAodGhpcy5kYXRlUmFuZ2UgJiYgdGhpcy5kYXRlUmFuZ2UubGVuZ3RoID09PSAyKSB7CiAgICAgICAgcGFyYW1zLmJlZ2luRGF0ZVJhbmdlID0gdGhpcy5kYXRlUmFuZ2VbMF07CiAgICAgICAgcGFyYW1zLmVuZERhdGVSYW5nZSA9IHRoaXMuZGF0ZVJhbmdlWzFdOwogICAgICB9CiAgICAgIHBhcmFtcy5hc3NvY2lhdGlvblN0YXR1cyA9IDA7CiAgICAgIHBhcmFtcy5wYWdlTnVtID0gdGhpcy51bmFzc2lnbmVkUXVlcnlQYXJhbXMucGFnZU51bTsKICAgICAgcGFyYW1zLnBhZ2VTaXplID0gdGhpcy51bmFzc2lnbmVkUXVlcnlQYXJhbXMucGFnZVNpemU7CiAgICAgICgwLCBfZW5naW5lZXJTYW1wbGVPcmRlci5saXN0RW5naW5lZXJTYW1wbGVPcmRlcikocGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMjcudW5hc3NpZ25lZE9yZGVycyA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgX3RoaXMyNy51bmFzc2lnbmVkVG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIhumFjeW3peeoi+W4iOaTjeS9nCAqL2hhbmRsZUFzc2lnbkVuZ2luZWVyOiBmdW5jdGlvbiBoYW5kbGVBc3NpZ25FbmdpbmVlcihyb3cpIHsKICAgICAgdmFyIF90aGlzMjggPSB0aGlzOwogICAgICB0aGlzLmNoYW5nZUVuZ2luZWVyRm9ybSA9IHsKICAgICAgICBpZDogcm93LmlkLAogICAgICAgIHNhbXBsZU9yZGVyQ29kZTogcm93LnNhbXBsZU9yZGVyQ29kZSwKICAgICAgICBjdXJyZW50RW5naW5lZXJOYW1lOiAnJywKICAgICAgICBvbGRFbmdpbmVlcklkOiBudWxsLAogICAgICAgIG5ld0VuZ2luZWVySWQ6IG51bGwsCiAgICAgICAgc2NoZWR1bGVkRGF0ZTogbnVsbCwKICAgICAgICBhZGp1c3RXb3JrU2NoZWR1bGU6IDAKICAgICAgfTsKCiAgICAgIC8vIOiOt+WPluWPr+eUqOW3peeoi+W4iOWIl+ihqAogICAgICAoMCwgX2VuZ2luZWVyU2FtcGxlT3JkZXIuZ2V0RW5naW5lZXJzQnlEaWZmaWN1bHR5TGV2ZWwpKHsKICAgICAgICBkaWZmaWN1bHR5TGV2ZWxJZDogcm93LmRpZmZpY3VsdHlMZXZlbElkLAogICAgICAgIGNhdGVnb3J5SWQ6IHJvdy5jYXRlZ29yeUlkCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMyOC5lbmdpbmVlck9wdGlvbnMgPSByZXNwb25zZS5kYXRhIHx8IFtdOwogICAgICB9KTsKICAgICAgLy8g6I635Y+W56CU5Y+R5omA5pyJ5bel56iL5biI5YiX6KGoCiAgICAgICgwLCBfZW5naW5lZXJTYW1wbGVPcmRlci5nZXRSZXNlYXJjaERlcGFydG1lbnRzVXNlcikoKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMjguc2VhcmNoZWRFbmdpbmVlcnMgPSByZXNwb25zZS5kYXRhIHx8IFtdOwogICAgICB9KTsKICAgICAgdGhpcy5jaGFuZ2VFbmdpbmVlck9wZW4gPSB0cnVlOwogICAgfSwKICAgIC8qKiDliKDpmaTmnKrliIbphY3miZPmoLfljZUgKi9oYW5kbGVEZWxldGVVbmFzc2lnbmVkOiBmdW5jdGlvbiBoYW5kbGVEZWxldGVVbmFzc2lnbmVkKHJvdykgewogICAgICB2YXIgX3RoaXMyOSA9IHRoaXM7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOaJk+agt+WNlee8luWPt+S4uiInICsgcm93LnNhbXBsZU9yZGVyQ29kZSArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuICgwLCBfZW5naW5lZXJTYW1wbGVPcmRlci5kZWxFbmdpbmVlclNhbXBsZU9yZGVyKShyb3cuaWQpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczI5LmhhbmRsZVVuYXNzaWduZWRPcmRlcnMoKTsKICAgICAgICBfdGhpczI5Lm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICB9LAogICAgLyoqIOmps+WbnuacquWIhumFjeaJk+agt+WNlSAqL2hhbmRsZVJlamVjdFVuYXNzaWduZWQ6IGZ1bmN0aW9uIGhhbmRsZVJlamVjdFVuYXNzaWduZWQocm93KSB7CiAgICAgIHZhciBfdGhpczMwID0gdGhpczsKICAgICAgdGhpcy5jdXJyZW50UmVqZWN0Um93ID0gcm93OwogICAgICB0aGlzLnJlamVjdEZvcm0uc2FtcGxlT3JkZXJDb2RlID0gcm93LnNhbXBsZU9yZGVyQ29kZTsKICAgICAgdGhpcy5yZWplY3RGb3JtLnJlamVjdFJlYXNvbiA9ICcnOwogICAgICB0aGlzLnJlamVjdERpYWxvZ09wZW4gPSB0cnVlOwogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMzMC4kcmVmcy5yZWplY3RGb3JtLmNsZWFyVmFsaWRhdGUoKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOacquWIhumFjeaJk+agt+WNleWIhumhteWkp+Wwj+aUueWPmCAqL2hhbmRsZVVuYXNzaWduZWRTaXplQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVVbmFzc2lnbmVkU2l6ZUNoYW5nZShuZXdTaXplKSB7CiAgICAgIHRoaXMudW5hc3NpZ25lZFF1ZXJ5UGFyYW1zLnBhZ2VTaXplID0gbmV3U2l6ZTsKICAgICAgdGhpcy5oYW5kbGVVbmFzc2lnbmVkT3JkZXJzKCk7CiAgICB9LAogICAgLyoqIOacquWIhumFjeaJk+agt+WNlemhteeggeaUueWPmCAqL2hhbmRsZVVuYXNzaWduZWRDdXJyZW50Q2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVVbmFzc2lnbmVkQ3VycmVudENoYW5nZShuZXdQYWdlKSB7CiAgICAgIHRoaXMudW5hc3NpZ25lZFF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSBuZXdQYWdlOwogICAgICB0aGlzLmhhbmRsZVVuYXNzaWduZWRPcmRlcnMoKTsKICAgIH0sCiAgICAvKiog5YiH5o2i5pyq5YiG6YWN6Z2i5p2/5pi+56S654q25oCBICovdG9nZ2xlVW5hc3NpZ25lZFBhbmVsOiBmdW5jdGlvbiB0b2dnbGVVbmFzc2lnbmVkUGFuZWwoKSB7CiAgICAgIHRoaXMuaXNVbmFzc2lnbmVkUGFuZWxDb2xsYXBzZWQgPSAhdGhpcy5pc1VuYXNzaWduZWRQYW5lbENvbGxhcHNlZDsKICAgIH0sCiAgICAvKiog5aSE55CG54q25oCB6L+H5rukICovaGFuZGxlU3RhdHVzRmlsdGVyOiBmdW5jdGlvbiBoYW5kbGVTdGF0dXNGaWx0ZXIoc3RhdHVzKSB7CiAgICAgIC8vIOa4hemZpOmAvuacn+i/h+a7pAogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmlzT3ZlcmR1ZSA9IG51bGw7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuY29tcGxldGlvblN0YXR1cyA9IHN0YXR1czsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8qKiDlpITnkIbpgL7mnJ/ku7vliqHov4fmu6QgKi9oYW5kbGVPdmVyZHVlRmlsdGVyOiBmdW5jdGlvbiBoYW5kbGVPdmVyZHVlRmlsdGVyKCkgewogICAgICAvLyDmuIXpmaTlrozmiJDnirbmgIHov4fmu6QKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jb21wbGV0aW9uU3RhdHVzID0gbnVsbDsKICAgICAgLy8g6K6+572u6YC+5pyf6L+H5rukCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuaXNPdmVyZHVlID0gdGhpcy5xdWVyeVBhcmFtcy5pc092ZXJkdWUgPT09IDEgPyBudWxsIDogMTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgICBpZiAodGhpcy5xdWVyeVBhcmFtcy5pc092ZXJkdWUgPT09IDEpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+W3suetm+mAieaYvuekuumAvuacn+S7u+WKoScpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn5bey5riF6Zmk6YC+5pyf5Lu75Yqh562b6YCJJyk7CiAgICAgIH0KICAgIH0sCiAgICAvKiog6I635Y+W57Sn5oCl56iL5bqm57G75Z6LICovZ2V0VXJnZW5jeVR5cGU6IGZ1bmN0aW9uIGdldFVyZ2VuY3lUeXBlKGVuZERhdGUsIGxhdGVzdFN0YXJ0VGltZSkgewogICAgICB2YXIgbm93ID0gbmV3IERhdGUoKTsKICAgICAgdmFyIGVuZCA9IG5ldyBEYXRlKGVuZERhdGUpOwogICAgICB2YXIgbGF0ZXN0ID0gbGF0ZXN0U3RhcnRUaW1lID8gbmV3IERhdGUobGF0ZXN0U3RhcnRUaW1lKSA6IG51bGw7CgogICAgICAvLyDorqHnrpfot53nprvmiKrmraLml6XmnJ/nmoTlpKnmlbAKICAgICAgdmFyIGRheXNUb0VuZCA9IE1hdGguY2VpbCgoZW5kIC0gbm93KSAvICgxMDAwICogNjAgKiA2MCAqIDI0KSk7CgogICAgICAvLyDlpoLmnpzmnInmnIDmmZrlvIDlp4vml7bpl7TvvIzmo4Dmn6XmmK/lkKblt7Lnu4/otoXov4cKICAgICAgaWYgKGxhdGVzdCAmJiBub3cgPiBsYXRlc3QpIHsKICAgICAgICByZXR1cm4gJ2Rhbmdlcic7IC8vIOW3sui2hei/h+acgOaZmuW8gOWni+aXtumXtAogICAgICB9CiAgICAgIGlmIChkYXlzVG9FbmQgPD0gMSkgewogICAgICAgIHJldHVybiAnZGFuZ2VyJzsgLy8g57Sn5oCl77yaMeWkqeWGheaIquatogogICAgICB9IGVsc2UgaWYgKGRheXNUb0VuZCA8PSAzKSB7CiAgICAgICAgcmV0dXJuICd3YXJuaW5nJzsgLy8g6K2m5ZGK77yaM+WkqeWGheaIquatogogICAgICB9IGVsc2UgaWYgKGRheXNUb0VuZCA8PSA3KSB7CiAgICAgICAgcmV0dXJuICdwcmltYXJ5JzsgLy8g5LiA6Iis77yaN+WkqeWGheaIquatogogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAnc3VjY2Vzcyc7IC8vIOWFhei2s+aXtumXtAogICAgICB9CiAgICB9LAogICAgLyoqIOiOt+WPlue0p+aApeeoi+W6puaWh+acrCAqL2dldFVyZ2VuY3lUZXh0OiBmdW5jdGlvbiBnZXRVcmdlbmN5VGV4dChlbmREYXRlLCBsYXRlc3RTdGFydFRpbWUpIHsKICAgICAgdmFyIG5vdyA9IG5ldyBEYXRlKCk7CiAgICAgIHZhciBlbmQgPSBuZXcgRGF0ZShlbmREYXRlKTsKICAgICAgdmFyIGxhdGVzdCA9IGxhdGVzdFN0YXJ0VGltZSA/IG5ldyBEYXRlKGxhdGVzdFN0YXJ0VGltZSkgOiBudWxsOwogICAgICAvLyDorqHnrpfot53nprvmiKrmraLml6XmnJ/nmoTlpKnmlbAKICAgICAgdmFyIGRheXNUb0VuZCA9IE1hdGguY2VpbCgoZW5kIC0gbm93KSAvICgxMDAwICogNjAgKiA2MCAqIDI0KSk7CiAgICAgIC8vIOWmguaenOacieacgOaZmuW8gOWni+aXtumXtO+8jOajgOafpeaYr+WQpuW3sue7j+i2hei/hwogICAgICBpZiAobGF0ZXN0ICYmIG5vdyA+IGxhdGVzdCkgewogICAgICAgIHJldHVybiAn6LaF5pyf5pyq5byA5aeLJzsKICAgICAgfQogICAgICBpZiAoZGF5c1RvRW5kIDw9IDApIHsKICAgICAgICByZXR1cm4gJ+W3sumAvuacnyc7CiAgICAgIH0gZWxzZSBpZiAoZGF5c1RvRW5kIDw9IDEpIHsKICAgICAgICByZXR1cm4gJ+e0p+aApSc7CiAgICAgIH0gZWxzZSBpZiAoZGF5c1RvRW5kIDw9IDMpIHsKICAgICAgICByZXR1cm4gJ+i+g+aApSc7CiAgICAgIH0gZWxzZSBpZiAoZGF5c1RvRW5kIDw9IDcpIHsKICAgICAgICByZXR1cm4gJ+S4gOiIrCc7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuICflhYXotrMnOwogICAgICB9CiAgICB9LAogICAgLyoqIOWIt+aWsOacquWIhumFjeaJk+agt+WNleWIl+ihqCAqL2hhbmRsZVJlZnJlc2hVbmFzc2lnbmVkOiBmdW5jdGlvbiBoYW5kbGVSZWZyZXNoVW5hc3NpZ25lZCgpIHsKICAgICAgdGhpcy5oYW5kbGVVbmFzc2lnbmVkT3JkZXJzKCk7CiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yi35paw5oiQ5YqfJyk7CiAgICB9LAogICAgLyoqIOiOt+WPlumAvuacn+S/oeaBryAqL2dldE92ZXJkdWVJbmZvOiBmdW5jdGlvbiBnZXRPdmVyZHVlSW5mbyhyb3cpIHsKICAgICAgdmFyIG5vdyA9IG5ldyBEYXRlKCk7CiAgICAgIHZhciBlbmREYXRlID0gbmV3IERhdGUocm93LmVuZERhdGUpOwogICAgICB2YXIgaXNPdmVyZHVlID0gZmFsc2U7CiAgICAgIHZhciBvdmVyZHVlRGF5cyA9IDA7CiAgICAgIC8vIOagueaNruWQjuWPsFNRTOmAu+i+keWIpOaWremAvuacnwogICAgICBpZiAocm93LmNvbXBsZXRpb25TdGF0dXMgPT09IDIpIHsKICAgICAgICAvLyDlt7LlrozmiJDkuJTpgL7mnJ/vvJrlrp7pmYXlrozmiJDml7bpl7QgPiDmiKrmraLml6XmnJ8KICAgICAgICBpZiAocm93LmFjdHVhbEZpbmlzaFRpbWUpIHsKICAgICAgICAgIHZhciBhY3R1YWxGaW5pc2hUaW1lID0gbmV3IERhdGUocm93LmFjdHVhbEZpbmlzaFRpbWUpOwogICAgICAgICAgaWYgKGFjdHVhbEZpbmlzaFRpbWUgPiBlbmREYXRlKSB7CiAgICAgICAgICAgIGlzT3ZlcmR1ZSA9IHRydWU7CiAgICAgICAgICAgIG92ZXJkdWVEYXlzID0gTWF0aC5jZWlsKChhY3R1YWxGaW5pc2hUaW1lIC0gZW5kRGF0ZSkgLyAoMTAwMCAqIDYwICogNjAgKiAyNCkpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSBlbHNlIGlmIChyb3cuY29tcGxldGlvblN0YXR1cyA9PT0gMSkgewogICAgICAgIC8vIOi/m+ihjOS4reS4lOmAvuacn++8muW9k+WJjeaXtumXtCA+IOaIquatouaXpeacnwogICAgICAgIGlmIChub3cgPiBlbmREYXRlKSB7CiAgICAgICAgICBpc092ZXJkdWUgPSB0cnVlOwogICAgICAgICAgb3ZlcmR1ZURheXMgPSBNYXRoLmNlaWwoKG5vdyAtIGVuZERhdGUpIC8gKDEwMDAgKiA2MCAqIDYwICogMjQpKTsKICAgICAgICB9CiAgICAgIH0gZWxzZSBpZiAocm93LmNvbXBsZXRpb25TdGF0dXMgPT09IDApIHsKICAgICAgICAvLyDmnKrlvIDlp4vkuJTpgL7mnJ/vvJrmnKrlvIDlp4vkvYblvZPliY3ml7bpl7QgPiDlvIDlp4vml6XmnJ8KICAgICAgICBpZiAobm93ID4gZW5kRGF0ZSkgewogICAgICAgICAgaXNPdmVyZHVlID0gdHJ1ZTsKICAgICAgICAgIG92ZXJkdWVEYXlzID0gTWF0aC5jZWlsKChub3cgLSBlbmREYXRlKSAvICgxMDAwICogNjAgKiA2MCAqIDI0KSk7CiAgICAgICAgfQogICAgICB9CiAgICAgIHJldHVybiB7CiAgICAgICAgaXNPdmVyZHVlOiBpc092ZXJkdWUsCiAgICAgICAgb3ZlcmR1ZURheXM6IG92ZXJkdWVEYXlzCiAgICAgIH07CiAgICB9LAogICAgLy8gPT09PT09PT09PT09PT09PT09PT0g5om55qyh566h55CG55u45YWz5pa55rOVID09PT09PT09PT09PT09PT09PT09CiAgICAvKiog5omT5byA5om55qyh566h55CG5a+56K+d5qGGICovCiAgICBoYW5kbGVCYXRjaE1hbmFnZW1lbnQ6IGZ1bmN0aW9uIGhhbmRsZUJhdGNoTWFuYWdlbWVudChyb3cpIHsKICAgICAgdGhpcy5zZWxlY3RlZE9yZGVyRm9yQmF0Y2ggPSByb3c7CiAgICAgIHRoaXMuY3VycmVudEJhdGNoUm93ID0gcm93OyAvLyDkv53lrZjlvZPliY3ooYzmlbDmja4KICAgICAgdGhpcy5iYXRjaE1hbmFnZW1lbnRPcGVuID0gdHJ1ZTsKICAgICAgdGhpcy5sb2FkQmF0Y2hEYXRhKHJvdy5pZCk7CiAgICB9LAogICAgLyoqIOWKoOi9veaJueasoeaVsOaNriAqL2xvYWRCYXRjaERhdGE6IGZ1bmN0aW9uIGxvYWRCYXRjaERhdGEoZW5naW5lZXJTYW1wbGVPcmRlcklkKSB7CiAgICAgIHZhciBfdGhpczMxID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTIoKSB7CiAgICAgICAgdmFyIGN1cnJlbnRCYXRjaFJlc3BvbnNlLCBiYXRjaGVzUmVzcG9uc2UsIGFsbEJhdGNoZXM7CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTIkKF9jb250ZXh0MikgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQyLnByZXYgPSBfY29udGV4dDIubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQyLnByZXYgPSAwOwogICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gMzsKICAgICAgICAgICAgICByZXR1cm4gKDAsIF9lbmdpbmVlclNhbXBsZU9yZGVyLmdldEN1cnJlbnRCYXRjaCkoZW5naW5lZXJTYW1wbGVPcmRlcklkKTsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIGN1cnJlbnRCYXRjaFJlc3BvbnNlID0gX2NvbnRleHQyLnNlbnQ7CiAgICAgICAgICAgICAgX3RoaXMzMS5jdXJyZW50QmF0Y2ggPSBjdXJyZW50QmF0Y2hSZXNwb25zZS5kYXRhOwoKICAgICAgICAgICAgICAvLyDliqDovb3miYDmnInmibnmrKEKICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDc7CiAgICAgICAgICAgICAgcmV0dXJuICgwLCBfZW5naW5lZXJTYW1wbGVPcmRlci5nZXRCYXRjaGVzQnlPcmRlcklkKShlbmdpbmVlclNhbXBsZU9yZGVySWQpOwogICAgICAgICAgICBjYXNlIDc6CiAgICAgICAgICAgICAgYmF0Y2hlc1Jlc3BvbnNlID0gX2NvbnRleHQyLnNlbnQ7CiAgICAgICAgICAgICAgYWxsQmF0Y2hlcyA9IGJhdGNoZXNSZXNwb25zZS5kYXRhIHx8IFtdOyAvLyDliIbnprvlvZPliY3mibnmrKHlkozljoblj7LmibnmrKHvvIzlubbkuLrmr4/kuKrljoblj7LmibnmrKHliqDovb3lrp7pqozmlbDph48KICAgICAgICAgICAgICBfdGhpczMxLmhpc3RvcnlCYXRjaGVzID0gYWxsQmF0Y2hlcy5maWx0ZXIoZnVuY3Rpb24gKGJhdGNoKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gYmF0Y2guaXNDdXJyZW50QmF0Y2ggPT09IDA7CiAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgIC8vIC8vIOS4uuavj+S4quWOhuWPsuaJueasoeWKoOi9veWunumqjOaVsOmHjwogICAgICAgICAgICAgIC8vIGZvciAobGV0IGJhdGNoIG9mIHRoaXMuaGlzdG9yeUJhdGNoZXMpIHsKICAgICAgICAgICAgICAvLyAgIHRyeSB7CiAgICAgICAgICAgICAgLy8gICAgIGNvbnN0IGV4cGVyaW1lbnRzUmVzcG9uc2UgPSBhd2FpdCBnZXRFeHBlcmltZW50c0J5QmF0Y2hJZChiYXRjaC5pZCk7CiAgICAgICAgICAgICAgLy8gICAgIGJhdGNoLmV4cGVyaW1lbnRDb3VudCA9IGV4cGVyaW1lbnRzUmVzcG9uc2UuZGF0YSA/IGV4cGVyaW1lbnRzUmVzcG9uc2UuZGF0YS5sZW5ndGggOiAwOwogICAgICAgICAgICAgIC8vICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgICAgICAvLyAgICAgY29uc29sZS53YXJuKGDliqDovb3mibnmrKEke2JhdGNoLmlkfeeahOWunumqjOaVsOmHj+Wksei0pTpgLCBlcnJvcik7CiAgICAgICAgICAgICAgLy8gICAgIGJhdGNoLmV4cGVyaW1lbnRDb3VudCA9IDA7CiAgICAgICAgICAgICAgLy8gICB9CiAgICAgICAgICAgICAgLy8gfQoKICAgICAgICAgICAgICAvLyDlpoLmnpzmnInlvZPliY3mibnmrKHvvIzliqDovb3lrp7pqozorrDlvZUKICAgICAgICAgICAgICBpZiAoIV90aGlzMzEuY3VycmVudEJhdGNoKSB7CiAgICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDEzOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gMTM7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMzEubG9hZEN1cnJlbnRCYXRjaEV4cGVyaW1lbnRzKF90aGlzMzEuY3VycmVudEJhdGNoLmlkKTsKICAgICAgICAgICAgY2FzZSAxMzoKICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDE5OwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDE1OgogICAgICAgICAgICAgIF9jb250ZXh0Mi5wcmV2ID0gMTU7CiAgICAgICAgICAgICAgX2NvbnRleHQyLnQwID0gX2NvbnRleHQyWyJjYXRjaCJdKDApOwogICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veaJueasoeaVsOaNruWksei0pTonLCBfY29udGV4dDIudDApOwogICAgICAgICAgICAgIF90aGlzMzEuJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9veaJueasoeaVsOaNruWksei0pScpOwogICAgICAgICAgICBjYXNlIDE5OgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUyLCBudWxsLCBbWzAsIDE1XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvKiog5Yqg6L295b2T5YmN5om55qyh55qE5a6e6aqM6K6w5b2VICovbG9hZEN1cnJlbnRCYXRjaEV4cGVyaW1lbnRzOiBmdW5jdGlvbiBsb2FkQ3VycmVudEJhdGNoRXhwZXJpbWVudHMoYmF0Y2hJZCkgewogICAgICB2YXIgX3RoaXMzMiA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUzKCkgewogICAgICAgIHZhciByZXNwb25zZTsKICAgICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlMyQoX2NvbnRleHQzKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDMucHJldiA9IF9jb250ZXh0My5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfY29udGV4dDMucHJldiA9IDA7CiAgICAgICAgICAgICAgX2NvbnRleHQzLm5leHQgPSAzOwogICAgICAgICAgICAgIHJldHVybiAoMCwgX2VuZ2luZWVyU2FtcGxlT3JkZXIuZ2V0RXhwZXJpbWVudHNCeUJhdGNoSWQpKGJhdGNoSWQpOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgcmVzcG9uc2UgPSBfY29udGV4dDMuc2VudDsKICAgICAgICAgICAgICBfdGhpczMyLmN1cnJlbnRCYXRjaEV4cGVyaW1lbnRzID0gcmVzcG9uc2UuZGF0YSB8fCBbXTsKICAgICAgICAgICAgICBfY29udGV4dDMubmV4dCA9IDEwOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDc6CiAgICAgICAgICAgICAgX2NvbnRleHQzLnByZXYgPSA3OwogICAgICAgICAgICAgIF9jb250ZXh0My50MCA9IF9jb250ZXh0M1siY2F0Y2giXSgwKTsKICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3lrp7pqozorrDlvZXlpLHotKU6JywgX2NvbnRleHQzLnQwKTsKICAgICAgICAgICAgY2FzZSAxMDoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQzLnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMywgbnVsbCwgW1swLCA3XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvKiog5byA5aeL5paw5om55qyhICovc3RhcnROZXdCYXRjaDogZnVuY3Rpb24gc3RhcnROZXdCYXRjaCgpIHsKICAgICAgdmFyIF90aGlzMzMgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlNCgpIHsKICAgICAgICB2YXIgcmVzcG9uc2U7CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTQkKF9jb250ZXh0NCkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ0LnByZXYgPSBfY29udGV4dDQubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgaWYgKF90aGlzMzMuY2FuRWRpdEJhdGNoKSB7CiAgICAgICAgICAgICAgICBfY29udGV4dDQubmV4dCA9IDM7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX3RoaXMzMy4kbWVzc2FnZS53YXJuaW5nKCflvZPliY3miZPmoLfljZXnirbmgIHkuI3lhYHorrjlvIDlp4vmlrDmibnmrKHvvIzlj6rmnInnirbmgIHkuLoi6L+b6KGM5LitIueahOaJk+agt+WNleaJjeWPr+S7pee8lui+keaJueasoeS/oeaBrycpOwogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDQuYWJydXB0KCJyZXR1cm4iKTsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIF9jb250ZXh0NC5wcmV2ID0gMzsKICAgICAgICAgICAgICBfY29udGV4dDQubmV4dCA9IDY7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMzMuJGNvbmZpcm0oJ+ehruiupOW8gOWni+aWsOeahOaJk+agt+aJueasoe+8nycsICfmj5DnpLonLCB7CiAgICAgICAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjYXNlIDY6CiAgICAgICAgICAgICAgX2NvbnRleHQ0Lm5leHQgPSA4OwogICAgICAgICAgICAgIHJldHVybiAoMCwgX2VuZ2luZWVyU2FtcGxlT3JkZXIuc3RhcnROZXdCYXRjaCkoX3RoaXMzMy5zZWxlY3RlZE9yZGVyRm9yQmF0Y2guaWQsICcnKTsKICAgICAgICAgICAgY2FzZSA4OgogICAgICAgICAgICAgIHJlc3BvbnNlID0gX2NvbnRleHQ0LnNlbnQ7CiAgICAgICAgICAgICAgX3RoaXMzMy4kbWVzc2FnZS5zdWNjZXNzKCfmlrDmibnmrKHlvIDlp4vmiJDlip8nKTsKCiAgICAgICAgICAgICAgLy8g6YeN5paw5Yqg6L295om55qyh5pWw5o2uCiAgICAgICAgICAgICAgX2NvbnRleHQ0Lm5leHQgPSAxMjsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMzMy5sb2FkQmF0Y2hEYXRhKF90aGlzMzMuc2VsZWN0ZWRPcmRlckZvckJhdGNoLmlkKTsKICAgICAgICAgICAgY2FzZSAxMjoKICAgICAgICAgICAgICAvLyDliLfmlrDkuLvliJfooagKICAgICAgICAgICAgICBfdGhpczMzLmdldExpc3QoKTsKICAgICAgICAgICAgICBfY29udGV4dDQubmV4dCA9IDE4OwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDE1OgogICAgICAgICAgICAgIF9jb250ZXh0NC5wcmV2ID0gMTU7CiAgICAgICAgICAgICAgX2NvbnRleHQ0LnQwID0gX2NvbnRleHQ0WyJjYXRjaCJdKDMpOwogICAgICAgICAgICAgIGlmIChfY29udGV4dDQudDAgIT09ICdjYW5jZWwnKSB7CiAgICAgICAgICAgICAgICBfdGhpczMzLiRtZXNzYWdlLmVycm9yKCflvIDlp4vmlrDmibnmrKHlpLHotKU6ICcgKyAoX2NvbnRleHQ0LnQwLm1lc3NhZ2UgfHwgX2NvbnRleHQ0LnQwKSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICBjYXNlIDE4OgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDQuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU0LCBudWxsLCBbWzMsIDE1XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvKiog57uT5p2f5b2T5YmN5om55qyhICovZmluaXNoQ3VycmVudEJhdGNoOiBmdW5jdGlvbiBmaW5pc2hDdXJyZW50QmF0Y2goKSB7CiAgICAgIC8vIOajgOafpee8lui+keadg+mZkAogICAgICBpZiAoIXRoaXMuY2FuRWRpdEJhdGNoKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCflvZPliY3miZPmoLfljZXnirbmgIHkuI3lhYHorrjnu5PmnZ/mibnmrKHvvIzlj6rmnInnirbmgIHkuLoi6L+b6KGM5LitIueahOaJk+agt+WNleaJjeWPr+S7pee8lui+keaJueasoeS/oeaBrycpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLmZpbmlzaEJhdGNoRm9ybSA9IHsKICAgICAgICBxdWFsaXR5RXZhbHVhdGlvbjogJycsCiAgICAgICAgcmVtYXJrOiAnJwogICAgICB9OwogICAgICB0aGlzLmZpbmlzaEJhdGNoT3BlbiA9IHRydWU7CiAgICB9LAogICAgLyoqIOaPkOS6pOe7k+adn+aJueasoSAqL3N1Ym1pdEZpbmlzaEJhdGNoOiBmdW5jdGlvbiBzdWJtaXRGaW5pc2hCYXRjaCgpIHsKICAgICAgdmFyIF90aGlzMzQgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlNSgpIHsKICAgICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlNSQoX2NvbnRleHQ1KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDUucHJldiA9IF9jb250ZXh0NS5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfY29udGV4dDUucHJldiA9IDA7CiAgICAgICAgICAgICAgX2NvbnRleHQ1Lm5leHQgPSAzOwogICAgICAgICAgICAgIHJldHVybiAoMCwgX2VuZ2luZWVyU2FtcGxlT3JkZXIuZmluaXNoQ3VycmVudEJhdGNoKShfdGhpczM0LnNlbGVjdGVkT3JkZXJGb3JCYXRjaC5pZCwgX3RoaXMzNC5maW5pc2hCYXRjaEZvcm0ucXVhbGl0eUV2YWx1YXRpb24sIF90aGlzMzQuZmluaXNoQmF0Y2hGb3JtLnJlbWFyayk7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICBfdGhpczM0LiRtZXNzYWdlLnN1Y2Nlc3MoJ+aJueasoee7k+adn+aIkOWKnycpOwogICAgICAgICAgICAgIF90aGlzMzQuZmluaXNoQmF0Y2hPcGVuID0gZmFsc2U7CgogICAgICAgICAgICAgIC8vIOmHjeaWsOWKoOi9veaJueasoeaVsOaNrgogICAgICAgICAgICAgIF9jb250ZXh0NS5uZXh0ID0gNzsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMzNC5sb2FkQmF0Y2hEYXRhKF90aGlzMzQuc2VsZWN0ZWRPcmRlckZvckJhdGNoLmlkKTsKICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgIC8vIOWIt+aWsOS4u+WIl+ihqAogICAgICAgICAgICAgIF90aGlzMzQuZ2V0TGlzdCgpOwogICAgICAgICAgICAgIF9jb250ZXh0NS5uZXh0ID0gMTQ7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMTA6CiAgICAgICAgICAgICAgX2NvbnRleHQ1LnByZXYgPSAxMDsKICAgICAgICAgICAgICBfY29udGV4dDUudDAgPSBfY29udGV4dDVbImNhdGNoIl0oMCk7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign57uT5p2f5om55qyh5aSx6LSlOicsIF9jb250ZXh0NS50MCk7CiAgICAgICAgICAgICAgX3RoaXMzNC4kbWVzc2FnZS5lcnJvcign57uT5p2f5om55qyh5aSx6LSlOiAnICsgKF9jb250ZXh0NS50MC5tZXNzYWdlIHx8IF9jb250ZXh0NS50MCkpOwogICAgICAgICAgICBjYXNlIDE0OgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDUuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU1LCBudWxsLCBbWzAsIDEwXV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvKiog5re75Yqg5a6e6aqM6K6w5b2VICovYWRkRXhwZXJpbWVudDogZnVuY3Rpb24gYWRkRXhwZXJpbWVudCgpIHsKICAgICAgLy8g5qOA5p+l57yW6L6R5p2D6ZmQCiAgICAgIGlmICghdGhpcy5jYW5FZGl0QmF0Y2gpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+W9k+WJjeaJk+agt+WNleeKtuaAgeS4jeWFgeiuuOa3u+WKoOWunumqjOiusOW9le+8jOWPquacieeKtuaAgeS4uiLov5vooYzkuK0i55qE5omT5qC35Y2V5omN5Y+v5Lul57yW6L6R5om55qyh5L+h5oGvJyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuZXhwZXJpbWVudEZvcm0gPSB7CiAgICAgICAgZXhwZXJpbWVudENvZGU6ICcnLAogICAgICAgIGV4cGVyaW1lbnROb3RlOiAnJwogICAgICB9OwogICAgICB0aGlzLmFkZEV4cGVyaW1lbnRPcGVuID0gdHJ1ZTsKICAgIH0sCiAgICAvKiog5o+Q5Lqk5a6e6aqM6K6w5b2VICovc3VibWl0RXhwZXJpbWVudDogZnVuY3Rpb24gc3VibWl0RXhwZXJpbWVudCgpIHsKICAgICAgdmFyIF90aGlzMzUgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlNigpIHsKICAgICAgICB2YXIgZXhwZXJpbWVudFJlY29yZDsKICAgICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlNiQoX2NvbnRleHQ2KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDYucHJldiA9IF9jb250ZXh0Ni5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBpZiAoX3RoaXMzNS5jYW5FZGl0QmF0Y2gpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0Ni5uZXh0ID0gMzsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfdGhpczM1LiRtZXNzYWdlLndhcm5pbmcoJ+W9k+WJjeaJk+agt+WNleeKtuaAgeS4jeWFgeiuuOa3u+WKoOWunumqjOiusOW9le+8jOWPquacieeKtuaAgeS4uiLov5vooYzkuK0i55qE5omT5qC35Y2V5omN5Y+v5Lul57yW6L6R5om55qyh5L+h5oGvJyk7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Ni5hYnJ1cHQoInJldHVybiIpOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgX2NvbnRleHQ2Lm5leHQgPSA1OwogICAgICAgICAgICAgIHJldHVybiBfdGhpczM1LiRyZWZzLmV4cGVyaW1lbnRGb3JtLnZhbGlkYXRlKCk7CiAgICAgICAgICAgIGNhc2UgNToKICAgICAgICAgICAgICBleHBlcmltZW50UmVjb3JkID0gewogICAgICAgICAgICAgICAgYmF0Y2hJZDogX3RoaXMzNS5jdXJyZW50QmF0Y2guaWQsCiAgICAgICAgICAgICAgICBleHBlcmltZW50Q29kZTogX3RoaXMzNS5leHBlcmltZW50Rm9ybS5leHBlcmltZW50Q29kZSwKICAgICAgICAgICAgICAgIGV4cGVyaW1lbnROb3RlOiBfdGhpczM1LmV4cGVyaW1lbnRGb3JtLmV4cGVyaW1lbnROb3RlIHx8ICcnCiAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICBfY29udGV4dDYubmV4dCA9IDg7CiAgICAgICAgICAgICAgcmV0dXJuICgwLCBfZW5naW5lZXJTYW1wbGVPcmRlci5hZGRFeHBlcmltZW50VG9CYXRjaCkoZXhwZXJpbWVudFJlY29yZCk7CiAgICAgICAgICAgIGNhc2UgODoKICAgICAgICAgICAgICBfdGhpczM1LiRtZXNzYWdlLnN1Y2Nlc3MoJ+WunumqjOiusOW9lea3u+WKoOaIkOWKnycpOwogICAgICAgICAgICAgIF90aGlzMzUuYWRkRXhwZXJpbWVudE9wZW4gPSBmYWxzZTsKCiAgICAgICAgICAgICAgLy8g6YeN5paw5Yqg6L295b2T5YmN5om55qyh55qE5a6e6aqM6K6w5b2VCiAgICAgICAgICAgICAgX2NvbnRleHQ2Lm5leHQgPSAxMjsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMzNS5sb2FkQ3VycmVudEJhdGNoRXhwZXJpbWVudHMoX3RoaXMzNS5jdXJyZW50QmF0Y2guaWQpOwogICAgICAgICAgICBjYXNlIDEyOgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDYuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU2KTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLyoqIOafpeeci+aJueasoeivpuaDhSAqL3ZpZXdCYXRjaERldGFpbDogZnVuY3Rpb24gdmlld0JhdGNoRGV0YWlsKGJhdGNoKSB7CiAgICAgIHZhciBfdGhpczM2ID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTcoKSB7CiAgICAgICAgdmFyIHJlc3BvbnNlLCBleHBlcmltZW50czsKICAgICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlNyQoX2NvbnRleHQ3KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDcucHJldiA9IF9jb250ZXh0Ny5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfY29udGV4dDcucHJldiA9IDA7CiAgICAgICAgICAgICAgX2NvbnRleHQ3Lm5leHQgPSAzOwogICAgICAgICAgICAgIHJldHVybiAoMCwgX2VuZ2luZWVyU2FtcGxlT3JkZXIuZ2V0RXhwZXJpbWVudHNCeUJhdGNoSWQpKGJhdGNoLmlkKTsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIHJlc3BvbnNlID0gX2NvbnRleHQ3LnNlbnQ7CiAgICAgICAgICAgICAgZXhwZXJpbWVudHMgPSByZXNwb25zZS5kYXRhIHx8IFtdOyAvLyDorr7nva7mibnmrKHor6bmg4XmlbDmja4KICAgICAgICAgICAgICBfdGhpczM2LmJhdGNoRGV0YWlsRGF0YSA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIGJhdGNoKSwge30sIHsKICAgICAgICAgICAgICAgIGV4cGVyaW1lbnRzOiBleHBlcmltZW50cwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIF90aGlzMzYuYmF0Y2hEZXRhaWxPcGVuID0gdHJ1ZTsKICAgICAgICAgICAgICBfY29udGV4dDcubmV4dCA9IDEzOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDk6CiAgICAgICAgICAgICAgX2NvbnRleHQ3LnByZXYgPSA5OwogICAgICAgICAgICAgIF9jb250ZXh0Ny50MCA9IF9jb250ZXh0N1siY2F0Y2giXSgwKTsKICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfmn6XnnIvmibnmrKHor6bmg4XlpLHotKU6JywgX2NvbnRleHQ3LnQwKTsKICAgICAgICAgICAgICBfdGhpczM2LiRtZXNzYWdlLmVycm9yKCfmn6XnnIvmibnmrKHor6bmg4XlpLHotKUnKTsKICAgICAgICAgICAgY2FzZSAxMzoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ3LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNywgbnVsbCwgW1swLCA5XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvKiog6K6h566X5oyB57ut5pe26Ze0ICovY2FsY3VsYXRlRHVyYXRpb246IGZ1bmN0aW9uIGNhbGN1bGF0ZUR1cmF0aW9uKHN0YXJ0VGltZSkgewogICAgICBpZiAoIXN0YXJ0VGltZSkgcmV0dXJuICcw5bCP5pe2JzsKICAgICAgdmFyIHN0YXJ0ID0gbmV3IERhdGUoc3RhcnRUaW1lKTsKICAgICAgdmFyIG5vdyA9IG5ldyBEYXRlKCk7CiAgICAgIHZhciBkaWZmTXMgPSBub3cgLSBzdGFydDsKICAgICAgdmFyIGRpZmZIb3VycyA9IE1hdGguZmxvb3IoZGlmZk1zIC8gKDEwMDAgKiA2MCAqIDYwKSk7CiAgICAgIHZhciBkaWZmTWludXRlcyA9IE1hdGguZmxvb3IoZGlmZk1zICUgKDEwMDAgKiA2MCAqIDYwKSAvICgxMDAwICogNjApKTsKICAgICAgaWYgKGRpZmZIb3VycyA+IDApIHsKICAgICAgICByZXR1cm4gIiIuY29uY2F0KGRpZmZIb3VycywgIlx1NUMwRlx1NjVGNiIpLmNvbmNhdChkaWZmTWludXRlcywgIlx1NTIwNlx1OTQ5RiIpOwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAiIi5jb25jYXQoZGlmZk1pbnV0ZXMsICJcdTUyMDZcdTk0OUYiKTsKICAgICAgfQogICAgfSwKICAgIC8qKiDojrflj5botKjph4/or4Tku7fnsbvlnosgKi9nZXRRdWFsaXR5RXZhbHVhdGlvblR5cGU6IGZ1bmN0aW9uIGdldFF1YWxpdHlFdmFsdWF0aW9uVHlwZShldmFsdWF0aW9uKSB7CiAgICAgIGlmICghZXZhbHVhdGlvbikgcmV0dXJuICcnOwogICAgICB2YXIgbG93ZXJFdmFsID0gZXZhbHVhdGlvbi50b0xvd2VyQ2FzZSgpOwogICAgICBpZiAobG93ZXJFdmFsLmluY2x1ZGVzKCfkvJjnp4AnKSB8fCBsb3dlckV2YWwuaW5jbHVkZXMoJ+iJr+WlvScpIHx8IGxvd2VyRXZhbC5pbmNsdWRlcygn5aW9JykpIHsKICAgICAgICByZXR1cm4gJ3N1Y2Nlc3MnOwogICAgICB9IGVsc2UgaWYgKGxvd2VyRXZhbC5pbmNsdWRlcygn5LiA6IisJykgfHwgbG93ZXJFdmFsLmluY2x1ZGVzKCfkuK3nrYknKSkgewogICAgICAgIHJldHVybiAnd2FybmluZyc7CiAgICAgIH0gZWxzZSBpZiAobG93ZXJFdmFsLmluY2x1ZGVzKCflt64nKSB8fCBsb3dlckV2YWwuaW5jbHVkZXMoJ+S4jeWQiOagvCcpIHx8IGxvd2VyRXZhbC5pbmNsdWRlcygn5aSx6LSlJykpIHsKICAgICAgICByZXR1cm4gJ2Rhbmdlcic7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuICdpbmZvJzsKICAgICAgfQogICAgfSwKICAgIC8qKiDlhbPpl63mibnmrKHnrqHnkIblr7nor53moYblubbmuIXnqbrmlbDmja4gKi9jbG9zZUJhdGNoTWFuYWdlbWVudDogZnVuY3Rpb24gY2xvc2VCYXRjaE1hbmFnZW1lbnQoKSB7CiAgICAgIC8vIOa4heepuuaJueasoeeuoeeQhuebuOWFs+eahOaVsOaNrgogICAgICB0aGlzLmN1cnJlbnRCYXRjaCA9IG51bGw7CiAgICAgIHRoaXMuaGlzdG9yeUJhdGNoZXMgPSBbXTsKICAgICAgdGhpcy5jdXJyZW50QmF0Y2hFeHBlcmltZW50cyA9IFtdOwogICAgICB0aGlzLnNlbGVjdGVkT3JkZXJGb3JCYXRjaCA9IG51bGw7CiAgICAgIHRoaXMuY3VycmVudEJhdGNoUm93ID0gbnVsbDsgLy8g5riF56m65b2T5YmN6KGM5pWw5o2uCiAgICAgIHRoaXMuYmF0Y2hEZXRhaWxEYXRhID0gbnVsbDsKCiAgICAgIC8vIOmHjee9ruihqOWNleaVsOaNrgogICAgICB0aGlzLmV4cGVyaW1lbnRGb3JtID0gewogICAgICAgIGV4cGVyaW1lbnRDb2RlOiAnJywKICAgICAgICBleHBlcmltZW50Tm90ZTogJycKICAgICAgfTsKICAgICAgdGhpcy5maW5pc2hCYXRjaEZvcm0gPSB7CiAgICAgICAgcXVhbGl0eUV2YWx1YXRpb246ICcnLAogICAgICAgIHJlbWFyazogJycKICAgICAgfTsKCiAgICAgIC8vIOWFs+mXreaJgOacieebuOWFs+eahOWtkOWvueivneahhgogICAgICB0aGlzLmJhdGNoRGV0YWlsT3BlbiA9IGZhbHNlOwogICAgICB0aGlzLmFkZEV4cGVyaW1lbnRPcGVuID0gZmFsc2U7CiAgICAgIHRoaXMuZmluaXNoQmF0Y2hPcGVuID0gZmFsc2U7CiAgICB9LAogICAgLyoqIOWFs+mXreaJueasoeivpuaDheWvueivneahhuW5tua4heepuuaVsOaNriAqL2Nsb3NlQmF0Y2hEZXRhaWw6IGZ1bmN0aW9uIGNsb3NlQmF0Y2hEZXRhaWwoKSB7CiAgICAgIC8vIOa4heepuuaJueasoeivpuaDheaVsOaNrgogICAgICB0aGlzLmJhdGNoRGV0YWlsRGF0YSA9IG51bGw7CiAgICB9LAogICAgLyoqIOWFs+mXrea3u+WKoOWunumqjOWvueivneahhuW5tua4heepuuaVsOaNriAqL2Nsb3NlQWRkRXhwZXJpbWVudDogZnVuY3Rpb24gY2xvc2VBZGRFeHBlcmltZW50KCkgewogICAgICAvLyDmuIXnqbrlrp7pqozooajljZXmlbDmja4KICAgICAgdGhpcy5leHBlcmltZW50Rm9ybSA9IHsKICAgICAgICBleHBlcmltZW50Q29kZTogJycsCiAgICAgICAgZXhwZXJpbWVudE5vdGU6ICcnCiAgICAgIH07CiAgICAgIC8vIOa4hemZpOihqOWNlemqjOivgeeKtuaAgQogICAgICBpZiAodGhpcy4kcmVmcy5leHBlcmltZW50Rm9ybSkgewogICAgICAgIHRoaXMuJHJlZnMuZXhwZXJpbWVudEZvcm0uY2xlYXJWYWxpZGF0ZSgpOwogICAgICB9CiAgICB9LAogICAgLyoqIOWFs+mXree7k+adn+aJueasoeWvueivneahhuW5tua4heepuuaVsOaNriAqL2Nsb3NlRmluaXNoQmF0Y2g6IGZ1bmN0aW9uIGNsb3NlRmluaXNoQmF0Y2goKSB7CiAgICAgIC8vIOa4heepuue7k+adn+aJueasoeihqOWNleaVsOaNrgogICAgICB0aGlzLmZpbmlzaEJhdGNoRm9ybSA9IHsKICAgICAgICBxdWFsaXR5RXZhbHVhdGlvbjogJycsCiAgICAgICAgcmVtYXJrOiAnJwogICAgICB9OwogICAgICAvLyDmuIXpmaTooajljZXpqozor4HnirbmgIEKICAgICAgaWYgKHRoaXMuJHJlZnMuZmluaXNoQmF0Y2hGb3JtKSB7CiAgICAgICAgdGhpcy4kcmVmcy5maW5pc2hCYXRjaEZvcm0uY2xlYXJWYWxpZGF0ZSgpOwogICAgICB9CiAgICB9LAogICAgLyoqIOWIt+aWsOaJueasoeaVsOaNriAqL3JlZnJlc2hCYXRjaERhdGE6IGZ1bmN0aW9uIHJlZnJlc2hCYXRjaERhdGEoKSB7CiAgICAgIHZhciBfdGhpczM3ID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTgoKSB7CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTgkKF9jb250ZXh0OCkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ4LnByZXYgPSBfY29udGV4dDgubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgaWYgKCFfdGhpczM3LnNlbGVjdGVkT3JkZXJGb3JCYXRjaCkgewogICAgICAgICAgICAgICAgX2NvbnRleHQ4Lm5leHQgPSA0OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0OC5uZXh0ID0gMzsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMzNy5sb2FkQmF0Y2hEYXRhKF90aGlzMzcuc2VsZWN0ZWRPcmRlckZvckJhdGNoLmlkKTsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIF90aGlzMzcuJG1lc3NhZ2Uuc3VjY2Vzcygn5om55qyh5pWw5o2u5bey5Yi35pawJyk7CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ4LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlOCk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8qKiDliqDovb3lrp7pqozlrqTnvJbnoIHliJfooaggKi9sb2FkRXhwZXJpbWVudENvZGVMaXN0OiBmdW5jdGlvbiBsb2FkRXhwZXJpbWVudENvZGVMaXN0KGVuZ2luZWVyU2FtcGxlT3JkZXJJZCkgewogICAgICB2YXIgX3RoaXMzOCA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWU5KCkgewogICAgICAgIHZhciByZXNwb25zZTsKICAgICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlOSQoX2NvbnRleHQ5KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDkucHJldiA9IF9jb250ZXh0OS5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfY29udGV4dDkucHJldiA9IDA7CiAgICAgICAgICAgICAgX2NvbnRleHQ5Lm5leHQgPSAzOwogICAgICAgICAgICAgIHJldHVybiAoMCwgX2VuZ2luZWVyU2FtcGxlT3JkZXIuZ2V0QmF0Y2hFeHBlcmltZW50Q29kZUxpc3QpKGVuZ2luZWVyU2FtcGxlT3JkZXJJZCk7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICByZXNwb25zZSA9IF9jb250ZXh0OS5zZW50OwogICAgICAgICAgICAgIF90aGlzMzguZXhwZXJpbWVudENvZGVMaXN0ID0gcmVzcG9uc2UuZGF0YSB8fCBbXTsKICAgICAgICAgICAgICBfY29udGV4dDkubmV4dCA9IDEyOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDc6CiAgICAgICAgICAgICAgX2NvbnRleHQ5LnByZXYgPSA3OwogICAgICAgICAgICAgIF9jb250ZXh0OS50MCA9IF9jb250ZXh0OVsiY2F0Y2giXSgwKTsKICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3lrp7pqozlrqTnvJbnoIHliJfooajlpLHotKU6JywgX2NvbnRleHQ5LnQwKTsKICAgICAgICAgICAgICBfdGhpczM4LmV4cGVyaW1lbnRDb2RlTGlzdCA9IFtdOwogICAgICAgICAgICAgIF90aGlzMzguJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9veWunumqjOWupOe8lueggeWIl+ihqOWksei0pScpOwogICAgICAgICAgICBjYXNlIDEyOgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDkuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU5LCBudWxsLCBbWzAsIDddXSk7CiAgICAgIH0pKSgpOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_engineer<PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "_projectItemOrder", "_vueTreeselect", "_interopRequireDefault", "_customer", "_validate", "_executionAddOrEdit", "name", "components", "Treeselect", "executionAddOrEdit", "data", "loading", "ids", "selectedRows", "single", "multiple", "showSearch", "total", "engineer<PERSON>ampleOrderList", "researchDeptDatas", "dylbOptions", "title", "open", "queryParams", "pageNum", "pageSize", "userId", "nick<PERSON><PERSON>", "sampleOrderCode", "completionStatus", "scheduledDate", "startDate", "actualStartTime", "actualFinishTime", "deptId", "deptIds", "associationStatus", "customerId", "productName", "confirmCode", "isOverdue", "laboratory", "form", "rules", "required", "message", "trigger", "statusOptions", "serviceModeOptions", "date<PERSON><PERSON><PERSON>", "scheduledDateRange", "startDateRange", "actualStartTimeRange", "actualFinishTimeRange", "dataPickerOptions", "shortcuts", "text", "onClick", "picker", "today", "Date", "$emit", "yesterday", "setTime", "getTime", "end", "start", "statusOpen", "dashboardStats", "changeEngineerOpen", "changeEngineerForm", "id", "currentEngineerName", "oldEngineerId", "newEngineerId", "adjustWorkSchedule", "changeEngineerRules", "engineerOptions", "batchManagementOpen", "currentBatch", "historyBatches", "currentBatchExperiments", "selectedOrderForBatch", "currentBatchRow", "batchDetailOpen", "batchDetailData", "addExperimentOpen", "experimentForm", "experimentCode", "experimentNote", "experimentRules", "finishBatchOpen", "finishBatchForm", "qualityEvaluation", "remark", "futureDatePickerOptions", "disabledDate", "time", "now", "engineerSelectType", "searchedEngineers", "unassignedOrders", "unassigned<PERSON>ueryP<PERSON><PERSON>", "unassignedTotal", "isUnassignedPanelCollapsed", "finishTaskOpen", "finishTaskLoading", "finishTaskForm", "laboratoryCode", "experimentCodeList", "finishTaskRules", "currentFinishRow", "updateLaboratoryCodeOpen", "updateLaboratoryCodeLoading", "updateLaboratoryCodeForm", "updateLaboratoryCodeRules", "currentUpdateLaboratoryCodeRow", "exportDialogOpen", "exportOptions", "exportLoading", "readonly", "currentProjectType", "confirmItemCodes", "customerOptions", "itemNames", "rejectDialogOpen", "rejectLoading", "rejectForm", "rejectReason", "overdueOperationOpen", "overdueOperationLoading", "overdueOperationForm", "expectedSampleTime", "reasonForNoSample", "solution", "currentOverdueRow", "rejectRules", "currentRejectRow", "computed", "canEditBatch", "created", "_this", "todayStr", "getFullYear", "String", "getMonth", "padStart", "getDate", "getList", "customerBaseAll", "then", "res", "getDicts", "response", "loadDashboardStats", "getResearchDepartments", "handleTree", "handleUnassignedOrders", "methods", "getProjectLevel", "row", "customerLevel", "projectLevel", "_this2", "params", "_objectSpread2", "default", "addDateRange", "beginDateRange", "endDateRange", "length", "listEngineerSampleOrder", "rows", "cancel", "reset", "serviceMode", "difficultyLevelId", "actualManHours", "estimatedManHours", "standardManHours", "isLocked", "endDate", "checkType", "sampleOrderRemark", "createBy", "createTime", "updateBy", "updateTime", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "handleAdd", "handleUpdate", "_this3", "getEngineerSampleOrder", "submitForm", "_this4", "$refs", "validate", "valid", "updateEngineerSampleOrder", "msgSuccess", "addEngineerSampleOrder", "handleDelete", "_this5", "$confirm", "delEngineerSampleOrder", "catch", "handleExport", "confirmExport", "$message", "warning", "executeExports", "index", "_this6", "success", "concat", "option", "typeName", "doExport", "error", "exportType", "_this7", "exportEngineerSampleOrder", "download", "msg", "doExportSampleOrder", "_this8", "confirmButtonText", "cancelButtonText", "type", "handleStart", "_this9", "updateSampleOrderStatus", "status", "handleDownloadSampleOrder", "_this10", "exportNrwItem", "itemId", "projectId", "handleBatchExportNrw", "_this11", "$modal", "msgError", "exportData", "exportMultipleNrw", "handleFinish", "_this12", "loadExperimentCodeList", "$nextTick", "clearValidate", "confirmFinishTask", "_this13", "Array", "isArray", "join", "handleFinishFromBatch", "_this14", "handleUpdateLaboratoryCode", "_this15", "confirmUpdateLaboratoryCode", "_this16", "handleReject", "_this17", "confirmReject", "_this18", "handleOverdueOperation", "_this19", "confirmOverdueOperation", "_this20", "orderDetail", "_this21", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "orderId", "wrap", "_callee$", "_context", "prev", "next", "projectOrderId", "getExecutionByOrderInfo", "sent", "isNull", "show", "t0", "console", "stop", "handleLockChange", "value", "_this22", "handleTableLockChange", "_this23", "_this24", "getDashboardStats", "handleChangeEngineer", "_this25", "getEngineersByDifficultyLevel", "categoryId", "getResearchDepartmentsUser", "submitChangeEngineer", "_this26", "sampleOrderId", "changeEngineer", "_this27", "handleAssignEngineer", "_this28", "handleDeleteUnassigned", "_this29", "handleRejectUnassigned", "_this30", "handleUnassignedSizeChange", "newSize", "handleUnassignedCurrentChange", "newPage", "toggleUnassignedPanel", "handleStatusFilter", "handleOverdueFilter", "info", "getUrgencyType", "latestStartTime", "latest", "daysToEnd", "Math", "ceil", "getUrgencyText", "handleRefreshUnassigned", "getOverdueInfo", "overdueDays", "handleBatchManagement", "loadBatchData", "engineerSampleOrderId", "_this31", "_callee2", "currentBatchResponse", "batchesResponse", "allBatches", "_callee2$", "_context2", "getCurrentBatch", "getBatchesByOrderId", "filter", "batch", "isCurrentBatch", "loadCurrentBatchExperiments", "batchId", "_this32", "_callee3", "_callee3$", "_context3", "getExperimentsByBatchId", "startNewBatch", "_this33", "_callee4", "_callee4$", "_context4", "abrupt", "finishCurrentBatch", "submitFinishBatch", "_this34", "_callee5", "_callee5$", "_context5", "addExperiment", "submitExperiment", "_this35", "_callee6", "experimentRecord", "_callee6$", "_context6", "addExperimentToBatch", "viewBatchDetail", "_this36", "_callee7", "experiments", "_callee7$", "_context7", "calculateDuration", "startTime", "diffMs", "diffHours", "floor", "diffMinutes", "getQualityEvaluationType", "evaluation", "lowerEval", "toLowerCase", "includes", "closeBatchManagement", "closeBatchDetail", "closeAddExperiment", "closeFinishBatch", "refreshBatchData", "_this37", "_callee8", "_callee8$", "_context8", "_this38", "_callee9", "_callee9$", "_context9", "getBatchExperimentCodeList"], "sources": ["src/views/software/engineerSampleOrder/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 统计概览 -->\r\n    <el-row :gutter=\"20\" class=\"stats-row\" style=\"margin-bottom: 20px;\">\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card total\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-s-order\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">总任务</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.total || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card completed\" @click.native=\"handleStatusFilter('2')\" :class=\"{'active': queryParams.completionStatus === '2'}\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-check\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">已完成</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.completed || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card in-progress\" @click.native=\"handleStatusFilter('1')\" :class=\"{'active': queryParams.completionStatus === '1'}\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-loading\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">进行中</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.inProgress || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card overdue\" @click.native=\"handleOverdueFilter\" :class=\"{'active': queryParams.isOverdue === 1}\" style=\"cursor: pointer;\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-warning\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">逾期任务</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.overdue || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 未分配工程师的打样单告警 -->\r\n    <el-card class=\"alert-card\">\r\n      <div slot=\"header\" class=\"alert-header\">\r\n        <div class=\"alert-title\" @click=\"toggleUnassignedPanel\">\r\n          <span><i class=\"el-icon-warning-outline\"></i>待分配打样单({{ unassignedTotal }}个)</span>\r\n        </div>\r\n        <div class=\"alert-actions\">\r\n          <el-button\r\n            type=\"text\"\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"handleRefreshUnassigned\"\r\n            style=\"margin-right: 10px\"\r\n            title=\"刷新列表\">\r\n            刷新\r\n          </el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            @click=\"toggleUnassignedPanel\"\r\n            style=\"margin-right: 10px\">\r\n            {{ isUnassignedPanelCollapsed ? '展开' : '收起' }}\r\n            <i :class=\"isUnassignedPanelCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\r\n          </el-button>\r\n          <el-pagination\r\n            @size-change=\"handleUnassignedSizeChange\"\r\n            @current-change=\"handleUnassignedCurrentChange\"\r\n            :current-page=\"unassignedQueryParams.pageNum\"\r\n            :page-sizes=\"[8, 16, 24, 32]\"\r\n            :page-size=\"unassignedQueryParams.pageSize\"\r\n            layout=\"total, sizes, prev, pager, next\"\r\n            :total=\"unassignedTotal\">\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n      <el-collapse-transition>\r\n        <div v-show=\"!isUnassignedPanelCollapsed\">\r\n          <el-row :gutter=\"20\" class=\"alert-cards\">\r\n            <el-col :xs=\"24\" :sm=\"12\" :md=\"8\" :lg=\"6\" v-for=\"item in unassignedOrders\" :key=\"item.id\">\r\n              <el-card shadow=\"hover\" class=\"alert-item\">\r\n                <div class=\"alert-item-header\">\r\n                  <div class=\"order-code-section\">\r\n                    <span style=\"color: #00afff;cursor: pointer\"  @click=\"orderDetail(item)\" class=\"order-code\">{{ item.sampleOrderCode }}</span>\r\n                    <el-tag\r\n                      :type=\"getUrgencyType(item.endDate, item.latestStartTime)\"\r\n                      size=\"mini\"\r\n                      class=\"urgency-tag\">\r\n                      {{ getUrgencyText(item.endDate, item.latestStartTime) }}\r\n                    </el-tag>\r\n                  </div>\r\n                </div>\r\n                <div class=\"alert-item-content\">\r\n                  <div class=\"info-section\">\r\n                    <!-- 预估工时和难度等级 -->\r\n                    <div class=\"info-row info-row-double\">\r\n                      <div class=\"info-item date-time-item\" v-if=\"item.latestStartTime\">\r\n                        <i class=\"el-icon-alarm-clock\" style=\"color: #909399;\"></i>\r\n                        <span class=\"info-label\">最晚开始:</span>\r\n                        <span class=\"info-value\">{{ parseTime(item.latestStartTime, '{y}-{m}-{d}') }}</span>\r\n                      </div>\r\n                      <div class=\"info-item standard-item\">\r\n                        <i class=\"el-icon-time\" style=\"color: #409EFF;\"></i>\r\n                        <span class=\"info-label\">预估工时:</span>\r\n                        <span class=\"info-value\">{{ item.estimatedManHours || '-' }}H</span>\r\n                      </div>\r\n                    </div>\r\n                    <!-- 最晚开始日期和截止日期排 -->\r\n                    <div class=\"info-row info-row-double\">\r\n                      <div class=\"info-item date-time-item\">\r\n                        <i class=\"el-icon-date\" style=\"color: #F56C6C;\"></i>\r\n                        <span class=\"info-label\">截止日期:</span>\r\n                        <span class=\"info-value\">{{ parseTime(item.endDate, '{y}-{m}-{d}') }}</span>\r\n                      </div>\r\n                      <div class=\"info-item standard-item\">\r\n                        <i class=\"el-icon-star-on\" style=\"color: #E6A23C;\"></i>\r\n                        <span class=\"info-label\">难度等级:</span>\r\n                        <el-tooltip :content=\"selectDictLabel(dylbOptions, item.difficultyLevelId)\" placement=\"top\">\r\n                          <span class=\"info-value difficulty-text\">{{selectDictLabel(dylbOptions,item.difficultyLevelId).replace(/[^a-zA-Z0-9]/g, '')}}</span>\r\n                        </el-tooltip>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <el-tooltip v-if=\"item.failureReason\" :content=\"item.failureReason\" placement=\"top\">\r\n                    <div class=\"info-reason\">\r\n                      <i class=\"el-icon-warning-outline\"></i>\r\n                      <span class=\"reason-text\">{{ item.failureReason }}</span>\r\n                    </div>\r\n                  </el-tooltip>\r\n                </div>\r\n                <div class=\"alert-item-footer\">\r\n                  <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-user-solid\" @click=\"handleAssignEngineer(item)\" v-hasPermi=\"['software:engineerSampleOrder:changeEngineer']\">\r\n                    分配工程师\r\n                  </el-button>\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-close\" @click=\"handleRejectUnassigned(item)\" v-hasPermi=\"['software:engineerSampleOrder:rejectRask']\" class=\"reject-btn\" style=\"color: #F56C6C;\">\r\n                    驳回\r\n                  </el-button>\r\n                </div>\r\n              </el-card>\r\n           </el-col>\r\n          </el-row>\r\n         </div>\r\n      </el-collapse-transition>\r\n    </el-card>\r\n\r\n    <!-- 筛选条件 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"客户\" prop=\"customerId\">\r\n        <el-select v-model=\"queryParams.customerId\" filterable placeholder=\"客户\" clearable size=\"small\">\r\n          <el-option\r\n            v-for=\"dict in customerOptions\"\r\n            :key=\"dict.id\"\r\n            :label=\"dict.name\"\r\n            :value=\"dict.id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"日期范围\" prop=\"dateRange\" label-width=\"80px\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"\r\n          style=\"width: 240px\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"排单日期\" prop=\"scheduledDate\">\r\n        <el-date-picker\r\n          v-model=\"scheduledDateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"申请日期\" prop=\"startDate\">\r\n        <el-date-picker\r\n          v-model=\"startDateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"研发组别\" prop=\"deptIds\">\r\n        <el-select v-model=\"queryParams.deptIds\" multiple filterable placeholder=\"请选择对应研发组别\">\r\n          <el-option\r\n            v-for=\"dept in researchDeptDatas\"\r\n            :key=\"dept.deptId\"\r\n            :label=\"dept.deptName\"\r\n            :value=\"dept.deptId\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"服务模式\" prop=\"serviceMode\">\r\n        <el-select v-model=\"queryParams.serviceMode\" placeholder=\"请选择客户服务模式\" clearable>\r\n          <el-option\r\n            v-for=\"dict in serviceModeOptions\"\r\n            :key=\"dict.dictValue\"\r\n            :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"实验室\" prop=\"laboratory\">\r\n        <el-select v-model=\"queryParams.laboratory\" placeholder=\"请选择实验室\" clearable>\r\n          <el-option label=\"宜侬\" value=\"0\" />\r\n          <el-option label=\"瀛彩\" value=\"1\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"确认编码\" prop=\"confirmCode\">\r\n        <el-input\r\n          v-model.trim=\"queryParams.confirmCode\"\r\n          placeholder=\"请输入确认编码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"产品名称\" prop=\"productName\">\r\n        <el-input\r\n          v-model.trim=\"queryParams.productName\"\r\n          placeholder=\"请输入产品名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"姓名\" prop=\"nickName\" label-width=\"50px\">\r\n        <el-input v-model.trim=\"queryParams.nickName\" placeholder=\"请输入工程师姓名\" clearable/>\r\n      </el-form-item>\r\n      <el-form-item label=\"打样单编号\" label-width=\"90px\" prop=\"sampleOrderCode\">\r\n        <el-input v-model.trim=\"queryParams.sampleOrderCode\" placeholder=\"请输入打样单编号\" clearable/>\r\n      </el-form-item>\r\n      <el-form-item label=\"完成情况\" prop=\"completionStatus\">\r\n        <el-select v-model=\"queryParams.completionStatus\" placeholder=\"请选择完成情况\" clearable>\r\n          <el-option\r\n            v-for=\"dict in statusOptions\"\r\n            :key=\"dict.dictValue\"\r\n            :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"实际开始时间\" prop=\"actualStartTime\" label-width=\"100px\">\r\n        <el-date-picker\r\n          v-model=\"actualStartTimeRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"实际完成时间\" prop=\"actualFinishTime\" label-width=\"100px\">\r\n        <el-date-picker\r\n          v-model=\"actualFinishTimeRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作按钮 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:add']\">新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" plain icon=\"el-icon-edit\" size=\"mini\" :disabled=\"single\" @click=\"handleUpdate\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:edit']\">修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:remove']\">删除</el-button>\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"doExportSampleOrder\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:export']\">导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" plain icon=\"el-icon-download\" size=\"mini\" :disabled=\"multiple\" @click=\"handleBatchExportNrw\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:export']\">导出打样单任务</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 主要内容区域 -->\r\n     <!-- 工单列表 -->\r\n    <el-table v-loading=\"loading\" :data=\"engineerSampleOrderList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <!-- <el-table-column label=\"主键ID\" align=\"center\" prop=\"id\" /> -->\r\n<!--      <el-table-column label=\"工程师ID\" align=\"center\" prop=\"userId\" />-->\r\n      <el-table-column align=\"center\" prop=\"sampleOrderCode\" width=\"160\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          打样单编号\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              点击编号可查看详细信息\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"orderDetail(scope.row)\">{{ scope.row.sampleOrderCode }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"startDate\" width=\"140\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          申请日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              客户提交打样申请的日期时间\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d} {h}:{i}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"scheduledDate\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          排单日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              工程师被安排处理此打样单的日期\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.scheduledDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"latestStartTime\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          最晚开始日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              必须在此日期前开始工作，否则可能影响交期\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.latestStartTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"endDate\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          最晚截至日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              打样工作必须在此日期前完成\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\" prop=\"completionStatus\">\r\n        <template slot=\"header\">\r\n          完成情况\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              当前打样单的状态\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"scope.row.completionStatus == '0' ? 'info' :\r\n                   scope.row.completionStatus == '1' ? 'primary' :\r\n                   scope.row.completionStatus == '2' ? 'success' : 'info'\"\r\n            size=\"mini\"\r\n          >\r\n            {{selectDictLabel(statusOptions,scope.row.completionStatus)}}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\">\r\n        <template slot=\"header\">\r\n          批次信息\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              显示当前批次进度，格式：当前批次/总批次\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"scope.row.totalBatches > 0\">\r\n            <el-tag size=\"mini\" type=\"primary\">\r\n              {{ scope.row.currentBatch || 0 }}/{{ scope.row.totalBatches || 0 }}\r\n            </el-tag>\r\n          </div>\r\n          <div v-else>\r\n            <el-tag size=\"mini\" type=\"info\">未开始</el-tag>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\">\r\n        <template slot=\"header\">\r\n          逾期情况\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              根据截止日期判断是否逾期及逾期天数\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"getOverdueInfo(scope.row).isOverdue\">\r\n            <el-tag type=\"danger\" size=\"mini\" style=\"margin-bottom: 2px;\">\r\n              逾期 {{ getOverdueInfo(scope.row).overdueDays }}天\r\n            </el-tag>\r\n          </div>\r\n          <el-tag v-else type=\"success\" size=\"mini\">\r\n            正常\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"nickName\" width=\"95\">\r\n        <template slot=\"header\">\r\n          跟进人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此打样单的工程师姓名及职级\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.nickName }}{{ scope.row.rank ? '-' + scope.row.rank : '' }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"assistantName\" width=\"95\">\r\n        <template slot=\"header\">\r\n          协助人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              此打样单难度高于工程师能力范围\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.assistantName }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"isLocked\" width=\"90\">\r\n        <template slot=\"header\">\r\n          锁定状态\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              锁定后不允许更换工程师，防止误操作\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-switch\r\n            v-model=\"scope.row.isLocked\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n            @change=\"(val) => handleTableLockChange(val, scope.row)\">\r\n          </el-switch>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"laboratoryCode\" width=\"150\">\r\n        <template slot=\"header\">\r\n          实验编码\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              实验室分配的唯一编码标识\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"difficultyLevelId\" width=\"80\">\r\n        <template slot=\"header\">\r\n          难度\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              打样工作的技术难度等级\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"selectDictLabel(dylbOptions,scope.row.difficultyLevelId)\" placement=\"top\">\r\n            <span>{{selectDictLabel(dylbOptions,scope.row.difficultyLevelId).replace(/[^a-zA-Z0-9]/g, '')}}</span>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"标准工时\" align=\"center\" prop=\"standardManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.standardManHours\">{{ scope.row.standardManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"laboratory\" width=\"90\">\r\n        <template slot=\"header\">\r\n          实验室\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此打样单的实验室分支\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{\r\n            scope.row.laboratory === '0' ? '宜侬' :\r\n            scope.row.laboratory === '1' ? '瀛彩' :\r\n            ''\r\n          }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"categoryName\" width=\"70\">\r\n        <template slot=\"header\">\r\n          品类\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              产品所属的分类类别\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"SKU数\" align=\"center\" prop=\"sku\" width=\"70\" />\r\n      <el-table-column align=\"center\" prop=\"applicant\" width=\"90\">\r\n        <template slot=\"header\">\r\n          申请人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              提交此打样申请的人员\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"sales\" width=\"70\">\r\n        <template slot=\"header\">\r\n          销售\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此项目的销售人员\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"customerName\" width=\"150\">\r\n        <template slot=\"header\">\r\n          客户名称\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              委托打样的客户公司名称\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"产品/项目等级\" align=\"center\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getProjectLevel(scope.row) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"productName\" width=\"150\">\r\n        <template slot=\"header\">\r\n          产品名称\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              需要打样的产品名称\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"实际工作时间\" align=\"center\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div v-if=\"scope.row.actualStartTime\">开始: {{ parseTime(scope.row.actualStartTime, '{y}-{m}-{d} {h}:{i}') }}</div>\r\n            <div v-if=\"scope.row.actualFinishTime\">完成: {{ parseTime(scope.row.actualFinishTime, '{y}-{m}-{d} {h}:{i}') }}</div>\r\n            <span v-if=\"!scope.row.actualStartTime && !scope.row.actualFinishTime\">-</span>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"实际工时\" align=\"center\" prop=\"actualManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.actualManHours\">{{ scope.row.actualManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"预估工时\" align=\"center\" prop=\"estimatedManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.estimatedManHours\">{{ scope.row.estimatedManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"服务模式\" align=\"center\" prop=\"serviceMode\" width=\"150\" />\r\n      <el-table-column label=\"计算类型\" align=\"center\" prop=\"checkType\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.checkType == 1\">客户等级</span>\r\n          <span v-else>打样单难度</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"完成质量\" align=\"center\" prop=\"qualityEvaluation\" />\r\n<!--      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"140\">-->\r\n<!--        <template slot-scope=\"scope\">-->\r\n<!--          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>-->\r\n<!--        </template>-->\r\n<!--      </el-table-column>-->\r\n      <el-table-column label=\"打样单备注\" align=\"center\" prop=\"sampleOrderRemark\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.sampleOrderRemark\" placement=\"top\" :disabled=\"!scope.row.sampleOrderRemark\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.sampleOrderRemark || '-' }}\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.remark\" placement=\"top\" :disabled=\"!scope.row.remark\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.remark || '-' }}\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"未出样原因\" align=\"center\" prop=\"reasonForNoSample\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.reasonForNoSample\" placement=\"top\" :disabled=\"!scope.row.reasonForNoSample\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.reasonForNoSample || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"解决方案\" align=\"center\" prop=\"solution\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.solution\" placement=\"top\" :disabled=\"!scope.row.solution\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.solution || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"驳回原因\" align=\"center\" prop=\"rejectReason\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.rejectReason\" placement=\"top\" :disabled=\"!scope.row.rejectReason\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.rejectReason || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"fixed-width\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <!-- <el-tooltip content=\"编辑\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:edit']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"删除\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:remove']\" />\r\n          </el-tooltip> -->\r\n          <el-tooltip  v-if=\"scope.row.completionStatus==0\" content=\"更换工程师或更改日期\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-user\" v-if=\"scope.row.isLocked === 0\" @click=\"handleChangeEngineer(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:changeEngineer']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"开始任务\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-video-play\" v-if=\"scope.row.completionStatus == '0'\" @click=\"handleStart(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:startRask']\" />\r\n          </el-tooltip>\r\n\r\n          <el-tooltip content=\"批次管理\" v-if=\"[0,1,2].includes(scope.row.completionStatus)\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-collection\" @click=\"handleBatchManagement(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:batchManagement']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"逾期操作\" v-if=\"getOverdueInfo(scope.row).isOverdue\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-warning\" @click=\"handleOverdueOperation(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:overdueOperation']\" style=\"color: #E6A23C;\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"驳回\" v-if=\"scope.row.completionStatus == 0\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-close\" @click=\"handleReject(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:rejectRask']\" style=\"color: #F56C6C;\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"更新实验室编码\" v-if=\"scope.row.completionStatus == '2' && scope.row.itemStatus == 0\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit-outline\" @click=\"handleUpdateLaboratoryCode(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:editSampleOrderCode']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"下载打样单\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-download\" @click=\"handleDownloadSampleOrder(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:export']\" />\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n      :auto-scroll=\"false\" @pagination=\"getList\" />\r\n\r\n    <!-- 添加或修改工程师打样单关联对话框 -->\r\n    <!-- <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"650px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程师ID\" prop=\"userId\">\r\n              <el-input v-model=\"form.userId\" placeholder=\"请输入工程师ID\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"打样单编号\" prop=\"sampleOrderCode\">\r\n              <el-input v-model=\"form.sampleOrderCode\" placeholder=\"请输入打样单编号\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"难度\" prop=\"difficultyLevelId\">\r\n              <el-select v-model=\"form.difficultyLevelId\" placeholder=\"请选择打样难度等级\">\r\n                  <el-option\r\n                    v-for=\"dict in dylbOptions\"\r\n                    :key=\"dict.dictValue\"\r\n                    :label=\"dict.dictLabel\"\r\n                    :value=\"dict.dictValue\"\r\n                  ></el-option>\r\n                </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"完成情况\" prop=\"completionStatus\">\r\n              <el-select v-model=\"form.completionStatus\" placeholder=\"请选择完成情况\" style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"dict in statusOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排单日期\" prop=\"scheduledDate\">\r\n              <el-date-picker\r\n                v-model=\"form.scheduledDate\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择排单日期\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"最晚截至日期\" prop=\"endDate\">\r\n              <el-date-picker\r\n                v-model=\"form.endDate\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择最晚截至日期\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"锁定状态\" prop=\"isLocked\">\r\n              <el-switch\r\n                v-model=\"form.isLocked\"\r\n                :active-value=\"1\"\r\n                :inactive-value=\"0\"\r\n                active-text=\"已锁定\"\r\n                inactive-text=\"未锁定\"\r\n                @change=\"handleLockChange\">\r\n              </el-switch>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际开始时间\" prop=\"actualStartTime\">\r\n              <el-date-picker\r\n                v-model=\"form.actualStartTime\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择开始时间\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际完成时间\" prop=\"actualFinishTime\">\r\n              <el-date-picker\r\n                v-model=\"form.actualFinishTime\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择完成时间\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际工时\" prop=\"actualManHours\">\r\n              <el-input-number v-model=\"form.actualManHours\" :min=\"0\" :precision=\"1\" :step=\"0.5\" style=\"width: 100%\" placeholder=\"请输入实际工时\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"预估工时\" prop=\"estimatedManHours\">\r\n              <el-input-number v-model=\"form.estimatedManHours\" :min=\"0\" :precision=\"1\" :step=\"0.5\" style=\"width: 100%\" placeholder=\"请输入预估工时\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"完成质量\" prop=\"qualityEvaluation\">\r\n              <el-input v-model=\"form.qualityEvaluation\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入完成质量情况\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"打样单备注\" prop=\"sampleOrderRemark\">\r\n              <el-input v-model=\"form.sampleOrderRemark\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入打样单备注信息\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"备注\" prop=\"remark\">\r\n              <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入备注信息\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog> -->\r\n\r\n    <!-- 更改工程师对话框 -->\r\n    <el-dialog title=\"更改工程师\" :visible.sync=\"changeEngineerOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"changeEngineerForm\" :model=\"changeEngineerForm\" :rules=\"changeEngineerRules\" label-width=\"150px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"changeEngineerForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"当前工程师\">\r\n          <el-input v-model=\"changeEngineerForm.currentEngineerName\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"选择工程师\" prop=\"newEngineerId\">\r\n          <el-radio-group v-model=\"engineerSelectType\" style=\"margin-bottom: 15px;\">\r\n            <el-radio label=\"specified\">指定部门工程师</el-radio>\r\n            <el-radio label=\"other\">其他部门工程师</el-radio>\r\n          </el-radio-group>\r\n\r\n          <!-- 指定工程师选择 -->\r\n          <div v-show=\"engineerSelectType === 'specified'\">\r\n            <el-select\r\n              v-model=\"changeEngineerForm.newEngineerId\"\r\n              placeholder=\"请选择工程师\"\r\n              style=\"width: 100%\"\r\n              filterable>\r\n              <el-option\r\n                v-for=\"engineer in engineerOptions\"\r\n                :key=\"engineer.userId\"\r\n                :label=\"engineer.nickName\"\r\n                :value=\"engineer.userId\"\r\n              />\r\n            </el-select>\r\n          </div>\r\n\r\n          <!-- 其他工程师选择 -->\r\n          <div v-show=\"engineerSelectType === 'other'\" class=\"other-engineer-select\">\r\n            <div class=\"select-item\" style=\"margin-top: 10px;\">\r\n              <el-select\r\n                v-model=\"changeEngineerForm.newEngineerId\"\r\n                placeholder=\"请选择工程师\"\r\n                style=\"width: 100%\"\r\n                filterable\r\n                remote>\r\n                <el-option\r\n                  v-for=\"engineer in searchedEngineers\"\r\n                  :key=\"engineer.userId\"\r\n                  :label=\"engineer.nickName\"\r\n                  :value=\"engineer.userId\"\r\n                />\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"选择指定日期\" prop=\"scheduledDate\">\r\n          <el-date-picker\r\n            v-model=\"changeEngineerForm.scheduledDate\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择日期\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            :picker-options=\"futureDatePickerOptions\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"重新调整工作安排\">\r\n          <el-switch\r\n            v-model=\"changeEngineerForm.adjustWorkSchedule\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n            active-text=\"是\"\r\n            inactive-text=\"否\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitChangeEngineer\">确 定</el-button>\r\n        <el-button @click=\"changeEngineerOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批次管理对话框 -->\r\n    <el-dialog title=\"批次管理\" :visible.sync=\"batchManagementOpen\" width=\"900px\" append-to-body @close=\"closeBatchManagement\">\r\n      <div class=\"batch-management-container\">\r\n        <!-- 状态提示信息 -->\r\n        <el-alert\r\n          v-if=\"!canEditBatch\"\r\n          title=\"当前打样单状态不允许编辑批次信息\"\r\n          type=\"warning\"\r\n          description=\"只有状态为'进行中'的打样单才可以编辑和添加测试批次内容，其他状态只能查看批次信息。\"\r\n          show-icon\r\n          :closable=\"false\"\r\n          style=\"margin-bottom: 20px;\">\r\n        </el-alert>\r\n\r\n        <!-- 当前批次信息 -->\r\n        <el-card class=\"current-batch-card\" v-if=\"currentBatch\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>当前批次 - 第{{ currentBatch.batchIndex }}批次</span>\r\n            <el-button\r\n              v-if=\"canEditBatch\"\r\n              style=\"float: right; padding: 3px 0\"\r\n              type=\"text\"\r\n              @click=\"finishCurrentBatch\">\r\n              结束当前批次\r\n            </el-button>\r\n            <el-tag\r\n              v-else\r\n              style=\"float: right;\"\r\n              type=\"info\"\r\n              size=\"mini\">\r\n              只读模式\r\n            </el-tag>\r\n          </div>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\">\r\n              <div class=\"batch-info-item\">\r\n                <label>开始时间：</label>\r\n                <span>{{ parseTime(currentBatch.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <div class=\"batch-info-item\">\r\n                <label>已用时长：</label>\r\n                <span>{{ calculateDuration(currentBatch.startTime) }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <div class=\"batch-info-item\">\r\n                <label>状态：</label>\r\n                <el-tag type=\"primary\" size=\"mini\">进行中</el-tag>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <!-- 实验记录管理 -->\r\n          <div class=\"experiment-section\" style=\"margin-top: 20px;\">\r\n            <div class=\"section-header\">\r\n              <span>实验记录</span>\r\n              <el-button\r\n                v-if=\"canEditBatch\"\r\n                size=\"mini\"\r\n                type=\"primary\"\r\n                @click=\"addExperiment\">\r\n                添加实验\r\n              </el-button>\r\n              <el-tag\r\n                v-else\r\n                size=\"mini\"\r\n                type=\"info\">\r\n                只读模式\r\n              </el-tag>\r\n            </div>\r\n            <el-table :data=\"currentBatchExperiments\" size=\"mini\" style=\"margin-top: 10px;\">\r\n              <el-table-column prop=\"experimentCode\" label=\"实验编号\" width=\"150\"></el-table-column>\r\n              <el-table-column prop=\"experimentNote\" label=\"实验备注\"></el-table-column>\r\n              <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"150\">\r\n                <template slot-scope=\"scope\">\r\n                  {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </el-card>\r\n\r\n        <!-- 开始新批次按钮 -->\r\n        <div class=\"new-batch-section\" v-if=\"!currentBatch\" style=\"text-align: center; margin: 20px 0;\">\r\n          <el-button\r\n            v-if=\"canEditBatch\"\r\n            type=\"primary\"\r\n            size=\"medium\"\r\n            @click=\"startNewBatch\">\r\n            开始新批次\r\n          </el-button>\r\n          <div v-else style=\"text-align: center; color: #909399;\">\r\n            <i class=\"el-icon-info\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"margin-top: 16px;\">当前状态不允许开始新批次</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 历史批次列表 -->\r\n        <el-card class=\"history-batches-card\" style=\"margin-top: 20px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>历史批次</span>\r\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"refreshBatchData\">刷新</el-button>\r\n          </div>\r\n          <el-table :data=\"historyBatches\" size=\"small\" border stripe>\r\n            <el-table-column prop=\"batchIndex\" label=\"批次序号\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"primary\">第{{ scope.row.batchIndex }}批次</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"startTime\" label=\"开始时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                {{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"endTime\" label=\"结束时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.endTime\">{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}') }}</span>\r\n                <span v-else style=\"color: #909399;\">未结束</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"actualManHours\" label=\"实际工时\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" :type=\"scope.row.actualManHours > 8 ? 'warning' : 'success'\">\r\n                  {{ scope.row.actualManHours || 0 }}小时\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"qualityEvaluation\" label=\"质量评价\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag v-if=\"scope.row.qualityEvaluation\" size=\"mini\"\r\n                        :type=\"getQualityEvaluationType(scope.row.qualityEvaluation)\">\r\n                  {{ scope.row.qualityEvaluation }}\r\n                </el-tag>\r\n                <span v-else style=\"color: #909399;\">未评价</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"remark\" label=\"批次备注\" min-width=\"150\" show-overflow-tooltip>\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.remark\" style=\"color: #606266;\">{{ scope.row.remark }}</span>\r\n                <span v-else style=\"color: #C0C4CC;\">无备注</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"120\" align=\"center\" fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button size=\"mini\" type=\"text\" @click=\"viewBatchDetail(scope.row)\" icon=\"el-icon-view\">\r\n                  查看详情\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n\r\n          <!-- 空状态 -->\r\n          <div v-if=\"!historyBatches || historyBatches.length === 0\" class=\"empty-state\" style=\"text-align: center; padding: 40px;\">\r\n            <i class=\"el-icon-document\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"color: #909399; margin-top: 16px;\">暂无历史批次记录</p>\r\n          </div>\r\n        </el-card>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button\r\n          v-if=\"currentBatchRow && currentBatchRow.completionStatus == '1'\"\r\n          type=\"primary\"\r\n          icon=\"el-icon-check\"\r\n          @click=\"handleFinishFromBatch\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:endRask']\">\r\n          完成任务\r\n        </el-button>\r\n        <el-button @click=\"batchManagementOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批次详情对话框 -->\r\n    <el-dialog title=\"批次详情\" :visible.sync=\"batchDetailOpen\" width=\"800px\" append-to-body @close=\"closeBatchDetail\">\r\n      <div v-if=\"batchDetailData\" class=\"batch-detail-container\">\r\n        <!-- 批次基本信息 -->\r\n        <el-card class=\"batch-info-card\" style=\"margin-bottom: 20px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>批次基本信息</span>\r\n          </div>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>批次序号：</label>\r\n                <el-tag type=\"primary\">第{{ batchDetailData.batchIndex }}批次</el-tag>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>开始时间：</label>\r\n                <span class=\"info-value\">{{ parseTime(batchDetailData.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>结束时间：</label>\r\n                <span class=\"info-value\">\r\n                  {{ batchDetailData.endTime ? parseTime(batchDetailData.endTime, '{y}-{m}-{d} {h}:{i}:{s}') : '未结束' }}\r\n                </span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>实际工时：</label>\r\n                <el-tag :type=\"batchDetailData.actualManHours > 8 ? 'warning' : 'success'\">\r\n                  {{ batchDetailData.actualManHours || 0 }}小时\r\n                </el-tag>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>质量评价：</label>\r\n                <el-tag v-if=\"batchDetailData.qualityEvaluation\"\r\n                        :type=\"getQualityEvaluationType(batchDetailData.qualityEvaluation)\">\r\n                  {{ batchDetailData.qualityEvaluation }}\r\n                </el-tag>\r\n                <span v-else class=\"info-value\">未评价</span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row style=\"margin-top: 15px;\">\r\n            <el-col :span=\"24\">\r\n              <div class=\"info-item\">\r\n                <label>批次备注：</label>\r\n                <div class=\"remark-content\">\r\n                  {{ batchDetailData.remark || '无备注' }}\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n\r\n        <!-- 实验记录详情 -->\r\n        <el-card class=\"experiments-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>实验记录详情</span>\r\n            <el-tag size=\"mini\" style=\"margin-left: 10px;\">\r\n              共{{ batchDetailData.experiments ? batchDetailData.experiments.length : 0 }}条记录\r\n            </el-tag>\r\n          </div>\r\n          <el-table :data=\"batchDetailData.experiments\" size=\"small\" border stripe>\r\n            <el-table-column prop=\"experimentCode\" label=\"实验编号\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span style=\"color: #409EFF; font-weight: bold;\">{{ scope.row.experimentCode }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"experimentNote\" label=\"实验备注\" min-width=\"200\" show-overflow-tooltip>\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.experimentNote\">{{ scope.row.experimentNote }}</span>\r\n                <span v-else style=\"color: #C0C4CC;\">无备注</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"info\">{{ scope.row.createBy || '系统' }}</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n\r\n          <!-- 实验记录空状态 -->\r\n          <div v-if=\"!batchDetailData.experiments || batchDetailData.experiments.length === 0\"\r\n               class=\"empty-experiments\" style=\"text-align: center; padding: 40px;\">\r\n            <i class=\"el-icon-data-line\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"color: #909399; margin-top: 16px;\">该批次暂无实验记录</p>\r\n          </div>\r\n        </el-card>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"batchDetailOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加实验对话框 -->\r\n    <el-dialog title=\"添加实验记录\" :visible.sync=\"addExperimentOpen\" width=\"500px\" append-to-body @close=\"closeAddExperiment\">\r\n      <el-form :model=\"experimentForm\" :rules=\"experimentRules\" ref=\"experimentForm\" label-width=\"100px\">\r\n        <el-form-item label=\"实验编号\" prop=\"experimentCode\">\r\n          <el-input v-model=\"experimentForm.experimentCode\" placeholder=\"请输入实验编号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"实验备注\" prop=\"experimentNote\">\r\n          <el-input v-model=\"experimentForm.experimentNote\" type=\"textarea\" placeholder=\"请输入实验备注\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitExperiment\">确 定</el-button>\r\n        <el-button @click=\"addExperimentOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 结束批次对话框 -->\r\n    <el-dialog title=\"结束当前批次\" :visible.sync=\"finishBatchOpen\" width=\"500px\" append-to-body @close=\"closeFinishBatch\">\r\n      <el-form :model=\"finishBatchForm\" ref=\"finishBatchForm\" label-width=\"100px\">\r\n        <el-form-item label=\"质量评价\">\r\n          <el-input v-model=\"finishBatchForm.qualityEvaluation\" type=\"textarea\" placeholder=\"请输入质量评价\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"finishBatchForm.remark\" type=\"textarea\" placeholder=\"请输入备注\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFinishBatch\">确 定</el-button>\r\n        <el-button @click=\"finishBatchOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 完成任务对话框 -->\r\n    <el-dialog title=\"完成任务\" :visible.sync=\"finishTaskOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"finishTaskForm\" :model=\"finishTaskForm\" :rules=\"finishTaskRules\" label-width=\"120px\">\r\n        <el-form-item label=\"实验室编码\" prop=\"laboratoryCode\">\r\n          <el-select v-model=\"finishTaskForm.laboratoryCode\" multiple filterable placeholder=\"请选择实验室编码\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"item in experimentCodeList\"\r\n              :key=\"item.id\"\r\n              :label=\"item.experimentCode\"\r\n              :value=\"item.experimentCode\">\r\n              <span style=\"float: left\">{{ item.experimentCode }}</span>\r\n              <span style=\"float: right; color: #8492a6; font-size: 13px\" v-if=\"item.experimentNote\">{{ item.experimentNote }}</span>\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"finishTaskForm.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注信息（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"finishTaskOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmFinishTask\" :loading=\"finishTaskLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 更新实验室编码对话框 -->\r\n    <el-dialog title=\"更新实验室编码\" :visible.sync=\"updateLaboratoryCodeOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"updateLaboratoryCodeForm\" :model=\"updateLaboratoryCodeForm\" :rules=\"updateLaboratoryCodeRules\" label-width=\"120px\">\r\n        <el-form-item label=\"实验室编码\" prop=\"laboratoryCode\">\r\n          <el-input v-model=\"updateLaboratoryCodeForm.laboratoryCode\" placeholder=\"请输入实验室编码(多个使用,分割)\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"updateLaboratoryCodeForm.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注信息（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"updateLaboratoryCodeOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmUpdateLaboratoryCode\" :loading=\"updateLaboratoryCodeLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 导出选择对话框 -->\r\n    <el-dialog title=\"选择导出内容\" :visible.sync=\"exportDialogOpen\" width=\"400px\" append-to-body>\r\n      <div class=\"export-options\">\r\n        <p style=\"margin-bottom: 20px; color: #606266;\">请选择要导出的打样单类型：</p>\r\n        <el-checkbox-group v-model=\"exportOptions\" style=\"display: flex; flex-direction: column;\">\r\n          <el-checkbox label=\"assigned\" style=\"margin-bottom: 15px;\">\r\n            <span style=\"font-weight: 500;\">已分配</span>\r\n            <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\r\n              导出已分配给工程师的打样单数据\r\n            </div>\r\n          </el-checkbox>\r\n          <el-checkbox label=\"unassigned\" style=\"margin-bottom: 15px;\">\r\n            <span style=\"font-weight: 500;\">未分配</span>\r\n            <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\r\n              导出尚未分配工程师的打样单数据\r\n            </div>\r\n          </el-checkbox>\r\n        </el-checkbox-group>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"exportDialogOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmExport\" :disabled=\"exportOptions.length === 0\" :loading=\"exportLoading\">\r\n          确认导出\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 驳回对话框 -->\r\n    <el-dialog title=\"驳回打样单\" :visible.sync=\"rejectDialogOpen\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"rejectForm\" :model=\"rejectForm\" :rules=\"rejectRules\" label-width=\"100px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"rejectForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"驳回理由\" prop=\"rejectReason\">\r\n          <el-input v-model=\"rejectForm.rejectReason\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入驳回理由\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"rejectDialogOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmReject\" :loading=\"rejectLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 逾期操作对话框 -->\r\n    <el-dialog title=\"逾期操作\" :visible.sync=\"overdueOperationOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"overdueOperationForm\" :model=\"overdueOperationForm\" label-width=\"120px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"overdueOperationForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"预计出样时间\">\r\n          <el-date-picker\r\n            v-model=\"overdueOperationForm.expectedSampleTime\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择日期\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            :picker-options=\"futureDatePickerOptions\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"未出样原因\">\r\n          <el-input v-model=\"overdueOperationForm.reasonForNoSample\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入未出样原因（非必填）\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"解决方案\">\r\n          <el-input v-model=\"overdueOperationForm.solution\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入解决方案（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"overdueOperationOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmOverdueOperation\" :loading=\"overdueOperationLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n    <!-- 添加或修改项目流程进行对话框 -->\r\n    <executionAddOrEdit ref=\"executionAddOrEdit\"></executionAddOrEdit>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listEngineerSampleOrder,\r\n  getEngineerSampleOrder,\r\n  delEngineerSampleOrder,\r\n  addEngineerSampleOrder,\r\n  updateEngineerSampleOrder,\r\n  updateSampleOrderStatus,\r\n  getDashboardStats,\r\n  getResearchDepartments,\r\n  getEngineersByDifficultyLevel,\r\n  changeEngineer,\r\n  getResearchDepartmentsUser,\r\n  getBatchesByOrderId,\r\n  getCurrentBatch,\r\n  startNewBatch,\r\n  finishCurrentBatch,\r\n  addExperimentToBatch,\r\n  getExperimentsByBatchId,\r\n  exportEngineerSampleOrder,\r\n  getExecutionByOrderInfo,\r\n  getBatchExperimentCodeList\r\n} from \"@/api/software/engineerSampleOrder\";\r\nimport {exportNrwItem, exportMultipleNrw} from \"@/api/project/projectItemOrder\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\nimport {customerBaseAll} from \"@/api/customer/customer\";\r\nimport {isNull} from \"@/utils/validate\";\r\nimport executionAddOrEdit from \"@/components/Project/components/executionAddOrEdit.vue\";\r\n\r\nexport default {\r\n  name: \"EngineerSampleOrder\",\r\n  components: {\r\n     Treeselect,executionAddOrEdit\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 选中的完整行数据\r\n      selectedRows: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 工程师打样单关联表格数据\r\n      engineerSampleOrderList: [],\r\n      // 研发部部门树列表\r\n      researchDeptDatas:[],\r\n      // 打样类别字典\r\n      dylbOptions: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        userId: null,\r\n        nickName: null,\r\n        sampleOrderCode: null,\r\n        completionStatus: null,\r\n        scheduledDate: null,\r\n        startDate: null,\r\n        actualStartTime: null,\r\n        actualFinishTime: null,\r\n        deptId: null,\r\n        deptIds: [],\r\n        associationStatus: null,\r\n        customerId: null,\r\n        productName: null,\r\n        confirmCode: null,\r\n        isOverdue: null,  // 增逾期任务过滤参数\r\n        laboratory: null  // 实验室筛选参数\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        userId: [\r\n          { required: true, message: \"工程师ID不能为空\", trigger: \"blur\" }\r\n        ],\r\n        sampleOrderCode: [\r\n          { required: true, message: \"打样单编号不能为空\", trigger: \"blur\" }\r\n        ],\r\n        completionStatus: [\r\n          { required: true, message: \"完成情况不能为空\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      statusOptions: [],\r\n      serviceModeOptions: [],\r\n      dateRange: [], // 新增的日期范围筛选\r\n      scheduledDateRange: [],\r\n      startDateRange: [],\r\n      actualStartTimeRange: [],\r\n      actualFinishTimeRange: [],\r\n      // 日期选择器配置\r\n      dataPickerOptions: {\r\n        shortcuts: [{\r\n          text: '今天',\r\n          onClick(picker) {\r\n            const today = new Date();\r\n            picker.$emit('pick', [today, today]);\r\n          }\r\n        }, {\r\n          text: '昨天',\r\n          onClick(picker) {\r\n            const yesterday = new Date();\r\n            yesterday.setTime(yesterday.getTime() - 3600 * 1000 * 24);\r\n            picker.$emit('pick', [yesterday, yesterday]);\r\n          }\r\n        }, {\r\n          text: '最近一周',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }, {\r\n          text: '最近一个月',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }]\r\n      },\r\n      // 状态更新对话框\r\n      statusOpen: false,\r\n      dashboardStats: {\r\n        \"total\": 0,\r\n        \"completed\": 0,\r\n        \"inProgress\": 0,\r\n        \"overdue\": 0\r\n      },\r\n      // 更改工程师对话框\r\n      changeEngineerOpen: false,\r\n      // 更改工程师表单\r\n      changeEngineerForm: {\r\n        id: null,\r\n        sampleOrderCode: null,\r\n        currentEngineerName: null,\r\n        oldEngineerId: null,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      },\r\n      // 更改工程师表单校验\r\n      changeEngineerRules: {\r\n        newEngineerId: [\r\n          { required: true, message: \"请选择工程师\", trigger: \"change\" }\r\n        ],\r\n        scheduledDate: [\r\n          { required: true, message: \"请选择日期\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 工程师选项\r\n      engineerOptions: [],\r\n      // 批次管理相关\r\n      batchManagementOpen: false,\r\n      currentBatch: null,\r\n      historyBatches: [],\r\n      currentBatchExperiments: [],\r\n      selectedOrderForBatch: null,\r\n      currentBatchRow: null, // 当前批次管理的行数据\r\n      // 批次详情对话框\r\n      batchDetailOpen: false,\r\n      batchDetailData: null,\r\n      // 添加实验对话框\r\n      addExperimentOpen: false,\r\n      experimentForm: {\r\n        experimentCode: '',\r\n        experimentNote: ''\r\n      },\r\n      experimentRules: {\r\n        experimentCode: [\r\n          { required: true, message: \"实验编号不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      // 结束批次对话框\r\n      finishBatchOpen: false,\r\n      finishBatchForm: {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      },\r\n      // 未来日期选择器配置\r\n      futureDatePickerOptions: {\r\n        disabledDate(time) {\r\n          return time.getTime() < Date.now() - 8.64e7; // 禁用今天之前的日期\r\n        }\r\n      },\r\n      engineerSelectType: 'specified',\r\n      searchedEngineers: [],\r\n      // 未分配打样单告警\r\n      unassignedOrders: [],\r\n      unassignedQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 8\r\n      },\r\n      unassignedTotal: 0,\r\n      // 未分配面板折叠状态\r\n      isUnassignedPanelCollapsed: true,\r\n      // 完成任务对话框\r\n      finishTaskOpen: false,\r\n      finishTaskLoading: false,\r\n      finishTaskForm: {\r\n        laboratoryCode: '',\r\n        remark: ''\r\n      },\r\n      experimentCodeList: [], // 实验室编码列表\r\n      finishTaskRules: {\r\n        laboratoryCode: [\r\n          { required: true, message: \"实验室编码不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentFinishRow: null,\r\n      // 更新实验室编码对话框\r\n      updateLaboratoryCodeOpen: false,\r\n      updateLaboratoryCodeLoading: false,\r\n      updateLaboratoryCodeForm: {\r\n        laboratoryCode: '',\r\n        remark: ''\r\n      },\r\n      updateLaboratoryCodeRules: {\r\n        laboratoryCode: [\r\n          { required: true, message: \"实验室编码不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentUpdateLaboratoryCodeRow: null,\r\n      // 导出选择对话框\r\n      exportDialogOpen: false,\r\n      exportOptions: [],\r\n      exportLoading: false,\r\n      readonly: true,\r\n      // 项目详情相关\r\n      currentProjectType: null,\r\n      confirmItemCodes: [],\r\n      customerOptions: [],\r\n      itemNames: [],\r\n      // 驳回对话框\r\n      rejectDialogOpen: false,\r\n      rejectLoading: false,\r\n      rejectForm: {\r\n        sampleOrderCode: '',\r\n        rejectReason: ''\r\n      },\r\n      // 逾期操作对话框\r\n      overdueOperationOpen: false,\r\n      overdueOperationLoading: false,\r\n      overdueOperationForm: {\r\n        sampleOrderCode: '',\r\n        expectedSampleTime: '',\r\n        reasonForNoSample: '',\r\n        solution: ''\r\n      },\r\n      currentOverdueRow: null,\r\n      rejectRules: {\r\n        rejectReason: [\r\n          { required: true, message: \"驳回理由不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentRejectRow: null\r\n    };\r\n  },\r\n  computed: {\r\n    /** 计算是否可以编辑批次 - 只有状态为'1'(进行中)的打样单才可以编辑 */\r\n    canEditBatch() {\r\n      return this.selectedOrderForBatch && this.selectedOrderForBatch.completionStatus === 1;\r\n    }\r\n  },\r\n  created() {\r\n    // 设置默认日期范围为当天\r\n    const today = new Date();\r\n    const todayStr = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');\r\n    this.dateRange = [todayStr, todayStr];\r\n\r\n    this.getList();\r\n    customerBaseAll().then(res=> this.customerOptions = res)\r\n\r\n    this.getDicts(\"DYD_GCSZT\").then(response => {\r\n        this.statusOptions = response.data;\r\n    });\r\n    this.getDicts(\"CUSTOMER_SERVICE_MODE\").then(response => {\r\n      this.serviceModeOptions = response.data;\r\n    });\r\n    this.getDicts(\"project_nrw_dylb\").then(response => {\r\n      this.dylbOptions = response.data;\r\n    });\r\n    this.loadDashboardStats();\r\n    // 获取研发部部门列表\r\n    getResearchDepartments().then(response => {\r\n      this.researchDeptDatas = this.handleTree(response.data, \"deptId\");\r\n    });\r\n    // 获取未分配打样单\r\n    this.handleUnassignedOrders();\r\n  },\r\n  methods: {\r\n    /** 计算产品/项目等级 */\r\n    getProjectLevel(row) {\r\n      const customerLevel = row.customerLevel || '';\r\n      const projectLevel = row.projectLevel || '';\r\n\r\n      // 如果 projectLevel 是空字符串或 \"/\" 就不相加\r\n      if (projectLevel === '' || projectLevel === '/') {\r\n        return customerLevel;\r\n      }\r\n\r\n      return customerLevel + projectLevel;\r\n    },\r\n    /** 查询工程师打样单关联列表 */\r\n    getList() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      this.loading = true;\r\n      params.associationStatus = 1\r\n      listEngineerSampleOrder(params).then(response => {\r\n        this.engineerSampleOrderList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        userId: null,\r\n        sampleOrderCode: null,\r\n        serviceMode: null,\r\n        difficultyLevelId: null,\r\n        completionStatus: null,\r\n        scheduledDate: null,\r\n        actualStartTime: null,\r\n        actualFinishTime: null,\r\n        actualManHours: null,\r\n        estimatedManHours: null,\r\n        standardManHours: null,\r\n        qualityEvaluation: null,\r\n        isLocked: 0,\r\n        startDate: null,\r\n        endDate: null,\r\n        checkType: null,\r\n        sampleOrderRemark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.unassignedQueryParams.pageNum = 1;\r\n      this.getList();\r\n      this.loadDashboardStats();\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.dateRange = [] // 重置日期范围\r\n      this.scheduledDateRange = []\r\n      this.startDateRange = []\r\n      this.actualStartTimeRange = []\r\n      this.actualFinishTimeRange = []\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.selectedRows = selection\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加工程师打样单关联\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids\r\n      getEngineerSampleOrder(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改工程师打样单关联\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateEngineerSampleOrder(this.form).then(response => {\r\n              this.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n              this.loadDashboardStats();\r\n            });\r\n          } else {\r\n            addEngineerSampleOrder(this.form).then(response => {\r\n              this.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n              this.loadDashboardStats();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$confirm('是否确认删除工程师打样单关联编号为\"' + row.sampleOrderCode + '\"的数据项？').then(function () {\r\n        return delEngineerSampleOrder(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.msgSuccess(\"删除成功\");\r\n        this.loadDashboardStats();\r\n      }).catch(() => { });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 重置导出选项并显示选择对话框\r\n      this.exportOptions = [];\r\n      this.exportDialogOpen = true;\r\n    },\r\n    /** 确认导出操作 */\r\n    confirmExport() {\r\n      if (this.exportOptions.length === 0) {\r\n        this.$message.warning('请至少选择一种导出类型');\r\n        return;\r\n      }\r\n      this.exportLoading = true;\r\n      this.executeExports(0);\r\n    },\r\n\r\n    /** 串行执行导出操作 */\r\n    executeExports(index) {\r\n      if (index >= this.exportOptions.length) {\r\n        // 所有导出完成\r\n        this.exportLoading = false;\r\n        this.exportDialogOpen = false;\r\n        this.$message.success(`导出完成，共导出${this.exportOptions.length}个文件`);\r\n        return;\r\n      }\r\n\r\n      const option = this.exportOptions[index];\r\n      const associationStatus = option === 'assigned' ? 1 : 0;\r\n      const typeName = option === 'assigned' ? '已分配' : '未分配';\r\n\r\n      this.doExport(associationStatus, option).then(() => {\r\n        this.$message.success(`${typeName}打样单导出成功`);\r\n        // 继续导出下一个\r\n        this.executeExports(index + 1);\r\n      }).catch((error) => {\r\n        this.exportLoading = false;\r\n        this.$message.error(`${typeName}打样单导出失败: ${error.message || error}`);\r\n      });\r\n    },\r\n    /** 执行导出操作 */\r\n    doExport(associationStatus, exportType) {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      params.associationStatus = associationStatus;\r\n      params.exportType = exportType;\r\n      return exportEngineerSampleOrder(params).then(response => {\r\n        this.download(response.msg);\r\n      });\r\n    },\r\n    /** 执行导出打样单（已分配/未分配） */\r\n    doExportSampleOrder() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      this.$confirm('是否确认导出所有（已分配/未分配）打样单关联数据？', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportEngineerSampleOrder(params);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n        this.$message.success(\"导出成功\");\r\n      }).catch(() => {\r\n        this.exportLoading = false;\r\n      });\r\n    },\r\n    /** 点击\"开始\"按钮操作 */\r\n    handleStart(row) {\r\n      this.$confirm('确定要将打样单：' + row.sampleOrderCode + '标记为进行中吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        updateSampleOrderStatus({\r\n          id: row.id,\r\n          status: '1'\r\n        }).then(response => {\r\n          this.msgSuccess('打样单已设置为已开始');\r\n          this.getList();\r\n          this.loadDashboardStats();\r\n        });\r\n      });\r\n    },\r\n    /** 下载打样单操作 */\r\n    handleDownloadSampleOrder(row) {\r\n      this.$confirm('是否确认导出打样单?', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportNrwItem({itemId: row.itemId,projectId: row.projectId})\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n      }).catch(() => {});\r\n    },\r\n    /** 批量导出打样单任务操作 */\r\n    handleBatchExportNrw() {\r\n      if (this.selectedRows.length === 0) {\r\n        this.$modal.msgError(\"请选择要导出的打样单任务\");\r\n        return;\r\n      }\r\n\r\n      // 构造批量导出数据，传递itemId和projectId\r\n      const exportData = this.selectedRows.map(row => ({\r\n        itemId: row.itemId,\r\n        projectId: row.projectId\r\n      }));\r\n\r\n      this.$confirm('是否确认导出所选打样单任务的内容物数据？', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportMultipleNrw(exportData);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n        this.$message.success(\"导出成功\");\r\n      }).catch(() => {\r\n        this.exportLoading = false;\r\n      });\r\n    },\r\n    /** 点击\"完成\"按钮操作 */\r\n    handleFinish(row) {\r\n      this.currentFinishRow = row;\r\n      this.finishTaskForm.laboratoryCode = '';\r\n      this.finishTaskForm.remark = '';\r\n      this.finishTaskOpen = true;\r\n      // 加载实验室编码列表\r\n      this.loadExperimentCodeList(row.id);\r\n      this.$nextTick(() => {\r\n        this.$refs.finishTaskForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认完成任务 */\r\n    confirmFinishTask() {\r\n      this.$refs.finishTaskForm.validate(valid => {\r\n        // 处理实验室编号字段\r\n        let laboratoryCode = this.finishTaskForm.laboratoryCode;\r\n\r\n        // 如果是数组类型，使用逗号拼接\r\n        if (Array.isArray(laboratoryCode)) {\r\n          laboratoryCode = laboratoryCode.join(\",\");\r\n        }\r\n        if (valid) {\r\n          this.finishTaskLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentFinishRow.id,\r\n            status: '2',\r\n            laboratoryCode: laboratoryCode,\r\n            remark: this.finishTaskForm.remark\r\n          }).then(response => {\r\n            this.finishTaskLoading = false;\r\n            this.finishTaskOpen = false;\r\n            this.msgSuccess('打样单已设置为已完成');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.finishTaskLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 从批次管理对话框完成任务 */\r\n    handleFinishFromBatch() {\r\n      this.currentFinishRow = this.currentBatchRow;\r\n      this.finishTaskForm.laboratoryCode = '';\r\n      this.finishTaskForm.remark = '';\r\n      this.finishTaskOpen = true;\r\n      // 加载实验室编码列表\r\n      this.loadExperimentCodeList(this.currentBatchRow.id);\r\n      this.$nextTick(() => {\r\n        this.$refs.finishTaskForm.clearValidate();\r\n      });\r\n    },\r\n    /** 点击\"更新实验室编码\"按钮操作 */\r\n    handleUpdateLaboratoryCode(row) {\r\n      this.currentUpdateLaboratoryCodeRow = row;\r\n      // 回显当前的实验室编码和备注\r\n      this.updateLaboratoryCodeForm.laboratoryCode = row.laboratoryCode || '';\r\n      this.updateLaboratoryCodeForm.remark = row.remark || '';\r\n      this.updateLaboratoryCodeOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.updateLaboratoryCodeForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认更新实验室编码 */\r\n    confirmUpdateLaboratoryCode() {\r\n      this.$refs.updateLaboratoryCodeForm.validate(valid => {\r\n        if (valid) {\r\n          this.updateLaboratoryCodeLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentUpdateLaboratoryCodeRow.id,\r\n            status: this.currentUpdateLaboratoryCodeRow.completionStatus, // 保持当前状态\r\n            laboratoryCode: this.updateLaboratoryCodeForm.laboratoryCode,\r\n            remark: this.updateLaboratoryCodeForm.remark\r\n          }).then(response => {\r\n            this.updateLaboratoryCodeLoading = false;\r\n            this.updateLaboratoryCodeOpen = false;\r\n            this.msgSuccess('实验室编码更新成功');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.updateLaboratoryCodeLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 点击\"驳回\"按钮操作 */\r\n    handleReject(row) {\r\n      this.currentRejectRow = row;\r\n      this.rejectForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.rejectForm.rejectReason = '';\r\n      this.rejectDialogOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.rejectForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认驳回 */\r\n    confirmReject() {\r\n      this.$refs.rejectForm.validate(valid => {\r\n        if (valid) {\r\n          this.rejectLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentRejectRow.id,\r\n            status: '3',\r\n            rejectReason: this.rejectForm.rejectReason\r\n          }).then(response => {\r\n            this.rejectLoading = false;\r\n            this.rejectDialogOpen = false;\r\n            this.msgSuccess('打样单已驳回');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n            // 如果是未分配打样单的驳回，也需要刷新未分配列表\r\n            this.handleUnassignedOrders();\r\n          }).catch(() => {\r\n            this.rejectLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 点击\"逾期操作\"按钮操作 */\r\n    handleOverdueOperation(row) {\r\n      this.currentOverdueRow = row;\r\n      this.overdueOperationForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.overdueOperationForm.expectedSampleTime = row.expectedSampleTime;\r\n      this.overdueOperationForm.reasonForNoSample = row.reasonForNoSample;\r\n      this.overdueOperationForm.solution = row.solution;\r\n      this.overdueOperationOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.overdueOperationForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认逾期操作 */\r\n    confirmOverdueOperation() {\r\n      this.$refs.overdueOperationForm.validate(valid => {\r\n        if (valid) {\r\n          this.overdueOperationLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentOverdueRow.id,\r\n            status: \"11\", // 特殊处理，只更新“逾期情况”填写的字段\r\n            expectedSampleTime: this.overdueOperationForm.expectedSampleTime,\r\n            reasonForNoSample: this.overdueOperationForm.reasonForNoSample,\r\n            solution: this.overdueOperationForm.solution\r\n          }).then(response => {\r\n            this.overdueOperationLoading = false;\r\n            this.overdueOperationOpen = false;\r\n            this.msgSuccess('逾期操作已完成');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.overdueOperationLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 查看打样单详情 */\r\n    async orderDetail(row) {\r\n      try {\r\n        //获取执行ID\r\n        const orderId = row.projectOrderId;\r\n        let data = await getExecutionByOrderInfo(orderId);\r\n        if(data!=null && !isNull(data.id)){\r\n            let id = data.id;\r\n            this.$refs.executionAddOrEdit.open = true;\r\n            await this.$nextTick()\r\n            this.$refs.executionAddOrEdit.reset()\r\n            this.$refs.executionAddOrEdit.show(id, 1)\r\n        }else{\r\n          this.$message.error('获取数据失败');\r\n        }\r\n      } catch (error) {\r\n        console.error('查看项目详情失败:', error);\r\n        this.$message.error('查看项目详情失败: ' + (error.message || '未知错误'));\r\n      }\r\n    },\r\n\r\n    /** 锁定状态改变处理 */\r\n    handleLockChange(value) {\r\n      if (value === 1) {\r\n        this.$confirm('锁定后将无法自动调整此单的排单日期，是否确认锁定？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.form.isLocked = 1;\r\n        }).catch(() => {\r\n          this.form.isLocked = 0;\r\n        });\r\n      }\r\n    },\r\n    /** 表格中锁定状态改变处理 */\r\n    handleTableLockChange(value, row) {\r\n      if (value === 1) {\r\n        this.$confirm('锁定后将无法自动调整此单的排单日期，是否确认锁定？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          // 调用更新接口\r\n          updateEngineerSampleOrder({\r\n            id: row.id,\r\n            isLocked: 1\r\n          }).then(response => {\r\n            this.msgSuccess(\"锁定成功\");\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          });\r\n        }).catch(() => {\r\n          // 取消操作，恢复原值\r\n          row.isLocked = 0;\r\n        });\r\n      } else {\r\n        // 解锁操作\r\n        updateEngineerSampleOrder({\r\n          id: row.id,\r\n          isLocked: 0\r\n        }).then(response => {\r\n          this.msgSuccess(\"解锁成功\");\r\n          this.getList();\r\n          this.loadDashboardStats();\r\n        });\r\n      }\r\n    },\r\n    /** 加载统计概览数据 */\r\n    loadDashboardStats() {\r\n      let params = { ...this.queryParams };\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      getDashboardStats(params).then(response => {\r\n        this.dashboardStats = response.data || {};\r\n      });\r\n    },\r\n    /** 更改工程师按钮操作 */\r\n    handleChangeEngineer(row) {\r\n      this.changeEngineerForm = {\r\n        id: row.id,\r\n        sampleOrderCode: row.sampleOrderCode,\r\n        currentEngineerName: row.nickName,\r\n        oldEngineerId: row.userId,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      };\r\n\r\n      // 获取可用工程师列表\r\n      getEngineersByDifficultyLevel({\r\n        difficultyLevelId: row.difficultyLevelId,\r\n        categoryId: row.categoryId\r\n      }).then(response => {\r\n        this.engineerOptions = response.data || [];\r\n      });\r\n      // 获取研发所有工程师列表\r\n      getResearchDepartmentsUser().then(response => {\r\n        this.searchedEngineers = response.data || [];\r\n      });\r\n      this.changeEngineerOpen = true;\r\n    },\r\n    /** 提交更改工程师 */\r\n    submitChangeEngineer() {\r\n      this.$refs[\"changeEngineerForm\"].validate(valid => {\r\n        if (valid) {\r\n          const data = {\r\n            sampleOrderId: this.changeEngineerForm.id,\r\n            oldEngineerId: this.changeEngineerForm.oldEngineerId,\r\n            newEngineerId: this.changeEngineerForm.newEngineerId,\r\n            scheduledDate: this.changeEngineerForm.scheduledDate,\r\n            adjustWorkSchedule: this.changeEngineerForm.adjustWorkSchedule\r\n          };\r\n\r\n          // 调用更改工程师的API接口\r\n          changeEngineer(data).then(response => {\r\n            this.msgSuccess(\"更改工程师成功\");\r\n            this.changeEngineerOpen = false;\r\n            this.getList();\r\n            // 获取未分配打样单\r\n            this.handleUnassignedOrders();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.msgError(\"更改工程师失败\");\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 获取未分配打样单列表 */\r\n    handleUnassignedOrders() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      params.associationStatus = 0\r\n      params.pageNum = this.unassignedQueryParams.pageNum;\r\n      params.pageSize = this.unassignedQueryParams.pageSize;\r\n      listEngineerSampleOrder(params).then(response => {\r\n        this.unassignedOrders = response.rows;\r\n        this.unassignedTotal = response.total;\r\n      });\r\n    },\r\n    /** 分配工程师操作 */\r\n    handleAssignEngineer(row) {\r\n      this.changeEngineerForm = {\r\n        id: row.id,\r\n        sampleOrderCode: row.sampleOrderCode,\r\n        currentEngineerName: '',\r\n        oldEngineerId: null,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      };\r\n\r\n      // 获取可用工程师列表\r\n      getEngineersByDifficultyLevel({\r\n        difficultyLevelId: row.difficultyLevelId,\r\n        categoryId: row.categoryId\r\n      }).then(response => {\r\n        this.engineerOptions = response.data || [];\r\n      });\r\n      // 获取研发所有工程师列表\r\n      getResearchDepartmentsUser().then(response => {\r\n        this.searchedEngineers = response.data || [];\r\n      });\r\n      this.changeEngineerOpen = true;\r\n    },\r\n    /** 删除未分配打样单 */\r\n    handleDeleteUnassigned(row) {\r\n      this.$confirm('是否确认删除打样单编号为\"' + row.sampleOrderCode + '\"的数据项？').then(function() {\r\n        return delEngineerSampleOrder(row.id);\r\n      }).then(() => {\r\n        this.handleUnassignedOrders();\r\n        this.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 驳回未分配打样单 */\r\n    handleRejectUnassigned(row) {\r\n      this.currentRejectRow = row;\r\n      this.rejectForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.rejectForm.rejectReason = '';\r\n      this.rejectDialogOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.rejectForm.clearValidate();\r\n      });\r\n    },\r\n    /** 未分配打样单分页大小改变 */\r\n    handleUnassignedSizeChange(newSize) {\r\n      this.unassignedQueryParams.pageSize = newSize;\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 未分配打样单页码改变 */\r\n    handleUnassignedCurrentChange(newPage) {\r\n      this.unassignedQueryParams.pageNum = newPage;\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 切换未分配面板显示状态 */\r\n    toggleUnassignedPanel() {\r\n      this.isUnassignedPanelCollapsed = !this.isUnassignedPanelCollapsed;\r\n    },\r\n    /** 处理状态过滤 */\r\n    handleStatusFilter(status) {\r\n      // 清除逾期过滤\r\n      this.queryParams.isOverdue = null;\r\n      this.queryParams.completionStatus = status;\r\n      this.handleQuery();\r\n    },\r\n\r\n    /** 处理逾期任务过滤 */\r\n    handleOverdueFilter() {\r\n      // 清除完成状态过滤\r\n      this.queryParams.completionStatus = null;\r\n      // 设置逾期过滤\r\n      this.queryParams.isOverdue = this.queryParams.isOverdue === 1 ? null : 1;\r\n      this.handleQuery();\r\n\r\n      if (this.queryParams.isOverdue === 1) {\r\n        this.$message.info('已筛选显示逾期任务');\r\n      } else {\r\n        this.$message.info('已清除逾期任务筛选');\r\n      }\r\n    },\r\n    /** 获取紧急程度类型 */\r\n    getUrgencyType(endDate, latestStartTime) {\r\n      const now = new Date();\r\n      const end = new Date(endDate);\r\n      const latest = latestStartTime ? new Date(latestStartTime) : null;\r\n\r\n      // 计算距离截止日期的天数\r\n      const daysToEnd = Math.ceil((end - now) / (1000 * 60 * 60 * 24));\r\n\r\n      // 如果有最晚开始时间，检查是否已经超过\r\n      if (latest && now > latest) {\r\n        return 'danger'; // 已超过最晚开始时间\r\n      }\r\n\r\n      if (daysToEnd <= 1) {\r\n        return 'danger'; // 紧急：1天内截止\r\n      } else if (daysToEnd <= 3) {\r\n        return 'warning'; // 警告：3天内截止\r\n      } else if (daysToEnd <= 7) {\r\n        return 'primary'; // 一般：7天内截止\r\n      } else {\r\n        return 'success'; // 充足时间\r\n      }\r\n    },\r\n    /** 获取紧急程度文本 */\r\n    getUrgencyText(endDate, latestStartTime) {\r\n      const now = new Date();\r\n      const end = new Date(endDate);\r\n      const latest = latestStartTime ? new Date(latestStartTime) : null;\r\n      // 计算距离截止日期的天数\r\n      const daysToEnd = Math.ceil((end - now) / (1000 * 60 * 60 * 24));\r\n      // 如果有最晚开始时间，检查是否已经超过\r\n      if (latest && now > latest) {\r\n        return '超期未开始';\r\n      }\r\n\r\n      if (daysToEnd <= 0) {\r\n        return '已逾期';\r\n      } else if (daysToEnd <= 1) {\r\n        return '紧急';\r\n      } else if (daysToEnd <= 3) {\r\n        return '较急';\r\n      } else if (daysToEnd <= 7) {\r\n        return '一般';\r\n      } else {\r\n        return '充足';\r\n      }\r\n    },\r\n    /** 刷新未分配打样单列表 */\r\n    handleRefreshUnassigned() {\r\n      this.handleUnassignedOrders();\r\n      this.$message.success('刷新成功');\r\n    },\r\n    /** 获取逾期信息 */\r\n    getOverdueInfo(row) {\r\n      const now = new Date();\r\n      const endDate = new Date(row.endDate);\r\n      let isOverdue = false;\r\n      let overdueDays = 0;\r\n      // 根据后台SQL逻辑判断逾期\r\n      if (row.completionStatus === 2) {\r\n        // 已完成且逾期：实际完成时间 > 截止日期\r\n        if (row.actualFinishTime) {\r\n          const actualFinishTime = new Date(row.actualFinishTime);\r\n          if (actualFinishTime > endDate) {\r\n            isOverdue = true;\r\n            overdueDays = Math.ceil((actualFinishTime - endDate) / (1000 * 60 * 60 * 24));\r\n          }\r\n        }\r\n      } else if (row.completionStatus === 1) {\r\n        // 进行中且逾期：当前时间 > 截止日期\r\n        if (now > endDate) {\r\n          isOverdue = true;\r\n          overdueDays = Math.ceil((now - endDate) / (1000 * 60 * 60 * 24));\r\n        }\r\n      } else if (row.completionStatus === 0) {\r\n        // 未开始且逾期：未开始但当前时间 > 开始日期\r\n        if (now > endDate) {\r\n          isOverdue = true;\r\n          overdueDays = Math.ceil((now - endDate) / (1000 * 60 * 60 * 24));\r\n        }\r\n      }\r\n\r\n      return {\r\n        isOverdue,\r\n        overdueDays\r\n      };\r\n    },\r\n\r\n    // ==================== 批次管理相关方法 ====================\r\n\r\n    /** 打开批次管理对话框 */\r\n    handleBatchManagement(row) {\r\n      this.selectedOrderForBatch = row;\r\n      this.currentBatchRow = row; // 保存当前行数据\r\n      this.batchManagementOpen = true;\r\n      this.loadBatchData(row.id);\r\n    },\r\n\r\n    /** 加载批次数据 */\r\n    async loadBatchData(engineerSampleOrderId) {\r\n      try {\r\n        // 加载当前批次\r\n        const currentBatchResponse = await getCurrentBatch(engineerSampleOrderId);\r\n        this.currentBatch = currentBatchResponse.data;\r\n\r\n        // 加载所有批次\r\n        const batchesResponse = await getBatchesByOrderId(engineerSampleOrderId);\r\n        const allBatches = batchesResponse.data || [];\r\n\r\n        // 分离当前批次和历史批次，并为每个历史批次加载实验数量\r\n        this.historyBatches = allBatches.filter(batch => batch.isCurrentBatch === 0);\r\n\r\n        // // 为每个历史批次加载实验数量\r\n        // for (let batch of this.historyBatches) {\r\n        //   try {\r\n        //     const experimentsResponse = await getExperimentsByBatchId(batch.id);\r\n        //     batch.experimentCount = experimentsResponse.data ? experimentsResponse.data.length : 0;\r\n        //   } catch (error) {\r\n        //     console.warn(`加载批次${batch.id}的实验数量失败:`, error);\r\n        //     batch.experimentCount = 0;\r\n        //   }\r\n        // }\r\n\r\n        // 如果有当前批次，加载实验记录\r\n        if (this.currentBatch) {\r\n          await this.loadCurrentBatchExperiments(this.currentBatch.id);\r\n        }\r\n      } catch (error) {\r\n        console.error('加载批次数据失败:', error);\r\n        this.$message.error('加载批次数据失败');\r\n      }\r\n    },\r\n\r\n    /** 加载当前批次的实验记录 */\r\n    async loadCurrentBatchExperiments(batchId) {\r\n      try {\r\n        const response = await getExperimentsByBatchId(batchId);\r\n        this.currentBatchExperiments = response.data || [];\r\n      } catch (error) {\r\n        console.error('加载实验记录失败:', error);\r\n      }\r\n    },\r\n\r\n    /** 开始新批次 */\r\n    async startNewBatch() {\r\n      // 检查编辑权限\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许开始新批次，只有状态为\"进行中\"的打样单才可以编辑批次信息');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await this.$confirm('确认开始新的打样批次？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        });\r\n\r\n        const response = await startNewBatch(this.selectedOrderForBatch.id, '');\r\n        this.$message.success('新批次开始成功');\r\n\r\n        // 重新加载批次数据\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n\r\n        // 刷新主列表\r\n        this.getList();\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('开始新批次失败: ' + (error.message || error));\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 结束当前批次 */\r\n    finishCurrentBatch() {\r\n      // 检查编辑权限\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许结束批次，只有状态为\"进行中\"的打样单才可以编辑批次信息');\r\n        return;\r\n      }\r\n\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n      this.finishBatchOpen = true;\r\n    },\r\n\r\n    /** 提交结束批次 */\r\n    async submitFinishBatch() {\r\n      try {\r\n        await finishCurrentBatch(\r\n          this.selectedOrderForBatch.id,\r\n          this.finishBatchForm.qualityEvaluation,\r\n          this.finishBatchForm.remark\r\n        );\r\n\r\n        this.$message.success('批次结束成功');\r\n        this.finishBatchOpen = false;\r\n\r\n        // 重新加载批次数据\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n\r\n        // 刷新主列表\r\n        this.getList();\r\n      } catch (error) {\r\n        console.error('结束批次失败:', error);\r\n        this.$message.error('结束批次失败: ' + (error.message || error));\r\n      }\r\n    },\r\n\r\n    /** 添加实验记录 */\r\n    addExperiment() {\r\n      // 检查编辑权限\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许添加实验记录，只有状态为\"进行中\"的打样单才可以编辑批次信息');\r\n        return;\r\n      }\r\n\r\n      this.experimentForm = {\r\n        experimentCode: '',\r\n        experimentNote: ''\r\n      };\r\n      this.addExperimentOpen = true;\r\n    },\r\n\r\n    /** 提交实验记录 */\r\n    async submitExperiment() {\r\n      // 检查编辑权限\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许添加实验记录，只有状态为\"进行中\"的打样单才可以编辑批次信息');\r\n        return;\r\n      }\r\n\r\n      await this.$refs.experimentForm.validate();\r\n\r\n      const experimentRecord = {\r\n        batchId: this.currentBatch.id,\r\n        experimentCode: this.experimentForm.experimentCode,\r\n        experimentNote: this.experimentForm.experimentNote || ''\r\n      };\r\n\r\n      await addExperimentToBatch(experimentRecord);\r\n\r\n      this.$message.success('实验记录添加成功');\r\n      this.addExperimentOpen = false;\r\n\r\n      // 重新加载当前批次的实验记录\r\n      await this.loadCurrentBatchExperiments(this.currentBatch.id);\r\n    },\r\n\r\n    /** 查看批次详情 */\r\n    async viewBatchDetail(batch) {\r\n      try {\r\n        const response = await getExperimentsByBatchId(batch.id);\r\n        const experiments = response.data || [];\r\n\r\n        // 设置批次详情数据\r\n        this.batchDetailData = {\r\n          ...batch,\r\n          experiments: experiments\r\n        };\r\n\r\n        this.batchDetailOpen = true;\r\n      } catch (error) {\r\n        console.error('查看批次详情失败:', error);\r\n        this.$message.error('查看批次详情失败');\r\n      }\r\n    },\r\n\r\n    /** 计算持续时间 */\r\n    calculateDuration(startTime) {\r\n      if (!startTime) return '0小时';\r\n\r\n      const start = new Date(startTime);\r\n      const now = new Date();\r\n      const diffMs = now - start;\r\n      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\r\n      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\r\n\r\n      if (diffHours > 0) {\r\n        return `${diffHours}小时${diffMinutes}分钟`;\r\n      } else {\r\n        return `${diffMinutes}分钟`;\r\n      }\r\n    },\r\n\r\n    /** 获取质量评价类型 */\r\n    getQualityEvaluationType(evaluation) {\r\n      if (!evaluation) return '';\r\n\r\n      const lowerEval = evaluation.toLowerCase();\r\n      if (lowerEval.includes('优秀') || lowerEval.includes('良好') || lowerEval.includes('好')) {\r\n        return 'success';\r\n      } else if (lowerEval.includes('一般') || lowerEval.includes('中等')) {\r\n        return 'warning';\r\n      } else if (lowerEval.includes('差') || lowerEval.includes('不合格') || lowerEval.includes('失败')) {\r\n        return 'danger';\r\n      } else {\r\n        return 'info';\r\n      }\r\n    },\r\n\r\n    /** 关闭批次管理对话框并清空数据 */\r\n    closeBatchManagement() {\r\n      // 清空批次管理相关的数据\r\n      this.currentBatch = null;\r\n      this.historyBatches = [];\r\n      this.currentBatchExperiments = [];\r\n      this.selectedOrderForBatch = null;\r\n      this.currentBatchRow = null; // 清空当前行数据\r\n      this.batchDetailData = null;\r\n\r\n      // 重置表单数据\r\n      this.experimentForm = {\r\n        experimentCode: '',\r\n        experimentNote: ''\r\n      };\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n\r\n      // 关闭所有相关的子对话框\r\n      this.batchDetailOpen = false;\r\n      this.addExperimentOpen = false;\r\n      this.finishBatchOpen = false;\r\n    },\r\n\r\n    /** 关闭批次详情对话框并清空数据 */\r\n    closeBatchDetail() {\r\n      // 清空批次详情数据\r\n      this.batchDetailData = null;\r\n    },\r\n\r\n    /** 关闭添加实验对话框并清空数据 */\r\n    closeAddExperiment() {\r\n      // 清空实验表单数据\r\n      this.experimentForm = {\r\n        experimentCode: '',\r\n        experimentNote: ''\r\n      };\r\n      // 清除表单验证状态\r\n      if (this.$refs.experimentForm) {\r\n        this.$refs.experimentForm.clearValidate();\r\n      }\r\n    },\r\n\r\n    /** 关闭结束批次对话框并清空数据 */\r\n    closeFinishBatch() {\r\n      // 清空结束批次表单数据\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n      // 清除表单验证状态\r\n      if (this.$refs.finishBatchForm) {\r\n        this.$refs.finishBatchForm.clearValidate();\r\n      }\r\n    },\r\n\r\n    /** 刷新批次数据 */\r\n    async refreshBatchData() {\r\n      if (this.selectedOrderForBatch) {\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n        this.$message.success('批次数据已刷新');\r\n      }\r\n    },\r\n\r\n    /** 加载实验室编码列表 */\r\n    async loadExperimentCodeList(engineerSampleOrderId) {\r\n      try {\r\n        const response = await getBatchExperimentCodeList(engineerSampleOrderId);\r\n        this.experimentCodeList = response.data || [];\r\n      } catch (error) {\r\n        console.error('加载实验室编码列表失败:', error);\r\n        this.experimentCodeList = [];\r\n        this.$message.error('加载实验室编码列表失败');\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.stats-row {\r\n  margin-bottom: 20px;\r\n}\r\n.stats-card {\r\n  border: none;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n/* 批次详情样式 */\r\n.batch-detail-container {\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.batch-info-card {\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 8px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.info-item label {\r\n  font-weight: 600;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n  min-width: 80px;\r\n}\r\n\r\n.info-value {\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.remark-content {\r\n  background-color: #F5F7FA;\r\n  padding: 12px;\r\n  border-radius: 4px;\r\n  border-left: 4px solid #409EFF;\r\n  color: #606266;\r\n  line-height: 1.5;\r\n  margin-top: 8px;\r\n  min-height: 40px;\r\n}\r\n\r\n.experiments-card {\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 8px;\r\n}\r\n\r\n.empty-state, .empty-experiments {\r\n  background-color: #FAFAFA;\r\n  border-radius: 8px;\r\n  margin: 10px 0;\r\n}\r\n\r\n/* 历史批次表格样式优化 */\r\n.history-batches-card .el-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-batches-card .el-table th {\r\n  background-color: #F5F7FA;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.history-batches-card .el-table--striped .el-table__body tr.el-table__row--striped td {\r\n  background-color: #FAFAFA;\r\n}\r\n\r\n.stats-card:not(.total) {\r\n  cursor: pointer;\r\n}\r\n\r\n.stats-card:not(.total):hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stats-card.active {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  position: relative;\r\n}\r\n\r\n.stats-card.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 3px;\r\n  background: currentColor;\r\n}\r\n\r\n.stats-card.completed.active::after {\r\n  background: #67C23A;\r\n}\r\n\r\n.stats-card.in-progress.active::after {\r\n  background: #409EFF;\r\n}\r\n\r\n.stats-card.overdue.active::after {\r\n  background: #F56C6C;\r\n}\r\n\r\n.stats-card.overdue .stats-icon {\r\n  background: linear-gradient(135deg, #F56C6C, #F78989);\r\n}\r\n\r\n.stats-content {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20px;\r\n}\r\n.stats-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20px;\r\n}\r\n.stats-icon i {\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n.stats-info {\r\n  flex: 1;\r\n}\r\n.stats-title {\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-bottom: 8px;\r\n}\r\n.stats-number {\r\n  font-size: 28px;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n.stats-card.total .stats-icon {\r\n  background: linear-gradient(135deg, #3a7bd5, #3a6073);\r\n}\r\n.stats-card.completed .stats-icon {\r\n  background: linear-gradient(135deg, #E6A23C, #F0C78A);\r\n}\r\n.stats-card.in-progress .stats-icon {\r\n  background: linear-gradient(135deg, #409EFF, #66B1FF);\r\n}\r\n.stats-card.overdue .stats-icon {\r\n  background: linear-gradient(135deg, #F56C6C, #F78989);\r\n}\r\n@media (max-width: 768px) {\r\n  .stats-card .stats-content {\r\n    padding: 15px;\r\n  }\r\n  .stats-icon {\r\n    width: 50px;\r\n    height: 50px;\r\n    margin-right: 15px;\r\n  }\r\n  .stats-icon i {\r\n    font-size: 20px;\r\n  }\r\n  .stats-number {\r\n    font-size: 24px;\r\n  }\r\n}\r\n.other-engineer-select {\r\n  margin-top: 10px;\r\n}\r\n.select-item {\r\n  width: 100%;\r\n}\r\n\r\n/* 未分配打样单告警样式 */\r\n.alert-card {\r\n  margin-bottom: 20px;\r\n}\r\n.alert-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.alert-title {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n}\r\n.alert-title span {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #FF1414;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.alert-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.alert-cards {\r\n  margin-top: 20px;\r\n}\r\n.alert-item {\r\n  margin-bottom: 20px;\r\n  transition: all 0.3s;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n  height: 225px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: #fff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n.alert-item:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);\r\n  border-color: #409EFF;\r\n}\r\n.alert-item-header {\r\n  margin-bottom: 12px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px solid #f0f2f5;\r\n  background: rgba(64, 158, 255, 0.02);\r\n  margin: -16px -16px 12px -16px;\r\n  padding: 12px 16px;\r\n  border-radius: 6px 6px 0 0;\r\n}\r\n.order-code-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.order-code {\r\n  font-size: 15px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n.urgency-tag {\r\n  margin-left: 8px;\r\n}\r\n.alert-item-content {\r\n  padding: 0;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n  min-height: 0;\r\n}\r\n.info-section {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n}\r\n.info-row {\r\n  margin-bottom: 8px;\r\n}\r\n.info-row-double {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  gap: 12px;\r\n}\r\n.info-row-double .info-item {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n.info-row-double .info-item.date-time-item {\r\n  flex: 1.5;\r\n}\r\n.info-row-double .info-item.standard-item {\r\n  flex: 1;\r\n}\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #606266;\r\n  font-size: 13px;\r\n}\r\n.info-label {\r\n  margin-left: 6px;\r\n  margin-right: 4px;\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n.info-value {\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n.difficulty-text {\r\n  cursor: pointer;\r\n  border-bottom: 1px dashed #ccc;\r\n}\r\n.info-reason {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  color: #f56c6c;\r\n  margin-top: 8px;\r\n  padding: 6px 8px;\r\n  background-color: #fef0f0;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #f56c6c;\r\n  font-size: 12px;\r\n}\r\n.reason-text {\r\n  margin-left: 4px;\r\n  line-height: 1.4;\r\n  word-break: break-word;\r\n}\r\n.text-overflow {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n.info-reason span {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n.info-item i,\r\n.info-date i,\r\n.info-reason i {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n}\r\n.alert-item-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: auto;\r\n  padding: 12px 0 0 0;\r\n  border-top: 1px solid #f0f2f5;\r\n  flex-shrink: 0;\r\n  background: rgba(250, 251, 252, 0.5);\r\n  border-radius: 0 0 6px 6px;\r\n  margin-left: -16px;\r\n  margin-right: -16px;\r\n  padding-left: 16px;\r\n  padding-right: 16px;\r\n}\r\n.alert-item-footer .el-button {\r\n  border-radius: 4px;\r\n  font-weight: 500;\r\n}\r\n.alert-item-footer .el-button--primary {\r\n  background: #409EFF;\r\n  border-color: #409EFF;\r\n  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);\r\n}\r\n.alert-item-footer .el-button--primary:hover {\r\n  background: #66b1ff;\r\n  border-color: #66b1ff;\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);\r\n}\r\n.alert-item-footer .el-button--text {\r\n  color: #909399;\r\n  transition: all 0.3s;\r\n}\r\n.alert-item-footer .el-button--text:hover {\r\n  color: #f56c6c;\r\n  background: rgba(245, 108, 108, 0.1);\r\n}\r\n.delete-btn:hover {\r\n  color: #f56c6c !important;\r\n}\r\n\r\n/* 确保卡片内容区域的统一布局 */\r\n.alert-item .el-card__body {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 16px;\r\n  position: relative;\r\n}\r\n\r\n/* 确保内容区域占据剩余空间 */\r\n.alert-item .alert-item-header {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.alert-item .alert-item-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 0;\r\n}\r\n\r\n.alert-item .alert-item-footer {\r\n  flex-shrink: 0;\r\n  margin-top: auto;\r\n}\r\n\r\n.alert-item .info-group {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.alert-item .info-date {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .alert-item {\r\n    height: auto;\r\n    min-height: 200px;\r\n  }\r\n\r\n  .info-row-double {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n\r\n  .alert-item-footer {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .alert-item-footer .el-button {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* 批次管理相关样式 */\r\n.batch-management-container {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.current-batch-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.batch-info-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.batch-info-item label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n}\r\n\r\n.experiment-section {\r\n  border-top: 1px solid #ebeef5;\r\n  padding-top: 15px;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.section-header span {\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.history-batches-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.new-batch-section {\r\n  padding: 40px 0;\r\n  text-align: center;\r\n  background-color: #fafafa;\r\n  border: 2px dashed #dcdfe6;\r\n  border-radius: 6px;\r\n}\r\n\r\n.new-batch-section .el-button {\r\n  padding: 12px 30px;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .batch-management-container {\r\n    max-height: 500px;\r\n  }\r\n\r\n  .batch-info-item {\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .section-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .section-header .el-button {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n\r\n/* 备注文本省略样式 */\r\n.remark-text-ellipsis {\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 2;\r\n  line-clamp: 2;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  line-height: 1.4;\r\n  max-height: 2.8em; /* 2行的高度，基于line-height */\r\n  word-break: break-word;\r\n  white-space: normal;\r\n  cursor: pointer;\r\n}\r\n\r\n.remark-text-ellipsis:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 导出选择对话框样式 */\r\n.export-options {\r\n  padding: 10px 0;\r\n}\r\n\r\n.export-options .el-checkbox {\r\n  width: 100%;\r\n  margin-right: 0;\r\n  padding: 15px;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.export-options .el-checkbox:hover {\r\n  border-color: #409eff;\r\n  background-color: #f0f9ff;\r\n}\r\n\r\n.export-options .el-checkbox.is-checked {\r\n  border-color: #409eff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.export-options .el-checkbox__label {\r\n  width: 100%;\r\n  padding-left: 10px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAk3CA,IAAAA,oBAAA,GAAAC,OAAA;AAsBA,IAAAC,iBAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAC,sBAAA,CAAAH,OAAA;AACAA,OAAA;AACA,IAAAI,SAAA,GAAAJ,OAAA;AACA,IAAAK,SAAA,GAAAL,OAAA;AACA,IAAAM,mBAAA,GAAAH,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAO,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,sBAAA;IAAAC,kBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,YAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,uBAAA;MACA;MACAC,iBAAA;MACA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,aAAA;QACAC,SAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,MAAA;QACAC,OAAA;QACAC,iBAAA;QACAC,UAAA;QACAC,WAAA;QACAC,WAAA;QACAC,SAAA;QAAA;QACAC,UAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAjB,MAAA,GACA;UAAAkB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAlB,eAAA,GACA;UAAAgB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAjB,gBAAA,GACA;UAAAe,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,aAAA;MACAC,kBAAA;MACAC,SAAA;MAAA;MACAC,kBAAA;MACAC,cAAA;MACAC,oBAAA;MACAC,qBAAA;MACA;MACAC,iBAAA;QACAC,SAAA;UACAC,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAC,KAAA,OAAAC,IAAA;YACAF,MAAA,CAAAG,KAAA,UAAAF,KAAA,EAAAA,KAAA;UACA;QACA;UACAH,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAI,SAAA,OAAAF,IAAA;YACAE,SAAA,CAAAC,OAAA,CAAAD,SAAA,CAAAE,OAAA;YACAN,MAAA,CAAAG,KAAA,UAAAC,SAAA,EAAAA,SAAA;UACA;QACA;UACAN,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAO,GAAA,OAAAL,IAAA;YACA,IAAAM,KAAA,OAAAN,IAAA;YACAM,KAAA,CAAAH,OAAA,CAAAG,KAAA,CAAAF,OAAA;YACAN,MAAA,CAAAG,KAAA,UAAAK,KAAA,EAAAD,GAAA;UACA;QACA;UACAT,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAO,GAAA,OAAAL,IAAA;YACA,IAAAM,KAAA,OAAAN,IAAA;YACAM,KAAA,CAAAH,OAAA,CAAAG,KAAA,CAAAF,OAAA;YACAN,MAAA,CAAAG,KAAA,UAAAK,KAAA,EAAAD,GAAA;UACA;QACA;MACA;MACA;MACAE,UAAA;MACAC,cAAA;QACA;QACA;QACA;QACA;MACA;MACA;MACAC,kBAAA;MACA;MACAC,kBAAA;QACAC,EAAA;QACA3C,eAAA;QACA4C,mBAAA;QACAC,aAAA;QACAC,aAAA;QACA5C,aAAA;QACA6C,kBAAA;MACA;MACA;MACAC,mBAAA;QACAF,aAAA,GACA;UAAA9B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAhB,aAAA,GACA;UAAAc,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACA+B,eAAA;MACA;MACAC,mBAAA;MACAC,YAAA;MACAC,cAAA;MACAC,uBAAA;MACAC,qBAAA;MACAC,eAAA;MAAA;MACA;MACAC,eAAA;MACAC,eAAA;MACA;MACAC,iBAAA;MACAC,cAAA;QACAC,cAAA;QACAC,cAAA;MACA;MACAC,eAAA;QACAF,cAAA,GACA;UAAA5C,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACA6C,eAAA;MACAC,eAAA;QACAC,iBAAA;QACAC,MAAA;MACA;MACA;MACAC,uBAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA;UACA,OAAAA,IAAA,CAAAjC,OAAA,KAAAJ,IAAA,CAAAsC,GAAA;QACA;MACA;MACAC,kBAAA;MACAC,iBAAA;MACA;MACAC,gBAAA;MACAC,qBAAA;QACA9E,OAAA;QACAC,QAAA;MACA;MACA8E,eAAA;MACA;MACAC,0BAAA;MACA;MACAC,cAAA;MACAC,iBAAA;MACAC,cAAA;QACAC,cAAA;QACAd,MAAA;MACA;MACAe,kBAAA;MAAA;MACAC,eAAA;QACAF,cAAA,GACA;UAAAhE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAiE,gBAAA;MACA;MACAC,wBAAA;MACAC,2BAAA;MACAC,wBAAA;QACAN,cAAA;QACAd,MAAA;MACA;MACAqB,yBAAA;QACAP,cAAA,GACA;UAAAhE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAsE,8BAAA;MACA;MACAC,gBAAA;MACAC,aAAA;MACAC,aAAA;MACAC,QAAA;MACA;MACAC,kBAAA;MACAC,gBAAA;MACAC,eAAA;MACAC,SAAA;MACA;MACAC,gBAAA;MACAC,aAAA;MACAC,UAAA;QACAnG,eAAA;QACAoG,YAAA;MACA;MACA;MACAC,oBAAA;MACAC,uBAAA;MACAC,oBAAA;QACAvG,eAAA;QACAwG,kBAAA;QACAC,iBAAA;QACAC,QAAA;MACA;MACAC,iBAAA;MACAC,WAAA;QACAR,YAAA,GACA;UAAApF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA2F,gBAAA;IACA;EACA;EACAC,QAAA;IACA,0CACAC,YAAA,WAAAA,aAAA;MACA,YAAAzD,qBAAA,SAAAA,qBAAA,CAAArD,gBAAA;IACA;EACA;EACA+G,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA;IACA,IAAAlF,KAAA,OAAAC,IAAA;IACA,IAAAkF,QAAA,GAAAnF,KAAA,CAAAoF,WAAA,WAAAC,MAAA,CAAArF,KAAA,CAAAsF,QAAA,QAAAC,QAAA,iBAAAF,MAAA,CAAArF,KAAA,CAAAwF,OAAA,IAAAD,QAAA;IACA,KAAAjG,SAAA,IAAA6F,QAAA,EAAAA,QAAA;IAEA,KAAAM,OAAA;IACA,IAAAC,yBAAA,IAAAC,IAAA,WAAAC,GAAA;MAAA,OAAAV,KAAA,CAAAlB,eAAA,GAAA4B,GAAA;IAAA;IAEA,KAAAC,QAAA,cAAAF,IAAA,WAAAG,QAAA;MACAZ,KAAA,CAAA9F,aAAA,GAAA0G,QAAA,CAAA/I,IAAA;IACA;IACA,KAAA8I,QAAA,0BAAAF,IAAA,WAAAG,QAAA;MACAZ,KAAA,CAAA7F,kBAAA,GAAAyG,QAAA,CAAA/I,IAAA;IACA;IACA,KAAA8I,QAAA,qBAAAF,IAAA,WAAAG,QAAA;MACAZ,KAAA,CAAAzH,WAAA,GAAAqI,QAAA,CAAA/I,IAAA;IACA;IACA,KAAAgJ,kBAAA;IACA;IACA,IAAAC,2CAAA,IAAAL,IAAA,WAAAG,QAAA;MACAZ,KAAA,CAAA1H,iBAAA,GAAA0H,KAAA,CAAAe,UAAA,CAAAH,QAAA,CAAA/I,IAAA;IACA;IACA;IACA,KAAAmJ,sBAAA;EACA;EACAC,OAAA;IACA,gBACAC,eAAA,WAAAA,gBAAAC,GAAA;MACA,IAAAC,aAAA,GAAAD,GAAA,CAAAC,aAAA;MACA,IAAAC,YAAA,GAAAF,GAAA,CAAAE,YAAA;;MAEA;MACA,IAAAA,YAAA,WAAAA,YAAA;QACA,OAAAD,aAAA;MACA;MAEA,OAAAA,aAAA,GAAAC,YAAA;IACA;IACA,mBACAd,OAAA,WAAAA,QAAA;MAAA,IAAAe,MAAA;MACA,IAAAC,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAA/I,WAAA;MACA6I,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAlH,kBAAA;MACAkH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAjH,cAAA;MACAiH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAhH,oBAAA;MACAgH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA/G,qBAAA;MACA;MACA,OAAA+G,MAAA,CAAAI,cAAA;MACA,OAAAJ,MAAA,CAAAK,YAAA;MACA;MACA,SAAAxH,SAAA,SAAAA,SAAA,CAAAyH,MAAA;QACAN,MAAA,CAAAI,cAAA,QAAAvH,SAAA;QACAmH,MAAA,CAAAK,YAAA,QAAAxH,SAAA;MACA;MACA,KAAAtC,OAAA;MACAyJ,MAAA,CAAAhI,iBAAA;MACA,IAAAuI,4CAAA,EAAAP,MAAA,EAAAd,IAAA,WAAAG,QAAA;QACAU,MAAA,CAAAjJ,uBAAA,GAAAuI,QAAA,CAAAmB,IAAA;QACAT,MAAA,CAAAlJ,KAAA,GAAAwI,QAAA,CAAAxI,KAAA;QACAkJ,MAAA,CAAAxJ,OAAA;MACA;IACA;IACA;IACAkK,MAAA,WAAAA,OAAA;MACA,KAAAvJ,IAAA;MACA,KAAAwJ,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAApI,IAAA;QACA6B,EAAA;QACA7C,MAAA;QACAE,eAAA;QACAmJ,WAAA;QACAC,iBAAA;QACAnJ,gBAAA;QACAC,aAAA;QACAE,eAAA;QACAC,gBAAA;QACAgJ,cAAA;QACAC,iBAAA;QACAC,gBAAA;QACAtF,iBAAA;QACAuF,QAAA;QACArJ,SAAA;QACAsJ,OAAA;QACAC,SAAA;QACAC,iBAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACA7F,MAAA;MACA;MACA,KAAA8F,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAtK,WAAA,CAAAC,OAAA;MACA,KAAA8E,qBAAA,CAAA9E,OAAA;MACA,KAAA4H,OAAA;MACA,KAAAM,kBAAA;MACA,KAAAG,sBAAA;IACA;IACA,aACAiC,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAA3I,SAAA;MACA,KAAAC,kBAAA;MACA,KAAAC,cAAA;MACA,KAAAC,oBAAA;MACA,KAAAC,qBAAA;MACA,KAAAwI,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAApL,GAAA,GAAAoL,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA3H,EAAA;MAAA;MACA,KAAA1D,YAAA,GAAAmL,SAAA;MACA,KAAAlL,MAAA,GAAAkL,SAAA,CAAAtB,MAAA;MACA,KAAA3J,QAAA,IAAAiL,SAAA,CAAAtB,MAAA;IACA;IACA,aACAyB,SAAA,WAAAA,UAAA;MACA,KAAArB,KAAA;MACA,KAAAxJ,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACA+K,YAAA,WAAAA,aAAApC,GAAA;MAAA,IAAAqC,MAAA;MACA,KAAAvB,KAAA;MACA,IAAAvG,EAAA,GAAAyF,GAAA,CAAAzF,EAAA,SAAA3D,GAAA;MACA,IAAA0L,2CAAA,EAAA/H,EAAA,EAAA+E,IAAA,WAAAG,QAAA;QACA4C,MAAA,CAAA3J,IAAA,GAAA+G,QAAA,CAAA/I,IAAA;QACA2L,MAAA,CAAA/K,IAAA;QACA+K,MAAA,CAAAhL,KAAA;MACA;IACA;IACA,WACAkL,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA9J,IAAA,CAAA6B,EAAA;YACA,IAAAqI,8CAAA,EAAAJ,MAAA,CAAA9J,IAAA,EAAA4G,IAAA,WAAAG,QAAA;cACA+C,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAAlL,IAAA;cACAkL,MAAA,CAAApD,OAAA;cACAoD,MAAA,CAAA9C,kBAAA;YACA;UACA;YACA,IAAAoD,2CAAA,EAAAN,MAAA,CAAA9J,IAAA,EAAA4G,IAAA,WAAAG,QAAA;cACA+C,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAAlL,IAAA;cACAkL,MAAA,CAAApD,OAAA;cACAoD,MAAA,CAAA9C,kBAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAqD,YAAA,WAAAA,aAAA/C,GAAA;MAAA,IAAAgD,MAAA;MACA,IAAApM,GAAA,GAAAoJ,GAAA,CAAAzF,EAAA,SAAA3D,GAAA;MACA,KAAAqM,QAAA,wBAAAjD,GAAA,CAAApI,eAAA,aAAA0H,IAAA;QACA,WAAA4D,2CAAA,EAAAtM,GAAA;MACA,GAAA0I,IAAA;QACA0D,MAAA,CAAA5D,OAAA;QACA4D,MAAA,CAAAH,UAAA;QACAG,MAAA,CAAAtD,kBAAA;MACA,GAAAyD,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA;MACA,KAAA9F,aAAA;MACA,KAAAD,gBAAA;IACA;IACA,aACAgG,aAAA,WAAAA,cAAA;MACA,SAAA/F,aAAA,CAAAoD,MAAA;QACA,KAAA4C,QAAA,CAAAC,OAAA;QACA;MACA;MACA,KAAAhG,aAAA;MACA,KAAAiG,cAAA;IACA;IAEA,eACAA,cAAA,WAAAA,eAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,KAAA,SAAAnG,aAAA,CAAAoD,MAAA;QACA;QACA,KAAAnD,aAAA;QACA,KAAAF,gBAAA;QACA,KAAAiG,QAAA,CAAAK,OAAA,oDAAAC,MAAA,MAAAtG,aAAA,CAAAoD,MAAA;QACA;MACA;MAEA,IAAAmD,MAAA,QAAAvG,aAAA,CAAAmG,KAAA;MACA,IAAArL,iBAAA,GAAAyL,MAAA;MACA,IAAAC,QAAA,GAAAD,MAAA;MAEA,KAAAE,QAAA,CAAA3L,iBAAA,EAAAyL,MAAA,EAAAvE,IAAA;QACAoE,MAAA,CAAAJ,QAAA,CAAAK,OAAA,IAAAC,MAAA,CAAAE,QAAA;QACA;QACAJ,MAAA,CAAAF,cAAA,CAAAC,KAAA;MACA,GAAAN,KAAA,WAAAa,KAAA;QACAN,MAAA,CAAAnG,aAAA;QACAmG,MAAA,CAAAJ,QAAA,CAAAU,KAAA,IAAAJ,MAAA,CAAAE,QAAA,kDAAAF,MAAA,CAAAI,KAAA,CAAAnL,OAAA,IAAAmL,KAAA;MACA;IACA;IACA,aACAD,QAAA,WAAAA,SAAA3L,iBAAA,EAAA6L,UAAA;MAAA,IAAAC,MAAA;MACA,IAAA9D,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAA/I,WAAA;MACA6I,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAlH,kBAAA;MACAkH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAjH,cAAA;MACAiH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAhH,oBAAA;MACAgH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA/G,qBAAA;MACA;MACA,OAAA+G,MAAA,CAAAI,cAAA;MACA,OAAAJ,MAAA,CAAAK,YAAA;MACA;MACA,SAAAxH,SAAA,SAAAA,SAAA,CAAAyH,MAAA;QACAN,MAAA,CAAAI,cAAA,QAAAvH,SAAA;QACAmH,MAAA,CAAAK,YAAA,QAAAxH,SAAA;MACA;MACAmH,MAAA,CAAAhI,iBAAA,GAAAA,iBAAA;MACAgI,MAAA,CAAA6D,UAAA,GAAAA,UAAA;MACA,WAAAE,8CAAA,EAAA/D,MAAA,EAAAd,IAAA,WAAAG,QAAA;QACAyE,MAAA,CAAAE,QAAA,CAAA3E,QAAA,CAAA4E,GAAA;MACA;IACA;IACA,uBACAC,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA,IAAAnE,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAA/I,WAAA;MACA6I,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAlH,kBAAA;MACAkH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAjH,cAAA;MACAiH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAhH,oBAAA;MACAgH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA/G,qBAAA;MACA;MACA,OAAA+G,MAAA,CAAAI,cAAA;MACA,OAAAJ,MAAA,CAAAK,YAAA;MACA;MACA,SAAAxH,SAAA,SAAAA,SAAA,CAAAyH,MAAA;QACAN,MAAA,CAAAI,cAAA,QAAAvH,SAAA;QACAmH,MAAA,CAAAK,YAAA,QAAAxH,SAAA;MACA;MACA,KAAAgK,QAAA;QACAuB,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAApF,IAAA;QACAiF,MAAA,CAAAhH,aAAA;QACA,WAAA4G,8CAAA,EAAA/D,MAAA;MACA,GAAAd,IAAA,WAAAG,QAAA;QACA8E,MAAA,CAAAH,QAAA,CAAA3E,QAAA,CAAA4E,GAAA;QACAE,MAAA,CAAAhH,aAAA;QACAgH,MAAA,CAAAjB,QAAA,CAAAK,OAAA;MACA,GAAAR,KAAA;QACAoB,MAAA,CAAAhH,aAAA;MACA;IACA;IACA,iBACAoH,WAAA,WAAAA,YAAA3E,GAAA;MAAA,IAAA4E,MAAA;MACA,KAAA3B,QAAA,cAAAjD,GAAA,CAAApI,eAAA;QACA4M,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAApF,IAAA;QACA,IAAAuF,4CAAA;UACAtK,EAAA,EAAAyF,GAAA,CAAAzF,EAAA;UACAuK,MAAA;QACA,GAAAxF,IAAA,WAAAG,QAAA;UACAmF,MAAA,CAAA/B,UAAA;UACA+B,MAAA,CAAAxF,OAAA;UACAwF,MAAA,CAAAlF,kBAAA;QACA;MACA;IACA;IACA,cACAqF,yBAAA,WAAAA,0BAAA/E,GAAA;MAAA,IAAAgF,OAAA;MACA,KAAA/B,QAAA;QACAuB,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAApF,IAAA;QACA0F,OAAA,CAAAzH,aAAA;QACA,WAAA0H,+BAAA;UAAAC,MAAA,EAAAlF,GAAA,CAAAkF,MAAA;UAAAC,SAAA,EAAAnF,GAAA,CAAAmF;QAAA;MACA,GAAA7F,IAAA,WAAAG,QAAA;QACAuF,OAAA,CAAAZ,QAAA,CAAA3E,QAAA,CAAA4E,GAAA;QACAW,OAAA,CAAAzH,aAAA;MACA,GAAA4F,KAAA;IACA;IACA,kBACAiC,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,SAAAxO,YAAA,CAAA6J,MAAA;QACA,KAAA4E,MAAA,CAAAC,QAAA;QACA;MACA;;MAEA;MACA,IAAAC,UAAA,QAAA3O,YAAA,CAAAoL,GAAA,WAAAjC,GAAA;QAAA;UACAkF,MAAA,EAAAlF,GAAA,CAAAkF,MAAA;UACAC,SAAA,EAAAnF,GAAA,CAAAmF;QACA;MAAA;MAEA,KAAAlC,QAAA;QACAuB,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAApF,IAAA;QACA+F,OAAA,CAAA9H,aAAA;QACA,WAAAkI,mCAAA,EAAAD,UAAA;MACA,GAAAlG,IAAA,WAAAG,QAAA;QACA4F,OAAA,CAAAjB,QAAA,CAAA3E,QAAA,CAAA4E,GAAA;QACAgB,OAAA,CAAA9H,aAAA;QACA8H,OAAA,CAAA/B,QAAA,CAAAK,OAAA;MACA,GAAAR,KAAA;QACAkC,OAAA,CAAA9H,aAAA;MACA;IACA;IACA,iBACAmI,YAAA,WAAAA,aAAA1F,GAAA;MAAA,IAAA2F,OAAA;MACA,KAAA5I,gBAAA,GAAAiD,GAAA;MACA,KAAArD,cAAA,CAAAC,cAAA;MACA,KAAAD,cAAA,CAAAb,MAAA;MACA,KAAAW,cAAA;MACA;MACA,KAAAmJ,sBAAA,CAAA5F,GAAA,CAAAzF,EAAA;MACA,KAAAsL,SAAA;QACAF,OAAA,CAAAlD,KAAA,CAAA9F,cAAA,CAAAmJ,aAAA;MACA;IACA;IACA,aACAC,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MACA,KAAAvD,KAAA,CAAA9F,cAAA,CAAA+F,QAAA,WAAAC,KAAA;QACA;QACA,IAAA/F,cAAA,GAAAoJ,OAAA,CAAArJ,cAAA,CAAAC,cAAA;;QAEA;QACA,IAAAqJ,KAAA,CAAAC,OAAA,CAAAtJ,cAAA;UACAA,cAAA,GAAAA,cAAA,CAAAuJ,IAAA;QACA;QACA,IAAAxD,KAAA;UACAqD,OAAA,CAAAtJ,iBAAA;UACA,IAAAmI,4CAAA;YACAtK,EAAA,EAAAyL,OAAA,CAAAjJ,gBAAA,CAAAxC,EAAA;YACAuK,MAAA;YACAlI,cAAA,EAAAA,cAAA;YACAd,MAAA,EAAAkK,OAAA,CAAArJ,cAAA,CAAAb;UACA,GAAAwD,IAAA,WAAAG,QAAA;YACAuG,OAAA,CAAAtJ,iBAAA;YACAsJ,OAAA,CAAAvJ,cAAA;YACAuJ,OAAA,CAAAnD,UAAA;YACAmD,OAAA,CAAA5G,OAAA;YACA4G,OAAA,CAAAtG,kBAAA;UACA,GAAAyD,KAAA;YACA6C,OAAA,CAAAtJ,iBAAA;UACA;QACA;MACA;IACA;IACA,mBACA0J,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,OAAA;MACA,KAAAtJ,gBAAA,QAAA5B,eAAA;MACA,KAAAwB,cAAA,CAAAC,cAAA;MACA,KAAAD,cAAA,CAAAb,MAAA;MACA,KAAAW,cAAA;MACA;MACA,KAAAmJ,sBAAA,MAAAzK,eAAA,CAAAZ,EAAA;MACA,KAAAsL,SAAA;QACAQ,OAAA,CAAA5D,KAAA,CAAA9F,cAAA,CAAAmJ,aAAA;MACA;IACA;IACA,sBACAQ,0BAAA,WAAAA,2BAAAtG,GAAA;MAAA,IAAAuG,OAAA;MACA,KAAAnJ,8BAAA,GAAA4C,GAAA;MACA;MACA,KAAA9C,wBAAA,CAAAN,cAAA,GAAAoD,GAAA,CAAApD,cAAA;MACA,KAAAM,wBAAA,CAAApB,MAAA,GAAAkE,GAAA,CAAAlE,MAAA;MACA,KAAAkB,wBAAA;MACA,KAAA6I,SAAA;QACAU,OAAA,CAAA9D,KAAA,CAAAvF,wBAAA,CAAA4I,aAAA;MACA;IACA;IACA,gBACAU,2BAAA,WAAAA,4BAAA;MAAA,IAAAC,OAAA;MACA,KAAAhE,KAAA,CAAAvF,wBAAA,CAAAwF,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA8D,OAAA,CAAAxJ,2BAAA;UACA,IAAA4H,4CAAA;YACAtK,EAAA,EAAAkM,OAAA,CAAArJ,8BAAA,CAAA7C,EAAA;YACAuK,MAAA,EAAA2B,OAAA,CAAArJ,8BAAA,CAAAvF,gBAAA;YAAA;YACA+E,cAAA,EAAA6J,OAAA,CAAAvJ,wBAAA,CAAAN,cAAA;YACAd,MAAA,EAAA2K,OAAA,CAAAvJ,wBAAA,CAAApB;UACA,GAAAwD,IAAA,WAAAG,QAAA;YACAgH,OAAA,CAAAxJ,2BAAA;YACAwJ,OAAA,CAAAzJ,wBAAA;YACAyJ,OAAA,CAAA5D,UAAA;YACA4D,OAAA,CAAArH,OAAA;YACAqH,OAAA,CAAA/G,kBAAA;UACA,GAAAyD,KAAA;YACAsD,OAAA,CAAAxJ,2BAAA;UACA;QACA;MACA;IACA;IACA,iBACAyJ,YAAA,WAAAA,aAAA1G,GAAA;MAAA,IAAA2G,OAAA;MACA,KAAAlI,gBAAA,GAAAuB,GAAA;MACA,KAAAjC,UAAA,CAAAnG,eAAA,GAAAoI,GAAA,CAAApI,eAAA;MACA,KAAAmG,UAAA,CAAAC,YAAA;MACA,KAAAH,gBAAA;MACA,KAAAgI,SAAA;QACAc,OAAA,CAAAlE,KAAA,CAAA1E,UAAA,CAAA+H,aAAA;MACA;IACA;IACA,WACAc,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA,KAAApE,KAAA,CAAA1E,UAAA,CAAA2E,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAkE,OAAA,CAAA/I,aAAA;UACA,IAAA+G,4CAAA;YACAtK,EAAA,EAAAsM,OAAA,CAAApI,gBAAA,CAAAlE,EAAA;YACAuK,MAAA;YACA9G,YAAA,EAAA6I,OAAA,CAAA9I,UAAA,CAAAC;UACA,GAAAsB,IAAA,WAAAG,QAAA;YACAoH,OAAA,CAAA/I,aAAA;YACA+I,OAAA,CAAAhJ,gBAAA;YACAgJ,OAAA,CAAAhE,UAAA;YACAgE,OAAA,CAAAzH,OAAA;YACAyH,OAAA,CAAAnH,kBAAA;YACA;YACAmH,OAAA,CAAAhH,sBAAA;UACA,GAAAsD,KAAA;YACA0D,OAAA,CAAA/I,aAAA;UACA;QACA;MACA;IACA;IACA,mBACAgJ,sBAAA,WAAAA,uBAAA9G,GAAA;MAAA,IAAA+G,OAAA;MACA,KAAAxI,iBAAA,GAAAyB,GAAA;MACA,KAAA7B,oBAAA,CAAAvG,eAAA,GAAAoI,GAAA,CAAApI,eAAA;MACA,KAAAuG,oBAAA,CAAAC,kBAAA,GAAA4B,GAAA,CAAA5B,kBAAA;MACA,KAAAD,oBAAA,CAAAE,iBAAA,GAAA2B,GAAA,CAAA3B,iBAAA;MACA,KAAAF,oBAAA,CAAAG,QAAA,GAAA0B,GAAA,CAAA1B,QAAA;MACA,KAAAL,oBAAA;MACA,KAAA4H,SAAA;QACAkB,OAAA,CAAAtE,KAAA,CAAAtE,oBAAA,CAAA2H,aAAA;MACA;IACA;IACA,aACAkB,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,OAAA;MACA,KAAAxE,KAAA,CAAAtE,oBAAA,CAAAuE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAsE,OAAA,CAAA/I,uBAAA;UACA,IAAA2G,4CAAA;YACAtK,EAAA,EAAA0M,OAAA,CAAA1I,iBAAA,CAAAhE,EAAA;YACAuK,MAAA;YAAA;YACA1G,kBAAA,EAAA6I,OAAA,CAAA9I,oBAAA,CAAAC,kBAAA;YACAC,iBAAA,EAAA4I,OAAA,CAAA9I,oBAAA,CAAAE,iBAAA;YACAC,QAAA,EAAA2I,OAAA,CAAA9I,oBAAA,CAAAG;UACA,GAAAgB,IAAA,WAAAG,QAAA;YACAwH,OAAA,CAAA/I,uBAAA;YACA+I,OAAA,CAAAhJ,oBAAA;YACAgJ,OAAA,CAAApE,UAAA;YACAoE,OAAA,CAAA7H,OAAA;YACA6H,OAAA,CAAAvH,kBAAA;UACA,GAAAyD,KAAA;YACA8D,OAAA,CAAA/I,uBAAA;UACA;QACA;MACA;IACA;IACA,cACAgJ,WAAA,WAAAA,YAAAlH,GAAA;MAAA,IAAAmH,OAAA;MAAA,WAAAC,kBAAA,CAAA9G,OAAA,mBAAA+G,oBAAA,CAAA/G,OAAA,IAAAgH,IAAA,UAAAC,QAAA;QAAA,IAAAC,OAAA,EAAA9Q,IAAA,EAAA6D,EAAA;QAAA,WAAA8M,oBAAA,CAAA/G,OAAA,IAAAmH,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAEA;cACAJ,OAAA,GAAAxH,GAAA,CAAA8H,cAAA;cAAAH,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAE,4CAAA,EAAAP,OAAA;YAAA;cAAA9Q,IAAA,GAAAiR,QAAA,CAAAK,IAAA;cAAA,MACAtR,IAAA,iBAAAuR,gBAAA,EAAAvR,IAAA,CAAA6D,EAAA;gBAAAoN,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAtN,EAAA,GAAA7D,IAAA,CAAA6D,EAAA;cACA4M,OAAA,CAAA1E,KAAA,CAAAhM,kBAAA,CAAAa,IAAA;cAAAqQ,QAAA,CAAAE,IAAA;cAAA,OACAV,OAAA,CAAAtB,SAAA;YAAA;cACAsB,OAAA,CAAA1E,KAAA,CAAAhM,kBAAA,CAAAqK,KAAA;cACAqG,OAAA,CAAA1E,KAAA,CAAAhM,kBAAA,CAAAyR,IAAA,CAAA3N,EAAA;cAAAoN,QAAA,CAAAE,IAAA;cAAA;YAAA;cAEAV,OAAA,CAAA7D,QAAA,CAAAU,KAAA;YAAA;cAAA2D,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAQ,EAAA,GAAAR,QAAA;cAGAS,OAAA,CAAApE,KAAA,cAAA2D,QAAA,CAAAQ,EAAA;cACAhB,OAAA,CAAA7D,QAAA,CAAAU,KAAA,iBAAA2D,QAAA,CAAAQ,EAAA,CAAAtP,OAAA;YAAA;YAAA;cAAA,OAAA8O,QAAA,CAAAU,IAAA;UAAA;QAAA,GAAAd,OAAA;MAAA;IAEA;IAEA,eACAe,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAC,OAAA;MACA,IAAAD,KAAA;QACA,KAAAtF,QAAA;UACAuB,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAApF,IAAA;UACAkJ,OAAA,CAAA9P,IAAA,CAAA0I,QAAA;QACA,GAAA+B,KAAA;UACAqF,OAAA,CAAA9P,IAAA,CAAA0I,QAAA;QACA;MACA;IACA;IACA,kBACAqH,qBAAA,WAAAA,sBAAAF,KAAA,EAAAvI,GAAA;MAAA,IAAA0I,OAAA;MACA,IAAAH,KAAA;QACA,KAAAtF,QAAA;UACAuB,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAApF,IAAA;UACA;UACA,IAAAsD,8CAAA;YACArI,EAAA,EAAAyF,GAAA,CAAAzF,EAAA;YACA6G,QAAA;UACA,GAAA9B,IAAA,WAAAG,QAAA;YACAiJ,OAAA,CAAA7F,UAAA;YACA6F,OAAA,CAAAtJ,OAAA;YACAsJ,OAAA,CAAAhJ,kBAAA;UACA;QACA,GAAAyD,KAAA;UACA;UACAnD,GAAA,CAAAoB,QAAA;QACA;MACA;QACA;QACA,IAAAwB,8CAAA;UACArI,EAAA,EAAAyF,GAAA,CAAAzF,EAAA;UACA6G,QAAA;QACA,GAAA9B,IAAA,WAAAG,QAAA;UACAiJ,OAAA,CAAA7F,UAAA;UACA6F,OAAA,CAAAtJ,OAAA;UACAsJ,OAAA,CAAAhJ,kBAAA;QACA;MACA;IACA;IACA,eACAA,kBAAA,WAAAA,mBAAA;MAAA,IAAAiJ,OAAA;MACA,IAAAvI,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAA/I,WAAA;MACA;MACA,SAAA0B,SAAA,SAAAA,SAAA,CAAAyH,MAAA;QACAN,MAAA,CAAAI,cAAA,QAAAvH,SAAA;QACAmH,MAAA,CAAAK,YAAA,QAAAxH,SAAA;MACA;MACA,IAAA2P,sCAAA,EAAAxI,MAAA,EAAAd,IAAA,WAAAG,QAAA;QACAkJ,OAAA,CAAAvO,cAAA,GAAAqF,QAAA,CAAA/I,IAAA;MACA;IACA;IACA,gBACAmS,oBAAA,WAAAA,qBAAA7I,GAAA;MAAA,IAAA8I,OAAA;MACA,KAAAxO,kBAAA;QACAC,EAAA,EAAAyF,GAAA,CAAAzF,EAAA;QACA3C,eAAA,EAAAoI,GAAA,CAAApI,eAAA;QACA4C,mBAAA,EAAAwF,GAAA,CAAArI,QAAA;QACA8C,aAAA,EAAAuF,GAAA,CAAAtI,MAAA;QACAgD,aAAA;QACA5C,aAAA;QACA6C,kBAAA;MACA;;MAEA;MACA,IAAAoO,kDAAA;QACA/H,iBAAA,EAAAhB,GAAA,CAAAgB,iBAAA;QACAgI,UAAA,EAAAhJ,GAAA,CAAAgJ;MACA,GAAA1J,IAAA,WAAAG,QAAA;QACAqJ,OAAA,CAAAjO,eAAA,GAAA4E,QAAA,CAAA/I,IAAA;MACA;MACA;MACA,IAAAuS,+CAAA,IAAA3J,IAAA,WAAAG,QAAA;QACAqJ,OAAA,CAAA1M,iBAAA,GAAAqD,QAAA,CAAA/I,IAAA;MACA;MACA,KAAA2D,kBAAA;IACA;IACA,cACA6O,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,KAAA1G,KAAA,uBAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAjM,IAAA;YACA0S,aAAA,EAAAD,OAAA,CAAA7O,kBAAA,CAAAC,EAAA;YACAE,aAAA,EAAA0O,OAAA,CAAA7O,kBAAA,CAAAG,aAAA;YACAC,aAAA,EAAAyO,OAAA,CAAA7O,kBAAA,CAAAI,aAAA;YACA5C,aAAA,EAAAqR,OAAA,CAAA7O,kBAAA,CAAAxC,aAAA;YACA6C,kBAAA,EAAAwO,OAAA,CAAA7O,kBAAA,CAAAK;UACA;;UAEA;UACA,IAAA0O,mCAAA,EAAA3S,IAAA,EAAA4I,IAAA,WAAAG,QAAA;YACA0J,OAAA,CAAAtG,UAAA;YACAsG,OAAA,CAAA9O,kBAAA;YACA8O,OAAA,CAAA/J,OAAA;YACA;YACA+J,OAAA,CAAAtJ,sBAAA;YACAsJ,OAAA,CAAAzJ,kBAAA;UACA,GAAAyD,KAAA;YACAgG,OAAA,CAAA5D,QAAA;UACA;QACA;MACA;IACA;IACA,iBACA1F,sBAAA,WAAAA,uBAAA;MAAA,IAAAyJ,OAAA;MACA,IAAAlJ,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAA/I,WAAA;MACA6I,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAlH,kBAAA;MACAkH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAjH,cAAA;MACAiH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAhH,oBAAA;MACAgH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA/G,qBAAA;MACA;MACA,OAAA+G,MAAA,CAAAI,cAAA;MACA,OAAAJ,MAAA,CAAAK,YAAA;MACA;MACA,SAAAxH,SAAA,SAAAA,SAAA,CAAAyH,MAAA;QACAN,MAAA,CAAAI,cAAA,QAAAvH,SAAA;QACAmH,MAAA,CAAAK,YAAA,QAAAxH,SAAA;MACA;MACAmH,MAAA,CAAAhI,iBAAA;MACAgI,MAAA,CAAA5I,OAAA,QAAA8E,qBAAA,CAAA9E,OAAA;MACA4I,MAAA,CAAA3I,QAAA,QAAA6E,qBAAA,CAAA7E,QAAA;MACA,IAAAkJ,4CAAA,EAAAP,MAAA,EAAAd,IAAA,WAAAG,QAAA;QACA6J,OAAA,CAAAjN,gBAAA,GAAAoD,QAAA,CAAAmB,IAAA;QACA0I,OAAA,CAAA/M,eAAA,GAAAkD,QAAA,CAAAxI,KAAA;MACA;IACA;IACA,cACAsS,oBAAA,WAAAA,qBAAAvJ,GAAA;MAAA,IAAAwJ,OAAA;MACA,KAAAlP,kBAAA;QACAC,EAAA,EAAAyF,GAAA,CAAAzF,EAAA;QACA3C,eAAA,EAAAoI,GAAA,CAAApI,eAAA;QACA4C,mBAAA;QACAC,aAAA;QACAC,aAAA;QACA5C,aAAA;QACA6C,kBAAA;MACA;;MAEA;MACA,IAAAoO,kDAAA;QACA/H,iBAAA,EAAAhB,GAAA,CAAAgB,iBAAA;QACAgI,UAAA,EAAAhJ,GAAA,CAAAgJ;MACA,GAAA1J,IAAA,WAAAG,QAAA;QACA+J,OAAA,CAAA3O,eAAA,GAAA4E,QAAA,CAAA/I,IAAA;MACA;MACA;MACA,IAAAuS,+CAAA,IAAA3J,IAAA,WAAAG,QAAA;QACA+J,OAAA,CAAApN,iBAAA,GAAAqD,QAAA,CAAA/I,IAAA;MACA;MACA,KAAA2D,kBAAA;IACA;IACA,eACAoP,sBAAA,WAAAA,uBAAAzJ,GAAA;MAAA,IAAA0J,OAAA;MACA,KAAAzG,QAAA,mBAAAjD,GAAA,CAAApI,eAAA,aAAA0H,IAAA;QACA,WAAA4D,2CAAA,EAAAlD,GAAA,CAAAzF,EAAA;MACA,GAAA+E,IAAA;QACAoK,OAAA,CAAA7J,sBAAA;QACA6J,OAAA,CAAA7G,UAAA;MACA,GAAAM,KAAA;IACA;IACA,eACAwG,sBAAA,WAAAA,uBAAA3J,GAAA;MAAA,IAAA4J,OAAA;MACA,KAAAnL,gBAAA,GAAAuB,GAAA;MACA,KAAAjC,UAAA,CAAAnG,eAAA,GAAAoI,GAAA,CAAApI,eAAA;MACA,KAAAmG,UAAA,CAAAC,YAAA;MACA,KAAAH,gBAAA;MACA,KAAAgI,SAAA;QACA+D,OAAA,CAAAnH,KAAA,CAAA1E,UAAA,CAAA+H,aAAA;MACA;IACA;IACA,mBACA+D,0BAAA,WAAAA,2BAAAC,OAAA;MACA,KAAAxN,qBAAA,CAAA7E,QAAA,GAAAqS,OAAA;MACA,KAAAjK,sBAAA;IACA;IACA,iBACAkK,6BAAA,WAAAA,8BAAAC,OAAA;MACA,KAAA1N,qBAAA,CAAA9E,OAAA,GAAAwS,OAAA;MACA,KAAAnK,sBAAA;IACA;IACA,kBACAoK,qBAAA,WAAAA,sBAAA;MACA,KAAAzN,0BAAA,SAAAA,0BAAA;IACA;IACA,aACA0N,kBAAA,WAAAA,mBAAApF,MAAA;MACA;MACA,KAAAvN,WAAA,CAAAiB,SAAA;MACA,KAAAjB,WAAA,CAAAM,gBAAA,GAAAiN,MAAA;MACA,KAAAjD,WAAA;IACA;IAEA,eACAsI,mBAAA,WAAAA,oBAAA;MACA;MACA,KAAA5S,WAAA,CAAAM,gBAAA;MACA;MACA,KAAAN,WAAA,CAAAiB,SAAA,QAAAjB,WAAA,CAAAiB,SAAA;MACA,KAAAqJ,WAAA;MAEA,SAAAtK,WAAA,CAAAiB,SAAA;QACA,KAAA8K,QAAA,CAAA8G,IAAA;MACA;QACA,KAAA9G,QAAA,CAAA8G,IAAA;MACA;IACA;IACA,eACAC,cAAA,WAAAA,eAAAhJ,OAAA,EAAAiJ,eAAA;MACA,IAAApO,GAAA,OAAAtC,IAAA;MACA,IAAAK,GAAA,OAAAL,IAAA,CAAAyH,OAAA;MACA,IAAAkJ,MAAA,GAAAD,eAAA,OAAA1Q,IAAA,CAAA0Q,eAAA;;MAEA;MACA,IAAAE,SAAA,GAAAC,IAAA,CAAAC,IAAA,EAAAzQ,GAAA,GAAAiC,GAAA;;MAEA;MACA,IAAAqO,MAAA,IAAArO,GAAA,GAAAqO,MAAA;QACA;MACA;MAEA,IAAAC,SAAA;QACA;MACA,WAAAA,SAAA;QACA;MACA,WAAAA,SAAA;QACA;MACA;QACA;MACA;IACA;IACA,eACAG,cAAA,WAAAA,eAAAtJ,OAAA,EAAAiJ,eAAA;MACA,IAAApO,GAAA,OAAAtC,IAAA;MACA,IAAAK,GAAA,OAAAL,IAAA,CAAAyH,OAAA;MACA,IAAAkJ,MAAA,GAAAD,eAAA,OAAA1Q,IAAA,CAAA0Q,eAAA;MACA;MACA,IAAAE,SAAA,GAAAC,IAAA,CAAAC,IAAA,EAAAzQ,GAAA,GAAAiC,GAAA;MACA;MACA,IAAAqO,MAAA,IAAArO,GAAA,GAAAqO,MAAA;QACA;MACA;MAEA,IAAAC,SAAA;QACA;MACA,WAAAA,SAAA;QACA;MACA,WAAAA,SAAA;QACA;MACA,WAAAA,SAAA;QACA;MACA;QACA;MACA;IACA;IACA,iBACAI,uBAAA,WAAAA,wBAAA;MACA,KAAA/K,sBAAA;MACA,KAAAyD,QAAA,CAAAK,OAAA;IACA;IACA,aACAkH,cAAA,WAAAA,eAAA7K,GAAA;MACA,IAAA9D,GAAA,OAAAtC,IAAA;MACA,IAAAyH,OAAA,OAAAzH,IAAA,CAAAoG,GAAA,CAAAqB,OAAA;MACA,IAAA7I,SAAA;MACA,IAAAsS,WAAA;MACA;MACA,IAAA9K,GAAA,CAAAnI,gBAAA;QACA;QACA,IAAAmI,GAAA,CAAA/H,gBAAA;UACA,IAAAA,gBAAA,OAAA2B,IAAA,CAAAoG,GAAA,CAAA/H,gBAAA;UACA,IAAAA,gBAAA,GAAAoJ,OAAA;YACA7I,SAAA;YACAsS,WAAA,GAAAL,IAAA,CAAAC,IAAA,EAAAzS,gBAAA,GAAAoJ,OAAA;UACA;QACA;MACA,WAAArB,GAAA,CAAAnI,gBAAA;QACA;QACA,IAAAqE,GAAA,GAAAmF,OAAA;UACA7I,SAAA;UACAsS,WAAA,GAAAL,IAAA,CAAAC,IAAA,EAAAxO,GAAA,GAAAmF,OAAA;QACA;MACA,WAAArB,GAAA,CAAAnI,gBAAA;QACA;QACA,IAAAqE,GAAA,GAAAmF,OAAA;UACA7I,SAAA;UACAsS,WAAA,GAAAL,IAAA,CAAAC,IAAA,EAAAxO,GAAA,GAAAmF,OAAA;QACA;MACA;MAEA;QACA7I,SAAA,EAAAA,SAAA;QACAsS,WAAA,EAAAA;MACA;IACA;IAEA;IAEA;IACAC,qBAAA,WAAAA,sBAAA/K,GAAA;MACA,KAAA9E,qBAAA,GAAA8E,GAAA;MACA,KAAA7E,eAAA,GAAA6E,GAAA;MACA,KAAAlF,mBAAA;MACA,KAAAkQ,aAAA,CAAAhL,GAAA,CAAAzF,EAAA;IACA;IAEA,aACAyQ,aAAA,WAAAA,cAAAC,qBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA9D,kBAAA,CAAA9G,OAAA,mBAAA+G,oBAAA,CAAA/G,OAAA,IAAAgH,IAAA,UAAA6D,SAAA;QAAA,IAAAC,oBAAA,EAAAC,eAAA,EAAAC,UAAA;QAAA,WAAAjE,oBAAA,CAAA/G,OAAA,IAAAmH,IAAA,UAAA8D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5D,IAAA,GAAA4D,SAAA,CAAA3D,IAAA;YAAA;cAAA2D,SAAA,CAAA5D,IAAA;cAAA4D,SAAA,CAAA3D,IAAA;cAAA,OAGA,IAAA4D,oCAAA,EAAAR,qBAAA;YAAA;cAAAG,oBAAA,GAAAI,SAAA,CAAAxD,IAAA;cACAkD,OAAA,CAAAnQ,YAAA,GAAAqQ,oBAAA,CAAA1U,IAAA;;cAEA;cAAA8U,SAAA,CAAA3D,IAAA;cAAA,OACA,IAAA6D,wCAAA,EAAAT,qBAAA;YAAA;cAAAI,eAAA,GAAAG,SAAA,CAAAxD,IAAA;cACAsD,UAAA,GAAAD,eAAA,CAAA3U,IAAA,QAEA;cACAwU,OAAA,CAAAlQ,cAAA,GAAAsQ,UAAA,CAAAK,MAAA,WAAAC,KAAA;gBAAA,OAAAA,KAAA,CAAAC,cAAA;cAAA;;cAEA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;;cAEA;cAAA,KACAX,OAAA,CAAAnQ,YAAA;gBAAAyQ,SAAA,CAAA3D,IAAA;gBAAA;cAAA;cAAA2D,SAAA,CAAA3D,IAAA;cAAA,OACAqD,OAAA,CAAAY,2BAAA,CAAAZ,OAAA,CAAAnQ,YAAA,CAAAR,EAAA;YAAA;cAAAiR,SAAA,CAAA3D,IAAA;cAAA;YAAA;cAAA2D,SAAA,CAAA5D,IAAA;cAAA4D,SAAA,CAAArD,EAAA,GAAAqD,SAAA;cAGApD,OAAA,CAAApE,KAAA,cAAAwH,SAAA,CAAArD,EAAA;cACA+C,OAAA,CAAA5H,QAAA,CAAAU,KAAA;YAAA;YAAA;cAAA,OAAAwH,SAAA,CAAAnD,IAAA;UAAA;QAAA,GAAA8C,QAAA;MAAA;IAEA;IAEA,kBACAW,2BAAA,WAAAA,4BAAAC,OAAA;MAAA,IAAAC,OAAA;MAAA,WAAA5E,kBAAA,CAAA9G,OAAA,mBAAA+G,oBAAA,CAAA/G,OAAA,IAAAgH,IAAA,UAAA2E,SAAA;QAAA,IAAAxM,QAAA;QAAA,WAAA4H,oBAAA,CAAA/G,OAAA,IAAAmH,IAAA,UAAAyE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvE,IAAA,GAAAuE,SAAA,CAAAtE,IAAA;YAAA;cAAAsE,SAAA,CAAAvE,IAAA;cAAAuE,SAAA,CAAAtE,IAAA;cAAA,OAEA,IAAAuE,4CAAA,EAAAL,OAAA;YAAA;cAAAtM,QAAA,GAAA0M,SAAA,CAAAnE,IAAA;cACAgE,OAAA,CAAA/Q,uBAAA,GAAAwE,QAAA,CAAA/I,IAAA;cAAAyV,SAAA,CAAAtE,IAAA;cAAA;YAAA;cAAAsE,SAAA,CAAAvE,IAAA;cAAAuE,SAAA,CAAAhE,EAAA,GAAAgE,SAAA;cAEA/D,OAAA,CAAApE,KAAA,cAAAmI,SAAA,CAAAhE,EAAA;YAAA;YAAA;cAAA,OAAAgE,SAAA,CAAA9D,IAAA;UAAA;QAAA,GAAA4D,QAAA;MAAA;IAEA;IAEA,YACAI,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MAAA,WAAAlF,kBAAA,CAAA9G,OAAA,mBAAA+G,oBAAA,CAAA/G,OAAA,IAAAgH,IAAA,UAAAiF,SAAA;QAAA,IAAA9M,QAAA;QAAA,WAAA4H,oBAAA,CAAA/G,OAAA,IAAAmH,IAAA,UAAA+E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7E,IAAA,GAAA6E,SAAA,CAAA5E,IAAA;YAAA;cAAA,IAEAyE,OAAA,CAAA3N,YAAA;gBAAA8N,SAAA,CAAA5E,IAAA;gBAAA;cAAA;cACAyE,OAAA,CAAAhJ,QAAA,CAAAC,OAAA;cAAA,OAAAkJ,SAAA,CAAAC,MAAA;YAAA;cAAAD,SAAA,CAAA7E,IAAA;cAAA6E,SAAA,CAAA5E,IAAA;cAAA,OAKAyE,OAAA,CAAArJ,QAAA;gBACAuB,iBAAA;gBACAC,gBAAA;gBACAC,IAAA;cACA;YAAA;cAAA+H,SAAA,CAAA5E,IAAA;cAAA,OAEA,IAAAwE,kCAAA,EAAAC,OAAA,CAAApR,qBAAA,CAAAX,EAAA;YAAA;cAAAkF,QAAA,GAAAgN,SAAA,CAAAzE,IAAA;cACAsE,OAAA,CAAAhJ,QAAA,CAAAK,OAAA;;cAEA;cAAA8I,SAAA,CAAA5E,IAAA;cAAA,OACAyE,OAAA,CAAAtB,aAAA,CAAAsB,OAAA,CAAApR,qBAAA,CAAAX,EAAA;YAAA;cAEA;cACA+R,OAAA,CAAAlN,OAAA;cAAAqN,SAAA,CAAA5E,IAAA;cAAA;YAAA;cAAA4E,SAAA,CAAA7E,IAAA;cAAA6E,SAAA,CAAAtE,EAAA,GAAAsE,SAAA;cAEA,IAAAA,SAAA,CAAAtE,EAAA;gBACAmE,OAAA,CAAAhJ,QAAA,CAAAU,KAAA,gBAAAyI,SAAA,CAAAtE,EAAA,CAAAtP,OAAA,IAAA4T,SAAA,CAAAtE,EAAA;cACA;YAAA;YAAA;cAAA,OAAAsE,SAAA,CAAApE,IAAA;UAAA;QAAA,GAAAkE,QAAA;MAAA;IAEA;IAEA,aACAI,kBAAA,WAAAA,mBAAA;MACA;MACA,UAAAhO,YAAA;QACA,KAAA2E,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAA3H,eAAA;QACAC,iBAAA;QACAC,MAAA;MACA;MACA,KAAAH,eAAA;IACA;IAEA,aACAiR,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAzF,kBAAA,CAAA9G,OAAA,mBAAA+G,oBAAA,CAAA/G,OAAA,IAAAgH,IAAA,UAAAwF,SAAA;QAAA,WAAAzF,oBAAA,CAAA/G,OAAA,IAAAmH,IAAA,UAAAsF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApF,IAAA,GAAAoF,SAAA,CAAAnF,IAAA;YAAA;cAAAmF,SAAA,CAAApF,IAAA;cAAAoF,SAAA,CAAAnF,IAAA;cAAA,OAEA,IAAA8E,uCAAA,EACAE,OAAA,CAAA3R,qBAAA,CAAAX,EAAA,EACAsS,OAAA,CAAAjR,eAAA,CAAAC,iBAAA,EACAgR,OAAA,CAAAjR,eAAA,CAAAE,MACA;YAAA;cAEA+Q,OAAA,CAAAvJ,QAAA,CAAAK,OAAA;cACAkJ,OAAA,CAAAlR,eAAA;;cAEA;cAAAqR,SAAA,CAAAnF,IAAA;cAAA,OACAgF,OAAA,CAAA7B,aAAA,CAAA6B,OAAA,CAAA3R,qBAAA,CAAAX,EAAA;YAAA;cAEA;cACAsS,OAAA,CAAAzN,OAAA;cAAA4N,SAAA,CAAAnF,IAAA;cAAA;YAAA;cAAAmF,SAAA,CAAApF,IAAA;cAAAoF,SAAA,CAAA7E,EAAA,GAAA6E,SAAA;cAEA5E,OAAA,CAAApE,KAAA,YAAAgJ,SAAA,CAAA7E,EAAA;cACA0E,OAAA,CAAAvJ,QAAA,CAAAU,KAAA,eAAAgJ,SAAA,CAAA7E,EAAA,CAAAtP,OAAA,IAAAmU,SAAA,CAAA7E,EAAA;YAAA;YAAA;cAAA,OAAA6E,SAAA,CAAA3E,IAAA;UAAA;QAAA,GAAAyE,QAAA;MAAA;IAEA;IAEA,aACAG,aAAA,WAAAA,cAAA;MACA;MACA,UAAAtO,YAAA;QACA,KAAA2E,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAAhI,cAAA;QACAC,cAAA;QACAC,cAAA;MACA;MACA,KAAAH,iBAAA;IACA;IAEA,aACA4R,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA/F,kBAAA,CAAA9G,OAAA,mBAAA+G,oBAAA,CAAA/G,OAAA,IAAAgH,IAAA,UAAA8F,SAAA;QAAA,IAAAC,gBAAA;QAAA,WAAAhG,oBAAA,CAAA/G,OAAA,IAAAmH,IAAA,UAAA6F,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3F,IAAA,GAAA2F,SAAA,CAAA1F,IAAA;YAAA;cAAA,IAEAsF,OAAA,CAAAxO,YAAA;gBAAA4O,SAAA,CAAA1F,IAAA;gBAAA;cAAA;cACAsF,OAAA,CAAA7J,QAAA,CAAAC,OAAA;cAAA,OAAAgK,SAAA,CAAAb,MAAA;YAAA;cAAAa,SAAA,CAAA1F,IAAA;cAAA,OAIAsF,OAAA,CAAA1K,KAAA,CAAAlH,cAAA,CAAAmH,QAAA;YAAA;cAEA2K,gBAAA;gBACAtB,OAAA,EAAAoB,OAAA,CAAApS,YAAA,CAAAR,EAAA;gBACAiB,cAAA,EAAA2R,OAAA,CAAA5R,cAAA,CAAAC,cAAA;gBACAC,cAAA,EAAA0R,OAAA,CAAA5R,cAAA,CAAAE,cAAA;cACA;cAAA8R,SAAA,CAAA1F,IAAA;cAAA,OAEA,IAAA2F,yCAAA,EAAAH,gBAAA;YAAA;cAEAF,OAAA,CAAA7J,QAAA,CAAAK,OAAA;cACAwJ,OAAA,CAAA7R,iBAAA;;cAEA;cAAAiS,SAAA,CAAA1F,IAAA;cAAA,OACAsF,OAAA,CAAArB,2BAAA,CAAAqB,OAAA,CAAApS,YAAA,CAAAR,EAAA;YAAA;YAAA;cAAA,OAAAgT,SAAA,CAAAlF,IAAA;UAAA;QAAA,GAAA+E,QAAA;MAAA;IACA;IAEA,aACAK,eAAA,WAAAA,gBAAA7B,KAAA;MAAA,IAAA8B,OAAA;MAAA,WAAAtG,kBAAA,CAAA9G,OAAA,mBAAA+G,oBAAA,CAAA/G,OAAA,IAAAgH,IAAA,UAAAqG,SAAA;QAAA,IAAAlO,QAAA,EAAAmO,WAAA;QAAA,WAAAvG,oBAAA,CAAA/G,OAAA,IAAAmH,IAAA,UAAAoG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlG,IAAA,GAAAkG,SAAA,CAAAjG,IAAA;YAAA;cAAAiG,SAAA,CAAAlG,IAAA;cAAAkG,SAAA,CAAAjG,IAAA;cAAA,OAEA,IAAAuE,4CAAA,EAAAR,KAAA,CAAArR,EAAA;YAAA;cAAAkF,QAAA,GAAAqO,SAAA,CAAA9F,IAAA;cACA4F,WAAA,GAAAnO,QAAA,CAAA/I,IAAA,QAEA;cACAgX,OAAA,CAAArS,eAAA,OAAAgF,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAsL,KAAA;gBACAgC,WAAA,EAAAA;cAAA,EACA;cAEAF,OAAA,CAAAtS,eAAA;cAAA0S,SAAA,CAAAjG,IAAA;cAAA;YAAA;cAAAiG,SAAA,CAAAlG,IAAA;cAAAkG,SAAA,CAAA3F,EAAA,GAAA2F,SAAA;cAEA1F,OAAA,CAAApE,KAAA,cAAA8J,SAAA,CAAA3F,EAAA;cACAuF,OAAA,CAAApK,QAAA,CAAAU,KAAA;YAAA;YAAA;cAAA,OAAA8J,SAAA,CAAAzF,IAAA;UAAA;QAAA,GAAAsF,QAAA;MAAA;IAEA;IAEA,aACAI,iBAAA,WAAAA,kBAAAC,SAAA;MACA,KAAAA,SAAA;MAEA,IAAA9T,KAAA,OAAAN,IAAA,CAAAoU,SAAA;MACA,IAAA9R,GAAA,OAAAtC,IAAA;MACA,IAAAqU,MAAA,GAAA/R,GAAA,GAAAhC,KAAA;MACA,IAAAgU,SAAA,GAAAzD,IAAA,CAAA0D,KAAA,CAAAF,MAAA;MACA,IAAAG,WAAA,GAAA3D,IAAA,CAAA0D,KAAA,CAAAF,MAAA;MAEA,IAAAC,SAAA;QACA,UAAAtK,MAAA,CAAAsK,SAAA,kBAAAtK,MAAA,CAAAwK,WAAA;MACA;QACA,UAAAxK,MAAA,CAAAwK,WAAA;MACA;IACA;IAEA,eACAC,wBAAA,WAAAA,yBAAAC,UAAA;MACA,KAAAA,UAAA;MAEA,IAAAC,SAAA,GAAAD,UAAA,CAAAE,WAAA;MACA,IAAAD,SAAA,CAAAE,QAAA,UAAAF,SAAA,CAAAE,QAAA,UAAAF,SAAA,CAAAE,QAAA;QACA;MACA,WAAAF,SAAA,CAAAE,QAAA,UAAAF,SAAA,CAAAE,QAAA;QACA;MACA,WAAAF,SAAA,CAAAE,QAAA,SAAAF,SAAA,CAAAE,QAAA,WAAAF,SAAA,CAAAE,QAAA;QACA;MACA;QACA;MACA;IACA;IAEA,qBACAC,oBAAA,WAAAA,qBAAA;MACA;MACA,KAAA3T,YAAA;MACA,KAAAC,cAAA;MACA,KAAAC,uBAAA;MACA,KAAAC,qBAAA;MACA,KAAAC,eAAA;MACA,KAAAE,eAAA;;MAEA;MACA,KAAAE,cAAA;QACAC,cAAA;QACAC,cAAA;MACA;MACA,KAAAG,eAAA;QACAC,iBAAA;QACAC,MAAA;MACA;;MAEA;MACA,KAAAV,eAAA;MACA,KAAAE,iBAAA;MACA,KAAAK,eAAA;IACA;IAEA,qBACAgT,gBAAA,WAAAA,iBAAA;MACA;MACA,KAAAtT,eAAA;IACA;IAEA,qBACAuT,kBAAA,WAAAA,mBAAA;MACA;MACA,KAAArT,cAAA;QACAC,cAAA;QACAC,cAAA;MACA;MACA;MACA,SAAAgH,KAAA,CAAAlH,cAAA;QACA,KAAAkH,KAAA,CAAAlH,cAAA,CAAAuK,aAAA;MACA;IACA;IAEA,qBACA+I,gBAAA,WAAAA,iBAAA;MACA;MACA,KAAAjT,eAAA;QACAC,iBAAA;QACAC,MAAA;MACA;MACA;MACA,SAAA2G,KAAA,CAAA7G,eAAA;QACA,KAAA6G,KAAA,CAAA7G,eAAA,CAAAkK,aAAA;MACA;IACA;IAEA,aACAgJ,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA3H,kBAAA,CAAA9G,OAAA,mBAAA+G,oBAAA,CAAA/G,OAAA,IAAAgH,IAAA,UAAA0H,SAAA;QAAA,WAAA3H,oBAAA,CAAA/G,OAAA,IAAAmH,IAAA,UAAAwH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtH,IAAA,GAAAsH,SAAA,CAAArH,IAAA;YAAA;cAAA,KACAkH,OAAA,CAAA7T,qBAAA;gBAAAgU,SAAA,CAAArH,IAAA;gBAAA;cAAA;cAAAqH,SAAA,CAAArH,IAAA;cAAA,OACAkH,OAAA,CAAA/D,aAAA,CAAA+D,OAAA,CAAA7T,qBAAA,CAAAX,EAAA;YAAA;cACAwU,OAAA,CAAAzL,QAAA,CAAAK,OAAA;YAAA;YAAA;cAAA,OAAAuL,SAAA,CAAA7G,IAAA;UAAA;QAAA,GAAA2G,QAAA;MAAA;IAEA;IAEA,gBACApJ,sBAAA,WAAAA,uBAAAqF,qBAAA;MAAA,IAAAkE,OAAA;MAAA,WAAA/H,kBAAA,CAAA9G,OAAA,mBAAA+G,oBAAA,CAAA/G,OAAA,IAAAgH,IAAA,UAAA8H,SAAA;QAAA,IAAA3P,QAAA;QAAA,WAAA4H,oBAAA,CAAA/G,OAAA,IAAAmH,IAAA,UAAA4H,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1H,IAAA,GAAA0H,SAAA,CAAAzH,IAAA;YAAA;cAAAyH,SAAA,CAAA1H,IAAA;cAAA0H,SAAA,CAAAzH,IAAA;cAAA,OAEA,IAAA0H,+CAAA,EAAAtE,qBAAA;YAAA;cAAAxL,QAAA,GAAA6P,SAAA,CAAAtH,IAAA;cACAmH,OAAA,CAAAtS,kBAAA,GAAA4C,QAAA,CAAA/I,IAAA;cAAA4Y,SAAA,CAAAzH,IAAA;cAAA;YAAA;cAAAyH,SAAA,CAAA1H,IAAA;cAAA0H,SAAA,CAAAnH,EAAA,GAAAmH,SAAA;cAEAlH,OAAA,CAAApE,KAAA,iBAAAsL,SAAA,CAAAnH,EAAA;cACAgH,OAAA,CAAAtS,kBAAA;cACAsS,OAAA,CAAA7L,QAAA,CAAAU,KAAA;YAAA;YAAA;cAAA,OAAAsL,SAAA,CAAAjH,IAAA;UAAA;QAAA,GAAA+G,QAAA;MAAA;IAEA;EACA;AACA", "ignoreList": []}]}