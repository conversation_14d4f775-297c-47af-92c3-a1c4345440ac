{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\engineerSampleOrder\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\engineerSampleOrder\\index.vue", "mtime": 1753927608484}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\babel.config.js", "mtime": 1743382537964}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_engineer<PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "_projectItemOrder", "_vueTreeselect", "_interopRequireDefault", "_customer", "_validate", "_executionAddOrEdit", "name", "components", "Treeselect", "executionAddOrEdit", "data", "loading", "ids", "selectedRows", "single", "multiple", "showSearch", "total", "engineer<PERSON>ampleOrderList", "researchDeptDatas", "dylbOptions", "title", "open", "queryParams", "pageNum", "pageSize", "userId", "nick<PERSON><PERSON>", "sampleOrderCode", "completionStatus", "scheduledDate", "startDate", "actualStartTime", "actualFinishTime", "deptId", "deptIds", "associationStatus", "customerId", "productName", "confirmCode", "isOverdue", "laboratory", "form", "rules", "required", "message", "trigger", "statusOptions", "serviceModeOptions", "date<PERSON><PERSON><PERSON>", "scheduledDateRange", "startDateRange", "actualStartTimeRange", "actualFinishTimeRange", "dataPickerOptions", "shortcuts", "text", "onClick", "picker", "today", "Date", "$emit", "yesterday", "setTime", "getTime", "end", "start", "statusOpen", "dashboardStats", "changeEngineerOpen", "changeEngineerForm", "id", "currentEngineerName", "oldEngineerId", "newEngineerId", "adjustWorkSchedule", "changeEngineerRules", "engineerOptions", "batchManagementOpen", "currentBatch", "historyBatches", "currentBatchExperiments", "selectedOrderForBatch", "currentBatchRow", "batchDetailOpen", "batchDetailData", "addExperimentOpen", "experimentForm", "experimentCode", "experimentNote", "experimentRules", "finishBatchOpen", "finishBatchForm", "qualityEvaluation", "remark", "futureDatePickerOptions", "disabledDate", "time", "now", "engineerSelectType", "searchedEngineers", "unassignedOrders", "unassigned<PERSON>ueryP<PERSON><PERSON>", "unassignedTotal", "isUnassignedPanelCollapsed", "finishTaskOpen", "finishTaskLoading", "finishTaskForm", "laboratoryCode", "finishTaskRules", "currentFinishRow", "updateLaboratoryCodeOpen", "updateLaboratoryCodeLoading", "updateLaboratoryCodeForm", "updateLaboratoryCodeRules", "currentUpdateLaboratoryCodeRow", "exportDialogOpen", "exportOptions", "exportLoading", "readonly", "currentProjectType", "confirmItemCodes", "customerOptions", "itemNames", "rejectDialogOpen", "rejectLoading", "rejectForm", "rejectReason", "overdueOperationOpen", "overdueOperationLoading", "overdueOperationForm", "expectedSampleTime", "reasonForNoSample", "solution", "currentOverdueRow", "rejectRules", "currentRejectRow", "computed", "canEditBatch", "created", "_this", "todayStr", "getFullYear", "String", "getMonth", "padStart", "getDate", "getList", "customerBaseAll", "then", "res", "getDicts", "response", "loadDashboardStats", "getResearchDepartments", "handleTree", "handleUnassignedOrders", "methods", "getProjectLevel", "row", "customerLevel", "projectLevel", "_this2", "params", "_objectSpread2", "default", "addDateRange", "beginDateRange", "endDateRange", "length", "listEngineerSampleOrder", "rows", "cancel", "reset", "serviceMode", "difficultyLevelId", "actualManHours", "estimatedManHours", "standardManHours", "isLocked", "endDate", "checkType", "sampleOrderRemark", "createBy", "createTime", "updateBy", "updateTime", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "handleAdd", "handleUpdate", "_this3", "getEngineerSampleOrder", "submitForm", "_this4", "$refs", "validate", "valid", "updateEngineerSampleOrder", "msgSuccess", "addEngineerSampleOrder", "handleDelete", "_this5", "$confirm", "delEngineerSampleOrder", "catch", "handleExport", "confirmExport", "$message", "warning", "executeExports", "index", "_this6", "success", "concat", "option", "typeName", "doExport", "error", "exportType", "_this7", "exportEngineerSampleOrder", "download", "msg", "doExportSampleOrder", "_this8", "confirmButtonText", "cancelButtonText", "type", "handleStart", "_this9", "updateSampleOrderStatus", "status", "handleDownloadSampleOrder", "_this10", "exportNrwItem", "itemId", "projectId", "handleBatchExportNrw", "_this11", "$modal", "msgError", "exportData", "exportMultipleNrw", "handleFinish", "_this12", "$nextTick", "clearValidate", "confirmFinishTask", "_this13", "handleFinishFromBatch", "_this14", "handleUpdateLaboratoryCode", "_this15", "confirmUpdateLaboratoryCode", "_this16", "handleReject", "_this17", "confirmReject", "_this18", "handleOverdueOperation", "_this19", "confirmOverdueOperation", "_this20", "orderDetail", "_this21", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "orderId", "wrap", "_callee$", "_context", "prev", "next", "projectOrderId", "getExecutionByOrderInfo", "sent", "isNull", "show", "t0", "console", "stop", "handleLockChange", "value", "_this22", "handleTableLockChange", "_this23", "_this24", "getDashboardStats", "handleChangeEngineer", "_this25", "getEngineersByDifficultyLevel", "categoryId", "getResearchDepartmentsUser", "submitChangeEngineer", "_this26", "sampleOrderId", "changeEngineer", "_this27", "handleAssignEngineer", "_this28", "handleDeleteUnassigned", "_this29", "handleRejectUnassigned", "_this30", "handleUnassignedSizeChange", "newSize", "handleUnassignedCurrentChange", "newPage", "toggleUnassignedPanel", "handleStatusFilter", "handleOverdueFilter", "info", "getUrgencyType", "latestStartTime", "latest", "daysToEnd", "Math", "ceil", "getUrgencyText", "handleRefreshUnassigned", "getOverdueInfo", "overdueDays", "handleBatchManagement", "loadBatchData", "engineerSampleOrderId", "_this31", "_callee2", "currentBatchResponse", "batchesResponse", "allBatches", "_callee2$", "_context2", "getCurrentBatch", "getBatchesByOrderId", "filter", "batch", "isCurrentBatch", "loadCurrentBatchExperiments", "batchId", "_this32", "_callee3", "_callee3$", "_context3", "getExperimentsByBatchId", "startNewBatch", "_this33", "_callee4", "_callee4$", "_context4", "abrupt", "finishCurrentBatch", "submitFinishBatch", "_this34", "_callee5", "_callee5$", "_context5", "addExperiment", "submitExperiment", "_this35", "_callee6", "experimentRecord", "_callee6$", "_context6", "addExperimentToBatch", "viewBatchDetail", "_this36", "_callee7", "experiments", "_callee7$", "_context7", "calculateDuration", "startTime", "diffMs", "diffHours", "floor", "diffMinutes", "getQualityEvaluationType", "evaluation", "lowerEval", "toLowerCase", "includes", "closeBatchManagement", "closeBatchDetail", "closeAddExperiment", "closeFinishBatch", "refreshBatchData", "_this37", "_callee8", "_callee8$", "_context8"], "sources": ["src/views/software/engineerSampleOrder/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 统计概览 -->\r\n    <el-row :gutter=\"20\" class=\"stats-row\" style=\"margin-bottom: 20px;\">\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card total\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-s-order\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">总任务</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.total || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card completed\" @click.native=\"handleStatusFilter('2')\" :class=\"{'active': queryParams.completionStatus === '2'}\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-check\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">已完成</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.completed || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card in-progress\" @click.native=\"handleStatusFilter('1')\" :class=\"{'active': queryParams.completionStatus === '1'}\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-loading\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">进行中</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.inProgress || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card overdue\" @click.native=\"handleOverdueFilter\" :class=\"{'active': queryParams.isOverdue === 1}\" style=\"cursor: pointer;\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-warning\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">逾期任务</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.overdue || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 未分配工程师的打样单告警 -->\r\n    <el-card class=\"alert-card\">\r\n      <div slot=\"header\" class=\"alert-header\">\r\n        <div class=\"alert-title\" @click=\"toggleUnassignedPanel\">\r\n          <span><i class=\"el-icon-warning-outline\"></i>待分配打样单({{ unassignedTotal }}个)</span>\r\n        </div>\r\n        <div class=\"alert-actions\">\r\n          <el-button\r\n            type=\"text\"\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"handleRefreshUnassigned\"\r\n            style=\"margin-right: 10px\"\r\n            title=\"刷新列表\">\r\n            刷新\r\n          </el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            @click=\"toggleUnassignedPanel\"\r\n            style=\"margin-right: 10px\">\r\n            {{ isUnassignedPanelCollapsed ? '展开' : '收起' }}\r\n            <i :class=\"isUnassignedPanelCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\r\n          </el-button>\r\n          <el-pagination\r\n            @size-change=\"handleUnassignedSizeChange\"\r\n            @current-change=\"handleUnassignedCurrentChange\"\r\n            :current-page=\"unassignedQueryParams.pageNum\"\r\n            :page-sizes=\"[8, 16, 24, 32]\"\r\n            :page-size=\"unassignedQueryParams.pageSize\"\r\n            layout=\"total, sizes, prev, pager, next\"\r\n            :total=\"unassignedTotal\">\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n      <el-collapse-transition>\r\n        <div v-show=\"!isUnassignedPanelCollapsed\">\r\n          <el-row :gutter=\"20\" class=\"alert-cards\">\r\n            <el-col :xs=\"24\" :sm=\"12\" :md=\"8\" :lg=\"6\" v-for=\"item in unassignedOrders\" :key=\"item.id\">\r\n              <el-card shadow=\"hover\" class=\"alert-item\">\r\n                <div class=\"alert-item-header\">\r\n                  <div class=\"order-code-section\">\r\n                    <span style=\"color: #00afff;cursor: pointer\"  @click=\"orderDetail(item)\" class=\"order-code\">{{ item.sampleOrderCode }}</span>\r\n                    <el-tag\r\n                      :type=\"getUrgencyType(item.endDate, item.latestStartTime)\"\r\n                      size=\"mini\"\r\n                      class=\"urgency-tag\">\r\n                      {{ getUrgencyText(item.endDate, item.latestStartTime) }}\r\n                    </el-tag>\r\n                  </div>\r\n                </div>\r\n                <div class=\"alert-item-content\">\r\n                  <div class=\"info-section\">\r\n                    <!-- 预估工时和难度等级 -->\r\n                    <div class=\"info-row info-row-double\">\r\n                      <div class=\"info-item date-time-item\" v-if=\"item.latestStartTime\">\r\n                        <i class=\"el-icon-alarm-clock\" style=\"color: #909399;\"></i>\r\n                        <span class=\"info-label\">最晚开始:</span>\r\n                        <span class=\"info-value\">{{ parseTime(item.latestStartTime, '{y}-{m}-{d}') }}</span>\r\n                      </div>\r\n                      <div class=\"info-item standard-item\">\r\n                        <i class=\"el-icon-time\" style=\"color: #409EFF;\"></i>\r\n                        <span class=\"info-label\">预估工时:</span>\r\n                        <span class=\"info-value\">{{ item.estimatedManHours || '-' }}H</span>\r\n                      </div>\r\n                    </div>\r\n                    <!-- 最晚开始日期和截止日期排 -->\r\n                    <div class=\"info-row info-row-double\">\r\n                      <div class=\"info-item date-time-item\">\r\n                        <i class=\"el-icon-date\" style=\"color: #F56C6C;\"></i>\r\n                        <span class=\"info-label\">截止日期:</span>\r\n                        <span class=\"info-value\">{{ parseTime(item.endDate, '{y}-{m}-{d}') }}</span>\r\n                      </div>\r\n                      <div class=\"info-item standard-item\">\r\n                        <i class=\"el-icon-star-on\" style=\"color: #E6A23C;\"></i>\r\n                        <span class=\"info-label\">难度等级:</span>\r\n                        <el-tooltip :content=\"selectDictLabel(dylbOptions, item.difficultyLevelId)\" placement=\"top\">\r\n                          <span class=\"info-value difficulty-text\">{{selectDictLabel(dylbOptions,item.difficultyLevelId).replace(/[^a-zA-Z0-9]/g, '')}}</span>\r\n                        </el-tooltip>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <el-tooltip v-if=\"item.failureReason\" :content=\"item.failureReason\" placement=\"top\">\r\n                    <div class=\"info-reason\">\r\n                      <i class=\"el-icon-warning-outline\"></i>\r\n                      <span class=\"reason-text\">{{ item.failureReason }}</span>\r\n                    </div>\r\n                  </el-tooltip>\r\n                </div>\r\n                <div class=\"alert-item-footer\">\r\n                  <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-user-solid\" @click=\"handleAssignEngineer(item)\" v-hasPermi=\"['software:engineerSampleOrder:changeEngineer']\">\r\n                    分配工程师\r\n                  </el-button>\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-close\" @click=\"handleRejectUnassigned(item)\" v-hasPermi=\"['software:engineerSampleOrder:rejectRask']\" class=\"reject-btn\" style=\"color: #F56C6C;\">\r\n                    驳回\r\n                  </el-button>\r\n                </div>\r\n              </el-card>\r\n           </el-col>\r\n          </el-row>\r\n         </div>\r\n      </el-collapse-transition>\r\n    </el-card>\r\n\r\n    <!-- 筛选条件 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"客户\" prop=\"customerId\">\r\n        <el-select v-model=\"queryParams.customerId\" filterable placeholder=\"客户\" clearable size=\"small\">\r\n          <el-option\r\n            v-for=\"dict in customerOptions\"\r\n            :key=\"dict.id\"\r\n            :label=\"dict.name\"\r\n            :value=\"dict.id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"日期范围\" prop=\"dateRange\" label-width=\"80px\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"\r\n          style=\"width: 240px\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"排单日期\" prop=\"scheduledDate\">\r\n        <el-date-picker\r\n          v-model=\"scheduledDateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"申请日期\" prop=\"startDate\">\r\n        <el-date-picker\r\n          v-model=\"startDateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"研发组别\" prop=\"deptIds\">\r\n        <el-select v-model=\"queryParams.deptIds\" multiple filterable placeholder=\"请选择对应研发组别\">\r\n          <el-option\r\n            v-for=\"dept in researchDeptDatas\"\r\n            :key=\"dept.deptId\"\r\n            :label=\"dept.deptName\"\r\n            :value=\"dept.deptId\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"服务模式\" prop=\"serviceMode\">\r\n        <el-select v-model=\"queryParams.serviceMode\" placeholder=\"请选择客户服务模式\" clearable>\r\n          <el-option\r\n            v-for=\"dict in serviceModeOptions\"\r\n            :key=\"dict.dictValue\"\r\n            :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"实验室\" prop=\"laboratory\">\r\n        <el-select v-model=\"queryParams.laboratory\" placeholder=\"请选择实验室\" clearable>\r\n          <el-option label=\"宜侬\" value=\"0\" />\r\n          <el-option label=\"瀛彩\" value=\"1\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"确认编码\" prop=\"confirmCode\">\r\n        <el-input\r\n          v-model=\"queryParams.confirmCode\"\r\n          placeholder=\"请输入确认编码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"产品名称\" prop=\"productName\">\r\n        <el-input\r\n          v-model=\"queryParams.productName\"\r\n          placeholder=\"请输入产品名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"姓名\" prop=\"nickName\" label-width=\"50px\">\r\n        <el-input v-model=\"queryParams.nickName\" placeholder=\"请输入工程师姓名\" clearable/>\r\n      </el-form-item>\r\n      <el-form-item label=\"打样单编号\" label-width=\"90px\" prop=\"sampleOrderCode\">\r\n        <el-input v-model=\"queryParams.sampleOrderCode\" placeholder=\"请输入打样单编号\" clearable/>\r\n      </el-form-item>\r\n      <el-form-item label=\"完成情况\" prop=\"completionStatus\">\r\n        <el-select v-model=\"queryParams.completionStatus\" placeholder=\"请选择完成情况\" clearable>\r\n          <el-option\r\n            v-for=\"dict in statusOptions\"\r\n            :key=\"dict.dictValue\"\r\n            :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"实际开始时间\" prop=\"actualStartTime\" label-width=\"100px\">\r\n        <el-date-picker\r\n          v-model=\"actualStartTimeRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"实际完成时间\" prop=\"actualFinishTime\" label-width=\"100px\">\r\n        <el-date-picker\r\n          v-model=\"actualFinishTimeRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作按钮 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:add']\">新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" plain icon=\"el-icon-edit\" size=\"mini\" :disabled=\"single\" @click=\"handleUpdate\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:edit']\">修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:remove']\">删除</el-button>\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"doExportSampleOrder\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:export']\">导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" plain icon=\"el-icon-download\" size=\"mini\" :disabled=\"multiple\" @click=\"handleBatchExportNrw\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:export']\">导出打样单任务</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 主要内容区域 -->\r\n     <!-- 工单列表 -->\r\n    <el-table v-loading=\"loading\" :data=\"engineerSampleOrderList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <!-- <el-table-column label=\"主键ID\" align=\"center\" prop=\"id\" /> -->\r\n<!--      <el-table-column label=\"工程师ID\" align=\"center\" prop=\"userId\" />-->\r\n      <el-table-column align=\"center\" prop=\"sampleOrderCode\" width=\"160\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          打样单编号\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              点击编号可查看详细信息\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"orderDetail(scope.row)\">{{ scope.row.sampleOrderCode }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"startDate\" width=\"140\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          申请日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              客户提交打样申请的日期时间\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d} {h}:{i}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"scheduledDate\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          排单日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              工程师被安排处理此打样单的日期\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.scheduledDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"latestStartTime\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          最晚开始日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              必须在此日期前开始工作，否则可能影响交期\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.latestStartTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"endDate\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          最晚截至日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              打样工作必须在此日期前完成\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\" prop=\"completionStatus\">\r\n        <template slot=\"header\">\r\n          完成情况\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              当前打样单的状态\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"scope.row.completionStatus == '0' ? 'info' :\r\n                   scope.row.completionStatus == '1' ? 'primary' :\r\n                   scope.row.completionStatus == '2' ? 'success' : 'info'\"\r\n            size=\"mini\"\r\n          >\r\n            {{selectDictLabel(statusOptions,scope.row.completionStatus)}}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\">\r\n        <template slot=\"header\">\r\n          批次信息\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              显示当前批次进度，格式：当前批次/总批次\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"scope.row.totalBatches > 0\">\r\n            <el-tag size=\"mini\" type=\"primary\">\r\n              {{ scope.row.currentBatch || 0 }}/{{ scope.row.totalBatches || 0 }}\r\n            </el-tag>\r\n          </div>\r\n          <div v-else>\r\n            <el-tag size=\"mini\" type=\"info\">未开始</el-tag>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\">\r\n        <template slot=\"header\">\r\n          逾期情况\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              根据截止日期判断是否逾期及逾期天数\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"getOverdueInfo(scope.row).isOverdue\">\r\n            <el-tag type=\"danger\" size=\"mini\" style=\"margin-bottom: 2px;\">\r\n              逾期 {{ getOverdueInfo(scope.row).overdueDays }}天\r\n            </el-tag>\r\n          </div>\r\n          <el-tag v-else type=\"success\" size=\"mini\">\r\n            正常\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"nickName\" width=\"95\">\r\n        <template slot=\"header\">\r\n          跟进人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此打样单的工程师姓名及职级\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.nickName }}{{ scope.row.rank ? '-' + scope.row.rank : '' }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"assistantName\" width=\"95\">\r\n        <template slot=\"header\">\r\n          协助人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              此打样单难度高于工程师能力范围\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.assistantName }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"isLocked\" width=\"90\">\r\n        <template slot=\"header\">\r\n          锁定状态\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              锁定后不允许更换工程师，防止误操作\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-switch\r\n            v-model=\"scope.row.isLocked\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n            @change=\"(val) => handleTableLockChange(val, scope.row)\">\r\n          </el-switch>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"laboratoryCode\" width=\"150\">\r\n        <template slot=\"header\">\r\n          实验编码\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              实验室分配的唯一编码标识\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"difficultyLevelId\" width=\"80\">\r\n        <template slot=\"header\">\r\n          难度\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              打样工作的技术难度等级\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"selectDictLabel(dylbOptions,scope.row.difficultyLevelId)\" placement=\"top\">\r\n            <span>{{selectDictLabel(dylbOptions,scope.row.difficultyLevelId).replace(/[^a-zA-Z0-9]/g, '')}}</span>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"标准工时\" align=\"center\" prop=\"standardManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.standardManHours\">{{ scope.row.standardManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"laboratory\" width=\"90\">\r\n        <template slot=\"header\">\r\n          实验室\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此打样单的实验室分支\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{\r\n            scope.row.laboratory === '0' ? '宜侬' :\r\n            scope.row.laboratory === '1' ? '瀛彩' :\r\n            ''\r\n          }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"categoryName\" width=\"70\">\r\n        <template slot=\"header\">\r\n          品类\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              产品所属的分类类别\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"SKU数\" align=\"center\" prop=\"sku\" width=\"70\" />\r\n      <el-table-column align=\"center\" prop=\"applicant\" width=\"90\">\r\n        <template slot=\"header\">\r\n          申请人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              提交此打样申请的人员\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"sales\" width=\"70\">\r\n        <template slot=\"header\">\r\n          销售\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此项目的销售人员\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"customerName\" width=\"150\">\r\n        <template slot=\"header\">\r\n          客户名称\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              委托打样的客户公司名称\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"产品/项目等级\" align=\"center\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getProjectLevel(scope.row) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"productName\" width=\"150\">\r\n        <template slot=\"header\">\r\n          产品名称\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              需要打样的产品名称\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"实际工作时间\" align=\"center\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div v-if=\"scope.row.actualStartTime\">开始: {{ parseTime(scope.row.actualStartTime, '{y}-{m}-{d} {h}:{i}') }}</div>\r\n            <div v-if=\"scope.row.actualFinishTime\">完成: {{ parseTime(scope.row.actualFinishTime, '{y}-{m}-{d} {h}:{i}') }}</div>\r\n            <span v-if=\"!scope.row.actualStartTime && !scope.row.actualFinishTime\">-</span>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"实际工时\" align=\"center\" prop=\"actualManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.actualManHours\">{{ scope.row.actualManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"预估工时\" align=\"center\" prop=\"estimatedManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.estimatedManHours\">{{ scope.row.estimatedManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"服务模式\" align=\"center\" prop=\"serviceMode\" width=\"150\" />\r\n      <el-table-column label=\"计算类型\" align=\"center\" prop=\"checkType\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.checkType == 1\">客户等级</span>\r\n          <span v-else>打样单难度</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"完成质量\" align=\"center\" prop=\"qualityEvaluation\" />\r\n<!--      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"140\">-->\r\n<!--        <template slot-scope=\"scope\">-->\r\n<!--          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>-->\r\n<!--        </template>-->\r\n<!--      </el-table-column>-->\r\n      <el-table-column label=\"打样单备注\" align=\"center\" prop=\"sampleOrderRemark\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.sampleOrderRemark\" placement=\"top\" :disabled=\"!scope.row.sampleOrderRemark\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.sampleOrderRemark || '-' }}\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.remark\" placement=\"top\" :disabled=\"!scope.row.remark\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.remark || '-' }}\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"未出样原因\" align=\"center\" prop=\"reasonForNoSample\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.reasonForNoSample\" placement=\"top\" :disabled=\"!scope.row.reasonForNoSample\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.reasonForNoSample || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"解决方案\" align=\"center\" prop=\"solution\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.solution\" placement=\"top\" :disabled=\"!scope.row.solution\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.solution || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"驳回原因\" align=\"center\" prop=\"rejectReason\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.rejectReason\" placement=\"top\" :disabled=\"!scope.row.rejectReason\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.rejectReason || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"fixed-width\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <!-- <el-tooltip content=\"编辑\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:edit']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"删除\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:remove']\" />\r\n          </el-tooltip> -->\r\n          <el-tooltip  v-if=\"scope.row.completionStatus==0\" content=\"更换工程师或更改日期\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-user\" v-if=\"scope.row.isLocked === 0\" @click=\"handleChangeEngineer(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:changeEngineer']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"开始任务\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-video-play\" v-if=\"scope.row.completionStatus == '0'\" @click=\"handleStart(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:startRask']\" />\r\n          </el-tooltip>\r\n\r\n          <el-tooltip content=\"批次管理\" v-if=\"[0,1,2].includes(scope.row.completionStatus)\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-collection\" @click=\"handleBatchManagement(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:batchManagement']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"逾期操作\" v-if=\"getOverdueInfo(scope.row).isOverdue\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-warning\" @click=\"handleOverdueOperation(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:overdueOperation']\" style=\"color: #E6A23C;\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"驳回\" v-if=\"scope.row.completionStatus == 0\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-close\" @click=\"handleReject(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:rejectRask']\" style=\"color: #F56C6C;\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"更新实验室编码\" v-if=\"scope.row.completionStatus == '2' && scope.row.itemStatus == 0\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit-outline\" @click=\"handleUpdateLaboratoryCode(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:editSampleOrderCode']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"下载打样单\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-download\" @click=\"handleDownloadSampleOrder(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:export']\" />\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n      :auto-scroll=\"false\" @pagination=\"getList\" />\r\n\r\n    <!-- 添加或修改工程师打样单关联对话框 -->\r\n    <!-- <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"650px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程师ID\" prop=\"userId\">\r\n              <el-input v-model=\"form.userId\" placeholder=\"请输入工程师ID\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"打样单编号\" prop=\"sampleOrderCode\">\r\n              <el-input v-model=\"form.sampleOrderCode\" placeholder=\"请输入打样单编号\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"难度\" prop=\"difficultyLevelId\">\r\n              <el-select v-model=\"form.difficultyLevelId\" placeholder=\"请选择打样难度等级\">\r\n                  <el-option\r\n                    v-for=\"dict in dylbOptions\"\r\n                    :key=\"dict.dictValue\"\r\n                    :label=\"dict.dictLabel\"\r\n                    :value=\"dict.dictValue\"\r\n                  ></el-option>\r\n                </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"完成情况\" prop=\"completionStatus\">\r\n              <el-select v-model=\"form.completionStatus\" placeholder=\"请选择完成情况\" style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"dict in statusOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排单日期\" prop=\"scheduledDate\">\r\n              <el-date-picker\r\n                v-model=\"form.scheduledDate\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择排单日期\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"最晚截至日期\" prop=\"endDate\">\r\n              <el-date-picker\r\n                v-model=\"form.endDate\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择最晚截至日期\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"锁定状态\" prop=\"isLocked\">\r\n              <el-switch\r\n                v-model=\"form.isLocked\"\r\n                :active-value=\"1\"\r\n                :inactive-value=\"0\"\r\n                active-text=\"已锁定\"\r\n                inactive-text=\"未锁定\"\r\n                @change=\"handleLockChange\">\r\n              </el-switch>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际开始时间\" prop=\"actualStartTime\">\r\n              <el-date-picker\r\n                v-model=\"form.actualStartTime\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择开始时间\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际完成时间\" prop=\"actualFinishTime\">\r\n              <el-date-picker\r\n                v-model=\"form.actualFinishTime\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择完成时间\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际工时\" prop=\"actualManHours\">\r\n              <el-input-number v-model=\"form.actualManHours\" :min=\"0\" :precision=\"1\" :step=\"0.5\" style=\"width: 100%\" placeholder=\"请输入实际工时\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"预估工时\" prop=\"estimatedManHours\">\r\n              <el-input-number v-model=\"form.estimatedManHours\" :min=\"0\" :precision=\"1\" :step=\"0.5\" style=\"width: 100%\" placeholder=\"请输入预估工时\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"完成质量\" prop=\"qualityEvaluation\">\r\n              <el-input v-model=\"form.qualityEvaluation\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入完成质量情况\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"打样单备注\" prop=\"sampleOrderRemark\">\r\n              <el-input v-model=\"form.sampleOrderRemark\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入打样单备注信息\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"备注\" prop=\"remark\">\r\n              <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入备注信息\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog> -->\r\n\r\n    <!-- 更改工程师对话框 -->\r\n    <el-dialog title=\"更改工程师\" :visible.sync=\"changeEngineerOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"changeEngineerForm\" :model=\"changeEngineerForm\" :rules=\"changeEngineerRules\" label-width=\"150px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"changeEngineerForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"当前工程师\">\r\n          <el-input v-model=\"changeEngineerForm.currentEngineerName\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"选择工程师\" prop=\"newEngineerId\">\r\n          <el-radio-group v-model=\"engineerSelectType\" style=\"margin-bottom: 15px;\">\r\n            <el-radio label=\"specified\">指定部门工程师</el-radio>\r\n            <el-radio label=\"other\">其他部门工程师</el-radio>\r\n          </el-radio-group>\r\n\r\n          <!-- 指定工程师选择 -->\r\n          <div v-show=\"engineerSelectType === 'specified'\">\r\n            <el-select\r\n              v-model=\"changeEngineerForm.newEngineerId\"\r\n              placeholder=\"请选择工程师\"\r\n              style=\"width: 100%\"\r\n              filterable>\r\n              <el-option\r\n                v-for=\"engineer in engineerOptions\"\r\n                :key=\"engineer.userId\"\r\n                :label=\"engineer.nickName\"\r\n                :value=\"engineer.userId\"\r\n              />\r\n            </el-select>\r\n          </div>\r\n\r\n          <!-- 其他工程师选择 -->\r\n          <div v-show=\"engineerSelectType === 'other'\" class=\"other-engineer-select\">\r\n            <div class=\"select-item\" style=\"margin-top: 10px;\">\r\n              <el-select\r\n                v-model=\"changeEngineerForm.newEngineerId\"\r\n                placeholder=\"请选择工程师\"\r\n                style=\"width: 100%\"\r\n                filterable\r\n                remote>\r\n                <el-option\r\n                  v-for=\"engineer in searchedEngineers\"\r\n                  :key=\"engineer.userId\"\r\n                  :label=\"engineer.nickName\"\r\n                  :value=\"engineer.userId\"\r\n                />\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"选择指定日期\" prop=\"scheduledDate\">\r\n          <el-date-picker\r\n            v-model=\"changeEngineerForm.scheduledDate\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择日期\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            :picker-options=\"futureDatePickerOptions\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"重新调整工作安排\">\r\n          <el-switch\r\n            v-model=\"changeEngineerForm.adjustWorkSchedule\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n            active-text=\"是\"\r\n            inactive-text=\"否\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitChangeEngineer\">确 定</el-button>\r\n        <el-button @click=\"changeEngineerOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批次管理对话框 -->\r\n    <el-dialog title=\"批次管理\" :visible.sync=\"batchManagementOpen\" width=\"900px\" append-to-body @close=\"closeBatchManagement\">\r\n      <div class=\"batch-management-container\">\r\n        <!-- 状态提示信息 -->\r\n        <el-alert\r\n          v-if=\"!canEditBatch\"\r\n          title=\"当前打样单状态不允许编辑批次信息\"\r\n          type=\"warning\"\r\n          description=\"只有状态为'进行中'的打样单才可以编辑和添加测试批次内容，其他状态只能查看批次信息。\"\r\n          show-icon\r\n          :closable=\"false\"\r\n          style=\"margin-bottom: 20px;\">\r\n        </el-alert>\r\n\r\n        <!-- 当前批次信息 -->\r\n        <el-card class=\"current-batch-card\" v-if=\"currentBatch\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>当前批次 - 第{{ currentBatch.batchIndex }}批次</span>\r\n            <el-button\r\n              v-if=\"canEditBatch\"\r\n              style=\"float: right; padding: 3px 0\"\r\n              type=\"text\"\r\n              @click=\"finishCurrentBatch\">\r\n              结束当前批次\r\n            </el-button>\r\n            <el-tag\r\n              v-else\r\n              style=\"float: right;\"\r\n              type=\"info\"\r\n              size=\"mini\">\r\n              只读模式\r\n            </el-tag>\r\n          </div>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\">\r\n              <div class=\"batch-info-item\">\r\n                <label>开始时间：</label>\r\n                <span>{{ parseTime(currentBatch.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <div class=\"batch-info-item\">\r\n                <label>已用时长：</label>\r\n                <span>{{ calculateDuration(currentBatch.startTime) }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <div class=\"batch-info-item\">\r\n                <label>状态：</label>\r\n                <el-tag type=\"primary\" size=\"mini\">进行中</el-tag>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <!-- 实验记录管理 -->\r\n          <div class=\"experiment-section\" style=\"margin-top: 20px;\">\r\n            <div class=\"section-header\">\r\n              <span>实验记录</span>\r\n              <el-button\r\n                v-if=\"canEditBatch\"\r\n                size=\"mini\"\r\n                type=\"primary\"\r\n                @click=\"addExperiment\">\r\n                添加实验\r\n              </el-button>\r\n              <el-tag\r\n                v-else\r\n                size=\"mini\"\r\n                type=\"info\">\r\n                只读模式\r\n              </el-tag>\r\n            </div>\r\n            <el-table :data=\"currentBatchExperiments\" size=\"mini\" style=\"margin-top: 10px;\">\r\n              <el-table-column prop=\"experimentCode\" label=\"实验编号\" width=\"150\"></el-table-column>\r\n              <el-table-column prop=\"experimentNote\" label=\"实验备注\"></el-table-column>\r\n              <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"150\">\r\n                <template slot-scope=\"scope\">\r\n                  {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </el-card>\r\n\r\n        <!-- 开始新批次按钮 -->\r\n        <div class=\"new-batch-section\" v-if=\"!currentBatch\" style=\"text-align: center; margin: 20px 0;\">\r\n          <el-button\r\n            v-if=\"canEditBatch\"\r\n            type=\"primary\"\r\n            size=\"medium\"\r\n            @click=\"startNewBatch\">\r\n            开始新批次\r\n          </el-button>\r\n          <div v-else style=\"text-align: center; color: #909399;\">\r\n            <i class=\"el-icon-info\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"margin-top: 16px;\">当前状态不允许开始新批次</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 历史批次列表 -->\r\n        <el-card class=\"history-batches-card\" style=\"margin-top: 20px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>历史批次</span>\r\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"refreshBatchData\">刷新</el-button>\r\n          </div>\r\n          <el-table :data=\"historyBatches\" size=\"small\" border stripe>\r\n            <el-table-column prop=\"batchIndex\" label=\"批次序号\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"primary\">第{{ scope.row.batchIndex }}批次</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"startTime\" label=\"开始时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                {{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"endTime\" label=\"结束时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.endTime\">{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}') }}</span>\r\n                <span v-else style=\"color: #909399;\">未结束</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"actualManHours\" label=\"实际工时\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" :type=\"scope.row.actualManHours > 8 ? 'warning' : 'success'\">\r\n                  {{ scope.row.actualManHours || 0 }}小时\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"qualityEvaluation\" label=\"质量评价\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag v-if=\"scope.row.qualityEvaluation\" size=\"mini\"\r\n                        :type=\"getQualityEvaluationType(scope.row.qualityEvaluation)\">\r\n                  {{ scope.row.qualityEvaluation }}\r\n                </el-tag>\r\n                <span v-else style=\"color: #909399;\">未评价</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"remark\" label=\"批次备注\" min-width=\"150\" show-overflow-tooltip>\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.remark\" style=\"color: #606266;\">{{ scope.row.remark }}</span>\r\n                <span v-else style=\"color: #C0C4CC;\">无备注</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"120\" align=\"center\" fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button size=\"mini\" type=\"text\" @click=\"viewBatchDetail(scope.row)\" icon=\"el-icon-view\">\r\n                  查看详情\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n\r\n          <!-- 空状态 -->\r\n          <div v-if=\"!historyBatches || historyBatches.length === 0\" class=\"empty-state\" style=\"text-align: center; padding: 40px;\">\r\n            <i class=\"el-icon-document\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"color: #909399; margin-top: 16px;\">暂无历史批次记录</p>\r\n          </div>\r\n        </el-card>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button\r\n          v-if=\"currentBatchRow && currentBatchRow.completionStatus == '1'\"\r\n          type=\"primary\"\r\n          icon=\"el-icon-check\"\r\n          @click=\"handleFinishFromBatch\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:endRask']\">\r\n          完成任务\r\n        </el-button>\r\n        <el-button @click=\"batchManagementOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批次详情对话框 -->\r\n    <el-dialog title=\"批次详情\" :visible.sync=\"batchDetailOpen\" width=\"800px\" append-to-body @close=\"closeBatchDetail\">\r\n      <div v-if=\"batchDetailData\" class=\"batch-detail-container\">\r\n        <!-- 批次基本信息 -->\r\n        <el-card class=\"batch-info-card\" style=\"margin-bottom: 20px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>批次基本信息</span>\r\n          </div>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>批次序号：</label>\r\n                <el-tag type=\"primary\">第{{ batchDetailData.batchIndex }}批次</el-tag>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>开始时间：</label>\r\n                <span class=\"info-value\">{{ parseTime(batchDetailData.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>结束时间：</label>\r\n                <span class=\"info-value\">\r\n                  {{ batchDetailData.endTime ? parseTime(batchDetailData.endTime, '{y}-{m}-{d} {h}:{i}:{s}') : '未结束' }}\r\n                </span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>实际工时：</label>\r\n                <el-tag :type=\"batchDetailData.actualManHours > 8 ? 'warning' : 'success'\">\r\n                  {{ batchDetailData.actualManHours || 0 }}小时\r\n                </el-tag>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>质量评价：</label>\r\n                <el-tag v-if=\"batchDetailData.qualityEvaluation\"\r\n                        :type=\"getQualityEvaluationType(batchDetailData.qualityEvaluation)\">\r\n                  {{ batchDetailData.qualityEvaluation }}\r\n                </el-tag>\r\n                <span v-else class=\"info-value\">未评价</span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row style=\"margin-top: 15px;\">\r\n            <el-col :span=\"24\">\r\n              <div class=\"info-item\">\r\n                <label>批次备注：</label>\r\n                <div class=\"remark-content\">\r\n                  {{ batchDetailData.remark || '无备注' }}\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n\r\n        <!-- 实验记录详情 -->\r\n        <el-card class=\"experiments-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>实验记录详情</span>\r\n            <el-tag size=\"mini\" style=\"margin-left: 10px;\">\r\n              共{{ batchDetailData.experiments ? batchDetailData.experiments.length : 0 }}条记录\r\n            </el-tag>\r\n          </div>\r\n          <el-table :data=\"batchDetailData.experiments\" size=\"small\" border stripe>\r\n            <el-table-column prop=\"experimentCode\" label=\"实验编号\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span style=\"color: #409EFF; font-weight: bold;\">{{ scope.row.experimentCode }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"experimentNote\" label=\"实验备注\" min-width=\"200\" show-overflow-tooltip>\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.experimentNote\">{{ scope.row.experimentNote }}</span>\r\n                <span v-else style=\"color: #C0C4CC;\">无备注</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"info\">{{ scope.row.createBy || '系统' }}</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n\r\n          <!-- 实验记录空状态 -->\r\n          <div v-if=\"!batchDetailData.experiments || batchDetailData.experiments.length === 0\"\r\n               class=\"empty-experiments\" style=\"text-align: center; padding: 40px;\">\r\n            <i class=\"el-icon-data-line\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"color: #909399; margin-top: 16px;\">该批次暂无实验记录</p>\r\n          </div>\r\n        </el-card>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"batchDetailOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加实验对话框 -->\r\n    <el-dialog title=\"添加实验记录\" :visible.sync=\"addExperimentOpen\" width=\"500px\" append-to-body @close=\"closeAddExperiment\">\r\n      <el-form :model=\"experimentForm\" :rules=\"experimentRules\" ref=\"experimentForm\" label-width=\"100px\">\r\n        <el-form-item label=\"实验编号\" prop=\"experimentCode\">\r\n          <el-input v-model=\"experimentForm.experimentCode\" placeholder=\"请输入实验编号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"实验备注\" prop=\"experimentNote\">\r\n          <el-input v-model=\"experimentForm.experimentNote\" type=\"textarea\" placeholder=\"请输入实验备注\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitExperiment\">确 定</el-button>\r\n        <el-button @click=\"addExperimentOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 结束批次对话框 -->\r\n    <el-dialog title=\"结束当前批次\" :visible.sync=\"finishBatchOpen\" width=\"500px\" append-to-body @close=\"closeFinishBatch\">\r\n      <el-form :model=\"finishBatchForm\" ref=\"finishBatchForm\" label-width=\"100px\">\r\n        <el-form-item label=\"质量评价\">\r\n          <el-input v-model=\"finishBatchForm.qualityEvaluation\" type=\"textarea\" placeholder=\"请输入质量评价\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"finishBatchForm.remark\" type=\"textarea\" placeholder=\"请输入备注\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFinishBatch\">确 定</el-button>\r\n        <el-button @click=\"finishBatchOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 完成任务对话框 -->\r\n    <el-dialog title=\"完成任务\" :visible.sync=\"finishTaskOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"finishTaskForm\" :model=\"finishTaskForm\" :rules=\"finishTaskRules\" label-width=\"120px\">\r\n        <el-form-item label=\"实验室编码\" prop=\"laboratoryCode\">\r\n          <el-input v-model=\"finishTaskForm.laboratoryCode\" placeholder=\"请输入实验室编码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"finishTaskForm.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注信息（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"finishTaskOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmFinishTask\" :loading=\"finishTaskLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 更新实验室编码对话框 -->\r\n    <el-dialog title=\"更新实验室编码\" :visible.sync=\"updateLaboratoryCodeOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"updateLaboratoryCodeForm\" :model=\"updateLaboratoryCodeForm\" :rules=\"updateLaboratoryCodeRules\" label-width=\"120px\">\r\n        <el-form-item label=\"实验室编码\" prop=\"laboratoryCode\">\r\n          <el-input v-model=\"updateLaboratoryCodeForm.laboratoryCode\" placeholder=\"请输入实验室编码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"updateLaboratoryCodeForm.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注信息（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"updateLaboratoryCodeOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmUpdateLaboratoryCode\" :loading=\"updateLaboratoryCodeLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 导出选择对话框 -->\r\n    <el-dialog title=\"选择导出内容\" :visible.sync=\"exportDialogOpen\" width=\"400px\" append-to-body>\r\n      <div class=\"export-options\">\r\n        <p style=\"margin-bottom: 20px; color: #606266;\">请选择要导出的打样单类型：</p>\r\n        <el-checkbox-group v-model=\"exportOptions\" style=\"display: flex; flex-direction: column;\">\r\n          <el-checkbox label=\"assigned\" style=\"margin-bottom: 15px;\">\r\n            <span style=\"font-weight: 500;\">已分配</span>\r\n            <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\r\n              导出已分配给工程师的打样单数据\r\n            </div>\r\n          </el-checkbox>\r\n          <el-checkbox label=\"unassigned\" style=\"margin-bottom: 15px;\">\r\n            <span style=\"font-weight: 500;\">未分配</span>\r\n            <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\r\n              导出尚未分配工程师的打样单数据\r\n            </div>\r\n          </el-checkbox>\r\n        </el-checkbox-group>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"exportDialogOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmExport\" :disabled=\"exportOptions.length === 0\" :loading=\"exportLoading\">\r\n          确认导出\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 驳回对话框 -->\r\n    <el-dialog title=\"驳回打样单\" :visible.sync=\"rejectDialogOpen\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"rejectForm\" :model=\"rejectForm\" :rules=\"rejectRules\" label-width=\"100px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"rejectForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"驳回理由\" prop=\"rejectReason\">\r\n          <el-input v-model=\"rejectForm.rejectReason\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入驳回理由\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"rejectDialogOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmReject\" :loading=\"rejectLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 逾期操作对话框 -->\r\n    <el-dialog title=\"逾期操作\" :visible.sync=\"overdueOperationOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"overdueOperationForm\" :model=\"overdueOperationForm\" label-width=\"120px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"overdueOperationForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"预计出样时间\">\r\n          <el-date-picker\r\n            v-model=\"overdueOperationForm.expectedSampleTime\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择日期\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            :picker-options=\"futureDatePickerOptions\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"未出样原因\">\r\n          <el-input v-model=\"overdueOperationForm.reasonForNoSample\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入未出样原因（非必填）\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"解决方案\">\r\n          <el-input v-model=\"overdueOperationForm.solution\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入解决方案（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"overdueOperationOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmOverdueOperation\" :loading=\"overdueOperationLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n    <!-- 添加或修改项目流程进行对话框 -->\r\n    <executionAddOrEdit ref=\"executionAddOrEdit\"></executionAddOrEdit>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listEngineerSampleOrder,\r\n  getEngineerSampleOrder,\r\n  delEngineerSampleOrder,\r\n  addEngineerSampleOrder,\r\n  updateEngineerSampleOrder,\r\n  updateSampleOrderStatus,\r\n  getDashboardStats,\r\n  getResearchDepartments,\r\n  getEngineersByDifficultyLevel,\r\n  changeEngineer,\r\n  getResearchDepartmentsUser,\r\n  getBatchesByOrderId,\r\n  getCurrentBatch,\r\n  startNewBatch,\r\n  finishCurrentBatch,\r\n  addExperimentToBatch,\r\n  getExperimentsByBatchId,\r\n  exportEngineerSampleOrder, getExecutionByOrderInfo\r\n} from \"@/api/software/engineerSampleOrder\";\r\nimport {exportNrwItem, exportMultipleNrw} from \"@/api/project/projectItemOrder\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\nimport {customerBaseAll} from \"@/api/customer/customer\";\r\nimport {isNull} from \"@/utils/validate\";\r\nimport executionAddOrEdit from \"@/components/Project/components/executionAddOrEdit.vue\";\r\n\r\nexport default {\r\n  name: \"EngineerSampleOrder\",\r\n  components: {\r\n     Treeselect,executionAddOrEdit\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 选中的完整行数据\r\n      selectedRows: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 工程师打样单关联表格数据\r\n      engineerSampleOrderList: [],\r\n      // 研发部部门树列表\r\n      researchDeptDatas:[],\r\n      // 打样类别字典\r\n      dylbOptions: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        userId: null,\r\n        nickName: null,\r\n        sampleOrderCode: null,\r\n        completionStatus: null,\r\n        scheduledDate: null,\r\n        startDate: null,\r\n        actualStartTime: null,\r\n        actualFinishTime: null,\r\n        deptId: null,\r\n        deptIds: [],\r\n        associationStatus: null,\r\n        customerId: null,\r\n        productName: null,\r\n        confirmCode: null,\r\n        isOverdue: null,  // 增逾期任务过滤参数\r\n        laboratory: null  // 实验室筛选参数\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        userId: [\r\n          { required: true, message: \"工程师ID不能为空\", trigger: \"blur\" }\r\n        ],\r\n        sampleOrderCode: [\r\n          { required: true, message: \"打样单编号不能为空\", trigger: \"blur\" }\r\n        ],\r\n        completionStatus: [\r\n          { required: true, message: \"完成情况不能为空\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      statusOptions: [],\r\n      serviceModeOptions: [],\r\n      dateRange: [], // 新增的日期范围筛选\r\n      scheduledDateRange: [],\r\n      startDateRange: [],\r\n      actualStartTimeRange: [],\r\n      actualFinishTimeRange: [],\r\n      // 日期选择器配置\r\n      dataPickerOptions: {\r\n        shortcuts: [{\r\n          text: '今天',\r\n          onClick(picker) {\r\n            const today = new Date();\r\n            picker.$emit('pick', [today, today]);\r\n          }\r\n        }, {\r\n          text: '昨天',\r\n          onClick(picker) {\r\n            const yesterday = new Date();\r\n            yesterday.setTime(yesterday.getTime() - 3600 * 1000 * 24);\r\n            picker.$emit('pick', [yesterday, yesterday]);\r\n          }\r\n        }, {\r\n          text: '最近一周',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }, {\r\n          text: '最近一个月',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }]\r\n      },\r\n      // 状态更新对话框\r\n      statusOpen: false,\r\n      dashboardStats: {\r\n        \"total\": 0,\r\n        \"completed\": 0,\r\n        \"inProgress\": 0,\r\n        \"overdue\": 0\r\n      },\r\n      // 更改工程师对话框\r\n      changeEngineerOpen: false,\r\n      // 更改工程师表单\r\n      changeEngineerForm: {\r\n        id: null,\r\n        sampleOrderCode: null,\r\n        currentEngineerName: null,\r\n        oldEngineerId: null,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      },\r\n      // 更改工程师表单校验\r\n      changeEngineerRules: {\r\n        newEngineerId: [\r\n          { required: true, message: \"请选择工程师\", trigger: \"change\" }\r\n        ],\r\n        scheduledDate: [\r\n          { required: true, message: \"请选择日期\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 工程师选项\r\n      engineerOptions: [],\r\n      // 批次管理相关\r\n      batchManagementOpen: false,\r\n      currentBatch: null,\r\n      historyBatches: [],\r\n      currentBatchExperiments: [],\r\n      selectedOrderForBatch: null,\r\n      currentBatchRow: null, // 当前批次管理的行数据\r\n      // 批次详情对话框\r\n      batchDetailOpen: false,\r\n      batchDetailData: null,\r\n      // 添加实验对话框\r\n      addExperimentOpen: false,\r\n      experimentForm: {\r\n        experimentCode: '',\r\n        experimentNote: ''\r\n      },\r\n      experimentRules: {\r\n        experimentCode: [\r\n          { required: true, message: \"实验编号不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      // 结束批次对话框\r\n      finishBatchOpen: false,\r\n      finishBatchForm: {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      },\r\n      // 未来日期选择器配置\r\n      futureDatePickerOptions: {\r\n        disabledDate(time) {\r\n          return time.getTime() < Date.now() - 8.64e7; // 禁用今天之前的日期\r\n        }\r\n      },\r\n      engineerSelectType: 'specified',\r\n      searchedEngineers: [],\r\n      // 未分配打样单告警\r\n      unassignedOrders: [],\r\n      unassignedQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 8\r\n      },\r\n      unassignedTotal: 0,\r\n      // 未分配面板折叠状态\r\n      isUnassignedPanelCollapsed: true,\r\n      // 完成任务对话框\r\n      finishTaskOpen: false,\r\n      finishTaskLoading: false,\r\n      finishTaskForm: {\r\n        laboratoryCode: '',\r\n        remark: ''\r\n      },\r\n      finishTaskRules: {\r\n        laboratoryCode: [\r\n          { required: true, message: \"实验室编码不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentFinishRow: null,\r\n      // 更新实验室编码对话框\r\n      updateLaboratoryCodeOpen: false,\r\n      updateLaboratoryCodeLoading: false,\r\n      updateLaboratoryCodeForm: {\r\n        laboratoryCode: '',\r\n        remark: ''\r\n      },\r\n      updateLaboratoryCodeRules: {\r\n        laboratoryCode: [\r\n          { required: true, message: \"实验室编码不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentUpdateLaboratoryCodeRow: null,\r\n      // 导出选择对话框\r\n      exportDialogOpen: false,\r\n      exportOptions: [],\r\n      exportLoading: false,\r\n      readonly: true,\r\n      // 项目详情相关\r\n      currentProjectType: null,\r\n      confirmItemCodes: [],\r\n      customerOptions: [],\r\n      itemNames: [],\r\n      // 驳回对话框\r\n      rejectDialogOpen: false,\r\n      rejectLoading: false,\r\n      rejectForm: {\r\n        sampleOrderCode: '',\r\n        rejectReason: ''\r\n      },\r\n      // 逾期操作对话框\r\n      overdueOperationOpen: false,\r\n      overdueOperationLoading: false,\r\n      overdueOperationForm: {\r\n        sampleOrderCode: '',\r\n        expectedSampleTime: '',\r\n        reasonForNoSample: '',\r\n        solution: ''\r\n      },\r\n      currentOverdueRow: null,\r\n      rejectRules: {\r\n        rejectReason: [\r\n          { required: true, message: \"驳回理由不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentRejectRow: null\r\n    };\r\n  },\r\n  computed: {\r\n    /** 计算是否可以编辑批次 - 只有状态为'1'(进行中)的打样单才可以编辑 */\r\n    canEditBatch() {\r\n      return this.selectedOrderForBatch && this.selectedOrderForBatch.completionStatus === 1;\r\n    }\r\n  },\r\n  created() {\r\n    // 设置默认日期范围为当天\r\n    const today = new Date();\r\n    const todayStr = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');\r\n    this.dateRange = [todayStr, todayStr];\r\n\r\n    this.getList();\r\n    customerBaseAll().then(res=> this.customerOptions = res)\r\n\r\n    this.getDicts(\"DYD_GCSZT\").then(response => {\r\n        this.statusOptions = response.data;\r\n    });\r\n    this.getDicts(\"CUSTOMER_SERVICE_MODE\").then(response => {\r\n      this.serviceModeOptions = response.data;\r\n    });\r\n    this.getDicts(\"project_nrw_dylb\").then(response => {\r\n      this.dylbOptions = response.data;\r\n    });\r\n    this.loadDashboardStats();\r\n    // 获取研发部部门列表\r\n    getResearchDepartments().then(response => {\r\n      this.researchDeptDatas = this.handleTree(response.data, \"deptId\");\r\n    });\r\n    // 获取未分配打样单\r\n    this.handleUnassignedOrders();\r\n  },\r\n  methods: {\r\n    /** 计算产品/项目等级 */\r\n    getProjectLevel(row) {\r\n      const customerLevel = row.customerLevel || '';\r\n      const projectLevel = row.projectLevel || '';\r\n\r\n      // 如果 projectLevel 是空字符串或 \"/\" 就不相加\r\n      if (projectLevel === '' || projectLevel === '/') {\r\n        return customerLevel;\r\n      }\r\n\r\n      return customerLevel + projectLevel;\r\n    },\r\n    /** 查询工程师打样单关联列表 */\r\n    getList() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      this.loading = true;\r\n      params.associationStatus = 1\r\n      listEngineerSampleOrder(params).then(response => {\r\n        this.engineerSampleOrderList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        userId: null,\r\n        sampleOrderCode: null,\r\n        serviceMode: null,\r\n        difficultyLevelId: null,\r\n        completionStatus: null,\r\n        scheduledDate: null,\r\n        actualStartTime: null,\r\n        actualFinishTime: null,\r\n        actualManHours: null,\r\n        estimatedManHours: null,\r\n        standardManHours: null,\r\n        qualityEvaluation: null,\r\n        isLocked: 0,\r\n        startDate: null,\r\n        endDate: null,\r\n        checkType: null,\r\n        sampleOrderRemark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.unassignedQueryParams.pageNum = 1;\r\n      this.getList();\r\n      this.loadDashboardStats();\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.dateRange = [] // 重置日期范围\r\n      this.scheduledDateRange = []\r\n      this.startDateRange = []\r\n      this.actualStartTimeRange = []\r\n      this.actualFinishTimeRange = []\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.selectedRows = selection\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加工程师打样单关联\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids\r\n      getEngineerSampleOrder(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改工程师打样单关联\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateEngineerSampleOrder(this.form).then(response => {\r\n              this.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n              this.loadDashboardStats();\r\n            });\r\n          } else {\r\n            addEngineerSampleOrder(this.form).then(response => {\r\n              this.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n              this.loadDashboardStats();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$confirm('是否确认删除工程师打样单关联编号为\"' + row.sampleOrderCode + '\"的数据项？').then(function () {\r\n        return delEngineerSampleOrder(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.msgSuccess(\"删除成功\");\r\n        this.loadDashboardStats();\r\n      }).catch(() => { });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 重置导出选项并显示选择对话框\r\n      this.exportOptions = [];\r\n      this.exportDialogOpen = true;\r\n    },\r\n    /** 确认导出操作 */\r\n    confirmExport() {\r\n      if (this.exportOptions.length === 0) {\r\n        this.$message.warning('请至少选择一种导出类型');\r\n        return;\r\n      }\r\n      this.exportLoading = true;\r\n      this.executeExports(0);\r\n    },\r\n\r\n    /** 串行执行导出操作 */\r\n    executeExports(index) {\r\n      if (index >= this.exportOptions.length) {\r\n        // 所有导出完成\r\n        this.exportLoading = false;\r\n        this.exportDialogOpen = false;\r\n        this.$message.success(`导出完成，共导出${this.exportOptions.length}个文件`);\r\n        return;\r\n      }\r\n\r\n      const option = this.exportOptions[index];\r\n      const associationStatus = option === 'assigned' ? 1 : 0;\r\n      const typeName = option === 'assigned' ? '已分配' : '未分配';\r\n\r\n      this.doExport(associationStatus, option).then(() => {\r\n        this.$message.success(`${typeName}打样单导出成功`);\r\n        // 继续导出下一个\r\n        this.executeExports(index + 1);\r\n      }).catch((error) => {\r\n        this.exportLoading = false;\r\n        this.$message.error(`${typeName}打样单导出失败: ${error.message || error}`);\r\n      });\r\n    },\r\n    /** 执行导出操作 */\r\n    doExport(associationStatus, exportType) {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      params.associationStatus = associationStatus;\r\n      params.exportType = exportType;\r\n      return exportEngineerSampleOrder(params).then(response => {\r\n        this.download(response.msg);\r\n      });\r\n    },\r\n    /** 执行导出打样单（已分配/未分配） */\r\n    doExportSampleOrder() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      this.$confirm('是否确认导出所有（已分配/未分配）打样单关联数据？', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportEngineerSampleOrder(params);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n        this.$message.success(\"导出成功\");\r\n      }).catch(() => {\r\n        this.exportLoading = false;\r\n      });\r\n    },\r\n    /** 点击\"开始\"按钮操作 */\r\n    handleStart(row) {\r\n      this.$confirm('确定要将打样单：' + row.sampleOrderCode + '标记为进行中吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        updateSampleOrderStatus({\r\n          id: row.id,\r\n          status: '1'\r\n        }).then(response => {\r\n          this.msgSuccess('打样单已设置为已开始');\r\n          this.getList();\r\n          this.loadDashboardStats();\r\n        });\r\n      });\r\n    },\r\n    /** 下载打样单操作 */\r\n    handleDownloadSampleOrder(row) {\r\n      this.$confirm('是否确认导出打样单?', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportNrwItem({itemId: row.itemId,projectId: row.projectId})\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n      }).catch(() => {});\r\n    },\r\n    /** 批量导出打样单任务操作 */\r\n    handleBatchExportNrw() {\r\n      if (this.selectedRows.length === 0) {\r\n        this.$modal.msgError(\"请选择要导出的打样单任务\");\r\n        return;\r\n      }\r\n\r\n      // 构造批量导出数据，传递itemId和projectId\r\n      const exportData = this.selectedRows.map(row => ({\r\n        itemId: row.itemId,\r\n        projectId: row.projectId\r\n      }));\r\n\r\n      this.$confirm('是否确认导出所选打样单任务的内容物数据？', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportMultipleNrw(exportData);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n        this.$message.success(\"导出成功\");\r\n      }).catch(() => {\r\n        this.exportLoading = false;\r\n      });\r\n    },\r\n    /** 点击\"完成\"按钮操作 */\r\n    handleFinish(row) {\r\n      this.currentFinishRow = row;\r\n      this.finishTaskForm.laboratoryCode = '';\r\n      this.finishTaskForm.remark = '';\r\n      this.finishTaskOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.finishTaskForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认完成任务 */\r\n    confirmFinishTask() {\r\n      this.$refs.finishTaskForm.validate(valid => {\r\n        if (valid) {\r\n          this.finishTaskLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentFinishRow.id,\r\n            status: '2',\r\n            laboratoryCode: this.finishTaskForm.laboratoryCode,\r\n            remark: this.finishTaskForm.remark\r\n          }).then(response => {\r\n            this.finishTaskLoading = false;\r\n            this.finishTaskOpen = false;\r\n            this.msgSuccess('打样单已设置为已完成');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.finishTaskLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 从批次管理对话框完成任务 */\r\n    handleFinishFromBatch() {\r\n      this.currentFinishRow = this.currentBatchRow;\r\n      this.finishTaskForm.laboratoryCode = '';\r\n      this.finishTaskForm.remark = '';\r\n      this.finishTaskOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.finishTaskForm.clearValidate();\r\n      });\r\n    },\r\n    /** 点击\"更新实验室编码\"按钮操作 */\r\n    handleUpdateLaboratoryCode(row) {\r\n      this.currentUpdateLaboratoryCodeRow = row;\r\n      // 回显当前的实验室编码和备注\r\n      this.updateLaboratoryCodeForm.laboratoryCode = row.laboratoryCode || '';\r\n      this.updateLaboratoryCodeForm.remark = row.remark || '';\r\n      this.updateLaboratoryCodeOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.updateLaboratoryCodeForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认更新实验室编码 */\r\n    confirmUpdateLaboratoryCode() {\r\n      this.$refs.updateLaboratoryCodeForm.validate(valid => {\r\n        if (valid) {\r\n          this.updateLaboratoryCodeLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentUpdateLaboratoryCodeRow.id,\r\n            status: this.currentUpdateLaboratoryCodeRow.completionStatus, // 保持当前状态\r\n            laboratoryCode: this.updateLaboratoryCodeForm.laboratoryCode,\r\n            remark: this.updateLaboratoryCodeForm.remark\r\n          }).then(response => {\r\n            this.updateLaboratoryCodeLoading = false;\r\n            this.updateLaboratoryCodeOpen = false;\r\n            this.msgSuccess('实验室编码更新成功');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.updateLaboratoryCodeLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 点击\"驳回\"按钮操作 */\r\n    handleReject(row) {\r\n      this.currentRejectRow = row;\r\n      this.rejectForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.rejectForm.rejectReason = '';\r\n      this.rejectDialogOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.rejectForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认驳回 */\r\n    confirmReject() {\r\n      this.$refs.rejectForm.validate(valid => {\r\n        if (valid) {\r\n          this.rejectLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentRejectRow.id,\r\n            status: '3',\r\n            rejectReason: this.rejectForm.rejectReason\r\n          }).then(response => {\r\n            this.rejectLoading = false;\r\n            this.rejectDialogOpen = false;\r\n            this.msgSuccess('打样单已驳回');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n            // 如果是未分配打样单的驳回，也需要刷新未分配列表\r\n            this.handleUnassignedOrders();\r\n          }).catch(() => {\r\n            this.rejectLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 点击\"逾期操作\"按钮操作 */\r\n    handleOverdueOperation(row) {\r\n      this.currentOverdueRow = row;\r\n      this.overdueOperationForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.overdueOperationForm.expectedSampleTime = row.expectedSampleTime;\r\n      this.overdueOperationForm.reasonForNoSample = row.reasonForNoSample;\r\n      this.overdueOperationForm.solution = row.solution;\r\n      this.overdueOperationOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.overdueOperationForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认逾期操作 */\r\n    confirmOverdueOperation() {\r\n      this.$refs.overdueOperationForm.validate(valid => {\r\n        if (valid) {\r\n          this.overdueOperationLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentOverdueRow.id,\r\n            status: \"11\", // 特殊处理，只更新“逾期情况”填写的字段\r\n            expectedSampleTime: this.overdueOperationForm.expectedSampleTime,\r\n            reasonForNoSample: this.overdueOperationForm.reasonForNoSample,\r\n            solution: this.overdueOperationForm.solution\r\n          }).then(response => {\r\n            this.overdueOperationLoading = false;\r\n            this.overdueOperationOpen = false;\r\n            this.msgSuccess('逾期操作已完成');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.overdueOperationLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 查看打样单详情 */\r\n    async orderDetail(row) {\r\n      try {\r\n        //获取执行ID\r\n        const orderId = row.projectOrderId;\r\n        let data = await getExecutionByOrderInfo(orderId);\r\n        if(data!=null && !isNull(data.id)){\r\n            let id = data.id;\r\n            this.$refs.executionAddOrEdit.open = true;\r\n            await this.$nextTick()\r\n            this.$refs.executionAddOrEdit.reset()\r\n            this.$refs.executionAddOrEdit.show(id, 1)\r\n        }else{\r\n          this.$message.error('获取数据失败');\r\n        }\r\n      } catch (error) {\r\n        console.error('查看项目详情失败:', error);\r\n        this.$message.error('查看项目详情失败: ' + (error.message || '未知错误'));\r\n      }\r\n    },\r\n\r\n    /** 锁定状态改变处理 */\r\n    handleLockChange(value) {\r\n      if (value === 1) {\r\n        this.$confirm('锁定后将无法自动调整此单的排单日期，是否确认锁定？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.form.isLocked = 1;\r\n        }).catch(() => {\r\n          this.form.isLocked = 0;\r\n        });\r\n      }\r\n    },\r\n    /** 表格中锁定状态改变处理 */\r\n    handleTableLockChange(value, row) {\r\n      if (value === 1) {\r\n        this.$confirm('锁定后将无法自动调整此单的排单日期，是否确认锁定？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          // 调用更新接口\r\n          updateEngineerSampleOrder({\r\n            id: row.id,\r\n            isLocked: 1\r\n          }).then(response => {\r\n            this.msgSuccess(\"锁定成功\");\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          });\r\n        }).catch(() => {\r\n          // 取消操作，恢复原值\r\n          row.isLocked = 0;\r\n        });\r\n      } else {\r\n        // 解锁操作\r\n        updateEngineerSampleOrder({\r\n          id: row.id,\r\n          isLocked: 0\r\n        }).then(response => {\r\n          this.msgSuccess(\"解锁成功\");\r\n          this.getList();\r\n          this.loadDashboardStats();\r\n        });\r\n      }\r\n    },\r\n    /** 加载统计概览数据 */\r\n    loadDashboardStats() {\r\n      let params = { ...this.queryParams };\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      getDashboardStats(params).then(response => {\r\n        this.dashboardStats = response.data || {};\r\n      });\r\n    },\r\n    /** 更改工程师按钮操作 */\r\n    handleChangeEngineer(row) {\r\n      this.changeEngineerForm = {\r\n        id: row.id,\r\n        sampleOrderCode: row.sampleOrderCode,\r\n        currentEngineerName: row.nickName,\r\n        oldEngineerId: row.userId,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      };\r\n\r\n      // 获取可用工程师列表\r\n      getEngineersByDifficultyLevel({\r\n        difficultyLevelId: row.difficultyLevelId,\r\n        categoryId: row.categoryId\r\n      }).then(response => {\r\n        this.engineerOptions = response.data || [];\r\n      });\r\n      // 获取研发所有工程师列表\r\n      getResearchDepartmentsUser().then(response => {\r\n        this.searchedEngineers = response.data || [];\r\n      });\r\n      this.changeEngineerOpen = true;\r\n    },\r\n    /** 提交更改工程师 */\r\n    submitChangeEngineer() {\r\n      this.$refs[\"changeEngineerForm\"].validate(valid => {\r\n        if (valid) {\r\n          const data = {\r\n            sampleOrderId: this.changeEngineerForm.id,\r\n            oldEngineerId: this.changeEngineerForm.oldEngineerId,\r\n            newEngineerId: this.changeEngineerForm.newEngineerId,\r\n            scheduledDate: this.changeEngineerForm.scheduledDate,\r\n            adjustWorkSchedule: this.changeEngineerForm.adjustWorkSchedule\r\n          };\r\n\r\n          // 调用更改工程师的API接口\r\n          changeEngineer(data).then(response => {\r\n            this.msgSuccess(\"更改工程师成功\");\r\n            this.changeEngineerOpen = false;\r\n            this.getList();\r\n            // 获取未分配打样单\r\n            this.handleUnassignedOrders();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.msgError(\"更改工程师失败\");\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 获取未分配打样单列表 */\r\n    handleUnassignedOrders() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      params.associationStatus = 0\r\n      params.pageNum = this.unassignedQueryParams.pageNum;\r\n      params.pageSize = this.unassignedQueryParams.pageSize;\r\n      listEngineerSampleOrder(params).then(response => {\r\n        this.unassignedOrders = response.rows;\r\n        this.unassignedTotal = response.total;\r\n      });\r\n    },\r\n    /** 分配工程师操作 */\r\n    handleAssignEngineer(row) {\r\n      this.changeEngineerForm = {\r\n        id: row.id,\r\n        sampleOrderCode: row.sampleOrderCode,\r\n        currentEngineerName: '',\r\n        oldEngineerId: null,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      };\r\n\r\n      // 获取可用工程师列表\r\n      getEngineersByDifficultyLevel({\r\n        difficultyLevelId: row.difficultyLevelId,\r\n        categoryId: row.categoryId\r\n      }).then(response => {\r\n        this.engineerOptions = response.data || [];\r\n      });\r\n      // 获取研发所有工程师列表\r\n      getResearchDepartmentsUser().then(response => {\r\n        this.searchedEngineers = response.data || [];\r\n      });\r\n      this.changeEngineerOpen = true;\r\n    },\r\n    /** 删除未分配打样单 */\r\n    handleDeleteUnassigned(row) {\r\n      this.$confirm('是否确认删除打样单编号为\"' + row.sampleOrderCode + '\"的数据项？').then(function() {\r\n        return delEngineerSampleOrder(row.id);\r\n      }).then(() => {\r\n        this.handleUnassignedOrders();\r\n        this.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 驳回未分配打样单 */\r\n    handleRejectUnassigned(row) {\r\n      this.currentRejectRow = row;\r\n      this.rejectForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.rejectForm.rejectReason = '';\r\n      this.rejectDialogOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.rejectForm.clearValidate();\r\n      });\r\n    },\r\n    /** 未分配打样单分页大小改变 */\r\n    handleUnassignedSizeChange(newSize) {\r\n      this.unassignedQueryParams.pageSize = newSize;\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 未分配打样单页码改变 */\r\n    handleUnassignedCurrentChange(newPage) {\r\n      this.unassignedQueryParams.pageNum = newPage;\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 切换未分配面板显示状态 */\r\n    toggleUnassignedPanel() {\r\n      this.isUnassignedPanelCollapsed = !this.isUnassignedPanelCollapsed;\r\n    },\r\n    /** 处理状态过滤 */\r\n    handleStatusFilter(status) {\r\n      // 清除逾期过滤\r\n      this.queryParams.isOverdue = null;\r\n      this.queryParams.completionStatus = status;\r\n      this.handleQuery();\r\n    },\r\n\r\n    /** 处理逾期任务过滤 */\r\n    handleOverdueFilter() {\r\n      // 清除完成状态过滤\r\n      this.queryParams.completionStatus = null;\r\n      // 设置逾期过滤\r\n      this.queryParams.isOverdue = this.queryParams.isOverdue === 1 ? null : 1;\r\n      this.handleQuery();\r\n\r\n      if (this.queryParams.isOverdue === 1) {\r\n        this.$message.info('已筛选显示逾期任务');\r\n      } else {\r\n        this.$message.info('已清除逾期任务筛选');\r\n      }\r\n    },\r\n    /** 获取紧急程度类型 */\r\n    getUrgencyType(endDate, latestStartTime) {\r\n      const now = new Date();\r\n      const end = new Date(endDate);\r\n      const latest = latestStartTime ? new Date(latestStartTime) : null;\r\n\r\n      // 计算距离截止日期的天数\r\n      const daysToEnd = Math.ceil((end - now) / (1000 * 60 * 60 * 24));\r\n\r\n      // 如果有最晚开始时间，检查是否已经超过\r\n      if (latest && now > latest) {\r\n        return 'danger'; // 已超过最晚开始时间\r\n      }\r\n\r\n      if (daysToEnd <= 1) {\r\n        return 'danger'; // 紧急：1天内截止\r\n      } else if (daysToEnd <= 3) {\r\n        return 'warning'; // 警告：3天内截止\r\n      } else if (daysToEnd <= 7) {\r\n        return 'primary'; // 一般：7天内截止\r\n      } else {\r\n        return 'success'; // 充足时间\r\n      }\r\n    },\r\n    /** 获取紧急程度文本 */\r\n    getUrgencyText(endDate, latestStartTime) {\r\n      const now = new Date();\r\n      const end = new Date(endDate);\r\n      const latest = latestStartTime ? new Date(latestStartTime) : null;\r\n      // 计算距离截止日期的天数\r\n      const daysToEnd = Math.ceil((end - now) / (1000 * 60 * 60 * 24));\r\n      // 如果有最晚开始时间，检查是否已经超过\r\n      if (latest && now > latest) {\r\n        return '超期未开始';\r\n      }\r\n\r\n      if (daysToEnd <= 0) {\r\n        return '已逾期';\r\n      } else if (daysToEnd <= 1) {\r\n        return '紧急';\r\n      } else if (daysToEnd <= 3) {\r\n        return '较急';\r\n      } else if (daysToEnd <= 7) {\r\n        return '一般';\r\n      } else {\r\n        return '充足';\r\n      }\r\n    },\r\n    /** 刷新未分配打样单列表 */\r\n    handleRefreshUnassigned() {\r\n      this.handleUnassignedOrders();\r\n      this.$message.success('刷新成功');\r\n    },\r\n    /** 获取逾期信息 */\r\n    getOverdueInfo(row) {\r\n      const now = new Date();\r\n      const endDate = new Date(row.endDate);\r\n      let isOverdue = false;\r\n      let overdueDays = 0;\r\n      // 根据后台SQL逻辑判断逾期\r\n      if (row.completionStatus === 2) {\r\n        // 已完成且逾期：实际完成时间 > 截止日期\r\n        if (row.actualFinishTime) {\r\n          const actualFinishTime = new Date(row.actualFinishTime);\r\n          if (actualFinishTime > endDate) {\r\n            isOverdue = true;\r\n            overdueDays = Math.ceil((actualFinishTime - endDate) / (1000 * 60 * 60 * 24));\r\n          }\r\n        }\r\n      } else if (row.completionStatus === 1) {\r\n        // 进行中且逾期：当前时间 > 截止日期\r\n        if (now > endDate) {\r\n          isOverdue = true;\r\n          overdueDays = Math.ceil((now - endDate) / (1000 * 60 * 60 * 24));\r\n        }\r\n      } else if (row.completionStatus === 0) {\r\n        // 未开始且逾期：未开始但当前时间 > 开始日期\r\n        if (now > endDate) {\r\n          isOverdue = true;\r\n          overdueDays = Math.ceil((now - endDate) / (1000 * 60 * 60 * 24));\r\n        }\r\n      }\r\n\r\n      return {\r\n        isOverdue,\r\n        overdueDays\r\n      };\r\n    },\r\n\r\n    // ==================== 批次管理相关方法 ====================\r\n\r\n    /** 打开批次管理对话框 */\r\n    handleBatchManagement(row) {\r\n      this.selectedOrderForBatch = row;\r\n      this.currentBatchRow = row; // 保存当前行数据\r\n      this.batchManagementOpen = true;\r\n      this.loadBatchData(row.id);\r\n    },\r\n\r\n    /** 加载批次数据 */\r\n    async loadBatchData(engineerSampleOrderId) {\r\n      try {\r\n        // 加载当前批次\r\n        const currentBatchResponse = await getCurrentBatch(engineerSampleOrderId);\r\n        this.currentBatch = currentBatchResponse.data;\r\n\r\n        // 加载所有批次\r\n        const batchesResponse = await getBatchesByOrderId(engineerSampleOrderId);\r\n        const allBatches = batchesResponse.data || [];\r\n\r\n        // 分离当前批次和历史批次，并为每个历史批次加载实验数量\r\n        this.historyBatches = allBatches.filter(batch => batch.isCurrentBatch === 0);\r\n\r\n        // // 为每个历史批次加载实验数量\r\n        // for (let batch of this.historyBatches) {\r\n        //   try {\r\n        //     const experimentsResponse = await getExperimentsByBatchId(batch.id);\r\n        //     batch.experimentCount = experimentsResponse.data ? experimentsResponse.data.length : 0;\r\n        //   } catch (error) {\r\n        //     console.warn(`加载批次${batch.id}的实验数量失败:`, error);\r\n        //     batch.experimentCount = 0;\r\n        //   }\r\n        // }\r\n\r\n        // 如果有当前批次，加载实验记录\r\n        if (this.currentBatch) {\r\n          await this.loadCurrentBatchExperiments(this.currentBatch.id);\r\n        }\r\n      } catch (error) {\r\n        console.error('加载批次数据失败:', error);\r\n        this.$message.error('加载批次数据失败');\r\n      }\r\n    },\r\n\r\n    /** 加载当前批次的实验记录 */\r\n    async loadCurrentBatchExperiments(batchId) {\r\n      try {\r\n        const response = await getExperimentsByBatchId(batchId);\r\n        this.currentBatchExperiments = response.data || [];\r\n      } catch (error) {\r\n        console.error('加载实验记录失败:', error);\r\n      }\r\n    },\r\n\r\n    /** 开始新批次 */\r\n    async startNewBatch() {\r\n      // 检查编辑权限\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许开始新批次，只有状态为\"进行中\"的打样单才可以编辑批次信息');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await this.$confirm('确认开始新的打样批次？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        });\r\n\r\n        const response = await startNewBatch(this.selectedOrderForBatch.id, '');\r\n        this.$message.success('新批次开始成功');\r\n\r\n        // 重新加载批次数据\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n\r\n        // 刷新主列表\r\n        this.getList();\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('开始新批次失败: ' + (error.message || error));\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 结束当前批次 */\r\n    finishCurrentBatch() {\r\n      // 检查编辑权限\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许结束批次，只有状态为\"进行中\"的打样单才可以编辑批次信息');\r\n        return;\r\n      }\r\n\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n      this.finishBatchOpen = true;\r\n    },\r\n\r\n    /** 提交结束批次 */\r\n    async submitFinishBatch() {\r\n      try {\r\n        await finishCurrentBatch(\r\n          this.selectedOrderForBatch.id,\r\n          this.finishBatchForm.qualityEvaluation,\r\n          this.finishBatchForm.remark\r\n        );\r\n\r\n        this.$message.success('批次结束成功');\r\n        this.finishBatchOpen = false;\r\n\r\n        // 重新加载批次数据\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n\r\n        // 刷新主列表\r\n        this.getList();\r\n      } catch (error) {\r\n        console.error('结束批次失败:', error);\r\n        this.$message.error('结束批次失败: ' + (error.message || error));\r\n      }\r\n    },\r\n\r\n    /** 添加实验记录 */\r\n    addExperiment() {\r\n      // 检查编辑权限\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许添加实验记录，只有状态为\"进行中\"的打样单才可以编辑批次信息');\r\n        return;\r\n      }\r\n\r\n      this.experimentForm = {\r\n        experimentCode: '',\r\n        experimentNote: ''\r\n      };\r\n      this.addExperimentOpen = true;\r\n    },\r\n\r\n    /** 提交实验记录 */\r\n    async submitExperiment() {\r\n      // 检查编辑权限\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许添加实验记录，只有状态为\"进行中\"的打样单才可以编辑批次信息');\r\n        return;\r\n      }\r\n\r\n      await this.$refs.experimentForm.validate();\r\n\r\n      const experimentRecord = {\r\n        batchId: this.currentBatch.id,\r\n        experimentCode: this.experimentForm.experimentCode,\r\n        experimentNote: this.experimentForm.experimentNote || ''\r\n      };\r\n\r\n      await addExperimentToBatch(experimentRecord);\r\n\r\n      this.$message.success('实验记录添加成功');\r\n      this.addExperimentOpen = false;\r\n\r\n      // 重新加载当前批次的实验记录\r\n      await this.loadCurrentBatchExperiments(this.currentBatch.id);\r\n    },\r\n\r\n    /** 查看批次详情 */\r\n    async viewBatchDetail(batch) {\r\n      try {\r\n        const response = await getExperimentsByBatchId(batch.id);\r\n        const experiments = response.data || [];\r\n\r\n        // 设置批次详情数据\r\n        this.batchDetailData = {\r\n          ...batch,\r\n          experiments: experiments\r\n        };\r\n\r\n        this.batchDetailOpen = true;\r\n      } catch (error) {\r\n        console.error('查看批次详情失败:', error);\r\n        this.$message.error('查看批次详情失败');\r\n      }\r\n    },\r\n\r\n    /** 计算持续时间 */\r\n    calculateDuration(startTime) {\r\n      if (!startTime) return '0小时';\r\n\r\n      const start = new Date(startTime);\r\n      const now = new Date();\r\n      const diffMs = now - start;\r\n      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\r\n      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\r\n\r\n      if (diffHours > 0) {\r\n        return `${diffHours}小时${diffMinutes}分钟`;\r\n      } else {\r\n        return `${diffMinutes}分钟`;\r\n      }\r\n    },\r\n\r\n    /** 获取质量评价类型 */\r\n    getQualityEvaluationType(evaluation) {\r\n      if (!evaluation) return '';\r\n\r\n      const lowerEval = evaluation.toLowerCase();\r\n      if (lowerEval.includes('优秀') || lowerEval.includes('良好') || lowerEval.includes('好')) {\r\n        return 'success';\r\n      } else if (lowerEval.includes('一般') || lowerEval.includes('中等')) {\r\n        return 'warning';\r\n      } else if (lowerEval.includes('差') || lowerEval.includes('不合格') || lowerEval.includes('失败')) {\r\n        return 'danger';\r\n      } else {\r\n        return 'info';\r\n      }\r\n    },\r\n\r\n    /** 关闭批次管理对话框并清空数据 */\r\n    closeBatchManagement() {\r\n      // 清空批次管理相关的数据\r\n      this.currentBatch = null;\r\n      this.historyBatches = [];\r\n      this.currentBatchExperiments = [];\r\n      this.selectedOrderForBatch = null;\r\n      this.currentBatchRow = null; // 清空当前行数据\r\n      this.batchDetailData = null;\r\n\r\n      // 重置表单数据\r\n      this.experimentForm = {\r\n        experimentCode: '',\r\n        experimentNote: ''\r\n      };\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n\r\n      // 关闭所有相关的子对话框\r\n      this.batchDetailOpen = false;\r\n      this.addExperimentOpen = false;\r\n      this.finishBatchOpen = false;\r\n    },\r\n\r\n    /** 关闭批次详情对话框并清空数据 */\r\n    closeBatchDetail() {\r\n      // 清空批次详情数据\r\n      this.batchDetailData = null;\r\n    },\r\n\r\n    /** 关闭添加实验对话框并清空数据 */\r\n    closeAddExperiment() {\r\n      // 清空实验表单数据\r\n      this.experimentForm = {\r\n        experimentCode: '',\r\n        experimentNote: ''\r\n      };\r\n      // 清除表单验证状态\r\n      if (this.$refs.experimentForm) {\r\n        this.$refs.experimentForm.clearValidate();\r\n      }\r\n    },\r\n\r\n    /** 关闭结束批次对话框并清空数据 */\r\n    closeFinishBatch() {\r\n      // 清空结束批次表单数据\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n      // 清除表单验证状态\r\n      if (this.$refs.finishBatchForm) {\r\n        this.$refs.finishBatchForm.clearValidate();\r\n      }\r\n    },\r\n\r\n    /** 刷新批次数据 */\r\n    async refreshBatchData() {\r\n      if (this.selectedOrderForBatch) {\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n        this.$message.success('批次数据已刷新');\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.stats-row {\r\n  margin-bottom: 20px;\r\n}\r\n.stats-card {\r\n  border: none;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n/* 批次详情样式 */\r\n.batch-detail-container {\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.batch-info-card {\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 8px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.info-item label {\r\n  font-weight: 600;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n  min-width: 80px;\r\n}\r\n\r\n.info-value {\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.remark-content {\r\n  background-color: #F5F7FA;\r\n  padding: 12px;\r\n  border-radius: 4px;\r\n  border-left: 4px solid #409EFF;\r\n  color: #606266;\r\n  line-height: 1.5;\r\n  margin-top: 8px;\r\n  min-height: 40px;\r\n}\r\n\r\n.experiments-card {\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 8px;\r\n}\r\n\r\n.empty-state, .empty-experiments {\r\n  background-color: #FAFAFA;\r\n  border-radius: 8px;\r\n  margin: 10px 0;\r\n}\r\n\r\n/* 历史批次表格样式优化 */\r\n.history-batches-card .el-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-batches-card .el-table th {\r\n  background-color: #F5F7FA;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.history-batches-card .el-table--striped .el-table__body tr.el-table__row--striped td {\r\n  background-color: #FAFAFA;\r\n}\r\n\r\n.stats-card:not(.total) {\r\n  cursor: pointer;\r\n}\r\n\r\n.stats-card:not(.total):hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stats-card.active {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  position: relative;\r\n}\r\n\r\n.stats-card.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 3px;\r\n  background: currentColor;\r\n}\r\n\r\n.stats-card.completed.active::after {\r\n  background: #67C23A;\r\n}\r\n\r\n.stats-card.in-progress.active::after {\r\n  background: #409EFF;\r\n}\r\n\r\n.stats-card.overdue.active::after {\r\n  background: #F56C6C;\r\n}\r\n\r\n.stats-card.overdue .stats-icon {\r\n  background: linear-gradient(135deg, #F56C6C, #F78989);\r\n}\r\n\r\n.stats-content {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20px;\r\n}\r\n.stats-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20px;\r\n}\r\n.stats-icon i {\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n.stats-info {\r\n  flex: 1;\r\n}\r\n.stats-title {\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-bottom: 8px;\r\n}\r\n.stats-number {\r\n  font-size: 28px;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n.stats-card.total .stats-icon {\r\n  background: linear-gradient(135deg, #3a7bd5, #3a6073);\r\n}\r\n.stats-card.completed .stats-icon {\r\n  background: linear-gradient(135deg, #E6A23C, #F0C78A);\r\n}\r\n.stats-card.in-progress .stats-icon {\r\n  background: linear-gradient(135deg, #409EFF, #66B1FF);\r\n}\r\n.stats-card.overdue .stats-icon {\r\n  background: linear-gradient(135deg, #F56C6C, #F78989);\r\n}\r\n@media (max-width: 768px) {\r\n  .stats-card .stats-content {\r\n    padding: 15px;\r\n  }\r\n  .stats-icon {\r\n    width: 50px;\r\n    height: 50px;\r\n    margin-right: 15px;\r\n  }\r\n  .stats-icon i {\r\n    font-size: 20px;\r\n  }\r\n  .stats-number {\r\n    font-size: 24px;\r\n  }\r\n}\r\n.other-engineer-select {\r\n  margin-top: 10px;\r\n}\r\n.select-item {\r\n  width: 100%;\r\n}\r\n\r\n/* 未分配打样单告警样式 */\r\n.alert-card {\r\n  margin-bottom: 20px;\r\n}\r\n.alert-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.alert-title {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n}\r\n.alert-title span {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #FF1414;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.alert-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.alert-cards {\r\n  margin-top: 20px;\r\n}\r\n.alert-item {\r\n  margin-bottom: 20px;\r\n  transition: all 0.3s;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n  height: 225px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: #fff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n.alert-item:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);\r\n  border-color: #409EFF;\r\n}\r\n.alert-item-header {\r\n  margin-bottom: 12px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px solid #f0f2f5;\r\n  background: rgba(64, 158, 255, 0.02);\r\n  margin: -16px -16px 12px -16px;\r\n  padding: 12px 16px;\r\n  border-radius: 6px 6px 0 0;\r\n}\r\n.order-code-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.order-code {\r\n  font-size: 15px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n.urgency-tag {\r\n  margin-left: 8px;\r\n}\r\n.alert-item-content {\r\n  padding: 0;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n  min-height: 0;\r\n}\r\n.info-section {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n}\r\n.info-row {\r\n  margin-bottom: 8px;\r\n}\r\n.info-row-double {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  gap: 12px;\r\n}\r\n.info-row-double .info-item {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n.info-row-double .info-item.date-time-item {\r\n  flex: 1.5;\r\n}\r\n.info-row-double .info-item.standard-item {\r\n  flex: 1;\r\n}\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #606266;\r\n  font-size: 13px;\r\n}\r\n.info-label {\r\n  margin-left: 6px;\r\n  margin-right: 4px;\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n.info-value {\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n.difficulty-text {\r\n  cursor: pointer;\r\n  border-bottom: 1px dashed #ccc;\r\n}\r\n.info-reason {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  color: #f56c6c;\r\n  margin-top: 8px;\r\n  padding: 6px 8px;\r\n  background-color: #fef0f0;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #f56c6c;\r\n  font-size: 12px;\r\n}\r\n.reason-text {\r\n  margin-left: 4px;\r\n  line-height: 1.4;\r\n  word-break: break-word;\r\n}\r\n.text-overflow {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n.info-reason span {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n.info-item i,\r\n.info-date i,\r\n.info-reason i {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n}\r\n.alert-item-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: auto;\r\n  padding: 12px 0 0 0;\r\n  border-top: 1px solid #f0f2f5;\r\n  flex-shrink: 0;\r\n  background: rgba(250, 251, 252, 0.5);\r\n  border-radius: 0 0 6px 6px;\r\n  margin-left: -16px;\r\n  margin-right: -16px;\r\n  padding-left: 16px;\r\n  padding-right: 16px;\r\n}\r\n.alert-item-footer .el-button {\r\n  border-radius: 4px;\r\n  font-weight: 500;\r\n}\r\n.alert-item-footer .el-button--primary {\r\n  background: #409EFF;\r\n  border-color: #409EFF;\r\n  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);\r\n}\r\n.alert-item-footer .el-button--primary:hover {\r\n  background: #66b1ff;\r\n  border-color: #66b1ff;\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);\r\n}\r\n.alert-item-footer .el-button--text {\r\n  color: #909399;\r\n  transition: all 0.3s;\r\n}\r\n.alert-item-footer .el-button--text:hover {\r\n  color: #f56c6c;\r\n  background: rgba(245, 108, 108, 0.1);\r\n}\r\n.delete-btn:hover {\r\n  color: #f56c6c !important;\r\n}\r\n\r\n/* 确保卡片内容区域的统一布局 */\r\n.alert-item .el-card__body {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 16px;\r\n  position: relative;\r\n}\r\n\r\n/* 确保内容区域占据剩余空间 */\r\n.alert-item .alert-item-header {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.alert-item .alert-item-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 0;\r\n}\r\n\r\n.alert-item .alert-item-footer {\r\n  flex-shrink: 0;\r\n  margin-top: auto;\r\n}\r\n\r\n.alert-item .info-group {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.alert-item .info-date {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .alert-item {\r\n    height: auto;\r\n    min-height: 200px;\r\n  }\r\n\r\n  .info-row-double {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n\r\n  .alert-item-footer {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .alert-item-footer .el-button {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* 批次管理相关样式 */\r\n.batch-management-container {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.current-batch-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.batch-info-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.batch-info-item label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n}\r\n\r\n.experiment-section {\r\n  border-top: 1px solid #ebeef5;\r\n  padding-top: 15px;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.section-header span {\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.history-batches-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.new-batch-section {\r\n  padding: 40px 0;\r\n  text-align: center;\r\n  background-color: #fafafa;\r\n  border: 2px dashed #dcdfe6;\r\n  border-radius: 6px;\r\n}\r\n\r\n.new-batch-section .el-button {\r\n  padding: 12px 30px;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .batch-management-container {\r\n    max-height: 500px;\r\n  }\r\n\r\n  .batch-info-item {\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .section-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .section-header .el-button {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n\r\n/* 备注文本省略样式 */\r\n.remark-text-ellipsis {\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 2;\r\n  line-clamp: 2;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  line-height: 1.4;\r\n  max-height: 2.8em; /* 2行的高度，基于line-height */\r\n  word-break: break-word;\r\n  white-space: normal;\r\n  cursor: pointer;\r\n}\r\n\r\n.remark-text-ellipsis:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 导出选择对话框样式 */\r\n.export-options {\r\n  padding: 10px 0;\r\n}\r\n\r\n.export-options .el-checkbox {\r\n  width: 100%;\r\n  margin-right: 0;\r\n  padding: 15px;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.export-options .el-checkbox:hover {\r\n  border-color: #409eff;\r\n  background-color: #f0f9ff;\r\n}\r\n\r\n.export-options .el-checkbox.is-checked {\r\n  border-color: #409eff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.export-options .el-checkbox__label {\r\n  width: 100%;\r\n  padding-left: 10px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAy2CA,IAAAA,oBAAA,GAAAC,OAAA;AAoBA,IAAAC,iBAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAC,sBAAA,CAAAH,OAAA;AACAA,OAAA;AACA,IAAAI,SAAA,GAAAJ,OAAA;AACA,IAAAK,SAAA,GAAAL,OAAA;AACA,IAAAM,mBAAA,GAAAH,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAO,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,sBAAA;IAAAC,kBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,YAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,uBAAA;MACA;MACAC,iBAAA;MACA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,aAAA;QACAC,SAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,MAAA;QACAC,OAAA;QACAC,iBAAA;QACAC,UAAA;QACAC,WAAA;QACAC,WAAA;QACAC,SAAA;QAAA;QACAC,UAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAjB,MAAA,GACA;UAAAkB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAlB,eAAA,GACA;UAAAgB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAjB,gBAAA,GACA;UAAAe,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,aAAA;MACAC,kBAAA;MACAC,SAAA;MAAA;MACAC,kBAAA;MACAC,cAAA;MACAC,oBAAA;MACAC,qBAAA;MACA;MACAC,iBAAA;QACAC,SAAA;UACAC,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAC,KAAA,OAAAC,IAAA;YACAF,MAAA,CAAAG,KAAA,UAAAF,KAAA,EAAAA,KAAA;UACA;QACA;UACAH,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAI,SAAA,OAAAF,IAAA;YACAE,SAAA,CAAAC,OAAA,CAAAD,SAAA,CAAAE,OAAA;YACAN,MAAA,CAAAG,KAAA,UAAAC,SAAA,EAAAA,SAAA;UACA;QACA;UACAN,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAO,GAAA,OAAAL,IAAA;YACA,IAAAM,KAAA,OAAAN,IAAA;YACAM,KAAA,CAAAH,OAAA,CAAAG,KAAA,CAAAF,OAAA;YACAN,MAAA,CAAAG,KAAA,UAAAK,KAAA,EAAAD,GAAA;UACA;QACA;UACAT,IAAA;UACAC,OAAA,WAAAA,QAAAC,MAAA;YACA,IAAAO,GAAA,OAAAL,IAAA;YACA,IAAAM,KAAA,OAAAN,IAAA;YACAM,KAAA,CAAAH,OAAA,CAAAG,KAAA,CAAAF,OAAA;YACAN,MAAA,CAAAG,KAAA,UAAAK,KAAA,EAAAD,GAAA;UACA;QACA;MACA;MACA;MACAE,UAAA;MACAC,cAAA;QACA;QACA;QACA;QACA;MACA;MACA;MACAC,kBAAA;MACA;MACAC,kBAAA;QACAC,EAAA;QACA3C,eAAA;QACA4C,mBAAA;QACAC,aAAA;QACAC,aAAA;QACA5C,aAAA;QACA6C,kBAAA;MACA;MACA;MACAC,mBAAA;QACAF,aAAA,GACA;UAAA9B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAhB,aAAA,GACA;UAAAc,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACA+B,eAAA;MACA;MACAC,mBAAA;MACAC,YAAA;MACAC,cAAA;MACAC,uBAAA;MACAC,qBAAA;MACAC,eAAA;MAAA;MACA;MACAC,eAAA;MACAC,eAAA;MACA;MACAC,iBAAA;MACAC,cAAA;QACAC,cAAA;QACAC,cAAA;MACA;MACAC,eAAA;QACAF,cAAA,GACA;UAAA5C,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACA6C,eAAA;MACAC,eAAA;QACAC,iBAAA;QACAC,MAAA;MACA;MACA;MACAC,uBAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA;UACA,OAAAA,IAAA,CAAAjC,OAAA,KAAAJ,IAAA,CAAAsC,GAAA;QACA;MACA;MACAC,kBAAA;MACAC,iBAAA;MACA;MACAC,gBAAA;MACAC,qBAAA;QACA9E,OAAA;QACAC,QAAA;MACA;MACA8E,eAAA;MACA;MACAC,0BAAA;MACA;MACAC,cAAA;MACAC,iBAAA;MACAC,cAAA;QACAC,cAAA;QACAd,MAAA;MACA;MACAe,eAAA;QACAD,cAAA,GACA;UAAAhE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAgE,gBAAA;MACA;MACAC,wBAAA;MACAC,2BAAA;MACAC,wBAAA;QACAL,cAAA;QACAd,MAAA;MACA;MACAoB,yBAAA;QACAN,cAAA,GACA;UAAAhE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAqE,8BAAA;MACA;MACAC,gBAAA;MACAC,aAAA;MACAC,aAAA;MACAC,QAAA;MACA;MACAC,kBAAA;MACAC,gBAAA;MACAC,eAAA;MACAC,SAAA;MACA;MACAC,gBAAA;MACAC,aAAA;MACAC,UAAA;QACAlG,eAAA;QACAmG,YAAA;MACA;MACA;MACAC,oBAAA;MACAC,uBAAA;MACAC,oBAAA;QACAtG,eAAA;QACAuG,kBAAA;QACAC,iBAAA;QACAC,QAAA;MACA;MACAC,iBAAA;MACAC,WAAA;QACAR,YAAA,GACA;UAAAnF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA0F,gBAAA;IACA;EACA;EACAC,QAAA;IACA,0CACAC,YAAA,WAAAA,aAAA;MACA,YAAAxD,qBAAA,SAAAA,qBAAA,CAAArD,gBAAA;IACA;EACA;EACA8G,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA;IACA,IAAAjF,KAAA,OAAAC,IAAA;IACA,IAAAiF,QAAA,GAAAlF,KAAA,CAAAmF,WAAA,WAAAC,MAAA,CAAApF,KAAA,CAAAqF,QAAA,QAAAC,QAAA,iBAAAF,MAAA,CAAApF,KAAA,CAAAuF,OAAA,IAAAD,QAAA;IACA,KAAAhG,SAAA,IAAA4F,QAAA,EAAAA,QAAA;IAEA,KAAAM,OAAA;IACA,IAAAC,yBAAA,IAAAC,IAAA,WAAAC,GAAA;MAAA,OAAAV,KAAA,CAAAlB,eAAA,GAAA4B,GAAA;IAAA;IAEA,KAAAC,QAAA,cAAAF,IAAA,WAAAG,QAAA;MACAZ,KAAA,CAAA7F,aAAA,GAAAyG,QAAA,CAAA9I,IAAA;IACA;IACA,KAAA6I,QAAA,0BAAAF,IAAA,WAAAG,QAAA;MACAZ,KAAA,CAAA5F,kBAAA,GAAAwG,QAAA,CAAA9I,IAAA;IACA;IACA,KAAA6I,QAAA,qBAAAF,IAAA,WAAAG,QAAA;MACAZ,KAAA,CAAAxH,WAAA,GAAAoI,QAAA,CAAA9I,IAAA;IACA;IACA,KAAA+I,kBAAA;IACA;IACA,IAAAC,2CAAA,IAAAL,IAAA,WAAAG,QAAA;MACAZ,KAAA,CAAAzH,iBAAA,GAAAyH,KAAA,CAAAe,UAAA,CAAAH,QAAA,CAAA9I,IAAA;IACA;IACA;IACA,KAAAkJ,sBAAA;EACA;EACAC,OAAA;IACA,gBACAC,eAAA,WAAAA,gBAAAC,GAAA;MACA,IAAAC,aAAA,GAAAD,GAAA,CAAAC,aAAA;MACA,IAAAC,YAAA,GAAAF,GAAA,CAAAE,YAAA;;MAEA;MACA,IAAAA,YAAA,WAAAA,YAAA;QACA,OAAAD,aAAA;MACA;MAEA,OAAAA,aAAA,GAAAC,YAAA;IACA;IACA,mBACAd,OAAA,WAAAA,QAAA;MAAA,IAAAe,MAAA;MACA,IAAAC,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAA9I,WAAA;MACA4I,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAjH,kBAAA;MACAiH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAhH,cAAA;MACAgH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA/G,oBAAA;MACA+G,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA9G,qBAAA;MACA;MACA,OAAA8G,MAAA,CAAAI,cAAA;MACA,OAAAJ,MAAA,CAAAK,YAAA;MACA;MACA,SAAAvH,SAAA,SAAAA,SAAA,CAAAwH,MAAA;QACAN,MAAA,CAAAI,cAAA,QAAAtH,SAAA;QACAkH,MAAA,CAAAK,YAAA,QAAAvH,SAAA;MACA;MACA,KAAAtC,OAAA;MACAwJ,MAAA,CAAA/H,iBAAA;MACA,IAAAsI,4CAAA,EAAAP,MAAA,EAAAd,IAAA,WAAAG,QAAA;QACAU,MAAA,CAAAhJ,uBAAA,GAAAsI,QAAA,CAAAmB,IAAA;QACAT,MAAA,CAAAjJ,KAAA,GAAAuI,QAAA,CAAAvI,KAAA;QACAiJ,MAAA,CAAAvJ,OAAA;MACA;IACA;IACA;IACAiK,MAAA,WAAAA,OAAA;MACA,KAAAtJ,IAAA;MACA,KAAAuJ,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAnI,IAAA;QACA6B,EAAA;QACA7C,MAAA;QACAE,eAAA;QACAkJ,WAAA;QACAC,iBAAA;QACAlJ,gBAAA;QACAC,aAAA;QACAE,eAAA;QACAC,gBAAA;QACA+I,cAAA;QACAC,iBAAA;QACAC,gBAAA;QACArF,iBAAA;QACAsF,QAAA;QACApJ,SAAA;QACAqJ,OAAA;QACAC,SAAA;QACAC,iBAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACA5F,MAAA;MACA;MACA,KAAA6F,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAArK,WAAA,CAAAC,OAAA;MACA,KAAA8E,qBAAA,CAAA9E,OAAA;MACA,KAAA2H,OAAA;MACA,KAAAM,kBAAA;MACA,KAAAG,sBAAA;IACA;IACA,aACAiC,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAA1I,SAAA;MACA,KAAAC,kBAAA;MACA,KAAAC,cAAA;MACA,KAAAC,oBAAA;MACA,KAAAC,qBAAA;MACA,KAAAuI,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAnL,GAAA,GAAAmL,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA1H,EAAA;MAAA;MACA,KAAA1D,YAAA,GAAAkL,SAAA;MACA,KAAAjL,MAAA,GAAAiL,SAAA,CAAAtB,MAAA;MACA,KAAA1J,QAAA,IAAAgL,SAAA,CAAAtB,MAAA;IACA;IACA,aACAyB,SAAA,WAAAA,UAAA;MACA,KAAArB,KAAA;MACA,KAAAvJ,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACA8K,YAAA,WAAAA,aAAApC,GAAA;MAAA,IAAAqC,MAAA;MACA,KAAAvB,KAAA;MACA,IAAAtG,EAAA,GAAAwF,GAAA,CAAAxF,EAAA,SAAA3D,GAAA;MACA,IAAAyL,2CAAA,EAAA9H,EAAA,EAAA8E,IAAA,WAAAG,QAAA;QACA4C,MAAA,CAAA1J,IAAA,GAAA8G,QAAA,CAAA9I,IAAA;QACA0L,MAAA,CAAA9K,IAAA;QACA8K,MAAA,CAAA/K,KAAA;MACA;IACA;IACA,WACAiL,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA7J,IAAA,CAAA6B,EAAA;YACA,IAAAoI,8CAAA,EAAAJ,MAAA,CAAA7J,IAAA,EAAA2G,IAAA,WAAAG,QAAA;cACA+C,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAAjL,IAAA;cACAiL,MAAA,CAAApD,OAAA;cACAoD,MAAA,CAAA9C,kBAAA;YACA;UACA;YACA,IAAAoD,2CAAA,EAAAN,MAAA,CAAA7J,IAAA,EAAA2G,IAAA,WAAAG,QAAA;cACA+C,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAAjL,IAAA;cACAiL,MAAA,CAAApD,OAAA;cACAoD,MAAA,CAAA9C,kBAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAqD,YAAA,WAAAA,aAAA/C,GAAA;MAAA,IAAAgD,MAAA;MACA,IAAAnM,GAAA,GAAAmJ,GAAA,CAAAxF,EAAA,SAAA3D,GAAA;MACA,KAAAoM,QAAA,wBAAAjD,GAAA,CAAAnI,eAAA,aAAAyH,IAAA;QACA,WAAA4D,2CAAA,EAAArM,GAAA;MACA,GAAAyI,IAAA;QACA0D,MAAA,CAAA5D,OAAA;QACA4D,MAAA,CAAAH,UAAA;QACAG,MAAA,CAAAtD,kBAAA;MACA,GAAAyD,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA;MACA,KAAA9F,aAAA;MACA,KAAAD,gBAAA;IACA;IACA,aACAgG,aAAA,WAAAA,cAAA;MACA,SAAA/F,aAAA,CAAAoD,MAAA;QACA,KAAA4C,QAAA,CAAAC,OAAA;QACA;MACA;MACA,KAAAhG,aAAA;MACA,KAAAiG,cAAA;IACA;IAEA,eACAA,cAAA,WAAAA,eAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,KAAA,SAAAnG,aAAA,CAAAoD,MAAA;QACA;QACA,KAAAnD,aAAA;QACA,KAAAF,gBAAA;QACA,KAAAiG,QAAA,CAAAK,OAAA,oDAAAC,MAAA,MAAAtG,aAAA,CAAAoD,MAAA;QACA;MACA;MAEA,IAAAmD,MAAA,QAAAvG,aAAA,CAAAmG,KAAA;MACA,IAAApL,iBAAA,GAAAwL,MAAA;MACA,IAAAC,QAAA,GAAAD,MAAA;MAEA,KAAAE,QAAA,CAAA1L,iBAAA,EAAAwL,MAAA,EAAAvE,IAAA;QACAoE,MAAA,CAAAJ,QAAA,CAAAK,OAAA,IAAAC,MAAA,CAAAE,QAAA;QACA;QACAJ,MAAA,CAAAF,cAAA,CAAAC,KAAA;MACA,GAAAN,KAAA,WAAAa,KAAA;QACAN,MAAA,CAAAnG,aAAA;QACAmG,MAAA,CAAAJ,QAAA,CAAAU,KAAA,IAAAJ,MAAA,CAAAE,QAAA,kDAAAF,MAAA,CAAAI,KAAA,CAAAlL,OAAA,IAAAkL,KAAA;MACA;IACA;IACA,aACAD,QAAA,WAAAA,SAAA1L,iBAAA,EAAA4L,UAAA;MAAA,IAAAC,MAAA;MACA,IAAA9D,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAA9I,WAAA;MACA4I,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAjH,kBAAA;MACAiH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAhH,cAAA;MACAgH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA/G,oBAAA;MACA+G,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA9G,qBAAA;MACA;MACA,OAAA8G,MAAA,CAAAI,cAAA;MACA,OAAAJ,MAAA,CAAAK,YAAA;MACA;MACA,SAAAvH,SAAA,SAAAA,SAAA,CAAAwH,MAAA;QACAN,MAAA,CAAAI,cAAA,QAAAtH,SAAA;QACAkH,MAAA,CAAAK,YAAA,QAAAvH,SAAA;MACA;MACAkH,MAAA,CAAA/H,iBAAA,GAAAA,iBAAA;MACA+H,MAAA,CAAA6D,UAAA,GAAAA,UAAA;MACA,WAAAE,8CAAA,EAAA/D,MAAA,EAAAd,IAAA,WAAAG,QAAA;QACAyE,MAAA,CAAAE,QAAA,CAAA3E,QAAA,CAAA4E,GAAA;MACA;IACA;IACA,uBACAC,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA,IAAAnE,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAA9I,WAAA;MACA4I,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAjH,kBAAA;MACAiH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAhH,cAAA;MACAgH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA/G,oBAAA;MACA+G,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA9G,qBAAA;MACA;MACA,OAAA8G,MAAA,CAAAI,cAAA;MACA,OAAAJ,MAAA,CAAAK,YAAA;MACA;MACA,SAAAvH,SAAA,SAAAA,SAAA,CAAAwH,MAAA;QACAN,MAAA,CAAAI,cAAA,QAAAtH,SAAA;QACAkH,MAAA,CAAAK,YAAA,QAAAvH,SAAA;MACA;MACA,KAAA+J,QAAA;QACAuB,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAApF,IAAA;QACAiF,MAAA,CAAAhH,aAAA;QACA,WAAA4G,8CAAA,EAAA/D,MAAA;MACA,GAAAd,IAAA,WAAAG,QAAA;QACA8E,MAAA,CAAAH,QAAA,CAAA3E,QAAA,CAAA4E,GAAA;QACAE,MAAA,CAAAhH,aAAA;QACAgH,MAAA,CAAAjB,QAAA,CAAAK,OAAA;MACA,GAAAR,KAAA;QACAoB,MAAA,CAAAhH,aAAA;MACA;IACA;IACA,iBACAoH,WAAA,WAAAA,YAAA3E,GAAA;MAAA,IAAA4E,MAAA;MACA,KAAA3B,QAAA,cAAAjD,GAAA,CAAAnI,eAAA;QACA2M,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAApF,IAAA;QACA,IAAAuF,4CAAA;UACArK,EAAA,EAAAwF,GAAA,CAAAxF,EAAA;UACAsK,MAAA;QACA,GAAAxF,IAAA,WAAAG,QAAA;UACAmF,MAAA,CAAA/B,UAAA;UACA+B,MAAA,CAAAxF,OAAA;UACAwF,MAAA,CAAAlF,kBAAA;QACA;MACA;IACA;IACA,cACAqF,yBAAA,WAAAA,0BAAA/E,GAAA;MAAA,IAAAgF,OAAA;MACA,KAAA/B,QAAA;QACAuB,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAApF,IAAA;QACA0F,OAAA,CAAAzH,aAAA;QACA,WAAA0H,+BAAA;UAAAC,MAAA,EAAAlF,GAAA,CAAAkF,MAAA;UAAAC,SAAA,EAAAnF,GAAA,CAAAmF;QAAA;MACA,GAAA7F,IAAA,WAAAG,QAAA;QACAuF,OAAA,CAAAZ,QAAA,CAAA3E,QAAA,CAAA4E,GAAA;QACAW,OAAA,CAAAzH,aAAA;MACA,GAAA4F,KAAA;IACA;IACA,kBACAiC,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,SAAAvO,YAAA,CAAA4J,MAAA;QACA,KAAA4E,MAAA,CAAAC,QAAA;QACA;MACA;;MAEA;MACA,IAAAC,UAAA,QAAA1O,YAAA,CAAAmL,GAAA,WAAAjC,GAAA;QAAA;UACAkF,MAAA,EAAAlF,GAAA,CAAAkF,MAAA;UACAC,SAAA,EAAAnF,GAAA,CAAAmF;QACA;MAAA;MAEA,KAAAlC,QAAA;QACAuB,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAApF,IAAA;QACA+F,OAAA,CAAA9H,aAAA;QACA,WAAAkI,mCAAA,EAAAD,UAAA;MACA,GAAAlG,IAAA,WAAAG,QAAA;QACA4F,OAAA,CAAAjB,QAAA,CAAA3E,QAAA,CAAA4E,GAAA;QACAgB,OAAA,CAAA9H,aAAA;QACA8H,OAAA,CAAA/B,QAAA,CAAAK,OAAA;MACA,GAAAR,KAAA;QACAkC,OAAA,CAAA9H,aAAA;MACA;IACA;IACA,iBACAmI,YAAA,WAAAA,aAAA1F,GAAA;MAAA,IAAA2F,OAAA;MACA,KAAA5I,gBAAA,GAAAiD,GAAA;MACA,KAAApD,cAAA,CAAAC,cAAA;MACA,KAAAD,cAAA,CAAAb,MAAA;MACA,KAAAW,cAAA;MACA,KAAAkJ,SAAA;QACAD,OAAA,CAAAlD,KAAA,CAAA7F,cAAA,CAAAiJ,aAAA;MACA;IACA;IACA,aACAC,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MACA,KAAAtD,KAAA,CAAA7F,cAAA,CAAA8F,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAoD,OAAA,CAAApJ,iBAAA;UACA,IAAAkI,4CAAA;YACArK,EAAA,EAAAuL,OAAA,CAAAhJ,gBAAA,CAAAvC,EAAA;YACAsK,MAAA;YACAjI,cAAA,EAAAkJ,OAAA,CAAAnJ,cAAA,CAAAC,cAAA;YACAd,MAAA,EAAAgK,OAAA,CAAAnJ,cAAA,CAAAb;UACA,GAAAuD,IAAA,WAAAG,QAAA;YACAsG,OAAA,CAAApJ,iBAAA;YACAoJ,OAAA,CAAArJ,cAAA;YACAqJ,OAAA,CAAAlD,UAAA;YACAkD,OAAA,CAAA3G,OAAA;YACA2G,OAAA,CAAArG,kBAAA;UACA,GAAAyD,KAAA;YACA4C,OAAA,CAAApJ,iBAAA;UACA;QACA;MACA;IACA;IACA,mBACAqJ,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,OAAA;MACA,KAAAlJ,gBAAA,QAAA3B,eAAA;MACA,KAAAwB,cAAA,CAAAC,cAAA;MACA,KAAAD,cAAA,CAAAb,MAAA;MACA,KAAAW,cAAA;MACA,KAAAkJ,SAAA;QACAK,OAAA,CAAAxD,KAAA,CAAA7F,cAAA,CAAAiJ,aAAA;MACA;IACA;IACA,sBACAK,0BAAA,WAAAA,2BAAAlG,GAAA;MAAA,IAAAmG,OAAA;MACA,KAAA/I,8BAAA,GAAA4C,GAAA;MACA;MACA,KAAA9C,wBAAA,CAAAL,cAAA,GAAAmD,GAAA,CAAAnD,cAAA;MACA,KAAAK,wBAAA,CAAAnB,MAAA,GAAAiE,GAAA,CAAAjE,MAAA;MACA,KAAAiB,wBAAA;MACA,KAAA4I,SAAA;QACAO,OAAA,CAAA1D,KAAA,CAAAvF,wBAAA,CAAA2I,aAAA;MACA;IACA;IACA,gBACAO,2BAAA,WAAAA,4BAAA;MAAA,IAAAC,OAAA;MACA,KAAA5D,KAAA,CAAAvF,wBAAA,CAAAwF,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA0D,OAAA,CAAApJ,2BAAA;UACA,IAAA4H,4CAAA;YACArK,EAAA,EAAA6L,OAAA,CAAAjJ,8BAAA,CAAA5C,EAAA;YACAsK,MAAA,EAAAuB,OAAA,CAAAjJ,8BAAA,CAAAtF,gBAAA;YAAA;YACA+E,cAAA,EAAAwJ,OAAA,CAAAnJ,wBAAA,CAAAL,cAAA;YACAd,MAAA,EAAAsK,OAAA,CAAAnJ,wBAAA,CAAAnB;UACA,GAAAuD,IAAA,WAAAG,QAAA;YACA4G,OAAA,CAAApJ,2BAAA;YACAoJ,OAAA,CAAArJ,wBAAA;YACAqJ,OAAA,CAAAxD,UAAA;YACAwD,OAAA,CAAAjH,OAAA;YACAiH,OAAA,CAAA3G,kBAAA;UACA,GAAAyD,KAAA;YACAkD,OAAA,CAAApJ,2BAAA;UACA;QACA;MACA;IACA;IACA,iBACAqJ,YAAA,WAAAA,aAAAtG,GAAA;MAAA,IAAAuG,OAAA;MACA,KAAA9H,gBAAA,GAAAuB,GAAA;MACA,KAAAjC,UAAA,CAAAlG,eAAA,GAAAmI,GAAA,CAAAnI,eAAA;MACA,KAAAkG,UAAA,CAAAC,YAAA;MACA,KAAAH,gBAAA;MACA,KAAA+H,SAAA;QACAW,OAAA,CAAA9D,KAAA,CAAA1E,UAAA,CAAA8H,aAAA;MACA;IACA;IACA,WACAW,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA,KAAAhE,KAAA,CAAA1E,UAAA,CAAA2E,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA8D,OAAA,CAAA3I,aAAA;UACA,IAAA+G,4CAAA;YACArK,EAAA,EAAAiM,OAAA,CAAAhI,gBAAA,CAAAjE,EAAA;YACAsK,MAAA;YACA9G,YAAA,EAAAyI,OAAA,CAAA1I,UAAA,CAAAC;UACA,GAAAsB,IAAA,WAAAG,QAAA;YACAgH,OAAA,CAAA3I,aAAA;YACA2I,OAAA,CAAA5I,gBAAA;YACA4I,OAAA,CAAA5D,UAAA;YACA4D,OAAA,CAAArH,OAAA;YACAqH,OAAA,CAAA/G,kBAAA;YACA;YACA+G,OAAA,CAAA5G,sBAAA;UACA,GAAAsD,KAAA;YACAsD,OAAA,CAAA3I,aAAA;UACA;QACA;MACA;IACA;IACA,mBACA4I,sBAAA,WAAAA,uBAAA1G,GAAA;MAAA,IAAA2G,OAAA;MACA,KAAApI,iBAAA,GAAAyB,GAAA;MACA,KAAA7B,oBAAA,CAAAtG,eAAA,GAAAmI,GAAA,CAAAnI,eAAA;MACA,KAAAsG,oBAAA,CAAAC,kBAAA,GAAA4B,GAAA,CAAA5B,kBAAA;MACA,KAAAD,oBAAA,CAAAE,iBAAA,GAAA2B,GAAA,CAAA3B,iBAAA;MACA,KAAAF,oBAAA,CAAAG,QAAA,GAAA0B,GAAA,CAAA1B,QAAA;MACA,KAAAL,oBAAA;MACA,KAAA2H,SAAA;QACAe,OAAA,CAAAlE,KAAA,CAAAtE,oBAAA,CAAA0H,aAAA;MACA;IACA;IACA,aACAe,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,OAAA;MACA,KAAApE,KAAA,CAAAtE,oBAAA,CAAAuE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAkE,OAAA,CAAA3I,uBAAA;UACA,IAAA2G,4CAAA;YACArK,EAAA,EAAAqM,OAAA,CAAAtI,iBAAA,CAAA/D,EAAA;YACAsK,MAAA;YAAA;YACA1G,kBAAA,EAAAyI,OAAA,CAAA1I,oBAAA,CAAAC,kBAAA;YACAC,iBAAA,EAAAwI,OAAA,CAAA1I,oBAAA,CAAAE,iBAAA;YACAC,QAAA,EAAAuI,OAAA,CAAA1I,oBAAA,CAAAG;UACA,GAAAgB,IAAA,WAAAG,QAAA;YACAoH,OAAA,CAAA3I,uBAAA;YACA2I,OAAA,CAAA5I,oBAAA;YACA4I,OAAA,CAAAhE,UAAA;YACAgE,OAAA,CAAAzH,OAAA;YACAyH,OAAA,CAAAnH,kBAAA;UACA,GAAAyD,KAAA;YACA0D,OAAA,CAAA3I,uBAAA;UACA;QACA;MACA;IACA;IACA,cACA4I,WAAA,WAAAA,YAAA9G,GAAA;MAAA,IAAA+G,OAAA;MAAA,WAAAC,kBAAA,CAAA1G,OAAA,mBAAA2G,oBAAA,CAAA3G,OAAA,IAAA4G,IAAA,UAAAC,QAAA;QAAA,IAAAC,OAAA,EAAAzQ,IAAA,EAAA6D,EAAA;QAAA,WAAAyM,oBAAA,CAAA3G,OAAA,IAAA+G,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAEA;cACAJ,OAAA,GAAApH,GAAA,CAAA0H,cAAA;cAAAH,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAE,4CAAA,EAAAP,OAAA;YAAA;cAAAzQ,IAAA,GAAA4Q,QAAA,CAAAK,IAAA;cAAA,MACAjR,IAAA,iBAAAkR,gBAAA,EAAAlR,IAAA,CAAA6D,EAAA;gBAAA+M,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAjN,EAAA,GAAA7D,IAAA,CAAA6D,EAAA;cACAuM,OAAA,CAAAtE,KAAA,CAAA/L,kBAAA,CAAAa,IAAA;cAAAgQ,QAAA,CAAAE,IAAA;cAAA,OACAV,OAAA,CAAAnB,SAAA;YAAA;cACAmB,OAAA,CAAAtE,KAAA,CAAA/L,kBAAA,CAAAoK,KAAA;cACAiG,OAAA,CAAAtE,KAAA,CAAA/L,kBAAA,CAAAoR,IAAA,CAAAtN,EAAA;cAAA+M,QAAA,CAAAE,IAAA;cAAA;YAAA;cAEAV,OAAA,CAAAzD,QAAA,CAAAU,KAAA;YAAA;cAAAuD,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAQ,EAAA,GAAAR,QAAA;cAGAS,OAAA,CAAAhE,KAAA,cAAAuD,QAAA,CAAAQ,EAAA;cACAhB,OAAA,CAAAzD,QAAA,CAAAU,KAAA,iBAAAuD,QAAA,CAAAQ,EAAA,CAAAjP,OAAA;YAAA;YAAA;cAAA,OAAAyO,QAAA,CAAAU,IAAA;UAAA;QAAA,GAAAd,OAAA;MAAA;IAEA;IAEA,eACAe,gBAAA,WAAAA,iBAAAC,KAAA;MAAA,IAAAC,OAAA;MACA,IAAAD,KAAA;QACA,KAAAlF,QAAA;UACAuB,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAApF,IAAA;UACA8I,OAAA,CAAAzP,IAAA,CAAAyI,QAAA;QACA,GAAA+B,KAAA;UACAiF,OAAA,CAAAzP,IAAA,CAAAyI,QAAA;QACA;MACA;IACA;IACA,kBACAiH,qBAAA,WAAAA,sBAAAF,KAAA,EAAAnI,GAAA;MAAA,IAAAsI,OAAA;MACA,IAAAH,KAAA;QACA,KAAAlF,QAAA;UACAuB,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAApF,IAAA;UACA;UACA,IAAAsD,8CAAA;YACApI,EAAA,EAAAwF,GAAA,CAAAxF,EAAA;YACA4G,QAAA;UACA,GAAA9B,IAAA,WAAAG,QAAA;YACA6I,OAAA,CAAAzF,UAAA;YACAyF,OAAA,CAAAlJ,OAAA;YACAkJ,OAAA,CAAA5I,kBAAA;UACA;QACA,GAAAyD,KAAA;UACA;UACAnD,GAAA,CAAAoB,QAAA;QACA;MACA;QACA;QACA,IAAAwB,8CAAA;UACApI,EAAA,EAAAwF,GAAA,CAAAxF,EAAA;UACA4G,QAAA;QACA,GAAA9B,IAAA,WAAAG,QAAA;UACA6I,OAAA,CAAAzF,UAAA;UACAyF,OAAA,CAAAlJ,OAAA;UACAkJ,OAAA,CAAA5I,kBAAA;QACA;MACA;IACA;IACA,eACAA,kBAAA,WAAAA,mBAAA;MAAA,IAAA6I,OAAA;MACA,IAAAnI,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAA9I,WAAA;MACA;MACA,SAAA0B,SAAA,SAAAA,SAAA,CAAAwH,MAAA;QACAN,MAAA,CAAAI,cAAA,QAAAtH,SAAA;QACAkH,MAAA,CAAAK,YAAA,QAAAvH,SAAA;MACA;MACA,IAAAsP,sCAAA,EAAApI,MAAA,EAAAd,IAAA,WAAAG,QAAA;QACA8I,OAAA,CAAAlO,cAAA,GAAAoF,QAAA,CAAA9I,IAAA;MACA;IACA;IACA,gBACA8R,oBAAA,WAAAA,qBAAAzI,GAAA;MAAA,IAAA0I,OAAA;MACA,KAAAnO,kBAAA;QACAC,EAAA,EAAAwF,GAAA,CAAAxF,EAAA;QACA3C,eAAA,EAAAmI,GAAA,CAAAnI,eAAA;QACA4C,mBAAA,EAAAuF,GAAA,CAAApI,QAAA;QACA8C,aAAA,EAAAsF,GAAA,CAAArI,MAAA;QACAgD,aAAA;QACA5C,aAAA;QACA6C,kBAAA;MACA;;MAEA;MACA,IAAA+N,kDAAA;QACA3H,iBAAA,EAAAhB,GAAA,CAAAgB,iBAAA;QACA4H,UAAA,EAAA5I,GAAA,CAAA4I;MACA,GAAAtJ,IAAA,WAAAG,QAAA;QACAiJ,OAAA,CAAA5N,eAAA,GAAA2E,QAAA,CAAA9I,IAAA;MACA;MACA;MACA,IAAAkS,+CAAA,IAAAvJ,IAAA,WAAAG,QAAA;QACAiJ,OAAA,CAAArM,iBAAA,GAAAoD,QAAA,CAAA9I,IAAA;MACA;MACA,KAAA2D,kBAAA;IACA;IACA,cACAwO,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,KAAAtG,KAAA,uBAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAhM,IAAA;YACAqS,aAAA,EAAAD,OAAA,CAAAxO,kBAAA,CAAAC,EAAA;YACAE,aAAA,EAAAqO,OAAA,CAAAxO,kBAAA,CAAAG,aAAA;YACAC,aAAA,EAAAoO,OAAA,CAAAxO,kBAAA,CAAAI,aAAA;YACA5C,aAAA,EAAAgR,OAAA,CAAAxO,kBAAA,CAAAxC,aAAA;YACA6C,kBAAA,EAAAmO,OAAA,CAAAxO,kBAAA,CAAAK;UACA;;UAEA;UACA,IAAAqO,mCAAA,EAAAtS,IAAA,EAAA2I,IAAA,WAAAG,QAAA;YACAsJ,OAAA,CAAAlG,UAAA;YACAkG,OAAA,CAAAzO,kBAAA;YACAyO,OAAA,CAAA3J,OAAA;YACA;YACA2J,OAAA,CAAAlJ,sBAAA;YACAkJ,OAAA,CAAArJ,kBAAA;UACA,GAAAyD,KAAA;YACA4F,OAAA,CAAAxD,QAAA;UACA;QACA;MACA;IACA;IACA,iBACA1F,sBAAA,WAAAA,uBAAA;MAAA,IAAAqJ,OAAA;MACA,IAAA9I,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAA9I,WAAA;MACA4I,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAjH,kBAAA;MACAiH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAAhH,cAAA;MACAgH,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA/G,oBAAA;MACA+G,MAAA,QAAAG,YAAA,CAAAH,MAAA,OAAA9G,qBAAA;MACA;MACA,OAAA8G,MAAA,CAAAI,cAAA;MACA,OAAAJ,MAAA,CAAAK,YAAA;MACA;MACA,SAAAvH,SAAA,SAAAA,SAAA,CAAAwH,MAAA;QACAN,MAAA,CAAAI,cAAA,QAAAtH,SAAA;QACAkH,MAAA,CAAAK,YAAA,QAAAvH,SAAA;MACA;MACAkH,MAAA,CAAA/H,iBAAA;MACA+H,MAAA,CAAA3I,OAAA,QAAA8E,qBAAA,CAAA9E,OAAA;MACA2I,MAAA,CAAA1I,QAAA,QAAA6E,qBAAA,CAAA7E,QAAA;MACA,IAAAiJ,4CAAA,EAAAP,MAAA,EAAAd,IAAA,WAAAG,QAAA;QACAyJ,OAAA,CAAA5M,gBAAA,GAAAmD,QAAA,CAAAmB,IAAA;QACAsI,OAAA,CAAA1M,eAAA,GAAAiD,QAAA,CAAAvI,KAAA;MACA;IACA;IACA,cACAiS,oBAAA,WAAAA,qBAAAnJ,GAAA;MAAA,IAAAoJ,OAAA;MACA,KAAA7O,kBAAA;QACAC,EAAA,EAAAwF,GAAA,CAAAxF,EAAA;QACA3C,eAAA,EAAAmI,GAAA,CAAAnI,eAAA;QACA4C,mBAAA;QACAC,aAAA;QACAC,aAAA;QACA5C,aAAA;QACA6C,kBAAA;MACA;;MAEA;MACA,IAAA+N,kDAAA;QACA3H,iBAAA,EAAAhB,GAAA,CAAAgB,iBAAA;QACA4H,UAAA,EAAA5I,GAAA,CAAA4I;MACA,GAAAtJ,IAAA,WAAAG,QAAA;QACA2J,OAAA,CAAAtO,eAAA,GAAA2E,QAAA,CAAA9I,IAAA;MACA;MACA;MACA,IAAAkS,+CAAA,IAAAvJ,IAAA,WAAAG,QAAA;QACA2J,OAAA,CAAA/M,iBAAA,GAAAoD,QAAA,CAAA9I,IAAA;MACA;MACA,KAAA2D,kBAAA;IACA;IACA,eACA+O,sBAAA,WAAAA,uBAAArJ,GAAA;MAAA,IAAAsJ,OAAA;MACA,KAAArG,QAAA,mBAAAjD,GAAA,CAAAnI,eAAA,aAAAyH,IAAA;QACA,WAAA4D,2CAAA,EAAAlD,GAAA,CAAAxF,EAAA;MACA,GAAA8E,IAAA;QACAgK,OAAA,CAAAzJ,sBAAA;QACAyJ,OAAA,CAAAzG,UAAA;MACA,GAAAM,KAAA;IACA;IACA,eACAoG,sBAAA,WAAAA,uBAAAvJ,GAAA;MAAA,IAAAwJ,OAAA;MACA,KAAA/K,gBAAA,GAAAuB,GAAA;MACA,KAAAjC,UAAA,CAAAlG,eAAA,GAAAmI,GAAA,CAAAnI,eAAA;MACA,KAAAkG,UAAA,CAAAC,YAAA;MACA,KAAAH,gBAAA;MACA,KAAA+H,SAAA;QACA4D,OAAA,CAAA/G,KAAA,CAAA1E,UAAA,CAAA8H,aAAA;MACA;IACA;IACA,mBACA4D,0BAAA,WAAAA,2BAAAC,OAAA;MACA,KAAAnN,qBAAA,CAAA7E,QAAA,GAAAgS,OAAA;MACA,KAAA7J,sBAAA;IACA;IACA,iBACA8J,6BAAA,WAAAA,8BAAAC,OAAA;MACA,KAAArN,qBAAA,CAAA9E,OAAA,GAAAmS,OAAA;MACA,KAAA/J,sBAAA;IACA;IACA,kBACAgK,qBAAA,WAAAA,sBAAA;MACA,KAAApN,0BAAA,SAAAA,0BAAA;IACA;IACA,aACAqN,kBAAA,WAAAA,mBAAAhF,MAAA;MACA;MACA,KAAAtN,WAAA,CAAAiB,SAAA;MACA,KAAAjB,WAAA,CAAAM,gBAAA,GAAAgN,MAAA;MACA,KAAAjD,WAAA;IACA;IAEA,eACAkI,mBAAA,WAAAA,oBAAA;MACA;MACA,KAAAvS,WAAA,CAAAM,gBAAA;MACA;MACA,KAAAN,WAAA,CAAAiB,SAAA,QAAAjB,WAAA,CAAAiB,SAAA;MACA,KAAAoJ,WAAA;MAEA,SAAArK,WAAA,CAAAiB,SAAA;QACA,KAAA6K,QAAA,CAAA0G,IAAA;MACA;QACA,KAAA1G,QAAA,CAAA0G,IAAA;MACA;IACA;IACA,eACAC,cAAA,WAAAA,eAAA5I,OAAA,EAAA6I,eAAA;MACA,IAAA/N,GAAA,OAAAtC,IAAA;MACA,IAAAK,GAAA,OAAAL,IAAA,CAAAwH,OAAA;MACA,IAAA8I,MAAA,GAAAD,eAAA,OAAArQ,IAAA,CAAAqQ,eAAA;;MAEA;MACA,IAAAE,SAAA,GAAAC,IAAA,CAAAC,IAAA,EAAApQ,GAAA,GAAAiC,GAAA;;MAEA;MACA,IAAAgO,MAAA,IAAAhO,GAAA,GAAAgO,MAAA;QACA;MACA;MAEA,IAAAC,SAAA;QACA;MACA,WAAAA,SAAA;QACA;MACA,WAAAA,SAAA;QACA;MACA;QACA;MACA;IACA;IACA,eACAG,cAAA,WAAAA,eAAAlJ,OAAA,EAAA6I,eAAA;MACA,IAAA/N,GAAA,OAAAtC,IAAA;MACA,IAAAK,GAAA,OAAAL,IAAA,CAAAwH,OAAA;MACA,IAAA8I,MAAA,GAAAD,eAAA,OAAArQ,IAAA,CAAAqQ,eAAA;MACA;MACA,IAAAE,SAAA,GAAAC,IAAA,CAAAC,IAAA,EAAApQ,GAAA,GAAAiC,GAAA;MACA;MACA,IAAAgO,MAAA,IAAAhO,GAAA,GAAAgO,MAAA;QACA;MACA;MAEA,IAAAC,SAAA;QACA;MACA,WAAAA,SAAA;QACA;MACA,WAAAA,SAAA;QACA;MACA,WAAAA,SAAA;QACA;MACA;QACA;MACA;IACA;IACA,iBACAI,uBAAA,WAAAA,wBAAA;MACA,KAAA3K,sBAAA;MACA,KAAAyD,QAAA,CAAAK,OAAA;IACA;IACA,aACA8G,cAAA,WAAAA,eAAAzK,GAAA;MACA,IAAA7D,GAAA,OAAAtC,IAAA;MACA,IAAAwH,OAAA,OAAAxH,IAAA,CAAAmG,GAAA,CAAAqB,OAAA;MACA,IAAA5I,SAAA;MACA,IAAAiS,WAAA;MACA;MACA,IAAA1K,GAAA,CAAAlI,gBAAA;QACA;QACA,IAAAkI,GAAA,CAAA9H,gBAAA;UACA,IAAAA,gBAAA,OAAA2B,IAAA,CAAAmG,GAAA,CAAA9H,gBAAA;UACA,IAAAA,gBAAA,GAAAmJ,OAAA;YACA5I,SAAA;YACAiS,WAAA,GAAAL,IAAA,CAAAC,IAAA,EAAApS,gBAAA,GAAAmJ,OAAA;UACA;QACA;MACA,WAAArB,GAAA,CAAAlI,gBAAA;QACA;QACA,IAAAqE,GAAA,GAAAkF,OAAA;UACA5I,SAAA;UACAiS,WAAA,GAAAL,IAAA,CAAAC,IAAA,EAAAnO,GAAA,GAAAkF,OAAA;QACA;MACA,WAAArB,GAAA,CAAAlI,gBAAA;QACA;QACA,IAAAqE,GAAA,GAAAkF,OAAA;UACA5I,SAAA;UACAiS,WAAA,GAAAL,IAAA,CAAAC,IAAA,EAAAnO,GAAA,GAAAkF,OAAA;QACA;MACA;MAEA;QACA5I,SAAA,EAAAA,SAAA;QACAiS,WAAA,EAAAA;MACA;IACA;IAEA;IAEA;IACAC,qBAAA,WAAAA,sBAAA3K,GAAA;MACA,KAAA7E,qBAAA,GAAA6E,GAAA;MACA,KAAA5E,eAAA,GAAA4E,GAAA;MACA,KAAAjF,mBAAA;MACA,KAAA6P,aAAA,CAAA5K,GAAA,CAAAxF,EAAA;IACA;IAEA,aACAoQ,aAAA,WAAAA,cAAAC,qBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA9D,kBAAA,CAAA1G,OAAA,mBAAA2G,oBAAA,CAAA3G,OAAA,IAAA4G,IAAA,UAAA6D,SAAA;QAAA,IAAAC,oBAAA,EAAAC,eAAA,EAAAC,UAAA;QAAA,WAAAjE,oBAAA,CAAA3G,OAAA,IAAA+G,IAAA,UAAA8D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5D,IAAA,GAAA4D,SAAA,CAAA3D,IAAA;YAAA;cAAA2D,SAAA,CAAA5D,IAAA;cAAA4D,SAAA,CAAA3D,IAAA;cAAA,OAGA,IAAA4D,oCAAA,EAAAR,qBAAA;YAAA;cAAAG,oBAAA,GAAAI,SAAA,CAAAxD,IAAA;cACAkD,OAAA,CAAA9P,YAAA,GAAAgQ,oBAAA,CAAArU,IAAA;;cAEA;cAAAyU,SAAA,CAAA3D,IAAA;cAAA,OACA,IAAA6D,wCAAA,EAAAT,qBAAA;YAAA;cAAAI,eAAA,GAAAG,SAAA,CAAAxD,IAAA;cACAsD,UAAA,GAAAD,eAAA,CAAAtU,IAAA,QAEA;cACAmU,OAAA,CAAA7P,cAAA,GAAAiQ,UAAA,CAAAK,MAAA,WAAAC,KAAA;gBAAA,OAAAA,KAAA,CAAAC,cAAA;cAAA;;cAEA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;;cAEA;cAAA,KACAX,OAAA,CAAA9P,YAAA;gBAAAoQ,SAAA,CAAA3D,IAAA;gBAAA;cAAA;cAAA2D,SAAA,CAAA3D,IAAA;cAAA,OACAqD,OAAA,CAAAY,2BAAA,CAAAZ,OAAA,CAAA9P,YAAA,CAAAR,EAAA;YAAA;cAAA4Q,SAAA,CAAA3D,IAAA;cAAA;YAAA;cAAA2D,SAAA,CAAA5D,IAAA;cAAA4D,SAAA,CAAArD,EAAA,GAAAqD,SAAA;cAGApD,OAAA,CAAAhE,KAAA,cAAAoH,SAAA,CAAArD,EAAA;cACA+C,OAAA,CAAAxH,QAAA,CAAAU,KAAA;YAAA;YAAA;cAAA,OAAAoH,SAAA,CAAAnD,IAAA;UAAA;QAAA,GAAA8C,QAAA;MAAA;IAEA;IAEA,kBACAW,2BAAA,WAAAA,4BAAAC,OAAA;MAAA,IAAAC,OAAA;MAAA,WAAA5E,kBAAA,CAAA1G,OAAA,mBAAA2G,oBAAA,CAAA3G,OAAA,IAAA4G,IAAA,UAAA2E,SAAA;QAAA,IAAApM,QAAA;QAAA,WAAAwH,oBAAA,CAAA3G,OAAA,IAAA+G,IAAA,UAAAyE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvE,IAAA,GAAAuE,SAAA,CAAAtE,IAAA;YAAA;cAAAsE,SAAA,CAAAvE,IAAA;cAAAuE,SAAA,CAAAtE,IAAA;cAAA,OAEA,IAAAuE,4CAAA,EAAAL,OAAA;YAAA;cAAAlM,QAAA,GAAAsM,SAAA,CAAAnE,IAAA;cACAgE,OAAA,CAAA1Q,uBAAA,GAAAuE,QAAA,CAAA9I,IAAA;cAAAoV,SAAA,CAAAtE,IAAA;cAAA;YAAA;cAAAsE,SAAA,CAAAvE,IAAA;cAAAuE,SAAA,CAAAhE,EAAA,GAAAgE,SAAA;cAEA/D,OAAA,CAAAhE,KAAA,cAAA+H,SAAA,CAAAhE,EAAA;YAAA;YAAA;cAAA,OAAAgE,SAAA,CAAA9D,IAAA;UAAA;QAAA,GAAA4D,QAAA;MAAA;IAEA;IAEA,YACAI,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MAAA,WAAAlF,kBAAA,CAAA1G,OAAA,mBAAA2G,oBAAA,CAAA3G,OAAA,IAAA4G,IAAA,UAAAiF,SAAA;QAAA,IAAA1M,QAAA;QAAA,WAAAwH,oBAAA,CAAA3G,OAAA,IAAA+G,IAAA,UAAA+E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7E,IAAA,GAAA6E,SAAA,CAAA5E,IAAA;YAAA;cAAA,IAEAyE,OAAA,CAAAvN,YAAA;gBAAA0N,SAAA,CAAA5E,IAAA;gBAAA;cAAA;cACAyE,OAAA,CAAA5I,QAAA,CAAAC,OAAA;cAAA,OAAA8I,SAAA,CAAAC,MAAA;YAAA;cAAAD,SAAA,CAAA7E,IAAA;cAAA6E,SAAA,CAAA5E,IAAA;cAAA,OAKAyE,OAAA,CAAAjJ,QAAA;gBACAuB,iBAAA;gBACAC,gBAAA;gBACAC,IAAA;cACA;YAAA;cAAA2H,SAAA,CAAA5E,IAAA;cAAA,OAEA,IAAAwE,kCAAA,EAAAC,OAAA,CAAA/Q,qBAAA,CAAAX,EAAA;YAAA;cAAAiF,QAAA,GAAA4M,SAAA,CAAAzE,IAAA;cACAsE,OAAA,CAAA5I,QAAA,CAAAK,OAAA;;cAEA;cAAA0I,SAAA,CAAA5E,IAAA;cAAA,OACAyE,OAAA,CAAAtB,aAAA,CAAAsB,OAAA,CAAA/Q,qBAAA,CAAAX,EAAA;YAAA;cAEA;cACA0R,OAAA,CAAA9M,OAAA;cAAAiN,SAAA,CAAA5E,IAAA;cAAA;YAAA;cAAA4E,SAAA,CAAA7E,IAAA;cAAA6E,SAAA,CAAAtE,EAAA,GAAAsE,SAAA;cAEA,IAAAA,SAAA,CAAAtE,EAAA;gBACAmE,OAAA,CAAA5I,QAAA,CAAAU,KAAA,gBAAAqI,SAAA,CAAAtE,EAAA,CAAAjP,OAAA,IAAAuT,SAAA,CAAAtE,EAAA;cACA;YAAA;YAAA;cAAA,OAAAsE,SAAA,CAAApE,IAAA;UAAA;QAAA,GAAAkE,QAAA;MAAA;IAEA;IAEA,aACAI,kBAAA,WAAAA,mBAAA;MACA;MACA,UAAA5N,YAAA;QACA,KAAA2E,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAA1H,eAAA;QACAC,iBAAA;QACAC,MAAA;MACA;MACA,KAAAH,eAAA;IACA;IAEA,aACA4Q,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAzF,kBAAA,CAAA1G,OAAA,mBAAA2G,oBAAA,CAAA3G,OAAA,IAAA4G,IAAA,UAAAwF,SAAA;QAAA,WAAAzF,oBAAA,CAAA3G,OAAA,IAAA+G,IAAA,UAAAsF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApF,IAAA,GAAAoF,SAAA,CAAAnF,IAAA;YAAA;cAAAmF,SAAA,CAAApF,IAAA;cAAAoF,SAAA,CAAAnF,IAAA;cAAA,OAEA,IAAA8E,uCAAA,EACAE,OAAA,CAAAtR,qBAAA,CAAAX,EAAA,EACAiS,OAAA,CAAA5Q,eAAA,CAAAC,iBAAA,EACA2Q,OAAA,CAAA5Q,eAAA,CAAAE,MACA;YAAA;cAEA0Q,OAAA,CAAAnJ,QAAA,CAAAK,OAAA;cACA8I,OAAA,CAAA7Q,eAAA;;cAEA;cAAAgR,SAAA,CAAAnF,IAAA;cAAA,OACAgF,OAAA,CAAA7B,aAAA,CAAA6B,OAAA,CAAAtR,qBAAA,CAAAX,EAAA;YAAA;cAEA;cACAiS,OAAA,CAAArN,OAAA;cAAAwN,SAAA,CAAAnF,IAAA;cAAA;YAAA;cAAAmF,SAAA,CAAApF,IAAA;cAAAoF,SAAA,CAAA7E,EAAA,GAAA6E,SAAA;cAEA5E,OAAA,CAAAhE,KAAA,YAAA4I,SAAA,CAAA7E,EAAA;cACA0E,OAAA,CAAAnJ,QAAA,CAAAU,KAAA,eAAA4I,SAAA,CAAA7E,EAAA,CAAAjP,OAAA,IAAA8T,SAAA,CAAA7E,EAAA;YAAA;YAAA;cAAA,OAAA6E,SAAA,CAAA3E,IAAA;UAAA;QAAA,GAAAyE,QAAA;MAAA;IAEA;IAEA,aACAG,aAAA,WAAAA,cAAA;MACA;MACA,UAAAlO,YAAA;QACA,KAAA2E,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAA/H,cAAA;QACAC,cAAA;QACAC,cAAA;MACA;MACA,KAAAH,iBAAA;IACA;IAEA,aACAuR,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA/F,kBAAA,CAAA1G,OAAA,mBAAA2G,oBAAA,CAAA3G,OAAA,IAAA4G,IAAA,UAAA8F,SAAA;QAAA,IAAAC,gBAAA;QAAA,WAAAhG,oBAAA,CAAA3G,OAAA,IAAA+G,IAAA,UAAA6F,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3F,IAAA,GAAA2F,SAAA,CAAA1F,IAAA;YAAA;cAAA,IAEAsF,OAAA,CAAApO,YAAA;gBAAAwO,SAAA,CAAA1F,IAAA;gBAAA;cAAA;cACAsF,OAAA,CAAAzJ,QAAA,CAAAC,OAAA;cAAA,OAAA4J,SAAA,CAAAb,MAAA;YAAA;cAAAa,SAAA,CAAA1F,IAAA;cAAA,OAIAsF,OAAA,CAAAtK,KAAA,CAAAjH,cAAA,CAAAkH,QAAA;YAAA;cAEAuK,gBAAA;gBACAtB,OAAA,EAAAoB,OAAA,CAAA/R,YAAA,CAAAR,EAAA;gBACAiB,cAAA,EAAAsR,OAAA,CAAAvR,cAAA,CAAAC,cAAA;gBACAC,cAAA,EAAAqR,OAAA,CAAAvR,cAAA,CAAAE,cAAA;cACA;cAAAyR,SAAA,CAAA1F,IAAA;cAAA,OAEA,IAAA2F,yCAAA,EAAAH,gBAAA;YAAA;cAEAF,OAAA,CAAAzJ,QAAA,CAAAK,OAAA;cACAoJ,OAAA,CAAAxR,iBAAA;;cAEA;cAAA4R,SAAA,CAAA1F,IAAA;cAAA,OACAsF,OAAA,CAAArB,2BAAA,CAAAqB,OAAA,CAAA/R,YAAA,CAAAR,EAAA;YAAA;YAAA;cAAA,OAAA2S,SAAA,CAAAlF,IAAA;UAAA;QAAA,GAAA+E,QAAA;MAAA;IACA;IAEA,aACAK,eAAA,WAAAA,gBAAA7B,KAAA;MAAA,IAAA8B,OAAA;MAAA,WAAAtG,kBAAA,CAAA1G,OAAA,mBAAA2G,oBAAA,CAAA3G,OAAA,IAAA4G,IAAA,UAAAqG,SAAA;QAAA,IAAA9N,QAAA,EAAA+N,WAAA;QAAA,WAAAvG,oBAAA,CAAA3G,OAAA,IAAA+G,IAAA,UAAAoG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlG,IAAA,GAAAkG,SAAA,CAAAjG,IAAA;YAAA;cAAAiG,SAAA,CAAAlG,IAAA;cAAAkG,SAAA,CAAAjG,IAAA;cAAA,OAEA,IAAAuE,4CAAA,EAAAR,KAAA,CAAAhR,EAAA;YAAA;cAAAiF,QAAA,GAAAiO,SAAA,CAAA9F,IAAA;cACA4F,WAAA,GAAA/N,QAAA,CAAA9I,IAAA,QAEA;cACA2W,OAAA,CAAAhS,eAAA,OAAA+E,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAkL,KAAA;gBACAgC,WAAA,EAAAA;cAAA,EACA;cAEAF,OAAA,CAAAjS,eAAA;cAAAqS,SAAA,CAAAjG,IAAA;cAAA;YAAA;cAAAiG,SAAA,CAAAlG,IAAA;cAAAkG,SAAA,CAAA3F,EAAA,GAAA2F,SAAA;cAEA1F,OAAA,CAAAhE,KAAA,cAAA0J,SAAA,CAAA3F,EAAA;cACAuF,OAAA,CAAAhK,QAAA,CAAAU,KAAA;YAAA;YAAA;cAAA,OAAA0J,SAAA,CAAAzF,IAAA;UAAA;QAAA,GAAAsF,QAAA;MAAA;IAEA;IAEA,aACAI,iBAAA,WAAAA,kBAAAC,SAAA;MACA,KAAAA,SAAA;MAEA,IAAAzT,KAAA,OAAAN,IAAA,CAAA+T,SAAA;MACA,IAAAzR,GAAA,OAAAtC,IAAA;MACA,IAAAgU,MAAA,GAAA1R,GAAA,GAAAhC,KAAA;MACA,IAAA2T,SAAA,GAAAzD,IAAA,CAAA0D,KAAA,CAAAF,MAAA;MACA,IAAAG,WAAA,GAAA3D,IAAA,CAAA0D,KAAA,CAAAF,MAAA;MAEA,IAAAC,SAAA;QACA,UAAAlK,MAAA,CAAAkK,SAAA,kBAAAlK,MAAA,CAAAoK,WAAA;MACA;QACA,UAAApK,MAAA,CAAAoK,WAAA;MACA;IACA;IAEA,eACAC,wBAAA,WAAAA,yBAAAC,UAAA;MACA,KAAAA,UAAA;MAEA,IAAAC,SAAA,GAAAD,UAAA,CAAAE,WAAA;MACA,IAAAD,SAAA,CAAAE,QAAA,UAAAF,SAAA,CAAAE,QAAA,UAAAF,SAAA,CAAAE,QAAA;QACA;MACA,WAAAF,SAAA,CAAAE,QAAA,UAAAF,SAAA,CAAAE,QAAA;QACA;MACA,WAAAF,SAAA,CAAAE,QAAA,SAAAF,SAAA,CAAAE,QAAA,WAAAF,SAAA,CAAAE,QAAA;QACA;MACA;QACA;MACA;IACA;IAEA,qBACAC,oBAAA,WAAAA,qBAAA;MACA;MACA,KAAAtT,YAAA;MACA,KAAAC,cAAA;MACA,KAAAC,uBAAA;MACA,KAAAC,qBAAA;MACA,KAAAC,eAAA;MACA,KAAAE,eAAA;;MAEA;MACA,KAAAE,cAAA;QACAC,cAAA;QACAC,cAAA;MACA;MACA,KAAAG,eAAA;QACAC,iBAAA;QACAC,MAAA;MACA;;MAEA;MACA,KAAAV,eAAA;MACA,KAAAE,iBAAA;MACA,KAAAK,eAAA;IACA;IAEA,qBACA2S,gBAAA,WAAAA,iBAAA;MACA;MACA,KAAAjT,eAAA;IACA;IAEA,qBACAkT,kBAAA,WAAAA,mBAAA;MACA;MACA,KAAAhT,cAAA;QACAC,cAAA;QACAC,cAAA;MACA;MACA;MACA,SAAA+G,KAAA,CAAAjH,cAAA;QACA,KAAAiH,KAAA,CAAAjH,cAAA,CAAAqK,aAAA;MACA;IACA;IAEA,qBACA4I,gBAAA,WAAAA,iBAAA;MACA;MACA,KAAA5S,eAAA;QACAC,iBAAA;QACAC,MAAA;MACA;MACA;MACA,SAAA0G,KAAA,CAAA5G,eAAA;QACA,KAAA4G,KAAA,CAAA5G,eAAA,CAAAgK,aAAA;MACA;IACA;IAEA,aACA6I,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA3H,kBAAA,CAAA1G,OAAA,mBAAA2G,oBAAA,CAAA3G,OAAA,IAAA4G,IAAA,UAAA0H,SAAA;QAAA,WAAA3H,oBAAA,CAAA3G,OAAA,IAAA+G,IAAA,UAAAwH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtH,IAAA,GAAAsH,SAAA,CAAArH,IAAA;YAAA;cAAA,KACAkH,OAAA,CAAAxT,qBAAA;gBAAA2T,SAAA,CAAArH,IAAA;gBAAA;cAAA;cAAAqH,SAAA,CAAArH,IAAA;cAAA,OACAkH,OAAA,CAAA/D,aAAA,CAAA+D,OAAA,CAAAxT,qBAAA,CAAAX,EAAA;YAAA;cACAmU,OAAA,CAAArL,QAAA,CAAAK,OAAA;YAAA;YAAA;cAAA,OAAAmL,SAAA,CAAA7G,IAAA;UAAA;QAAA,GAAA2G,QAAA;MAAA;IAEA;EACA;AACA", "ignoreList": []}]}