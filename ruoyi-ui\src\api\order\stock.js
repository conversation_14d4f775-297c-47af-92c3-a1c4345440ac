import request from '@/utils/request'

// 查询库存记录列表
export function listStock(query) {
  return request({
    url: '/order/stock/list',
    method: 'get',
    params: query
  })
}

// 查询库存记录列表
export function listStockByLibrary(query) {
  return request({
    url: '/order/stock/listByLibrary',
    method: 'get',
    params: query
  })
}

// 查询库存记录详细
export function getStock(id) {
  return request({
    url: '/order/stock/' + id,
    method: 'get'
  })
}

// 新增库存记录
export function addStock(data) {
  return request({
    url: '/order/stock',
    method: 'post',
    data: data
  })
}

// 修改库存记录
export function updateStock(data) {
  return request({
    url: '/order/stock',
    method: 'put',
    data: data
  })
}

// 修改库存记录
export function updateStockUploadImg(data) {
  return request({
    url: '/order/stock/updateStockUploadImg',
    method: 'post',
    data: data
  })
}

// 删除库存记录
export function delStock(id) {
  return request({
    url: '/order/stock/' + id,
    method: 'delete'
  })
}

export function exportStockRk(query) {
  return request({
    url: '/order/stock/exportRk',
    method: 'get',
    params: query
  })
}

export function exportStockCk(query) {
  return request({
    url: '/order/stock/exportCk',
    method: 'get',
    params: query
  })
}

// 出库
export function shipmentStock(data) {
  return request({
    url: '/order/stock/shipment',
    method: 'put',
    data: data
  })
}

// 出库审核
export function refundAuditStock(data) {
  return request({
    url: '/order/stock/refundAudit',
    method: 'put',
    data: data
  })
}

// 撤销子项目订单
export function revokeStock(data) {
  return request({
    url: '/order/stock/revoke',
    method: 'post',
    data: data
  })
}

//终止发货申请
export function stopStockInfo(data) {
  return request({
    url: '/order/stock/stopStock',
    method: 'put',
    data: data
  })
}


export function allCkGoodsList(query) {
  return request({
    url: '/order/stock/allCkGoodsList',
    method: 'get',
    params: query
  })
}

export function allStock(query) {
  return request({
    url: '/order/stock/all',
    method: 'get',
    params: query
  })
}

export function allocationFreight(data) {
  return request({
    url: '/order/stock/allocation',
    method: 'put',
    data: data
  })
}

export function listRemindStock(query) {
  return request({
    url: '/order/stock/remindStockList',
    method: 'get',
    params: query
  })
}

export function allUnDzStock(query) {
  return request({
    url: '/order/stock/allUnDz',
    method: 'get',
    params: query
  })
}
