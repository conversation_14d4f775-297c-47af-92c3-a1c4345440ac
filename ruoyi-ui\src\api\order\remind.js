import request from '@/utils/request'

// 查询订单提醒列表
export function listRemind(query) {
  return request({
    url: '/order/remind/list',
    method: 'get',
    params: query
  })
}

// 查询订单提醒详细
export function getRemind(id) {
  return request({
    url: '/order/remind/' + id,
    method: 'get'
  })
}

export function getRemindInfo(query) {
  return request({
    url: '/order/remind/info',
    method: 'get',
    params: query
  })
}

// 新增订单提醒
export function addRemind(data) {
  return request({
    url: '/order/remind',
    method: 'post',
    data: data
  })
}

// 修改订单提醒
export function updateRemind(data) {
  return request({
    url: '/order/remind',
    method: 'put',
    data: data
  })
}

// 删除订单提醒
export function delRemind(id) {
  return request({
    url: '/order/remind/' + id,
    method: 'delete'
  })
}

// 导出订单提醒
export function exportRemind(query) {
  return request({
    url: '/order/remind/export',
    method: 'get',
    params: query
  })
}
