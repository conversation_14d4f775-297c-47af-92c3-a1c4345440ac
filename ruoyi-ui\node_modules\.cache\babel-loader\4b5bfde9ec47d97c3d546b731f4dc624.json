{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\userTabs.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\userTabs.vue", "mtime": 1753954679645}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\babel.config.js", "mtime": 1743382537964}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_userTable", "_interopRequireDefault", "require", "_baseTable", "name", "components", "DayHoursBaseTable", "DayHoursUserTable", "props", "sailings", "type", "String", "required", "dayUserList", "Array", "weightMinutesList", "otherMinutesList", "manageMinutesList", "qcMinutesList", "attendanceLogList", "mesHoursList", "dayHours", "Object", "userList", "restList", "data", "currentTab", "tabOptions", "label", "value", "created", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "stop", "methods", "sailingsChange", "userCode", "$emit", "computeItemData", "getSumHours", "userType", "userHours", "laborHours", "outerHours"], "sources": ["src/views/production/dayHours/userTabs.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-tabs v-model=\"currentTab\" >\r\n      <el-tab-pane v-for=\"tab in tabOptions\" :key=\"tab.value\" :name=\"tab.value\" :label=\"tab.label\" >\r\n\r\n        <DayHoursUserTable\r\n          :day-hours=\"dayHours\"\r\n          :mes-hours-list=\"mesHoursList\"\r\n          :user-array=\"dayUserList.filter(i=>i.userType === tab.value)\"\r\n          :sum-hours=\"getSumHours(tab.value)\"\r\n          @sailingsChange=\"sailingsChange\"\r\n          @computeItemData=\"computeItemData\"\r\n        />\r\n\r\n        <template v-if=\"tab.value === 'user'\" >\r\n          <DayHoursBaseTable\r\n            title=\"称量\"\r\n            :attendance-log-list=\"attendanceLogList\"\r\n            :rest-list=\"restList\"\r\n            :user-list=\"userList\"\r\n            :user-array=\"weightMinutesList\"\r\n            :day-hours=\"dayHours\"\r\n            :sailings=\"sailings\"\r\n            @computeItemData=\"computeItemData('weight')\"\r\n          />\r\n\r\n          <DayHoursBaseTable\r\n            title=\"间接\"\r\n            :attendance-log-list=\"attendanceLogList\"\r\n            :rest-list=\"restList\"\r\n            :user-list=\"userList\"\r\n            :user-array=\"otherMinutesList\"\r\n            :day-hours=\"dayHours\"\r\n            :sailings=\"sailings\"\r\n            @computeItemData=\"computeItemData('other')\"\r\n          />\r\n\r\n          <DayHoursBaseTable\r\n            title=\"管理\"\r\n            :attendance-log-list=\"attendanceLogList\"\r\n            :rest-list=\"restList\"\r\n            :user-list=\"userList\"\r\n            :user-array=\"manageMinutesList\"\r\n            :day-hours=\"dayHours\"\r\n            :sailings=\"sailings\"\r\n            @computeItemData=\"computeItemData('manage')\"\r\n          />\r\n\r\n          <DayHoursBaseTable\r\n            title=\"质检\"\r\n            :attendance-log-list=\"attendanceLogList\"\r\n            :rest-list=\"restList\"\r\n            :user-list=\"userList\"\r\n            :user-array=\"qcMinutesList\"\r\n            :day-hours=\"dayHours\"\r\n            :sailings=\"sailings\"\r\n            @computeItemData=\"computeItemData('qc')\"\r\n          />\r\n\r\n        </template>\r\n\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n  </div>\r\n</template>\r\n<script >\r\nimport DayHoursUserTable from \"@/views/production/dayHours/userTable.vue\";\r\nimport DayHoursBaseTable from \"@/views/production/dayHours/baseTable.vue\";\r\n\r\nexport default {\r\n  name: 'dayHoursUserTabs',\r\n  components: {DayHoursBaseTable, DayHoursUserTable},\r\n  props: {\r\n    sailings: {\r\n      type: String,\r\n      required: true,\r\n    },\r\n    dayUserList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    weightMinutesList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    otherMinutesList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    manageMinutesList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    qcMinutesList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    attendanceLogList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    mesHoursList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    dayHours: {\r\n      type: Object,\r\n      required: true,\r\n    },\r\n    userList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    restList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      currentTab: 'user',\r\n      tabOptions: [\r\n        {label: '正式工',value: 'user'},\r\n        {label: '劳务工',value: 'labor'},\r\n        {label: '包干工',value: 'outer'},\r\n      ],\r\n    }\r\n  },\r\n  async created() {\r\n  },\r\n  methods: {\r\n    sailingsChange(userCode) {\r\n      this.$emit('sailingsChange',userCode)\r\n    },\r\n    computeItemData(type) {\r\n      if(type) {\r\n        this.$emit('computeItemData',type)\r\n      } else {\r\n        this.$emit('computeItemData')\r\n      }\r\n    },\r\n    getSumHours(userType) {\r\n      if(userType === 'user') {\r\n        return this.dayHours.userHours\r\n      } else if(userType === 'labor') {\r\n        return this.dayHours.laborHours\r\n      } else if(userType === 'outer') {\r\n        return this.dayHours.outerHours\r\n      }\r\n    }\r\n  },\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n::v-deep .el-divider {\r\n  .el-divider__text {\r\n    font-size: 18px;\r\n    font-weight: 650;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;AAkEA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAF,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,UAAA;IAAAC,iBAAA,EAAAA,kBAAA;IAAAC,iBAAA,EAAAA;EAAA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,WAAA;MACAH,IAAA,EAAAI,KAAA;MACAF,QAAA;IACA;IACAG,iBAAA;MACAL,IAAA,EAAAI,KAAA;MACAF,QAAA;IACA;IACAI,gBAAA;MACAN,IAAA,EAAAI,KAAA;MACAF,QAAA;IACA;IACAK,iBAAA;MACAP,IAAA,EAAAI,KAAA;MACAF,QAAA;IACA;IACAM,aAAA;MACAR,IAAA,EAAAI,KAAA;MACAF,QAAA;IACA;IACAO,iBAAA;MACAT,IAAA,EAAAI,KAAA;MACAF,QAAA;IACA;IACAQ,YAAA;MACAV,IAAA,EAAAI,KAAA;MACAF,QAAA;IACA;IACAS,QAAA;MACAX,IAAA,EAAAY,MAAA;MACAV,QAAA;IACA;IACAW,QAAA;MACAb,IAAA,EAAAI,KAAA;MACAF,QAAA;IACA;IACAY,QAAA;MACAd,IAAA,EAAAI,KAAA;MACAF,QAAA;IACA;EACA;EACAa,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,UAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;MAAA,WAAAF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;UAAA;YAAA,OAAAF,QAAA,CAAAG,IAAA;QAAA;MAAA,GAAAN,OAAA;IAAA;EACA;EACAO,OAAA;IACAC,cAAA,WAAAA,eAAAC,QAAA;MACA,KAAAC,KAAA,mBAAAD,QAAA;IACA;IACAE,eAAA,WAAAA,gBAAApC,IAAA;MACA,IAAAA,IAAA;QACA,KAAAmC,KAAA,oBAAAnC,IAAA;MACA;QACA,KAAAmC,KAAA;MACA;IACA;IACAE,WAAA,WAAAA,YAAAC,QAAA;MACA,IAAAA,QAAA;QACA,YAAA3B,QAAA,CAAA4B,SAAA;MACA,WAAAD,QAAA;QACA,YAAA3B,QAAA,CAAA6B,UAAA;MACA,WAAAF,QAAA;QACA,YAAA3B,QAAA,CAAA8B,UAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}