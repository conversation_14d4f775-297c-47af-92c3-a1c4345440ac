import request from '@/utils/request'

// 查询原料列表
export function listMaterial(query) {
  return request({
    url: '/rd/material/list',
    method: 'get',
    params: query
  })
}

// 查询原料详细
export function getMaterial(id) {
  return request({
    url: '/rd/material/' + id,
    method: 'get'
  })
}

// 新增原料
export function addMaterial(data) {
  return request({
    url: '/rd/material',
    method: 'post',
    data: data
  })
}

// 修改原料
export function updateMaterial(data) {
  return request({
    url: '/rd/material',
    method: 'put',
    data: data
  })
}

// 删除原料
export function delMaterial(id) {
  return request({
    url: '/rd/material/' + id,
    method: 'delete'
  })
}

// 导出原料
export function exportMaterial(query) {
  return request({
    url: '/rd/material/export',
    method: 'get',
    params: query
  })
}

export function allMaterial(query) {
  return request({
    url: '/rd/material/all',
    method: 'get',
    params: query
  })
}
