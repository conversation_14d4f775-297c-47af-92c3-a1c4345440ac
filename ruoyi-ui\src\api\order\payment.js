import request from '@/utils/request'

// 查询订单商品预收款列表
export function listPayment(query) {
  return request({
    url: '/order/payment/list',
    method: 'get',
    params: query
  })
}

// 查询订单商品预收款详细
export function getPayment(id) {
  return request({
    url: '/order/payment/' + id,
    method: 'get'
  })
}

// 新增订单商品预收款
export function addPayment(data) {
  return request({
    url: '/order/payment',
    method: 'post',
    data: data
  })
}

// 修改订单商品预收款
export function updatePayment(data) {
  return request({
    url: '/order/payment',
    method: 'put',
    data: data
  })
}

// 删除订单商品预收款
export function delPayment(id) {
  return request({
    url: '/order/payment/' + id,
    method: 'delete'
  })
}

// 导出订单商品预收款
export function exportPayment(query) {
  return request({
    url: '/order/payment/export',
    method: 'get',
    params: query
  })
}

export function exportBalance(query) {
  return request({
    url: '/order/payment/exportBalance',
    method: 'get',
    params: query
  })
}

export function allGoingPaymentOrder(query) {
  return request({
    url: '/order/all',
    method: 'get',
    params: query
  })
}

export function relationAll(query) {
  return request({
    url: '/order/relationAll',
    method: 'get',
    params: query
  })
}

export function allGoingAllPaymentOrder(query) {
  return request({
    url: '/order/allPaymentOrder',
    method: 'get',
    params: query
  })
}

export function allGoingPaymentAllOrder(query) {
  return request({
    url: '/order/allStatus',
    method: 'get',
    params: query
  })
}

export function allOrderGoodsPaymentDataLogList(query) {
  return request({
    url: '/order/payment/allOrderGoodsPaymentDataLogList',
    method: 'get',
    params: query
  })
}

export function allOrderGoodsBalancesPaymentDataLogList(query) {
  return request({
    url: '/order/payment/allOrderGoodsBalancesPaymentDataLogList',
    method: 'get',
    params: query
  })
}

//获取回款-发票历史记录数据
export function allOrderGoodsBalancesBillingPaymentDataLogList(query) {
  return request({
    url: '/order/payment/allOrderGoodsBalancesBillingPaymentDataLogList',
    method: 'get',
    params: query
  })
}

export function allPaymentOrderGoods(query) {
  return request({
    url: '/order/payment/allPaymentOrderGoods',
    method: 'get',
    params: query
  })
}

// 撤销子项目订单
export function revokeOrder(data) {
  return request({
    url: '/order/payment/revoke',
    method: 'post',
    data: data
  })
}
// 撤销子项目订单
export function balanceRevoke(data) {
  return request({
    url: '/order/payment/balanceRevoke',
    method: 'post',
    data: data
  })
}

// 查询对账清单列表
export function allOrderGoodsPaymentLogItem(query) {
  return request({
    url: '/order/payment/allOrdeGoodsPaymentLogItem',
    method: 'get',
    params: query
  })
}

// 查询对账清单列表-回款
export function allOrderGoodsPaymentBalanceLogItem(query) {
  return request({
    url: '/order/payment/allOrderGoodsPaymentBalanceLogItem',
    method: 'get',
    params: query
  })
}

// 查询开票记录
export function allBillingOrderGoodsList(query) {
  return request({
    url: '/order/billingApplyLog/all',
    method: 'get',
    params: query
  })
}

// 查询开票详情记录记录
export function allBillingApplyItemLog(query) {
  return request({
    url: '/order/billingApplyItemLog/allBillingApplyItemLog',
    method: 'get',
    params: query
  })
}

export function listOrderGoodsUnPayment(query) {
  return request({
    url: '/order/payment/orderGoodsUnPaymentList',
    method: 'get',
    params: query
  })
}
