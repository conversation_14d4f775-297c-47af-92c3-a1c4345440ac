import request from '@/utils/request'

export function dayListOuterUser(query) {
  return request({
    url: '/production/outerUser/outerUserDayList',
    method: 'get',
    params: query
  })
}

// 查询包干工每日名单列表
export function listOuterUser(query) {
  return request({
    url: '/production/outerUser/list',
    method: 'get',
    params: query
  })
}

// 查询包干工每日名单详细
export function getOuterUser(id) {
  return request({
    url: '/production/outerUser/' + id,
    method: 'get'
  })
}

// 新增包干工每日名单
export function addOuterUser(data) {
  return request({
    url: '/production/outerUser',
    method: 'post',
    data: data
  })
}

// 修改包干工每日名单
export function updateOuterUser(data) {
  return request({
    url: '/production/outerUser',
    method: 'put',
    data: data
  })
}

// 删除包干工每日名单
export function delOuterUser(id) {
  return request({
    url: '/production/outerUser/' + id,
    method: 'delete'
  })
}

// 导出包干工每日名单
export function exportOuterUser(query) {
  return request({
    url: '/production/outerUser/export',
    method: 'get',
    params: query
  })
}

export function allOuterUser(query) {
  return request({
    url: '/production/outerUser/all',
    method: 'get',
    params: query
  })
}

export function importTemplateOuterUser() {
  return request({
    url: '/production/outerUser/importTemplate',
    method: 'get'
  })
}


export function printLable(data) {
  return request({
    url: '/production/outerUser/printLable',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
