import request from '@/utils/request'

// 查询erp库存交易数据列表
export function listTransaction(query) {
  return request({
    url: '/order/transaction/list',
    method: 'get',
    params: query
  })
}

// 查询erp库存交易数据列表
export function listHrTransaction(query) {
  return request({
    url: '/order/transaction/hrList',
    method: 'get',
    params: query
  })
}

// 查询erp库存交易数据详细
export function getTransaction(id) {
  return request({
    url: '/order/transaction/' + id,
    method: 'get'
  })
}

// 新增erp库存交易数据
export function addTransaction(data) {
  return request({
    url: '/order/transaction',
    method: 'post',
    data: data
  })
}

// 修改erp库存交易数据
export function updateTransaction(data) {
  return request({
    url: '/order/transaction',
    method: 'put',
    data: data
  })
}

// 删除erp库存交易数据
export function delTransaction(id) {
  return request({
    url: '/order/transaction/' + id,
    method: 'delete'
  })
}

// 导出erp库存交易数据
export function exportTransaction(query) {
  return request({
    url: '/order/transaction/export',
    method: 'get',
    params: query
  })
}
// 导出erp库存交易数据
export function exportHrTransaction(query) {
  return request({
    url: '/order/transaction/hrExport',
    method: 'get',
    params: query
  })
}
