import request from '@/utils/request'

// 查询订单价格记录列表
export function listProjectPriceLog(query) {
  return request({
    url: '/project/projectPriceLog/list',
    method: 'get',
    params: query
  })
}

// 查询订单价格记录详细
export function getProjectPriceLog(id) {
  return request({
    url: '/project/projectPriceLog/' + id,
    method: 'get'
  })
}

// 新增订单价格记录
export function addProjectPriceLog(data) {
  return request({
    url: '/project/projectPriceLog',
    method: 'post',
    data: data
  })
}

// 修改订单价格记录
export function updateProjectPriceLog(data) {
  return request({
    url: '/project/projectPriceLog',
    method: 'put',
    data: data
  })
}

// 删除订单价格记录
export function delProjectPriceLog(id) {
  return request({
    url: '/project/projectPriceLog/' + id,
    method: 'delete'
  })
}

// 导出订单价格记录
export function exportProjectPriceLog(query) {
  return request({
    url: '/project/projectPriceLog/export',
    method: 'get',
    params: query
  })
}

export function allProjectPriceLog(query) {
  return request({
    url: '/project/projectPriceLog/all',
    method: 'get',
    params: query
  })
}

export function pgAllProjectPriceLog(query) {
  return request({
    url: '/project/projectPriceLog/pgAll',
    method: 'get',
    params: query
  })
}
