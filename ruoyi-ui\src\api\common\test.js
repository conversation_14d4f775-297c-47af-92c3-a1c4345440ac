import request from '@/utils/request'

export function getTestInfo(data) {
  return request({
    url: '/test',
    method: 'post',
    data,
  })
}

export function orderAuditPss(data) {
  return request({
    url: '/test/saveOrder',
    method: 'post',
    data,
  })
}

export function delDisuseInstance() {
  return request({
    url: '/test/delDisuseInstance',
    method: 'post',
  })
}

export function asyncMesUser() {
  return request({
    url: '/test/asyncMesUser',
    method: 'post',
  })
}

export function asyncMesDept() {
  return request({
    url: '/test/asyncMesDept',
    method: 'post',
  })
}
