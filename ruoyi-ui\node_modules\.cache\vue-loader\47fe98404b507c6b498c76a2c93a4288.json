{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\qc\\index.vue?vue&type=style&index=0&id=6546dba2&lang=scss&scoped=true", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\qc\\index.vue", "mtime": 1753956861403}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1744596528942}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1744596530059}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1744596529996}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1744596552583}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyVA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/qc", "sourcesContent": ["<template>\r\n  <div class=\"page-wrapper\">\r\n\r\n    <div class=\"second-wrapper\" >\r\n      <div class=\"cell\">\r\n        <div class=\"press\">审核</div>\r\n        <div class=\"row\" v-has-permi=\"['qc:materia:materialStandardAudit']\" >\r\n          <el-badge :value=\"badge.materialStandardAuditCount\" :max=\"99\" :hidden=\"badge.materialStandardAuditCount?false:true\">\r\n            <router-link to='/qc/materialStandardAudit'>\r\n              <span class=\"link\">原料标准审核</span>\r\n            </router-link>\r\n          </el-badge>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:process:audit:microbe']\" >\r\n          <router-link to='/qc/process/audit/microbe'>\r\n            <span class=\"link\">微生物日记录审核</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:process:audit:materialTest']\" >\r\n          <router-link to='/qc/process/audit/materialTest'>\r\n            <span class=\"link\">物料测试审核</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:process:audit:cpfx']\" >\r\n          <router-link to='/qc/process/audit/cpfx'>\r\n            <span class=\"link\">成品放行审核</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\">\r\n          <router-link to='/process/instance?type=2'>\r\n            <span class=\"link\">流程列表</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['process:errorReportSystem:list']\">\r\n          <router-link to='/process/errorReportSystem'>\r\n            <span class=\"link\">异常报告（体系）申请</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['process:errorReportSystem:log']\">\r\n          <router-link to='/process/errorReportSystemLog'>\r\n            <span class=\"link\">异常报告（体系）记录</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['filemanage:filemanage:list']\" >\r\n          <router-link to='/filemanage/filemanage'>\r\n            <span class=\"link\" >文件申请</span>\r\n          </router-link>\r\n        </div>\r\n      </div>\r\n      <div class=\"cell\">\r\n        <div class=\"press\">成品</div>\r\n        <div class=\"row\" v-has-permi=\"['qc:finishedCategory:list']\">\r\n          <router-link to='/qc/finishedCategory'>\r\n            <span class=\"link\">成品物料类型</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:finishedType:list']\">\r\n          <router-link to='/qc/finishedType'>\r\n            <span class=\"link\" >成品检验类型</span>\r\n          </router-link>\r\n        </div>\r\n<!--        <div class=\"row\" v-has-permi=\"['qc:finishedProject:list']\">-->\r\n<!--          <router-link to='/qc/finishedProject'>-->\r\n<!--            <span class=\"link\" >成品检验项目</span>-->\r\n<!--          </router-link>-->\r\n<!--        </div>-->\r\n        <div class=\"row\" v-has-permi=\"['qc:finishedProjectModel:list']\">\r\n          <router-link to='/qc/finishedProjectModel'>\r\n            <span class=\"link\" >成品检验模板</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:finishedStandard:list']\">\r\n          <el-badge :value=\"badge.finishedStandardCount\" :max=\"99\" :hidden=\"badge.finishedStandardCount?false:true\">\r\n            <router-link to='/qc/finishedStandard'>\r\n              <span class=\"link\" >成品检验标准</span>\r\n            </router-link>\r\n          </el-badge>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:finishedInspection:list']\">\r\n          <el-badge :value=\"badge.finishedStandardInspectionCount\" :max=\"99\" :hidden=\"badge.finishedStandardInspectionCount?false:true\">\r\n            <router-link to='/qc/finishedInspection'>\r\n              <span class=\"link\" >成品检验记录</span>\r\n            </router-link>\r\n          </el-badge>\r\n        </div>\r\n      </div>\r\n      <div class=\"cell\">\r\n        <div class=\"press\">包材</div>\r\n        <div class=\"row\" v-has-permi=\"['qc:materialType:list']\">\r\n          <router-link to='/qc/materialType'>\r\n            <span class=\"link\" >包材物料类型</span>\r\n          </router-link>\r\n        </div>\r\n<!--        <div class=\"row\" v-has-permi=\"['qc:materialProject:list']\">-->\r\n<!--          <router-link to='/qc/materialProject'>-->\r\n<!--            <span class=\"link\" >包材检测项目</span>-->\r\n<!--          </router-link>-->\r\n<!--        </div>-->\r\n        <div class=\"row\" v-has-permi=\"['qc:materialProjectModel:list']\">\r\n          <router-link to='/qc/materialProjectModel'>\r\n            <span class=\"link\" >包材检验模板</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:packaging:inspection:standard:list']\">\r\n          <el-badge :value=\"badge.packageStandardCount\" :max=\"99\" :hidden=\"badge.packageStandardCount?false:true\">\r\n            <router-link to='/qc/packagingInspectionStandard'>\r\n              <span class=\"link\" >包材检验标准</span>\r\n            </router-link>\r\n          </el-badge>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:packagingInspectionRecord:list']\">\r\n          <el-badge :value=\"badge.packageStandardInspectionCount\" :max=\"99\" :hidden=\"badge.packageStandardInspectionCount?false:true\">\r\n            <router-link to='/qc/packagingInspectionRecord'>\r\n              <span class=\"link\" >包材检验记录</span>\r\n            </router-link>\r\n          </el-badge>\r\n        </div>\r\n      </div>\r\n      <div class=\"cell\">\r\n        <div class=\"press\">半成品</div>\r\n        <div class=\"row\" v-has-permi=\"['qc:bcpTemplate:list']\">\r\n            <router-link to='/qc/bcpTemplate'>\r\n              <span class=\"link\" >半成品检验模板</span>\r\n            </router-link>\r\n         </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:bcpStandard:list']\">\r\n          <el-badge :value=\"badge.semimanufacturesStandardCount\" :max=\"99\" :hidden=\"badge.semimanufacturesStandardCount?false:true\">\r\n            <router-link to='/qc/bcpStandard'>\r\n              <span class=\"link\" >半成品检验标准</span>\r\n            </router-link>\r\n          </el-badge>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:bcpInspection:list']\">\r\n          <el-badge :value=\"badge.semimanufacturesStandardInspectionCount\" :max=\"99\" :hidden=\"badge.semimanufacturesStandardInspectionCount?false:true\">\r\n            <router-link to='/qc/bcpInspection'>\r\n              <span class=\"link\" >半成品检验记录</span>\r\n            </router-link>\r\n          </el-badge>\r\n        </div>\r\n      </div>\r\n      <div class=\"cell\">\r\n        <div class=\"press\">原料</div>\r\n        <div class=\"row\" v-has-permi=\"['qc:qcMaterialTest:list']\">\r\n          <el-badge :value=\"badge.materialStandardCount\" :max=\"99\" :hidden=\"badge.materialStandardCount?false:true\">\r\n            <router-link to='/qc/qcMaterialTest'>\r\n              <span class=\"link\" >原料检测标准</span>\r\n            </router-link>\r\n          </el-badge>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:materialRecord:list']\">\r\n          <el-badge :value=\"badge.materialStandardInspectionCount\" :max=\"99\" :hidden=\"badge.materialStandardInspectionCount?false:true\">\r\n            <router-link to='/qc/materialRecordAll'>\r\n              <span class=\"link\" >原料检验记录</span>\r\n            </router-link>\r\n          </el-badge>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:materialRecord:blist']\">\r\n          <router-link to='/qc/materialRecordBAll'>\r\n            <span class=\"link\" >B代码检验记录</span>\r\n          </router-link>\r\n        </div>\r\n      </div>\r\n      <div class=\"cell\">\r\n        <div class=\"press\">微生物</div>\r\n        <div class=\"row\" v-has-permi=\"['qc:microbeStandard:list']\">\r\n          <i class=\"ali-icon ali-manufacturing\"></i>\r\n          <router-link to='/qc/microbeStandard'>\r\n            <span class=\"link\" >微生物检验标准</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:microbeInspection:tabs']\">\r\n          <i class=\"ali-icon ali-manufacturing\"></i>\r\n          <router-link to='/qc/microbeInspection/tabs'>\r\n            <span class=\"link\" >微生物检验台账</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:microbeDay:list']\">\r\n          <i class=\"ali-icon ali-manufacturing\"></i>\r\n          <router-link to='/qc/microbeDay'>\r\n            <span class=\"link\" >微生物检验日记录</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:microbeInspection:list']\">\r\n          <i class=\"ali-icon ali-manufacturing\"></i>\r\n          <router-link to='/qc/microbeInspection'>\r\n            <span class=\"link\" >微生物检验记录</span>\r\n          </router-link>\r\n        </div>\r\n      </div>\r\n      <div class=\"cell\">\r\n        <div class=\"press\">其它</div>\r\n        <div class=\"row\" v-has-permi=\"['qc:audit:list']\">\r\n          <router-link to='/qc/audit'>\r\n            <span class=\"link\" >准入检查</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:jcxm:list']\">\r\n          <router-link to='/qc/jcxm'>\r\n            <span class=\"link\" >检测项目(原料/半成品/包材/成品)</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:jcyq:list']\">\r\n          <router-link to='/qc/jcyq'>\r\n            <span class=\"link\" >检测仪器</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:zxbz:list']\">\r\n          <router-link to='/qc/zxbz'>\r\n            <span class=\"link\" >QC执行标准</span>\r\n          </router-link>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:tz:list']\">\r\n          <el-badge :value=\"badge.tzCount\" :max=\"99\" :hidden=\"badge.tzCount?false:true\">\r\n            <router-link to='/qc/tz'>\r\n              <span class=\"link\" >标准品台账</span>\r\n            </router-link>\r\n          </el-badge>\r\n        </div>\r\n        <div v-has-permi=\"['sop:bomChange:all']\" class=\"row\" >\r\n          <router-link to='/sop/bomChange/all'>\r\n            <span class=\"link\" >bom变更全部</span>\r\n          </router-link>\r\n        </div>\r\n        <div v-has-permi=\"['sop:bomChange:all']\" class=\"row\" >\r\n          <router-link to='/sop/bomChange/all'>\r\n            <span class=\"link\" >bom变更全部</span>\r\n          </router-link>\r\n        </div>\r\n        <div v-has-permi=\"['quantity:qc:order']\" class=\"row\">\r\n          <router-link to='/qc/quantityOrderData'>\r\n            <span class=\"link\">订单列表</span>\r\n          </router-link>\r\n        </div>\r\n<!--        <div class=\"row\" v-has-permi=\"['qc:detectCode:list']\">-->\r\n<!--          <router-link to='/qc/detectCode'>-->\r\n<!--            <span class=\"link\" >检测编码</span>-->\r\n<!--          </router-link>-->\r\n<!--        </div>-->\r\n<!--        <div class=\"row\" v-has-permi=\"['qc:jcff:list']\">-->\r\n<!--          <router-link to='/qc/jcff'>-->\r\n<!--            <span class=\"link\" >检测方法</span>-->\r\n<!--          </router-link>-->\r\n<!--        </div>-->\r\n<!--        <div class=\"row\" v-has-permi=\"['qc:defectiveItems:list']\">-->\r\n<!--          <router-link to='/qc/defectiveItems'>-->\r\n<!--            <span class=\"link\" >缺陷项目</span>-->\r\n<!--          </router-link>-->\r\n<!--        </div>-->\r\n      </div>\r\n<!--      <div class=\"cell\">-->\r\n<!--        <div class=\"press\">内容物</div>-->\r\n<!--        <div class=\"row\" v-has-permi=\"['qc:semimanufactures:list']\">-->\r\n<!--             <router-link to='/qc/semimanufactures'>-->\r\n<!--              <span class=\"link\" >内容物检验标准</span>-->\r\n<!--            </router-link>-->\r\n<!--         </div>-->\r\n<!--        <div class=\"row\" v-has-permi=\"['qc:bcpRecord:list']\">-->\r\n<!--            <router-link to='/qc/bcpRecord'>-->\r\n<!--              <span class=\"link\" >内容物检验记录</span>-->\r\n<!--            </router-link>-->\r\n<!--         </div>-->\r\n<!--      </div>-->\r\n      <div class=\"cell\">\r\n        <div class=\"press\">文件管理</div>\r\n        <div class=\"row\" v-has-permi=\"['filemanage:template:list']\" >\r\n          <router-link to='/filemanage/template'>\r\n            <span class=\"link\" >文件模板</span>\r\n          </router-link>\r\n        </div>\r\n        <!-- <div class=\"row\" v-has-permi=\"['filemanage:filemanage:list']\" >\r\n          <router-link to='/filemanage/filemanage'>\r\n            <span class=\"link\" >文件申请</span>\r\n          </router-link>\r\n        </div> -->\r\n        <div class=\"row\" v-has-permi=\"['filemanage:archive:list']\" >\r\n          <router-link to='/filemanage/archive'>\r\n            <span class=\"link\" >档案库</span>\r\n          </router-link>\r\n        </div>\r\n        <!-- <div class=\"row\" v-has-permi=\"['filemanage:archive:list']\" >\r\n          <router-link to='/filemanage/archive/indexOld'>\r\n            <span class=\"link\" >旧档案库</span>\r\n          </router-link>\r\n        </div> -->\r\n        <div class=\"row\" v-has-permi=\"['filemanage:lawregulation:list']\" >\r\n          <router-link to='/filemanage/lawregulation'>\r\n            <span class=\"link\" >法律法规</span>\r\n          </router-link>\r\n        </div>\r\n      </div>\r\n\r\n\r\n\r\n      <div class=\"cell\">\r\n        <div class=\"press\">物料测试</div>\r\n        <div class=\"row\" v-has-permi=\"['qc:materialTesting:list']\">\r\n          <span class=\"link\" @click=\"openWindow('/qc/layout')\" >项目视图</span>\r\n        </div>\r\n        <div class=\"row\" v-has-permi=\"['qc:materialTesting:list']\" >\r\n          <router-link to='/qc/materialTesting'>\r\n            <span class=\"link\" >物料测试存档</span>\r\n          </router-link>\r\n        </div>\r\n        <div v-has-permi=\"['rd:stability:list']\" class=\"row\" >\r\n          <router-link to='/rd/stability/all'>\r\n            <span class=\"link\" >配方稳定性</span>\r\n          </router-link>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {getQualityStatisticsData} from \"@/api/qc/finishedInspection\";\r\n\r\nexport default {\r\n  name: \"qcIndex\",\r\n  data() {\r\n    return {\r\n      badge:{\r\n\r\n      }\r\n    }\r\n  },\r\n  async created() {\r\n    await this.getStatisticsData();\r\n  },\r\n  methods: {\r\n    async getStatisticsData() {\r\n      let res = await getQualityStatisticsData();\r\n      this.badge = res;\r\n    },\r\n    openWindow(path) {\r\n      let newpage = this.$router.resolve({\r\n        path\r\n      })\r\n      window.open(newpage.href, '_blank');\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-wrapper {\r\n  padding: 2vh 1vw;\r\n  background: #fafbfe;\r\n  overflow: auto;\r\n\r\n  .first-wrapper {\r\n    display: grid;\r\n    grid-template-columns: 1fr 1fr 1fr 1fr;\r\n    grid-gap: 2vh 1vw;\r\n    height: 26vh;\r\n\r\n    .cell {\r\n      background: #fff;\r\n      box-shadow: 0 0 35px 0 rgb(154 161 171 / 15%);\r\n      transition: all 250ms cubic-bezier(.02, .01, .47, 1);\r\n      border-radius: 5px;\r\n      padding: 1vh .5vw;\r\n      height: 24vh;\r\n      overflow: auto;\r\n\r\n      .press {\r\n        margin-bottom: 1vh;\r\n      }\r\n\r\n      .row {\r\n        display: flex;\r\n        align-items: center;\r\n        height: 4vh;\r\n        border-bottom: 1px solid #eee;\r\n        padding: .5vh 1vw;\r\n\r\n        .link {\r\n          margin-left: 10px;\r\n          font-size: 12px;\r\n          cursor:pointer;\r\n          color: #00afff;\r\n          transition: all 250ms cubic-bezier(.02, .01, .47, 1);\r\n        }\r\n\r\n        .link:hover {\r\n          box-shadow: 0px 1rem 2rem 0px rgba(48, 55, 66, 0.15);\r\n          transform: translate(0,-5px);\r\n          transition-delay: 0s !important;\r\n        }\r\n      }\r\n\r\n    }\r\n\r\n    .cell:hover {\r\n      box-shadow: 0px 1rem 2rem 0px rgba(48, 55, 66, 0.15);\r\n      transform: translate(0,-5px);\r\n      transition-delay: 0s !important;\r\n    }\r\n\r\n    .row {\r\n      display: flex;\r\n      align-items: center;\r\n      height: 4vh;\r\n      border-bottom: 1px solid #eee;\r\n      padding: .5vh 1vw;\r\n\r\n      .link {\r\n        margin-left: 10px;\r\n        font-size: 12px;\r\n        cursor:pointer;\r\n        color: #00afff;\r\n        transition: all 250ms cubic-bezier(.02, .01, .47, 1);\r\n      }\r\n\r\n      .link:hover {\r\n        box-shadow: 0px 1rem 2rem 0px rgba(48, 55, 66, 0.15);\r\n        transform: translate(0,-5px);\r\n        transition-delay: 0s !important;\r\n      }\r\n    }\r\n  }\r\n\r\n  .second-wrapper {\r\n    display: grid;\r\n    grid-template-columns: 1fr 1fr 1fr;\r\n    grid-gap: 2vh 1vw;\r\n    height: 112vh;\r\n\r\n    .cell {\r\n      background: #fff;\r\n      box-shadow: 0 0 35px 0 rgb(154 161 171 / 15%);\r\n      transition: all 250ms cubic-bezier(.02, .01, .47, 1);\r\n      border-radius: 5px;\r\n      padding: 1vh .5vw;\r\n      height: 36vh;\r\n      overflow: auto;\r\n\r\n      .press {\r\n        margin-bottom: 1vh;\r\n      }\r\n    }\r\n\r\n    .cell:hover {\r\n      box-shadow: 0px 1rem 2rem 0px rgba(48, 55, 66, 0.15);\r\n      transform: translate(0,-5px);\r\n      transition-delay: 0s !important;\r\n    }\r\n\r\n    .row {\r\n      display: flex;\r\n      align-items: center;\r\n      height: 4vh;\r\n      border-bottom: 1px solid #eee;\r\n      padding: .5vh 1vw;\r\n\r\n      .link {\r\n        margin-left: 10px;\r\n        font-size: 12px;\r\n        cursor:pointer;\r\n        color: #00afff;\r\n        transition: all 250ms cubic-bezier(.02, .01, .47, 1);\r\n      }\r\n\r\n      .link:hover {\r\n        box-shadow: 0px 1rem 2rem 0px rgba(48, 55, 66, 0.15);\r\n        transform: translate(0,-5px);\r\n        transition-delay: 0s !important;\r\n      }\r\n\r\n      .disabled {\r\n        color: #909399;\r\n        font-size: 12px;\r\n      }\r\n    }\r\n  }\r\n\r\n}\r\n\r\n::v-deep .el-pagination {\r\n  width: 100%;\r\n  overflow: hidden;\r\n}\r\n</style>\r\n"]}]}