{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\api\\qc\\audit.js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\api\\qc\\audit.js", "mtime": 1753956861427}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\babel.config.js", "mtime": 1743382537964}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\eslint-loader\\index.js", "mtime": 1744596523454}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9zZWFuL3dvcmtzcGFjZS9lbm93X3Byb2plY3QvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmFkZEF1ZGl0ID0gYWRkQXVkaXQ7CmV4cG9ydHMuZGVsQXVkaXQgPSBkZWxBdWRpdDsKZXhwb3J0cy5leHBvcnRBdWRpdCA9IGV4cG9ydEF1ZGl0OwpleHBvcnRzLmdldEF1ZGl0ID0gZ2V0QXVkaXQ7CmV4cG9ydHMubGlzdEF1ZGl0ID0gbGlzdEF1ZGl0OwpleHBvcnRzLnVwZGF0ZUF1ZGl0ID0gdXBkYXRlQXVkaXQ7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDmn6Xor6Lkuqflk4Hlh4blhaXmo4Dmn6XliJfooagKZnVuY3Rpb24gbGlzdEF1ZGl0KHF1ZXJ5KSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvcWMvYXVkaXQvbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6Lkuqflk4Hlh4blhaXmo4Dmn6Xor6bnu4YKZnVuY3Rpb24gZ2V0QXVkaXQoaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9xYy9hdWRpdC8nICsgaWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOaWsOWinuS6p+WTgeWHhuWFpeajgOafpQpmdW5jdGlvbiBhZGRBdWRpdChkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvcWMvYXVkaXQnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOS/ruaUueS6p+WTgeWHhuWFpeajgOafpQpmdW5jdGlvbiB1cGRhdGVBdWRpdChkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvcWMvYXVkaXQnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5Yig6Zmk5Lqn5ZOB5YeG5YWl5qOA5p+lCmZ1bmN0aW9uIGRlbEF1ZGl0KGlkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvcWMvYXVkaXQvJyArIGlkLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9CgovLyDlr7zlh7rkuqflk4Hlh4blhaXmo4Dmn6UKZnVuY3Rpb24gZXhwb3J0QXVkaXQocXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9xYy9hdWRpdC9leHBvcnQnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listAudit", "query", "request", "url", "method", "params", "get<PERSON><PERSON><PERSON>", "id", "add<PERSON><PERSON><PERSON>", "data", "updateAudit", "<PERSON><PERSON><PERSON><PERSON>", "exportAudit"], "sources": ["C:/sean/workspace/enow_project/ruoyi-ui/src/api/qc/audit.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询产品准入检查列表\r\nexport function listAudit(query) {\r\n  return request({\r\n    url: '/qc/audit/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询产品准入检查详细\r\nexport function getAudit(id) {\r\n  return request({\r\n    url: '/qc/audit/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增产品准入检查\r\nexport function addAudit(data) {\r\n  return request({\r\n    url: '/qc/audit',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改产品准入检查\r\nexport function updateAudit(data) {\r\n  return request({\r\n    url: '/qc/audit',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除产品准入检查\r\nexport function delAudit(id) {\r\n  return request({\r\n    url: '/qc/audit/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 导出产品准入检查\r\nexport function exportAudit(query) {\r\n  return request({\r\n    url: '/qc/audit/export',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC/B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,QAAQA,CAACC,EAAE,EAAE;EAC3B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY,GAAGI,EAAE;IACtBH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,QAAQA,CAACC,IAAI,EAAE;EAC7B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,WAAWA,CAACD,IAAI,EAAE;EAChC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,QAAQA,CAACJ,EAAE,EAAE;EAC3B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY,GAAGI,EAAE;IACtBH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,WAAWA,CAACX,KAAK,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}