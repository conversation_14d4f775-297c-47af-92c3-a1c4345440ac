import request from '@/utils/request'

// 查询成分使用量列表
export function listIngredientsUsage(query) {
  return request({
    url: '/rd/ingredientsUsage/list',
    method: 'get',
    params: query
  })
}

export function allIngredientsUsage(query) {
  return request({
    url: '/rd/ingredientsUsage/all',
    method: 'get',
    params: query
  })
}

// 查询成分使用量详细
export function getIngredientsUsage(id) {
  return request({
    url: '/rd/ingredientsUsage/' + id,
    method: 'get'
  })
}

// 新增成分使用量
export function addIngredientsUsage(data) {
  return request({
    url: '/rd/ingredientsUsage',
    method: 'post',
    data: data
  })
}

// 修改成分使用量
export function updateIngredientsUsage(data) {
  return request({
    url: '/rd/ingredientsUsage',
    method: 'put',
    data: data
  })
}

// 删除成分使用量
export function delIngredientsUsage(id) {
  return request({
    url: '/rd/ingredientsUsage/' + id,
    method: 'delete'
  })
}

// 导出成分使用量
export function exportIngredientsUsage(query) {
  return request({
    url: '/rd/ingredientsUsage/export',
    method: 'get',
    params: query
  })
}

export function importTemplateIngredientsUsage() {
  return request({
    url: '/rd/ingredientsUsage/importTemplate',
    method: 'get'
  })
}
