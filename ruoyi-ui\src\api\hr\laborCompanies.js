import request from '@/utils/request'

// 查询劳务公司列表
export function allLaborCompanies(query) {
  return request({
    url: '/hr/laborCompanies/all',
    method: 'get',
    params: query
  })
}
// 查询劳务公司列表
export function listLaborCompanies(query) {
  return request({
    url: '/hr/laborCompanies/list',
    method: 'get',
    params: query
  })
}

// 查询劳务公司详细
export function getLaborCompanies(id) {
  return request({
    url: '/hr/laborCompanies/' + id,
    method: 'get'
  })
}

// 新增劳务公司
export function addLaborCompanies(data) {
  return request({
    url: '/hr/laborCompanies',
    method: 'post',
    data: data
  })
}

// 修改劳务公司
export function updateLaborCompanies(data) {
  return request({
    url: '/hr/laborCompanies',
    method: 'put',
    data: data
  })
}

// 删除劳务公司
export function delLaborCompanies(id) {
  return request({
    url: '/hr/laborCompanies/' + id,
    method: 'delete'
  })
}

// 导出劳务公司
export function exportLaborCompanies(query) {
  return request({
    url: '/hr/laborCompanies/export',
    method: 'get',
    params: query
  })
}
