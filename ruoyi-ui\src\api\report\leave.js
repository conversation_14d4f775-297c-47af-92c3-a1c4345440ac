import request from '@/utils/request'

export function listReportLeave(query) {
  return request({
    url: '/report/leave/list',
    method: 'get',
    params: query
  })
}
export function listReportLeaveUser(query) {
  return request({
    url: '/report/leave/userList',
    method: 'get',
    params: query
  })
}
export function auditReportLeave(query) {
  return request({
    url: '/report/leave/audit',
    method: 'get',
    params: query
  })
}
export function auditUserReportLeave(query) {
  return request({
    url: '/report/leave/auditUser',
    method: 'get',
    params: query
  })
}
export function leaveUserReportLeave(query) {
  return request({
    url: '/report/leave/leaveUser',
    method: 'get',
    params: query
  })
}
