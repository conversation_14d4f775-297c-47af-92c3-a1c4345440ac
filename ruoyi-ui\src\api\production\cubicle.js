import request from '@/utils/request'

// 查询工位图列表
export function listCubicle(query) {
  return request({
    url: '/production/cubicle/list',
    method: 'get',
    params: query
  })
}

// 查询工位图详细
export function getCubicle(id) {
  return request({
    url: '/production/cubicle/' + id,
    method: 'get'
  })
}

// 新增工位图
export function addCubicle(data) {
  return request({
    url: '/production/cubicle',
    method: 'post',
    data: data
  })
}

// 修改工位图
export function updateCubicle(data) {
  return request({
    url: '/production/cubicle',
    method: 'put',
    data: data
  })
}

// 删除工位图
export function delCubicle(id) {
  return request({
    url: '/production/cubicle/' + id,
    method: 'delete'
  })
}

// 导出工位图
export function exportCubicle(query) {
  return request({
    url: '/production/cubicle/export',
    method: 'get',
    params: query
  })
}