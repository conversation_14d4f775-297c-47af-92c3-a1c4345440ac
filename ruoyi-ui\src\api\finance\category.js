import request from '@/utils/request'

// 查询财务科目代码列表
export function listCategory(query) {
  return request({
    url: '/finance/category/list',
    method: 'get',
    params: query
  })
}

// 查询财务科目代码详细
export function getCategory(categoryId) {
  return request({
    url: '/finance/category/' + categoryId,
    method: 'get'
  })
}

// 新增财务科目代码
export function addCategory(data) {
  return request({
    url: '/finance/category',
    method: 'post',
    data: data
  })
}

// 修改财务科目代码
export function updateCategory(data) {
  return request({
    url: '/finance/category',
    method: 'put',
    data: data
  })
}

// 删除财务科目代码
export function delCategory(categoryId) {
  return request({
    url: '/finance/category/' + categoryId,
    method: 'delete'
  })
}

// 导出财务科目代码
export function exportCategory(query) {
  return request({
    url: '/finance/category/export',
    method: 'get',
    params: query
  })
}