import request from '@/utils/request'

// 查询线长管理列表
export function listLineLeader(query) {
  return request({
    url: '/production/lineLeader/list',
    method: 'get',
    params: query
  })
}

// 查询线长管理详细
export function getLineLeader(id) {
  return request({
    url: '/production/lineLeader/' + id,
    method: 'get'
  })
}

// 新增线长管理
export function addLineLeader(data) {
  return request({
    url: '/production/lineLeader',
    method: 'post',
    data: data
  })
}

// 修改线长管理
export function updateLineLeader(data) {
  return request({
    url: '/production/lineLeader',
    method: 'put',
    data: data
  })
}

// 删除线长管理
export function delLineLeader(id) {
  return request({
    url: '/production/lineLeader/' + id,
    method: 'delete'
  })
}

// 导出线长管理
export function exportLineLeader(query) {
  return request({
    url: '/production/lineLeader/export',
    method: 'get',
    params: query
  })
}

export function allLineLeader(query) {
  return request({
    url: '/production/lineLeader/all',
    method: 'get',
    params: query
  })
}
