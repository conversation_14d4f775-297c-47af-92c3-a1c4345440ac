{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\project\\project\\materialTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\project\\project\\materialTable.vue", "mtime": 1753954679647}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQge2dldEJvbUJ5RXJwQ29kZSwgZ2V0UHJvZHVjdEJ5TWQwMDN9IGZyb20gIkAvYXBpL2NvbW1vbi9lcnAiOw0KaW1wb3J0IE1hdGVyaWFsR29vZHNTZWxlY3RUYWJsZSBmcm9tICJAL3ZpZXdzL3Jlc291cmNlL21hdGVyaWFsR29vZHMvc2VsZWN0VGFibGUudnVlIjsNCmltcG9ydCB7c3VwcGxpZXJBbGx9IGZyb20gIkAvYXBpL3N1cHBsaWVyL3N1cHBsaWVyIjsNCmltcG9ydCB7YWxsQmNMb2d9IGZyb20gIkAvYXBpL3Byb2plY3QvYmNMb2ciOw0KaW1wb3J0IEJjUHJpY2VUYWJsZSBmcm9tICJAL3ZpZXdzL3NvcC9iYy9iY1ByaWNlVGFibGUudnVlIjsNCmltcG9ydCBDdXN0b21Db2xzIGZyb20gIkAvY29tcG9uZW50cy9jdXN0b21Db2xzLnZ1ZSI7DQppbXBvcnQge2FsbFRyZWVEYXRhfSBmcm9tICJAL2FwaS9zeXN0ZW0vdHJlZURhdGEiOw0KaW1wb3J0IHtlcnBCb219IGZyb20gIkAvYXBpL3Byb2R1Y3Rpb24vc2NoZWR1bGVQbGFuIjsNCmltcG9ydCB7ZGVsQmMsIHVwZGF0ZUJjRm9yY2V9IGZyb20gIkAvYXBpL3Byb2plY3QvYmMiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdwcm9qZWN0QmNUYWJsZScsDQogIGNvbXBvbmVudHM6IHsNCiAgICBDdXN0b21Db2xzLA0KICAgIEJjUHJpY2VUYWJsZSwNCiAgICBNYXRlcmlhbEdvb2RzU2VsZWN0VGFibGUsDQogIH0sDQogIHByb3BzOiB7DQogICAgZm9ybTogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgfSwNCiAgICBtYXRlcmlhbExpc3Q6IHsNCiAgICAgIHR5cGU6IEFycmF5LA0KICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgfSwNCiAgICBkZXZTdGF0dXM6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICcwJywNCiAgICB9LA0KICAgIHJlYWRvbmx5OiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UsDQogICAgfSwNCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICAnZm9ybS5pZCc6IHsNCiAgICAgIGFzeW5jIGhhbmRsZXIodikgew0KICAgICAgICB0aGlzLnJlc2V0UXVlcnkoKQ0KICAgICAgICBpZih2ICYmIHRoaXMucmVhZG9ubHkpIHsvL+WmguaenOaYr+inhuWbvuaooeW8jw0KICAgICAgICAgIGxldCBiY0xvZ0xpc3QgPSBhd2FpdCBhbGxCY0xvZyh7cHJvamVjdElkOiB2LCBzdGF0dXM6ICczJ30pDQogICAgICAgICAgbGV0IGxvZ0FycmF5ID0gW10NCiAgICAgICAgICBmb3IgKGxldCBpdGVtIG9mIHRoaXMubG9nT3B0aW9ucykgew0KICAgICAgICAgICAgbGV0IGJjTG9nID0gYmNMb2dMaXN0LmZpbHRlcihpID0+IGkucHJvamVjdElkID09PSB2ICYmIGkudHlwZSA9PT0gaXRlbS5kaWN0VmFsdWUpDQogICAgICAgICAgICBpZiAoYmNMb2cgJiYgYmNMb2cubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICBsZXQgYXJyYXkgPSBbXQ0KICAgICAgICAgICAgICBsZXQga2V5cyA9IFtdDQogICAgICAgICAgICAgIGZvciAobGV0IGJjIG9mIGJjTG9nKSB7DQogICAgICAgICAgICAgICAgaWYgKCFrZXlzLmluY2x1ZGVzKGJjLnByb2plY3RJdGVtT3JkZXJDb2RlKSkgew0KICAgICAgICAgICAgICAgICAga2V5cy5wdXNoKGJjLnByb2plY3RJdGVtT3JkZXJDb2RlKQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBmb3IgKGxldCBrIG9mIGtleXMpIHsNCiAgICAgICAgICAgICAgICBsZXQgcHJvamVjdEJjSWRzID0gYmNMb2cuZmlsdGVyKGkgPT4gaS5wcm9qZWN0SXRlbU9yZGVyQ29kZSA9PT0gaykubWFwKGkgPT4gaS5wcm9qZWN0QmNJZCkNCiAgICAgICAgICAgICAgICBhcnJheS5wdXNoKHsNCiAgICAgICAgICAgICAgICAgIHByb2plY3RJdGVtT3JkZXJDb2RlOiBrLA0KICAgICAgICAgICAgICAgICAgcHJvamVjdEJjSWRzLA0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgbG9nQXJyYXkucHVzaCh7DQogICAgICAgICAgICAgICAgbGFiZWw6IGl0ZW0uZGljdExhYmVsLA0KICAgICAgICAgICAgICAgIHZhbHVlOiBpdGVtLmRpY3RWYWx1ZSwNCiAgICAgICAgICAgICAgICBhcnJheSwNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5sb2dBcnJheSA9IGxvZ0FycmF5DQogICAgICAgICAgdGhpcy5iY0xvZ0xpc3QgPSBiY0xvZ0xpc3QNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIGltbWVkaWF0ZTogdHJ1ZSwNCiAgICB9DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIG5hbWU6IG51bGwsDQogICAgICB9LA0KICAgICAgY3VycmVudFBhZ2U6IDEsDQogICAgICBwYWdlU2l6ZTogMTAsDQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIGJ0bkxvYWRpbmc6IGZhbHNlLA0KICAgICAgYm9tT3BlbjogZmFsc2UsDQogICAgICBiY09wZW46IGZhbHNlLA0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICBmdWxsc2NyZWVuRmxhZzogZmFsc2UsDQogICAgICBiY1R5cGVPcHRpb25zOiBbXSwNCiAgICAgIGJvbURhdGE6IFtdLA0KICAgICAgYm9tVHJlZTogW10sDQogICAgICBjdXJyZW50QmNBcnJheTogW10sDQogICAgICB0eXBlT3B0aW9uczogWw0KICAgICAgICB7bGFiZWw6ICfljIXmnZDlvIDlj5EnLHZhbHVlOiAnMCd9LA0KICAgICAgICB7bGFiZWw6ICflrqLmiLflvIDlj5EnLHZhbHVlOiAnMSd9LA0KICAgICAgICB7bGFiZWw6ICfph4fotK3lvIDlj5EnLHZhbHVlOiAnMid9LA0KICAgICAgICB7bGFiZWw6ICfoh6rliLYnLHZhbHVlOiAnMyd9LCAgLy8yMDI0MTEyMueglOWPkeW8gOWPkeaUueS4uuiHquWItg0KICAgICAgXSwNCiAgICAgIHJlc291cmNlVHlwZU9wdGlvbnM6IFsNCiAgICAgICAge2xhYmVsOiAn5pmu6YCa5re75YqgJyx2YWx1ZTogJ2N1c3RvbWVyJ30sDQogICAgICAgIHtsYWJlbDogJ2JvbScsdmFsdWU6ICdib20nfSwNCiAgICAgICAge2xhYmVsOiAn5YyF5p2Q5bqTJyx2YWx1ZTogJ2JjJ30sDQogICAgICAgIHtsYWJlbDogJ+WMheadkOW8gOWPkScsdmFsdWU6ICdkZXYnfSwNCiAgICAgIF0sDQogICAgICBhdHRyT3B0aW9uczogW10sDQogICAgICB6cnFPcHRpb25zOiBbXSwNCiAgICAgIG1hdGVyaWFsVHlwZU9wdGlvbnM6IFtdLA0KICAgICAgc3VwcGxpZXJMaXN0OiBbXSwNCiAgICAgIHRpdGxlOiBudWxsLA0KICAgICAgY29sdW1uczogWw0KICAgICAgICB7bGFiZWw6ICflvIDlj5HnsbvlnosnLHZpc2libGU6IHRydWV9LA0KICAgICAgICB7bGFiZWw6ICfpobnnm67ljIXmnZDnvJbnoIEnLHZpc2libGU6IHRydWV9LA0KICAgICAgICB7bGFiZWw6ICfnsbvliKsnLHZpc2libGU6IHRydWV9LA0KICAgICAgICB7bGFiZWw6ICfljIXoo4XmnZDmlpknLHZpc2libGU6IHRydWV9LA0KICAgICAgICB7bGFiZWw6ICfljIXmnZDlupPnvJbnoIEnLHZpc2libGU6IHRydWV9LA0KICAgICAgICB7bGFiZWw6ICfljIXmnZBFUlDnvJbnoIEnLHZpc2libGU6IHRydWV9LA0KICAgICAgICB7bGFiZWw6ICfnianmlpnlsZ7mgKcnLHZpc2libGU6IHRydWV9LA0KICAgICAgICB7bGFiZWw6ICflkI3np7AnLHZpc2libGU6IHRydWV9LA0KICAgICAgICB7bGFiZWw6ICfop4TmoLwnLHZpc2libGU6IHRydWV9LA0KICAgICAgICB7bGFiZWw6ICflsLrlr7gnLHZpc2libGU6IHRydWV9LA0KICAgICAgICB7bGFiZWw6ICflnovlj7cnLHZpc2libGU6IHRydWV9LA0KICAgICAgICB7bGFiZWw6ICfkvpvlupTllYYnLHZpc2libGU6IHRydWV9LA0KICAgICAgICB7bGFiZWw6ICflm77niYcnLHZpc2libGU6IGZhbHNlfSwNCiAgICAgICAge2xhYmVsOiAnQ09BL1NQRUMnLHZpc2libGU6IGZhbHNlfSwNCiAgICAgICAge2xhYmVsOiAn5aSH5rOoJyx2aXNpYmxlOiB0cnVlfSwNCiAgICAgIF0sDQogICAgICBsb2dBcnJheTogW10sDQogICAgICBiY0xvZ0xpc3Q6IFtdLA0KICAgICAgbG9nT3B0aW9uczogW10sDQogICAgICBwcmljZUFycmF5OiBbXSwNCiAgICAgIGNvbmZpZ0FycmF5OiBbXSwNCiAgICAgIGZpbGVzOiBbXSwNCiAgICAgIGN1cnJlbnRSb3c6IHt9LA0KICAgICAgZ3JhZGVkVHlwZU9wdGlvbnM6IFsNCiAgICAgICAge2xhYmVsOiAn6K6i5Y2V5Lu3Jyx2YWx1ZTogJzAnfSwNCiAgICAgICAge2xhYmVsOiAnTU9R5Lu3Jyx2YWx1ZTogJzEnfSwNCiAgICAgICAge2xhYmVsOiAn5qKv5bqm5Lu3KOS4gOahoyknLHZhbHVlOiAnMid9LA0KICAgICAgICB7bGFiZWw6ICfmoq/luqbku7co5LqM5qGjKScsdmFsdWU6ICczJ30sDQogICAgICAgIHtsYWJlbDogJ+air+W6puS7tyjkuInmoaMpJyx2YWx1ZTogJzQnfSwNCiAgICAgICAge2xhYmVsOiAn5qKv5bqm5Lu3KOWbm+ahoyknLHZhbHVlOiAnNSd9LA0KICAgICAgICB7bGFiZWw6ICfmoq/luqbku7co5LqU5qGjKScsdmFsdWU6ICc2J30sDQogICAgICAgIHtsYWJlbDogJ+air+W6puS7tyjlha3moaMpJyx2YWx1ZTogJzcnfSwNCiAgICAgICAge2xhYmVsOiAn5qKv5bqm5Lu3KOS4g+ahoyknLHZhbHVlOiAnOCd9LA0KICAgICAgICB7bGFiZWw6ICfmoq/luqbku7co5YWr5qGjKScsdmFsdWU6ICc5J30sDQogICAgICAgIHtsYWJlbDogJ+air+W6puS7tyjkuZ3moaMpJyx2YWx1ZTogJzEwJ30sDQogICAgICBdLA0KICAgICAgc3RhZ2VPcHRpb25zOiBbDQogICAgICAgIHtsYWJlbDon6KO45YyF5Lu3Jyx2YWx1ZTogJzAnfSwNCiAgICAgICAge2xhYmVsOiflr7vmoLfpmLbmrrUnLHZhbHVlOiAnMSd9LA0KICAgICAgICB7bGFiZWw6J+aJk+agt+mYtuautScsdmFsdWU6ICcyJ30sDQogICAgICAgIHtsYWJlbDon6K6i5Y2V6Zi25q61Jyx2YWx1ZTogJzMnfSwNCiAgICAgIF0sDQogICAgICBhbGxvY2F0b3JPcHRpb25zOiBbXSwNCiAgICAgIGVycENvZGU6IFtdLA0KICAgICAgdHlwZU9wZW46IGZhbHNlLA0KICAgICAgdHJlZU9wZW46IGZhbHNlLA0KICAgICAgbWIwMDVPcHRpb25zOiBbDQogICAgICAgIHtsYWJlbDogJ+WMheadkCcsdmFsdWU6ICcxMDQnfSwNCiAgICAgICAge2xhYmVsOiAn5Y2K5oiQ5ZOBJyx2YWx1ZTogJzEwMyd9LA0KICAgICAgICB7bGFiZWw6ICfoo7joo4Xlk4EnLHZhbHVlOiAnMTAyJ30sDQogICAgICAgIHtsYWJlbDogJ+aIkOWTgScsdmFsdWU6ICcxMDEnfSwNCiAgICAgIF0sDQogICAgICBydWxlczogew0KICAgICAgICBtYjAwNTogWw0KICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSxtc2c6ICfor7fpgInmi6nnianmlpnnsbvlnosnfQ0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgbWF0ZXJpYWxBcnJheTogW10sDQogICAgfQ0KICB9LA0KICBhc3luYyBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0RGljdHMoIlBST0RVQ1RfUFJPUEVSVElFUyIpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgY29uc3QgYXR0ck9wdGlvbnMgPSByZXNwb25zZS5kYXRhDQogICAgICBhdHRyT3B0aW9ucy5wdXNoKHtkaWN0TGFiZWw6ICfoh6rliLYnLGRpY3RWYWx1ZTogJzAnfSkNCiAgICAgIGF0dHJPcHRpb25zLnB1c2goe2RpY3RMYWJlbDogJ+Wklui0rScsZGljdFZhbHVlOiAnMSd9KQ0KICAgICAgYXR0ck9wdGlvbnMucHVzaCh7ZGljdExhYmVsOiAn5a6i5oyH5Luj6YeHJyxkaWN0VmFsdWU6ICczJ30pDQogICAgICB0aGlzLmF0dHJPcHRpb25zID0gYXR0ck9wdGlvbnMNCiAgICB9KQ0KICAgIGxldCBsb2dSZXMgPSBhd2FpdCB0aGlzLmdldERpY3RzKCJwcm9qZWN0X2JjX2xvZyIpDQogICAgdGhpcy5sb2dPcHRpb25zID0gbG9nUmVzLmRhdGE7DQogICAgbGV0IGJjVHlwZVJlcyA9IGF3YWl0IHRoaXMuZ2V0RGljdHMoInByb2plY3RfYmNfdHlwZSIpDQogICAgdGhpcy5iY1R5cGVPcHRpb25zID0gYmNUeXBlUmVzLmRhdGE7DQogICAgdGhpcy5nZXREaWN0cygiYmMtenJxIikudGhlbihyZXNwb25zZSA9PiB7DQogICAgICB0aGlzLnpycU9wdGlvbnMgPSByZXNwb25zZS5kYXRhOw0KICAgIH0pDQogICAgdGhpcy5nZXREaWN0cygiQlpDTCIpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgdGhpcy5tYXRlcmlhbFR5cGVPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsNCiAgICB9KQ0KICAgIHRoaXMuZ2V0RGljdHMoImJjLWZwcSIpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgdGhpcy5hbGxvY2F0b3JPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsNCiAgICB9KQ0KICAgIGxldCBzdXBwbGllckxpc3QgPSBhd2FpdCBzdXBwbGllckFsbCh7c3VwcGxpZXJUeXBlOiAnMScscmVxVHlwZToxfSk7DQogICAgdGhpcy5zdXBwbGllckxpc3QgPSBzdXBwbGllckxpc3Q7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBnZXRMaXN0KCkgew0KICAgICAgbGV0IG1hdGVyaWFsQXJyYXkgPSB0aGlzLm1hdGVyaWFsTGlzdC5maWx0ZXIoaSA9PiBpLm1iMDA1ICE9PSAnMTAzJykNCiAgICAgIGxldCBwYXJhbXMgPSBPYmplY3QuYXNzaWduKHt9LCB0aGlzLnF1ZXJ5UGFyYW1zKQ0KICAgICAgaWYgKHBhcmFtcy5uYW1lKSB7DQogICAgICAgIG1hdGVyaWFsQXJyYXkgPSBtYXRlcmlhbEFycmF5LmZpbHRlcihpID0+IGkubmFtZSA9PSBwYXJhbXMubmFtZSkNCiAgICAgIH0NCiAgICAgIHRoaXMubWF0ZXJpYWxBcnJheSA9IG1hdGVyaWFsQXJyYXkuc2xpY2UoKHRoaXMuY3VycmVudFBhZ2UtMSkgKiB0aGlzLnBhZ2VTaXplLCB0aGlzLmN1cnJlbnRQYWdlICogdGhpcy5wYWdlU2l6ZSkNCiAgICB9LA0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IDENCiAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgfSwNCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgaGFuZGxlQ3VycmVudENoYW5nZSh2YWwpIHsNCiAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSB2YWwNCiAgICB9LA0KICAgIG9wZW5Eb2MoKSB7DQogICAgICB3aW5kb3cub3BlbignaHR0cHM6Ly92aWV3Lm9mZmljZWFwcHMubGl2ZS5jb20vb3Avdmlldy5hc3B4P3NyYz1odHRwczovL2Vub3cub3NzLWNuLWJlaWppbmcuYWxpeXVuY3MuY29tL2ltYWdlcy8yMDI0MDkwOS8xNzI1ODQ3ODMyNTM3LnBwdHgnKQ0KICAgIH0sDQogICAgYXN5bmMgY29uZmlybVRyZWUoKSB7DQogICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgIGlkOiB0aGlzLmN1cnJlbnRSb3cuaWQsDQogICAgICAgIG1iMDA1OiB0aGlzLmN1cnJlbnRSb3cubWIwMDUsDQogICAgICAgIHpycVR5cGU6IHRoaXMuY3VycmVudFJvdy56cnFUeXBlLA0KICAgICAgICBhbGxvY2F0b3I6IHRoaXMuY3VycmVudFJvdy5hbGxvY2F0b3Iuam9pbignLCcpLA0KICAgICAgICB0eXBlOiB0aGlzLmN1cnJlbnRSb3cudHlwZSwNCiAgICAgIH0NCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHVwZGF0ZUJjRm9yY2UocGFyYW1zKQ0KICAgICAgdGhpcy50cmVlT3BlbiA9IGZhbHNlDQogICAgfSwNCiAgICBtYjAwNUNoYW5nZSgpIHsNCiAgICAgIHRoaXMuY3VycmVudFJvdy50eXBlID0gbnVsbA0KICAgICAgdGhpcy5jdXJyZW50Um93LnpycVR5cGUgPSBudWxsDQogICAgICB0aGlzLmN1cnJlbnRSb3cuYWxsb2NhdG9yID0gW10NCiAgICB9LA0KICAgIHR5cGVDaGFuZ2UoKSB7DQogICAgICB0aGlzLmN1cnJlbnRSb3cubWIwMDUgPSAnMTA0Jw0KICAgICAgdGhpcy5jdXJyZW50Um93LnpycVR5cGUgPSBudWxsDQogICAgICB0aGlzLmN1cnJlbnRSb3cuYWxsb2NhdG9yID0gW10NCiAgICB9LA0KICAgIHpycVR5cGVDaGFuZ2UoKSB7DQogICAgICB0aGlzLmN1cnJlbnRSb3cubWIwMDUgPSAnMTA0Jw0KICAgICAgdGhpcy5jdXJyZW50Um93LnR5cGUgPSAnMCcNCiAgICB9LA0KICAgIGFsbG9jYXRvckNoYW5nZSgpIHsNCiAgICAgIHRoaXMuY3VycmVudFJvdy5tYjAwNSA9ICcxMDQnDQogICAgICB0aGlzLmN1cnJlbnRSb3cudHlwZSA9ICcwJw0KICAgIH0sDQogICAgYXN5bmMgY29uZmlybU1hdGVyaWFsKCkgew0KICAgICAgYXdhaXQgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKCkNCiAgICAgIGNvbnN0IGZvcm0gPSB0aGlzLmN1cnJlbnRSb3cNCiAgICAgIGlmKGZvcm0ubWIwMDUgPT09ICcxMDQnKSB7DQogICAgICAgIGlmKCFmb3JtLnR5cGUpIHsNCiAgICAgICAgICB0aGlzLm1zZ0Vycm9yKCfor7fpgInmi6nljIXmnZDnsbvlnosnKQ0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIGlmKGZvcm0udHlwZT09PScwJykgeyAvLyDlpoLmnpzmmK/kuLvljIXmnZAs5rKh5pyJ6YCJ5oup5Li75YyF5p2Q57G75Z6LDQogICAgICAgICAgaWYoIWZvcm0uenJxVHlwZSkgew0KICAgICAgICAgICAgdGhpcy5tc2dFcnJvcign6K+36YCJ5oup5Li75YyF5p2Q57G75Z6LJykNCiAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgdGhpcy50eXBlT3BlbiA9IGZhbHNlDQogICAgfSwNCiAgICBhbGxvY2F0b3JUZXh0KGFsbG9jYXRvcikgew0KICAgICAgY29uc3QgYXJyID0gdGhpcy5hbGxvY2F0b3JPcHRpb25zLmZpbHRlcihpPT4gYWxsb2NhdG9yLmluY2x1ZGVzKGkuZGljdFZhbHVlKSkNCiAgICAgIGlmKGFyciAmJiBhcnJbMF0pIHsNCiAgICAgICAgcmV0dXJuIGFyci5tYXAoaT0+IGkuZGljdExhYmVsKS5qb2luKCcsJykNCiAgICAgIH0NCiAgICB9LA0KICAgIG1iMDA1VGV4dChtYjAwNSkgew0KICAgICAgY29uc3QgYXJyID0gdGhpcy5tYjAwNU9wdGlvbnMuZmlsdGVyKGk9PiBtYjAwNSA9PT0gaS52YWx1ZSkNCiAgICAgIGlmKGFyciAmJiBhcnJbMF0pIHsNCiAgICAgICAgcmV0dXJuIGFyclswXS5sYWJlbA0KICAgICAgfQ0KICAgIH0sDQogICAgc2hvd1R5cGUocm93KSB7DQogICAgICB0aGlzLmN1cnJlbnRSb3cgPSByb3cNCiAgICAgIHRoaXMudHJlZU9wZW4gPSB0cnVlDQogICAgfSwNCiAgICBtYXRlcmlhbFRleHQoaXRlbSkgew0KICAgICAgY29uc3QgYXJyYXkgPSBbXQ0KICAgICAgaWYoaXRlbS5tYjAwNSkgew0KICAgICAgICBhcnJheS5wdXNoKHRoaXMubWIwMDVUZXh0KGl0ZW0ubWIwMDUpKQ0KICAgICAgICBpZihpdGVtLm1iMDA1ID09PSAnMTA0Jykgew0KICAgICAgICAgIGlmKGl0ZW0udHlwZSkgew0KICAgICAgICAgICAgYXJyYXkucHVzaCh0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLmJjVHlwZU9wdGlvbnMsaXRlbS50eXBlKSkNCiAgICAgICAgICB9DQogICAgICAgICAgaWYoaXRlbS56cnFUeXBlKSB7DQogICAgICAgICAgICBhcnJheS5wdXNoKHRoaXMuc2VsZWN0RGljdExhYmVsKHRoaXMuenJxT3B0aW9ucyxpdGVtLnpycVR5cGUpKQ0KICAgICAgICAgIH0NCiAgICAgICAgICBpZihpdGVtLmFsbG9jYXRvci5sZW5ndGgpIHsNCiAgICAgICAgICAgIGFycmF5LnB1c2godGhpcy5hbGxvY2F0b3JUZXh0KGl0ZW0uYWxsb2NhdG9yKSkNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuIGFycmF5LmpvaW4oJy8nKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuICLor7fpgInmi6kiDQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBzaG93Q29sKCkgew0KICAgICAgYXdhaXQgdGhpcy4kbmV4dFRpY2soKQ0KICAgICAgdGhpcy4kcmVmcy5jdXN0b21Db2xzLmNvbHVtbnNPcGVuID0gdHJ1ZQ0KICAgIH0sDQogICAgY29sU3VjY2VzcygpIHsNCg0KICAgIH0sDQogICAgdmFsdWVUb0xhYmVsKG9wdGlvbnMsdmFsdWUpIHsNCiAgICAgIGNvbnN0IGFyciA9IG9wdGlvbnMuZmlsdGVyKGk9PiBpLnZhbHVlID09PSB2YWx1ZSkNCiAgICAgIGlmKGFyciAmJiBhcnJbMF0pIHsNCiAgICAgICAgcmV0dXJuIGFyclswXS5sYWJlbA0KICAgICAgfQ0KICAgIH0sDQogICAgcHJpY2VUZXh0KHJvdykgew0KICAgICAgaWYocm93LnByaWNlQXJyYXkpIHsNCiAgICAgICAgY29uc3QgbyA9IHJvdy5wcmljZUFycmF5WzBdDQogICAgICAgIGNvbnN0IGFycmF5ID0gW10NCiAgICAgICAgaWYoby5zdGFnZSkgew0KICAgICAgICAgIGFycmF5LnB1c2godGhpcy52YWx1ZVRvTGFiZWwodGhpcy5zdGFnZU9wdGlvbnMsby5zdGFnZSkpDQogICAgICAgIH0NCiAgICAgICAgYXJyYXkucHVzaCgiKCIpDQogICAgICAgIGFycmF5LnB1c2goby5jcmVhdGVEYXRlKQ0KICAgICAgICBhcnJheS5wdXNoKCIpIikNCiAgICAgICAgaWYoby5hcnJheSAmJiBvLmFycmF5WzBdKSB7DQogICAgICAgICAgY29uc3Qgc3ViID0gby5hcnJheVswXQ0KICAgICAgICAgIGlmKHN1Yi5ncmFkZWRUeXBlKSB7DQogICAgICAgICAgICBhcnJheS5wdXNoKHRoaXMudmFsdWVUb0xhYmVsKHRoaXMuZ3JhZGVkVHlwZU9wdGlvbnMsc3ViLmdyYWRlZFR5cGUpKQ0KICAgICAgICAgIH0NCiAgICAgICAgICBpZihzdWIucHJpY2UpIHsNCiAgICAgICAgICAgIGFycmF5LnB1c2goc3ViLnByaWNlKQ0KICAgICAgICAgIH0NCiAgICAgICAgICBpZihzdWIubW9xKSB7DQogICAgICAgICAgICBhcnJheS5wdXNoKCfotbforqLph486JyArIHN1Yi5tb3EpDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHJldHVybiBhcnJheS5qb2luKCcnKQ0KICAgICAgfQ0KICAgIH0sDQogICAgdmFsaWRhdGVUYWJsZSgpIHsNCiAgICAgIGZvciAoY29uc3QgaXRlbSBvZiB0aGlzLm1hdGVyaWFsTGlzdCkgew0KICAgICAgICBpZighaXRlbS5vcFR5cGUpIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+ivt+mAieaLqeW8gOWPkeexu+WeiyEnKQ0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIGlmKCFpdGVtLm5hbWUpIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+ivt+i+k+WFpeWQjeensCEnKQ0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIGlmKCFpdGVtLm1iMDA1KSB7DQogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCfor7fovpPlhaXnianmlpnnsbvlnoshJykNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KICAgICAgICBpZighaXRlbS5tYjAwOCkgew0KICAgICAgICAgIHRocm93IG5ldyBFcnJvcign6K+36L6T5YWl54mp5paZ5bGe5oCnIScpDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCiAgICAgICAgaWYoaXRlbS5tYjAwNSA9PT0gJzEwNCcpIHsNCiAgICAgICAgICBpZighaXRlbS50eXBlKSB7DQogICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+ivt+mAieaLqeexu+WIqyEnKQ0KICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgfQ0KICAgICAgICAgIGlmKGl0ZW0udHlwZT09PScwJyAmJiAoIWl0ZW0uenJxVHlwZSAmJiAhaXRlbS5hbGxvY2F0b3IubGVuZ3RoKSkgey8v5Li75a655Zmo5oiW5YiG6YWN5Zmo5LqM6YCJ5LiADQogICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+ivt+mAieaLqeS4u+WuueWZqOexu+WIq+aIluWIhumFjeWZqCEnKQ0KICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBzYXZlU3VjY2VzcygpIHsNCiAgICAgIHRoaXMuJGVtaXQoInNhdmVTdWNjZXNzIikNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlDQogICAgfSwNCiAgICByZXNldFByaWNlQXJyYXkoKSB7DQogICAgICB0aGlzLmNvbmZpZ0FycmF5ID0gWw0KICAgICAgICAnMScNCiAgICAgIF0NCiAgICAgIHRoaXMucHJpY2VBcnJheSA9IFtdDQogICAgICB0aGlzLmZpbGVzID0gW10NCiAgICB9LA0KICAgIGFzeW5jIGhhbmRsZVByaWNlKHJvdykgew0KICAgICAgdGhpcy5jdXJyZW50Um93ID0gcm93DQogICAgICB0aGlzLnJlc2V0UHJpY2VBcnJheSgpDQogICAgICBpZihyb3cucHJpY2VBcnJheSkgew0KICAgICAgICB0aGlzLnByaWNlQXJyYXkgPSByb3cucHJpY2VBcnJheQ0KICAgICAgfQ0KICAgICAgaWYocm93LmNvbmZpZ0FycmF5KSB7DQogICAgICAgIHRoaXMuY29uZmlnQXJyYXkgPSByb3cuY29uZmlnQXJyYXkNCiAgICAgIH0NCiAgICAgIGlmKHJvdy5maWxlcykgew0KICAgICAgICB0aGlzLmZpbGVzID0gcm93LmZpbGVzDQogICAgICB9DQogICAgICB0aGlzLm9wZW4gPSB0cnVlDQogICAgfSwNCiAgICByZXNvdXJjZVRleHQocmVzb3VyY2VUeXBlKSB7DQogICAgICBjb25zdCBhcnIgPSB0aGlzLnJlc291cmNlVHlwZU9wdGlvbnMuZmlsdGVyKGk9PiBpLnZhbHVlID09PSByZXNvdXJjZVR5cGUpDQogICAgICBpZihhcnIgJiYgYXJyWzBdKSB7DQogICAgICAgIHJldHVybiBhcnJbMF0ubGFiZWwNCiAgICAgIH0NCiAgICB9LA0KICAgIGZpbGVDaGFuZ2UoZmlsZUxpc3QsaWQpIHsNCiAgICAgIGZvcihsZXQgaXRlbSBvZiB0aGlzLm1hdGVyaWFsTGlzdCkgew0KICAgICAgICBpZihpdGVtLmtleSA9PT0gaWQgfHwgaXRlbS5pZCA9PT0gaWQpIHsNCiAgICAgICAgICBpdGVtLmZpbGVzID0gZmlsZUxpc3QNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgY29uZmlybUJjKCkgew0KICAgICAgZm9yIChjb25zdCBpdGVtIG9mIHRoaXMuY3VycmVudEJjQXJyYXkpIHsNCiAgICAgICAgbGV0IG8gPSB7DQogICAgICAgICAgcmVzb3VyY2VUeXBlOiAnYmMnLA0KICAgICAgICAgIG9wVHlwZTogbnVsbCwNCiAgICAgICAgICBjb2RlOiBudWxsLA0KICAgICAgICAgIGJjQ29kZTogaXRlbS5tYXRlcmlhbENvZGUsDQogICAgICAgICAgZXJwQ29kZTogaXRlbS5lcnBDb2RlLA0KICAgICAgICAgIG5hbWU6IGl0ZW0ubWF0ZXJpYWxOYW1lLA0KICAgICAgICAgIHNwZWM6IGl0ZW0uY2FwYWNpdHksDQogICAgICAgICAgdHlwZTogbnVsbCwNCiAgICAgICAgICB6cnFUeXBlOiBudWxsLA0KICAgICAgICAgIGFsbG9jYXRvcjogW10sDQogICAgICAgICAgbWF0ZXJpYWxUeXBlOiBudWxsLA0KICAgICAgICAgIGxlbmd0aDogaXRlbS5sZW5ndGgsDQogICAgICAgICAgd2lkdGg6IGl0ZW0ud2lkdGgsDQogICAgICAgICAgaGVpZ2h0OiBpdGVtLmhlaWdodCwNCiAgICAgICAgICBtb2RlbDogbnVsbCwNCiAgICAgICAgICB2ZW5kb3I6IG51bGwsDQogICAgICAgICAgZWNneTogbnVsbCwNCiAgICAgICAgICByZW1hcms6IG51bGwsDQogICAgICAgICAgaW1nczogbnVsbCwNCiAgICAgICAgICBmaWxlczogW10sDQogICAgICAgICAgcHJpY2VBcnJheTogW10sDQogICAgICAgICAgY29uZmlnQXJyYXk6IFtdLA0KICAgICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgICAgZGV2U3RhdHVzOiB0aGlzLmRldlN0YXR1cywNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLm1hdGVyaWFsTGlzdC5wdXNoKG8pDQogICAgICB9DQogICAgICB0aGlzLmJjT3BlbiA9IGZhbHNlDQogICAgfSwNCiAgICBiY0NoYW5nZShiY0FycmF5KSB7DQogICAgICB0aGlzLmN1cnJlbnRCY0FycmF5ID0gYmNBcnJheQ0KICAgIH0sDQogICAgc2VsZWN0QmMoKSB7DQogICAgICB0aGlzLmN1cnJlbnRCY0FycmF5ID0gW10NCiAgICAgIHRoaXMuYmNPcGVuID0gdHJ1ZQ0KICAgIH0sDQogICAgYXN5bmMgY29uZmlybUJvbSgpIHsNCiAgICAgIGF3YWl0IHRoaXMuJG5leHRUaWNrKCkNCiAgICAgIGNvbnN0IGlkcyA9IHRoaXMuJHJlZnMuYm9tVHJlZS5nZXRDaGVja2VkTm9kZXMoKS5tYXAoaSA9PiBpLmlkKQ0KICAgICAgY29uc3QgYXJyID0gdGhpcy5ib21EYXRhLmZpbHRlcihpPT5pZHMuaW5jbHVkZXMoaS5pZCkpDQogICAgICBpZihhcnIgJiYgYXJyWzBdKSB7DQogICAgICAgIGZvciAoY29uc3QgaXRlbSBvZiBhcnIpIHsNCiAgICAgICAgICBsZXQgbyA9IHsNCiAgICAgICAgICAgIHJlc291cmNlVHlwZTogJ2JvbScsDQogICAgICAgICAgICBvcFR5cGU6IG51bGwsDQogICAgICAgICAgICBjb2RlOiBudWxsLA0KICAgICAgICAgICAgYmNDb2RlOiBudWxsLA0KICAgICAgICAgICAgZXJwQ29kZTogaXRlbS5tZDAwMywNCiAgICAgICAgICAgIG5hbWU6IGl0ZW0ubWIwMDIsDQogICAgICAgICAgICBtYjAwNTogaXRlbS5tYjAwNSwNCiAgICAgICAgICAgIG1iMDA4OiBpdGVtLm1iMDA4LA0KICAgICAgICAgICAgc3BlYzogbnVsbCwNCiAgICAgICAgICAgIHR5cGU6IG51bGwsDQogICAgICAgICAgICB6cnFUeXBlOiBudWxsLA0KICAgICAgICAgICAgYWxsb2NhdG9yOiBbXSwNCiAgICAgICAgICAgIG1hdGVyaWFsVHlwZTogbnVsbCwNCiAgICAgICAgICAgIGxlbmd0aDogMCwNCiAgICAgICAgICAgIHdpZHRoOiAwLA0KICAgICAgICAgICAgaGVpZ2h0OiAwLA0KICAgICAgICAgICAgbW9kZWw6IG51bGwsDQogICAgICAgICAgICB2ZW5kb3I6IG51bGwsDQogICAgICAgICAgICBlY2d5OiBudWxsLA0KICAgICAgICAgICAgcmVtYXJrOiBudWxsLA0KICAgICAgICAgICAgaW1nczogbnVsbCwNCiAgICAgICAgICAgIGZpbGVzOiBbXSwNCiAgICAgICAgICAgIHByaWNlQXJyYXk6IFtdLA0KICAgICAgICAgICAgY29uZmlnQXJyYXk6IFtdLA0KICAgICAgICAgICAgc3VwcGxpZXJJZDogbnVsbCwNCiAgICAgICAgICAgIGRldlN0YXR1czogdGhpcy5kZXZTdGF0dXMsDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMubWF0ZXJpYWxMaXN0LnB1c2gobykNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgdGhpcy5ib21PcGVuID0gZmFsc2UNCiAgICB9LA0KICAgIHRvQm9tVHJlZShsaXN0LCBtZDAwMSkgew0KICAgICAgcmV0dXJuIGxpc3QuZmlsdGVyKGl0ZW0gPT4gew0KICAgICAgICBpZihtZDAwMSkgew0KICAgICAgICAgIGlmIChpdGVtLm1kMDAxID09PSBtZDAwMSkgew0KICAgICAgICAgICAgbGV0IGNoaWxkcmVuID0gdGhpcy50b0JvbVRyZWUobGlzdCwgaXRlbS5tZDAwMykNCiAgICAgICAgICAgIGlmKGNoaWxkcmVuICYmIGNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgaXRlbS5jaGlsZHJlbiA9IGNoaWxkcmVuDQogICAgICAgICAgICB9DQogICAgICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBpZiAoW3VuZGVmaW5lZCxudWxsLCcnXS5pbmNsdWRlcyhpdGVtLm1kMDAxKSkgew0KICAgICAgICAgICAgbGV0IGNoaWxkcmVuID0gdGhpcy50b0JvbVRyZWUobGlzdCwgaXRlbS5tZDAwMykNCiAgICAgICAgICAgIGlmKGNoaWxkcmVuICYmIGNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgaXRlbS5jaGlsZHJlbiA9IGNoaWxkcmVuDQogICAgICAgICAgICB9DQogICAgICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICBhc3luYyBnZXRFcnBJbmZvKCkgew0KICAgICAgY29uc3QgZXJwTm8gPSB0aGlzLmVycENvZGUNCiAgICAgIGlmIChlcnBObykgew0KICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlDQogICAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgICAgbGV0IGVycFJlcyA9IGF3YWl0IGdldFByb2R1Y3RCeU1kMDAzKGVycE5vKTsNCiAgICAgICAgaWYoZXJwUmVzICYmIGVycFJlcy5kYXRhKSB7DQogICAgICAgICAgbGV0IGRhdGEgPSBlcnBSZXMuZGF0YQ0KICAgICAgICAgIGxldCBmb3JtID0gdGhpcy5mb3JtDQogICAgICAgICAgbGV0IG1iMDAyID0gZGF0YS5NQjAwMg0KICAgICAgICAgIGlmKG1iMDAyKXsNCiAgICAgICAgICAgIGZvcm0ubWEwMDMgPSBkYXRhLk1BMDAzDQogICAgICAgICAgICBmb3JtLm1iMDAyID0gbWIwMDINCiAgICAgICAgICAgIGZvcm0ubWIwMDUgPSBkYXRhLk1CMDA1DQoNCiAgICAgICAgICAgIGxldCBib21MaXN0ID0gYXdhaXQgZXJwQm9tKHtlcnBOb30pDQogICAgICAgICAgICBib21MaXN0ID0gYm9tTGlzdC5maWx0ZXIoaSA9PiBpLm1iMDA1ICE9PSAnMTA1JykNCiAgICAgICAgICAgIGJvbUxpc3QucHVzaCh7DQogICAgICAgICAgICAgIGlkOiBlcnBObywNCiAgICAgICAgICAgICAgbWIwMDI6IG1iMDAyLA0KICAgICAgICAgICAgICBtZDAwMzogZXJwTm8sDQogICAgICAgICAgICAgIG1iMDA1OiBkYXRhLk1CMDA1LA0KICAgICAgICAgICAgICBtYjAwODogZGF0YS5tYjAwOCwNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICBsZXQgYm9tVHJlZSA9IHRoaXMudG9Cb21UcmVlKEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkoYm9tTGlzdCkpLCB1bmRlZmluZWQpDQogICAgICAgICAgICB0aGlzLmJvbVRyZWUgPSBib21UcmVlDQogICAgICAgICAgICB0aGlzLmJvbURhdGEgPSBib21MaXN0DQogICAgICAgICAgfWVsc2V7DQogICAgICAgICAgICB0aGlzLm1zZ0Vycm9yKCdlcnDku6PnoIHovpPlhaXmnInor68nKTsNCiAgICAgICAgICAgIGZvcm0ubWEwMDMgPSBudWxsOw0KICAgICAgICAgICAgZm9ybS5tYjAwMiA9IG51bGw7DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMubXNnRXJyb3IoJ2VycOS7o+eggei+k+WFpeacieivrycpOw0KICAgICAgICB9DQogICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlDQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBzaG93Qm9tKCkgew0KICAgICAgdGhpcy5ib21PcGVuID0gdHJ1ZQ0KICAgIH0sDQogICAgYWRkSXRlbSgpIHsNCiAgICAgIGxldCBvID0gew0KICAgICAgICByaWQ6IHRoaXMuJG5hbm9pZCgpLA0KICAgICAgICByZXNvdXJjZVR5cGU6ICdjdXN0b21lcicsDQogICAgICAgIG9wVHlwZTogbnVsbCwNCiAgICAgICAgdHlwZTogbnVsbCwNCiAgICAgICAgY29kZTogbnVsbCwNCiAgICAgICAgYmNDb2RlOiBudWxsLA0KICAgICAgICBlcnBDb2RlOiBudWxsLA0KICAgICAgICBuYW1lOiBudWxsLA0KICAgICAgICBzcGVjOiBudWxsLA0KICAgICAgICB6cnFUeXBlOiBudWxsLA0KICAgICAgICBhbGxvY2F0b3I6IFtdLA0KICAgICAgICBtYXRlcmlhbFR5cGU6IG51bGwsDQogICAgICAgIGxlbmd0aDogMCwNCiAgICAgICAgd2lkdGg6IDAsDQogICAgICAgIGhlaWdodDogMCwNCiAgICAgICAgbW9kZWw6IG51bGwsDQogICAgICAgIHZlbmRvcjogbnVsbCwNCiAgICAgICAgZWNneTogbnVsbCwNCiAgICAgICAgcmVtYXJrOiBudWxsLA0KICAgICAgICBpbWdzOiBudWxsLA0KICAgICAgICBmaWxlczogW10sDQogICAgICAgIHByaWNlQXJyYXk6IFtdLA0KICAgICAgICBjb25maWdBcnJheTogW10sDQogICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgIGRldlN0YXR1czogdGhpcy5kZXZTdGF0dXMsDQogICAgICB9DQogICAgICB0aGlzLm1hdGVyaWFsTGlzdC5wdXNoKG8pDQogICAgfSwNCiAgICBhc3luYyBkZWxJdGVtKHJvdyl7DQogICAgICBsZXQgaW5kZXg7DQogICAgICBpZihyb3cuaWQpIHsNCiAgICAgICAgaW5kZXggPSB0aGlzLm1hdGVyaWFsTGlzdC5maW5kSW5kZXgoaT0+IGkuaWQgPT09IHJvdy5pZCkNCiAgICAgIH0gZWxzZSBpZihyb3cucmlkKSB7DQogICAgICAgIGluZGV4ID0gdGhpcy5tYXRlcmlhbExpc3QuZmluZEluZGV4KGk9PiBpLnJpZCA9PT0gcm93LnJpZCkNCiAgICAgIH0NCiAgICAgIHRoaXMubWF0ZXJpYWxMaXN0LnNwbGljZShpbmRleCwxKQ0KICAgICAgaWYocm93LmlkKSB7DQogICAgICAgIGF3YWl0IGRlbEJjKHJvdy5pZCkNCiAgICAgIH0NCiAgICB9LA0KICAgIHRkQ2xhc3MocHJvamVjdEJjSWRzLHByb2plY3RCY0lkKSB7DQogICAgICByZXR1cm4gcHJvamVjdEJjSWRzLmluY2x1ZGVzKHByb2plY3RCY0lkKSA/ICdlbC1pY29uLWNoZWNrJyA6ICcnDQogICAgfSwNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["materialTable.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsa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file": "materialTable.vue", "sourceRoot": "src/views/project/project", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-row :gutter=\"20\">\r\n      <el-tooltip class=\"item\" content=\"自定义列\" effect=\"dark\">\r\n        <el-button\r\n          circle\r\n          icon=\"el-icon-menu\"\r\n          size=\"mini\"\r\n          @click=\"showCol\"/>\r\n      </el-tooltip>\r\n    </el-row>\r\n\r\n<!--    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"mini\" label-width=\"120px\">-->\r\n<!--      <el-row>-->\r\n<!--        <el-col :span=\"8\">-->\r\n<!--          <el-form-item label=\"品名\" prop=\"name\">-->\r\n<!--            <el-input-->\r\n<!--              v-model=\"queryParams.name\"-->\r\n<!--              clearable-->\r\n<!--            />-->\r\n<!--          </el-form-item>-->\r\n<!--        </el-col>-->\r\n<!--        <el-col :span=\"8\">-->\r\n<!--          <el-form-item>-->\r\n<!--            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>-->\r\n<!--            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>-->\r\n<!--          </el-form-item>-->\r\n<!--        </el-col>-->\r\n<!--      </el-row>-->\r\n<!--    </el-form>-->\r\n\r\n    <div class=\"table-wrapper small-table\">\r\n      <table :class=\"readonly?'mask':''\" class=\"base-table bc-table\">\r\n        <thead>\r\n        <tr>\r\n          <th v-if=\"!readonly\" :rowspan=\"2\" class=\"nth0\" style=\"width: 80px\" >\r\n            <el-tooltip content=\"普通添加\" >\r\n              <i class=\"el-icon-circle-plus-outline\" @click=\"addItem\" />\r\n            </el-tooltip>\r\n<!--            <el-tooltip content=\"通过erp编码添加(单个物料)\" >-->\r\n<!--              <i class=\"el-icon-circle-plus-outline\" @click=\"showErp\" />-->\r\n<!--            </el-tooltip>-->\r\n            <el-tooltip content=\"通过bom结构添加\" >\r\n              <i class=\"el-icon-circle-plus-outline\" @click=\"showBom\" />\r\n            </el-tooltip>\r\n            <el-tooltip content=\"选择包材库\" >\r\n              <i class=\"el-icon-circle-plus-outline\" @click=\"selectBc\" />\r\n            </el-tooltip>\r\n          </th>\r\n          <th v-if=\"columnsFlag('项目包材编码')\" :rowspan=\"2\" style=\"width: 120px\" >\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            项目包材编码\r\n          </th>\r\n          <th v-if=\"columnsFlag('开发类型')\" :rowspan=\"2\" style=\"width: 120px\" >\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            开发类型\r\n          </th>\r\n          <th v-if=\"columnsFlag('名称')\" :rowspan=\"2\" style=\"width: 500px\" >\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            名称\r\n          </th>\r\n          <th v-if=\"columnsFlag('类别')\" :rowspan=\"2\" style=\"width: 150px\" >\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            类别\r\n          </th>\r\n          <th v-if=\"columnsFlag('物料属性')\" :rowspan=\"2\" style=\"width: 150px\" >\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            物料属性\r\n          </th>\r\n          <th v-if=\"columnsFlag('包装材料')\" :rowspan=\"2\" style=\"width: 150px\" >包装材料</th>\r\n<!--          <th v-if=\"columnsFlag('包材库编码')\" :rowspan=\"2\" style=\"width: 150px\" >包材库编码</th>-->\r\n          <th v-if=\"columnsFlag('包材ERP编码')\" :rowspan=\"2\" style=\"width: 150px\" >包材ERP编码</th>\r\n          <th v-if=\"columnsFlag('规格')\" :rowspan=\"2\" style=\"width: 150px\" >规格</th>\r\n          <th v-if=\"columnsFlag('尺寸')\" :rowspan=\"2\" style=\"width: 250px\" >尺寸</th>\r\n          <th v-if=\"columnsFlag('型号')\" :rowspan=\"2\" style=\"width: 150px\" >型号</th>\r\n          <th v-if=\"columnsFlag('供应商')\" :rowspan=\"2\" style=\"width: 150px\" >供应商</th>\r\n          <th v-if=\"columnsFlag('图片')\" :rowspan=\"2\" style=\"width: 300px\" >图片</th>\r\n          <th v-if=\"columnsFlag('COA/SPEC')\" :rowspan=\"2\" style=\"width: 300px\" >COA/SPEC</th>\r\n          <th v-if=\"columnsFlag('备注') && devStatus === '0'\" :rowspan=\"2\" style=\"width: 300px\" >备注</th>\r\n          <th v-if=\"devStatus === '1'\" :rowspan=\"2\" style=\"width: 120px\" >报价日期</th>\r\n          <th v-if=\"devStatus === '1'\" :rowspan=\"2\" style=\"width: 120px\" >阶段</th>\r\n          <th v-if=\"devStatus === '1'\" :rowspan=\"2\" class=\"nth0\" style=\"width: 120px\" >价格</th>\r\n          <th v-for=\"log in logArray\" :key=\"log.value\" :colspan=\"log.array.length\" :style=\"{width: log.array.length * 80 + 'px'}\">{{log.label}}</th>\r\n        </tr>\r\n        <tr>\r\n          <template v-for=\"log in logArray\" >\r\n            <th v-for=\"(l,index) in log.array\" :key=\"log.value  + '_' + index \" style=\"width: 80px\" >{{l.projectItemOrderCode.substring(l.projectItemOrderCode.indexOf(\"-\")+1)}}</th>\r\n          </template>\r\n        </tr>\r\n        </thead>\r\n        <tbody>\r\n        <tr v-for=\"(item,index) in materialList.slice((currentPage-1) * pageSize, currentPage * pageSize)\" :key=\"index\">\r\n          <td v-if=\"!readonly\" class=\"nth0\" >\r\n            <i class=\"el-icon-remove-outline\" @click=\"delItem(item)\" ></i>\r\n          </td>\r\n          <td v-if=\"columnsFlag('项目包材编码')\" >{{item.code}}</td>\r\n          <td v-if=\"columnsFlag('开发类型')\" >\r\n            <el-select v-model=\"item.opType\" size=\"mini\" >\r\n              <el-option\r\n                v-for=\"item in typeOptions\"\r\n                :key=\"item.value\"\r\n                :disabled=\"item.value==2\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              />\r\n            </el-select>\r\n          </td>\r\n          <td v-if=\"columnsFlag('名称')\" >\r\n            <el-input v-model.trim=\"item.name\" size=\"mini\" />\r\n          </td>\r\n          <td v-if=\"columnsFlag('类别')\" >\r\n            <span style=\"color: #00afff;cursor: pointer\" @click=\"showType(item)\" >{{materialText(item)}}</span>\r\n<!--            <el-select v-model=\"item.type\" size=\"mini\" >-->\r\n<!--              <el-option-->\r\n<!--                v-for=\"dict in bcTypeOptions\"-->\r\n<!--                :key=\"dict.dictValue\"-->\r\n<!--                :label=\"dict.dictLabel\"-->\r\n<!--                :value=\"dict.dictValue\"-->\r\n<!--              />-->\r\n<!--            </el-select>-->\r\n<!--            <template v-if=\"item.type === '0'\" >-->\r\n<!--              <el-select v-model=\"item.zrqType\" placeholder=\"主容器\" size=\"mini\" >-->\r\n<!--                <el-option-->\r\n<!--                  v-for=\"dict in zrqOptions\"-->\r\n<!--                  :key=\"dict.dictValue\"-->\r\n<!--                  :label=\"dict.dictLabel\"-->\r\n<!--                  :value=\"dict.dictValue\"-->\r\n<!--                />-->\r\n<!--              </el-select>-->\r\n<!--              <el-select v-model=\"item.allocator\" clearable multiple placeholder=\"分配器\" size=\"mini\" >-->\r\n<!--                <el-option-->\r\n<!--                  v-for=\"dict in allocatorOptions\"-->\r\n<!--                  :key=\"dict.dictValue\"-->\r\n<!--                  :label=\"dict.dictLabel\"-->\r\n<!--                  :value=\"dict.dictValue\"-->\r\n<!--                />-->\r\n<!--              </el-select>-->\r\n<!--            </template>-->\r\n          </td>\r\n          <td v-if=\"columnsFlag('物料属性')\" >\r\n            <el-select v-model=\"item.mb008\" size=\"mini\" >\r\n              <el-option\r\n                  v-for=\"d in attrOptions\"\r\n                  :key=\"d.dictValue\"\r\n                  :label=\"d.dictLabel\"\r\n                  :value=\"d.dictValue\"\r\n              />\r\n            </el-select>\r\n          </td>\r\n          <td v-if=\"columnsFlag('包装材料')\" >\r\n            <el-select v-model=\"item.materialType\" size=\"mini\" >\r\n              <el-option\r\n                v-for=\"dict in materialTypeOptions\"\r\n                :key=\"dict.dictValue\"\r\n                :label=\"dict.dictLabel\"\r\n                :value=\"dict.dictValue\"\r\n              />\r\n            </el-select>\r\n          </td>\r\n<!--          <td v-if=\"columnsFlag('包材库编码')\" >{{item.bcCode}}</td>-->\r\n          <td v-if=\"columnsFlag('包材ERP编码')\" >\r\n            <el-input v-model.trim=\"item.erpCode\" size=\"mini\" />\r\n          </td>\r\n          <td v-if=\"columnsFlag('规格')\" ><el-input v-model=\"item.spec\" size=\"mini\" /></td>\r\n          <td v-if=\"columnsFlag('尺寸')\" >\r\n            <div style=\"display: flex;align-items: center;\" >\r\n              <el-input v-model=\"item.length\" placeholder=\"长\" size=\"mini\" style=\"width: 80px\" type=\"number\" />\r\n              *\r\n              <el-input v-model=\"item.width\" placeholder=\"宽\" size=\"mini\" style=\"width: 80px\" type=\"number\" />\r\n              *\r\n              <el-input v-model=\"item.height\" placeholder=\"高\" size=\"mini\" style=\"width: 80px\" type=\"number\" />\r\n            </div>\r\n          </td>\r\n          <td v-if=\"columnsFlag('型号')\" ><el-input v-model=\"item.model\" size=\"mini\" /></td>\r\n          <td v-if=\"columnsFlag('供应商')\" >\r\n            <el-select v-if=\"devStatus === '1'\" v-model=\"item.supplierId\" clearable  filterable size=\"mini\" >\r\n              <el-option\r\n                v-for=\"item in supplierList\"\r\n                :key=\"item.id\"\r\n                :label=\"item.name\"\r\n                :value=\"item.id\"\r\n              />\r\n            </el-select>\r\n          </td>\r\n          <td v-if=\"columnsFlag('图片')\" ><ImageUpload v-model=\"item.imgs\" :is-show-tip=\"false\" /></td>\r\n          <td v-if=\"columnsFlag('COA/SPEC')\" ><FileUpload :id=\"item.id?item.id:item.key\" v-model=\"item.files\" :is-show-tip=\"false\" :view-type=\"1\" @change=\"fileChange\" /></td>\r\n          <td v-if=\"columnsFlag('备注') && devStatus === '0'\" >\r\n            <el-input v-model=\"item.remark\" autosize placeholder=\"客户指定供应商信息、联系方式,以及其它特殊要求\" size=\"mini\" type=\"textarea\" />\r\n          </td>\r\n          <td v-if=\"devStatus === '1'\" >{{item.priceDate}}</td>\r\n          <td v-if=\"devStatus === '1'\" >{{valueToLabel(stageOptions,item.stage)}}</td>\r\n          <td v-if=\"devStatus === '1'\" class=\"nth0\" >\r\n            <div v-if=\"item.id\" style=\"color: #00afff;cursor: pointer\" @click=\"handlePrice(item)\"  >\r\n                <span v-if=\"item.price\">\r\n                  {{item.price}}\r\n                </span>\r\n              <el-tooltip v-else content=\"阶梯价\" placement=\"top\">\r\n                <span class=\"el-icon-edit\" />\r\n              </el-tooltip>\r\n            </div>\r\n          </td>\r\n          <template v-for=\"log in logArray\" >\r\n            <td v-for=\"(l,z) in log.array\" :key=\"log.value  + '_' + z \" style=\"width: 80px\" >\r\n              <span :class=\"tdClass(l.projectBcIds,item.id)\"></span>\r\n            </td>\r\n          </template>\r\n        </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <el-pagination\r\n      layout=\"prev, pager, next\"\r\n      @current-change=\"handleCurrentChange\"\r\n      :current-page=\"currentPage\"\r\n      :page-size=\"pageSize\"\r\n      :total=\"materialList.length\">\r\n    </el-pagination>\r\n\r\n    <el-dialog :visible.sync=\"bomOpen\" append-to-body title=\"选择bom\" width=\"1200px\">\r\n      <el-input v-model=\"erpCode\" size=\"mini\" >\r\n        <template slot=\"append\" >\r\n          <el-button :loading=\"btnLoading\" icon=\"el-icon-search\" @click=\"getErpInfo\" />\r\n        </template>\r\n      </el-input>\r\n      <el-tree\r\n        ref=\"bomTree\"\r\n        :data=\"bomTree\"\r\n        check-strictly\r\n        default-expand-all\r\n        node-key=\"id\"\r\n        show-checkbox\r\n      >\r\n        <span slot-scope=\"{ node, data }\" class=\"custom-tree-node\" >\r\n          <span>{{ data.mb002 }}</span>\r\n          <span>{{ data.md003 }}</span>\r\n        </span>\r\n      </el-tree>\r\n      <div class=\"dialog-footer\" style=\"margin-top: 20px\">\r\n        <el-button :loading=\"btnLoading\" size=\"mini\" type=\"primary\" @click=\"confirmBom\" >确 定</el-button>\r\n        <el-button size=\"mini\" @click=\"bomOpen = false\" >取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog :visible.sync=\"bcOpen\" append-to-body title=\"选择包材\" width=\"1200px\">\r\n      <MaterialGoodsSelectTable @change=\"bcChange\" />\r\n      <div class=\"dialog-footer\" style=\"margin-top: 20px\">\r\n        <el-button :loading=\"btnLoading\" size=\"mini\" type=\"primary\" @click=\"confirmBc\" >确 定</el-button>\r\n        <el-button size=\"mini\" @click=\"bcOpen = false\" >取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog :close-on-click-modal=\"false\" :fullscreen=\"fullscreenFlag\" :visible.sync=\"open\" append-to-body width=\"1200px\">\r\n      <div slot=\"title\" class=\"dialog-title\">\r\n        阶梯价\r\n        <el-button :icon=\"fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'\" type=\"text\"\r\n                   @click=\"fullscreenFlag = !fullscreenFlag\"/>\r\n      </div>\r\n      <BcPriceTable\r\n        v-if=\"currentRow.id\"\r\n        :config-array=\"configArray\"\r\n        :files=\"files\"\r\n        :form=\"currentRow\"\r\n        :price-array=\"priceArray\"\r\n        :project-bc-id=\"currentRow.id\"\r\n        @saveSuccess=\"saveSuccess\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <CustomCols\r\n      ref=\"customCols\"\r\n      :default-columns=\"columns\"\r\n      name=\"projectBc\"\r\n      @success=\"colSuccess\"\r\n    />\r\n\r\n    <el-dialog :visible.sync=\"typeOpen\" append-to-body title=\"选择类别\" width=\"1200px\">\r\n      <el-form ref=\"form\" :model=\"currentRow\" :rules=\"rules\" label-width=\"120px\" size=\"mini\" >\r\n        <el-form-item prop=\"mb005\">\r\n          <template #label>\r\n            物料类型\r\n          </template>\r\n          <el-radio-group v-model=\"currentRow.mb005\" style=\"width: 90%;\" @input=\"mb005Change\" >\r\n            <el-row :gutter=\"20\" >\r\n              <el-col v-for=\"dict in mb005Options\" :key=\"dict.value\" :span=\"3\" >\r\n                <el-radio :label=\"dict.value\" style=\"padding-bottom: 10px\" >\r\n                  {{dict.label}}\r\n                </el-radio>\r\n              </el-col>\r\n            </el-row>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"currentRow.mb005 === '104'\" prop=\"type\">\r\n          <template #label>\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            包材类别\r\n          </template>\r\n          <el-radio-group v-model=\"currentRow.type\" @input=\"typeChange\" >\r\n            <el-row :gutter=\"20\" >\r\n              <el-col v-for=\"dict in bcTypeOptions\" :key=\"dict.dictValue\" :span=\"3\" >\r\n                <el-radio :label=\"dict.dictValue\" style=\"padding-bottom: 10px\">\r\n                  {{dict.dictLabel}}\r\n                </el-radio>\r\n              </el-col>\r\n            </el-row>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"currentRow.mb005 === '104' && currentRow.type === '0'\" >\r\n          <template #label>\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            主容器-类别\r\n          </template>\r\n          <el-radio-group v-model=\"currentRow.zrqType\" >\r\n            <el-row :gutter=\"20\" >\r\n              <el-col v-for=\"dict in zrqOptions\" :key=\"dict.dictValue\" :span=\"3\" >\r\n                <el-radio :label=\"dict.dictValue\" style=\"padding-bottom: 10px\">\r\n                  {{dict.dictLabel}}\r\n                </el-radio>\r\n              </el-col>\r\n            </el-row>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"currentRow.mb005 === '104' && currentRow.allocator && currentRow.type === '0'\" label=\"分配器\" >\r\n          <el-checkbox-group v-model=\"currentRow.allocator\">\r\n            <el-row :gutter=\"20\" >\r\n              <el-col v-for=\"dict in allocatorOptions\" :key=\"dict.dictValue\" :span=\"3\" >\r\n                <el-checkbox\r\n                  :label=\"dict.dictValue\">\r\n                  {{dict.dictLabel}}\r\n                </el-checkbox>\r\n              </el-col>\r\n            </el-row>\r\n          </el-checkbox-group>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div class=\"dialog-footer\" style=\"margin-top: 20px\">\r\n        <el-button :loading=\"btnLoading\" size=\"mini\" type=\"primary\" @click=\"confirmMaterial\" >确 定</el-button>\r\n        <el-button size=\"mini\" @click=\"typeOpen = false\" >取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog :visible.sync=\"treeOpen\" append-to-body width=\"1200px\">\r\n      <template #title>\r\n        选择类别\r\n        <el-button\r\n          icon=\"el-icon-question\"\r\n          size=\"mini\"\r\n          type=\"text\"\r\n          @click=\"openDoc\"\r\n        >包材分类</el-button>\r\n      </template>\r\n\r\n      <el-radio-group v-model=\"currentRow.mb005\" style=\"width: 90%;\" @input=\"mb005Change\" >\r\n        <el-row :gutter=\"20\" >\r\n          <el-col v-for=\"dict in mb005Options.filter(i=>i.value !== '104')\" :key=\"dict.value\" :span=\"3\" >\r\n            <el-radio :label=\"dict.value\" style=\"font-size: 18px;font-weight: 700;margin-bottom: 10px\" >\r\n              {{dict.label}}\r\n            </el-radio>\r\n          </el-col>\r\n        </el-row>\r\n      </el-radio-group>\r\n      <div style=\"display: flex;margin-bottom: 10px\" >\r\n        <div v-for=\"dict in mb005Options.filter(i=>i.value === '104')\" :key=\"dict.value\" class=\"label\"  style=\"font-size: 15px;font-weight: 700;width: 100px\" >\r\n          {{dict.label}}\r\n        </div>\r\n        <div >\r\n          <div>\r\n            <div class=\"row-wrapper\">\r\n              <div class=\"label\" >主包材类别</div>\r\n              <div class=\"content\">\r\n                <el-radio-group v-model=\"currentRow.zrqType\" @input=\"zrqTypeChange\" >\r\n                  <el-row :gutter=\"20\" >\r\n                    <el-col v-for=\"dict in zrqOptions\" :key=\"dict.dictValue\" :span=\"4\" >\r\n                      <el-radio :label=\"dict.dictValue\" style=\"padding-bottom: 10px\">\r\n                        {{dict.dictLabel}}\r\n                      </el-radio>\r\n                    </el-col>\r\n                  </el-row>\r\n                </el-radio-group>\r\n              </div>\r\n            </div>\r\n            <div class=\"row-wrapper\">\r\n              <div class=\"label\">分配器</div>\r\n              <div class=\"content\">\r\n                <el-checkbox-group v-model=\"currentRow.allocator\" @input=\"allocatorChange\" >\r\n                  <el-row :gutter=\"20\" >\r\n                    <el-col v-for=\"dict in allocatorOptions\" :key=\"dict.dictValue\" :span=\"4\" >\r\n                      <el-checkbox\r\n                        :label=\"dict.dictValue\">\r\n                        {{dict.dictLabel}}\r\n                      </el-checkbox>\r\n                    </el-col>\r\n                  </el-row>\r\n                </el-checkbox-group>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div style=\"margin-top: 10px\">\r\n            <el-radio-group v-model=\"currentRow.type\" @input=\"typeChange\" >\r\n              <el-row :gutter=\"20\" >\r\n                <el-col v-for=\"dict in bcTypeOptions.filter(i=>i.dictValue !== '0')\" :key=\"dict.dictValue\" :span=\"4\" >\r\n                  <el-radio :label=\"dict.dictValue\" style=\"padding-bottom: 10px\">\r\n                    {{dict.dictLabel}}\r\n                    <el-tooltip v-if=\"dict.dictLabel === '辅助工具'\" content=\"(例如:粉扑、粉刷、勺子等)\" >\r\n                      <i class=\"el-icon-question\" />\r\n                    </el-tooltip>\r\n                  </el-radio>\r\n                </el-col>\r\n              </el-row>\r\n            </el-radio-group>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"dialog-footer\" style=\"margin-top: 20px\">\r\n        <el-button :loading=\"btnLoading\" size=\"mini\" type=\"primary\" @click=\"confirmTree\" >确 定</el-button>\r\n        <el-button size=\"mini\" @click=\"treeOpen = false\" >取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n<script>\r\nimport {getBomByErpCode, getProductByMd003} from \"@/api/common/erp\";\r\nimport MaterialGoodsSelectTable from \"@/views/resource/materialGoods/selectTable.vue\";\r\nimport {supplierAll} from \"@/api/supplier/supplier\";\r\nimport {allBcLog} from \"@/api/project/bcLog\";\r\nimport BcPriceTable from \"@/views/sop/bc/bcPriceTable.vue\";\r\nimport CustomCols from \"@/components/customCols.vue\";\r\nimport {allTreeData} from \"@/api/system/treeData\";\r\nimport {erpBom} from \"@/api/production/schedulePlan\";\r\nimport {delBc, updateBcForce} from \"@/api/project/bc\";\r\n\r\nexport default {\r\n  name: 'projectBcTable',\r\n  components: {\r\n    CustomCols,\r\n    BcPriceTable,\r\n    MaterialGoodsSelectTable,\r\n  },\r\n  props: {\r\n    form: {\r\n      type: Object,\r\n      required: true,\r\n    },\r\n    materialList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    devStatus: {\r\n      type: String,\r\n      default: '0',\r\n    },\r\n    readonly: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  watch: {\r\n    'form.id': {\r\n      async handler(v) {\r\n        this.resetQuery()\r\n        if(v && this.readonly) {//如果是视图模式\r\n          let bcLogList = await allBcLog({projectId: v, status: '3'})\r\n          let logArray = []\r\n          for (let item of this.logOptions) {\r\n            let bcLog = bcLogList.filter(i => i.projectId === v && i.type === item.dictValue)\r\n            if (bcLog && bcLog.length > 0) {\r\n              let array = []\r\n              let keys = []\r\n              for (let bc of bcLog) {\r\n                if (!keys.includes(bc.projectItemOrderCode)) {\r\n                  keys.push(bc.projectItemOrderCode)\r\n                }\r\n              }\r\n              for (let k of keys) {\r\n                let projectBcIds = bcLog.filter(i => i.projectItemOrderCode === k).map(i => i.projectBcId)\r\n                array.push({\r\n                  projectItemOrderCode: k,\r\n                  projectBcIds,\r\n                })\r\n              }\r\n              logArray.push({\r\n                label: item.dictLabel,\r\n                value: item.dictValue,\r\n                array,\r\n              })\r\n            }\r\n          }\r\n          this.logArray = logArray\r\n          this.bcLogList = bcLogList\r\n        }\r\n      },\r\n      immediate: true,\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      queryParams: {\r\n        name: null,\r\n      },\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      loading: false,\r\n      btnLoading: false,\r\n      bomOpen: false,\r\n      bcOpen: false,\r\n      open: false,\r\n      fullscreenFlag: false,\r\n      bcTypeOptions: [],\r\n      bomData: [],\r\n      bomTree: [],\r\n      currentBcArray: [],\r\n      typeOptions: [\r\n        {label: '包材开发',value: '0'},\r\n        {label: '客户开发',value: '1'},\r\n        {label: '采购开发',value: '2'},\r\n        {label: '自制',value: '3'},  //20241122研发开发改为自制\r\n      ],\r\n      resourceTypeOptions: [\r\n        {label: '普通添加',value: 'customer'},\r\n        {label: 'bom',value: 'bom'},\r\n        {label: '包材库',value: 'bc'},\r\n        {label: '包材开发',value: 'dev'},\r\n      ],\r\n      attrOptions: [],\r\n      zrqOptions: [],\r\n      materialTypeOptions: [],\r\n      supplierList: [],\r\n      title: null,\r\n      columns: [\r\n        {label: '开发类型',visible: true},\r\n        {label: '项目包材编码',visible: true},\r\n        {label: '类别',visible: true},\r\n        {label: '包装材料',visible: true},\r\n        {label: '包材库编码',visible: true},\r\n        {label: '包材ERP编码',visible: true},\r\n        {label: '物料属性',visible: true},\r\n        {label: '名称',visible: true},\r\n        {label: '规格',visible: true},\r\n        {label: '尺寸',visible: true},\r\n        {label: '型号',visible: true},\r\n        {label: '供应商',visible: true},\r\n        {label: '图片',visible: false},\r\n        {label: 'COA/SPEC',visible: false},\r\n        {label: '备注',visible: true},\r\n      ],\r\n      logArray: [],\r\n      bcLogList: [],\r\n      logOptions: [],\r\n      priceArray: [],\r\n      configArray: [],\r\n      files: [],\r\n      currentRow: {},\r\n      gradedTypeOptions: [\r\n        {label: '订单价',value: '0'},\r\n        {label: 'MOQ价',value: '1'},\r\n        {label: '梯度价(一档)',value: '2'},\r\n        {label: '梯度价(二档)',value: '3'},\r\n        {label: '梯度价(三档)',value: '4'},\r\n        {label: '梯度价(四档)',value: '5'},\r\n        {label: '梯度价(五档)',value: '6'},\r\n        {label: '梯度价(六档)',value: '7'},\r\n        {label: '梯度价(七档)',value: '8'},\r\n        {label: '梯度价(八档)',value: '9'},\r\n        {label: '梯度价(九档)',value: '10'},\r\n      ],\r\n      stageOptions: [\r\n        {label:'裸包价',value: '0'},\r\n        {label:'寻样阶段',value: '1'},\r\n        {label:'打样阶段',value: '2'},\r\n        {label:'订单阶段',value: '3'},\r\n      ],\r\n      allocatorOptions: [],\r\n      erpCode: [],\r\n      typeOpen: false,\r\n      treeOpen: false,\r\n      mb005Options: [\r\n        {label: '包材',value: '104'},\r\n        {label: '半成品',value: '103'},\r\n        {label: '裸装品',value: '102'},\r\n        {label: '成品',value: '101'},\r\n      ],\r\n      rules: {\r\n        mb005: [\r\n          {required: true,msg: '请选择物料类型'}\r\n        ]\r\n      },\r\n      materialArray: [],\r\n    }\r\n  },\r\n  async created() {\r\n    this.getDicts(\"PRODUCT_PROPERTIES\").then(response => {\r\n      const attrOptions = response.data\r\n      attrOptions.push({dictLabel: '自制',dictValue: '0'})\r\n      attrOptions.push({dictLabel: '外购',dictValue: '1'})\r\n      attrOptions.push({dictLabel: '客指代采',dictValue: '3'})\r\n      this.attrOptions = attrOptions\r\n    })\r\n    let logRes = await this.getDicts(\"project_bc_log\")\r\n    this.logOptions = logRes.data;\r\n    let bcTypeRes = await this.getDicts(\"project_bc_type\")\r\n    this.bcTypeOptions = bcTypeRes.data;\r\n    this.getDicts(\"bc-zrq\").then(response => {\r\n      this.zrqOptions = response.data;\r\n    })\r\n    this.getDicts(\"BZCL\").then(response => {\r\n      this.materialTypeOptions = response.data;\r\n    })\r\n    this.getDicts(\"bc-fpq\").then(response => {\r\n      this.allocatorOptions = response.data;\r\n    })\r\n    let supplierList = await supplierAll({supplierType: '1',reqType:1});\r\n    this.supplierList = supplierList;\r\n  },\r\n  methods: {\r\n    getList() {\r\n      let materialArray = this.materialList.filter(i => i.mb005 !== '103')\r\n      let params = Object.assign({}, this.queryParams)\r\n      if (params.name) {\r\n        materialArray = materialArray.filter(i => i.name == params.name)\r\n      }\r\n      this.materialArray = materialArray.slice((this.currentPage-1) * this.pageSize, this.currentPage * this.pageSize)\r\n    },\r\n    handleQuery() {\r\n      this.currentPage = 1\r\n      this.getList()\r\n    },\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.currentPage = val\r\n    },\r\n    openDoc() {\r\n      window.open('https://view.officeapps.live.com/op/view.aspx?src=https://enow.oss-cn-beijing.aliyuncs.com/images/20240909/1725847832537.pptx')\r\n    },\r\n    async confirmTree() {\r\n      const params = {\r\n        id: this.currentRow.id,\r\n        mb005: this.currentRow.mb005,\r\n        zrqType: this.currentRow.zrqType,\r\n        allocator: this.currentRow.allocator.join(','),\r\n        type: this.currentRow.type,\r\n      }\r\n      const res = await updateBcForce(params)\r\n      this.treeOpen = false\r\n    },\r\n    mb005Change() {\r\n      this.currentRow.type = null\r\n      this.currentRow.zrqType = null\r\n      this.currentRow.allocator = []\r\n    },\r\n    typeChange() {\r\n      this.currentRow.mb005 = '104'\r\n      this.currentRow.zrqType = null\r\n      this.currentRow.allocator = []\r\n    },\r\n    zrqTypeChange() {\r\n      this.currentRow.mb005 = '104'\r\n      this.currentRow.type = '0'\r\n    },\r\n    allocatorChange() {\r\n      this.currentRow.mb005 = '104'\r\n      this.currentRow.type = '0'\r\n    },\r\n    async confirmMaterial() {\r\n      await this.$refs[\"form\"].validate()\r\n      const form = this.currentRow\r\n      if(form.mb005 === '104') {\r\n        if(!form.type) {\r\n          this.msgError('请选择包材类型')\r\n          return\r\n        }\r\n        if(form.type==='0') { // 如果是主包材,没有选择主包材类型\r\n          if(!form.zrqType) {\r\n            this.msgError('请选择主包材类型')\r\n            return\r\n          }\r\n        }\r\n      }\r\n      this.typeOpen = false\r\n    },\r\n    allocatorText(allocator) {\r\n      const arr = this.allocatorOptions.filter(i=> allocator.includes(i.dictValue))\r\n      if(arr && arr[0]) {\r\n        return arr.map(i=> i.dictLabel).join(',')\r\n      }\r\n    },\r\n    mb005Text(mb005) {\r\n      const arr = this.mb005Options.filter(i=> mb005 === i.value)\r\n      if(arr && arr[0]) {\r\n        return arr[0].label\r\n      }\r\n    },\r\n    showType(row) {\r\n      this.currentRow = row\r\n      this.treeOpen = true\r\n    },\r\n    materialText(item) {\r\n      const array = []\r\n      if(item.mb005) {\r\n        array.push(this.mb005Text(item.mb005))\r\n        if(item.mb005 === '104') {\r\n          if(item.type) {\r\n            array.push(this.selectDictLabel(this.bcTypeOptions,item.type))\r\n          }\r\n          if(item.zrqType) {\r\n            array.push(this.selectDictLabel(this.zrqOptions,item.zrqType))\r\n          }\r\n          if(item.allocator.length) {\r\n            array.push(this.allocatorText(item.allocator))\r\n          }\r\n        }\r\n        return array.join('/')\r\n      } else {\r\n        return \"请选择\"\r\n      }\r\n    },\r\n    async showCol() {\r\n      await this.$nextTick()\r\n      this.$refs.customCols.columnsOpen = true\r\n    },\r\n    colSuccess() {\r\n\r\n    },\r\n    valueToLabel(options,value) {\r\n      const arr = options.filter(i=> i.value === value)\r\n      if(arr && arr[0]) {\r\n        return arr[0].label\r\n      }\r\n    },\r\n    priceText(row) {\r\n      if(row.priceArray) {\r\n        const o = row.priceArray[0]\r\n        const array = []\r\n        if(o.stage) {\r\n          array.push(this.valueToLabel(this.stageOptions,o.stage))\r\n        }\r\n        array.push(\"(\")\r\n        array.push(o.createDate)\r\n        array.push(\")\")\r\n        if(o.array && o.array[0]) {\r\n          const sub = o.array[0]\r\n          if(sub.gradedType) {\r\n            array.push(this.valueToLabel(this.gradedTypeOptions,sub.gradedType))\r\n          }\r\n          if(sub.price) {\r\n            array.push(sub.price)\r\n          }\r\n          if(sub.moq) {\r\n            array.push('起订量:' + sub.moq)\r\n          }\r\n        }\r\n        return array.join('')\r\n      }\r\n    },\r\n    validateTable() {\r\n      for (const item of this.materialList) {\r\n        if(!item.opType) {\r\n          throw new Error('请选择开发类型!')\r\n          return\r\n        }\r\n        if(!item.name) {\r\n          throw new Error('请输入名称!')\r\n          return\r\n        }\r\n        if(!item.mb005) {\r\n          throw new Error('请输入物料类型!')\r\n          return\r\n        }\r\n        if(!item.mb008) {\r\n          throw new Error('请输入物料属性!')\r\n          return\r\n        }\r\n        if(item.mb005 === '104') {\r\n          if(!item.type) {\r\n            throw new Error('请选择类别!')\r\n            return\r\n          }\r\n          if(item.type==='0' && (!item.zrqType && !item.allocator.length)) {//主容器或分配器二选一\r\n            throw new Error('请选择主容器类别或分配器!')\r\n            return\r\n          }\r\n        }\r\n      }\r\n    },\r\n    async saveSuccess() {\r\n      this.$emit(\"saveSuccess\")\r\n      this.open = false\r\n    },\r\n    resetPriceArray() {\r\n      this.configArray = [\r\n        '1'\r\n      ]\r\n      this.priceArray = []\r\n      this.files = []\r\n    },\r\n    async handlePrice(row) {\r\n      this.currentRow = row\r\n      this.resetPriceArray()\r\n      if(row.priceArray) {\r\n        this.priceArray = row.priceArray\r\n      }\r\n      if(row.configArray) {\r\n        this.configArray = row.configArray\r\n      }\r\n      if(row.files) {\r\n        this.files = row.files\r\n      }\r\n      this.open = true\r\n    },\r\n    resourceText(resourceType) {\r\n      const arr = this.resourceTypeOptions.filter(i=> i.value === resourceType)\r\n      if(arr && arr[0]) {\r\n        return arr[0].label\r\n      }\r\n    },\r\n    fileChange(fileList,id) {\r\n      for(let item of this.materialList) {\r\n        if(item.key === id || item.id === id) {\r\n          item.files = fileList\r\n        }\r\n      }\r\n    },\r\n    confirmBc() {\r\n      for (const item of this.currentBcArray) {\r\n        let o = {\r\n          resourceType: 'bc',\r\n          opType: null,\r\n          code: null,\r\n          bcCode: item.materialCode,\r\n          erpCode: item.erpCode,\r\n          name: item.materialName,\r\n          spec: item.capacity,\r\n          type: null,\r\n          zrqType: null,\r\n          allocator: [],\r\n          materialType: null,\r\n          length: item.length,\r\n          width: item.width,\r\n          height: item.height,\r\n          model: null,\r\n          vendor: null,\r\n          ecgy: null,\r\n          remark: null,\r\n          imgs: null,\r\n          files: [],\r\n          priceArray: [],\r\n          configArray: [],\r\n          supplierId: null,\r\n          devStatus: this.devStatus,\r\n        }\r\n        this.materialList.push(o)\r\n      }\r\n      this.bcOpen = false\r\n    },\r\n    bcChange(bcArray) {\r\n      this.currentBcArray = bcArray\r\n    },\r\n    selectBc() {\r\n      this.currentBcArray = []\r\n      this.bcOpen = true\r\n    },\r\n    async confirmBom() {\r\n      await this.$nextTick()\r\n      const ids = this.$refs.bomTree.getCheckedNodes().map(i => i.id)\r\n      const arr = this.bomData.filter(i=>ids.includes(i.id))\r\n      if(arr && arr[0]) {\r\n        for (const item of arr) {\r\n          let o = {\r\n            resourceType: 'bom',\r\n            opType: null,\r\n            code: null,\r\n            bcCode: null,\r\n            erpCode: item.md003,\r\n            name: item.mb002,\r\n            mb005: item.mb005,\r\n            mb008: item.mb008,\r\n            spec: null,\r\n            type: null,\r\n            zrqType: null,\r\n            allocator: [],\r\n            materialType: null,\r\n            length: 0,\r\n            width: 0,\r\n            height: 0,\r\n            model: null,\r\n            vendor: null,\r\n            ecgy: null,\r\n            remark: null,\r\n            imgs: null,\r\n            files: [],\r\n            priceArray: [],\r\n            configArray: [],\r\n            supplierId: null,\r\n            devStatus: this.devStatus,\r\n          }\r\n          this.materialList.push(o)\r\n        }\r\n      }\r\n      this.bomOpen = false\r\n    },\r\n    toBomTree(list, md001) {\r\n      return list.filter(item => {\r\n        if(md001) {\r\n          if (item.md001 === md001) {\r\n            let children = this.toBomTree(list, item.md003)\r\n            if(children && children.length > 0) {\r\n              item.children = children\r\n            }\r\n            return true\r\n          }\r\n        } else {\r\n          if ([undefined,null,''].includes(item.md001)) {\r\n            let children = this.toBomTree(list, item.md003)\r\n            if(children && children.length > 0) {\r\n              item.children = children\r\n            }\r\n            return true\r\n          }\r\n        }\r\n        return false\r\n      })\r\n    },\r\n    async getErpInfo() {\r\n      const erpNo = this.erpCode\r\n      if (erpNo) {\r\n        this.btnLoading = true\r\n        this.loading = true\r\n        let erpRes = await getProductByMd003(erpNo);\r\n        if(erpRes && erpRes.data) {\r\n          let data = erpRes.data\r\n          let form = this.form\r\n          let mb002 = data.MB002\r\n          if(mb002){\r\n            form.ma003 = data.MA003\r\n            form.mb002 = mb002\r\n            form.mb005 = data.MB005\r\n\r\n            let bomList = await erpBom({erpNo})\r\n            bomList = bomList.filter(i => i.mb005 !== '105')\r\n            bomList.push({\r\n              id: erpNo,\r\n              mb002: mb002,\r\n              md003: erpNo,\r\n              mb005: data.MB005,\r\n              mb008: data.mb008,\r\n            })\r\n            let bomTree = this.toBomTree(JSON.parse(JSON.stringify(bomList)), undefined)\r\n            this.bomTree = bomTree\r\n            this.bomData = bomList\r\n          }else{\r\n            this.msgError('erp代码输入有误');\r\n            form.ma003 = null;\r\n            form.mb002 = null;\r\n          }\r\n        } else {\r\n          this.msgError('erp代码输入有误');\r\n        }\r\n        this.btnLoading = false\r\n        this.loading = false\r\n      }\r\n    },\r\n    async showBom() {\r\n      this.bomOpen = true\r\n    },\r\n    addItem() {\r\n      let o = {\r\n        rid: this.$nanoid(),\r\n        resourceType: 'customer',\r\n        opType: null,\r\n        type: null,\r\n        code: null,\r\n        bcCode: null,\r\n        erpCode: null,\r\n        name: null,\r\n        spec: null,\r\n        zrqType: null,\r\n        allocator: [],\r\n        materialType: null,\r\n        length: 0,\r\n        width: 0,\r\n        height: 0,\r\n        model: null,\r\n        vendor: null,\r\n        ecgy: null,\r\n        remark: null,\r\n        imgs: null,\r\n        files: [],\r\n        priceArray: [],\r\n        configArray: [],\r\n        supplierId: null,\r\n        devStatus: this.devStatus,\r\n      }\r\n      this.materialList.push(o)\r\n    },\r\n    async delItem(row){\r\n      let index;\r\n      if(row.id) {\r\n        index = this.materialList.findIndex(i=> i.id === row.id)\r\n      } else if(row.rid) {\r\n        index = this.materialList.findIndex(i=> i.rid === row.rid)\r\n      }\r\n      this.materialList.splice(index,1)\r\n      if(row.id) {\r\n        await delBc(row.id)\r\n      }\r\n    },\r\n    tdClass(projectBcIds,projectBcId) {\r\n      return projectBcIds.includes(projectBcId) ? 'el-icon-check' : ''\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.row-wrapper {\r\n  display: flex;\r\n\r\n  .label {\r\n    width: 150px;\r\n    font-size: 13px;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .content {\r\n\r\n  }\r\n}\r\n.table-wrapper {\r\n\r\n  .base-table {\r\n    thead {\r\n      position: sticky;\r\n      top: 0;\r\n      z-index: 3;\r\n\r\n      .nth0 {\r\n        background-color: rgba(248,248,249,1);\r\n      }\r\n\r\n    }\r\n\r\n    tbody {\r\n      .nth0 {\r\n        background-color: rgba(255,255,255,1);\r\n      }\r\n    }\r\n\r\n    .nth0 {\r\n      position: sticky;\r\n      left: 0;\r\n      z-index: 1;\r\n    }\r\n\r\n  }\r\n\r\n}\r\n</style>\r\n"]}]}