import request from '@/utils/request'

// 查询成品检验类型列表
export function listFinishedType(query) {
  return request({
    url: '/qc/finishedType/list',
    method: 'get',
    params: query
  })
}

// 查询成品检验类型详细
export function getFinishedType(id) {
  return request({
    url: '/qc/finishedType/' + id,
    method: 'get'
  })
}

// 新增成品检验类型
export function addFinishedType(data) {
  return request({
    url: '/qc/finishedType',
    method: 'post',
    data: data
  })
}

// 修改成品检验类型
export function updateFinishedType(data) {
  return request({
    url: '/qc/finishedType',
    method: 'put',
    data: data
  })
}

// 删除成品检验类型
export function delFinishedType(id) {
  return request({
    url: '/qc/finishedType/' + id,
    method: 'delete'
  })
}

// 导出成品检验类型
export function exportFinishedType(query) {
  return request({
    url: '/qc/finishedType/export',
    method: 'get',
    params: query
  })
}

export function allFinishedType(query) {
  return request({
    url: '/qc/finishedType/all',
    method: 'get',
    params: query
  })
}
