import request from '@/utils/request'

// 查询设备类型列表
export function listEquipmentType(query) {
  return request({
    url: '/production/equipmentType/list',
    method: 'get',
    params: query
  })
}

// 查询设备类型详细
export function getEquipmentType(id) {
  return request({
    url: '/production/equipmentType/' + id,
    method: 'get'
  })
}

// 新增设备类型
export function addEquipmentType(data) {
  return request({
    url: '/production/equipmentType',
    method: 'post',
    data: data
  })
}

// 修改设备类型
export function updateEquipmentType(data) {
  return request({
    url: '/production/equipmentType',
    method: 'put',
    data: data
  })
}

// 删除设备类型
export function delEquipmentType(id) {
  return request({
    url: '/production/equipmentType/' + id,
    method: 'delete'
  })
}

// 导出设备类型
export function exportEquipmentType(query) {
  return request({
    url: '/production/equipmentType/export',
    method: 'get',
    params: query
  })
}

export function allEquipmentType(query) {
  return request({
    url: '/production/equipmentType/all',
    method: 'get',
    params: query
  })
}

export function getProductionEquipmentTypeByPid(pid) {
  return request({
    url: '/production/equipmentType/getProductionEquipmentTypeByPid' + pid,
    method: 'get'
  })
}
