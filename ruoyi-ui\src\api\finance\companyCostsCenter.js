import request from '@/utils/request'

// 查询各中心人力成本列表
export function listCompanyCostsCenter(query) {
  return request({
    url: '/finance/companyCostsCenter/list',
    method: 'get',
    params: query
  })
}


//工资汇总
export function statisticsWagesData(query) {
  return request({
    url: '/finance/companyCostsCenter/statisticsWages',
    method: 'get',
    params: query
  })
}

// 查询各中心人力成本详细
export function getCompanyCostsCenter(id) {
  return request({
    url: '/finance/companyCostsCenter/' + id,
    method: 'get'
  })
}

// 新增各中心人力成本
export function addCompanyCostsCenter(data) {
  return request({
    url: '/finance/companyCostsCenter',
    method: 'post',
    data: data
  })
}

// 修改各中心人力成本
export function updateCompanyCostsCenter(data) {
  return request({
    url: '/finance/companyCostsCenter',
    method: 'put',
    data: data
  })
}



// 修改各中心人力成本
export function refresh(data) {
  return request({
    url: '/finance/companyCostsCenter/refresh',
    method: 'put',
    data: data
  })
}

// 删除各中心人力成本
export function delCompanyCostsCenter(id) {
  return request({
    url: '/finance/companyCostsCenter/' + id,
    method: 'delete'
  })
}

// 导出各中心人力成本
export function exportCompanyCostsCenter(query) {
  return request({
    url: '/finance/companyCostsCenter/export',
    method: 'get',
    params: query
  })
}

export function exportStatisticsWages(query) {
  return request({
    url: '/finance/companyCostsCenter/exportStatisticsWages',
    method: 'get',
    params: query
  })
}
