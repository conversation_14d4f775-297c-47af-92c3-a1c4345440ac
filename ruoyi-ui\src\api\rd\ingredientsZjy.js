import request from '@/utils/request'

// 查询成分-中检院列表
export function listIngredientsZjy(query) {
  return request({
    url: '/rd/ingredientsZjy/list',
    method: 'get',
    params: query
  })
}

// 查询成分-中检院详细
export function getIngredientsZjy(id) {
  return request({
    url: '/rd/ingredientsZjy/' + id,
    method: 'get'
  })
}

// 新增成分-中检院
export function addIngredientsZjy(data) {
  return request({
    url: '/rd/ingredientsZjy',
    method: 'post',
    data: data
  })
}

// 修改成分-中检院
export function updateIngredientsZjy(data) {
  return request({
    url: '/rd/ingredientsZjy',
    method: 'put',
    data: data
  })
}

// 删除成分-中检院
export function delIngredientsZjy(id) {
  return request({
    url: '/rd/ingredientsZjy/' + id,
    method: 'delete'
  })
}

// 导出成分-中检院
export function exportIngredientsZjy(query) {
  return request({
    url: '/rd/ingredientsZjy/export',
    method: 'get',
    params: query
  })
}

export function allIngredientsZjy(query) {
  return request({
    url: '/rd/ingredientsZjy/all',
    method: 'get',
    params: query
  })
}
