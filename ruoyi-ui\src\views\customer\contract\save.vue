<template>
  <div v-loading="loading">
    <el-form ref="form" :model="form" :rules="rules" size="mini" label-width="150px" >
      <div :class="readonly ? 'mask' : ''" >
        <el-row :gutter="20" >
          <el-col :span="8">
            <el-form-item label="文件名称" prop="name">
              <el-input v-model="form.name" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="甲方" prop="partyA">
              <el-input v-model="form.partyA" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="乙方" prop="partyB">
              <el-input v-model="form.partyB" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="签订日期" prop="signedDate">
              <el-date-picker
                v-model="form.signedDate"
                clearable
                type="date"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="起效日期" prop="onSetDate">
              <el-date-picker
                v-model="form.onSetDate"
                clearable
                type="date"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="到期日期" prop="expireDate">
              <el-date-picker
                v-model="form.expireDate"
                clearable
                type="date"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="账期描述" prop="paymentTerm">
          <el-input v-model="form.paymentTerm" autosize type="textarea" />
        </el-form-item>
        <el-form-item label="财务风险描述" prop="financialRisk">
          <el-input v-model="form.financialRisk" autosize type="textarea" />
        </el-form-item>
        <el-form-item label="交货数量控制条款" prop="deliveryControl">
          <el-input v-model="form.deliveryControl" autosize type="textarea" />
        </el-form-item>
        <el-form-item label="交货延误风险条款" prop="delayRisk">
          <el-input v-model="form.delayRisk" autosize type="textarea" />
        </el-form-item>
        <el-form-item label="质量责任协议" prop="qualityResponsibility">
          <el-input v-model="form.qualityResponsibility" autosize type="textarea" />
        </el-form-item>
        <el-form-item label="质量风险描述" prop="qualityRisk">
          <el-input v-model="form.qualityRisk" autosize type="textarea" />
        </el-form-item>
        <el-form-item label="生产损耗与成本风险" prop="productionRisk">
          <el-input v-model="form.productionRisk" autosize type="textarea" />
        </el-form-item>
        <el-form-item label="知识产权与保密风险" prop="ipRisk">
          <el-input v-model="form.ipRisk" autosize type="textarea" />
        </el-form-item>
        <el-form-item label="违约责任条款" prop="breachLiability">
          <el-input v-model="form.breachLiability" autosize type="textarea" />
        </el-form-item>
        <el-form-item label="数据与记录风险" prop="dataRisk">
          <el-input v-model="form.dataRisk" autosize type="textarea" />
        </el-form-item>
        <el-form-item label="其他风险描述" prop="other">
          <el-input v-model="form.other" autosize type="textarea" />
        </el-form-item>
      </div>

      <el-divider content-position="left" >加工合同</el-divider>

      <FileUpload v-model="files" :readonly="readonly" @change="fileChange" />

      <el-divider content-position="left" >质量协议</el-divider>

      <FileUpload v-model="qualityFiles" :readonly="readonly" />

      <template v-if="showDoc" >
        <el-divider content-position="left" >解析结果 <span style="color: #F56C6C" >解析时间较长,请耐心等待(请尽量上传原件,提高解析识别率,缩短等待时间).所有解析结果需人工重新确认,此处解析只做辅助使用</span> </el-divider>

        <div class="table-wrapper" >
          <table class="base-table small-table" >
            <tr>
              <th ><i class="el-icon-circle-plus-outline" @click="addItem" /></th>
              <th >乙方建议</th>
            </tr>
            <tr v-for="(item,index) in partyBSuggestions" :key="index" >
              <td><i class="el-icon-remove-outline" @click="delItem(index)" /></td>
              <td>
                <el-input v-model="partyBSuggestions[index]" size="mini" />
              </td>
            </tr>
          </table>
        </div>
<!--        <el-row :gutter="20" >-->
<!--          <el-col :col="16" >-->
<!--            <div class="table-wrapper" >-->
<!--              <table class="base-table small-table" >-->
<!--                <tr>-->
<!--                  <th style="width: 120px" >标签</th>-->
<!--                  <th >解析内容</th>-->
<!--                </tr>-->
<!--                <tr v-for="(item,index) in dataArray" :key="index" >-->
<!--                  <td>{{item.label}}</td>-->
<!--                  <td>{{item.value}}</td>-->
<!--                </tr>-->
<!--              </table>-->
<!--            </div>-->
<!--          </el-col>-->
<!--          <el-col :col="8" >-->
<!--            -->
<!--          </el-col>-->
<!--        </el-row>-->
      </template>

    </el-form>
    <div v-if="!readonly" slot="footer" class="dialog-footer" style="margin-top: 20px">
      <el-button type="primary" @click="submitForm" size="mini" :loading="btnLoading" >确 定</el-button>
      <el-button @click="cancel" size="mini" >取 消</el-button>
    </div>
  </div>
</template>
<script>
import {addContract, getContract, updateContract} from "@/api/customer/contract";
import {getAiResolveContract} from "@/api/common/ai";

export default {
  name: "customerContractSave",
  props: {
    customerId: {
      type: Number,
      required: true,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      btnLoading: false,
      form: {},
      rules: {
        name: [
          {required: true,message: '请问输入文件名'},
        ]
      },
      deliveryTypeOptions: ['+','-','±'],
      files: [],
      qualityFiles: [],
      showDoc: false,
      dataArray: [],
      o : {
        "party_a": "甲方名称",
        "party_b": "乙方名称",
        "signed_date": "签订日期",
        "on_set_date": "起效日期",
        "expire_date": "到期日期",
        "payment_term": "账期描述",
        "financial_risk": "财务风险描述",
        "delivery_control": "交货数量控制条款",
        "delay_risk": "交货延误风险条款",
        "quality_responsibility": "质量责任协议",
        "quality_risk": "质量风险描述",
        "production_risk": "生产损耗与成本风险",
        "ip_risk": "知识产权与保密风险",
        "breach_liability": "违约责任条款",
        "data_risk": "数据与记录风险",
        "other_risks": "其他风险描述",
        "party_b_suggestions": "乙方建议",
      },
      partyBSuggestions: [],
    }
  },
  async created() {
  },
  methods: {
    async fileChange(files) {
      console.log(files)
      // for (const file of files) {
      //   await this.selectFile(file)
      // }
    },
    addItem() {
      this.partyBSuggestions.push('')
    },
    delItem(i) {
      this.partyBSuggestions.splice(i,1)
    },
    async selectFile(file) {
      this.loading = true
      if(file.url) {
        this.dataArray = []
        const res = await getAiResolveContract(file)
        if(res.code === 200 ) {
          const data = res.data
          if(data) {
            const o = data.extracted_data
            if(o) {
              for (const k of Object.keys(o)) {
                this.dataArray.push({
                  label: this.o[k],
                  key: k,
                  value: o[k]
                })
              }
            }
            this.form.name = file.name
            this.form.partyA = o.party_a
            this.form.partyB = o.party_b
            this.form.signedDate = o.signed_date
            this.form.onSetDate = o.on_set_date
            this.form.expireDate = o.expire_date
            this.form.paymentTerm = o.payment_term
            this.form.financialRisk = o.financial_risk
            this.form.deliveryControl = o.delivery_control
            this.form.delayRisk = o.delay_risk
            this.form.qualityResponsibility = o.quality_responsibility
            this.form.qualityRisk = o.quality_risk
            this.form.productionRisk = o.production_risk
            this.form.ipRisk = o.ip_risk
            this.form.breachLiability = o.breach_liability
            this.form.dataRisk = o.data_risk
            this.form.other = o.other_risks
            this.form.text = JSON.stringify(data)
            this.showDoc = true
          }
          if(data.advice) {
            this.partyBSuggestions = data.advice.party_b_suggestions
          }
        }
      }
      this.loading = false
    },
    cancel() {
      this.$parent.$parent.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        id: null,
        customerId: null,
        code: null,
        name: null,
        periodType: null,
        periodDays: null,
        nums: null,
        deliveryType: null,
        overdue: null,
        qualityResponsibility: null,
        other: null,
        signedDate: null,
        onSetDate: null,
        expireDate: null,
        locationCode: null,
        remark: null,
        partyA: null,
        partyB: null,
        financialRisk: null,
        deliveryControl: null,
        delayRisk: null,
        qualityRisk: null,
        productionRisk: null,
        ipRisk: null,
        breachLiability: null,
        dataRisk: null,
        text: null,
      };
      this.resetForm("form")
      this.files = []
      this.qualityFiles = []
      this.dataArray = []
      this.partyBSuggestions = []
      this.showDoc = true
    },
    async init(id) {
      this.loading = true
      const res = await getContract(id)
      const form = res.data

      if(form.files) {
        this.files = JSON.parse(form.files)
      }

      if(form.qualityFiles) {
        this.qualityFiles = JSON.parse(form.qualityFiles)
      }

      if(form.partyBSuggestions) {
        this.partyBSuggestions = JSON.parse(form.partyBSuggestions)
      }

      this.form = form
      this.loading = false
    },
    async submitForm() {
      await this.$refs["form"].validate()
      let form = Object.assign({},this.form)

      form.files = JSON.stringify(this.files)
      form.qualityFiles = JSON.stringify(this.qualityFiles)
      form.partyBSuggestions = JSON.stringify(this.partyBSuggestions)

      if (form.id != null) {
        try {
          this.btnLoading = true
          await updateContract(form)
          this.btnLoading = false
          this.msgSuccess("修改成功")
          this.$parent.$parent.open = false
          await this.$parent.$parent.getList()
        } catch (e) {
          this.btnLoading = false
        }
      } else {
        form.customerId = this.customerId
        try {
          this.btnLoading = true
          await addContract(form)
          this.btnLoading = false
          this.msgSuccess("新增成功")
          this.$parent.$parent.open = false;
          await this.$parent.$parent.getList()
        } catch (e) {
          this.btnLoading = false
        }
      }
    },
  }
}
</script>
<style scoped lang="scss">

</style>
