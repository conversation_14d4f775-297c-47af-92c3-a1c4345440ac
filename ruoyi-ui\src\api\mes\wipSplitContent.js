import request from '@/utils/request'

// 查询分批作业列表
export function listWipSplitContent(query) {
  return request({
    url: '/mes/wipSplitContent/list',
    method: 'get',
    params: query
  })
}

// 查询分批作业详细
export function getWipSplitContent(fromlotno) {
  return request({
    url: '/mes/wipSplitContent/' + fromlotno,
    method: 'get'
  })
}

// 新增分批作业
export function addWipSplitContent(data) {
  return request({
    url: '/mes/wipSplitContent',
    method: 'post',
    data: data
  })
}

// 修改分批作业
export function updateWipSplitContent(data) {
  return request({
    url: '/mes/wipSplitContent',
    method: 'put',
    data: data
  })
}

// 删除分批作业
export function delWipSplitContent(fromlotno) {
  return request({
    url: '/mes/wipSplitContent/' + fromlotno,
    method: 'delete'
  })
}

// 导出分批作业
export function exportWipSplitContent(query) {
  return request({
    url: '/mes/wipSplitContent/export',
    method: 'get',
    params: query
  })
}