import request from '@/utils/request'

// 查询缺陷项目维护列表
export function listDefectiveItems(query) {
  return request({
    url: '/qc/defectiveItems/list',
    method: 'get',
    params: query
  })
}

// 查询缺陷项目维护详细
export function getDefectiveItems(id) {
  return request({
    url: '/qc/defectiveItems/' + id,
    method: 'get'
  })
}

// 新增缺陷项目维护
export function addDefectiveItems(data) {
  return request({
    url: '/qc/defectiveItems',
    method: 'post',
    data: data
  })
}

// 修改缺陷项目维护
export function updateDefectiveItems(data) {
  return request({
    url: '/qc/defectiveItems',
    method: 'put',
    data: data
  })
}

// 删除缺陷项目维护
export function delDefectiveItems(id) {
  return request({
    url: '/qc/defectiveItems/' + id,
    method: 'delete'
  })
}

// 导出缺陷项目维护
export function exportDefectiveItems(query) {
  return request({
    url: '/qc/defectiveItems/export',
    method: 'get',
    params: query
  })
}
