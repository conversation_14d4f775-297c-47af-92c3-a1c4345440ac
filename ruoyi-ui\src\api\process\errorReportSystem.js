import request from '@/utils/request'

// 查询异常报告单（体系不符合）列表
export function listErrorReportSystem(query) {
  return request({
    url: '/process/errorReportSystem/list',
    method: 'get',
    params: query
  })
}
export function logErrorReportSystem(query) {
  return request({
    url: '/process/errorReportSystem/log',
    method: 'get',
    params: query
  })
}
export function listErrorReportSystemAudit(query) {
  return request({
    url: '/process/errorReportSystem/audit',
    method: 'get',
    params: query
  })
}
export function listErrorReportSystemHistory(query) {
  return request({
    url: '/process/errorReportSystem/history',
    method: 'get',
    params: query
  })
}
// 查询异常报告单（体系不符合）详细
export function getErrorReportSystem(id) {
  return request({
    url: '/process/errorReportSystem/' + id,
    method: 'get'
  })
}
export function getErrorReportSystemItem(id) {
  return request({
    url: '/process/errorReportSystem/item/' + id,
    method: 'get'
  })
}

// 新增异常报告单（体系不符合）
export function addErrorReportSystem(data) {
  return request({
    url: '/process/errorReportSystem',
    method: 'post',
    data: data
  })
}
export function feedbackErrorReportSystem(data){
  return request({
    url: '/process/errorReportSystem/feedback',
    method: 'post',
    data: data
  })
}
export function resultsTrackingErrorReportSystem(data){
  return request({
    url: '/process/errorReportSystem/resultsTracking',
    method: 'post',
    data: data
  })
}

export function statusErrorReportSystem(data){
  return request({
    url: '/process/errorReportSystem/status',
    method: 'post',
    data: data
  })
}
