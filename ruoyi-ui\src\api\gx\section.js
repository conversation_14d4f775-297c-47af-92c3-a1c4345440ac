import request from '@/utils/request'

// 查询功效版块列表
export function listSection(query) {
  return request({
    url: '/gx/section/list',
    method: 'get',
    params: query
  })
}

// 查询功效版块详细
export function getSection(id) {
  return request({
    url: '/gx/section/' + id,
    method: 'get'
  })
}

// 新增功效版块
export function addSection(data) {
  return request({
    url: '/gx/section',
    method: 'post',
    data: data
  })
}

// 修改功效版块
export function updateSection(data) {
  return request({
    url: '/gx/section',
    method: 'put',
    data: data
  })
}

// 删除功效版块
export function delSection(id) {
  return request({
    url: '/gx/section/' + id,
    method: 'delete'
  })
}

// 导出功效版块
export function exportSection(query) {
  return request({
    url: '/gx/section/export',
    method: 'get',
    params: query
  })
}

export function allSection(query) {
  return request({
    url: '/gx/section/all',
    method: 'get',
    params: query
  })
}
