import request from '@/utils/request'

// 查询送检费用对账单列表
export function listReconciles(query) {
  return request({
    url: '/legal/reconciles/list',
    method: 'get',
    params: query
  })
}

export function unPayListReconciles(query) {
  return request({
    url: '/legal/reconciles/unPayList',
    method: 'get',
    params: query
  })
}

// 查询送检费用对账单详细
export function getReconciles(id) {
  return request({
    url: '/legal/reconciles/' + id,
    method: 'get'
  })
}

// 新增送检费用对账单
export function addReconciles(data) {
  return request({
    url: '/legal/reconciles',
    method: 'post',
    data: data
  })
}

// 修改送检费用对账单
export function updateReconciles(data) {
  return request({
    url: '/legal/reconciles',
    method: 'put',
    data: data
  })
}

// 删除送检费用对账单
export function delReconciles(id) {
  return request({
    url: '/legal/reconciles/' + id,
    method: 'delete'
  })
}

// 导出送检费用对账单
export function exportReconciles(query) {
  return request({
    url: '/legal/reconciles/export',
    method: 'get',
    params: query
  })
}

export function batchAddReconciles(data) {
  return request({
    url: '/legal/reconciles/batchAdd',
    method: 'post',
    data: data
  })
}

export function revokeReconciles(id) {
  return request({
    url: '/legal/reconciles/revoke/' + id,
    method: 'put',
  })
}
