import request from '@/utils/request'

// 查询文件评审列表
export function listReview(query) {
  return request({
    url: '/filemanage/review/list',
    method: 'get',
    params: query
  })
}

export function reviewList(query) {
  return request({
    url: '/filemanage/review/reviewList',
    method: 'get',
    params: query
  })
}

export function exportReview(query) {
  return request({
    url: '/filemanage/review/exportReview',
    method: 'get',
    params: query
  })
}


// 查询文件评审详细
export function getReview(id) {
  return request({
    url: '/filemanage/review/' + id,
    method: 'get'
  })
}

// 新增文件评审
export function addReview(data) {
  return request({
    url: '/filemanage/review',
    method: 'post',
    data: data
  })
}

// 修改文件评审
export function updateReview(data) {
  return request({
    url: '/filemanage/review',
    method: 'put',
    data: data
  })
}

// 删除文件评审
export function delReview(id) {
  return request({
    url: '/filemanage/review/' + id,
    method: 'delete'
  })
}

// 导出文件评审
export function exportReview2(query) {
  return request({
    url: '/filemanage/review/export',
    method: 'get',
    params: query
  })
}
