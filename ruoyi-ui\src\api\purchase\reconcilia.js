import request from '@/utils/request'

// 查询采购对账列表
export function listReconcilia(query) {
  return request({
    url: '/purchase/reconcilia/list',
    method: 'get',
    params: query
  })
}

// 查询采购对账详细
export function getReconcilia(id) {
  return request({
    url: '/purchase/reconcilia/' + id,
    method: 'get'
  })
}

export function getReconciliaGoods(id) {
  return request({
    url: '/purchase/reconcilia/goods/' + id,
    method: 'get'
  })
}

export function getOrderErp(id) {
  return request({
    url: '/purchase/reconcilia/getOrderErp/' + id,
    method: 'get'
  })
}

export function listAuditPurchaseData(data) {
  return request({
    url: '/purchase/reconcilia/audit',
    method: 'post',
    data: data
  })
}


export function submitAudit(data) {
  return request({
    url: '/purchase/reconcilia/submitAudit',
    method: 'put',
    data: data
  })
}

export function cancelAudit(data) {
  return request({
    url: '/purchase/reconcilia/cancelAudit',
    method: 'put',
    data: data
  })
}

export function revokeAudit(data) {
  return request({
    url: '/purchase/reconcilia/revokeAudit',
    method: 'put',
    data: data
  })
}

// 新增采购对账
export function addReconcilia(data) {
  return request({
    url: '/purchase/reconcilia',
    method: 'post',
    data: data
  })
}

// 修改采购对账
export function updateReconcilia(data) {
  return request({
    url: '/purchase/reconcilia',
    method: 'put',
    data: data
  })
}

// 修改采购对账-上传附件
export function updateReconciliaFiles(data) {
  return request({
    url: '/purchase/reconcilia/editFiles',
    method: 'put',
    data: data
  })
}

// 删除采购对账
export function delReconcilia(id) {
  return request({
    url: '/purchase/reconcilia/' + id,
    method: 'delete'
  })
}

// 导出采购对账
export function exportReconcilia(query) {
  return request({
    url: '/purchase/reconcilia/export',
    method: 'get',
    params: query
  })
}



export function queryPurchaseData(data) {
  return request({
    url: '/purchase/reconcilia/queryPurchaseData',
    method: 'post',
    data
  })
}


export function queryReturnData(data) {
  return request({
    url: '/purchase/reconcilia/queryReturnData',
    method: 'post',
    data
  })
}

export function queryProductionDefectData(data) {
  return request({
    url: '/purchase/reconcilia/queryProductionDefectData',
    method: 'post',
    data
  })
}
