import request from '@/utils/request'

// 查询考勤排班方案列表
export function listSchedulingPlan(query) {
  return request({
    url: '/hr/schedulingPlan/list',
    method: 'get',
    params: query
  })
}

// 查询考勤排班方案详细
export function getSchedulingPlan(id) {
  return request({
    url: '/hr/schedulingPlan/' + id,
    method: 'get'
  })
}

// 新增考勤排班方案
export function addSchedulingPlan(data) {
  return request({
    url: '/hr/schedulingPlan',
    method: 'post',
    data: data
  })
}

// 修改考勤排班方案
export function updateSchedulingPlan(data) {
  return request({
    url: '/hr/schedulingPlan',
    method: 'put',
    data: data
  })
}

// 删除考勤排班方案
export function delSchedulingPlan(id) {
  return request({
    url: '/hr/schedulingPlan/' + id,
    method: 'delete'
  })
}

// 导出考勤排班方案
export function exportSchedulingPlan(query) {
  return request({
    url: '/hr/schedulingPlan/export',
    method: 'get',
    params: query
  })
}
export function getSchedulingPlanAll() {
  return request({
    url: '/hr/schedulingPlan/all',
    method: 'get'
  })
}
export function getSchedulingPlanItemAll() {
  return request({
    url: '/hr/schedulingPlan/itemAll',
    method: 'get'
  })
}
