import request from '@/utils/request'

// 查询劳务人员工时列表
export function listLaborUserHours(query) {
  return request({
    url: '/production/laborUserHours/list',
    method: 'get',
    params: query
  })
}

// 查询劳务人员工时详细
export function getLaborUserHours(id) {
  return request({
    url: '/production/laborUserHours/' + id,
    method: 'get'
  })
}

// 新增劳务人员工时
export function addLaborUserHours(data) {
  return request({
    url: '/production/laborUserHours',
    method: 'post',
    data: data
  })
}

// 修改劳务人员工时
export function updateLaborUserHours(data) {
  return request({
    url: '/production/laborUserHours',
    method: 'put',
    data: data
  })
}

export function confirmLaborUserHours(data) {
  return request({
    url: '/production/laborUserHours/confirmHours',
    method: 'put',
    data: data
  })
}

// 删除劳务人员工时
export function delLaborUserHours(id) {
  return request({
    url: '/production/laborUserHours/' + id,
    method: 'delete'
  })
}

// 导出劳务人员工时
export function exportLaborUserHours(query) {
  return request({
    url: '/production/laborUserHours/export',
    method: 'get',
    params: query
  })
}
