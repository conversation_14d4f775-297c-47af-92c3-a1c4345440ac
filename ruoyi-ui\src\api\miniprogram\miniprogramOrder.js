import request from '@/utils/request'

// 查询客户订单列表
export function listMiniprogramOrder(query) {
  return request({
    url: '/miniprogram/miniprogramOrder/list',
    method: 'get',
    params: query
  })
}

// 查询客户订单商品列表
export function goodsItemList(query) {
  return request({
    url: '/miniprogram/miniprogramOrder/goodsItemList',
    method: 'get',
    params: query
  })
}

// 查询客户订单详细
export function getMiniprogramOrder(id) {
  return request({
    url: '/miniprogram/miniprogramOrder/' + id,
    method: 'get'
  })
}

// 新增客户订单
export function addMiniprogramOrder(data) {
  return request({
    url: '/miniprogram/miniprogramOrder',
    method: 'post',
    data: data
  })
}
// 新增客户订单
export function addExhibitsOrder(data) {
  return request({
    url: '/miniprogram/miniprogramOrder/addExhibitsOrder',
    method: 'post',
    data: data
  })
}

// 修改客户订单
export function updateMiniprogramOrder(data) {
  return request({
    url: '/miniprogram/miniprogramOrder',
    method: 'put',
    data: data
  })
}

// 发货物流
export function sendLogisticsInfo(data) {
  return request({
    url: '/miniprogram/miniprogramOrder/sendLogisticsInfo',
    method: 'put',
    data: data
  })
}

// 删除客户订单
export function delMiniprogramOrder(id) {
  return request({
    url: '/miniprogram/miniprogramOrder/' + id,
    method: 'delete'
  })
}

// 导出客户订单
export function exportMiniprogramOrder(query) {
  return request({
    url: '/miniprogram/miniprogramOrder/export',
    method: 'get',
    params: query
  })
}
