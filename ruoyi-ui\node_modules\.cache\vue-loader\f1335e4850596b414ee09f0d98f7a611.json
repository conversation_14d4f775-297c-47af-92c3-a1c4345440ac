{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\project\\project\\materialTable.vue?vue&type=style&index=0&id=47b1da5e&lang=scss&scoped=true", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\project\\project\\materialTable.vue", "mtime": 1753954679647}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1744596528942}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1744596530059}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1744596529996}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1744596552583}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnJvdy13cmFwcGVyIHsNCiAgZGlzcGxheTogZmxleDsNCg0KICAubGFiZWwgew0KICAgIHdpZHRoOiAxNTBweDsNCiAgICBmb250LXNpemU6IDEzcHg7DQogICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgfQ0KDQogIC5jb250ZW50IHsNCg0KICB9DQp9DQoudGFibGUtd3JhcHBlciB7DQoNCiAgLmJhc2UtdGFibGUgew0KICAgIHRoZWFkIHsNCiAgICAgIHBvc2l0aW9uOiBzdGlja3k7DQogICAgICB0b3A6IDA7DQogICAgICB6LWluZGV4OiAzOw0KDQogICAgICAubnRoMCB7DQogICAgICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjQ4LDI0OCwyNDksMSk7DQogICAgICB9DQoNCiAgICB9DQoNCiAgICB0Ym9keSB7DQogICAgICAubnRoMCB7DQogICAgICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LDI1NSwyNTUsMSk7DQogICAgICB9DQogICAgfQ0KDQogICAgLm50aDAgew0KICAgICAgcG9zaXRpb246IHN0aWNreTsNCiAgICAgIGxlZnQ6IDA7DQogICAgICB6LWluZGV4OiAxOw0KICAgIH0NCg0KICB9DQoNCn0NCg=="}, {"version": 3, "sources": ["materialTable.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAw/BA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA", "file": "materialTable.vue", "sourceRoot": "src/views/project/project", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-row :gutter=\"20\">\r\n      <el-tooltip class=\"item\" content=\"自定义列\" effect=\"dark\">\r\n        <el-button\r\n          circle\r\n          icon=\"el-icon-menu\"\r\n          size=\"mini\"\r\n          @click=\"showCol\"/>\r\n      </el-tooltip>\r\n    </el-row>\r\n\r\n<!--    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"mini\" label-width=\"120px\">-->\r\n<!--      <el-row>-->\r\n<!--        <el-col :span=\"8\">-->\r\n<!--          <el-form-item label=\"品名\" prop=\"name\">-->\r\n<!--            <el-input-->\r\n<!--              v-model=\"queryParams.name\"-->\r\n<!--              clearable-->\r\n<!--            />-->\r\n<!--          </el-form-item>-->\r\n<!--        </el-col>-->\r\n<!--        <el-col :span=\"8\">-->\r\n<!--          <el-form-item>-->\r\n<!--            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>-->\r\n<!--            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>-->\r\n<!--          </el-form-item>-->\r\n<!--        </el-col>-->\r\n<!--      </el-row>-->\r\n<!--    </el-form>-->\r\n\r\n    <div class=\"table-wrapper small-table\">\r\n      <table :class=\"readonly?'mask':''\" class=\"base-table bc-table\">\r\n        <thead>\r\n        <tr>\r\n          <th v-if=\"!readonly\" :rowspan=\"2\" class=\"nth0\" style=\"width: 80px\" >\r\n            <el-tooltip content=\"普通添加\" >\r\n              <i class=\"el-icon-circle-plus-outline\" @click=\"addItem\" />\r\n            </el-tooltip>\r\n<!--            <el-tooltip content=\"通过erp编码添加(单个物料)\" >-->\r\n<!--              <i class=\"el-icon-circle-plus-outline\" @click=\"showErp\" />-->\r\n<!--            </el-tooltip>-->\r\n            <el-tooltip content=\"通过bom结构添加\" >\r\n              <i class=\"el-icon-circle-plus-outline\" @click=\"showBom\" />\r\n            </el-tooltip>\r\n            <el-tooltip content=\"选择包材库\" >\r\n              <i class=\"el-icon-circle-plus-outline\" @click=\"selectBc\" />\r\n            </el-tooltip>\r\n          </th>\r\n          <th v-if=\"columnsFlag('项目包材编码')\" :rowspan=\"2\" style=\"width: 120px\" >\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            项目包材编码\r\n          </th>\r\n          <th v-if=\"columnsFlag('开发类型')\" :rowspan=\"2\" style=\"width: 120px\" >\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            开发类型\r\n          </th>\r\n          <th v-if=\"columnsFlag('名称')\" :rowspan=\"2\" style=\"width: 500px\" >\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            名称\r\n          </th>\r\n          <th v-if=\"columnsFlag('类别')\" :rowspan=\"2\" style=\"width: 150px\" >\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            类别\r\n          </th>\r\n          <th v-if=\"columnsFlag('物料属性')\" :rowspan=\"2\" style=\"width: 150px\" >\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            物料属性\r\n          </th>\r\n          <th v-if=\"columnsFlag('包装材料')\" :rowspan=\"2\" style=\"width: 150px\" >包装材料</th>\r\n<!--          <th v-if=\"columnsFlag('包材库编码')\" :rowspan=\"2\" style=\"width: 150px\" >包材库编码</th>-->\r\n          <th v-if=\"columnsFlag('包材ERP编码')\" :rowspan=\"2\" style=\"width: 150px\" >包材ERP编码</th>\r\n          <th v-if=\"columnsFlag('规格')\" :rowspan=\"2\" style=\"width: 150px\" >规格</th>\r\n          <th v-if=\"columnsFlag('尺寸')\" :rowspan=\"2\" style=\"width: 250px\" >尺寸</th>\r\n          <th v-if=\"columnsFlag('型号')\" :rowspan=\"2\" style=\"width: 150px\" >型号</th>\r\n          <th v-if=\"columnsFlag('供应商')\" :rowspan=\"2\" style=\"width: 150px\" >供应商</th>\r\n          <th v-if=\"columnsFlag('图片')\" :rowspan=\"2\" style=\"width: 300px\" >图片</th>\r\n          <th v-if=\"columnsFlag('COA/SPEC')\" :rowspan=\"2\" style=\"width: 300px\" >COA/SPEC</th>\r\n          <th v-if=\"columnsFlag('备注') && devStatus === '0'\" :rowspan=\"2\" style=\"width: 300px\" >备注</th>\r\n          <th v-if=\"devStatus === '1'\" :rowspan=\"2\" style=\"width: 120px\" >报价日期</th>\r\n          <th v-if=\"devStatus === '1'\" :rowspan=\"2\" style=\"width: 120px\" >阶段</th>\r\n          <th v-if=\"devStatus === '1'\" :rowspan=\"2\" class=\"nth0\" style=\"width: 120px\" >价格</th>\r\n          <th v-for=\"log in logArray\" :key=\"log.value\" :colspan=\"log.array.length\" :style=\"{width: log.array.length * 80 + 'px'}\">{{log.label}}</th>\r\n        </tr>\r\n        <tr>\r\n          <template v-for=\"log in logArray\" >\r\n            <th v-for=\"(l,index) in log.array\" :key=\"log.value  + '_' + index \" style=\"width: 80px\" >{{l.projectItemOrderCode.substring(l.projectItemOrderCode.indexOf(\"-\")+1)}}</th>\r\n          </template>\r\n        </tr>\r\n        </thead>\r\n        <tbody>\r\n        <tr v-for=\"(item,index) in materialList.slice((currentPage-1) * pageSize, currentPage * pageSize)\" :key=\"index\">\r\n          <td v-if=\"!readonly\" class=\"nth0\" >\r\n            <i class=\"el-icon-remove-outline\" @click=\"delItem(item)\" ></i>\r\n          </td>\r\n          <td v-if=\"columnsFlag('项目包材编码')\" >{{item.code}}</td>\r\n          <td v-if=\"columnsFlag('开发类型')\" >\r\n            <el-select v-model=\"item.opType\" size=\"mini\" >\r\n              <el-option\r\n                v-for=\"item in typeOptions\"\r\n                :key=\"item.value\"\r\n                :disabled=\"item.value==2\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              />\r\n            </el-select>\r\n          </td>\r\n          <td v-if=\"columnsFlag('名称')\" >\r\n            <el-input v-model.trim=\"item.name\" size=\"mini\" />\r\n          </td>\r\n          <td v-if=\"columnsFlag('类别')\" >\r\n            <span style=\"color: #00afff;cursor: pointer\" @click=\"showType(item)\" >{{materialText(item)}}</span>\r\n<!--            <el-select v-model=\"item.type\" size=\"mini\" >-->\r\n<!--              <el-option-->\r\n<!--                v-for=\"dict in bcTypeOptions\"-->\r\n<!--                :key=\"dict.dictValue\"-->\r\n<!--                :label=\"dict.dictLabel\"-->\r\n<!--                :value=\"dict.dictValue\"-->\r\n<!--              />-->\r\n<!--            </el-select>-->\r\n<!--            <template v-if=\"item.type === '0'\" >-->\r\n<!--              <el-select v-model=\"item.zrqType\" placeholder=\"主容器\" size=\"mini\" >-->\r\n<!--                <el-option-->\r\n<!--                  v-for=\"dict in zrqOptions\"-->\r\n<!--                  :key=\"dict.dictValue\"-->\r\n<!--                  :label=\"dict.dictLabel\"-->\r\n<!--                  :value=\"dict.dictValue\"-->\r\n<!--                />-->\r\n<!--              </el-select>-->\r\n<!--              <el-select v-model=\"item.allocator\" clearable multiple placeholder=\"分配器\" size=\"mini\" >-->\r\n<!--                <el-option-->\r\n<!--                  v-for=\"dict in allocatorOptions\"-->\r\n<!--                  :key=\"dict.dictValue\"-->\r\n<!--                  :label=\"dict.dictLabel\"-->\r\n<!--                  :value=\"dict.dictValue\"-->\r\n<!--                />-->\r\n<!--              </el-select>-->\r\n<!--            </template>-->\r\n          </td>\r\n          <td v-if=\"columnsFlag('物料属性')\" >\r\n            <el-select v-model=\"item.mb008\" size=\"mini\" >\r\n              <el-option\r\n                  v-for=\"d in attrOptions\"\r\n                  :key=\"d.dictValue\"\r\n                  :label=\"d.dictLabel\"\r\n                  :value=\"d.dictValue\"\r\n              />\r\n            </el-select>\r\n          </td>\r\n          <td v-if=\"columnsFlag('包装材料')\" >\r\n            <el-select v-model=\"item.materialType\" size=\"mini\" >\r\n              <el-option\r\n                v-for=\"dict in materialTypeOptions\"\r\n                :key=\"dict.dictValue\"\r\n                :label=\"dict.dictLabel\"\r\n                :value=\"dict.dictValue\"\r\n              />\r\n            </el-select>\r\n          </td>\r\n<!--          <td v-if=\"columnsFlag('包材库编码')\" >{{item.bcCode}}</td>-->\r\n          <td v-if=\"columnsFlag('包材ERP编码')\" >\r\n            <el-input v-model.trim=\"item.erpCode\" size=\"mini\" />\r\n          </td>\r\n          <td v-if=\"columnsFlag('规格')\" ><el-input v-model=\"item.spec\" size=\"mini\" /></td>\r\n          <td v-if=\"columnsFlag('尺寸')\" >\r\n            <div style=\"display: flex;align-items: center;\" >\r\n              <el-input v-model=\"item.length\" placeholder=\"长\" size=\"mini\" style=\"width: 80px\" type=\"number\" />\r\n              *\r\n              <el-input v-model=\"item.width\" placeholder=\"宽\" size=\"mini\" style=\"width: 80px\" type=\"number\" />\r\n              *\r\n              <el-input v-model=\"item.height\" placeholder=\"高\" size=\"mini\" style=\"width: 80px\" type=\"number\" />\r\n            </div>\r\n          </td>\r\n          <td v-if=\"columnsFlag('型号')\" ><el-input v-model=\"item.model\" size=\"mini\" /></td>\r\n          <td v-if=\"columnsFlag('供应商')\" >\r\n            <el-select v-if=\"devStatus === '1'\" v-model=\"item.supplierId\" clearable  filterable size=\"mini\" >\r\n              <el-option\r\n                v-for=\"item in supplierList\"\r\n                :key=\"item.id\"\r\n                :label=\"item.name\"\r\n                :value=\"item.id\"\r\n              />\r\n            </el-select>\r\n          </td>\r\n          <td v-if=\"columnsFlag('图片')\" ><ImageUpload v-model=\"item.imgs\" :is-show-tip=\"false\" /></td>\r\n          <td v-if=\"columnsFlag('COA/SPEC')\" ><FileUpload :id=\"item.id?item.id:item.key\" v-model=\"item.files\" :is-show-tip=\"false\" :view-type=\"1\" @change=\"fileChange\" /></td>\r\n          <td v-if=\"columnsFlag('备注') && devStatus === '0'\" >\r\n            <el-input v-model=\"item.remark\" autosize placeholder=\"客户指定供应商信息、联系方式,以及其它特殊要求\" size=\"mini\" type=\"textarea\" />\r\n          </td>\r\n          <td v-if=\"devStatus === '1'\" >{{item.priceDate}}</td>\r\n          <td v-if=\"devStatus === '1'\" >{{valueToLabel(stageOptions,item.stage)}}</td>\r\n          <td v-if=\"devStatus === '1'\" class=\"nth0\" >\r\n            <div v-if=\"item.id\" style=\"color: #00afff;cursor: pointer\" @click=\"handlePrice(item)\"  >\r\n                <span v-if=\"item.price\">\r\n                  {{item.price}}\r\n                </span>\r\n              <el-tooltip v-else content=\"阶梯价\" placement=\"top\">\r\n                <span class=\"el-icon-edit\" />\r\n              </el-tooltip>\r\n            </div>\r\n          </td>\r\n          <template v-for=\"log in logArray\" >\r\n            <td v-for=\"(l,z) in log.array\" :key=\"log.value  + '_' + z \" style=\"width: 80px\" >\r\n              <span :class=\"tdClass(l.projectBcIds,item.id)\"></span>\r\n            </td>\r\n          </template>\r\n        </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <el-pagination\r\n      layout=\"prev, pager, next\"\r\n      @current-change=\"handleCurrentChange\"\r\n      :current-page=\"currentPage\"\r\n      :page-size=\"pageSize\"\r\n      :total=\"materialList.length\">\r\n    </el-pagination>\r\n\r\n    <el-dialog :visible.sync=\"bomOpen\" append-to-body title=\"选择bom\" width=\"1200px\">\r\n      <el-input v-model=\"erpCode\" size=\"mini\" >\r\n        <template slot=\"append\" >\r\n          <el-button :loading=\"btnLoading\" icon=\"el-icon-search\" @click=\"getErpInfo\" />\r\n        </template>\r\n      </el-input>\r\n      <el-tree\r\n        ref=\"bomTree\"\r\n        :data=\"bomTree\"\r\n        check-strictly\r\n        default-expand-all\r\n        node-key=\"id\"\r\n        show-checkbox\r\n      >\r\n        <span slot-scope=\"{ node, data }\" class=\"custom-tree-node\" >\r\n          <span>{{ data.mb002 }}</span>\r\n          <span>{{ data.md003 }}</span>\r\n        </span>\r\n      </el-tree>\r\n      <div class=\"dialog-footer\" style=\"margin-top: 20px\">\r\n        <el-button :loading=\"btnLoading\" size=\"mini\" type=\"primary\" @click=\"confirmBom\" >确 定</el-button>\r\n        <el-button size=\"mini\" @click=\"bomOpen = false\" >取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog :visible.sync=\"bcOpen\" append-to-body title=\"选择包材\" width=\"1200px\">\r\n      <MaterialGoodsSelectTable @change=\"bcChange\" />\r\n      <div class=\"dialog-footer\" style=\"margin-top: 20px\">\r\n        <el-button :loading=\"btnLoading\" size=\"mini\" type=\"primary\" @click=\"confirmBc\" >确 定</el-button>\r\n        <el-button size=\"mini\" @click=\"bcOpen = false\" >取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog :close-on-click-modal=\"false\" :fullscreen=\"fullscreenFlag\" :visible.sync=\"open\" append-to-body width=\"1200px\">\r\n      <div slot=\"title\" class=\"dialog-title\">\r\n        阶梯价\r\n        <el-button :icon=\"fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'\" type=\"text\"\r\n                   @click=\"fullscreenFlag = !fullscreenFlag\"/>\r\n      </div>\r\n      <BcPriceTable\r\n        v-if=\"currentRow.id\"\r\n        :config-array=\"configArray\"\r\n        :files=\"files\"\r\n        :form=\"currentRow\"\r\n        :price-array=\"priceArray\"\r\n        :project-bc-id=\"currentRow.id\"\r\n        @saveSuccess=\"saveSuccess\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <CustomCols\r\n      ref=\"customCols\"\r\n      :default-columns=\"columns\"\r\n      name=\"projectBc\"\r\n      @success=\"colSuccess\"\r\n    />\r\n\r\n    <el-dialog :visible.sync=\"typeOpen\" append-to-body title=\"选择类别\" width=\"1200px\">\r\n      <el-form ref=\"form\" :model=\"currentRow\" :rules=\"rules\" label-width=\"120px\" size=\"mini\" >\r\n        <el-form-item prop=\"mb005\">\r\n          <template #label>\r\n            物料类型\r\n          </template>\r\n          <el-radio-group v-model=\"currentRow.mb005\" style=\"width: 90%;\" @input=\"mb005Change\" >\r\n            <el-row :gutter=\"20\" >\r\n              <el-col v-for=\"dict in mb005Options\" :key=\"dict.value\" :span=\"3\" >\r\n                <el-radio :label=\"dict.value\" style=\"padding-bottom: 10px\" >\r\n                  {{dict.label}}\r\n                </el-radio>\r\n              </el-col>\r\n            </el-row>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"currentRow.mb005 === '104'\" prop=\"type\">\r\n          <template #label>\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            包材类别\r\n          </template>\r\n          <el-radio-group v-model=\"currentRow.type\" @input=\"typeChange\" >\r\n            <el-row :gutter=\"20\" >\r\n              <el-col v-for=\"dict in bcTypeOptions\" :key=\"dict.dictValue\" :span=\"3\" >\r\n                <el-radio :label=\"dict.dictValue\" style=\"padding-bottom: 10px\">\r\n                  {{dict.dictLabel}}\r\n                </el-radio>\r\n              </el-col>\r\n            </el-row>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"currentRow.mb005 === '104' && currentRow.type === '0'\" >\r\n          <template #label>\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            主容器-类别\r\n          </template>\r\n          <el-radio-group v-model=\"currentRow.zrqType\" >\r\n            <el-row :gutter=\"20\" >\r\n              <el-col v-for=\"dict in zrqOptions\" :key=\"dict.dictValue\" :span=\"3\" >\r\n                <el-radio :label=\"dict.dictValue\" style=\"padding-bottom: 10px\">\r\n                  {{dict.dictLabel}}\r\n                </el-radio>\r\n              </el-col>\r\n            </el-row>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"currentRow.mb005 === '104' && currentRow.allocator && currentRow.type === '0'\" label=\"分配器\" >\r\n          <el-checkbox-group v-model=\"currentRow.allocator\">\r\n            <el-row :gutter=\"20\" >\r\n              <el-col v-for=\"dict in allocatorOptions\" :key=\"dict.dictValue\" :span=\"3\" >\r\n                <el-checkbox\r\n                  :label=\"dict.dictValue\">\r\n                  {{dict.dictLabel}}\r\n                </el-checkbox>\r\n              </el-col>\r\n            </el-row>\r\n          </el-checkbox-group>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div class=\"dialog-footer\" style=\"margin-top: 20px\">\r\n        <el-button :loading=\"btnLoading\" size=\"mini\" type=\"primary\" @click=\"confirmMaterial\" >确 定</el-button>\r\n        <el-button size=\"mini\" @click=\"typeOpen = false\" >取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog :visible.sync=\"treeOpen\" append-to-body width=\"1200px\">\r\n      <template #title>\r\n        选择类别\r\n        <el-button\r\n          icon=\"el-icon-question\"\r\n          size=\"mini\"\r\n          type=\"text\"\r\n          @click=\"openDoc\"\r\n        >包材分类</el-button>\r\n      </template>\r\n\r\n      <el-radio-group v-model=\"currentRow.mb005\" style=\"width: 90%;\" @input=\"mb005Change\" >\r\n        <el-row :gutter=\"20\" >\r\n          <el-col v-for=\"dict in mb005Options.filter(i=>i.value !== '104')\" :key=\"dict.value\" :span=\"3\" >\r\n            <el-radio :label=\"dict.value\" style=\"font-size: 18px;font-weight: 700;margin-bottom: 10px\" >\r\n              {{dict.label}}\r\n            </el-radio>\r\n          </el-col>\r\n        </el-row>\r\n      </el-radio-group>\r\n      <div style=\"display: flex;margin-bottom: 10px\" >\r\n        <div v-for=\"dict in mb005Options.filter(i=>i.value === '104')\" :key=\"dict.value\" class=\"label\"  style=\"font-size: 15px;font-weight: 700;width: 100px\" >\r\n          {{dict.label}}\r\n        </div>\r\n        <div >\r\n          <div>\r\n            <div class=\"row-wrapper\">\r\n              <div class=\"label\" >主包材类别</div>\r\n              <div class=\"content\">\r\n                <el-radio-group v-model=\"currentRow.zrqType\" @input=\"zrqTypeChange\" >\r\n                  <el-row :gutter=\"20\" >\r\n                    <el-col v-for=\"dict in zrqOptions\" :key=\"dict.dictValue\" :span=\"4\" >\r\n                      <el-radio :label=\"dict.dictValue\" style=\"padding-bottom: 10px\">\r\n                        {{dict.dictLabel}}\r\n                      </el-radio>\r\n                    </el-col>\r\n                  </el-row>\r\n                </el-radio-group>\r\n              </div>\r\n            </div>\r\n            <div class=\"row-wrapper\">\r\n              <div class=\"label\">分配器</div>\r\n              <div class=\"content\">\r\n                <el-checkbox-group v-model=\"currentRow.allocator\" @input=\"allocatorChange\" >\r\n                  <el-row :gutter=\"20\" >\r\n                    <el-col v-for=\"dict in allocatorOptions\" :key=\"dict.dictValue\" :span=\"4\" >\r\n                      <el-checkbox\r\n                        :label=\"dict.dictValue\">\r\n                        {{dict.dictLabel}}\r\n                      </el-checkbox>\r\n                    </el-col>\r\n                  </el-row>\r\n                </el-checkbox-group>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div style=\"margin-top: 10px\">\r\n            <el-radio-group v-model=\"currentRow.type\" @input=\"typeChange\" >\r\n              <el-row :gutter=\"20\" >\r\n                <el-col v-for=\"dict in bcTypeOptions.filter(i=>i.dictValue !== '0')\" :key=\"dict.dictValue\" :span=\"4\" >\r\n                  <el-radio :label=\"dict.dictValue\" style=\"padding-bottom: 10px\">\r\n                    {{dict.dictLabel}}\r\n                    <el-tooltip v-if=\"dict.dictLabel === '辅助工具'\" content=\"(例如:粉扑、粉刷、勺子等)\" >\r\n                      <i class=\"el-icon-question\" />\r\n                    </el-tooltip>\r\n                  </el-radio>\r\n                </el-col>\r\n              </el-row>\r\n            </el-radio-group>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"dialog-footer\" style=\"margin-top: 20px\">\r\n        <el-button :loading=\"btnLoading\" size=\"mini\" type=\"primary\" @click=\"confirmTree\" >确 定</el-button>\r\n        <el-button size=\"mini\" @click=\"treeOpen = false\" >取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n<script>\r\nimport {getBomByErpCode, getProductByMd003} from \"@/api/common/erp\";\r\nimport MaterialGoodsSelectTable from \"@/views/resource/materialGoods/selectTable.vue\";\r\nimport {supplierAll} from \"@/api/supplier/supplier\";\r\nimport {allBcLog} from \"@/api/project/bcLog\";\r\nimport BcPriceTable from \"@/views/sop/bc/bcPriceTable.vue\";\r\nimport CustomCols from \"@/components/customCols.vue\";\r\nimport {allTreeData} from \"@/api/system/treeData\";\r\nimport {erpBom} from \"@/api/production/schedulePlan\";\r\nimport {delBc, updateBcForce} from \"@/api/project/bc\";\r\n\r\nexport default {\r\n  name: 'projectBcTable',\r\n  components: {\r\n    CustomCols,\r\n    BcPriceTable,\r\n    MaterialGoodsSelectTable,\r\n  },\r\n  props: {\r\n    form: {\r\n      type: Object,\r\n      required: true,\r\n    },\r\n    materialList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    devStatus: {\r\n      type: String,\r\n      default: '0',\r\n    },\r\n    readonly: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  watch: {\r\n    'form.id': {\r\n      async handler(v) {\r\n        this.resetQuery()\r\n        if(v && this.readonly) {//如果是视图模式\r\n          let bcLogList = await allBcLog({projectId: v, status: '3'})\r\n          let logArray = []\r\n          for (let item of this.logOptions) {\r\n            let bcLog = bcLogList.filter(i => i.projectId === v && i.type === item.dictValue)\r\n            if (bcLog && bcLog.length > 0) {\r\n              let array = []\r\n              let keys = []\r\n              for (let bc of bcLog) {\r\n                if (!keys.includes(bc.projectItemOrderCode)) {\r\n                  keys.push(bc.projectItemOrderCode)\r\n                }\r\n              }\r\n              for (let k of keys) {\r\n                let projectBcIds = bcLog.filter(i => i.projectItemOrderCode === k).map(i => i.projectBcId)\r\n                array.push({\r\n                  projectItemOrderCode: k,\r\n                  projectBcIds,\r\n                })\r\n              }\r\n              logArray.push({\r\n                label: item.dictLabel,\r\n                value: item.dictValue,\r\n                array,\r\n              })\r\n            }\r\n          }\r\n          this.logArray = logArray\r\n          this.bcLogList = bcLogList\r\n        }\r\n      },\r\n      immediate: true,\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      queryParams: {\r\n        name: null,\r\n      },\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      loading: false,\r\n      btnLoading: false,\r\n      bomOpen: false,\r\n      bcOpen: false,\r\n      open: false,\r\n      fullscreenFlag: false,\r\n      bcTypeOptions: [],\r\n      bomData: [],\r\n      bomTree: [],\r\n      currentBcArray: [],\r\n      typeOptions: [\r\n        {label: '包材开发',value: '0'},\r\n        {label: '客户开发',value: '1'},\r\n        {label: '采购开发',value: '2'},\r\n        {label: '自制',value: '3'},  //20241122研发开发改为自制\r\n      ],\r\n      resourceTypeOptions: [\r\n        {label: '普通添加',value: 'customer'},\r\n        {label: 'bom',value: 'bom'},\r\n        {label: '包材库',value: 'bc'},\r\n        {label: '包材开发',value: 'dev'},\r\n      ],\r\n      attrOptions: [],\r\n      zrqOptions: [],\r\n      materialTypeOptions: [],\r\n      supplierList: [],\r\n      title: null,\r\n      columns: [\r\n        {label: '开发类型',visible: true},\r\n        {label: '项目包材编码',visible: true},\r\n        {label: '类别',visible: true},\r\n        {label: '包装材料',visible: true},\r\n        {label: '包材库编码',visible: true},\r\n        {label: '包材ERP编码',visible: true},\r\n        {label: '物料属性',visible: true},\r\n        {label: '名称',visible: true},\r\n        {label: '规格',visible: true},\r\n        {label: '尺寸',visible: true},\r\n        {label: '型号',visible: true},\r\n        {label: '供应商',visible: true},\r\n        {label: '图片',visible: false},\r\n        {label: 'COA/SPEC',visible: false},\r\n        {label: '备注',visible: true},\r\n      ],\r\n      logArray: [],\r\n      bcLogList: [],\r\n      logOptions: [],\r\n      priceArray: [],\r\n      configArray: [],\r\n      files: [],\r\n      currentRow: {},\r\n      gradedTypeOptions: [\r\n        {label: '订单价',value: '0'},\r\n        {label: 'MOQ价',value: '1'},\r\n        {label: '梯度价(一档)',value: '2'},\r\n        {label: '梯度价(二档)',value: '3'},\r\n        {label: '梯度价(三档)',value: '4'},\r\n        {label: '梯度价(四档)',value: '5'},\r\n        {label: '梯度价(五档)',value: '6'},\r\n        {label: '梯度价(六档)',value: '7'},\r\n        {label: '梯度价(七档)',value: '8'},\r\n        {label: '梯度价(八档)',value: '9'},\r\n        {label: '梯度价(九档)',value: '10'},\r\n      ],\r\n      stageOptions: [\r\n        {label:'裸包价',value: '0'},\r\n        {label:'寻样阶段',value: '1'},\r\n        {label:'打样阶段',value: '2'},\r\n        {label:'订单阶段',value: '3'},\r\n      ],\r\n      allocatorOptions: [],\r\n      erpCode: [],\r\n      typeOpen: false,\r\n      treeOpen: false,\r\n      mb005Options: [\r\n        {label: '包材',value: '104'},\r\n        {label: '半成品',value: '103'},\r\n        {label: '裸装品',value: '102'},\r\n        {label: '成品',value: '101'},\r\n      ],\r\n      rules: {\r\n        mb005: [\r\n          {required: true,msg: '请选择物料类型'}\r\n        ]\r\n      },\r\n      materialArray: [],\r\n    }\r\n  },\r\n  async created() {\r\n    this.getDicts(\"PRODUCT_PROPERTIES\").then(response => {\r\n      const attrOptions = response.data\r\n      attrOptions.push({dictLabel: '自制',dictValue: '0'})\r\n      attrOptions.push({dictLabel: '外购',dictValue: '1'})\r\n      attrOptions.push({dictLabel: '客指代采',dictValue: '3'})\r\n      this.attrOptions = attrOptions\r\n    })\r\n    let logRes = await this.getDicts(\"project_bc_log\")\r\n    this.logOptions = logRes.data;\r\n    let bcTypeRes = await this.getDicts(\"project_bc_type\")\r\n    this.bcTypeOptions = bcTypeRes.data;\r\n    this.getDicts(\"bc-zrq\").then(response => {\r\n      this.zrqOptions = response.data;\r\n    })\r\n    this.getDicts(\"BZCL\").then(response => {\r\n      this.materialTypeOptions = response.data;\r\n    })\r\n    this.getDicts(\"bc-fpq\").then(response => {\r\n      this.allocatorOptions = response.data;\r\n    })\r\n    let supplierList = await supplierAll({supplierType: '1',reqType:1});\r\n    this.supplierList = supplierList;\r\n  },\r\n  methods: {\r\n    getList() {\r\n      let materialArray = this.materialList.filter(i => i.mb005 !== '103')\r\n      let params = Object.assign({}, this.queryParams)\r\n      if (params.name) {\r\n        materialArray = materialArray.filter(i => i.name == params.name)\r\n      }\r\n      this.materialArray = materialArray.slice((this.currentPage-1) * this.pageSize, this.currentPage * this.pageSize)\r\n    },\r\n    handleQuery() {\r\n      this.currentPage = 1\r\n      this.getList()\r\n    },\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.currentPage = val\r\n    },\r\n    openDoc() {\r\n      window.open('https://view.officeapps.live.com/op/view.aspx?src=https://enow.oss-cn-beijing.aliyuncs.com/images/20240909/1725847832537.pptx')\r\n    },\r\n    async confirmTree() {\r\n      const params = {\r\n        id: this.currentRow.id,\r\n        mb005: this.currentRow.mb005,\r\n        zrqType: this.currentRow.zrqType,\r\n        allocator: this.currentRow.allocator.join(','),\r\n        type: this.currentRow.type,\r\n      }\r\n      const res = await updateBcForce(params)\r\n      this.treeOpen = false\r\n    },\r\n    mb005Change() {\r\n      this.currentRow.type = null\r\n      this.currentRow.zrqType = null\r\n      this.currentRow.allocator = []\r\n    },\r\n    typeChange() {\r\n      this.currentRow.mb005 = '104'\r\n      this.currentRow.zrqType = null\r\n      this.currentRow.allocator = []\r\n    },\r\n    zrqTypeChange() {\r\n      this.currentRow.mb005 = '104'\r\n      this.currentRow.type = '0'\r\n    },\r\n    allocatorChange() {\r\n      this.currentRow.mb005 = '104'\r\n      this.currentRow.type = '0'\r\n    },\r\n    async confirmMaterial() {\r\n      await this.$refs[\"form\"].validate()\r\n      const form = this.currentRow\r\n      if(form.mb005 === '104') {\r\n        if(!form.type) {\r\n          this.msgError('请选择包材类型')\r\n          return\r\n        }\r\n        if(form.type==='0') { // 如果是主包材,没有选择主包材类型\r\n          if(!form.zrqType) {\r\n            this.msgError('请选择主包材类型')\r\n            return\r\n          }\r\n        }\r\n      }\r\n      this.typeOpen = false\r\n    },\r\n    allocatorText(allocator) {\r\n      const arr = this.allocatorOptions.filter(i=> allocator.includes(i.dictValue))\r\n      if(arr && arr[0]) {\r\n        return arr.map(i=> i.dictLabel).join(',')\r\n      }\r\n    },\r\n    mb005Text(mb005) {\r\n      const arr = this.mb005Options.filter(i=> mb005 === i.value)\r\n      if(arr && arr[0]) {\r\n        return arr[0].label\r\n      }\r\n    },\r\n    showType(row) {\r\n      this.currentRow = row\r\n      this.treeOpen = true\r\n    },\r\n    materialText(item) {\r\n      const array = []\r\n      if(item.mb005) {\r\n        array.push(this.mb005Text(item.mb005))\r\n        if(item.mb005 === '104') {\r\n          if(item.type) {\r\n            array.push(this.selectDictLabel(this.bcTypeOptions,item.type))\r\n          }\r\n          if(item.zrqType) {\r\n            array.push(this.selectDictLabel(this.zrqOptions,item.zrqType))\r\n          }\r\n          if(item.allocator.length) {\r\n            array.push(this.allocatorText(item.allocator))\r\n          }\r\n        }\r\n        return array.join('/')\r\n      } else {\r\n        return \"请选择\"\r\n      }\r\n    },\r\n    async showCol() {\r\n      await this.$nextTick()\r\n      this.$refs.customCols.columnsOpen = true\r\n    },\r\n    colSuccess() {\r\n\r\n    },\r\n    valueToLabel(options,value) {\r\n      const arr = options.filter(i=> i.value === value)\r\n      if(arr && arr[0]) {\r\n        return arr[0].label\r\n      }\r\n    },\r\n    priceText(row) {\r\n      if(row.priceArray) {\r\n        const o = row.priceArray[0]\r\n        const array = []\r\n        if(o.stage) {\r\n          array.push(this.valueToLabel(this.stageOptions,o.stage))\r\n        }\r\n        array.push(\"(\")\r\n        array.push(o.createDate)\r\n        array.push(\")\")\r\n        if(o.array && o.array[0]) {\r\n          const sub = o.array[0]\r\n          if(sub.gradedType) {\r\n            array.push(this.valueToLabel(this.gradedTypeOptions,sub.gradedType))\r\n          }\r\n          if(sub.price) {\r\n            array.push(sub.price)\r\n          }\r\n          if(sub.moq) {\r\n            array.push('起订量:' + sub.moq)\r\n          }\r\n        }\r\n        return array.join('')\r\n      }\r\n    },\r\n    validateTable() {\r\n      for (const item of this.materialList) {\r\n        if(!item.opType) {\r\n          throw new Error('请选择开发类型!')\r\n          return\r\n        }\r\n        if(!item.name) {\r\n          throw new Error('请输入名称!')\r\n          return\r\n        }\r\n        if(!item.mb005) {\r\n          throw new Error('请输入物料类型!')\r\n          return\r\n        }\r\n        if(!item.mb008) {\r\n          throw new Error('请输入物料属性!')\r\n          return\r\n        }\r\n        if(item.mb005 === '104') {\r\n          if(!item.type) {\r\n            throw new Error('请选择类别!')\r\n            return\r\n          }\r\n          if(item.type==='0' && (!item.zrqType && !item.allocator.length)) {//主容器或分配器二选一\r\n            throw new Error('请选择主容器类别或分配器!')\r\n            return\r\n          }\r\n        }\r\n      }\r\n    },\r\n    async saveSuccess() {\r\n      this.$emit(\"saveSuccess\")\r\n      this.open = false\r\n    },\r\n    resetPriceArray() {\r\n      this.configArray = [\r\n        '1'\r\n      ]\r\n      this.priceArray = []\r\n      this.files = []\r\n    },\r\n    async handlePrice(row) {\r\n      this.currentRow = row\r\n      this.resetPriceArray()\r\n      if(row.priceArray) {\r\n        this.priceArray = row.priceArray\r\n      }\r\n      if(row.configArray) {\r\n        this.configArray = row.configArray\r\n      }\r\n      if(row.files) {\r\n        this.files = row.files\r\n      }\r\n      this.open = true\r\n    },\r\n    resourceText(resourceType) {\r\n      const arr = this.resourceTypeOptions.filter(i=> i.value === resourceType)\r\n      if(arr && arr[0]) {\r\n        return arr[0].label\r\n      }\r\n    },\r\n    fileChange(fileList,id) {\r\n      for(let item of this.materialList) {\r\n        if(item.key === id || item.id === id) {\r\n          item.files = fileList\r\n        }\r\n      }\r\n    },\r\n    confirmBc() {\r\n      for (const item of this.currentBcArray) {\r\n        let o = {\r\n          resourceType: 'bc',\r\n          opType: null,\r\n          code: null,\r\n          bcCode: item.materialCode,\r\n          erpCode: item.erpCode,\r\n          name: item.materialName,\r\n          spec: item.capacity,\r\n          type: null,\r\n          zrqType: null,\r\n          allocator: [],\r\n          materialType: null,\r\n          length: item.length,\r\n          width: item.width,\r\n          height: item.height,\r\n          model: null,\r\n          vendor: null,\r\n          ecgy: null,\r\n          remark: null,\r\n          imgs: null,\r\n          files: [],\r\n          priceArray: [],\r\n          configArray: [],\r\n          supplierId: null,\r\n          devStatus: this.devStatus,\r\n        }\r\n        this.materialList.push(o)\r\n      }\r\n      this.bcOpen = false\r\n    },\r\n    bcChange(bcArray) {\r\n      this.currentBcArray = bcArray\r\n    },\r\n    selectBc() {\r\n      this.currentBcArray = []\r\n      this.bcOpen = true\r\n    },\r\n    async confirmBom() {\r\n      await this.$nextTick()\r\n      const ids = this.$refs.bomTree.getCheckedNodes().map(i => i.id)\r\n      const arr = this.bomData.filter(i=>ids.includes(i.id))\r\n      if(arr && arr[0]) {\r\n        for (const item of arr) {\r\n          let o = {\r\n            resourceType: 'bom',\r\n            opType: null,\r\n            code: null,\r\n            bcCode: null,\r\n            erpCode: item.md003,\r\n            name: item.mb002,\r\n            mb005: item.mb005,\r\n            mb008: item.mb008,\r\n            spec: null,\r\n            type: null,\r\n            zrqType: null,\r\n            allocator: [],\r\n            materialType: null,\r\n            length: 0,\r\n            width: 0,\r\n            height: 0,\r\n            model: null,\r\n            vendor: null,\r\n            ecgy: null,\r\n            remark: null,\r\n            imgs: null,\r\n            files: [],\r\n            priceArray: [],\r\n            configArray: [],\r\n            supplierId: null,\r\n            devStatus: this.devStatus,\r\n          }\r\n          this.materialList.push(o)\r\n        }\r\n      }\r\n      this.bomOpen = false\r\n    },\r\n    toBomTree(list, md001) {\r\n      return list.filter(item => {\r\n        if(md001) {\r\n          if (item.md001 === md001) {\r\n            let children = this.toBomTree(list, item.md003)\r\n            if(children && children.length > 0) {\r\n              item.children = children\r\n            }\r\n            return true\r\n          }\r\n        } else {\r\n          if ([undefined,null,''].includes(item.md001)) {\r\n            let children = this.toBomTree(list, item.md003)\r\n            if(children && children.length > 0) {\r\n              item.children = children\r\n            }\r\n            return true\r\n          }\r\n        }\r\n        return false\r\n      })\r\n    },\r\n    async getErpInfo() {\r\n      const erpNo = this.erpCode\r\n      if (erpNo) {\r\n        this.btnLoading = true\r\n        this.loading = true\r\n        let erpRes = await getProductByMd003(erpNo);\r\n        if(erpRes && erpRes.data) {\r\n          let data = erpRes.data\r\n          let form = this.form\r\n          let mb002 = data.MB002\r\n          if(mb002){\r\n            form.ma003 = data.MA003\r\n            form.mb002 = mb002\r\n            form.mb005 = data.MB005\r\n\r\n            let bomList = await erpBom({erpNo})\r\n            bomList = bomList.filter(i => i.mb005 !== '105')\r\n            bomList.push({\r\n              id: erpNo,\r\n              mb002: mb002,\r\n              md003: erpNo,\r\n              mb005: data.MB005,\r\n              mb008: data.mb008,\r\n            })\r\n            let bomTree = this.toBomTree(JSON.parse(JSON.stringify(bomList)), undefined)\r\n            this.bomTree = bomTree\r\n            this.bomData = bomList\r\n          }else{\r\n            this.msgError('erp代码输入有误');\r\n            form.ma003 = null;\r\n            form.mb002 = null;\r\n          }\r\n        } else {\r\n          this.msgError('erp代码输入有误');\r\n        }\r\n        this.btnLoading = false\r\n        this.loading = false\r\n      }\r\n    },\r\n    async showBom() {\r\n      this.bomOpen = true\r\n    },\r\n    addItem() {\r\n      let o = {\r\n        rid: this.$nanoid(),\r\n        resourceType: 'customer',\r\n        opType: null,\r\n        type: null,\r\n        code: null,\r\n        bcCode: null,\r\n        erpCode: null,\r\n        name: null,\r\n        spec: null,\r\n        zrqType: null,\r\n        allocator: [],\r\n        materialType: null,\r\n        length: 0,\r\n        width: 0,\r\n        height: 0,\r\n        model: null,\r\n        vendor: null,\r\n        ecgy: null,\r\n        remark: null,\r\n        imgs: null,\r\n        files: [],\r\n        priceArray: [],\r\n        configArray: [],\r\n        supplierId: null,\r\n        devStatus: this.devStatus,\r\n      }\r\n      this.materialList.push(o)\r\n    },\r\n    async delItem(row){\r\n      let index;\r\n      if(row.id) {\r\n        index = this.materialList.findIndex(i=> i.id === row.id)\r\n      } else if(row.rid) {\r\n        index = this.materialList.findIndex(i=> i.rid === row.rid)\r\n      }\r\n      this.materialList.splice(index,1)\r\n      if(row.id) {\r\n        await delBc(row.id)\r\n      }\r\n    },\r\n    tdClass(projectBcIds,projectBcId) {\r\n      return projectBcIds.includes(projectBcId) ? 'el-icon-check' : ''\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.row-wrapper {\r\n  display: flex;\r\n\r\n  .label {\r\n    width: 150px;\r\n    font-size: 13px;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .content {\r\n\r\n  }\r\n}\r\n.table-wrapper {\r\n\r\n  .base-table {\r\n    thead {\r\n      position: sticky;\r\n      top: 0;\r\n      z-index: 3;\r\n\r\n      .nth0 {\r\n        background-color: rgba(248,248,249,1);\r\n      }\r\n\r\n    }\r\n\r\n    tbody {\r\n      .nth0 {\r\n        background-color: rgba(255,255,255,1);\r\n      }\r\n    }\r\n\r\n    .nth0 {\r\n      position: sticky;\r\n      left: 0;\r\n      z-index: 1;\r\n    }\r\n\r\n  }\r\n\r\n}\r\n</style>\r\n"]}]}