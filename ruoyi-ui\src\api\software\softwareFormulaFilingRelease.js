import request from '@/utils/request'

// 查询配方备案释放列表
export function listSoftwareFormulaFilingRelease(query) {
  return request({
    url: '/software/softwareFormulaFilingRelease/list',
    method: 'get',
    params: query
  })
}

//配方审核列表
export function listAuditFormulaFilingRealeaseData(data) {
  return request({
    url: '/software/softwareFormulaFilingRelease/audit',
    method: 'post',
    data: data
  })
}

// 查询配方备案释放详细
export function getSoftwareFormulaFilingRelease(id) {
  return request({
    url: '/software/softwareFormulaFilingRelease/' + id,
    method: 'get'
  })
}


//验证
export function verifySoftwareFormulaFilingRelease(data) {
  return request({
    url: '/software/softwareFormulaFilingRelease/verify',
    method: 'post',
    data: data
  })
}
//验证
export function verifyPassSoftwareFormulaFilingRelease(data) {
  return request({
    url: '/software/softwareFormulaFilingRelease/passVerify',
    method: 'post',
    data: data
  })
}

// 新增配方备案释放
export function addSoftwareFormulaFilingRelease(data) {
  return request({
    url: '/software/softwareFormulaFilingRelease',
    method: 'post',
    data: data
  })
}

// 修改配方备案释放
export function updateSoftwareFormulaFilingRelease(data) {
  return request({
    url: '/software/softwareFormulaFilingRelease',
    method: 'put',
    data: data
  })
}

// 删除配方备案释放
export function delSoftwareFormulaFilingRelease(id) {
  return request({
    url: '/software/softwareFormulaFilingRelease/' + id,
    method: 'delete'
  })
}

// 导出配方备案释放
export function exportSoftwareFormulaFilingRelease(query) {
  return request({
    url: '/software/softwareFormulaFilingRelease/export',
    method: 'get',
    params: query
  })
}


//撤销审核
export function cancelAudit(data) {
  return request({
    url: '/software/softwareFormulaFilingRelease/cancelAudit',
    method: 'put',
    data: data
  })
}

