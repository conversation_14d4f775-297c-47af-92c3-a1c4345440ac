import request from '@/utils/request'

// 查询其他工时明细列表
export function listOtherMinutes(query) {
  return request({
    url: '/production/otherMinutes/list',
    method: 'get',
    params: query
  })
}

// 查询其他工时明细详细
export function getOtherMinutes(id) {
  return request({
    url: '/production/otherMinutes/' + id,
    method: 'get'
  })
}

// 新增其他工时明细
export function addOtherMinutes(data) {
  return request({
    url: '/production/otherMinutes',
    method: 'post',
    data: data
  })
}

// 修改其他工时明细
export function updateOtherMinutes(data) {
  return request({
    url: '/production/otherMinutes',
    method: 'put',
    data: data
  })
}

// 删除其他工时明细
export function delOtherMinutes(id) {
  return request({
    url: '/production/otherMinutes/' + id,
    method: 'delete'
  })
}

// 导出其他工时明细
export function exportOtherMinutes(query) {
  return request({
    url: '/production/otherMinutes/export',
    method: 'get',
    params: query
  })
}

export function allOtherMinutes(query) {
  return request({
    url: '/production/otherMinutes/all',
    method: 'get',
    params: query
  })
}

// 查询其他工时明细详细
export function getOtherDayMinutesByParams(params) {
  return request({
    url: '/production/otherMinutes/getDayMinutes',
    method: 'get',
    params,
  })
}
