import request from "@/utils/request";

export function listPurchaseInvoice(query) {
  return request({
    url: '/purchase/invoice/list',
    method: 'get',
    params: query
  })
}

export function listChoosePurchaseInvoice(query) {
  return request({
    url: '/purchase/invoice/chooseList',
    method: 'get',
    params: query
  })
}

export function listPurchaseInvoiceAudit(query) {
  return request({
    url: '/purchase/invoice/audit',
    method: 'get',
    params: query
  })
}

export function cancelAudit(data) {
  return request({
    url: '/purchase/invoice/cancelAudit',
    method: 'put',
    data: data
  })
}



// 查询包材原料价格审核列表
export function purchaseInvoiceListHistory(query) {
  return request({
    url: '/purchase/invoice/history',
    method: 'get',
    params: query
  })
}


export function getPurchaseInvoice(id) {
  return request({
    url: '/purchase/invoice/' + id,
    method: 'get'
  })
}
export function getPurchaseInvoiceReconciliaId(query) {
  return request({
    url: '/purchase/invoice/reconcilia',
    method: 'get',
    params: query
  })
}
export function addPurchaseInvoice(data){
  return request({
    url: '/purchase/invoice/add',
    method: 'post',
    data: data
  })
}
