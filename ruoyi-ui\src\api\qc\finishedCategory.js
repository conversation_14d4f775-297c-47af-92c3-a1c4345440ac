import request from '@/utils/request'

// 查询成品类别列表
export function listFinishedCategory(query) {
  return request({
    url: '/qc/finishedCategory/list',
    method: 'get',
    params: query
  })
}

// 查询成品类别详细
export function getFinishedCategory(id) {
  return request({
    url: '/qc/finishedCategory/' + id,
    method: 'get'
  })
}

// 新增成品类别
export function addFinishedCategory(data) {
  return request({
    url: '/qc/finishedCategory',
    method: 'post',
    data: data
  })
}

// 修改成品类别
export function updateFinishedCategory(data) {
  return request({
    url: '/qc/finishedCategory',
    method: 'put',
    data: data
  })
}

// 删除成品类别
export function delFinishedCategory(id) {
  return request({
    url: '/qc/finishedCategory/' + id,
    method: 'delete'
  })
}

// 导出成品类别
export function exportFinishedCategory(query) {
  return request({
    url: '/qc/finishedCategory/export',
    method: 'get',
    params: query
  })
}



export function allFinishedCategory(query) {
  return request({
    url: '/qc/finishedCategory/all',
    method: 'get',
    params: query
  })
}
