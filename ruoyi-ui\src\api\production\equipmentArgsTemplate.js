import request from '@/utils/request'

// 查询设备参数模板列表
export function listEquipmentArgsTemplate(query) {
  return request({
    url: '/production/equipmentArgsTemplate/list',
    method: 'get',
    params: query
  })
}

// 查询设备参数模板详细
export function getEquipmentArgsTemplate(id) {
  return request({
    url: '/production/equipmentArgsTemplate/' + id,
    method: 'get'
  })
}

// 新增设备参数模板
export function addEquipmentArgsTemplate(data) {
  return request({
    url: '/production/equipmentArgsTemplate',
    method: 'post',
    data: data
  })
}

// 修改设备参数模板
export function updateEquipmentArgsTemplate(data) {
  return request({
    url: '/production/equipmentArgsTemplate',
    method: 'put',
    data: data
  })
}

// 删除设备参数模板
export function delEquipmentArgsTemplate(id) {
  return request({
    url: '/production/equipmentArgsTemplate/' + id,
    method: 'delete'
  })
}

// 导出设备参数模板
export function exportEquipmentArgsTemplate(query) {
  return request({
    url: '/production/equipmentArgsTemplate/export',
    method: 'get',
    params: query
  })
}

export function allEquipmentArgsTemplate(query) {
  return request({
    url: '/production/equipmentArgsTemplate/all',
    method: 'get',
    params: query
  })
}

export function importEquipmentArgsTemplate() {
  return request({
    url: '/production/equipmentArgsTemplate/importEquipmentArgsTemplate',
    method: 'get'
  })
}
