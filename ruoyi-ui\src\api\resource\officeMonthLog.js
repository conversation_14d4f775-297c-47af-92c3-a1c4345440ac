import request from '@/utils/request'

// 查询月末盘存列表
export function listOfficeMonthLog(query) {
  return request({
    url: '/resource/officeMonthLog/list',
    method: 'get',
    params: query
  })
}

// 查询月末盘存详细
export function getOfficeMonthLog(id) {
  return request({
    url: '/resource/officeMonthLog/' + id,
    method: 'get'
  })
}

// 导出月末盘存
export function exportOfficeMonthLog(query) {
  return request({
    url: '/resource/officeMonthLog/export',
    method: 'get',
    params: query
  })
}

export function groupSumOfficeMonthLog(query) {
  return request({
    url: '/resource/officeMonthLog/groupSum',
    method: 'get',
    params: query
  })
}
