import request from '@/utils/request'

// 查询生产群组列表
export function listProductionGroup(query) {
  return request({
    url: '/production/productionGroup/list',
    method: 'get',
    params: query
  })
}

// 查询生产群组详细
export function getProductionGroup(id) {
  return request({
    url: '/production/productionGroup/' + id,
    method: 'get'
  })
}

// 新增生产群组
export function addProductionGroup(data) {
  return request({
    url: '/production/productionGroup',
    method: 'post',
    data: data
  })
}

// 修改生产群组
export function updateProductionGroup(data) {
  return request({
    url: '/production/productionGroup',
    method: 'put',
    data: data
  })
}

// 删除生产群组
export function delProductionGroup(id) {
  return request({
    url: '/production/productionGroup/' + id,
    method: 'delete'
  })
}

// 导出生产群组
export function exportProductionGroup(query) {
  return request({
    url: '/production/productionGroup/export',
    method: 'get',
    params: query
  })
}

export function allProductionGroup(query) {
  return request({
    url: '/production/productionGroup/all',
    method: 'get',
    params: query
  })
}
