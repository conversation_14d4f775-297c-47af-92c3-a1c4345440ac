import request from '@/utils/request'

export function allFinishedProjectItem(query) {
  return request({
    url: '/qc/finishedProjectItem/all',
    method: 'get',
    params: query
  })
}

export function revertAllFinishedProjectItem(query) {
  return request({
    url: '/qc/finishedProjectItem/revertAll',
    method: 'get',
    params: query
  })
}

// 查询成品检验项目明细详细
export function getFinishedProjectItem(id) {
  return request({
    url: '/qc/finishedProjectItem/' + id,
    method: 'get'
  })
}

// 删除成品检验项目明细
export function delFinishedProjectItem(id) {
  return request({
    url: '/qc/finishedProjectItem/' + id,
    method: 'delete'
  })
}

// 导出成品检验项目明细
export function exportFinishedProjectItem(query) {
  return request({
    url: '/qc/finishedProjectItem/export',
    method: 'get',
    params: query
  })
}
