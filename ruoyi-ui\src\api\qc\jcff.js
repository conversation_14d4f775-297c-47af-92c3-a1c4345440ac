import request from '@/utils/request'

// 查询检测方法列表
export function listJcff(query) {
  return request({
    url: '/qc/jcff/list',
    method: 'get',
    params: query
  })
}

// 查询检测方法详细
export function getJcff(id) {
  return request({
    url: '/qc/jcff/' + id,
    method: 'get'
  })
}

// 新增检测方法
export function addJcff(data) {
  return request({
    url: '/qc/jcff',
    method: 'post',
    data: data
  })
}

// 修改检测方法
export function updateJcff(data) {
  return request({
    url: '/qc/jcff',
    method: 'put',
    data: data
  })
}

// 删除检测方法
export function delJcff(id) {
  return request({
    url: '/qc/jcff/' + id,
    method: 'delete'
  })
}

// 导出检测方法
export function exportJcff(query) {
  return request({
    url: '/qc/jcff/export',
    method: 'get',
    params: query
  })
}

export function jcffAll(query) {
  return request({
    url: '/qc/jcff/all',
    method: 'get',
    params: query
  })
}
