import request from '@/utils/request'

// 查询派工设定列表
export function listWipDispatchSet(query) {
  return request({
    url: '/mes/wipDispatchSet/list',
    method: 'get',
    params: query
  })
}

// 查询派工设定详细
export function getWipDispatchSet(equipmentno) {
  return request({
    url: '/mes/wipDispatchSet/' + equipmentno,
    method: 'get'
  })
}

// 新增派工设定
export function addWipDispatchSet(data) {
  return request({
    url: '/mes/wipDispatchSet',
    method: 'post',
    data: data
  })
}

// 修改派工设定
export function updateWipDispatchSet(data) {
  return request({
    url: '/mes/wipDispatchSet',
    method: 'put',
    data: data
  })
}

// 删除派工设定
export function delWipDispatchSet(equipmentno) {
  return request({
    url: '/mes/wipDispatchSet/' + equipmentno,
    method: 'delete'
  })
}

// 导出派工设定
export function exportWipDispatchSet(query) {
  return request({
    url: '/mes/wipDispatchSet/export',
    method: 'get',
    params: query
  })
}