import request from '@/utils/request'

// 查询bom变更反馈列表
export function listBomChangeFeedback(query) {
  return request({
    url: '/sop/bomChangeFeedback/list',
    method: 'get',
    params: query
  })
}

export function unFinishedListBomChangeFeedback(query) {
  return request({
    url: '/sop/bomChangeFeedback/unFinishedList',
    method: 'get',
    params: query
  })
}

export function allBomChangeFeedback(query) {
  return request({
    url: '/sop/bomChangeFeedback/all',
    method: 'get',
    params: query
  })
}

// 查询bom变更反馈详细
export function getBomChangeFeedback(id) {
  return request({
    url: '/sop/bomChangeFeedback/' + id,
    method: 'get'
  })
}

// 新增bom变更反馈
export function addBomChangeFeedback(data) {
  return request({
    url: '/sop/bomChangeFeedback',
    method: 'post',
    data: data
  })
}

// 修改bom变更反馈
export function updateBomChangeFeedback(data) {
  return request({
    url: '/sop/bomChangeFeedback',
    method: 'put',
    data: data
  })
}

export function updateFeedbackByChangeIdAndProject(data) {
  return request({
    url: '/sop/bomChangeFeedback/updateByChangeIdAndProject',
    method: 'put',
    data: data
  })
}

export function terminateBomByChangeIdAndProject(data) {
  return request({
    url: '/sop/bomChangeFeedback/terminateByChangeIdAndProject',
    method: 'put',
    data: data
  })
}

// 删除bom变更反馈
export function delBomChangeFeedback(id) {
  return request({
    url: '/sop/bomChangeFeedback/' + id,
    method: 'delete'
  })
}

// 导出bom变更反馈
export function exportBomChangeFeedback(query) {
  return request({
    url: '/sop/bomChangeFeedback/export',
    method: 'get',
    params: query
  })
}
