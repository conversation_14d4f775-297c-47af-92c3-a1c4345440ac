import request from '@/utils/request'
import { getToken } from '@/utils/auth'

// 查询用户可使用的智能体列表
export function listAgentByUser(query) {
  return request({
    url: '/ai/chat/listByUser',
    method: 'get',
    params: query
  })
}

/**
 * 获取对话列表
 */
export function fetchConversations(query) {
  return request({
    url: '/ai/chat/conversations',
    method: 'get',
    params: query
  })
}

/**
* 获取特定对话的聊天记录
*/
export function fetchChatList(query) {
  return request({
    url: `/ai/chat/messages`,
    method: 'get',
    params: query
  })
}

/**
* 非流式聊天
*/
export function chatMessage(query) {
  return request({
    url: `/ai/chat/chat`,
    method: 'post',
    data: query
  })
}

/**
 * 流式聊天
 */
export function streamChatMessage(query, onMessage, onError) {
  const token = getToken();

  // 使用 fetch API 来处理流式响应
  fetch(process.env.VUE_APP_BASE_API+`/ai/chat/stream`, {
    method: 'POST',
    headers: {
      'Authorization': 'EnowRequestBearer ' + token,
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify(query)
  }).then(response => {

    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    function processChunk(chunk) {
      buffer += chunk;
      let index;
      while ((index = buffer.indexOf('\n\n')) !== -1) {
        const event = buffer.substring(0, index);
        // console.log("event==", event);
        buffer = buffer.substring(index + 2); // 移除已处理的部分

        // 去掉前缀 "data: " 并修剪空白
        const data = event.replace(/^data:/, '').trim();
        if (data) {
          try {
            const eventData = JSON.parse(data);

            // 检查是否为错误信息
            if (eventData.error) {
              onError(new Error(eventData.error)); // 处理错误信息
              return; // 终止处理
            }

            // 调用回调函数
            onMessage(eventData);
          } catch (err) {
            console.error('解析数据失败:', err);
          }
        }
      }
    }

    function read() {
      reader.read().then(({ done, value }) => {
        if (done) {
          // 处理剩余数据
          if (buffer.trim()) processChunk(buffer);
          // console.log('流结束');
          return;
        }
        const chunk = decoder.decode(value, { stream: true });
        processChunk(chunk);
        read();
      }).catch(onError);
    }

    read();
  }).catch(onError);

  return { close: () => { } };
}


/**
* 获取应用参数
*/
export function fetchAppParams(id) {
  return request({
    url: '/ai/chat/' + id,
    method: 'get'
  })
}

/**
 * 重命名对话
 */
export function renameConversation(data) {
  // console.log("重命名对话==",data)
  return request({
    url: `/ai/chat/renameConversation`,
    method: 'post',
    data: data
  })
}

// 删除对话
export function deleteConversation(data) {
  // console.log("删除对话==", data)
  return request({
    url: `/ai/chat/deleteConversation`,
    method: 'delete',
    data: data
  })
}

// 更新消息反馈
export function updateFeedback(data) {
  console.log("调用更新消息反馈==", data)
  return request({
    url: `/ai/chat/updateFeedback`,
    method: 'post',
    data: data
  })
}

/**
 * 停止回答
 */
export function stopCompletion(agentId, taskId ) {
  return request({
    url: `/ai/chat/stopCompletion/${agentId}`,
    method: 'get',
    params: { taskId }
  });
}

/**
 * 上传文件
 *
 */
export function uploadFile(agentId, file) {
  // 创建一个FormData对象，用于上传文件
  const formData = new FormData();
  formData.append('agentId', agentId);
  formData.append('file', file);

  return request({
    url: '/ai/chat/uploadFile',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data' // 指定内容类型为multipart/form-data
    }
  });
}

