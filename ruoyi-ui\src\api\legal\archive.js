import request from '@/utils/request'

// 查询法务存单列表
export function listArchive(query) {
  return request({
    url: '/legal/archive/list',
    method: 'get',
    params: query
  })
}

// 查询法务存单详细
export function getArchive(id) {
  return request({
    url: '/legal/archive/' + id,
    method: 'get'
  })
}

// 新增法务存单
export function addArchive(data) {
  return request({
    url: '/legal/archive',
    method: 'post',
    data: data
  })
}

// 修改法务存单
export function updateArchive(data) {
  return request({
    url: '/legal/archive',
    method: 'put',
    data: data
  })
}

// 删除法务存单
export function delArchive(id) {
  return request({
    url: '/legal/archive/' + id,
    method: 'delete'
  })
}

// 导出法务存单
export function exportArchive(query) {
  return request({
    url: '/legal/archive/export',
    method: 'get',
    params: query
  })
}

export function allArchive(query) {
  return request({
    url: '/legal/archive/all',
    method: 'get',
    params: query
  })
}

// 新增花盒
export function addFlowerBox(data) {
  return request({
    url: '/legal/archive/addFlowerBox',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data: data
  })
}

// 修改花盒
export function editFlowerBox(data) {
  return request({
    url: '/legal/archive/editFlowerBox',
    method: 'put',
    headers: { 'Content-Type': 'multipart/form-data' },
    data: data
  })
}

export function getArchiveByQuery(query) {
  return request({
    url: '/legal/archive/archiveByQuery',
    method: 'get',
    params: query,
  })
}
