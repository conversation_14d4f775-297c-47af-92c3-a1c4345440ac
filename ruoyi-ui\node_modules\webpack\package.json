{"name": "webpack", "version": "4.47.0", "author": "<PERSON> @sokra", "description": "Packs CommonJs/AMD modules for the browser. Allows to split your codebase into multiple bundles, which can be loaded on demand. Support loaders to preprocess files, i.e. json, jsx, es7, css, less, ... and your custom stuff.", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-module-context": "1.9.0", "@webassemblyjs/wasm-edit": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0", "acorn": "^6.4.1", "ajv": "^6.10.2", "ajv-keywords": "^3.4.1", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^4.5.0", "eslint-scope": "^4.0.3", "json-parse-better-errors": "^1.0.2", "loader-runner": "^2.4.0", "loader-utils": "^1.2.3", "memory-fs": "^0.4.1", "micromatch": "^3.1.10", "mkdirp": "^0.5.3", "neo-async": "^2.6.1", "node-libs-browser": "^2.2.1", "schema-utils": "^1.0.0", "tapable": "^1.1.3", "terser-webpack-plugin": "^1.4.3", "watchpack": "^1.7.4", "webpack-sources": "^1.4.1"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}, "webpack-command": {"optional": true}}, "devDependencies": {"@babel/core": "^7.7.2", "@types/node": "^10.12.21", "@types/tapable": "^1.0.1", "@types/webpack-sources": "^0.1.4", "@yarnpkg/lockfile": "^1.1.0", "babel-loader": "^8.0.6", "benchmark": "^2.1.1", "bundle-loader": "~0.5.0", "coffee-loader": "^0.9.0", "coffeescript": "^2.3.2", "coveralls": "^3.0.2", "css-loader": "^2.1.0", "es6-promise-polyfill": "^1.1.1", "eslint": "^5.8.0", "eslint-config-prettier": "^4.0.0", "eslint-plugin-jest": "^22.2.2", "eslint-plugin-jsdoc": "^15.3.2", "eslint-plugin-node": "^8.0.0", "eslint-plugin-prettier": "^3.0.0", "express": "~4.16.4", "file-loader": "^3.0.1", "glob": "^7.1.3", "husky": "^1.1.3", "i18n-webpack-plugin": "^1.0.0", "istanbul": "^0.4.5", "jest": "^24.9.0", "jest-junit": "^8.0.0", "json-loader": "^0.5.7", "json-schema-to-typescript": "^6.0.1", "less": "^3.9.0", "less-loader": "^4.0.3", "lint-staged": "^8.0.4", "lodash": "^4.17.4", "prettier": "^1.14.3", "pug": "^2.0.4", "pug-loader": "^2.4.0", "raw-loader": "^1.0.0", "react": "^16.8.0", "react-dom": "^16.8.0", "rimraf": "^2.6.2", "script-loader": "~0.7.0", "simple-git": "^1.65.0", "strip-ansi": "^5.2.0", "style-loader": "^0.23.1", "typescript": "^3.0.0-rc", "url-loader": "^1.1.2", "val-loader": "^1.0.2", "vm-browserify": "~1.1.0", "wast-loader": "^1.5.5", "webpack-dev-middleware": "^3.5.1", "webassembly-feature": "1.3.0", "worker-loader": "^2.0.0", "xxhashjs": "^0.2.1"}, "engines": {"node": ">=6.11.5"}, "repository": {"type": "git", "url": "https://github.com/webpack/webpack.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "homepage": "https://github.com/webpack/webpack", "main": "lib/webpack.js", "web": "lib/webpack.web.js", "bin": "./bin/webpack.js", "files": ["lib/", "bin/", "buildin/", "declarations/", "hot/", "web_modules/", "schemas/", "SECURITY.md"], "scripts": {"setup": "node ./setup/setup.js", "test": "node --max-old-space-size=4096 --trace-deprecation node_modules/jest-cli/bin/jest", "test:update-snapshots": "yarn jest -u", "test:integration": "node --max-old-space-size=4096 --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.test.js\"", "test:basic": "node --max-old-space-size=4096 --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/te{st/TestCasesNormal,st/StatsTestCases,st/ConfigTestCases}.test.js\"", "test:unit": "node --max-old-space-size=4096 --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.unittest.js\"", "travis:integration": "yarn cover:integration --ci $JEST", "travis:basic": "yarn cover:basic --ci $JEST", "travis:lintunit": "yarn lint && yarn cover:unit --ci $JEST", "travis:benchmark": "yarn benchmark --ci", "appveyor:integration": "yarn cover:integration --ci %JEST%", "appveyor:unit": "yarn cover:unit --ci %JEST%", "appveyor:benchmark": "yarn benchmark --ci", "build:examples": "cd examples && node buildAll.js", "pretest": "yarn lint", "prelint": "yarn setup", "lint": "yarn code-lint && yarn jest-lint && yarn type-lint && yarn special-lint", "code-lint": "eslint . --ext '.js' --cache", "type-lint": "tsc --pretty", "special-lint": "node tooling/inherit-types && node tooling/format-schemas && node tooling/compile-to-definitions", "special-lint-fix": "node tooling/inherit-types --write --override && node tooling/format-schemas --write && node tooling/compile-to-definitions --write", "fix": "yarn code-lint --fix && yarn special-lint-fix", "pretty": "prettier --loglevel warn --write \"*.{ts,js,json,yml,yaml}\" \"{setup,lib,bin,hot,buildin,benchmark,tooling,schemas}/**/*.{js,json}\" \"test/*.js\" \"test/helpers/*.js\" \"test/{configCases,watchCases,statsCases,hotCases}/**/webpack.config.js\" \"examples/**/webpack.config.js\"", "jest-lint": "node --max-old-space-size=4096 node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.lint.js\" --no-verbose", "benchmark": "node --max-old-space-size=4096 --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.benchmark.js\" --runInBand", "cover": "yarn cover:all && yarn cover:report", "cover:all": "node --max-old-space-size=4096 node_modules/jest-cli/bin/jest --coverage", "cover:basic": "node --max-old-space-size=4096 node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/te{st/TestCasesNormal,st/StatsTestCases,st/ConfigTestCases}.test.js\" --coverage", "cover:integration": "node --max-old-space-size=4096 node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.test.js\" --coverage", "cover:unit": "node --max-old-space-size=4096 node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.unittest.js\" --coverage", "cover:report": "istanbul report"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js|{lib,setup,bin,hot,buildin,tooling,schemas}/**/*.js|test/*.js|{test,examples}/**/webpack.config.js}": ["eslint --cache"]}, "jest": {"forceExit": true, "setupFilesAfterEnv": ["<rootDir>/test/setupTestFramework.js"], "testMatch": ["<rootDir>/test/*.test.js", "<rootDir>/test/*.unittest.js"], "watchPathIgnorePatterns": ["<rootDir>/.git", "<rootDir>/node_modules", "<rootDir>/test/js", "<rootDir>/test/browsertest/js", "<rootDir>/test/fixtures/temp-cache-fixture", "<rootDir>/test/fixtures/temp-", "<rootDir>/benchmark", "<rootDir>/examples/*/dist", "<rootDir>/coverage", "<rootDir>/.eslintcache"], "modulePathIgnorePatterns": ["<rootDir>/.git", "<rootDir>/node_modules/webpack/node_modules", "<rootDir>/test/js", "<rootDir>/test/browsertest/js", "<rootDir>/test/fixtures/temp-cache-fixture", "<rootDir>/test/fixtures/temp-", "<rootDir>/benchmark", "<rootDir>/examples/*/dist", "<rootDir>/coverage", "<rootDir>/.eslintcache"], "transformIgnorePatterns": ["<rootDir>"], "coverageDirectory": "<rootDir>/coverage", "coveragePathIgnorePatterns": ["\\.runtime\\.js$", "<rootDir>/test", "<rootDir>/schemas", "<rootDir>/node_modules"], "testEnvironment": "node", "coverageReporters": ["json"]}}