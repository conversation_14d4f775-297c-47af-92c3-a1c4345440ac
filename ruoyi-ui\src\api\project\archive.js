import request from '@/utils/request'

// 查询项目存档列表
export function listArchive(query) {
  return request({
    url: '/project/archive/list',
    method: 'get',
    params: query
  })
}

// 查询项目存档详细
export function getArchive(id) {
  return request({
    url: '/project/archive/' + id,
    method: 'get'
  })
}

// 查询项目存档详细
export function getArchiveByCode(code) {
  return request({
    url: '/project/archive/details/' + code,
    method: 'get'
  })
}

// 新增项目存档
export function addArchive(data) {
  return request({
    url: '/project/archive',
    method: 'post',
    data: data
  })
}

// 修改项目存档
export function updateArchive(data) {
  return request({
    url: '/project/archive',
    method: 'put',
    data: data
  })
}

// 删除项目存档
export function delArchive(id) {
  return request({
    url: '/project/archive/' + id,
    method: 'delete'
  })
}

// 导出项目存档
export function exportProjectArchive(id) {
  return request({
    url: '/project/archive/exportArchive/' + id,
    method: 'get',
  })
}

export function allArchive(query) {
  return request({
    url: '/project/archive/all',
    method: 'get',
    params: query
  })
}

//提交审核
export function submitAudit(data) {
  return request({
    url: '/project/archive/submitAudit',
    method: 'put',
    data: data
  })
}

//撤销申请
export function cancelAudit(data) {
  return request({
    url: '/project/archive/cancelAudit',
    method: 'put',
    data: data
  })
}
