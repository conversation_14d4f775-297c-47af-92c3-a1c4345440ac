import request from '@/utils/request'
export function daysListAttendance(query) {
  return request({
    url: '/hr/attendance/daysList',
    method: 'get',
    params: query
  })
}
// 查询考勤列表
export function listAttendance(query) {
  return request({
    url: '/hr/attendance/list',
    method: 'get',
    params: query
  })
}

export function kqAttendanceList(query) {
  return request({
    url: '/hr/attendance/kqList',
    method: 'get',
    params: query
  })
}


// 查询考勤详细
export function getAttendance(id) {
  return request({
    url: '/hr/attendance/' + id,
    method: 'get'
  })
}

//删除考勤详细
export function removeAttendance(id) {
  return request({
    url: '/hr/attendance/delete/' + id,
    method: 'get'
  })
}

// 查询考勤列表统计
export function countAttendance(query) {
  return request({
    url: '/hr/attendance/count',
    method: 'get',
    params: query
  })
}

// 生成月度考勤统计
export function generateAttendance(data) {
  return request({
    url: '/hr/attendance/generate',
    method: 'post',
    data: data
  })
}

// 生成月度考勤统计
export function batchGenerateAttendance(data) {
  return request({
    url: '/hr/attendance/batchGenerate',
    method: 'post',
    data: data
  })
}

// 修改考勤
export function updateAttendance(data) {
  return request({
    url: '/hr/attendance',
    method: 'put',
    data: data
  })
}

// 删除考勤
export function delAttendance(id) {
  return request({
    url: '/hr/attendance/' + id,
    method: 'delete'
  })
}
//批量设置考勤
export function batchSetAttendance(data) {
  return request({
    url: '/hr/attendance/batchSet',
    method: 'post',
    data: data
  })
}
//同步考勤记录
export function syncAttendance(data) {
  return request({
    url: '/hr/attendance/sync',
    method: 'post',
    data: data
  })
}

// 导出考勤
export function exportAttendance(query) {
  return request({
    url: '/hr/attendance/export',
    method: 'get',
    params: query
  })
}
// 查询考勤方案列表
export function getAttendancePlanAll() {
  return request({
    url: '/hr/attendance/planAll',
    method: 'get'
  })
}

// 查询考勤列表统计
export function countAttendanceMonth(query) {
  return request({
    url: '/hr/attendance/month/count',
    method: 'get',
    params: query
  })
}
export function listAttendanceMonth(query) {
  return request({
    url: '/hr/attendance/month',
    method: 'get',
    params: query
  })
}

export function listAttendanceLeaveList(query) {
  return request({
    url: '/hr/attendance/leaveList',
    method: 'get',
    params: query
  })
}
export function listAttendanceOutList(query) {
  return request({
    url: '/hr/attendance/outList',
    method: 'get',
    params: query
  })
}
export function listAttendanceOvertimeList(query) {
  return request({
    url: '/hr/attendance/overtimeList',
    method: 'get',
    params: query
  })
}
export function listAttendanceClockErrorList(query) {
  return request({
    url: '/hr/attendance/clockErrorList',
    method: 'get',
    params: query
  })
}
export function isAppLogAttendance(query) {
  return request({
    url: '/hr/attendance/isAppLog',
    method: 'get',
    params: query
  })
}

export function listAttendanceByUserIds(query) {
  return request({
    url: '/hr/attendance/userIds',
    method: 'get',
    params: query
  })
}
export function resultAttendance(data) {
  return request({
    url: '/hr/attendance/result',
    method: 'post',
    data: data
  })
}

export function listAttendanceLog(query) {
  return request({
    url: '/hr/attendance/log',
    method: 'get',
    params: query
  })
}

export function liteAllAttendance(query) {
  return request({
    url: '/hr/attendance/liteAll',
    method: 'get',
    params: query
  })
}

export function updateAttendanceUser(data) {
  return request({
    url: 'hr/attendance/updateAttendanceUser',
    method: 'put',
    data,
  })
}

export function updateAttendanceScheduleHour(data) {
  return request({
    url: 'hr/attendance/updateAttendanceScheduleHour',
    method: 'put',
    data,
  })
}

export function refreshAttendance(data) {
  return request({
    url: 'hr/attendance/refresh',
    method: 'post',
    data,
  })
}
export function batchRefreshAttendance(data) {
  return request({
    url: 'hr/attendance/batchRefresh',
    method: 'post',
    data,
  })
}

export function monthExportUser(query) {
  return request({
    url: '/hr/attendance/month/export/user',
    method: 'get',
    params: query
  })
}
export function monthExportLabor(query) {
  return request({
    url: '/hr/attendance/month/export/labor',
    method: 'get',
    params: query
  })
}

// 导出加班时长
export function exportOvertimeData(query) {
  return request({
    url: '/hr/attendance/exportOvertimeData',
    method: 'get',
    params: query
  })
}

// 导出日考勤信息
export function exportExportAttendanceData(query) {
  return request({
    url: '/hr/attendance/exportExportAttendanceData',
    method: 'get',
    params: query
  })
}

export function getAbsenteeismCount(userId) {
  return request({
    url: '/hr/attendance/absenteeismCount/' + userId,
    method: 'get'
  })
}

export function getLateCount(userId) {
  return request({
    url: '/hr/attendance/lateCount/' + userId,
    method: 'get'
  })
}

export function getEarlyCount(userId) {
  return request({
    url: '/hr/attendance/earlyCount/' + userId,
    method: 'get'
  })
}

export function pushHrUserAnDate(data) {
  return request({
    url: 'hr/attendance/pushHrUserAnDate',
    method: 'put',
    data,
  })
}
