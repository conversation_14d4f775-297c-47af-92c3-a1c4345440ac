import request from '@/utils/request'

// 查询应聘需求列表
export function listSysApplicationUser(query) {
  return request({
    url: '/hr/sysApplicationUser/list',
    method: 'get',
    params: query
  })
}

// 查询应聘需求列表
export function logSysApplicationUser(query) {
  return request({
    url: '/hr/sysApplicationUser/log',
    method: 'get',
    params: query
  })
}

// 查询应聘需求列表(历史记录)
export function historySysApplicationUser(query) {
  return request({
    url: '/hr/sysApplicationUser/history',
    method: 'get',
    params: query
  })
}

// 查询应聘需求列表
export function auditSysApplicationUser(data) {
  return request({
    url: '/hr/sysApplicationUser/audit',
    method: 'post',
    data: data
  })
}

// 查询应聘需求详细
export function getSysApplicationUser(id) {
  return request({
    url: '/hr/sysApplicationUser/' + id,
    method: 'get'
  })
}

//撤销审核
export function cancelAudit(data) {
  return request({
    url: '/hr/sysApplicationUser/cancelAudit',
    method: 'put',
    data: data
  })
}

// 新增应聘需求
export function addSysApplicationUser(data) {
  return request({
    url: '/hr/sysApplicationUser',
    method: 'post',
    data: data
  })
}

// 修改应聘需求
export function updateSysApplicationUser(data) {
  return request({
    url: '/hr/sysApplicationUser',
    method: 'put',
    data: data
  })
}

// 修改应聘需求
export function updateSysApplicationUserFile(data) {
  return request({
    url: '/hr/sysApplicationUser/editFile',
    method: 'put',
    data: data
  })
}

// 删除应聘需求
export function delSysApplicationUser(id) {
  return request({
    url: '/hr/sysApplicationUser/' + id,
    method: 'delete'
  })
}

// 导出应聘需求
export function exportSysApplicationUser(query) {
  return request({
    url: '/hr/sysApplicationUser/export',
    method: 'get',
    params: query
  })
}

export function allSysApplicationUser(query) {
  return request({
    url: '/hr/sysApplicationUser/all',
    method: 'get',
    params: query,
  })
}
