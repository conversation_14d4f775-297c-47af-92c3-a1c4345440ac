import request from '@/utils/request'

// 查询半成品列表
export function listSemimanufactures(query) {
  return request({
    url: '/qc/semimanufactures/list',
    method: 'get',
    params: query
  })
}

// 查询半成品详细
export function getSemimanufactures(id) {
  return request({
    url: '/qc/semimanufactures/' + id,
    method: 'get'
  })
}

// 新增半成品
export function addSemimanufactures(data) {
  return request({
    url: '/qc/semimanufactures',
    method: 'post',
    data: data
  })
}

// 修改半成品
export function updateSemimanufactures(data) {
  return request({
    url: '/qc/semimanufactures',
    method: 'put',
    data: data
  })
}

// 删除半成品
export function delSemimanufactures(id) {
  return request({
    url: '/qc/semimanufactures/' + id,
    method: 'delete'
  })
}

// 导出半成品
export function exportSemimanufactures(query) {
  return request({
    url: '/qc/semimanufactures/export',
    method: 'get',
    params: query
  })
}

// 导出半成品稳定性记录
export function exportSemimanufacturesStability(query) {
  return request({
    url: '/qc/semimanufactures/exportStability',
    method: 'get',
    params: query
  })
}

// 导出原料检测标准
export function importQcNrw(query) {
  return request({
    url: '/qc/semimanufactures/importInitData',
    method: 'get',
    params: query
  })
}


export function bcpAll(query) {
  return request({
    url: '/qc/semimanufactures/all',
    method: 'get',
    params: query
  })
}

export function getSemimanufacturesByCode(code) {
  return request({
    url: '/qc/semimanufactures/getByCode/' + code,
    method: 'get'
  })
}

export function exportBcpStandard(params) {
  return request({
    url: '/qc/semimanufactures/exportBcpStandard',
    method: 'get',
    params,
  })
}
