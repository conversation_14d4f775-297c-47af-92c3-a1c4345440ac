import request from '@/utils/request'

// 查询出库申请列表
export function listLibrary(query) {
  return request({
    url: '/order/library/list',
    method: 'get',
    params: query
  })
}

// 查询出库申请详细
export function getLibrary(id) {
  return request({
    url: '/order/library/' + id,
    method: 'get'
  })
}

// 新增出库申请
export function addLibrary(data) {
  return request({
    url: '/order/library',
    method: 'post',
    data: data
  })
}

// 修改出库申请
export function updateLibrary(data) {
  return request({
    url: '/order/library',
    method: 'put',
    data: data
  })
}

// 终止出库申请
export function stopLibrary(data) {
  return request({
    url: '/order/library/stopLibrary',
    method: 'put',
    data: data
  })
}

// 删除出库申请
export function delLibrary(id) {
  return request({
    url: '/order/library/' + id,
    method: 'delete'
  })
}

// 导出出库申请
export function exportLibrary(query) {
  return request({
    url: '/order/library/export',
    method: 'get',
    params: query
  })
}

// 导出出库申请
export function exportLibraryErp(query) {
  return request({
    url: '/order/library/exportErp',
    method: 'get',
    params: query
  })
}

// 撤销出库申请单
export function revokeLibrary(data) {
  return request({
    url: '/order/library/revoke',
    method: 'post',
    data: data
  })
}
