import request from '@/utils/request'

// 查询配置计划列表
export function listDispositionPlan(query) {
  return request({
    url: '/production/dispositionPlan/list',
    method: 'get',
    params: query
  })
}

// 查询配置计划详细
export function getDispositionPlan(id) {
  return request({
    url: '/production/dispositionPlan/' + id,
    method: 'get'
  })
}

export async function getDispositionJsonPlan(id) {
  const planRes = await request({
    url: '/production/dispositionPlan/' + id,
    method: 'get'
  })
  if(planRes.code === 200) {
    if(planRes.data.dispositionJson) {
      const dispositionJson = JSON.parse(planRes.data.dispositionJson)
      planRes.data.dispositionJson = dispositionJson
      planRes.data.sailings = dispositionJson.sailings
      planRes.data.planDate = dispositionJson.planDate
      planRes.data.startTime = dispositionJson.startTime + ':00'
      planRes.data.endTime = dispositionJson.endTime + ':00'
      planRes.data.planHour = dispositionJson.hours
      planRes.data.planPersonNums = dispositionJson.planPersonNums
    } else {
      planRes.data.dispositionJson = {}
    }
  }
  return planRes
}

export async function getWeightJsonPlan(id) {
  const planRes = await request({
    url: '/production/dispositionPlan/' + id,
    method: 'get'
  })
  if(planRes.code === 200) {
    if(planRes.data.weightJson) {
      planRes.data.weightJson = JSON.parse(planRes.data.weightJson)
    } else {
      planRes.data.weightJson = {}
    }
  }
  return planRes
}


// 新增配置计划
export function addDispositionPlan(data) {
  return request({
    url: '/production/dispositionPlan',
    method: 'post',
    data: data
  })
}

// 修改配置计划
export function updateDispositionPlan(data) {
  return request({
    url: '/production/dispositionPlan',
    method: 'put',
    data: data
  })
}

export function submitSchedulePlanHours(data) {
  return request({
    url: '/production/dispositionPlan/submitHours',
    method: 'put',
    data: data
  })
}

export function finishDispositionPlan(data) {
  return request({
    url: '/production/dispositionPlan/finish',
    method: 'put',
    data: data
  })
}


// 删除配置计划
export function delDispositionPlan(id) {
  return request({
    url: '/production/dispositionPlan/' + id,
    method: 'delete'
  })
}

// 导出配置计划
export function exportDispositionPlan(query) {
  return request({
    url: '/production/dispositionPlan/export',
    method: 'get',
    params: query
  })
}

export function allDispositionPlan(query) {
  return request({
    url: '/production/dispositionPlan/all',
    method: 'get',
    params: query
  })
}

export function resetDispositionPlanRkArray(id) {
  return request({
    url: '/production/dispositionPlan/resetRkArray/' + id,
    method: 'put',
  })
}

export function dispositionPlanSubmitAudit(data) {
  return request({
    url: '/production/dispositionPlan/submitAudit',
    method: 'put',
    data: data
  })
}

export function dispositionPlanCancelAudit(data) {
  return request({
    url: '/production/dispositionPlan/cancelAudit',
    method: 'put',
    data: data
  })
}

export function dispositionPlanSubmitChangeAudit(data) {
  return request({
    url: '/production/dispositionPlan/submitChangeAudit',
    method: 'put',
    data: data
  })
}

export function dispositionPlanCancelChangeAudit(data) {
  return request({
    url: '/production/dispositionPlan/cancelChangeAudit',
    method: 'put',
    data: data
  })
}

export function asyncDisposition() {
  return request({
    url: '/production/dispositionPlan/asyncDisposition',
    method: 'get',
  })
}

export function materialDispositionList() {
  return request({
    url: '/production/dispositionPlan/materialDispositionList',
    method: 'get',
  })
}


export function updateHourStatusDispositionPlan(data) {
  return request({
    url: '/production/dispositionPlan/editHourStatus',
    method: 'put',
    data
  })
}

export function updateConfirmStatusDispositionPlan(data) {
  return request({
    url: '/production/dispositionPlan/editConfirmStatus',
    method: 'put',
    data
  })
}

export function updateFinishedStatusDispositionPlan(data) {
  return request({
    url: '/production/dispositionPlan/editFinishedStatus',
    method: 'put',
    data
  })
}

export function updateCancelStatusDispositionPlan(data) {
  return request({
    url: '/production/dispositionPlan/editCancelStatus',
    method: 'put',
    data
  })
}

export function asyncDispositionPlanHours(data) {
  return request({
    url: '/production/dispositionPlan/asyncHours',
    method: 'put',
    data: data
  })
}

export function asyncDispositionPlanHour(data) {
  return request({
    url: '/production/dispositionPlan/asyncHour',
    method: 'put',
    data: data
  })
}
