import request from '@/utils/request'

// 查询对账其他需收费用订单商品列表
export function listGoodsOther(query) {
  return request({
    url: '/order/goodsOther/list',
    method: 'get',
    params: query
  })
}

// 查询对账其他需收费用订单商品详细
export function getGoodsOther(id) {
  return request({
    url: '/order/goodsOther/' + id,
    method: 'get'
  })
}

// 新增对账其他需收费用订单商品
export function addGoodsOther(data) {
  return request({
    url: '/order/goodsOther',
    method: 'post',
    data: data
  })
}

// 修改对账其他需收费用订单商品
export function updateGoodsOther(data) {
  return request({
    url: '/order/goodsOther',
    method: 'put',
    data: data
  })
}

// 删除对账其他需收费用订单商品
export function delGoodsOther(id) {
  return request({
    url: '/order/goodsOther/' + id,
    method: 'delete'
  })
}

// 导出对账其他需收费用订单商品
export function exportGoodsOther(query) {
  return request({
    url: '/order/goodsOther/export',
    method: 'get',
    params: query
  })
}

export function allGoodsOther(query) {
  return request({
    url: '/order/goodsOther/all',
    method: 'get',
    params: query
  })
}
