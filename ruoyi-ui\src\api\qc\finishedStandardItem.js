import request from '@/utils/request'

export function allFinishedStandardItem(query) {
  return request({
    url: '/qc/finishedStandardItem/all',
    method: 'get',
    params: query
  })
}

// 查询成品检验标准明细详细
export function getFinishedStandardItem(id) {
  return request({
    url: '/qc/finishedStandardItem/' + id,
    method: 'get'
  })
}

// 新增成品检验标准明细
export function addFinishedStandardItem(data) {
  return request({
    url: '/qc/finishedStandardItem',
    method: 'post',
    data: data
  })
}

// 修改成品检验标准明细
export function updateFinishedStandardItem(data) {
  return request({
    url: '/qc/finishedStandardItem',
    method: 'put',
    data: data
  })
}

// 删除成品检验标准明细
export function delFinishedStandardItem(id) {
  return request({
    url: '/qc/finishedStandardItem/' + id,
    method: 'delete'
  })
}

// 导出成品检验标准明细
export function exportFinishedStandardItem(query) {
  return request({
    url: '/qc/finishedStandardItem/export',
    method: 'get',
    params: query
  })
}
