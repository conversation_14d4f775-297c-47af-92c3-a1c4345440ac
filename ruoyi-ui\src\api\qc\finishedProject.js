import request from '@/utils/request'

// 查询成品检验项目列表
export function listFinishedProject(query) {
  return request({
    url: '/qc/finishedProject/list',
    method: 'get',
    params: query
  })
}

// 查询成品检验项目详细
export function getFinishedProject(id) {
  return request({
    url: '/qc/finishedProject/' + id,
    method: 'get'
  })
}

// 新增成品检验项目
export function addFinishedProject(data) {
  return request({
    url: '/qc/finishedProject',
    method: 'post',
    data: data
  })
}

// 修改成品检验项目
export function updateFinishedProject(data) {
  return request({
    url: '/qc/finishedProject',
    method: 'put',
    data: data
  })
}

// 删除成品检验项目
export function delFinishedProject(id) {
  return request({
    url: '/qc/finishedProject/' + id,
    method: 'delete'
  })
}

// 导出成品检验项目
export function exportFinishedProject(query) {
  return request({
    url: '/qc/finishedProject/export',
    method: 'get',
    params: query
  })
}