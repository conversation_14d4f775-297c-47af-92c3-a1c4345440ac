import request from '@/utils/request'

// 查询提醒消息列表
export function listProjectRemind(query) {
  return request({
    url: '/project/projectRemind/list',
    method: 'get',
    params: query
  })
}
// 修改提醒消息
export function readUserRemind(id) {
  return request({
    url: '/project/projectRemind/readUserRemind/' + id,
    method: 'put'
  })
}

export function delUserRemind(id) {
  return request({
    url: '/project/projectRemind/delUserRemind/' + id,
    method: 'put'
  })
}

export function batchReadUserRemind(types) {
  return request({
    url: '/project/projectRemind/batchReadUserRemind/' + types,
    method: 'put'
  })
}

export function batchDelUserRemind(types) {
  return request({
    url: '/project/projectRemind/batchDelUserRemind/' + types,
    method: 'put'
  })
}

export function readCount() {
  return request({
    url: '/project/projectRemind/readCount',
    method: 'get'
  })
}

export function exportProjectRemind(query) {
  return request({
    url: '/project/projectRemind/export',
    method: 'get',
    params: query
  })
}

export function taskProjectRemind(query) {
  return request({
    url: '/project/projectRemind/task',
    method: 'get',
    params: query
  })
}


export function hrUserCipher(data) {
  return request({
    url: '/hr/user/cipher',
    method: 'post',
    data
  });
}
