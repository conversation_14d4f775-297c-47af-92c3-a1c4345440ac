import request from '@/utils/request'

// 查询检测项目列表
export function listDetectCode(query) {
  return request({
    url: '/qc/detectCode/list',
    method: 'get',
    params: query
  })
}

// 查询检测项目详细
export function getDetectCode(id) {
  return request({
    url: '/qc/detectCode/' + id,
    method: 'get'
  })
}

// 新增检测项目
export function addDetectCode(data) {
  return request({
    url: '/qc/detectCode',
    method: 'post',
    data: data
  })
}

// 修改检测项目
export function updateDetectCode(data) {
  return request({
    url: '/qc/detectCode',
    method: 'put',
    data: data
  })
}

// 删除检测项目
export function delDetectCode(id) {
  return request({
    url: '/qc/detectCode/' + id,
    method: 'delete'
  })
}

// 导出检测项目
export function exportDetectCode(query) {
  return request({
    url: '/qc/detectCode/export',
    method: 'get',
    params: query
  })
}

export function allDetectCode(query) {
  return request({
    url: '/qc/detectCode/all',
    method: 'get',
    params: query
  })
}
