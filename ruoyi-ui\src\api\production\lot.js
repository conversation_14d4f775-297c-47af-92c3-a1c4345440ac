import request from '@/utils/request'

// 查询产品批号列表
export function listLot(query) {
  return request({
    url: '/production/lot/list',
    method: 'get',
    params: query
  })
}

// 查询产品批号详细
export function getLot(id) {
  return request({
    url: '/production/lot/' + id,
    method: 'get'
  })
}

// 新增产品批号
export function addLot(data) {
  return request({
    url: '/production/lot',
    method: 'post',
    data: data
  })
}

// 修改产品批号
export function updateLot(data) {
  return request({
    url: '/production/lot',
    method: 'put',
    data: data
  })
}

// 删除产品批号
export function delLot(id) {
  return request({
    url: '/production/lot/' + id,
    method: 'delete'
  })
}

// 导出产品批号
export function exportLot(query) {
  return request({
    url: '/production/lot/export',
    method: 'get',
    params: query
  })
}

export function allLot(query) {
  return request({
    url: '/production/lot/all',
    method: 'get',
    params: query
  })
}
