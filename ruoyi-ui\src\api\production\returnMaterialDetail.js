import request from '@/utils/request'

// 查询退料单申请明细列表
export function listReturnMaterialDetail(query) {
  return request({
    url: '/production/returnMaterialDetail/list',
    method: 'get',
    params: query
  })
}

// 查询退料单申请明细列表
export function listReturnMaterialDetailAll(query) {
  return request({
    url: '/production/returnMaterialDetail/listAll',
    method: 'get',
    params: query
  })
}

// 查询退料单申请明细详细
export function getReturnMaterialDetail(id) {
  return request({
    url: '/production/returnMaterialDetail/' + id,
    method: 'get'
  })
}

// 新增退料单申请明细
export function addReturnMaterialDetail(data) {
  return request({
    url: '/production/returnMaterialDetail',
    method: 'post',
    data: data
  })
}

// 修改退料单申请明细
export function updateReturnMaterialDetail(data) {
  return request({
    url: '/production/returnMaterialDetail',
    method: 'put',
    data: data
  })
}

// 删除退料单申请明细
export function delReturnMaterialDetail(id) {
  return request({
    url: '/production/returnMaterialDetail/' + id,
    method: 'delete'
  })
}

// 导出退料单申请明细
export function exportReturnMaterialDetail(query) {
  return request({
    url: '/production/returnMaterialDetail/export',
    method: 'get',
    params: query
  })
}
