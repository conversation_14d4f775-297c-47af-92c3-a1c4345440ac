import request from '@/utils/request'

export function selectedListScheduleStandard(query) {
  return request({
    url: '/order/scheduleStandard/selectedList',
    method: 'get',
    params: query
  })
}

// 查询标准工时产出率列表
export function listScheduleStandard(query) {
  return request({
    url: '/order/scheduleStandard/list',
    method: 'get',
    params: query
  })
}

// 查询标准工时产出率详细
export function getScheduleStandard(id) {
  return request({
    url: '/order/scheduleStandard/' + id,
    method: 'get'
  })
}

export function getScheduleStandardByCode(code) {
  return request({
    url: '/order/scheduleStandard/getByCode/' + code,
    method: 'get'
  })
}

// 新增标准工时产出率
export function addScheduleStandard(data) {
  return request({
    url: '/order/scheduleStandard',
    method: 'post',
    data: data
  })
}

// 修改标准工时产出率
export function updateScheduleStandard(data) {
  return request({
    url: '/order/scheduleStandard',
    method: 'put',
    data: data
  })
}

export function saveOrUpdateScheduleStandard(data) {
  return request({
    url: '/order/scheduleStandard/saveOrUpdate',
    method: 'post',
    data: data
  })
}

// 删除标准工时产出率
export function delScheduleStandard(id) {
  return request({
    url: '/order/scheduleStandard/' + id,
    method: 'delete'
  })
}

// 导出标准工时产出率
export function exportScheduleStandard(query) {
  return request({
    url: '/order/scheduleStandard/export',
    method: 'get',
    params: query
  })
}

export function batchSaveScheduleStandard(data) {
  return request({
    url: '/order/scheduleStandard/batchSave',
    method: 'post',
    data: data
  })
}

export function refreshBomType() {
  return request({
    url: '/order/scheduleStandard/refreshBomType',
    method: 'get'
  })
}

export function allScheduleStandard(query) {
  return request({
    url: '/order/scheduleStandard/all',
    method: 'get',
    params: query
  })
}
