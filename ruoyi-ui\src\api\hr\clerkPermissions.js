import request from '@/utils/request'

// 查询文员代办权限列表
export function listClerkPermissions(query) {
  return request({
    url: '/hr/clerkPermissions/list',
    method: 'get',
    params: query
  })
}

// 查询文员代办权限详细
export function getClerkPermissions(id) {
  return request({
    url: '/hr/clerkPermissions/' + id,
    method: 'get'
  })
}

// 新增文员代办权限
export function addClerkPermissions(data) {
  return request({
    url: '/hr/clerkPermissions',
    method: 'post',
    data: data
  })
}

// 修改文员代办权限
export function updateClerkPermissions(data) {
  return request({
    url: '/hr/clerkPermissions',
    method: 'put',
    data: data
  })
}

// 删除文员代办权限
export function delClerkPermissions(id) {
  return request({
    url: '/hr/clerkPermissions/' + id,
    method: 'delete'
  })
}

// 导出文员代办权限
export function exportClerkPermissions(query) {
  return request({
    url: '/hr/clerkPermissions/export',
    method: 'get',
    params: query
  })
}
