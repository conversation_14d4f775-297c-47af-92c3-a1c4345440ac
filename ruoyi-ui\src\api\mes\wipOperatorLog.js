import request from '@/utils/request'

// 查询操作记录列表
export function listWipOperatorLog(query) {
  return request({
    url: '/mes/wipOperatorLog/list',
    method: 'get',
    params: query
  })
}

// 查询操作记录详细
export function getWipOperatorLog(sid) {
  return request({
    url: '/mes/wipOperatorLog/' + sid,
    method: 'get'
  })
}

// 新增操作记录
export function addWipOperatorLog(data) {
  return request({
    url: '/mes/wipOperatorLog',
    method: 'post',
    data: data
  })
}

// 修改操作记录
export function updateWipOperatorLog(data) {
  return request({
    url: '/mes/wipOperatorLog',
    method: 'put',
    data: data
  })
}

// 删除操作记录
export function delWipOperatorLog(sid) {
  return request({
    url: '/mes/wipOperatorLog/' + sid,
    method: 'delete'
  })
}

// 导出操作记录
export function exportWipOperatorLog(query) {
  return request({
    url: '/mes/wipOperatorLog/export',
    method: 'get',
    params: query
  })
}
