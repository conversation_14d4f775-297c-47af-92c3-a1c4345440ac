import request from '@/utils/request'

// 查询成品检验记录列表
export function listFinishedInspection(query) {
  return request({
    url: '/qc/finishedInspection/list',
    method: 'get',
    params: query
  })
}

// 查询成品检验记录详细
export function getFinishedInspection(id) {
  return request({
    url: '/qc/finishedInspection/' + id,
    method: 'get'
  })
}

// 查询成品检验记录详细
export function getQualityStatisticsData(id) {
  return request({
    url: '/qc/finishedInspection/getQualityStatisticsData',
    method: 'get'
  })
}


// 查询品质部台账数据提醒
export function getQualityTzStatisticsData(id) {
  return request({
    url: '/qc/finishedInspection/getQualityTzStatisticsData',
    method: 'get'
  })
}


// 查询成品检验记录详细
export function getFinishedInspectionDetail(id) {
  return request({
    url: '/qc/finishedInspection/detail/' + id,
    method: 'get'
  })
}

// 新增成品检验记录
export function addFinishedInspection(data) {
  return request({
    url: '/qc/finishedInspection',
    method: 'post',
    data: data
  })
}

// 修改成品检验记录
export function updateFinishedInspection(data) {
  return request({
    url: '/qc/finishedInspection',
    method: 'put',
    data: data
  })
}

// 删除成品检验记录
export function delFinishedInspection(id) {
  return request({
    url: '/qc/finishedInspection/' + id,
    method: 'delete'
  })
}

// 导出成品检验记录
export function exportFinishedInspection(id) {
  return request({
    url: '/qc/finishedInspection/export/' + id,
    method: 'get',
  })
}

export function finishedInspectionSubmitAudit(data) {
  return request({
    url: '/qc/finishedInspection/submitAudit',
    method: 'put',
    data: data
  })
}

export function finishedInspectionCancelAudit(data) {
  return request({
    url: '/qc/finishedInspection/cancelAudit',
    method: 'put',
    data: data
  })
}

export function exportFinishedGoodsFxd(id) {
  return request({
    url: '/qc/finishedInspection/exportFinishedGoodsFxd/' + id,
    method: 'get',
  })
}

export function exportFactoryFinishedGoods(query) {
  return request({
    url: '/qc/finishedInspection/exportFactory',
    method: 'get',
    params: query,
  })
}

export function finishedInspectionActHistoricTaskInstanceList(instanceId) {
  return request({
    url: '/qc/finishedInspection/actHistoricTaskInstanceList/' + instanceId,
    method: 'get'
  })
}

export function finishedInspectionCancelAll() {
  return request({
    url: '/qc/finishedInspection/cancelAll',
    method: 'put',
  })
}

export function refreshWarehouseRecord(query) {
  return request({
    url: '/qc/finishedInspection/refreshWarehouseRecord',
    method: 'get',
    params: query
  })
}

export function batchExportQcMaterialTest(query) {
  return request({
    url: '/qc/finishedInspection/batchExport',
    method: 'get',
    params: query
  })
}

export function allFinishedInspection(query) {
  return request({
    url: '/qc/finishedInspection/all',
    method: 'get',
    params: query
  })
}

export function allGroupByBatchFinishedInspection(query) {
  return request({
    url: '/qc/finishedInspection/allGroupByBatch',
    method: 'get',
    params: query
  })
}
