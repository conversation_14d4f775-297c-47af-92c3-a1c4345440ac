import request from '@/utils/request'

// 查询客户资金日志列表
export function listBalanceLog(query) {
  return request({
    url: '/customer/balanceLog/list',
    method: 'get',
    params: query
  })
}

// 查询客户资金日志详细
export function getBalanceLog(id) {
  return request({
    url: '/customer/balanceLog/' + id,
    method: 'get'
  })
}

// 查询客户资金日志详细
export function getCustomerAccountInfo(query) {
  return request({
    url: '/customer/balanceLog/getCustomerAccountInfo',
    method: 'get',
    params: query
  })
}

// 新增客户资金日志
export function addBalanceLog(data) {
  return request({
    url: '/customer/balanceLog',
    method: 'post',
    data: data
  })
}

// 修改客户资金日志
export function updateBalanceLog(data) {
  return request({
    url: '/customer/balanceLog',
    method: 'put',
    data: data
  })
}

// 删除客户资金日志
export function delBalanceLog(id) {
  return request({
    url: '/customer/balanceLog/' + id,
    method: 'delete'
  })
}

// 导出客户资金日志
export function exportBalanceLog(query) {
  return request({
    url: '/customer/balanceLog/export',
    method: 'get',
    params: query
  })
}
