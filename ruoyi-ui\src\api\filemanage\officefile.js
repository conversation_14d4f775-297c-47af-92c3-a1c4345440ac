import request from '@/utils/request'

// 查询文件系统列表
export function listFile(query) {
  return request({
    url: '/filemanage/officefile/list',
    method: 'get',
    params: query
  })
}

export function queryBusinessFile(query) {
  return request({
    url: '/filemanage/officefile/queryBusinessFile',
    method: 'get',
    params: query
  })
}

export function historyBusinessFile(query) {
  return request({
    url: '/filemanage/officefile/historyBusinessFile',
    method: 'get',
    params: query
  })
}

// 查询文件系统详细
export function getOfficeFile(fileId) {
  return request({
    url: '/filemanage/officefile/' + fileId,
    method: 'get'
  })
}

// 新增文件系统
export function addOfficeFile(data) {
  return request({
    url: '/filemanage/officefile',
    method: 'post',
    data: data
  })
}

// 修改文件系统
export function updateFile(data) {
  return request({
    url: '/filemanage/officefile',
    method: 'put',
    data: data
  })
}

// 删除文件系统
export function delFile(fileId) {
  return request({
    url: '/filemanage/officefile/' + fileId,
    method: 'delete'
  })
}

// 导出文件系统
export function exportFile(query) {
  return request({
    url: '/filemanage/officefile/export',
    method: 'get',
    params: query
  })
}

export function getFileStamp(query) {
  return request({
    url: '/filemanage/officefile/getFileStamp',
    method: 'get',
    params: query
  })
}

export function uploadPdf(query) {
  return request({
    url: '/filemanage/officefile/uploadPdf',
    method: 'put',
    params: query
  })
}

export function setPermission(data) {
  return request({
    url: '/filemanage/officefile/handlePermission',
    method: 'post',
    data: data
  })
}
