import request from '@/utils/request'

// 查询bom变更列表
export function listBomChange(query) {
  return request({
    url: '/sop/bomChange/list',
    method: 'get',
    params: query
  })
}

export function applyListBomChange(query) {
  return request({
    url: '/sop/bomChange/applyList',
    method: 'get',
    params: query
  })
}

export function unErpListBomChange(query) {
  return request({
    url: '/sop/bomChange/unErpList',
    method: 'get',
    params: query
  })
}

// 查询bom变更详细
export function getBomChange(id) {
  return request({
    url: '/sop/bomChange/' + id,
    method: 'get'
  })
}

// 新增bom变更
export function addBomChange(data) {
  return request({
    url: '/sop/bomChange',
    method: 'post',
    data: data
  })
}

// 修改bom变更
export function updateBomChange(data) {
  return request({
    url: '/sop/bomChange',
    method: 'put',
    data: data
  })
}

export function updateAuditBomChange(data) {
  return request({
    url: '/sop/bomChange/editAudit',
    method: 'put',
    data: data
  })
}

export function confirmErp(id) {
  return request({
    url: '/sop/bomChange/confirmErp/' + id,
    method: 'put',
  })
}

export function rejectBomChange(data) {
  return request({
    url: '/sop/bomChange/rejectBom',
    method: 'put',
    data,
  })
}

// 删除bom变更
export function delBomChange(id) {
  return request({
    url: '/sop/bomChange/' + id,
    method: 'delete'
  })
}

// 导出bom变更
export function exportBomChange(query) {
  return request({
    url: '/sop/bomChange/export',
    method: 'get',
    params: query
  })
}

export function submitAudit(data) {
  return request({
    url: '/sop/bomChange/submitAudit',
    method: 'put',
    data: data
  })
}

//撤销申请
export function cancelAudit(data) {
  return request({
    url: '/sop/bomChange/cancelAudit',
    method: 'put',
    data: data
  })
}

export function exemptAudit(instanceId) {
  return request({
    url: '/sop/bomChange/exemptAudit/' + instanceId,
    method: 'put',
  })
}
