import request from '@/utils/request'

// 查询采购申请明细列表
export function listMaterialPurchaseUserItem(query) {
  return request({
    url: '/process/materialPurchaseUserItem/list',
    method: 'get',
    params: query
  })
}

export function unBindSupplierPurchaseItemList(query) {
  return request({
    url: '/process/materialPurchaseUserItem/unBindSupplierPurchaseItemList',
    method: 'get',
    params: query
  })
}

export function allMaterialPurchaseUserItem(query) {
  return request({
    url: '/process/materialPurchaseUserItem/all',
    method: 'get',
    params: query
  })
}

export function allSignMaterialPurchaseUserItem(query) {
  return request({
    url: '/process/materialPurchaseUserItem/allSignList',
    method: 'get',
    params: query
  })
}

// 查询采购申请明细详细
export function getMaterialPurchaseUserItem(id) {
  return request({
    url: '/process/materialPurchaseUserItem/' + id,
    method: 'get'
  })
}

// 新增采购申请明细
export function addMaterialPurchaseUserItem(data) {
  return request({
    url: '/process/materialPurchaseUserItem',
    method: 'post',
    data: data
  })
}

// 修改采购申请明细
export function updateMaterialPurchaseUserItem(data) {
  return request({
    url: '/process/materialPurchaseUserItem',
    method: 'put',
    data: data
  })
}

// 删除采购申请明细
export function delMaterialPurchaseUserItem(id) {
  return request({
    url: '/process/materialPurchaseUserItem/' + id,
    method: 'delete'
  })
}

// 导出采购申请明细
export function exportMaterialPurchaseUserItem(query) {
  return request({
    url: '/process/materialPurchaseUserItem/export',
    method: 'get',
    params: query
  })
}
