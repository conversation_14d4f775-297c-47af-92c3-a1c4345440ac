{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\project\\project\\makeBom.vue?vue&type=template&id=72d7a4dc&scoped=true", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\project\\project\\makeBom.vue", "mtime": 1753954679647}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1744596530059}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}