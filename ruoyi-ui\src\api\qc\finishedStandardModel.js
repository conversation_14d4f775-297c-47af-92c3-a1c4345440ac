import request from '@/utils/request'

// 查询成品标准类型列表
export function listFinishedStandardModel(query) {
  return request({
    url: '/qc/finishedStandardModel/list',
    method: 'get',
    params: query
  })
}

// 查询成品标准类型详细
export function getFinishedStandardModel(id) {
  return request({
    url: '/qc/finishedStandardModel/' + id,
    method: 'get'
  })
}

// 新增成品标准类型
export function addFinishedStandardModel(data) {
  return request({
    url: '/qc/finishedStandardModel',
    method: 'post',
    data: data
  })
}

// 修改成品标准类型
export function updateFinishedStandardModel(data) {
  return request({
    url: '/qc/finishedStandardModel',
    method: 'put',
    data: data
  })
}

// 删除成品标准类型
export function delFinishedStandardModel(id) {
  return request({
    url: '/qc/finishedStandardModel/' + id,
    method: 'delete'
  })
}

// 导出成品标准类型
export function exportFinishedStandardModel(query) {
  return request({
    url: '/qc/finishedStandardModel/export',
    method: 'get',
    params: query
  })
}

export function allFinishedStandardModel(query) {
  return request({
    url: '/qc/finishedStandardModel/all',
    method: 'get',
    params: query
  })
}
