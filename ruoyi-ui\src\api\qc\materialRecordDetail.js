import request from '@/utils/request'

// 查询原料检验记录-详情列表
export function listMaterialRecordDetail(query) {
  return request({
    url: '/qc/materialRecordDetail/list',
    method: 'get',
    params: query
  })
}

// 查询原料检验记录-详情详细
export function getMaterialRecordDetail(id) {
  return request({
    url: '/qc/materialRecordDetail/' + id,
    method: 'get'
  })
}

// 新增原料检验记录-详情
export function addMaterialRecordDetail(data) {
  return request({
    url: '/qc/materialRecordDetail',
    method: 'post',
    data: data
  })
}

// 修改原料检验记录-详情
export function updateMaterialRecordDetail(data) {
  return request({
    url: '/qc/materialRecordDetail',
    method: 'put',
    data: data
  })
}

// 删除原料检验记录-详情
export function delMaterialRecordDetail(id) {
  return request({
    url: '/qc/materialRecordDetail/' + id,
    method: 'delete'
  })
}

// 导出原料检验记录-详情
export function exportMaterialRecordDetail(query) {
  return request({
    url: '/qc/materialRecordDetail/export',
    method: 'get',
    params: query
  })
}


export function materialRecordDetailAll(query) {
  return request({
    url: '/qc/materialRecordDetail/all',
    method: 'get',
    params: query
  })
}
