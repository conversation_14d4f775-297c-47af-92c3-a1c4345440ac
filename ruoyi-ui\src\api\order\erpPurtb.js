import request from '@/utils/request'

// 查询请购单单身信息档列表
export function listErpPurtb(query) {
  return request({
    url: '/order/erpPurtb/list',
    method: 'get',
    params: query
  })
}

// 查询请购单单身信息档列表
export function listErpPurtbMerge(query) {
  return request({
    url: '/order/erpPurtb/listMerge',
    method: 'get',
    params: query
  })
}

// 查询请购单单身信息档详细
export function getErpPurtb(id) {
  return request({
    url: '/order/erpPurtb/' + id,
    method: 'get'
  })
}

// 新增请购单单身信息档
export function addErpPurtb(data) {
  return request({
    url: '/order/erpPurtb',
    method: 'post',
    data: data
  })
}

// 修改请购单单身信息档
export function updateErpPurtb(data) {
  return request({
    url: '/order/erpPurtb',
    method: 'put',
    data: data
  })
}

// 删除请购单单身信息档
export function delErpPurtb(id) {
  return request({
    url: '/order/erpPurtb/' + id,
    method: 'delete'
  })
}

// 导出请购单单身信息档
export function exportErpPurtb(query) {
  return request({
    url: '/order/erpPurtb/export',
    method: 'get',
    params: query
  })
}

//同步数据
export function syncOrderPurtbData(query) {
  return request({
    url: '/order/erpPurtb/syncOrderPurtbData',
    method: 'get',
    params: query
  })
}
