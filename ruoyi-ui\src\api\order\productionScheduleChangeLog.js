import request from '@/utils/request'

// 查询工单时间变化记录列表
export function listProductionScheduleChangeLog(query) {
  return request({
    url: '/order/productionScheduleChangeLog/list',
    method: 'get',
    params: query
  })
}

// 查询工单时间变化记录详细
export function getProductionScheduleChangeLog(id) {
  return request({
    url: '/order/productionScheduleChangeLog/' + id,
    method: 'get'
  })
}

// 新增工单时间变化记录
export function addProductionScheduleChangeLog(data) {
  return request({
    url: '/order/productionScheduleChangeLog',
    method: 'post',
    data: data
  })
}

// 修改工单时间变化记录
export function updateProductionScheduleChangeLog(data) {
  return request({
    url: '/order/productionScheduleChangeLog',
    method: 'put',
    data: data
  })
}

// 删除工单时间变化记录
export function delProductionScheduleChangeLog(id) {
  return request({
    url: '/order/productionScheduleChangeLog/' + id,
    method: 'delete'
  })
}

// 导出工单时间变化记录
export function exportProductionScheduleChangeLog(query) {
  return request({
    url: '/order/productionScheduleChangeLog/export',
    method: 'get',
    params: query
  })
}