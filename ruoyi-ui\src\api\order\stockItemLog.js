import request from '@/utils/request'

export function listStockItemLog(query) {
  return request({
    url: '/order/stockItemLog/list',
    method: 'get',
    params: query
  })
}

// 查询订单出库记录详细
export function getStockItemLog(id) {
  return request({
    url: '/order/stockItemLog/' + id,
    method: 'get'
  })
}

// 新增订单出库记录
export function addStockItemLog(data) {
  return request({
    url: '/order/stockItemLog',
    method: 'post',
    data: data
  })
}

// 修改订单出库记录
export function updateStockItemLog(data) {
  return request({
    url: '/order/stockItemLog',
    method: 'put',
    data: data
  })
}

// 删除订单出库记录
export function delStockItemLog(id) {
  return request({
    url: '/order/stockItemLog/' + id,
    method: 'delete'
  })
}

// 导出订单出库记录
export function exportStockItemLog(query) {
  return request({
    url: '/order/stockItemLog/export',
    method: 'get',
    params: query
  })
}

export function allStockItemLog(query) {
  return request({
    url: '/order/stockItemLog/all',
    method: 'get',
    params: query
  })
}

export function allUnDzStockItemLog(query) {
  return request({
    url: '/order/stockItemLog/allUnDz',
    method: 'get',
    params: query
  })
}

export function listFreightLog(query) {
  return request({
    url: '/order/stockItemLog/freightList',
    method: 'get',
    params: query
  })
}
