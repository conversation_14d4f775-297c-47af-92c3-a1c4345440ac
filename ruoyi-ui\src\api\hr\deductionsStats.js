import request from '@/utils/request'

// 查询考勤扣款统计列表
export function listDeductionsStats(query) {
  return request({
    url: '/hr/deductionsStats/list',
    method: 'get',
    params: query
  })
}

// 查询考勤扣款统计详细
export function getDeductionsStats(id) {
  return request({
    url: '/hr/deductionsStats/' + id,
    method: 'get'
  })
}

// 新增考勤扣款统计
export function addDeductionsStats(data) {
  return request({
    url: '/hr/deductionsStats',
    method: 'post',
    data: data
  })
}

// 修改考勤扣款统计
export function updateDeductionsStats(data) {
  return request({
    url: '/hr/deductionsStats',
    method: 'put',
    data: data
  })
}

// 删除考勤扣款统计
export function delDeductionsStats(id) {
  return request({
    url: '/hr/deductionsStats/' + id,
    method: 'delete'
  })
}

// 导出考勤扣款统计
export function exportDeductionsStats(query) {
  return request({
    url: '/hr/deductionsStats/export',
    method: 'get',
    params: query
  })
}
