import request from '@/utils/request'

// 查询对账列表
export function listReconcilia(query) {
  return request({
    url: '/order/reconcilia/list',
    method: 'get',
    params: query
  })
}

// 查询对账详细
export function getReconcilia(id) {
  return request({
    url: '/order/reconcilia/' + id,
    method: 'get'
  })
}

// 新增对账
export function addReconcilia(data) {
  return request({
    url: '/order/reconcilia',
    method: 'post',
    data: data
  })
}

// 修改对账
export function updateReconcilia(data) {
  return request({
    url: '/order/reconcilia',
    method: 'put',
    data: data
  })
}

// 供应链初审驳回,跟单确认驳回
export function rejectReconcilia(data) {
  return request({
    url: '/order/reconcilia/reject',
    method: 'put',
    data: data
  })
}

//复审通过
export function confirmRconcilia(data) {
  return request({
    url: '/order/reconcilia/confirmRconcilia',
    method: 'put',
    data: data
  })
}

// 删除对账
export function delReconcilia(id) {
  return request({
    url: '/order/reconcilia/' + id,
    method: 'delete'
  })
}

// 导出对账
export function exportReconcilia(query) {
  return request({
    url: '/order/reconcilia/export',
    method: 'get',
    params: query
  })
}

export function reconciliaExport(query) {
  return request({
    url: '/order/reconcilia/exportReconcilia',
    method: 'get',
    params: query
  })
}

export function finishReconcilia(data) {
  return request({
    url: '/order/reconcilia/finish',
    method: 'put',
    data: data
  })
}

export function allReconcilia(query) {
  return request({
    url: '/order/reconcilia/all',
    method: 'get',
    params: query
  })
}

export function allOtherReconciliaData(query) {
  return request({
    url: '/order/reconcilia/allOtherReconciliaData',
    method: 'get',
    params: query
  })
}

export function allOtherGoodsReconciliaData(query) {
  return request({
    url: '/order/reconcilia/allOtherGoodsReconciliaData',
    method: 'get',
    params: query
  })
}

// 撤销子项目订单
export function revokeReconcilia(data) {
  return request({
    url: '/order/reconcilia/revoke',
    method: 'post',
    data: data
  })
}

export function allBillingReconcilia(query) {
  return request({
    url: '/order/reconcilia/allBilling',
    method: 'get',
    params: query
  })
}

export function finishedRevoke(data) {
  return request({
    url: '/order/reconcilia/finishedRevoke',
    method: 'post',
    data: data
  })
}

export function listRemindReconciliaBalance(query) {
  return request({
    url: '/order/reconcilia/remindReconciliaBalanceList',
    method: 'get',
    params: query
  })
}

export function listRemindReconciliaBilling(query) {
  return request({
    url: '/order/reconcilia/remindReconciliaBillingList',
    method: 'get',
    params: query
  })
}

export function listRemindReconciliaSign(query) {
  return request({
    url: '/order/reconcilia/remindReconciliaSignList',
    method: 'get',
    params: query
  })
}
