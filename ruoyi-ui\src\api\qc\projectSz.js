import request from '@/utils/request'

// 查询失重测试列表
export function listProjectSz(query) {
  return request({
    url: '/qc/projectSz/list',
    method: 'get',
    params: query
  })
}

// 查询失重测试详细
export function getProjectSz(id) {
  return request({
    url: '/qc/projectSz/' + id,
    method: 'get'
  })
}

// 新增失重测试
export function addProjectSz(data) {
  return request({
    url: '/qc/projectSz',
    method: 'post',
    data: data
  })
}

// 修改失重测试
export function updateProjectSz(data) {
  return request({
    url: '/qc/projectSz',
    method: 'put',
    data: data
  })
}

// 删除失重测试
export function delProjectSz(id) {
  return request({
    url: '/qc/projectSz/' + id,
    method: 'delete'
  })
}

// 导出失重测试
export function exportProjectSz(query) {
  return request({
    url: '/qc/projectSz/export',
    method: 'get',
    params: query
  })
}

export function allProjectSz(query) {
  return request({
    url: '/qc/projectSz/all',
    method: 'get',
    params: query
  })
}
