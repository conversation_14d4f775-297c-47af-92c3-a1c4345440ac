import request from '@/utils/request'

// 查询生产入库记录列表
export function listRkLog(query) {
  return request({
    url: '/production/rkLog/list',
    method: 'get',
    params: query
  })
}

// 查询erp入库记录数据
export function queryErpReceiptRelationData(query) {
  return request({
    url: '/production/rkLog/queryErpReceiptRelationData',
    method: 'get',
    params: query
  })
}

// 关联erp入库单号
export function updateErpReceiptRelationData(data) {
  return request({
    url: '/production/rkLog/updateErpReceiptRelationData',
    method: 'put',
    data: data
  })
}


// 查询生产入库记录详细
export function getRkLog(query) {
  return request({
    url: '/production/rkLog',
    method: 'get',
    params: query
  })
}

// 新增生产入库记录
export function addRkLog(data) {
  return request({
    url: '/production/rkLog',
    method: 'post',
    data: data
  })
}

// 修改生产入库记录
export function updateRkLog(data) {
  return request({
    url: '/production/rkLog',
    method: 'put',
    data: data
  })
}

// 删除生产入库记录
export function delRkLog(id) {
  return request({
    url: '/production/rkLog/' + id,
    method: 'delete'
  })
}

// 导出生产入库记录
export function exportRkLog(query) {
  return request({
    url: '/production/rkLog/export',
    method: 'get',
    params: query
  })
}

export function allRkLog(query) {
  return request({
    url: '/production/rkLog/all',
    method: 'get',
    params: query
  })
}
