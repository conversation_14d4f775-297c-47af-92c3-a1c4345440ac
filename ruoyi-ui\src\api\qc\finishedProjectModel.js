import request from '@/utils/request'

// 查询成品检验项目模板列表
export function listFinishedProjectModel(query) {
  return request({
    url: '/qc/finishedProjectModel/list',
    method: 'get',
    params: query
  })
}

// 查询成品检验项目模板详细
export function getFinishedProjectModel(id) {
  return request({
    url: '/qc/finishedProjectModel/' + id,
    method: 'get'
  })
}

// 新增成品检验项目模板
export function addFinishedProjectModel(data) {
  return request({
    url: '/qc/finishedProjectModel',
    method: 'post',
    data: data
  })
}

// 修改成品检验项目模板
export function updateFinishedProjectModel(data) {
  return request({
    url: '/qc/finishedProjectModel',
    method: 'put',
    data: data
  })
}

// 删除成品检验项目模板
export function delFinishedProjectModel(id) {
  return request({
    url: '/qc/finishedProjectModel/' + id,
    method: 'delete'
  })
}

// 导出成品检验项目模板
export function exportFinishedProjectModel(query) {
  return request({
    url: '/qc/finishedProjectModel/export',
    method: 'get',
    params: query
  })
}

export function allFinishedProjectModel(query) {
  return request({
    url: '/qc/finishedProjectModel/all',
    method: 'get',
    params: query
  })
}
