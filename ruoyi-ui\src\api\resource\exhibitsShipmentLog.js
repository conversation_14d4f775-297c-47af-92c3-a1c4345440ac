import request from '@/utils/request'

// 查询展厅快递列表
export function listExhibitsShipmentLog(query) {
  return request({
    url: '/resource/exhibitsShipmentLog/list',
    method: 'get',
    params: query
  })
}

// 查询展厅快递详细
export function getExhibitsShipmentLog(id) {
  return request({
    url: '/resource/exhibitsShipmentLog/' + id,
    method: 'get'
  })
}

// 新增展厅快递
export function addExhibitsShipmentLog(data) {
  return request({
    url: '/resource/exhibitsShipmentLog',
    method: 'post',
    data: data
  })
}

// 修改展厅快递
export function updateExhibitsShipmentLog(data) {
  return request({
    url: '/resource/exhibitsShipmentLog',
    method: 'put',
    data: data
  })
}

// 删除展厅快递
export function delExhibitsShipmentLog(id) {
  return request({
    url: '/resource/exhibitsShipmentLog/' + id,
    method: 'delete'
  })
}

// 导出展厅快递
export function exportExhibitsShipmentLog(query) {
  return request({
    url: '/resource/exhibitsShipmentLog/export',
    method: 'get',
    params: query
  })
}