import request from '@/utils/request'

// 查询包材供应商价格记录列表
export function listMaterialSupplierPriceLog(query) {
  return request({
    url: '/resource/materialSupplierPriceLog/list',
    method: 'get',
    params: query
  })
}

// 查询包材供应商价格记录详细
export function getMaterialSupplierPriceLog(id) {
  return request({
    url: '/resource/materialSupplierPriceLog/' + id,
    method: 'get'
  })
}

// 新增包材供应商价格记录
export function addMaterialSupplierPriceLog(data) {
  return request({
    url: '/resource/materialSupplierPriceLog',
    method: 'post',
    data: data
  })
}

// 修改包材供应商价格记录
export function updateMaterialSupplierPriceLog(data) {
  return request({
    url: '/resource/materialSupplierPriceLog',
    method: 'put',
    data: data
  })
}

// 删除包材供应商价格记录
export function delMaterialSupplierPriceLog(id) {
  return request({
    url: '/resource/materialSupplierPriceLog/' + id,
    method: 'delete'
  })
}

// 导出包材供应商价格记录
export function exportMaterialSupplierPriceLog(query) {
  return request({
    url: '/resource/materialSupplierPriceLog/export',
    method: 'get',
    params: query
  })
}