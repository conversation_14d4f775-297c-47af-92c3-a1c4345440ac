import request from '@/utils/request'

// 查询温湿度记录列表
export function listWsd(query) {
  return request({
    url: '/production/wsd/list',
    method: 'get',
    params: query
  })
}

// 查询温湿度记录详细
export function getWsd(id) {
  return request({
    url: '/production/wsd/' + id,
    method: 'get'
  })
}

// 新增温湿度记录
export function addWsd(data) {
  return request({
    url: '/production/wsd',
    method: 'post',
    data: data
  })
}

// 修改温湿度记录
export function updateWsd(data) {
  return request({
    url: '/production/wsd',
    method: 'put',
    data: data
  })
}

// 删除温湿度记录
export function delWsd(id) {
  return request({
    url: '/production/wsd/' + id,
    method: 'delete'
  })
}

// 导出温湿度记录
export function exportWsd(query) {
  return request({
    url: '/production/wsd/export',
    method: 'get',
    params: query
  })
}