import request from '@/utils/request'

// 查询津贴方案列表
export function listAllowance(query) {
  return request({
    url: '/hr/allowance/list',
    method: 'get',
    params: query
  })
}

// 查询津贴方案详细
export function getAllowance(id) {
  return request({
    url: '/hr/allowance/' + id,
    method: 'get'
  })
}

// 新增津贴方案
export function addAllowance(data) {
  return request({
    url: '/hr/allowance',
    method: 'post',
    data: data
  })
}

// 修改津贴方案
export function updateAllowance(data) {
  return request({
    url: '/hr/allowance',
    method: 'put',
    data: data
  })
}

// 删除津贴方案
export function delAllowance(id) {
  return request({
    url: '/hr/allowance/' + id,
    method: 'delete'
  })
}

// 导出津贴方案
export function exportAllowance(query) {
  return request({
    url: '/hr/allowance/export',
    method: 'get',
    params: query
  })
}
