import request from '@/utils/request'

// 查询半成品检验记录列表
export function listBcpInspection(query) {
  return request({
    url: '/qc/bcpInspection/list',
    method: 'get',
    params: query
  })
}

// 查询半成品检验记录详细
export function getBcpInspection(id) {
  return request({
    url: '/qc/bcpInspection/' + id,
    method: 'get'
  })
}

// 新增半成品检验记录
export function addBcpInspection(data) {
  return request({
    url: '/qc/bcpInspection',
    method: 'post',
    data: data
  })
}

// 修改半成品检验记录
export function updateBcpInspection(data) {
  return request({
    url: '/qc/bcpInspection',
    method: 'put',
    data: data
  })
}

// 删除半成品检验记录
export function delBcpInspection(id) {
  return request({
    url: '/qc/bcpInspection/' + id,
    method: 'delete'
  })
}

export function exportBcpInspectionBg(id) {
  return request({
    url: '/qc/bcpInspection/exportBcpInspectionBg/' + id,
    method: 'get',
  })
}

export function exportBcpInspectionJl(id) {
  return request({
    url: '/qc/bcpInspection/exportBcpInspectionJl/' + id,
    method: 'get',
  })
}

export function exportBcpInspectionAll(query) {
  return request({
    url: '/qc/bcpInspection/exportAll',
    method: 'get',
    params: query,
  })
}

export function asyncBcpInspection() {
  return request({
    url: '/qc/bcpInspection/asyncInspection',
    method: 'get'
  })
}

export function asyncBcpInspectionItem() {
  return request({
    url: '/qc/bcpInspection/asyncBcpInspectionItem',
    method: 'get'
  })
}

export function getBcpInspectionByParams(query) {
  return request({
    url: '/qc/bcpInspection/getByParams',
    method: 'get',
    params: query
  })
}

export function getListBcpInspectionByErp(query) {
  return request({
    url: '/qc/bcpInspection/getListByErp',
    method: 'get',
    params: query
  })
}
