import request from '@/utils/request'

// 查询功效类型列表
export function listType(query) {
  return request({
    url: '/gx/type/list',
    method: 'get',
    params: query
  })
}

// 查询功效类型详细
export function getType(id) {
  return request({
    url: '/gx/type/' + id,
    method: 'get'
  })
}

// 新增功效类型
export function addType(data) {
  return request({
    url: '/gx/type',
    method: 'post',
    data: data
  })
}

// 修改功效类型
export function updateType(data) {
  return request({
    url: '/gx/type',
    method: 'put',
    data: data
  })
}

// 删除功效类型
export function delType(id) {
  return request({
    url: '/gx/type/' + id,
    method: 'delete'
  })
}

// 导出功效类型
export function exportType(query) {
  return request({
    url: '/gx/type/export',
    method: 'get',
    params: query
  })
}

export function allType(query) {
  return request({
    url: '/gx/type/all',
    method: 'get',
    params: query
  })
}
