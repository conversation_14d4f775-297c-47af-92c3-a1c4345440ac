import request from '@/utils/request'

// 查询工单平衡-记录数据列表
export function listOrderProductionBalanceData(query) {
  return request({
    url: '/order/orderProductionBalanceData/list',
    method: 'get',
    params: query
  })
}

// 查询工单平衡-记录数据详细
export function getOrderProductionBalanceData(id) {
  return request({
    url: '/order/orderProductionBalanceData/' + id,
    method: 'get'
  })
}

// 新增工单平衡-记录数据
export function addOrderProductionBalanceData(data) {
  return request({
    url: '/order/orderProductionBalanceData',
    method: 'post',
    data: data
  })
}

// 修改工单平衡-记录数据
export function updateOrderProductionBalanceData(data) {
  return request({
    url: '/order/orderProductionBalanceData',
    method: 'put',
    data: data
  })
}

// 删除工单平衡-记录数据
export function delOrderProductionBalanceData(id) {
  return request({
    url: '/order/orderProductionBalanceData/' + id,
    method: 'delete'
  })
}

// 导出工单平衡-记录数据
export function exportOrderProductionBalanceData(query) {
  return request({
    url: '/order/orderProductionBalanceData/export',
    method: 'get',
    params: query
  })
}
// 导出工单平衡-记录数据
export function exportProduction(query) {
  return request({
    url: '/order/orderProductionBalanceData/exportProduction',
    method: 'get',
    params: query
  })
}
