import request from '@/utils/request'

// 查询退料列表
export function listMocte(query) {
  return request({
    url: '/order/mocte/list',
    method: 'get',
    params: query
  })
}

// 查询退料详细
export function getMocte(id) {
  return request({
    url: '/order/mocte/' + id,
    method: 'get'
  })
}

// 新增退料
export function addMocte(data) {
  return request({
    url: '/order/mocte',
    method: 'post',
    data: data
  })
}

// 修改退料
export function updateMocte(data) {
  return request({
    url: '/order/mocte',
    method: 'put',
    data: data
  })
}

// 删除退料
export function delMocte(id) {
  return request({
    url: '/order/mocte/' + id,
    method: 'delete'
  })
}

// 导出退料
export function exportMocte(query) {
  return request({
    url: '/order/mocte/export',
    method: 'get',
    params: query
  })
}