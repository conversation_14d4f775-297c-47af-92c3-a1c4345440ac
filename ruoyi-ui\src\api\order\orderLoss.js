import request from '@/utils/request'

// 查询订单损耗率列表
export function listOrderLoss(query) {
  return request({
    url: '/order/orderLoss/list',
    method: 'get',
    params: query
  })
}

// 查询订单损耗率列表(根据品号纬度)
export function listOrderCodeLoss(query) {
  return request({
    url: '/order/orderLoss/listCode',
    method: 'get',
    params: query
  })
}

// 查询订单损耗率详细
export function getOrderLoss(id) {
  return request({
    url: '/order/orderLoss/' + id,
    method: 'get'
  })
}

// 新增订单损耗率
export function addOrderLoss(data) {
  return request({
    url: '/order/orderLoss',
    method: 'post',
    data: data
  })
}

// 修改订单损耗率
export function updateOrderLoss(data) {
  return request({
    url: '/order/orderLoss',
    method: 'put',
    data: data
  })
}

// 删除订单损耗率
export function delOrderLoss(id) {
  return request({
    url: '/order/orderLoss/' + id,
    method: 'delete'
  })
}

// 导出订单损耗率
export function exportOrderLoss(query) {
  return request({
    url: '/order/orderLoss/export',
    method: 'get',
    params: query
  })
}
