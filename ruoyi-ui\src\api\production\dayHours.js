import request from '@/utils/request'

// 查询生产工时日报列表
export function listDayHours(query) {
  return request({
    url: '/production/dayHours/list',
    method: 'get',
    params: query
  })
}

// 查询生产工时日报详细
export function getDayHours(id) {
  return request({
    url: '/production/dayHours/' + id,
    method: 'get'
  })
}

// 新增生产工时日报
export function addDayHours(data) {
  return request({
    url: '/production/dayHours',
    method: 'post',
    data: data
  })
}

// 修改生产工时日报
export function updateDayHours(data) {
  return request({
    url: '/production/dayHours',
    method: 'put',
    data: data
  })
}

// 删除生产工时日报
export function delDayHours(id) {
  return request({
    url: '/production/dayHours/' + id,
    method: 'delete'
  })
}

// 导出生产工时日报
export function exportDayHours(query) {
  return request({
    url: '/production/dayHours/export',
    method: 'get',
    params: query
  })
}

export function exportWagesHours(id) {
  return request({
    url: '/production/dayHours/exportWageHours/' + id,
    method: 'get'
  })
}

export function exportAbnormalHours(id) {
  return request({
    url: '/production/dayHours/exportAbnormalHours/' + id,
    method: 'get'
  })
}
