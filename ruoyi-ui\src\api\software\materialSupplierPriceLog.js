import request from '@/utils/request'

// 查询原料供应商价格记录列表
export function listMaterialSupplierPriceLog(query) {
  return request({
    url: '/software/materialSupplierPriceLog/list',
    method: 'get',
    params: query
  })
}

// 查询原料供应商价格记录详细
export function getMaterialSupplierPriceLog(id) {
  return request({
    url: '/software/materialSupplierPriceLog/' + id,
    method: 'get'
  })
}

// 新增原料供应商价格记录
export function addMaterialSupplierPriceLog(data) {
  return request({
    url: '/software/materialSupplierPriceLog',
    method: 'post',
    data: data
  })
}

// 修改原料供应商价格记录
export function updateMaterialSupplierPriceLog(data) {
  return request({
    url: '/software/materialSupplierPriceLog',
    method: 'put',
    data: data
  })
}

// 删除原料供应商价格记录
export function delMaterialSupplierPriceLog(id) {
  return request({
    url: '/software/materialSupplierPriceLog/' + id,
    method: 'delete'
  })
}

// 导出原料供应商价格记录
export function exportMaterialSupplierPriceLog(query) {
  return request({
    url: '/software/materialSupplierPriceLog/export',
    method: 'get',
    params: query
  })
}