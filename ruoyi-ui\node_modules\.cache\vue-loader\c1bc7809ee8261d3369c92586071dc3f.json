{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\index.vue", "mtime": 1753954679643}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0RGF5SG91cnMsDQogIGRlbERheUhvdXJzLA0KICBleHBvcnREYXlIb3VycywNCiAgZXhwb3J0V2FnZXNIb3VycywgZXhwb3J0QWJub3JtYWxIb3Vycw0KfSBmcm9tICJAL2FwaS9wcm9kdWN0aW9uL2RheUhvdXJzIjsNCmltcG9ydCBEYXlIb3Vyc1NhdmUgZnJvbSAiQC92aWV3cy9wcm9kdWN0aW9uL2RheUhvdXJzL3NhdmUudnVlIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiRGF5SG91cnMiLA0KICBjb21wb25lbnRzOiB7RGF5SG91cnNTYXZlfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBidG5Mb2FkaW5nOiBmYWxzZSwNCiAgICAgIGZ1bGxzY3JlZW5GbGFnOiB0cnVlLA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgc2hvd1NlYXJjaDogZmFsc2UsDQogICAgICB0b3RhbDogMCwNCiAgICAgIGRheUhvdXJzTGlzdDogW10sDQogICAgICB0aXRsZTogIiIsDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgZmFjdG9yeTogbnVsbCwNCiAgICAgICAgd29ya0RhdGU6IG51bGwsDQogICAgICB9LA0KICAgICAgY3VycmVudFJvdzoge30sDQogICAgICBmYWN0b3J5T3B0aW9uczogWw0KICAgICAgICB7bGFiZWw6J+WunOS+rCcsdmFsdWU6ICdDT01QQU5ZX1lOJyx9LA0KICAgICAgICB7bGFiZWw6J+eAm+W9qScsdmFsdWU6ICdDT01QQU5ZX1lDJyx9LA0KICAgICAgXSwNCiAgICB9Ow0KICB9LA0KICBhc3luYyBjcmVhdGVkKCkgew0KICAgIGF3YWl0IHRoaXMuZ2V0TGlzdCgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBhc3luYyBnZXRMaXN0KCkgew0KICAgICAgbGV0IHBhcmFtcyA9IE9iamVjdC5hc3NpZ24oe30sIHRoaXMucXVlcnlQYXJhbXMpDQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICBsZXQgcmVzID0gYXdhaXQgbGlzdERheUhvdXJzKHBhcmFtcykNCiAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICB0aGlzLmRheUhvdXJzTGlzdCA9IHJlcy5yb3dzDQogICAgICB0aGlzLnRvdGFsID0gcmVzLnRvdGFsDQogICAgfSwNCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICBhc3luYyBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLmN1cnJlbnRSb3cgPSByb3cNCiAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICAgIGF3YWl0IHRoaXMuJG5leHRUaWNrKCkNCiAgICAgIGNvbnN0IGRheUhvdXJzU2F2ZSA9IHRoaXMuJHJlZnMuZGF5SG91cnNTYXZlDQogICAgICBpZihkYXlIb3Vyc1NhdmUpIHsNCiAgICAgICAgZGF5SG91cnNTYXZlLnJlc2V0KCkNCiAgICAgICAgYXdhaXQgZGF5SG91cnNTYXZlLmluaXQocm93LmlkKQ0KICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgdHJ5IHsNCiAgICAgICAgYXdhaXQgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk55Sf5Lqn5bel5pe25pel5oqlPycpDQogICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IHRydWUNCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgZGVsRGF5SG91cnMocm93LmlkKQ0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0TGlzdCgpDQogICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKQ0KICAgICAgICB9DQogICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlDQogICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBxdWVyeVBhcmFtcyA9IHRoaXMucXVlcnlQYXJhbXM7DQogICAgICAgIGF3YWl0IHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWvvOWHuuaJgOacieeUn+S6p+W3peaXtuaXpeaKpeaVsOaNrumhuT8nKQ0KICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlDQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGV4cG9ydERheUhvdXJzKHF1ZXJ5UGFyYW1zKQ0KICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB0aGlzLmRvd25sb2FkKHJlcy5tc2cpDQogICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBleHBvcnRXYWdlc0hvdXJzKGlkKSB7DQogICAgICB0cnkgew0KICAgICAgICBhd2FpdCB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTlr7zlh7o/JykNCiAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gdHJ1ZQ0KICAgICAgICBsZXQgcmVzID0gYXdhaXQgZXhwb3J0V2FnZXNIb3VycyhpZCkNCiAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgdGhpcy5kb3dubG9hZChyZXMubXNnKTsNCiAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIGV4cG9ydEFibm9ybWFsSG91cnMoaWQpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGF3YWl0IHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWvvOWHuj8nKQ0KICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlDQogICAgICAgIGxldCByZXMgPSBhd2FpdCBleHBvcnRBYm5vcm1hbEhvdXJzKGlkKQ0KICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB0aGlzLmRvd25sb2FkKHJlcy5tc2cpOw0KICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfQ0KICAgIH0sDQogIH0NCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0LA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/production/dayHours", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" v-show=\"showSearch\" size=\"mini\" label-width=\"80px\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"厂区\" prop=\"factory\">\r\n            <el-select v-model=\"queryParams.factory\" >\r\n              <el-option\r\n                v-for=\"item in factoryOptions\"\r\n                :key=\"item.value\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"排班日期\" prop=\"workDate\">\r\n            <el-date-picker\r\n              clearable\r\n              v-model=\"queryParams.workDate\"\r\n              type=\"date\"\r\n              value-format=\"yyyy-MM-dd\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          :loading=\"btnLoading\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['production:dayHours:export']\"\r\n        >导出\r\n        </el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\" />\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"dayHoursList\">\r\n      <el-table-column label=\"排班日期\" width=\"120\" align=\"center\" >\r\n        <template v-slot=\"scope\">\r\n          <span style=\"cursor: pointer;color: #00afff\" @click=\"handleUpdate(scope.row,true)\">{{ scope.row.workDate }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"工厂\" width=\"120\" align=\"center\" prop=\"factory\" >\r\n        <template v-slot=\"scope\" >\r\n          <span style=\"color: #1c84c6\" v-if=\"scope.row.factory === 'COMPANY_YN'\" >宜侬</span>\r\n          <span style=\"color: #FF99CC\" v-if=\"scope.row.factory === 'COMPANY_YC'\" >瀛彩</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"正式工人数\" width=\"120\" align=\"center\" prop=\"userNums\"/>\r\n      <el-table-column label=\"劳务工人数\" width=\"120\" align=\"center\" prop=\"laborNums\"/>\r\n      <el-table-column label=\"包干工人数\" width=\"120\" align=\"center\" prop=\"outerNums\"/>\r\n      <el-table-column label=\"正式工工时\" width=\"120\" align=\"center\" >\r\n        <template v-slot=\"scope\" >\r\n          {{minutesToHours(scope.row.userMinutes).toFixed(2)}}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"劳务工工时\" width=\"120\" align=\"center\" >\r\n        <template v-slot=\"scope\" >\r\n          {{minutesToHours(scope.row.laborMinutes).toFixed(2)}}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"包干工工时\" width=\"120\" align=\"center\" prop=\"outerHours\" >\r\n        <template v-slot=\"scope\" >\r\n          {{minutesToHours(scope.row.outerMinutes).toFixed(2)}}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"总工时\" width=\"120\" align=\"center\" prop=\"sumHours\">\r\n        <template v-slot=\"scope\" >\r\n          {{minutesToHours(scope.row.sumMinutes).toFixed(2)}}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"有效工时\" width=\"120\" align=\"center\" prop=\"effectiveHours\">\r\n        <template v-slot=\"scope\" >\r\n          {{minutesToHours(scope.row.effectiveMinutes).toFixed(2)}}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"无效工时\" width=\"120\" align=\"center\" prop=\"invalidHours\">\r\n        <template v-slot=\"scope\" >\r\n          {{minutesToHours(scope.row.invalidMinutes).toFixed(2)}}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"休息工时\" width=\"120\" align=\"center\" prop=\"restHours\">\r\n        <template v-slot=\"scope\" >\r\n          {{minutesToHours(scope.row.restMinutes).toFixed(2)}}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"间接工时\" width=\"120\" align=\"center\" prop=\"otherHours\">\r\n        <template v-slot=\"scope\" >\r\n          {{minutesToHours(scope.row.otherMinutes).toFixed(2)}}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"管理工时\" width=\"120\" align=\"center\" prop=\"manageHours\">\r\n        <template v-slot=\"scope\" >\r\n          {{minutesToHours(scope.row.manageMinutes).toFixed(2)}}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"工资工时\" width=\"120\" align=\"center\" prop=\"wagesHours\">\r\n        <template v-slot=\"scope\" >\r\n          {{minutesToHours(scope.row.wagesMinutes).toFixed(2)}}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"推送人\" width=\"120\" align=\"center\" prop=\"pushUser\"/>\r\n      <el-table-column label=\"推送时间\" width=\"160\" align=\"center\" prop=\"pushTime\"/>\r\n      <el-table-column label=\"备注\" width=\"120\" align=\"center\" prop=\"remark\"/>\r\n      <el-table-column fixed=\"right\" label=\"操作\" width=\"120\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template v-slot=\"scope\">\r\n          <el-tooltip content=\"修改\" placement=\"top\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleUpdate(scope.row)\"\r\n              v-hasPermi=\"['production:dayHours:edit']\"\r\n            />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"导出工资工时\" placement=\"top\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-download\"\r\n              :loading=\"btnLoading\"\r\n              @click=\"exportWagesHours(scope.row.id)\"\r\n            />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"导出异常工时\" placement=\"top\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-download\"\r\n              :loading=\"btnLoading\"\r\n              @click=\"exportAbnormalHours(scope.row.id)\"\r\n            />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"删除\" placement=\"top\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-delete\"\r\n              @click=\"handleDelete(scope.row)\"\r\n              v-hasPermi=\"['production:dayHours:remove']\"\r\n            />\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <el-dialog :fullscreen=\"fullscreenFlag\" :visible.sync=\"open\" width=\"1200px\" :close-on-click-modal=\"false\"\r\n               append-to-body>\r\n      <div class=\"dialog-title\" slot=\"title\">\r\n        <div>\r\n          <span style=\"color: #1c84c6\" v-if=\"currentRow.factory === 'COMPANY_YN'\" >宜侬</span>\r\n          <span style=\"color: #FF99CC\" v-if=\"currentRow.factory === 'COMPANY_YC'\" >瀛彩</span>\r\n          ({{currentRow.workDate}})\r\n        </div>\r\n        <el-button @click=\"fullscreenFlag = !fullscreenFlag\" type=\"text\"\r\n                   :icon=\"fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'\"/>\r\n      </div>\r\n      <DayHoursSave ref=\"dayHoursSave\" />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listDayHours,\r\n  delDayHours,\r\n  exportDayHours,\r\n  exportWagesHours, exportAbnormalHours\r\n} from \"@/api/production/dayHours\";\r\nimport DayHoursSave from \"@/views/production/dayHours/save.vue\";\r\n\r\nexport default {\r\n  name: \"DayHours\",\r\n  components: {DayHoursSave},\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      btnLoading: false,\r\n      fullscreenFlag: true,\r\n      single: true,\r\n      showSearch: false,\r\n      total: 0,\r\n      dayHoursList: [],\r\n      title: \"\",\r\n      open: false,\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        factory: null,\r\n        workDate: null,\r\n      },\r\n      currentRow: {},\r\n      factoryOptions: [\r\n        {label:'宜侬',value: 'COMPANY_YN',},\r\n        {label:'瀛彩',value: 'COMPANY_YC',},\r\n      ],\r\n    };\r\n  },\r\n  async created() {\r\n    await this.getList()\r\n  },\r\n  methods: {\r\n    async getList() {\r\n      let params = Object.assign({}, this.queryParams)\r\n      this.loading = true\r\n      let res = await listDayHours(params)\r\n      this.loading = false\r\n      this.dayHoursList = res.rows\r\n      this.total = res.total\r\n    },\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    async handleUpdate(row) {\r\n      this.currentRow = row\r\n      this.open = true\r\n      await this.$nextTick()\r\n      const dayHoursSave = this.$refs.dayHoursSave\r\n      if(dayHoursSave) {\r\n        dayHoursSave.reset()\r\n        await dayHoursSave.init(row.id)\r\n      }\r\n    },\r\n    async handleDelete(row) {\r\n      try {\r\n        await this.$confirm('是否确认删除生产工时日报?')\r\n        this.btnLoading = true\r\n        const res = await delDayHours(row.id)\r\n        if (res.code === 200) {\r\n          await this.getList()\r\n          this.msgSuccess(\"删除成功\")\r\n        }\r\n        this.btnLoading = false\r\n      } catch (e) {\r\n        this.btnLoading = false\r\n      }\r\n    },\r\n    async handleExport() {\r\n      try {\r\n        const queryParams = this.queryParams;\r\n        await this.$confirm('是否确认导出所有生产工时日报数据项?')\r\n        this.btnLoading = true\r\n        const res = await exportDayHours(queryParams)\r\n        this.btnLoading = false\r\n        this.download(res.msg)\r\n      } catch (e) {\r\n        this.btnLoading = false\r\n      }\r\n    },\r\n    async exportWagesHours(id) {\r\n      try {\r\n        await this.$confirm('是否确认导出?')\r\n        this.btnLoading = true\r\n        let res = await exportWagesHours(id)\r\n        this.btnLoading = false\r\n        this.download(res.msg);\r\n      } catch (e) {\r\n        this.btnLoading = false\r\n      }\r\n    },\r\n    async exportAbnormalHours(id) {\r\n      try {\r\n        await this.$confirm('是否确认导出?')\r\n        this.btnLoading = true\r\n        let res = await exportAbnormalHours(id)\r\n        this.btnLoading = false\r\n        this.download(res.msg);\r\n      } catch (e) {\r\n        this.btnLoading = false\r\n      }\r\n    },\r\n  }\r\n};\r\n</script>\r\n"]}]}