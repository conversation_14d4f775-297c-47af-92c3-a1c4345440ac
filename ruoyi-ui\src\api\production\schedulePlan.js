import request from '@/utils/request'
import {batchExportQcMaterialTest} from "@/api/qc/qcMaterialTest";

// 查询排班计划列表
export function listSchedulePlan(query) {
  return request({
    url: '/production/schedulePlan/list',
    method: 'get',
    params: query
  })
}

export function listHoursDiffSchedulePlan(query) {
  return request({
    url: '/production/schedulePlan/hoursDiffList',
    method: 'get',
    params: query
  })
}

// 计划查询排班计划列表
export function listOrderSchedulePlan(query) {
  return request({
    url: '/production/schedulePlan/orderPlanList',
    method: 'get',
    params: query
  })
}

// 查询排班计划详细
export function getSchedulePlan(id) {
  return request({
    url: '/production/schedulePlan/' + id,
    method: 'get'
  })
}

// 新增排班计划
export function addSchedulePlan(data) {
  return request({
    url: '/production/schedulePlan',
    method: 'post',
    data: data
  })
}

// 修改排班计划
export function updateSchedulePlan(data) {
  return request({
    url: '/production/schedulePlan',
    method: 'put',
    data: data
  })
}

export function submitSchedulePlanHours(data) {
  return request({
    url: '/production/schedulePlan/submitHours',
    method: 'put',
    data: data
  })
}

export function finishSchedulePlan(data) {
  return request({
    url: '/production/schedulePlan/finish',
    method: 'put',
    data: data
  })
}

// 修改排班计划及供应链工单时间
export function updatePlanAndScheduleTime(data) {
  return request({
    url: '/production/schedulePlan/editPlanAndScheduleTime',
    method: 'put',
    data: data
  })
}

export function updateHourStatusSchedulePlan(data) {
  return request({
    url: '/production/schedulePlan/editHourStatus',
    method: 'put',
    data
  })
}

export function updateConfirmStatusSchedulePlan(data) {
  return request({
    url: '/production/schedulePlan/editConfirmStatus',
    method: 'put',
    data
  })
}

export function updateFinishedStatusSchedulePlan(data) {
  return request({
    url: '/production/schedulePlan/editFinishedStatus',
    method: 'put',
    data
  })
}

export function updateCancelStatusSchedulePlan(data) {
  return request({
    url: '/production/schedulePlan/editCancelStatus',
    method: 'put',
    data
  })
}

// 修改排班计划及供应链工单时间
export function resetSchedulePlanRkArray(id) {
  return request({
    url: '/production/schedulePlan/resetRkArray/' + id,
    method: 'put',
  })
}

// 删除排班计划
export function delSchedulePlan(id) {
  return request({
    url: '/production/schedulePlan/' + id,
    method: 'delete'
  })
}

export function allSchedulePlan(query) {
  return request({
    url: '/production/schedulePlan/all',
    method: 'get',
    params: query
  })
}

export function allBaseSchedulePlan(query) {
  return request({
    url: '/production/schedulePlan/allBase',
    method: 'get',
    params: query
  })
}

export function allMaterialSchedulePlan(query) {
  return request({
    url: '/production/schedulePlan/allMaterial',
    method: 'get',
    params: query
  })
}

export function getPickingCode(query) {
  return request({
    url: '/production/schedulePlan/getPickingCode',
    method: 'get',
    params: query
  })
}

export function erpBom(query) {
  return request({
    url: '/production/schedulePlan/erpBom',
    method: 'get',
    params: query
  })
}

export function planStats(query) {
  return request({
    url: '/production/schedulePlan/stats',
    method: 'get',
    params: query
  })
}

export function planLineStats(query) {
  return request({
    url: '/production/schedulePlan/lineStats',
    method: 'get',
    params: query
  })
}

export function schedulePlanSubmitAudit(data) {
  return request({
    url: '/production/schedulePlan/submitAudit',
    method: 'put',
    data: data
  })
}

export function schedulePlanCancelAudit(data) {
  return request({
    url: '/production/schedulePlan/cancelAudit',
    method: 'put',
    data: data
  })
}

export function schedulePlanSubmitChangeAudit(data) {
  return request({
    url: '/production/schedulePlan/submitChangeAudit',
    method: 'put',
    data: data
  })
}

export function schedulePlanCancelChangeAudit(data) {
  return request({
    url: '/production/schedulePlan/cancelChangeAudit',
    method: 'put',
    data: data
  })
}

export function updateBatchProductionPlan(data) {
  return request({
    url: '/production/schedulePlan/updateBatchProductionPlan',
    method: 'put',
    data: data
  })
}

export function getWorkTypeMaterialArray(query) {
  return request({
    url: '/production/schedulePlan/workTypeMaterialArray',
    method: 'get',
    params: query
  })
}

export function exportMaterialOther(data) {
  return request({
    url: '/production/schedulePlan/exportMaterialOther',
    method: 'post',
    data
  })
}

export function exportMaterialBcp(data) {
  return request({
    url: '/production/schedulePlan/exportMaterialBcp',
    method: 'post',
    data
  })
}

export function exportMaterialTable(data) {
  return request({
    url: '/production/schedulePlan/exportMaterialTable',
    method: 'post',
    data
  })
}

export function exportMaterialDosage(data) {
  return request({
    url: '/production/schedulePlan/exportMaterialDosage',
    method: 'post',
    data
  })
}

export function asyncMaterial() {
  return request({
    url: '/production/schedulePlan/asyncMaterial',
    method: 'get',
  })
}

export function batchExportMaterial(data) {
  return request({
    url: '/production/schedulePlan/batchExportMaterial',
    method: 'post',
    data
  })
}

export function updateBatchProductionPlanHour(data) {
  return request({
    url: '/production/schedulePlan/updateBatchProductionPlanHour',
    method: 'put',
    data: data
  })
}

export function exportSchedulePlan(query) {
  return request({
    url: '/production/schedulePlan/export',
    method: 'get',
    params: query,
  })
}

export function exportSchedulePlanCharts(query) {
  return request({
    url: '/production/schedulePlan/exportCharts',
    method: 'get',
    params: query,
  })
}

export function monthHoursDiffSchedulePlan(query) {
  return request({
    url: '/production/schedulePlan/monthHoursDiff',
    method: 'get',
    params: query,
  })
}

export function asyncSchedulePlanHours(data) {
  return request({
    url: '/production/schedulePlan/asyncHours',
    method: 'put',
    data: data
  })
}

export function asyncSchedulePlanHour(data) {
  return request({
    url: '/production/schedulePlan/asyncHour',
    method: 'put',
    data: data
  })
}

export function exportDiffSchedulePlanHours(query) {
  return request({
    url: '/production/schedulePlan/exportDiffHours',
    method: 'get',
    params: query,
  })
}

export function asyncSchedulePlanProductNums() {
  return request({
    url: '/production/schedulePlan/asyncNums',
    method: 'get',
  })
}

export function getSchedulePlanGbHours(id) {
  return request({
    url: '/production/schedulePlan/getGbHours/' + id,
    method: 'get'
  })
}

export function importTemplateSchedulePlan() {
  return request({
    url: '/production/schedulePlan/importTemplate',
    method: 'get'
  })
}

export function exportProductionPlan() {
  return request({
    url: '/production/schedulePlan/exportPlan',
    method: 'get',
  })
}
