import request from '@/utils/request'

// 查询erp报废单列表
export function listInvtm(query) {
  return request({
    url: '/order/invtm/list',
    method: 'get',
    params: query
  })
}

// 查询erp报废单详细
export function getInvtm(id) {
  return request({
    url: '/order/invtm/' + id,
    method: 'get'
  })
}

// 新增erp报废单
export function addInvtm(data) {
  return request({
    url: '/order/invtm',
    method: 'post',
    data: data
  })
}

// 修改erp报废单
export function updateInvtm(data) {
  return request({
    url: '/order/invtm',
    method: 'put',
    data: data
  })
}

// 删除erp报废单
export function delInvtm(id) {
  return request({
    url: '/order/invtm/' + id,
    method: 'delete'
  })
}

// 导出erp报废单
export function exportInvtm(query) {
  return request({
    url: '/order/invtm/export',
    method: 'get',
    params: query
  })
}