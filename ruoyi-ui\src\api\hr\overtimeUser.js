import request from '@/utils/request'

export function listOvertimeUserLeadershipSee(query) {
  return request({
    url: '/hr/overtimeUser/leadershipSee',
    method: 'get',
    params: query
  })
}
// 查询加班用户申请列表
export function listOvertimeUser(query) {
  return request({
    url: '/hr/overtimeUser/list',
    method: 'get',
    params: query
  })
}
// 查询加班用户申请审批列表
export function listAuditOvertimeUser(query) {
  return request({
    url: '/hr/overtimeUser/audit',
    method: 'get',
    params: query
  })
}
// 查询加班申请列表
export function logOvertimeUser(query) {
  return request({
    url: '/hr/overtimeUser/log',
    method: 'get',
    params: query
  })
}
// 查询加班用户申请详细
export function getOvertimeUser(id) {
  return request({
    url: '/hr/overtimeUser/' + id,
    method: 'get'
  })
}
export function getOvertimeUsageList(params) {
  return request({
    url: '/hr/overtimeUser/usages',
    method: 'get',
    params: params
  })
}
// 新增加班用户申请
export function addOvertimeUser(data) {
  return request({
    url: '/hr/overtimeUser',
    method: 'post',
    data: data
  })
}

// 修改加班用户申请
export function updateOvertimeUser(data) {
  return request({
    url: '/hr/overtimeUser',
    method: 'put',
    data: data
  })
}


export function addOvertimeUserUndo(data) {
  return request({
    url: '/hr/overtimeUser/undo',
    method: 'post',
    data: data
  })
}
export function copyOvertimeUser(query) {
  return request({
    url: '/hr/overtimeUser/copy',
    method: 'get',
    params: query
  })
}

export function delOvertimeUser(id) {
  return request({
    url: '/hr/overtimeUser/' + id,
    method: 'delete'
  })
}
export function submitAudit(data) {
  return request({
    url: '/hr/overtimeUser/submitAudit',
    method: 'put',
    data: data
  })
}
export function cancelAudit(data) {
  return request({
    url: '/hr/overtimeUser/cancelAudit',
    method: 'put',
    data: data
  })
}

export function pigeonholeOvertimeUser(data) {
  return request({
    url: '/hr/overtimeUser/pigeonhole',
    method: 'post',
    data: data
  })
}
