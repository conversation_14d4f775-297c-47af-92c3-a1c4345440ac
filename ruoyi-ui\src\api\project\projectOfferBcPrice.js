import request from '@/utils/request'

// 查询包材固定价格列表
export function listProjectOfferBcPrice(query) {
  return request({
    url: '/project/projectOfferBcPrice/list',
    method: 'get',
    params: query
  })
}

// 查询包材固定价格详细
export function getProjectOfferBcPrice(id) {
  return request({
    url: '/project/projectOfferBcPrice/' + id,
    method: 'get'
  })
}

// 新增包材固定价格
export function addProjectOfferBcPrice(data) {
  return request({
    url: '/project/projectOfferBcPrice',
    method: 'post',
    data: data
  })
}

// 修改包材固定价格
export function updateProjectOfferBcPrice(data) {
  return request({
    url: '/project/projectOfferBcPrice',
    method: 'put',
    data: data
  })
}

// 删除包材固定价格
export function delProjectOfferBcPrice(id) {
  return request({
    url: '/project/projectOfferBcPrice/' + id,
    method: 'delete'
  })
}

// 导出包材固定价格
export function exportProjectOfferBcPrice(query) {
  return request({
    url: '/project/projectOfferBcPrice/export',
    method: 'get',
    params: query
  })
}