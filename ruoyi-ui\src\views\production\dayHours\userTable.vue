<template>
  <div>
    <el-divider content-position="left" >产线</el-divider>

    <el-form ref="queryForm" :model="queryParams" label-width="80px" size="mini" >
      <el-row>
        <el-col :span="8">
          <el-form-item label="工号" prop="userCode">
            <el-input v-model="queryParams.userCode" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="姓名" prop="nickName">
            <el-input v-model="queryParams.nickName" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-button icon="el-icon-search" size="mini" type="primary" @click="filterUser">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-col>
      </el-row>
    </el-form>

    <div class="table-wrapper">
      <table class="base-table small-table" >
        <colgroup>
          <col style="width: 50px" /><!-- 序号 -->
          <col style="width: 100px" /><!-- 员工姓名 -->
          <col style="width: 120px" /><!-- 工号 -->
          <col style="width: 100px" /><!-- 类型 -->
          <col style="width: 160px" /><!-- 开始时间 -->
          <col style="width: 160px" /><!-- 结束时间 -->
          <col style="width: 100px" /><!-- 时长 -->
          <col style="width: 100px" /><!-- 异常状态 -->
          <col style="width: 120px" /><!-- 设备编号 -->
          <col style="width: 160px" /><!-- 批次号 -->
          <col style="width: 120px" /><!-- 工作日期 -->
          <col style="width: 160px" /><!-- 进站时间 -->
          <col style="width: 160px" /><!-- 出站时间 -->
          <col style="width: 100px" /><!-- 工时 -->
          <col style="width: 100px" /><!-- sap工时 -->
          <col style="width: 100px" /><!-- 休息工时 -->
          <col style="width: 500px" /><!-- 有效时段 -->
          <col style="width: 100px" /><!-- 有效工时 -->
          <col style="width: 100px" /><!-- 无效工时 -->
          <col style="width: 100px" /><!-- 工资工时 -->
          <col style="width: 100px" /><!-- 修正工时 -->
          <col style="width: 120px" /><!-- 异常状态 -->
          <col style="width: 240px" /><!-- 备注 -->
        </colgroup>
        <thead>
          <tr >
            <th :rowspan="2" >序号</th>
            <th :rowspan="2" >员工姓名</th>
            <th :rowspan="2" >工号</th>
            <th :colspan="5" >工时对比</th>
            <th :colspan="7" >sap根据mes设备进出站记录拆解后的记录</th>
            <th :rowspan="2" >休息工时
              <el-tooltip content="工厂标准休息时间与mes时间的交集半点向下取整" >
                <i class="el-icon-question" />
              </el-tooltip>
            </th>
            <th :rowspan="2" >
              有效时段
            </th>
            <th :rowspan="2" >
              有效工时
              <el-tooltip content="sap工时" >
                <i class="el-icon-question" />
              </el-tooltip>
            </th>
            <th :rowspan="2" >
              无效工时
              <el-tooltip content="工资工时-有效工时" >
                <i class="el-icon-question" />
              </el-tooltip>
            </th>
            <th :rowspan="2" >
              工资工时
              <el-tooltip >
                <div slot="content">
                  mes时长半点向下取整
                </div>
                <i class="el-icon-question" />
              </el-tooltip>
            </th>
            <th :rowspan="2" >
              修正工时
              <el-tooltip >
                <div slot="content">
                  修正过后以修正的工时为准
                </div>
                <i class="el-icon-question" />
              </el-tooltip>
            </th>
            <th :rowspan="2" >
              异常状态
              <el-tooltip >
                <div slot="content">
                  <div>1.上工时间早于考勤打卡时间:上工考勤异常(代上工风险)</div>
                  <div>2.下工时间晚于考勤打卡时间:下工考勤异常(代下工风险)</div>
                  <div>3.下工 20分钟后未打卡:下班考勤异常</div>
                  <div>4.末道产线记录出站,15分钟内员工未下工:下工异常</div>
                  <div>5.上工后半小时没有匹配上产线:上工异常</div>
                  <div>6.转场超过半小时:转场异常 (扣除标准休息时间)</div>
                  <div>7.有效工时低于工资工时 85%,有效工时异常.</div>
                  <div>8.末道产线记录出站后,超出 15 分钟后才下工,或没有下工数据的,且考勤时间晚于末道产线记录 20 分钟之后的,工资工时以末道产线记录出站/暂停时间为准.</div>
                  <div>9.没有上下班考勤记录的,工资工时统一记录为 0,需人工核实,修改工资工时需审批</div>
                </div>
                <i class="el-icon-question" />
              </el-tooltip>
            </th>
            <th :rowspan="2" >备注</th>
          </tr>
          <tr>
            <th >类型</th>
            <th >开始时间</th>
            <th >结束时间</th>
            <th >时长</th>
            <th >
              异常状态
              <el-tooltip >
                <div slot="content">
                  <div>1.上工时间早于考勤打卡时间:上工考勤异常(代上工风险)</div>
                  <div>2.下工时间晚于考勤打卡时间:下工考勤异常(代下工风险)</div>
                  <div>3.下工 20分钟后未打卡:下班考勤异常</div>
                  <div>4.末道产线记录出站,15分钟内员工未下工:下工异常</div>
                  <div>5.上工后半小时没有匹配上产线:上工异常</div>
                  <div>6.转场超过半小时:转场异常 (扣除标准休息时间)</div>
                  <div>7.有效工时低于工资工时 85%,有效工时异常.</div>
                  <div>8.末道产线记录出站后,超出 15 分钟后才下工,或没有下工数据的,且考勤时间晚于末道产线记录 20 分钟之后的,工资工时以末道产线记录出站/暂停时间为准.</div>
                  <div>9.没有上下班考勤记录的,工资工时统一记录为 0,需人工核实,修改工资工时需审批</div>
                </div>
                <i class="el-icon-question" />
              </el-tooltip>
            </th>
            <th >设备编号</th>
            <th >批次号</th>
            <th >工作日期</th>
            <th >进站时间</th>
            <th >出站时间</th>
            <th >工时</th>
            <th >sap工时</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(u,i) in filterUserArray" :key="u.userId">
            <td >{{i+1}}</td>
            <td >{{u.nickName}}</td>
            <td >{{u.userCode}}</td>
            <td :colspan="5" style="width: 620px;padding: 0" >
              <table class="base-table small-table" >
                <tr>
                  <th style="width: 100px" >考勤</th>
                  <td style="width: 160px" >
                    <span style="color: #1c84c6;cursor: pointer" @click="selectAttendanceLog(u,'attendanceStart')">
                      {{u.attendanceStartTime}}
                    </span>
                  </td>
                  <td style="width: 160px" >
                    <span style="color: #1c84c6;cursor: pointer" @click="selectAttendanceLog(u,'attendanceEnd')">
                      {{u.attendanceEndTime}}
                    </span>
                  </td>
                  <td style="width: 100px" >
                    <span style="color: #1c84c6;cursor: pointer" @click="attendanceLog(u.userId)">
                      {{minutesToHours(u.attendanceMinutes).toFixed(2)}}
                    </span>
                  </td>
                  <td :rowspan="3" style="width: 100px" >
                    <div v-for="e in u.exceptionArray" :key="e" style="color: #F56C6C" >
                      {{selectOptionsLabel(exceptionOptions,e)}}
                    </div>
                  </td>
                </tr>
                <tr>
                  <th style="width: 100px" >mes</th>
                  <td style="width: 160px" >{{u.mesMinTime}}</td>
                  <td style="width: 160px" >{{u.mesMaxTime}}</td>
                  <td style="width: 100px" >
                    <span style="color: #1c84c6;cursor: pointer" @click="mesLog(u)">
                      {{minutesToHours(u.mesMinutes).toFixed(2)}}
                    </span>
                  </td>
                </tr>
                <tr>
                  <th style="width: 100px" >sap</th>
                  <td style="width: 160px" >{{u.sapMinTime}}</td>
                  <td style="width: 160px" >{{u.sapMaxTime}}</td>
                  <td style="width: 100px" >{{minutesToHours(u.sapMinutes).toFixed(2)}}</td>
                </tr>
              </table>
            </td>
            <td :colspan="6" style="width: 820px;padding: 0" >
              <table class="base-table small-table" >
                <tr v-for="h in u.sapArray" :key="h.id">
                  <td style="width: 120px" >{{h.equipmentNo}}</td>
                  <td style="width: 160px" >
                    <span style="color: #1c84c6;cursor: pointer" @click="mesLotLogs(h)">
                      {{h.lotNo}}
                    </span>
                  </td>
                  <td style="width: 120px" >{{h.workDate}}</td>
                  <td style="width: 160px" >{{h.startTime}}</td>
                  <td style="width: 160px" >{{h.endTime}}</td>
                  <td style="width: 100px" >{{minutesToHours(h.minutes).toFixed(2)}}</td>
                </tr>
              </table>
            </td>
            <td >{{minutesToHours(u.sapSumMinutes).toFixed(2)}}</td>
            <td >{{minutesToHours(u.restMinutes).toFixed(2)}}</td>
            <td >
              <MesTimeLine
                :time-array="u.timeArray"
                :attendance-array="u.attendanceArray"
                :mes-array="u.mesArray"
                :sap-array="u.sapArray"
              />
            </td>
            <td >{{minutesToHours(u.effectiveMinutes).toFixed(2)}}</td>
            <td >{{minutesToHours(u.invalidMinutes).toFixed(2)}}</td>
            <td >
              {{minutesToHours(u.wagesMinutes).toFixed(2)}}
            </td>
            <td >
              <!-- v-if="u.exceptionArray && u.exceptionArray.length" -->
              <el-input v-model="u.finalMinutes" autosize size="mini" @input="$emit('computeItemData')" />
            </td>
            <td >
              <div v-for="e in u.exceptionArray" :key="e" style="color: #F56C6C" >
                {{selectOptionsLabel(exceptionOptions,e)}}
              </div>
            </td>
            <td >
              <el-input v-model="u.remark" autosize size="mini" />
            </td>
          </tr>
          <tr>
            <th>合计</th>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td>{{sumHours}}</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
        </tbody>
      </table>
    </div>

    <el-dialog :close-on-click-modal="false" :visible.sync="selectAttendanceLogOpen" append-to-body width="600px">
      <table class="base-table small-table">
        <tr>
          <th style="width: 320px">打卡地址</th>
          <th style="width: 180px">打卡时间</th>
        </tr>
        <tr v-for="item in attendanceLogList" :key="item.id" >
          <td>{{item.userAddress}}</td>
          <td>
            <span style="color: #00afff;cursor: pointer" @click="selectUserTime(item.userCheckTime)" >
              {{item.userCheckTime}}
            </span>
          </td>
        </tr>
      </table>
    </el-dialog>

    <el-dialog :close-on-click-modal="false" :visible.sync="attendanceLogOpen" append-to-body width="600px">
      <table class="base-table small-table">
        <tr>
          <th style="width: 320px">打卡地址</th>
          <th style="width: 180px">打卡时间</th>
        </tr>
        <tr v-for="item in attendanceLogList" :key="item.id" >
          <td>{{item.userAddress}}</td>
          <td>{{item.userCheckTime}}</td>
        </tr>
      </table>
    </el-dialog>

    <el-dialog :fullscreen="fullscreenFlag" :visible.sync="open" width="1200px" :close-on-click-modal="false" append-to-body>
      <div class="dialog-title" slot="title">{{ title }}
        <el-button @click="fullscreenFlag = !fullscreenFlag" type="text"
                   :icon="fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'"/>
      </div>
      <MesHoursList ref="mesHoursList" :work-date="dayHours.workDate" :user-code="currentRow.userCode" @sailingsChange="sailingsChange" />
    </el-dialog>

    <el-dialog :title="title" :fullscreen="fullscreenFlag" :visible.sync="planOpen" width="1200px" :close-on-click-modal="false"  append-to-body>
      <div class="dialog-title" slot="title">
        {{title}}
        <el-button @click="fullscreenFlag = !fullscreenFlag" type="text"
                   :icon="fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'" />
      </div>
      <MesProductPlanSave ref="mesProductPlanSave" :readonly="true" :plan-type="currentRow.planType" />
    </el-dialog>

    <el-dialog :fullscreen="fullscreenFlag" :visible.sync="logOpen" width="1200px" :close-on-click-modal="false"
               append-to-body>
      <div class="dialog-title" slot="title">{{ title }}
        <el-button @click="fullscreenFlag = !fullscreenFlag" type="text"
                   :icon="fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'"/>
      </div>
      <div class="table-wrapper">
        <table class="base-table small-table" >
          <tr>
            <th style="width: 80px" >作业站</th>
            <th style="width: 120px" >类型</th>
            <th style="width: 120px" >时间</th>
            <th style="width: 180px" >暂停原因</th>
            <th style="width: 100px" >数量</th>
            <th style="width: 120px" >人员编号</th>
            <th style="width: 80px" >人员名称</th>
          </tr>
          <tr v-for="item in mesLogArray" :key="item.lotNo" >
            <td style="width: 80px" >{{item.opNo}}</td>
            <td style="width: 120px" >{{ selectOptionsLabel(opTypeOptions, item.opType)}}</td>
            <td style="width: 120px" >{{item.createTime}}</td>
            <td style="width: 180px" >{{item.reasonName}}</td>
            <td style="width: 100px" >{{item.qty}}</td>
            <td style="width: 120px" >{{item.userNo}}</td>
            <td style="width: 80px" >{{item.userName}}</td>
          </tr>
        </table>
      </div>
    </el-dialog>

  </div>
</template>
<script >
import {allAttendanceLog} from "@/api/hr/attendanceLog";
import MesHoursList from "@/views/production/mesHours/list.vue";
import MesProductPlanSave from "@/views/mes/production/production/save.vue";
import {getSchedulePlanByCode} from "@/api/production/schedulePlan";
import {getDispositionPlan} from "@/api/production/dispositionPlan";
import MesTimeLine from "@/views/production/dayHours/mesTimeLine.vue";
import {allMesLotWaitVo, allWipLotLog} from "@/api/mes/mesView";
import {diffMinutes} from "@/utils/production/time";

export default {
  name: 'dayHoursUserTable',
  components: {MesTimeLine, MesProductPlanSave, MesHoursList},
  props: {
    userArray: {
      type: Array,
      required: true,
    },
    mesHoursList: {
      type: Array,
      required: true,
    },
    sumHours: {
      type: Number,
      required: true,
    },
    dayHours: {
      type: Object,
      required: true,
    },
  },
  watch: {
    userArray: {
      async handler() {
        await this.filterUser()
      },
      immediate: true,
      deep: true,
    }
  },
  data() {
    return {
      btnLoading: false,
      loading: false,
      title: '',
      fullscreenFlag: true,
      attendanceLogOpen: false,
      selectAttendanceLogOpen: false,
      open: false,
      planOpen: false,
      queryParams: {
        userCode: null,
        nickName: null,
      },
      statusOptions: [],
      filterUserArray: [],
      attendanceLogList: [],
      currentRow: {},
      logOpen: false,
      mesLogArray: [],
      opTypeOptions: [
        {label: '进站',value: 'CHECKIN'},
        {label: '暂停',value: 'WAITDISPOSITION'},
        {label: '解除暂停-继续生产',value: 'RELEASE-GO'},
        {label: '出站',value: 'CHECKOUT'},
        {label: '解除暂停-结束生产',value: 'RELEASE-Inventory'},
        {label: '设备变更',value: 'EQPCHANGE'},
        {label: '开批',value: 'LOTCREATE'},
      ],
      exceptionOptions: [
        {label: '上工考勤异常',value: 1},
        {label: '下工考勤异常',value: 2},
        {label: 'mes上工异常',value: 3},
        {label: 'mes下工异常',value: 4},
        {label: '转场异常',value: 5},
        {label: '有效工时异常',value: 6},
        {label: 'sap上工异常',value: 7},
        {label: 'sap下工异常',value: 8},
      ],
      currentType: null,
    }
  },
  async created() {
    this.getDicts("production_status").then(response => {
      this.statusOptions = response.data
    })
  },
  methods: {
    sailingsChange(userCode){
      this.$emit('sailingsChange',userCode)
    },
    async mesLotLogs(row) {
      this.logOpen = true
      this.title = row.lotNo + "关联的生产批操作记录";
      const waitList = await allMesLotWaitVo({lotNo: row.lotNo})
      const lotLogs = await allWipLotLog({lotNo: row.lotNo})
      for (const lotLog of lotLogs) {
        if(lotLog.opType === 'WAITDISPOSITION') {
          const arr = waitList.filter(i=> i.waitDate === lotLog.createTime)
          if(arr && arr[0]) {
            lotLog.reasonName = arr[0].reasonName
          }
        }
      }
      this.mesLogArray = lotLogs
    },
    async planView(row) {
      this.currentRow = row
      this.planOpen = true
      this.title = '计划详情'
      await this.$nextTick()
      const mesProductPlanSave = this.$refs.mesProductPlanSave
      if(mesProductPlanSave) {
        mesProductPlanSave.reset()
        let res
        if(row.planType === 'production') {
          res = await getSchedulePlanByCode(row.planCode)
        } else {
          res = await getDispositionPlan(row.planCode)
        }
        await mesProductPlanSave.init(res.data.id)
      }
    },
    async mesLog(row) {
      this.currentRow = row
      this.open = true
      this.title = 'mes工时明细'
      await this.$nextTick()
      await this.$refs.mesHoursList.getList()
    },
    async selectUserTime(time) {
      this.currentRow[this.currentType + 'Time'] = time
      this.currentRow.attendanceMinutes = diffMinutes(this.currentRow.attendanceEndTime,this.currentRow.attendanceStartTime)
      this.currentRow.attendanceArray = [{startTime: this.currentRow.attendanceStartTime,endTime: this.currentRow.attendanceEndTime}]
      this.selectAttendanceLogOpen = false
    },
    async selectAttendanceLog(user,type) {
      const workDate = this.dayHours.workDate
      if(workDate) {
        const searchDateArray = [workDate,]
        searchDateArray.push(this.moment(workDate).add(1, 'days').format('YYYY-MM-DD'))
        const params = {
          userId: user.userId,
          searchDateArray
        }
        try {
          this.btnLoading = true
          const attendanceLogList = await allAttendanceLog(params)
          for (const item of attendanceLogList) {
            if(item.userCheckTime) {
              item.userCheckTime = this.moment(item.userCheckTime).format('YYYY-MM-DD HH:mm:ss')
            }
          }
          this.attendanceLogList = attendanceLogList
          this.btnLoading = false
          this.currentRow = user
          this.currentType = type
          this.selectAttendanceLogOpen = true
        } catch (e) {
          this.btnLoading = false
        }
      }
    },
    async attendanceLog(userId) {
      const workDate = this.dayHours.workDate
      if(workDate) {
        const searchDateArray = [workDate,]
        searchDateArray.push(this.moment(workDate).add(1, 'days').format('YYYY-MM-DD'))
        const params = {
          userId,
          searchDateArray
        }
        try {
          this.btnLoading = true
          const attendanceLogList = await allAttendanceLog(params)
          for (const item of attendanceLogList) {
            if(item.userCheckTime) {
              item.userCheckTime = this.moment(item.userCheckTime).format('YYYY-MM-DD HH:mm:ss')
            }
          }
          this.attendanceLogList = attendanceLogList
          this.btnLoading = false
          this.attendanceLogOpen = true
        } catch (e) {
          this.btnLoading = false
        }
      }
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.filterUser()
    },
    filterUser() {
      let filterUserArray = this.userArray
      const queryParams = this.queryParams
      if (queryParams.userCode) {
        filterUserArray = filterUserArray.filter(i => i.userCode === queryParams.userCode)
      }
      if (queryParams.nickName) {
        filterUserArray = filterUserArray.filter(i => i.nickName === queryParams.nickName)
      }
      this.filterUserArray = filterUserArray
    },
  },
}
</script>
<style scoped lang="scss">
.table-wrapper {
  max-height: 80vh;

  .base-table {

    thead {
      position: sticky;
      top: 0;
      z-index: 3;
    }
  }
}
</style>
