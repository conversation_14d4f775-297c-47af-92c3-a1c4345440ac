import request from '@/utils/request'

export function myListFee(query) {
  return request({
    url: '/legal/fee/my/list',
    method: 'get',
    params: query
  })
}

export function customerFeeList(query) {
  return request({
    url: '/legal/fee/feeList',
    method: 'get',
    params: query
  })
}

// 查询送检费用列表
export function listFee(query) {
  return request({
    url: '/legal/fee/list',
    method: 'get',
    params: query
  })
}

// 查询送检费用详细
export function getFee(id) {
  return request({
    url: '/legal/fee/' + id,
    method: 'get'
  })
}

export function getFeeByOrderId(orderId) {
  return request({
    url: '/legal/fee/getByOrderId/' + orderId,
    method: 'get'
  })
}

// 新增送检费用
export function addFee(data) {
  return request({
    url: '/legal/fee',
    method: 'post',
    data: data
  })
}

// 修改送检费用
export function updateFee(data) {
  return request({
    url: '/legal/fee',
    method: 'put',
    data: data
  })
}

// 删除送检费用
export function delFee(id) {
  return request({
    url: '/legal/fee/' + id,
    method: 'delete'
  })
}

// 导出送检费用
export function exportFee(query) {
  return request({
    url: '/legal/fee/export',
    method: 'get',
    params: query
  })
}

export function submitAudit(data) {
  return request({
    url: '/legal/fee/submitAudit',
    method: 'put',
    data: data
  })
}

//撤销申请
export function cancelAudit(data) {
  return request({
    url: '/legal/fee/cancelAudit',
    method: 'put',
    data: data
  })
}

export function allFee(query) {
  return request({
    url: '/legal/fee/all',
    method: 'get',
    params: query
  })
}
