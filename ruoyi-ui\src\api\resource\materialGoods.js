import request from '@/utils/request'

// 查询包材用品规格列表
export function listMaterialGoods(query) {
  return request({
    url: '/resource/materialGoods/list',
    method: 'get',
    params: query
  })
}

// 查询包材用品规格详细
export function getMaterialGoods(id) {
  return request({
    url: '/resource/materialGoods/' + id,
    method: 'get'
  })
}

// 新增包材用品规格
export function addMaterialGoods(data) {
  return request({
    url: '/resource/materialGoods',
    method: 'post',
    data: data
  })
}

// 修改包材用品规格
export function updateMaterialGoods(data) {
  return request({
    url: '/resource/materialGoods',
    method: 'put',
    data: data
  })
}

// 删除包材用品规格
export function delMaterialGoods(id) {
  return request({
    url: '/resource/materialGoods/' + id,
    method: 'delete'
  })
}

// 导出包材用品规格
export function exportMaterialGoods(query) {
  return request({
    url: '/resource/materialGoods/export',
    method: 'get',
    params: query
  })
}

export function allMaterialGoods(query) {
  return request({
    url: '/resource/materialGoods/all',
    method: 'get',
    params: query
  })
}
