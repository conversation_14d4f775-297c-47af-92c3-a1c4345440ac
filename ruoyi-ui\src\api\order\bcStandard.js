import request from '@/utils/request'

export function selectedListBcStandard(query) {
  return request({
    url: '/order/bcStandard/selectedList',
    method: 'get',
    params: query
  })
}

// 查询包材标准详细
export function getBcStandard(query) {
  return request({
    url: '/order/bcStandard',
    method: 'get',
    params: query,
  })
}

export function saveOrUpdateBcStandard(data) {
  return request({
    url: '/order/bcStandard/saveOrUpdate',
    method: 'post',
    data: data
  })
}

// 删除包材标准
export function delBcStandard(id) {
  return request({
    url: '/order/bcStandard/' + id,
    method: 'delete'
  })
}

// 导出包材标准
export function exportBcStandard(query) {
  return request({
    url: '/order/bcStandard/export',
    method: 'get',
    params: query
  })
}

export function batchSaveBcStandard(data) {
  return request({
    url: '/order/bcStandard/batchSave',
    method: 'post',
    data: data
  })
}

export function allBcStandardByMd003Array(data) {
  return request({
    url: '/order/bcStandard/allByMd003Set',
    method: 'post',
    data,
  })
}

export function allBcStandard(query) {
  return request({
    url: '/order/bcStandard/all',
    method: 'get',
    params: query
  })
}
