import request from '@/utils/request'

// 查询ERP出库记录列表
export function listErpCkLog(query) {
  return request({
    url: '/order/erpCkLog/list',
    method: 'get',
    params: query
  })
}

// 查询ERP出库记录详细
export function getErpCkLog(id) {
  return request({
    url: '/order/erpCkLog/' + id,
    method: 'get'
  })
}

// 新增ERP出库记录
export function addErpCkLog(data) {
  return request({
    url: '/order/erpCkLog',
    method: 'post',
    data: data
  })
}

// 修改ERP出库记录
export function updateErpCkLog(data) {
  return request({
    url: '/order/erpCkLog',
    method: 'put',
    data: data
  })
}

// 删除ERP出库记录
export function delErpCkLog(id) {
  return request({
    url: '/order/erpCkLog/' + id,
    method: 'delete'
  })
}

// 导出ERP出库记录
export function exportErpCkLog(query) {
  return request({
    url: '/order/erpCkLog/export',
    method: 'get',
    params: query
  })
}

export function allErpCkLog(query) {
  return request({
    url: '/order/erpCkLog/all',
    method: 'get',
    params: query
  })
}

export function importTemplateErpCkLog() {
  return request({
    url: '/order/erpCkLog/importTemplate',
    method: 'get'
  })
}

