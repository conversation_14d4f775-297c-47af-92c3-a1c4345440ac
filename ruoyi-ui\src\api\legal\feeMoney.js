import request from '@/utils/request'

// 查询送检费用明细列表
export function listFeeMoney(query) {
  return request({
    url: '/legal/feeMoney/list',
    method: 'get',
    params: query
  })
}

// 查询送检费用明细详细
export function getFeeMoney(id) {
  return request({
    url: '/legal/feeMoney/' + id,
    method: 'get'
  })
}

// 新增送检费用明细
export function addFeeMoney(data) {
  return request({
    url: '/legal/feeMoney',
    method: 'post',
    data: data
  })
}

// 修改送检费用明细
export function updateFeeMoney(data) {
  return request({
    url: '/legal/feeMoney',
    method: 'put',
    data: data
  })
}

// 删除送检费用明细
export function delFeeMoney(id) {
  return request({
    url: '/legal/feeMoney/' + id,
    method: 'delete'
  })
}

// 导出送检费用明细
export function exportFeeMoney(query) {
  return request({
    url: '/legal/feeMoney/export',
    method: 'get',
    params: query
  })
}

export function allFeeMoney(query) {
  return request({
    url: '/legal/feeMoney/all',
    method: 'get',
    params: query,
  })
}

export function nonReconcilesListFee(query) {
  return request({
    url: '/legal/feeMoney/nonReconcilesList',
    method: 'get',
    params: query
  })
}
