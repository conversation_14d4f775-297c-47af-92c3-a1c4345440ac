{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\userTable.vue?vue&type=style&index=0&id=4f2a111a&scoped=true&lang=scss", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\userTable.vue", "mtime": 1753954679644}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1744596528942}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1744596530059}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1744596529996}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1744596552583}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoudGFibGUtd3JhcHBlciB7DQogIG1heC1oZWlnaHQ6IDgwdmg7DQoNCiAgLmJhc2UtdGFibGUgew0KDQogICAgdGhlYWQgew0KICAgICAgcG9zaXRpb246IHN0aWNreTsNCiAgICAgIHRvcDogMDsNCiAgICAgIHotaW5kZXg6IDM7DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["userTable.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmiBA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "userTable.vue", "sourceRoot": "src/views/production/dayHours", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-divider content-position=\"left\" >产线</el-divider>\r\n\r\n    <el-form ref=\"queryForm\" :model=\"queryParams\" label-width=\"80px\" size=\"mini\" >\r\n      <el-row>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"工号\" prop=\"userCode\">\r\n            <el-input v-model=\"queryParams.userCode\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"姓名\" prop=\"nickName\">\r\n            <el-input v-model=\"queryParams.nickName\" />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"filterUser\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <div class=\"table-wrapper\">\r\n      <table class=\"base-table small-table\" >\r\n        <colgroup>\r\n          <col style=\"width: 50px\" /><!-- 序号 -->\r\n          <col style=\"width: 100px\" /><!-- 员工姓名 -->\r\n          <col style=\"width: 120px\" /><!-- 工号 -->\r\n          <col style=\"width: 100px\" /><!-- 类型 -->\r\n          <col style=\"width: 160px\" /><!-- 开始时间 -->\r\n          <col style=\"width: 160px\" /><!-- 结束时间 -->\r\n          <col style=\"width: 100px\" /><!-- 时长 -->\r\n          <col style=\"width: 100px\" /><!-- 异常状态 -->\r\n          <col style=\"width: 120px\" /><!-- 设备编号 -->\r\n          <col style=\"width: 160px\" /><!-- 批次号 -->\r\n          <col style=\"width: 120px\" /><!-- 工作日期 -->\r\n          <col style=\"width: 160px\" /><!-- 进站时间 -->\r\n          <col style=\"width: 160px\" /><!-- 出站时间 -->\r\n          <col style=\"width: 100px\" /><!-- 工时 -->\r\n          <col style=\"width: 100px\" /><!-- sap工时 -->\r\n          <col style=\"width: 100px\" /><!-- 休息工时 -->\r\n          <col style=\"width: 500px\" /><!-- 有效时段 -->\r\n          <col style=\"width: 100px\" /><!-- 有效工时 -->\r\n          <col style=\"width: 100px\" /><!-- 无效工时 -->\r\n          <col style=\"width: 100px\" /><!-- 工资工时 -->\r\n          <col style=\"width: 100px\" /><!-- 修正工时 -->\r\n          <col style=\"width: 120px\" /><!-- 异常状态 -->\r\n          <col style=\"width: 240px\" /><!-- 备注 -->\r\n        </colgroup>\r\n        <thead>\r\n          <tr >\r\n            <th :rowspan=\"2\" >序号</th>\r\n            <th :rowspan=\"2\" >员工姓名</th>\r\n            <th :rowspan=\"2\" >工号</th>\r\n            <th :colspan=\"5\" >工时对比</th>\r\n            <th :colspan=\"7\" >sap根据mes设备进出站记录拆解后的记录</th>\r\n            <th :rowspan=\"2\" >休息工时\r\n              <el-tooltip content=\"工厂标准休息时间与mes时间的交集半点向下取整\" >\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th :rowspan=\"2\" >\r\n              有效时段\r\n            </th>\r\n            <th :rowspan=\"2\" >\r\n              有效工时\r\n              <el-tooltip content=\"sap工时\" >\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th :rowspan=\"2\" >\r\n              无效工时\r\n              <el-tooltip content=\"工资工时-有效工时\" >\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th :rowspan=\"2\" >\r\n              工资工时\r\n              <el-tooltip >\r\n                <div slot=\"content\">\r\n                  mes时长半点向下取整\r\n                </div>\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th :rowspan=\"2\" >\r\n              修正工时\r\n              <el-tooltip >\r\n                <div slot=\"content\">\r\n                  修正过后以修正的工时为准\r\n                </div>\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th :rowspan=\"2\" >\r\n              异常状态\r\n              <el-tooltip >\r\n                <div slot=\"content\">\r\n                  <div>1.上工时间早于考勤打卡时间:上工考勤异常(代上工风险)</div>\r\n                  <div>2.下工时间晚于考勤打卡时间:下工考勤异常(代下工风险)</div>\r\n                  <div>3.下工 20分钟后未打卡:下班考勤异常</div>\r\n                  <div>4.末道产线记录出站,15分钟内员工未下工:下工异常</div>\r\n                  <div>5.上工后半小时没有匹配上产线:上工异常</div>\r\n                  <div>6.转场超过半小时:转场异常 (扣除标准休息时间)</div>\r\n                  <div>7.有效工时低于工资工时 85%,有效工时异常.</div>\r\n                  <div>8.末道产线记录出站后,超出 15 分钟后才下工,或没有下工数据的,且考勤时间晚于末道产线记录 20 分钟之后的,工资工时以末道产线记录出站/暂停时间为准.</div>\r\n                  <div>9.没有上下班考勤记录的,工资工时统一记录为 0,需人工核实,修改工资工时需审批</div>\r\n                </div>\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th :rowspan=\"2\" >备注</th>\r\n          </tr>\r\n          <tr>\r\n            <th >类型</th>\r\n            <th >开始时间</th>\r\n            <th >结束时间</th>\r\n            <th >时长</th>\r\n            <th >\r\n              异常状态\r\n              <el-tooltip >\r\n                <div slot=\"content\">\r\n                  <div>1.上工时间早于考勤打卡时间:上工考勤异常(代上工风险)</div>\r\n                  <div>2.下工时间晚于考勤打卡时间:下工考勤异常(代下工风险)</div>\r\n                  <div>3.下工 20分钟后未打卡:下班考勤异常</div>\r\n                  <div>4.末道产线记录出站,15分钟内员工未下工:下工异常</div>\r\n                  <div>5.上工后半小时没有匹配上产线:上工异常</div>\r\n                  <div>6.转场超过半小时:转场异常 (扣除标准休息时间)</div>\r\n                  <div>7.有效工时低于工资工时 85%,有效工时异常.</div>\r\n                  <div>8.末道产线记录出站后,超出 15 分钟后才下工,或没有下工数据的,且考勤时间晚于末道产线记录 20 分钟之后的,工资工时以末道产线记录出站/暂停时间为准.</div>\r\n                  <div>9.没有上下班考勤记录的,工资工时统一记录为 0,需人工核实,修改工资工时需审批</div>\r\n                </div>\r\n                <i class=\"el-icon-question\" />\r\n              </el-tooltip>\r\n            </th>\r\n            <th >设备编号</th>\r\n            <th >批次号</th>\r\n            <th >工作日期</th>\r\n            <th >进站时间</th>\r\n            <th >出站时间</th>\r\n            <th >工时</th>\r\n            <th >sap工时</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr v-for=\"(u,i) in filterUserArray\" :key=\"u.userId\">\r\n            <td >{{i+1}}</td>\r\n            <td >{{u.nickName}}</td>\r\n            <td >{{u.userCode}}</td>\r\n            <td :colspan=\"5\" style=\"width: 620px;padding: 0\" >\r\n              <table class=\"base-table small-table\" >\r\n                <tr>\r\n                  <th style=\"width: 100px\" >考勤</th>\r\n                  <td style=\"width: 160px\" >\r\n                    <span style=\"color: #1c84c6;cursor: pointer\" @click=\"selectAttendanceLog(u,'attendanceStart')\">\r\n                      {{u.attendanceStartTime}}\r\n                    </span>\r\n                  </td>\r\n                  <td style=\"width: 160px\" >\r\n                    <span style=\"color: #1c84c6;cursor: pointer\" @click=\"selectAttendanceLog(u,'attendanceEnd')\">\r\n                      {{u.attendanceEndTime}}\r\n                    </span>\r\n                  </td>\r\n                  <td style=\"width: 100px\" >\r\n                    <span style=\"color: #1c84c6;cursor: pointer\" @click=\"attendanceLog(u.userId)\">\r\n                      {{minutesToHours(u.attendanceMinutes).toFixed(2)}}\r\n                    </span>\r\n                  </td>\r\n                  <td :rowspan=\"3\" style=\"width: 100px\" >\r\n                    <div v-for=\"e in u.exceptionArray\" :key=\"e\" style=\"color: #F56C6C\" >\r\n                      {{selectOptionsLabel(exceptionOptions,e)}}\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n                <tr>\r\n                  <th style=\"width: 100px\" >mes</th>\r\n                  <td style=\"width: 160px\" >{{u.mesMinTime}}</td>\r\n                  <td style=\"width: 160px\" >{{u.mesMaxTime}}</td>\r\n                  <td style=\"width: 100px\" >\r\n                    <span style=\"color: #1c84c6;cursor: pointer\" @click=\"mesLog(u)\">\r\n                      {{minutesToHours(u.mesMinutes).toFixed(2)}}\r\n                    </span>\r\n                  </td>\r\n                </tr>\r\n                <tr>\r\n                  <th style=\"width: 100px\" >sap</th>\r\n                  <td style=\"width: 160px\" >{{u.sapMinTime}}</td>\r\n                  <td style=\"width: 160px\" >{{u.sapMaxTime}}</td>\r\n                  <td style=\"width: 100px\" >{{minutesToHours(u.sapMinutes).toFixed(2)}}</td>\r\n                </tr>\r\n              </table>\r\n            </td>\r\n            <td :colspan=\"6\" style=\"width: 820px;padding: 0\" >\r\n              <table class=\"base-table small-table\" >\r\n                <tr v-for=\"h in u.sapArray\" :key=\"h.id\">\r\n                  <td style=\"width: 120px\" >{{h.equipmentNo}}</td>\r\n                  <td style=\"width: 160px\" >\r\n                    <span style=\"color: #1c84c6;cursor: pointer\" @click=\"mesLotLogs(h)\">\r\n                      {{h.lotNo}}\r\n                    </span>\r\n                  </td>\r\n                  <td style=\"width: 120px\" >{{h.workDate}}</td>\r\n                  <td style=\"width: 160px\" >{{h.startTime}}</td>\r\n                  <td style=\"width: 160px\" >{{h.endTime}}</td>\r\n                  <td style=\"width: 100px\" >{{minutesToHours(h.minutes).toFixed(2)}}</td>\r\n                </tr>\r\n              </table>\r\n            </td>\r\n            <td >{{minutesToHours(u.sapSumMinutes).toFixed(2)}}</td>\r\n            <td >{{minutesToHours(u.restMinutes).toFixed(2)}}</td>\r\n            <td >\r\n              <MesTimeLine\r\n                :time-array=\"u.timeArray\"\r\n                :attendance-array=\"u.attendanceArray\"\r\n                :mes-array=\"u.mesArray\"\r\n                :sap-array=\"u.sapArray\"\r\n              />\r\n            </td>\r\n            <td >{{minutesToHours(u.effectiveMinutes).toFixed(2)}}</td>\r\n            <td >{{minutesToHours(u.invalidMinutes).toFixed(2)}}</td>\r\n            <td >\r\n              {{minutesToHours(u.wagesMinutes).toFixed(2)}}\r\n            </td>\r\n            <td >\r\n              <!-- v-if=\"u.exceptionArray && u.exceptionArray.length\" -->\r\n              <el-input v-model=\"u.finalMinutes\" autosize size=\"mini\" @input=\"$emit('computeItemData')\" />\r\n            </td>\r\n            <td >\r\n              <div v-for=\"e in u.exceptionArray\" :key=\"e\" style=\"color: #F56C6C\" >\r\n                {{selectOptionsLabel(exceptionOptions,e)}}\r\n              </div>\r\n            </td>\r\n            <td >\r\n              <el-input v-model=\"u.remark\" autosize size=\"mini\" />\r\n            </td>\r\n          </tr>\r\n          <tr>\r\n            <th>合计</th>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td>{{sumHours}}</td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n            <td></td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <el-dialog :close-on-click-modal=\"false\" :visible.sync=\"selectAttendanceLogOpen\" append-to-body width=\"600px\">\r\n      <table class=\"base-table small-table\">\r\n        <tr>\r\n          <th style=\"width: 320px\">打卡地址</th>\r\n          <th style=\"width: 180px\">打卡时间</th>\r\n        </tr>\r\n        <tr v-for=\"item in attendanceLogList\" :key=\"item.id\" >\r\n          <td>{{item.userAddress}}</td>\r\n          <td>\r\n            <span style=\"color: #00afff;cursor: pointer\" @click=\"selectUserTime(item.userCheckTime)\" >\r\n              {{item.userCheckTime}}\r\n            </span>\r\n          </td>\r\n        </tr>\r\n      </table>\r\n    </el-dialog>\r\n\r\n    <el-dialog :close-on-click-modal=\"false\" :visible.sync=\"attendanceLogOpen\" append-to-body width=\"600px\">\r\n      <table class=\"base-table small-table\">\r\n        <tr>\r\n          <th style=\"width: 320px\">打卡地址</th>\r\n          <th style=\"width: 180px\">打卡时间</th>\r\n        </tr>\r\n        <tr v-for=\"item in attendanceLogList\" :key=\"item.id\" >\r\n          <td>{{item.userAddress}}</td>\r\n          <td>{{item.userCheckTime}}</td>\r\n        </tr>\r\n      </table>\r\n    </el-dialog>\r\n\r\n    <el-dialog :fullscreen=\"fullscreenFlag\" :visible.sync=\"open\" width=\"1200px\" :close-on-click-modal=\"false\" append-to-body>\r\n      <div class=\"dialog-title\" slot=\"title\">{{ title }}\r\n        <el-button @click=\"fullscreenFlag = !fullscreenFlag\" type=\"text\"\r\n                   :icon=\"fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'\"/>\r\n      </div>\r\n      <MesHoursList ref=\"mesHoursList\" :work-date=\"dayHours.workDate\" :user-code=\"currentRow.userCode\" @sailingsChange=\"sailingsChange\" />\r\n    </el-dialog>\r\n\r\n    <el-dialog :title=\"title\" :fullscreen=\"fullscreenFlag\" :visible.sync=\"planOpen\" width=\"1200px\" :close-on-click-modal=\"false\"  append-to-body>\r\n      <div class=\"dialog-title\" slot=\"title\">\r\n        {{title}}\r\n        <el-button @click=\"fullscreenFlag = !fullscreenFlag\" type=\"text\"\r\n                   :icon=\"fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'\" />\r\n      </div>\r\n      <MesProductPlanSave ref=\"mesProductPlanSave\" :readonly=\"true\" :plan-type=\"currentRow.planType\" />\r\n    </el-dialog>\r\n\r\n    <el-dialog :fullscreen=\"fullscreenFlag\" :visible.sync=\"logOpen\" width=\"1200px\" :close-on-click-modal=\"false\"\r\n               append-to-body>\r\n      <div class=\"dialog-title\" slot=\"title\">{{ title }}\r\n        <el-button @click=\"fullscreenFlag = !fullscreenFlag\" type=\"text\"\r\n                   :icon=\"fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'\"/>\r\n      </div>\r\n      <div class=\"table-wrapper\">\r\n        <table class=\"base-table small-table\" >\r\n          <tr>\r\n            <th style=\"width: 80px\" >作业站</th>\r\n            <th style=\"width: 120px\" >类型</th>\r\n            <th style=\"width: 120px\" >时间</th>\r\n            <th style=\"width: 180px\" >暂停原因</th>\r\n            <th style=\"width: 100px\" >数量</th>\r\n            <th style=\"width: 120px\" >人员编号</th>\r\n            <th style=\"width: 80px\" >人员名称</th>\r\n          </tr>\r\n          <tr v-for=\"item in mesLogArray\" :key=\"item.lotNo\" >\r\n            <td style=\"width: 80px\" >{{item.opNo}}</td>\r\n            <td style=\"width: 120px\" >{{ selectOptionsLabel(opTypeOptions, item.opType)}}</td>\r\n            <td style=\"width: 120px\" >{{item.createTime}}</td>\r\n            <td style=\"width: 180px\" >{{item.reasonName}}</td>\r\n            <td style=\"width: 100px\" >{{item.qty}}</td>\r\n            <td style=\"width: 120px\" >{{item.userNo}}</td>\r\n            <td style=\"width: 80px\" >{{item.userName}}</td>\r\n          </tr>\r\n        </table>\r\n      </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n<script >\r\nimport {allAttendanceLog} from \"@/api/hr/attendanceLog\";\r\nimport MesHoursList from \"@/views/production/mesHours/list.vue\";\r\nimport MesProductPlanSave from \"@/views/mes/production/production/save.vue\";\r\nimport {getSchedulePlanByCode} from \"@/api/production/schedulePlan\";\r\nimport {getDispositionPlan} from \"@/api/production/dispositionPlan\";\r\nimport MesTimeLine from \"@/views/production/dayHours/mesTimeLine.vue\";\r\nimport {allMesLotWaitVo, allWipLotLog} from \"@/api/mes/mesView\";\r\nimport {diffMinutes} from \"@/utils/production/time\";\r\n\r\nexport default {\r\n  name: 'dayHoursUserTable',\r\n  components: {MesTimeLine, MesProductPlanSave, MesHoursList},\r\n  props: {\r\n    userArray: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    mesHoursList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    sumHours: {\r\n      type: Number,\r\n      required: true,\r\n    },\r\n    dayHours: {\r\n      type: Object,\r\n      required: true,\r\n    },\r\n  },\r\n  watch: {\r\n    userArray: {\r\n      async handler() {\r\n        await this.filterUser()\r\n      },\r\n      immediate: true,\r\n      deep: true,\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      loading: false,\r\n      title: '',\r\n      fullscreenFlag: true,\r\n      attendanceLogOpen: false,\r\n      selectAttendanceLogOpen: false,\r\n      open: false,\r\n      planOpen: false,\r\n      queryParams: {\r\n        userCode: null,\r\n        nickName: null,\r\n      },\r\n      statusOptions: [],\r\n      filterUserArray: [],\r\n      attendanceLogList: [],\r\n      currentRow: {},\r\n      logOpen: false,\r\n      mesLogArray: [],\r\n      opTypeOptions: [\r\n        {label: '进站',value: 'CHECKIN'},\r\n        {label: '暂停',value: 'WAITDISPOSITION'},\r\n        {label: '解除暂停-继续生产',value: 'RELEASE-GO'},\r\n        {label: '出站',value: 'CHECKOUT'},\r\n        {label: '解除暂停-结束生产',value: 'RELEASE-Inventory'},\r\n        {label: '设备变更',value: 'EQPCHANGE'},\r\n        {label: '开批',value: 'LOTCREATE'},\r\n      ],\r\n      exceptionOptions: [\r\n        {label: '上工考勤异常',value: 1},\r\n        {label: '下工考勤异常',value: 2},\r\n        {label: 'mes上工异常',value: 3},\r\n        {label: 'mes下工异常',value: 4},\r\n        {label: '转场异常',value: 5},\r\n        {label: '有效工时异常',value: 6},\r\n        {label: 'sap上工异常',value: 7},\r\n        {label: 'sap下工异常',value: 8},\r\n      ],\r\n      currentType: null,\r\n    }\r\n  },\r\n  async created() {\r\n    this.getDicts(\"production_status\").then(response => {\r\n      this.statusOptions = response.data\r\n    })\r\n  },\r\n  methods: {\r\n    sailingsChange(userCode){\r\n      this.$emit('sailingsChange',userCode)\r\n    },\r\n    async mesLotLogs(row) {\r\n      this.logOpen = true\r\n      this.title = row.lotNo + \"关联的生产批操作记录\";\r\n      const waitList = await allMesLotWaitVo({lotNo: row.lotNo})\r\n      const lotLogs = await allWipLotLog({lotNo: row.lotNo})\r\n      for (const lotLog of lotLogs) {\r\n        if(lotLog.opType === 'WAITDISPOSITION') {\r\n          const arr = waitList.filter(i=> i.waitDate === lotLog.createTime)\r\n          if(arr && arr[0]) {\r\n            lotLog.reasonName = arr[0].reasonName\r\n          }\r\n        }\r\n      }\r\n      this.mesLogArray = lotLogs\r\n    },\r\n    async planView(row) {\r\n      this.currentRow = row\r\n      this.planOpen = true\r\n      this.title = '计划详情'\r\n      await this.$nextTick()\r\n      const mesProductPlanSave = this.$refs.mesProductPlanSave\r\n      if(mesProductPlanSave) {\r\n        mesProductPlanSave.reset()\r\n        let res\r\n        if(row.planType === 'production') {\r\n          res = await getSchedulePlanByCode(row.planCode)\r\n        } else {\r\n          res = await getDispositionPlan(row.planCode)\r\n        }\r\n        await mesProductPlanSave.init(res.data.id)\r\n      }\r\n    },\r\n    async mesLog(row) {\r\n      this.currentRow = row\r\n      this.open = true\r\n      this.title = 'mes工时明细'\r\n      await this.$nextTick()\r\n      await this.$refs.mesHoursList.getList()\r\n    },\r\n    async selectUserTime(time) {\r\n      this.currentRow[this.currentType + 'Time'] = time\r\n      this.currentRow.attendanceMinutes = diffMinutes(this.currentRow.attendanceEndTime,this.currentRow.attendanceStartTime)\r\n      this.currentRow.attendanceArray = [{startTime: this.currentRow.attendanceStartTime,endTime: this.currentRow.attendanceEndTime}]\r\n      this.selectAttendanceLogOpen = false\r\n    },\r\n    async selectAttendanceLog(user,type) {\r\n      const workDate = this.dayHours.workDate\r\n      if(workDate) {\r\n        const searchDateArray = [workDate,]\r\n        searchDateArray.push(this.moment(workDate).add(1, 'days').format('YYYY-MM-DD'))\r\n        const params = {\r\n          userId: user.userId,\r\n          searchDateArray\r\n        }\r\n        try {\r\n          this.btnLoading = true\r\n          const attendanceLogList = await allAttendanceLog(params)\r\n          for (const item of attendanceLogList) {\r\n            if(item.userCheckTime) {\r\n              item.userCheckTime = this.moment(item.userCheckTime).format('YYYY-MM-DD HH:mm:ss')\r\n            }\r\n          }\r\n          this.attendanceLogList = attendanceLogList\r\n          this.btnLoading = false\r\n          this.currentRow = user\r\n          this.currentType = type\r\n          this.selectAttendanceLogOpen = true\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n    async attendanceLog(userId) {\r\n      const workDate = this.dayHours.workDate\r\n      if(workDate) {\r\n        const searchDateArray = [workDate,]\r\n        searchDateArray.push(this.moment(workDate).add(1, 'days').format('YYYY-MM-DD'))\r\n        const params = {\r\n          userId,\r\n          searchDateArray\r\n        }\r\n        try {\r\n          this.btnLoading = true\r\n          const attendanceLogList = await allAttendanceLog(params)\r\n          for (const item of attendanceLogList) {\r\n            if(item.userCheckTime) {\r\n              item.userCheckTime = this.moment(item.userCheckTime).format('YYYY-MM-DD HH:mm:ss')\r\n            }\r\n          }\r\n          this.attendanceLogList = attendanceLogList\r\n          this.btnLoading = false\r\n          this.attendanceLogOpen = true\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.filterUser()\r\n    },\r\n    filterUser() {\r\n      let filterUserArray = this.userArray\r\n      const queryParams = this.queryParams\r\n      if (queryParams.userCode) {\r\n        filterUserArray = filterUserArray.filter(i => i.userCode === queryParams.userCode)\r\n      }\r\n      if (queryParams.nickName) {\r\n        filterUserArray = filterUserArray.filter(i => i.nickName === queryParams.nickName)\r\n      }\r\n      this.filterUserArray = filterUserArray\r\n    },\r\n  },\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.table-wrapper {\r\n  max-height: 80vh;\r\n\r\n  .base-table {\r\n\r\n    thead {\r\n      position: sticky;\r\n      top: 0;\r\n      z-index: 3;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}