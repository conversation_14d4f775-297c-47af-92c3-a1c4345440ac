import request from '@/utils/request'

// 查询打卡方案列表
export function listClockPlan(query) {
  return request({
    url: '/hr/clockPlan/list',
    method: 'get',
    params: query
  })
}

// 查询打卡方案详细
export function getClockPlan(id) {
  return request({
    url: '/hr/clockPlan/' + id,
    method: 'get'
  })
}

// 新增打卡方案
export function addClockPlan(data) {
  return request({
    url: '/hr/clockPlan',
    method: 'post',
    data: data
  })
}

// 修改打卡方案
export function updateClockPlan(data) {
  return request({
    url: '/hr/clockPlan',
    method: 'put',
    data: data
  })
}

// 删除打卡方案
export function delClockPlan(id) {
  return request({
    url: '/hr/clockPlan/' + id,
    method: 'delete'
  })
}

// 导出打卡方案
export function exportClockPlan(query) {
  return request({
    url: '/hr/clockPlan/export',
    method: 'get',
    params: query
  })
}


// 查询全部打卡方案
export function getClockPlanAll() {
  return request({
    url: '/hr/clockPlan/all',
    method: 'get'
  })
}
