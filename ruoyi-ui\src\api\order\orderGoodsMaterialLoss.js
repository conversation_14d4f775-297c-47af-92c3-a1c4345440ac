import request from '@/utils/request'

// 查询订单商品物料损益明细列表
export function listOrderGoodsMaterialLoss(query) {
  return request({
    url: '/order/orderGoodsMaterialLoss/list',
    method: 'get',
    params: query
  })
}
// 查询订单商品物料损益明细列表
export function listOrderGoodsMaterialDataLoss(query) {
  return request({
    url: '/order/orderGoodsMaterialLoss/dataList',
    method: 'get',
    params: query
  })
}


// 查询订单商品物料损益明细详细
export function getOrderGoodsMaterialLoss(id) {
  return request({
    url: '/order/orderGoodsMaterialLoss/' + id,
    method: 'get'
  })
}

// 新增订单商品物料损益明细
export function addOrderGoodsMaterialLoss(data) {
  return request({
    url: '/order/orderGoodsMaterialLoss',
    method: 'post',
    data: data
  })
}

// 修改订单商品物料损益明细
export function updateOrderGoodsMaterialLoss(data) {
  return request({
    url: '/order/orderGoodsMaterialLoss',
    method: 'put',
    data: data
  })
}

// 删除订单商品物料损益明细
export function delOrderGoodsMaterialLoss(id) {
  return request({
    url: '/order/orderGoodsMaterialLoss/' + id,
    method: 'delete'
  })
}

// 导出订单商品物料损益明细
export function exportOrderGoodsMaterialLoss(query) {
  return request({
    url: '/order/orderGoodsMaterialLoss/export',
    method: 'get',
    params: query
  })
}

//根据工单品号 获取订单内容
export function queryOrderGoodsFinishedDetailData(query) {
  return request({
    url: '/order/orderGoodsMaterialLoss/queryOrderGoodsFinishedDetailData',
    method: 'get',
    params: query
  })
}
