import request from '@/utils/request'

// 查询送检备案统计数据列表
export function listSjbaProcess(query) {
  return request({
    url: '/project/sjbaProcess/list',
    method: 'get',
    params: query
  })
}

// 查询送检备案统计数据详细
export function getSjbaProcess(id) {
  return request({
    url: '/project/sjbaProcess/' + id,
    method: 'get'
  })
}

// 新增送检备案统计数据
export function addSjbaProcess(data) {
  return request({
    url: '/project/sjbaProcess',
    method: 'post',
    data: data
  })
}

// 修改送检备案统计数据
export function updateSjbaProcess(data) {
  return request({
    url: '/project/sjbaProcess',
    method: 'put',
    data: data
  })
}

// 删除送检备案统计数据
export function delSjbaProcess(id) {
  return request({
    url: '/project/sjbaProcess/' + id,
    method: 'delete'
  })
}

// 导出送检备案统计数据
export function exportSjbaProcess(query) {
  return request({
    url: '/project/sjbaProcess/export',
    method: 'get',
    params: query
  })
}