{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\project\\project\\materialTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\project\\project\\materialTable.vue", "mtime": 1753954679647}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\babel.config.js", "mtime": 1743382537964}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9zZWFuL3dvcmtzcGFjZS9lbm93X3Byb2plY3QvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5lcnJvci5jYXVzZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maW5kLWluZGV4LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuam9pbi5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5wdXNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5zbGljZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc3BsaWNlLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuZmlsdGVyLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmpzb24uc3RyaW5naWZ5LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3Qua2V5cy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLmluY2x1ZGVzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaXRlcmF0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IuanMiKTsKdmFyIF9yZWdlbmVyYXRvclJ1bnRpbWUyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJDOi9zZWFuL3dvcmtzcGFjZS9lbm93X3Byb2plY3QvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvcmVnZW5lcmF0b3JSdW50aW1lLmpzIikpOwp2YXIgX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJDOi9zZWFuL3dvcmtzcGFjZS9lbm93X3Byb2plY3QvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlci5qcyIpKTsKdmFyIF9hc3luY1RvR2VuZXJhdG9yMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQzovc2Vhbi93b3Jrc3BhY2UvZW5vd19wcm9qZWN0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2FzeW5jVG9HZW5lcmF0b3IuanMiKSk7CnZhciBfZXJwID0gcmVxdWlyZSgiQC9hcGkvY29tbW9uL2VycCIpOwp2YXIgX3NlbGVjdFRhYmxlID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3ZpZXdzL3Jlc291cmNlL21hdGVyaWFsR29vZHMvc2VsZWN0VGFibGUudnVlIikpOwp2YXIgX3N1cHBsaWVyID0gcmVxdWlyZSgiQC9hcGkvc3VwcGxpZXIvc3VwcGxpZXIiKTsKdmFyIF9iY0xvZyA9IHJlcXVpcmUoIkAvYXBpL3Byb2plY3QvYmNMb2ciKTsKdmFyIF9iY1ByaWNlVGFibGUgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdmlld3Mvc29wL2JjL2JjUHJpY2VUYWJsZS52dWUiKSk7CnZhciBfY3VzdG9tQ29scyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC9jb21wb25lbnRzL2N1c3RvbUNvbHMudnVlIikpOwp2YXIgX3RyZWVEYXRhID0gcmVxdWlyZSgiQC9hcGkvc3lzdGVtL3RyZWVEYXRhIik7CnZhciBfc2NoZWR1bGVQbGFuID0gcmVxdWlyZSgiQC9hcGkvcHJvZHVjdGlvbi9zY2hlZHVsZVBsYW4iKTsKdmFyIF9iYyA9IHJlcXVpcmUoIkAvYXBpL3Byb2plY3QvYmMiKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICdwcm9qZWN0QmNUYWJsZScsCiAgY29tcG9uZW50czogewogICAgQ3VzdG9tQ29sczogX2N1c3RvbUNvbHMuZGVmYXVsdCwKICAgIEJjUHJpY2VUYWJsZTogX2JjUHJpY2VUYWJsZS5kZWZhdWx0LAogICAgTWF0ZXJpYWxHb29kc1NlbGVjdFRhYmxlOiBfc2VsZWN0VGFibGUuZGVmYXVsdAogIH0sCiAgcHJvcHM6IHsKICAgIGZvcm06IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfSwKICAgIG1hdGVyaWFsTGlzdDogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0sCiAgICBkZXZTdGF0dXM6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnMCcKICAgIH0sCiAgICByZWFkb25seTogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgICdmb3JtLmlkJzogewogICAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKHYpIHsKICAgICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgICAgICB2YXIgYmNMb2dMaXN0LCBsb2dBcnJheSwgX2l0ZXJhdG9yLCBfc3RlcCwgX2xvb3A7CiAgICAgICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlJChfY29udGV4dDMpIHsKICAgICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQzLnByZXYgPSBfY29udGV4dDMubmV4dCkgewogICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgIF90aGlzLnJlc2V0UXVlcnkoKTsKICAgICAgICAgICAgICAgIGlmICghKHYgJiYgX3RoaXMucmVhZG9ubHkpKSB7CiAgICAgICAgICAgICAgICAgIF9jb250ZXh0My5uZXh0ID0gMjQ7CiAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgX2NvbnRleHQzLm5leHQgPSA0OwogICAgICAgICAgICAgICAgcmV0dXJuICgwLCBfYmNMb2cuYWxsQmNMb2cpKHsKICAgICAgICAgICAgICAgICAgcHJvamVjdElkOiB2LAogICAgICAgICAgICAgICAgICBzdGF0dXM6ICczJwogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgICAgYmNMb2dMaXN0ID0gX2NvbnRleHQzLnNlbnQ7CiAgICAgICAgICAgICAgICBsb2dBcnJheSA9IFtdOwogICAgICAgICAgICAgICAgX2l0ZXJhdG9yID0gKDAsIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyMi5kZWZhdWx0KShfdGhpcy5sb2dPcHRpb25zKTsKICAgICAgICAgICAgICAgIF9jb250ZXh0My5wcmV2ID0gNzsKICAgICAgICAgICAgICAgIF9sb29wID0gLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLm1hcmsoZnVuY3Rpb24gX2xvb3AoKSB7CiAgICAgICAgICAgICAgICAgIHZhciBpdGVtLCBiY0xvZywgYXJyYXksIGtleXMsIF9pdGVyYXRvcjIsIF9zdGVwMiwgYmMsIF9sb29wMiwgX2ksIF9rZXlzOwogICAgICAgICAgICAgICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkud3JhcChmdW5jdGlvbiBfbG9vcCQoX2NvbnRleHQyKSB7CiAgICAgICAgICAgICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQyLnByZXYgPSBfY29udGV4dDIubmV4dCkgewogICAgICAgICAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgICAgICAgICBpdGVtID0gX3N0ZXAudmFsdWU7CiAgICAgICAgICAgICAgICAgICAgICAgIGJjTG9nID0gYmNMb2dMaXN0LmZpbHRlcihmdW5jdGlvbiAoaSkgewogICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBpLnByb2plY3RJZCA9PT0gdiAmJiBpLnR5cGUgPT09IGl0ZW0uZGljdFZhbHVlOwogICAgICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCEoYmNMb2cgJiYgYmNMb2cubGVuZ3RoID4gMCkpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDE1OwogICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgIGFycmF5ID0gW107CiAgICAgICAgICAgICAgICAgICAgICAgIGtleXMgPSBbXTsKICAgICAgICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yMiA9ICgwLCBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcjIuZGVmYXVsdCkoYmNMb2cpOwogICAgICAgICAgICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgICAgICAgICAgIGZvciAoX2l0ZXJhdG9yMi5zKCk7ICEoX3N0ZXAyID0gX2l0ZXJhdG9yMi5uKCkpLmRvbmU7KSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYyA9IF9zdGVwMi52YWx1ZTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICgha2V5cy5pbmNsdWRlcyhiYy5wcm9qZWN0SXRlbU9yZGVyQ29kZSkpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5cy5wdXNoKGJjLnByb2plY3RJdGVtT3JkZXJDb2RlKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgICAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjIuZShlcnIpOwogICAgICAgICAgICAgICAgICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjIuZigpOwogICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgIF9sb29wMiA9IC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS5tYXJrKGZ1bmN0aW9uIF9sb29wMigpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICB2YXIgaywgcHJvamVjdEJjSWRzOwogICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9sb29wMiQoX2NvbnRleHQpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0LnByZXYgPSBfY29udGV4dC5uZXh0KSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrID0gX2tleXNbX2ldOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHByb2plY3RCY0lkcyA9IGJjTG9nLmZpbHRlcihmdW5jdGlvbiAoaSkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGkucHJvamVjdEl0ZW1PcmRlckNvZGUgPT09IGs7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkubWFwKGZ1bmN0aW9uIChpKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gaS5wcm9qZWN0QmNJZDsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhcnJheS5wdXNoKHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHByb2plY3RJdGVtT3JkZXJDb2RlOiBrLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvamVjdEJjSWRzOiBwcm9qZWN0QmNJZHMKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgfSwgX2xvb3AyKTsKICAgICAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgICAgICAgIF9pID0gMCwgX2tleXMgPSBrZXlzOwogICAgICAgICAgICAgICAgICAgICAgY2FzZSA5OgogICAgICAgICAgICAgICAgICAgICAgICBpZiAoIShfaSA8IF9rZXlzLmxlbmd0aCkpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDE0OwogICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuZGVsZWdhdGVZaWVsZChfbG9vcDIoKSwgInQwIiwgMTEpOwogICAgICAgICAgICAgICAgICAgICAgY2FzZSAxMToKICAgICAgICAgICAgICAgICAgICAgICAgX2krKzsKICAgICAgICAgICAgICAgICAgICAgICAgX2NvbnRleHQyLm5leHQgPSA5OwogICAgICAgICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgICAgICAgIGNhc2UgMTQ6CiAgICAgICAgICAgICAgICAgICAgICAgIGxvZ0FycmF5LnB1c2goewogICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiBpdGVtLmRpY3RMYWJlbCwKICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogaXRlbS5kaWN0VmFsdWUsCiAgICAgICAgICAgICAgICAgICAgICAgICAgYXJyYXk6IGFycmF5CiAgICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgICAgY2FzZSAxNToKICAgICAgICAgICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuc3RvcCgpOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgfSwgX2xvb3ApOwogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICBfaXRlcmF0b3IucygpOwogICAgICAgICAgICAgIGNhc2UgMTA6CiAgICAgICAgICAgICAgICBpZiAoKF9zdGVwID0gX2l0ZXJhdG9yLm4oKSkuZG9uZSkgewogICAgICAgICAgICAgICAgICBfY29udGV4dDMubmV4dCA9IDE0OwogICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDMuZGVsZWdhdGVZaWVsZChfbG9vcCgpLCAidDAiLCAxMik7CiAgICAgICAgICAgICAgY2FzZSAxMjoKICAgICAgICAgICAgICAgIF9jb250ZXh0My5uZXh0ID0gMTA7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICBjYXNlIDE0OgogICAgICAgICAgICAgICAgX2NvbnRleHQzLm5leHQgPSAxOTsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIGNhc2UgMTY6CiAgICAgICAgICAgICAgICBfY29udGV4dDMucHJldiA9IDE2OwogICAgICAgICAgICAgICAgX2NvbnRleHQzLnQxID0gX2NvbnRleHQzWyJjYXRjaCJdKDcpOwogICAgICAgICAgICAgICAgX2l0ZXJhdG9yLmUoX2NvbnRleHQzLnQxKTsKICAgICAgICAgICAgICBjYXNlIDE5OgogICAgICAgICAgICAgICAgX2NvbnRleHQzLnByZXYgPSAxOTsKICAgICAgICAgICAgICAgIF9pdGVyYXRvci5mKCk7CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQzLmZpbmlzaCgxOSk7CiAgICAgICAgICAgICAgY2FzZSAyMjoKICAgICAgICAgICAgICAgIF90aGlzLmxvZ0FycmF5ID0gbG9nQXJyYXk7CiAgICAgICAgICAgICAgICBfdGhpcy5iY0xvZ0xpc3QgPSBiY0xvZ0xpc3Q7CiAgICAgICAgICAgICAgY2FzZSAyNDoKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0My5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0sIF9jYWxsZWUsIG51bGwsIFtbNywgMTYsIDE5LCAyMl1dKTsKICAgICAgICB9KSkoKTsKICAgICAgfSwKICAgICAgaW1tZWRpYXRlOiB0cnVlCiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBuYW1lOiBudWxsCiAgICAgIH0sCiAgICAgIGN1cnJlbnRQYWdlOiAxLAogICAgICBwYWdlU2l6ZTogMTAsCiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICBidG5Mb2FkaW5nOiBmYWxzZSwKICAgICAgYm9tT3BlbjogZmFsc2UsCiAgICAgIGJjT3BlbjogZmFsc2UsCiAgICAgIG9wZW46IGZhbHNlLAogICAgICBmdWxsc2NyZWVuRmxhZzogZmFsc2UsCiAgICAgIGJjVHlwZU9wdGlvbnM6IFtdLAogICAgICBib21EYXRhOiBbXSwKICAgICAgYm9tVHJlZTogW10sCiAgICAgIGN1cnJlbnRCY0FycmF5OiBbXSwKICAgICAgdHlwZU9wdGlvbnM6IFt7CiAgICAgICAgbGFiZWw6ICfljIXmnZDlvIDlj5EnLAogICAgICAgIHZhbHVlOiAnMCcKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAn5a6i5oi35byA5Y+RJywKICAgICAgICB2YWx1ZTogJzEnCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+mHh+i0reW8gOWPkScsCiAgICAgICAgdmFsdWU6ICcyJwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICfoh6rliLYnLAogICAgICAgIHZhbHVlOiAnMycKICAgICAgfSAvLzIwMjQxMTIy56CU5Y+R5byA5Y+R5pS55Li66Ieq5Yi2CiAgICAgIF0sCiAgICAgIHJlc291cmNlVHlwZU9wdGlvbnM6IFt7CiAgICAgICAgbGFiZWw6ICfmma7pgJrmt7vliqAnLAogICAgICAgIHZhbHVlOiAnY3VzdG9tZXInCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ2JvbScsCiAgICAgICAgdmFsdWU6ICdib20nCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+WMheadkOW6kycsCiAgICAgICAgdmFsdWU6ICdiYycKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAn5YyF5p2Q5byA5Y+RJywKICAgICAgICB2YWx1ZTogJ2RldicKICAgICAgfV0sCiAgICAgIGF0dHJPcHRpb25zOiBbXSwKICAgICAgenJxT3B0aW9uczogW10sCiAgICAgIG1hdGVyaWFsVHlwZU9wdGlvbnM6IFtdLAogICAgICBzdXBwbGllckxpc3Q6IFtdLAogICAgICB0aXRsZTogbnVsbCwKICAgICAgY29sdW1uczogW3sKICAgICAgICBsYWJlbDogJ+W8gOWPkeexu+WeiycsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICfpobnnm67ljIXmnZDnvJbnoIEnLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAn57G75YirJywKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+WMheijheadkOaWmScsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICfljIXmnZDlupPnvJbnoIEnLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAn5YyF5p2QRVJQ57yW56CBJywKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+eJqeaWmeWxnuaApycsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICflkI3np7AnLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAn6KeE5qC8JywKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+WwuuWvuCcsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICflnovlj7cnLAogICAgICAgIHZpc2libGU6IHRydWUKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAn5L6b5bqU5ZWGJywKICAgICAgICB2aXNpYmxlOiB0cnVlCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+WbvueJhycsCiAgICAgICAgdmlzaWJsZTogZmFsc2UKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAnQ09BL1NQRUMnLAogICAgICAgIHZpc2libGU6IGZhbHNlCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+Wkh+azqCcsCiAgICAgICAgdmlzaWJsZTogdHJ1ZQogICAgICB9XSwKICAgICAgbG9nQXJyYXk6IFtdLAogICAgICBiY0xvZ0xpc3Q6IFtdLAogICAgICBsb2dPcHRpb25zOiBbXSwKICAgICAgcHJpY2VBcnJheTogW10sCiAgICAgIGNvbmZpZ0FycmF5OiBbXSwKICAgICAgZmlsZXM6IFtdLAogICAgICBjdXJyZW50Um93OiB7fSwKICAgICAgZ3JhZGVkVHlwZU9wdGlvbnM6IFt7CiAgICAgICAgbGFiZWw6ICforqLljZXku7cnLAogICAgICAgIHZhbHVlOiAnMCcKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAnTU9R5Lu3JywKICAgICAgICB2YWx1ZTogJzEnCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+air+W6puS7tyjkuIDmoaMpJywKICAgICAgICB2YWx1ZTogJzInCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+air+W6puS7tyjkuozmoaMpJywKICAgICAgICB2YWx1ZTogJzMnCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+air+W6puS7tyjkuInmoaMpJywKICAgICAgICB2YWx1ZTogJzQnCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+air+W6puS7tyjlm5vmoaMpJywKICAgICAgICB2YWx1ZTogJzUnCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+air+W6puS7tyjkupTmoaMpJywKICAgICAgICB2YWx1ZTogJzYnCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+air+W6puS7tyjlha3moaMpJywKICAgICAgICB2YWx1ZTogJzcnCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+air+W6puS7tyjkuIPmoaMpJywKICAgICAgICB2YWx1ZTogJzgnCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+air+W6puS7tyjlhavmoaMpJywKICAgICAgICB2YWx1ZTogJzknCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+air+W6puS7tyjkuZ3moaMpJywKICAgICAgICB2YWx1ZTogJzEwJwogICAgICB9XSwKICAgICAgc3RhZ2VPcHRpb25zOiBbewogICAgICAgIGxhYmVsOiAn6KO45YyF5Lu3JywKICAgICAgICB2YWx1ZTogJzAnCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ+Wvu+agt+mYtuautScsCiAgICAgICAgdmFsdWU6ICcxJwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICfmiZPmoLfpmLbmrrUnLAogICAgICAgIHZhbHVlOiAnMicKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAn6K6i5Y2V6Zi25q61JywKICAgICAgICB2YWx1ZTogJzMnCiAgICAgIH1dLAogICAgICBhbGxvY2F0b3JPcHRpb25zOiBbXSwKICAgICAgZXJwQ29kZTogW10sCiAgICAgIHR5cGVPcGVuOiBmYWxzZSwKICAgICAgdHJlZU9wZW46IGZhbHNlLAogICAgICBtYjAwNU9wdGlvbnM6IFt7CiAgICAgICAgbGFiZWw6ICfljIXmnZAnLAogICAgICAgIHZhbHVlOiAnMTA0JwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICfljYrmiJDlk4EnLAogICAgICAgIHZhbHVlOiAnMTAzJwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICfoo7joo4Xlk4EnLAogICAgICAgIHZhbHVlOiAnMTAyJwogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICfmiJDlk4EnLAogICAgICAgIHZhbHVlOiAnMTAxJwogICAgICB9XSwKICAgICAgcnVsZXM6IHsKICAgICAgICBtYjAwNTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbXNnOiAn6K+36YCJ5oup54mp5paZ57G75Z6LJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIG1hdGVyaWFsQXJyYXk6IFtdCiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTIoKSB7CiAgICAgIHZhciBsb2dSZXMsIGJjVHlwZVJlcywgc3VwcGxpZXJMaXN0OwogICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlMiQoX2NvbnRleHQ0KSB7CiAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ0LnByZXYgPSBfY29udGV4dDQubmV4dCkgewogICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICBfdGhpczIuZ2V0RGljdHMoIlBST0RVQ1RfUFJPUEVSVElFUyIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgdmFyIGF0dHJPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICAgICAgICBhdHRyT3B0aW9ucy5wdXNoKHsKICAgICAgICAgICAgICAgIGRpY3RMYWJlbDogJ+iHquWIticsCiAgICAgICAgICAgICAgICBkaWN0VmFsdWU6ICcwJwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIGF0dHJPcHRpb25zLnB1c2goewogICAgICAgICAgICAgICAgZGljdExhYmVsOiAn5aSW6LStJywKICAgICAgICAgICAgICAgIGRpY3RWYWx1ZTogJzEnCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgYXR0ck9wdGlvbnMucHVzaCh7CiAgICAgICAgICAgICAgICBkaWN0TGFiZWw6ICflrqLmjIfku6Pph4cnLAogICAgICAgICAgICAgICAgZGljdFZhbHVlOiAnMycKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICBfdGhpczIuYXR0ck9wdGlvbnMgPSBhdHRyT3B0aW9uczsKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIF9jb250ZXh0NC5uZXh0ID0gMzsKICAgICAgICAgICAgcmV0dXJuIF90aGlzMi5nZXREaWN0cygicHJvamVjdF9iY19sb2ciKTsKICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgbG9nUmVzID0gX2NvbnRleHQ0LnNlbnQ7CiAgICAgICAgICAgIF90aGlzMi5sb2dPcHRpb25zID0gbG9nUmVzLmRhdGE7CiAgICAgICAgICAgIF9jb250ZXh0NC5uZXh0ID0gNzsKICAgICAgICAgICAgcmV0dXJuIF90aGlzMi5nZXREaWN0cygicHJvamVjdF9iY190eXBlIik7CiAgICAgICAgICBjYXNlIDc6CiAgICAgICAgICAgIGJjVHlwZVJlcyA9IF9jb250ZXh0NC5zZW50OwogICAgICAgICAgICBfdGhpczIuYmNUeXBlT3B0aW9ucyA9IGJjVHlwZVJlcy5kYXRhOwogICAgICAgICAgICBfdGhpczIuZ2V0RGljdHMoImJjLXpycSIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgX3RoaXMyLnpycU9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgX3RoaXMyLmdldERpY3RzKCJCWkNMIikudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgICBfdGhpczIubWF0ZXJpYWxUeXBlT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgICBfdGhpczIuZ2V0RGljdHMoImJjLWZwcSIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgX3RoaXMyLmFsbG9jYXRvck9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgX2NvbnRleHQ0Lm5leHQgPSAxNDsKICAgICAgICAgICAgcmV0dXJuICgwLCBfc3VwcGxpZXIuc3VwcGxpZXJBbGwpKHsKICAgICAgICAgICAgICBzdXBwbGllclR5cGU6ICcxJywKICAgICAgICAgICAgICByZXFUeXBlOiAxCiAgICAgICAgICAgIH0pOwogICAgICAgICAgY2FzZSAxNDoKICAgICAgICAgICAgc3VwcGxpZXJMaXN0ID0gX2NvbnRleHQ0LnNlbnQ7CiAgICAgICAgICAgIF90aGlzMi5zdXBwbGllckxpc3QgPSBzdXBwbGllckxpc3Q7CiAgICAgICAgICBjYXNlIDE2OgogICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0NC5zdG9wKCk7CiAgICAgICAgfQogICAgICB9LCBfY2FsbGVlMik7CiAgICB9KSkoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QoKSB7CiAgICAgIHZhciBtYXRlcmlhbEFycmF5ID0gdGhpcy5tYXRlcmlhbExpc3QuZmlsdGVyKGZ1bmN0aW9uIChpKSB7CiAgICAgICAgcmV0dXJuIGkubWIwMDUgIT09ICcxMDMnOwogICAgICB9KTsKICAgICAgdmFyIHBhcmFtcyA9IE9iamVjdC5hc3NpZ24oe30sIHRoaXMucXVlcnlQYXJhbXMpOwogICAgICBpZiAocGFyYW1zLm5hbWUpIHsKICAgICAgICBtYXRlcmlhbEFycmF5ID0gbWF0ZXJpYWxBcnJheS5maWx0ZXIoZnVuY3Rpb24gKGkpIHsKICAgICAgICAgIHJldHVybiBpLm5hbWUgPT0gcGFyYW1zLm5hbWU7CiAgICAgICAgfSk7CiAgICAgIH0KICAgICAgdGhpcy5tYXRlcmlhbEFycmF5ID0gbWF0ZXJpYWxBcnJheS5zbGljZSgodGhpcy5jdXJyZW50UGFnZSAtIDEpICogdGhpcy5wYWdlU2l6ZSwgdGhpcy5jdXJyZW50UGFnZSAqIHRoaXMucGFnZVNpemUpOwogICAgfSwKICAgIGhhbmRsZVF1ZXJ5OiBmdW5jdGlvbiBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIHJlc2V0UXVlcnk6IGZ1bmN0aW9uIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSB2YWw7CiAgICB9LAogICAgb3BlbkRvYzogZnVuY3Rpb24gb3BlbkRvYygpIHsKICAgICAgd2luZG93Lm9wZW4oJ2h0dHBzOi8vdmlldy5vZmZpY2VhcHBzLmxpdmUuY29tL29wL3ZpZXcuYXNweD9zcmM9aHR0cHM6Ly9lbm93Lm9zcy1jbi1iZWlqaW5nLmFsaXl1bmNzLmNvbS9pbWFnZXMvMjAyNDA5MDkvMTcyNTg0NzgzMjUzNy5wcHR4Jyk7CiAgICB9LAogICAgY29uZmlybVRyZWU6IGZ1bmN0aW9uIGNvbmZpcm1UcmVlKCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTMoKSB7CiAgICAgICAgdmFyIHBhcmFtcywgcmVzOwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUzJChfY29udGV4dDUpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0NS5wcmV2ID0gX2NvbnRleHQ1Lm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIHBhcmFtcyA9IHsKICAgICAgICAgICAgICAgIGlkOiBfdGhpczMuY3VycmVudFJvdy5pZCwKICAgICAgICAgICAgICAgIG1iMDA1OiBfdGhpczMuY3VycmVudFJvdy5tYjAwNSwKICAgICAgICAgICAgICAgIHpycVR5cGU6IF90aGlzMy5jdXJyZW50Um93LnpycVR5cGUsCiAgICAgICAgICAgICAgICBhbGxvY2F0b3I6IF90aGlzMy5jdXJyZW50Um93LmFsbG9jYXRvci5qb2luKCcsJyksCiAgICAgICAgICAgICAgICB0eXBlOiBfdGhpczMuY3VycmVudFJvdy50eXBlCiAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICBfY29udGV4dDUubmV4dCA9IDM7CiAgICAgICAgICAgICAgcmV0dXJuICgwLCBfYmMudXBkYXRlQmNGb3JjZSkocGFyYW1zKTsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIHJlcyA9IF9jb250ZXh0NS5zZW50OwogICAgICAgICAgICAgIF90aGlzMy50cmVlT3BlbiA9IGZhbHNlOwogICAgICAgICAgICBjYXNlIDU6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0NS5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTMpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBtYjAwNUNoYW5nZTogZnVuY3Rpb24gbWIwMDVDaGFuZ2UoKSB7CiAgICAgIHRoaXMuY3VycmVudFJvdy50eXBlID0gbnVsbDsKICAgICAgdGhpcy5jdXJyZW50Um93LnpycVR5cGUgPSBudWxsOwogICAgICB0aGlzLmN1cnJlbnRSb3cuYWxsb2NhdG9yID0gW107CiAgICB9LAogICAgdHlwZUNoYW5nZTogZnVuY3Rpb24gdHlwZUNoYW5nZSgpIHsKICAgICAgdGhpcy5jdXJyZW50Um93Lm1iMDA1ID0gJzEwNCc7CiAgICAgIHRoaXMuY3VycmVudFJvdy56cnFUeXBlID0gbnVsbDsKICAgICAgdGhpcy5jdXJyZW50Um93LmFsbG9jYXRvciA9IFtdOwogICAgfSwKICAgIHpycVR5cGVDaGFuZ2U6IGZ1bmN0aW9uIHpycVR5cGVDaGFuZ2UoKSB7CiAgICAgIHRoaXMuY3VycmVudFJvdy5tYjAwNSA9ICcxMDQnOwogICAgICB0aGlzLmN1cnJlbnRSb3cudHlwZSA9ICcwJzsKICAgIH0sCiAgICBhbGxvY2F0b3JDaGFuZ2U6IGZ1bmN0aW9uIGFsbG9jYXRvckNoYW5nZSgpIHsKICAgICAgdGhpcy5jdXJyZW50Um93Lm1iMDA1ID0gJzEwNCc7CiAgICAgIHRoaXMuY3VycmVudFJvdy50eXBlID0gJzAnOwogICAgfSwKICAgIGNvbmZpcm1NYXRlcmlhbDogZnVuY3Rpb24gY29uZmlybU1hdGVyaWFsKCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTQoKSB7CiAgICAgICAgdmFyIGZvcm07CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTQkKF9jb250ZXh0NikgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ2LnByZXYgPSBfY29udGV4dDYubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQ2Lm5leHQgPSAyOwogICAgICAgICAgICAgIHJldHVybiBfdGhpczQuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSgpOwogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgZm9ybSA9IF90aGlzNC5jdXJyZW50Um93OwogICAgICAgICAgICAgIGlmICghKGZvcm0ubWIwMDUgPT09ICcxMDQnKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQ2Lm5leHQgPSAxMTsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBpZiAoZm9ybS50eXBlKSB7CiAgICAgICAgICAgICAgICBfY29udGV4dDYubmV4dCA9IDc7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX3RoaXM0Lm1zZ0Vycm9yKCfor7fpgInmi6nljIXmnZDnsbvlnosnKTsKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ2LmFicnVwdCgicmV0dXJuIik7CiAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgICBpZiAoIShmb3JtLnR5cGUgPT09ICcwJykpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0Ni5uZXh0ID0gMTE7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgaWYgKGZvcm0uenJxVHlwZSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQ2Lm5leHQgPSAxMTsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfdGhpczQubXNnRXJyb3IoJ+ivt+mAieaLqeS4u+WMheadkOexu+WeiycpOwogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDYuYWJydXB0KCJyZXR1cm4iKTsKICAgICAgICAgICAgY2FzZSAxMToKICAgICAgICAgICAgICBfdGhpczQudHlwZU9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgY2FzZSAxMjoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ2LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNCk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGFsbG9jYXRvclRleHQ6IGZ1bmN0aW9uIGFsbG9jYXRvclRleHQoYWxsb2NhdG9yKSB7CiAgICAgIHZhciBhcnIgPSB0aGlzLmFsbG9jYXRvck9wdGlvbnMuZmlsdGVyKGZ1bmN0aW9uIChpKSB7CiAgICAgICAgcmV0dXJuIGFsbG9jYXRvci5pbmNsdWRlcyhpLmRpY3RWYWx1ZSk7CiAgICAgIH0pOwogICAgICBpZiAoYXJyICYmIGFyclswXSkgewogICAgICAgIHJldHVybiBhcnIubWFwKGZ1bmN0aW9uIChpKSB7CiAgICAgICAgICByZXR1cm4gaS5kaWN0TGFiZWw7CiAgICAgICAgfSkuam9pbignLCcpOwogICAgICB9CiAgICB9LAogICAgbWIwMDVUZXh0OiBmdW5jdGlvbiBtYjAwNVRleHQobWIwMDUpIHsKICAgICAgdmFyIGFyciA9IHRoaXMubWIwMDVPcHRpb25zLmZpbHRlcihmdW5jdGlvbiAoaSkgewogICAgICAgIHJldHVybiBtYjAwNSA9PT0gaS52YWx1ZTsKICAgICAgfSk7CiAgICAgIGlmIChhcnIgJiYgYXJyWzBdKSB7CiAgICAgICAgcmV0dXJuIGFyclswXS5sYWJlbDsKICAgICAgfQogICAgfSwKICAgIHNob3dUeXBlOiBmdW5jdGlvbiBzaG93VHlwZShyb3cpIHsKICAgICAgdGhpcy5jdXJyZW50Um93ID0gcm93OwogICAgICB0aGlzLnRyZWVPcGVuID0gdHJ1ZTsKICAgIH0sCiAgICBtYXRlcmlhbFRleHQ6IGZ1bmN0aW9uIG1hdGVyaWFsVGV4dChpdGVtKSB7CiAgICAgIHZhciBhcnJheSA9IFtdOwogICAgICBpZiAoaXRlbS5tYjAwNSkgewogICAgICAgIGFycmF5LnB1c2godGhpcy5tYjAwNVRleHQoaXRlbS5tYjAwNSkpOwogICAgICAgIGlmIChpdGVtLm1iMDA1ID09PSAnMTA0JykgewogICAgICAgICAgaWYgKGl0ZW0udHlwZSkgewogICAgICAgICAgICBhcnJheS5wdXNoKHRoaXMuc2VsZWN0RGljdExhYmVsKHRoaXMuYmNUeXBlT3B0aW9ucywgaXRlbS50eXBlKSk7CiAgICAgICAgICB9CiAgICAgICAgICBpZiAoaXRlbS56cnFUeXBlKSB7CiAgICAgICAgICAgIGFycmF5LnB1c2godGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy56cnFPcHRpb25zLCBpdGVtLnpycVR5cGUpKTsKICAgICAgICAgIH0KICAgICAgICAgIGlmIChpdGVtLmFsbG9jYXRvci5sZW5ndGgpIHsKICAgICAgICAgICAgYXJyYXkucHVzaCh0aGlzLmFsbG9jYXRvclRleHQoaXRlbS5hbGxvY2F0b3IpKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgICAgcmV0dXJuIGFycmF5LmpvaW4oJy8nKTsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gIuivt+mAieaLqSI7CiAgICAgIH0KICAgIH0sCiAgICBzaG93Q29sOiBmdW5jdGlvbiBzaG93Q29sKCkgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTUoKSB7CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTUkKF9jb250ZXh0NykgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ3LnByZXYgPSBfY29udGV4dDcubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQ3Lm5leHQgPSAyOwogICAgICAgICAgICAgIHJldHVybiBfdGhpczUuJG5leHRUaWNrKCk7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBfdGhpczUuJHJlZnMuY3VzdG9tQ29scy5jb2x1bW5zT3BlbiA9IHRydWU7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ3LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGNvbFN1Y2Nlc3M6IGZ1bmN0aW9uIGNvbFN1Y2Nlc3MoKSB7fSwKICAgIHZhbHVlVG9MYWJlbDogZnVuY3Rpb24gdmFsdWVUb0xhYmVsKG9wdGlvbnMsIHZhbHVlKSB7CiAgICAgIHZhciBhcnIgPSBvcHRpb25zLmZpbHRlcihmdW5jdGlvbiAoaSkgewogICAgICAgIHJldHVybiBpLnZhbHVlID09PSB2YWx1ZTsKICAgICAgfSk7CiAgICAgIGlmIChhcnIgJiYgYXJyWzBdKSB7CiAgICAgICAgcmV0dXJuIGFyclswXS5sYWJlbDsKICAgICAgfQogICAgfSwKICAgIHByaWNlVGV4dDogZnVuY3Rpb24gcHJpY2VUZXh0KHJvdykgewogICAgICBpZiAocm93LnByaWNlQXJyYXkpIHsKICAgICAgICB2YXIgbyA9IHJvdy5wcmljZUFycmF5WzBdOwogICAgICAgIHZhciBhcnJheSA9IFtdOwogICAgICAgIGlmIChvLnN0YWdlKSB7CiAgICAgICAgICBhcnJheS5wdXNoKHRoaXMudmFsdWVUb0xhYmVsKHRoaXMuc3RhZ2VPcHRpb25zLCBvLnN0YWdlKSk7CiAgICAgICAgfQogICAgICAgIGFycmF5LnB1c2goIigiKTsKICAgICAgICBhcnJheS5wdXNoKG8uY3JlYXRlRGF0ZSk7CiAgICAgICAgYXJyYXkucHVzaCgiKSIpOwogICAgICAgIGlmIChvLmFycmF5ICYmIG8uYXJyYXlbMF0pIHsKICAgICAgICAgIHZhciBzdWIgPSBvLmFycmF5WzBdOwogICAgICAgICAgaWYgKHN1Yi5ncmFkZWRUeXBlKSB7CiAgICAgICAgICAgIGFycmF5LnB1c2godGhpcy52YWx1ZVRvTGFiZWwodGhpcy5ncmFkZWRUeXBlT3B0aW9ucywgc3ViLmdyYWRlZFR5cGUpKTsKICAgICAgICAgIH0KICAgICAgICAgIGlmIChzdWIucHJpY2UpIHsKICAgICAgICAgICAgYXJyYXkucHVzaChzdWIucHJpY2UpOwogICAgICAgICAgfQogICAgICAgICAgaWYgKHN1Yi5tb3EpIHsKICAgICAgICAgICAgYXJyYXkucHVzaCgn6LW36K6i6YePOicgKyBzdWIubW9xKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgICAgcmV0dXJuIGFycmF5LmpvaW4oJycpOwogICAgICB9CiAgICB9LAogICAgdmFsaWRhdGVUYWJsZTogZnVuY3Rpb24gdmFsaWRhdGVUYWJsZSgpIHsKICAgICAgdmFyIF9pdGVyYXRvcjMgPSAoMCwgX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIyLmRlZmF1bHQpKHRoaXMubWF0ZXJpYWxMaXN0KSwKICAgICAgICBfc3RlcDM7CiAgICAgIHRyeSB7CiAgICAgICAgZm9yIChfaXRlcmF0b3IzLnMoKTsgIShfc3RlcDMgPSBfaXRlcmF0b3IzLm4oKSkuZG9uZTspIHsKICAgICAgICAgIHZhciBpdGVtID0gX3N0ZXAzLnZhbHVlOwogICAgICAgICAgaWYgKCFpdGVtLm9wVHlwZSkgewogICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+ivt+mAieaLqeW8gOWPkeexu+WeiyEnKTsKICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgfQogICAgICAgICAgaWYgKCFpdGVtLm5hbWUpIHsKICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCfor7fovpPlhaXlkI3np7AhJyk7CiAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgIH0KICAgICAgICAgIGlmICghaXRlbS5tYjAwNSkgewogICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+ivt+i+k+WFpeeJqeaWmeexu+WeiyEnKTsKICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgfQogICAgICAgICAgaWYgKCFpdGVtLm1iMDA4KSB7CiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcign6K+36L6T5YWl54mp5paZ5bGe5oCnIScpOwogICAgICAgICAgICByZXR1cm47CiAgICAgICAgICB9CiAgICAgICAgICBpZiAoaXRlbS5tYjAwNSA9PT0gJzEwNCcpIHsKICAgICAgICAgICAgaWYgKCFpdGVtLnR5cGUpIHsKICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+ivt+mAieaLqeexu+WIqyEnKTsKICAgICAgICAgICAgICByZXR1cm47CiAgICAgICAgICAgIH0KICAgICAgICAgICAgaWYgKGl0ZW0udHlwZSA9PT0gJzAnICYmICFpdGVtLnpycVR5cGUgJiYgIWl0ZW0uYWxsb2NhdG9yLmxlbmd0aCkgewogICAgICAgICAgICAgIC8v5Li75a655Zmo5oiW5YiG6YWN5Zmo5LqM6YCJ5LiACiAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCfor7fpgInmi6nkuLvlrrnlmajnsbvliKvmiJbliIbphY3lmaghJyk7CiAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICBfaXRlcmF0b3IzLmUoZXJyKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICBfaXRlcmF0b3IzLmYoKTsKICAgICAgfQogICAgfSwKICAgIHNhdmVTdWNjZXNzOiBmdW5jdGlvbiBzYXZlU3VjY2VzcygpIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWU2KCkgewogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWU2JChfY29udGV4dDgpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0OC5wcmV2ID0gX2NvbnRleHQ4Lm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF90aGlzNi4kZW1pdCgic2F2ZVN1Y2Nlc3MiKTsKICAgICAgICAgICAgICBfdGhpczYub3BlbiA9IGZhbHNlOwogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0OC5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTYpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICByZXNldFByaWNlQXJyYXk6IGZ1bmN0aW9uIHJlc2V0UHJpY2VBcnJheSgpIHsKICAgICAgdGhpcy5jb25maWdBcnJheSA9IFsnMSddOwogICAgICB0aGlzLnByaWNlQXJyYXkgPSBbXTsKICAgICAgdGhpcy5maWxlcyA9IFtdOwogICAgfSwKICAgIGhhbmRsZVByaWNlOiBmdW5jdGlvbiBoYW5kbGVQcmljZShyb3cpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWU3KCkgewogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWU3JChfY29udGV4dDkpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0OS5wcmV2ID0gX2NvbnRleHQ5Lm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF90aGlzNy5jdXJyZW50Um93ID0gcm93OwogICAgICAgICAgICAgIF90aGlzNy5yZXNldFByaWNlQXJyYXkoKTsKICAgICAgICAgICAgICBpZiAocm93LnByaWNlQXJyYXkpIHsKICAgICAgICAgICAgICAgIF90aGlzNy5wcmljZUFycmF5ID0gcm93LnByaWNlQXJyYXk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGlmIChyb3cuY29uZmlnQXJyYXkpIHsKICAgICAgICAgICAgICAgIF90aGlzNy5jb25maWdBcnJheSA9IHJvdy5jb25maWdBcnJheTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgaWYgKHJvdy5maWxlcykgewogICAgICAgICAgICAgICAgX3RoaXM3LmZpbGVzID0gcm93LmZpbGVzOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfdGhpczcub3BlbiA9IHRydWU7CiAgICAgICAgICAgIGNhc2UgNjoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ5LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNyk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIHJlc291cmNlVGV4dDogZnVuY3Rpb24gcmVzb3VyY2VUZXh0KHJlc291cmNlVHlwZSkgewogICAgICB2YXIgYXJyID0gdGhpcy5yZXNvdXJjZVR5cGVPcHRpb25zLmZpbHRlcihmdW5jdGlvbiAoaSkgewogICAgICAgIHJldHVybiBpLnZhbHVlID09PSByZXNvdXJjZVR5cGU7CiAgICAgIH0pOwogICAgICBpZiAoYXJyICYmIGFyclswXSkgewogICAgICAgIHJldHVybiBhcnJbMF0ubGFiZWw7CiAgICAgIH0KICAgIH0sCiAgICBmaWxlQ2hhbmdlOiBmdW5jdGlvbiBmaWxlQ2hhbmdlKGZpbGVMaXN0LCBpZCkgewogICAgICB2YXIgX2l0ZXJhdG9yNCA9ICgwLCBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcjIuZGVmYXVsdCkodGhpcy5tYXRlcmlhbExpc3QpLAogICAgICAgIF9zdGVwNDsKICAgICAgdHJ5IHsKICAgICAgICBmb3IgKF9pdGVyYXRvcjQucygpOyAhKF9zdGVwNCA9IF9pdGVyYXRvcjQubigpKS5kb25lOykgewogICAgICAgICAgdmFyIGl0ZW0gPSBfc3RlcDQudmFsdWU7CiAgICAgICAgICBpZiAoaXRlbS5rZXkgPT09IGlkIHx8IGl0ZW0uaWQgPT09IGlkKSB7CiAgICAgICAgICAgIGl0ZW0uZmlsZXMgPSBmaWxlTGlzdDsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgIF9pdGVyYXRvcjQuZShlcnIpOwogICAgICB9IGZpbmFsbHkgewogICAgICAgIF9pdGVyYXRvcjQuZigpOwogICAgICB9CiAgICB9LAogICAgY29uZmlybUJjOiBmdW5jdGlvbiBjb25maXJtQmMoKSB7CiAgICAgIHZhciBfaXRlcmF0b3I1ID0gKDAsIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyMi5kZWZhdWx0KSh0aGlzLmN1cnJlbnRCY0FycmF5KSwKICAgICAgICBfc3RlcDU7CiAgICAgIHRyeSB7CiAgICAgICAgZm9yIChfaXRlcmF0b3I1LnMoKTsgIShfc3RlcDUgPSBfaXRlcmF0b3I1Lm4oKSkuZG9uZTspIHsKICAgICAgICAgIHZhciBpdGVtID0gX3N0ZXA1LnZhbHVlOwogICAgICAgICAgdmFyIG8gPSB7CiAgICAgICAgICAgIHJlc291cmNlVHlwZTogJ2JjJywKICAgICAgICAgICAgb3BUeXBlOiBudWxsLAogICAgICAgICAgICBjb2RlOiBudWxsLAogICAgICAgICAgICBiY0NvZGU6IGl0ZW0ubWF0ZXJpYWxDb2RlLAogICAgICAgICAgICBlcnBDb2RlOiBpdGVtLmVycENvZGUsCiAgICAgICAgICAgIG5hbWU6IGl0ZW0ubWF0ZXJpYWxOYW1lLAogICAgICAgICAgICBzcGVjOiBpdGVtLmNhcGFjaXR5LAogICAgICAgICAgICB0eXBlOiBudWxsLAogICAgICAgICAgICB6cnFUeXBlOiBudWxsLAogICAgICAgICAgICBhbGxvY2F0b3I6IFtdLAogICAgICAgICAgICBtYXRlcmlhbFR5cGU6IG51bGwsCiAgICAgICAgICAgIGxlbmd0aDogaXRlbS5sZW5ndGgsCiAgICAgICAgICAgIHdpZHRoOiBpdGVtLndpZHRoLAogICAgICAgICAgICBoZWlnaHQ6IGl0ZW0uaGVpZ2h0LAogICAgICAgICAgICBtb2RlbDogbnVsbCwKICAgICAgICAgICAgdmVuZG9yOiBudWxsLAogICAgICAgICAgICBlY2d5OiBudWxsLAogICAgICAgICAgICByZW1hcms6IG51bGwsCiAgICAgICAgICAgIGltZ3M6IG51bGwsCiAgICAgICAgICAgIGZpbGVzOiBbXSwKICAgICAgICAgICAgcHJpY2VBcnJheTogW10sCiAgICAgICAgICAgIGNvbmZpZ0FycmF5OiBbXSwKICAgICAgICAgICAgc3VwcGxpZXJJZDogbnVsbCwKICAgICAgICAgICAgZGV2U3RhdHVzOiB0aGlzLmRldlN0YXR1cwogICAgICAgICAgfTsKICAgICAgICAgIHRoaXMubWF0ZXJpYWxMaXN0LnB1c2gobyk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICBfaXRlcmF0b3I1LmUoZXJyKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICBfaXRlcmF0b3I1LmYoKTsKICAgICAgfQogICAgICB0aGlzLmJjT3BlbiA9IGZhbHNlOwogICAgfSwKICAgIGJjQ2hhbmdlOiBmdW5jdGlvbiBiY0NoYW5nZShiY0FycmF5KSB7CiAgICAgIHRoaXMuY3VycmVudEJjQXJyYXkgPSBiY0FycmF5OwogICAgfSwKICAgIHNlbGVjdEJjOiBmdW5jdGlvbiBzZWxlY3RCYygpIHsKICAgICAgdGhpcy5jdXJyZW50QmNBcnJheSA9IFtdOwogICAgICB0aGlzLmJjT3BlbiA9IHRydWU7CiAgICB9LAogICAgY29uZmlybUJvbTogZnVuY3Rpb24gY29uZmlybUJvbSgpIHsKICAgICAgdmFyIF90aGlzOCA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWU4KCkgewogICAgICAgIHZhciBpZHMsIGFyciwgX2l0ZXJhdG9yNiwgX3N0ZXA2LCBpdGVtLCBvOwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWU4JChfY29udGV4dDEwKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDEwLnByZXYgPSBfY29udGV4dDEwLm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0MTAubmV4dCA9IDI7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzOC4kbmV4dFRpY2soKTsKICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgIGlkcyA9IF90aGlzOC4kcmVmcy5ib21UcmVlLmdldENoZWNrZWROb2RlcygpLm1hcChmdW5jdGlvbiAoaSkgewogICAgICAgICAgICAgICAgcmV0dXJuIGkuaWQ7CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgYXJyID0gX3RoaXM4LmJvbURhdGEuZmlsdGVyKGZ1bmN0aW9uIChpKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gaWRzLmluY2x1ZGVzKGkuaWQpOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIGlmIChhcnIgJiYgYXJyWzBdKSB7CiAgICAgICAgICAgICAgICBfaXRlcmF0b3I2ID0gKDAsIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyMi5kZWZhdWx0KShhcnIpOwogICAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgICAgZm9yIChfaXRlcmF0b3I2LnMoKTsgIShfc3RlcDYgPSBfaXRlcmF0b3I2Lm4oKSkuZG9uZTspIHsKICAgICAgICAgICAgICAgICAgICBpdGVtID0gX3N0ZXA2LnZhbHVlOwogICAgICAgICAgICAgICAgICAgIG8gPSB7CiAgICAgICAgICAgICAgICAgICAgICByZXNvdXJjZVR5cGU6ICdib20nLAogICAgICAgICAgICAgICAgICAgICAgb3BUeXBlOiBudWxsLAogICAgICAgICAgICAgICAgICAgICAgY29kZTogbnVsbCwKICAgICAgICAgICAgICAgICAgICAgIGJjQ29kZTogbnVsbCwKICAgICAgICAgICAgICAgICAgICAgIGVycENvZGU6IGl0ZW0ubWQwMDMsCiAgICAgICAgICAgICAgICAgICAgICBuYW1lOiBpdGVtLm1iMDAyLAogICAgICAgICAgICAgICAgICAgICAgbWIwMDU6IGl0ZW0ubWIwMDUsCiAgICAgICAgICAgICAgICAgICAgICBtYjAwODogaXRlbS5tYjAwOCwKICAgICAgICAgICAgICAgICAgICAgIHNwZWM6IG51bGwsCiAgICAgICAgICAgICAgICAgICAgICB0eXBlOiBudWxsLAogICAgICAgICAgICAgICAgICAgICAgenJxVHlwZTogbnVsbCwKICAgICAgICAgICAgICAgICAgICAgIGFsbG9jYXRvcjogW10sCiAgICAgICAgICAgICAgICAgICAgICBtYXRlcmlhbFR5cGU6IG51bGwsCiAgICAgICAgICAgICAgICAgICAgICBsZW5ndGg6IDAsCiAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMCwKICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogMCwKICAgICAgICAgICAgICAgICAgICAgIG1vZGVsOiBudWxsLAogICAgICAgICAgICAgICAgICAgICAgdmVuZG9yOiBudWxsLAogICAgICAgICAgICAgICAgICAgICAgZWNneTogbnVsbCwKICAgICAgICAgICAgICAgICAgICAgIHJlbWFyazogbnVsbCwKICAgICAgICAgICAgICAgICAgICAgIGltZ3M6IG51bGwsCiAgICAgICAgICAgICAgICAgICAgICBmaWxlczogW10sCiAgICAgICAgICAgICAgICAgICAgICBwcmljZUFycmF5OiBbXSwKICAgICAgICAgICAgICAgICAgICAgIGNvbmZpZ0FycmF5OiBbXSwKICAgICAgICAgICAgICAgICAgICAgIHN1cHBsaWVySWQ6IG51bGwsCiAgICAgICAgICAgICAgICAgICAgICBkZXZTdGF0dXM6IF90aGlzOC5kZXZTdGF0dXMKICAgICAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgICAgIF90aGlzOC5tYXRlcmlhbExpc3QucHVzaChvKTsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgICAgICAgICAgIF9pdGVyYXRvcjYuZShlcnIpOwogICAgICAgICAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgICAgICAgICAgX2l0ZXJhdG9yNi5mKCk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzOC5ib21PcGVuID0gZmFsc2U7CiAgICAgICAgICAgIGNhc2UgNjoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQxMC5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTgpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICB0b0JvbVRyZWU6IGZ1bmN0aW9uIHRvQm9tVHJlZShsaXN0LCBtZDAwMSkgewogICAgICB2YXIgX3RoaXM5ID0gdGhpczsKICAgICAgcmV0dXJuIGxpc3QuZmlsdGVyKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgaWYgKG1kMDAxKSB7CiAgICAgICAgICBpZiAoaXRlbS5tZDAwMSA9PT0gbWQwMDEpIHsKICAgICAgICAgICAgdmFyIGNoaWxkcmVuID0gX3RoaXM5LnRvQm9tVHJlZShsaXN0LCBpdGVtLm1kMDAzKTsKICAgICAgICAgICAgaWYgKGNoaWxkcmVuICYmIGNoaWxkcmVuLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgICBpdGVtLmNoaWxkcmVuID0gY2hpbGRyZW47CiAgICAgICAgICAgIH0KICAgICAgICAgICAgcmV0dXJuIHRydWU7CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGlmIChbdW5kZWZpbmVkLCBudWxsLCAnJ10uaW5jbHVkZXMoaXRlbS5tZDAwMSkpIHsKICAgICAgICAgICAgdmFyIF9jaGlsZHJlbiA9IF90aGlzOS50b0JvbVRyZWUobGlzdCwgaXRlbS5tZDAwMyk7CiAgICAgICAgICAgIGlmIChfY2hpbGRyZW4gJiYgX2NoaWxkcmVuLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgICBpdGVtLmNoaWxkcmVuID0gX2NoaWxkcmVuOwogICAgICAgICAgICB9CiAgICAgICAgICAgIHJldHVybiB0cnVlOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIGdldEVycEluZm86IGZ1bmN0aW9uIGdldEVycEluZm8oKSB7CiAgICAgIHZhciBfdGhpczEwID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTkoKSB7CiAgICAgICAgdmFyIGVycE5vLCBlcnBSZXMsIGRhdGEsIGZvcm0sIG1iMDAyLCBib21MaXN0LCBib21UcmVlOwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWU5JChfY29udGV4dDExKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDExLnByZXYgPSBfY29udGV4dDExLm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIGVycE5vID0gX3RoaXMxMC5lcnBDb2RlOwogICAgICAgICAgICAgIGlmICghZXJwTm8pIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0MTEubmV4dCA9IDMzOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzMTAuYnRuTG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgICAgX3RoaXMxMC5sb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgICAgICBfY29udGV4dDExLm5leHQgPSA2OwogICAgICAgICAgICAgIHJldHVybiAoMCwgX2VycC5nZXRQcm9kdWN0QnlNZDAwMykoZXJwTm8pOwogICAgICAgICAgICBjYXNlIDY6CiAgICAgICAgICAgICAgZXJwUmVzID0gX2NvbnRleHQxMS5zZW50OwogICAgICAgICAgICAgIGlmICghKGVycFJlcyAmJiBlcnBSZXMuZGF0YSkpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0MTEubmV4dCA9IDMwOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGRhdGEgPSBlcnBSZXMuZGF0YTsKICAgICAgICAgICAgICBmb3JtID0gX3RoaXMxMC5mb3JtOwogICAgICAgICAgICAgIG1iMDAyID0gZGF0YS5NQjAwMjsKICAgICAgICAgICAgICBpZiAoIW1iMDAyKSB7CiAgICAgICAgICAgICAgICBfY29udGV4dDExLm5leHQgPSAyNTsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBmb3JtLm1hMDAzID0gZGF0YS5NQTAwMzsKICAgICAgICAgICAgICBmb3JtLm1iMDAyID0gbWIwMDI7CiAgICAgICAgICAgICAgZm9ybS5tYjAwNSA9IGRhdGEuTUIwMDU7CiAgICAgICAgICAgICAgX2NvbnRleHQxMS5uZXh0ID0gMTc7CiAgICAgICAgICAgICAgcmV0dXJuICgwLCBfc2NoZWR1bGVQbGFuLmVycEJvbSkoewogICAgICAgICAgICAgICAgZXJwTm86IGVycE5vCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGNhc2UgMTc6CiAgICAgICAgICAgICAgYm9tTGlzdCA9IF9jb250ZXh0MTEuc2VudDsKICAgICAgICAgICAgICBib21MaXN0ID0gYm9tTGlzdC5maWx0ZXIoZnVuY3Rpb24gKGkpIHsKICAgICAgICAgICAgICAgIHJldHVybiBpLm1iMDA1ICE9PSAnMTA1JzsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICBib21MaXN0LnB1c2goewogICAgICAgICAgICAgICAgaWQ6IGVycE5vLAogICAgICAgICAgICAgICAgbWIwMDI6IG1iMDAyLAogICAgICAgICAgICAgICAgbWQwMDM6IGVycE5vLAogICAgICAgICAgICAgICAgbWIwMDU6IGRhdGEuTUIwMDUsCiAgICAgICAgICAgICAgICBtYjAwODogZGF0YS5tYjAwOAogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIGJvbVRyZWUgPSBfdGhpczEwLnRvQm9tVHJlZShKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KGJvbUxpc3QpKSwgdW5kZWZpbmVkKTsKICAgICAgICAgICAgICBfdGhpczEwLmJvbVRyZWUgPSBib21UcmVlOwogICAgICAgICAgICAgIF90aGlzMTAuYm9tRGF0YSA9IGJvbUxpc3Q7CiAgICAgICAgICAgICAgX2NvbnRleHQxMS5uZXh0ID0gMjg7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMjU6CiAgICAgICAgICAgICAgX3RoaXMxMC5tc2dFcnJvcignZXJw5Luj56CB6L6T5YWl5pyJ6K+vJyk7CiAgICAgICAgICAgICAgZm9ybS5tYTAwMyA9IG51bGw7CiAgICAgICAgICAgICAgZm9ybS5tYjAwMiA9IG51bGw7CiAgICAgICAgICAgIGNhc2UgMjg6CiAgICAgICAgICAgICAgX2NvbnRleHQxMS5uZXh0ID0gMzE7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMzA6CiAgICAgICAgICAgICAgX3RoaXMxMC5tc2dFcnJvcignZXJw5Luj56CB6L6T5YWl5pyJ6K+vJyk7CiAgICAgICAgICAgIGNhc2UgMzE6CiAgICAgICAgICAgICAgX3RoaXMxMC5idG5Mb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXMxMC5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgIGNhc2UgMzM6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0MTEuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU5KTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgc2hvd0JvbTogZnVuY3Rpb24gc2hvd0JvbSgpIHsKICAgICAgdmFyIF90aGlzMTEgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlMTAoKSB7CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTEwJChfY29udGV4dDEyKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDEyLnByZXYgPSBfY29udGV4dDEyLm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF90aGlzMTEuYm9tT3BlbiA9IHRydWU7CiAgICAgICAgICAgIGNhc2UgMToKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQxMi5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTEwKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgYWRkSXRlbTogZnVuY3Rpb24gYWRkSXRlbSgpIHsKICAgICAgdmFyIG8gPSB7CiAgICAgICAgcmlkOiB0aGlzLiRuYW5vaWQoKSwKICAgICAgICByZXNvdXJjZVR5cGU6ICdjdXN0b21lcicsCiAgICAgICAgb3BUeXBlOiBudWxsLAogICAgICAgIHR5cGU6IG51bGwsCiAgICAgICAgY29kZTogbnVsbCwKICAgICAgICBiY0NvZGU6IG51bGwsCiAgICAgICAgZXJwQ29kZTogbnVsbCwKICAgICAgICBuYW1lOiBudWxsLAogICAgICAgIHNwZWM6IG51bGwsCiAgICAgICAgenJxVHlwZTogbnVsbCwKICAgICAgICBhbGxvY2F0b3I6IFtdLAogICAgICAgIG1hdGVyaWFsVHlwZTogbnVsbCwKICAgICAgICBsZW5ndGg6IDAsCiAgICAgICAgd2lkdGg6IDAsCiAgICAgICAgaGVpZ2h0OiAwLAogICAgICAgIG1vZGVsOiBudWxsLAogICAgICAgIHZlbmRvcjogbnVsbCwKICAgICAgICBlY2d5OiBudWxsLAogICAgICAgIHJlbWFyazogbnVsbCwKICAgICAgICBpbWdzOiBudWxsLAogICAgICAgIGZpbGVzOiBbXSwKICAgICAgICBwcmljZUFycmF5OiBbXSwKICAgICAgICBjb25maWdBcnJheTogW10sCiAgICAgICAgc3VwcGxpZXJJZDogbnVsbCwKICAgICAgICBkZXZTdGF0dXM6IHRoaXMuZGV2U3RhdHVzCiAgICAgIH07CiAgICAgIHRoaXMubWF0ZXJpYWxMaXN0LnB1c2gobyk7CiAgICB9LAogICAgZGVsSXRlbTogZnVuY3Rpb24gZGVsSXRlbShyb3cpIHsKICAgICAgdmFyIF90aGlzMTIgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlMTEoKSB7CiAgICAgICAgdmFyIGluZGV4OwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUxMSQoX2NvbnRleHQxMykgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQxMy5wcmV2ID0gX2NvbnRleHQxMy5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBpZiAocm93LmlkKSB7CiAgICAgICAgICAgICAgICBpbmRleCA9IF90aGlzMTIubWF0ZXJpYWxMaXN0LmZpbmRJbmRleChmdW5jdGlvbiAoaSkgewogICAgICAgICAgICAgICAgICByZXR1cm4gaS5pZCA9PT0gcm93LmlkOwogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgfSBlbHNlIGlmIChyb3cucmlkKSB7CiAgICAgICAgICAgICAgICBpbmRleCA9IF90aGlzMTIubWF0ZXJpYWxMaXN0LmZpbmRJbmRleChmdW5jdGlvbiAoaSkgewogICAgICAgICAgICAgICAgICByZXR1cm4gaS5yaWQgPT09IHJvdy5yaWQ7CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX3RoaXMxMi5tYXRlcmlhbExpc3Quc3BsaWNlKGluZGV4LCAxKTsKICAgICAgICAgICAgICBpZiAoIXJvdy5pZCkgewogICAgICAgICAgICAgICAgX2NvbnRleHQxMy5uZXh0ID0gNTsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dDEzLm5leHQgPSA1OwogICAgICAgICAgICAgIHJldHVybiAoMCwgX2JjLmRlbEJjKShyb3cuaWQpOwogICAgICAgICAgICBjYXNlIDU6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0MTMuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUxMSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIHRkQ2xhc3M6IGZ1bmN0aW9uIHRkQ2xhc3MocHJvamVjdEJjSWRzLCBwcm9qZWN0QmNJZCkgewogICAgICByZXR1cm4gcHJvamVjdEJjSWRzLmluY2x1ZGVzKHByb2plY3RCY0lkKSA/ICdlbC1pY29uLWNoZWNrJyA6ICcnOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_erp", "require", "_selectTable", "_interopRequireDefault", "_supplier", "_bcLog", "_bcPriceTable", "_customCols", "_treeData", "_schedulePlan", "_bc", "name", "components", "CustomCols", "BcPriceTable", "MaterialGoodsSelectTable", "props", "form", "type", "Object", "required", "materialList", "Array", "devStatus", "String", "default", "readonly", "Boolean", "watch", "handler", "v", "_this", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "bcLogList", "logArray", "_iterator", "_step", "_loop", "wrap", "_callee$", "_context3", "prev", "next", "reset<PERSON><PERSON>y", "allBcLog", "projectId", "status", "sent", "_createForOfIteratorHelper2", "logOptions", "item", "bcLog", "array", "keys", "_iterator2", "_step2", "bc", "_loop2", "_i", "_keys", "_loop$", "_context2", "value", "filter", "i", "dict<PERSON><PERSON>ue", "length", "s", "n", "done", "includes", "projectItemOrderCode", "push", "err", "e", "f", "k", "projectBcIds", "_loop2$", "_context", "map", "projectBcId", "stop", "<PERSON><PERSON><PERSON>", "label", "dict<PERSON><PERSON>l", "t1", "finish", "immediate", "data", "queryParams", "currentPage", "pageSize", "loading", "btnLoading", "bom<PERSON>pen", "bcOpen", "open", "fullscreenFlag", "bcTypeOptions", "bomData", "bomTree", "currentBcArray", "typeOptions", "resourceTypeOptions", "attrOptions", "zrqOptions", "materialTypeOptions", "supplierList", "title", "columns", "visible", "priceArray", "config<PERSON><PERSON><PERSON>", "files", "currentRow", "gradedTypeOptions", "stageOptions", "allocatorOptions", "erpCode", "typeOpen", "treeOpen", "mb005Options", "rules", "mb005", "msg", "materialArray", "created", "_this2", "_callee2", "logRes", "bcTypeRes", "_callee2$", "_context4", "getDicts", "then", "response", "supplierAll", "supplierType", "reqType", "methods", "getList", "params", "assign", "slice", "handleQuery", "resetForm", "handleCurrentChange", "val", "openDoc", "window", "confirmTree", "_this3", "_callee3", "res", "_callee3$", "_context5", "id", "zrqType", "allocator", "join", "updateBcForce", "mb005Change", "typeChange", "zrqTypeChange", "allocator<PERSON><PERSON>e", "confirmMaterial", "_this4", "_callee4", "_callee4$", "_context6", "$refs", "validate", "msgError", "abrupt", "allocatorText", "arr", "mb005Text", "showType", "row", "materialText", "selectDictLabel", "showCol", "_this5", "_callee5", "_callee5$", "_context7", "$nextTick", "customCols", "columnsOpen", "colSuccess", "valueToLabel", "options", "priceText", "o", "stage", "createDate", "sub", "gradedType", "price", "moq", "validateTable", "_iterator3", "_step3", "opType", "Error", "mb008", "saveSuccess", "_this6", "_callee6", "_callee6$", "_context8", "$emit", "resetPriceArray", "handlePrice", "_this7", "_callee7", "_callee7$", "_context9", "resourceText", "resourceType", "fileChange", "fileList", "_iterator4", "_step4", "key", "confirmBc", "_iterator5", "_step5", "code", "bcCode", "materialCode", "materialName", "spec", "capacity", "materialType", "width", "height", "model", "vendor", "ecgy", "remark", "imgs", "supplierId", "bcChange", "bcArray", "selectBc", "confirmBom", "_this8", "_callee8", "ids", "_iterator6", "_step6", "_callee8$", "_context10", "getCheckedNodes", "md003", "mb002", "toBomTree", "list", "md001", "_this9", "children", "undefined", "getErpInfo", "_this10", "_callee9", "erpNo", "erpRes", "bomList", "_callee9$", "_context11", "getProductByMd003", "MB002", "ma003", "MA003", "MB005", "erpBom", "JSON", "parse", "stringify", "showBom", "_this11", "_callee10", "_callee10$", "_context12", "addItem", "rid", "$nanoid", "delItem", "_this12", "_callee11", "index", "_callee11$", "_context13", "findIndex", "splice", "delBc", "tdClass"], "sources": ["src/views/project/project/materialTable.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-row :gutter=\"20\">\r\n      <el-tooltip class=\"item\" content=\"自定义列\" effect=\"dark\">\r\n        <el-button\r\n          circle\r\n          icon=\"el-icon-menu\"\r\n          size=\"mini\"\r\n          @click=\"showCol\"/>\r\n      </el-tooltip>\r\n    </el-row>\r\n\r\n<!--    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"mini\" label-width=\"120px\">-->\r\n<!--      <el-row>-->\r\n<!--        <el-col :span=\"8\">-->\r\n<!--          <el-form-item label=\"品名\" prop=\"name\">-->\r\n<!--            <el-input-->\r\n<!--              v-model=\"queryParams.name\"-->\r\n<!--              clearable-->\r\n<!--            />-->\r\n<!--          </el-form-item>-->\r\n<!--        </el-col>-->\r\n<!--        <el-col :span=\"8\">-->\r\n<!--          <el-form-item>-->\r\n<!--            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>-->\r\n<!--            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>-->\r\n<!--          </el-form-item>-->\r\n<!--        </el-col>-->\r\n<!--      </el-row>-->\r\n<!--    </el-form>-->\r\n\r\n    <div class=\"table-wrapper small-table\">\r\n      <table :class=\"readonly?'mask':''\" class=\"base-table bc-table\">\r\n        <thead>\r\n        <tr>\r\n          <th v-if=\"!readonly\" :rowspan=\"2\" class=\"nth0\" style=\"width: 80px\" >\r\n            <el-tooltip content=\"普通添加\" >\r\n              <i class=\"el-icon-circle-plus-outline\" @click=\"addItem\" />\r\n            </el-tooltip>\r\n<!--            <el-tooltip content=\"通过erp编码添加(单个物料)\" >-->\r\n<!--              <i class=\"el-icon-circle-plus-outline\" @click=\"showErp\" />-->\r\n<!--            </el-tooltip>-->\r\n            <el-tooltip content=\"通过bom结构添加\" >\r\n              <i class=\"el-icon-circle-plus-outline\" @click=\"showBom\" />\r\n            </el-tooltip>\r\n            <el-tooltip content=\"选择包材库\" >\r\n              <i class=\"el-icon-circle-plus-outline\" @click=\"selectBc\" />\r\n            </el-tooltip>\r\n          </th>\r\n          <th v-if=\"columnsFlag('项目包材编码')\" :rowspan=\"2\" style=\"width: 120px\" >\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            项目包材编码\r\n          </th>\r\n          <th v-if=\"columnsFlag('开发类型')\" :rowspan=\"2\" style=\"width: 120px\" >\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            开发类型\r\n          </th>\r\n          <th v-if=\"columnsFlag('名称')\" :rowspan=\"2\" style=\"width: 500px\" >\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            名称\r\n          </th>\r\n          <th v-if=\"columnsFlag('类别')\" :rowspan=\"2\" style=\"width: 150px\" >\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            类别\r\n          </th>\r\n          <th v-if=\"columnsFlag('物料属性')\" :rowspan=\"2\" style=\"width: 150px\" >\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            物料属性\r\n          </th>\r\n          <th v-if=\"columnsFlag('包装材料')\" :rowspan=\"2\" style=\"width: 150px\" >包装材料</th>\r\n<!--          <th v-if=\"columnsFlag('包材库编码')\" :rowspan=\"2\" style=\"width: 150px\" >包材库编码</th>-->\r\n          <th v-if=\"columnsFlag('包材ERP编码')\" :rowspan=\"2\" style=\"width: 150px\" >包材ERP编码</th>\r\n          <th v-if=\"columnsFlag('规格')\" :rowspan=\"2\" style=\"width: 150px\" >规格</th>\r\n          <th v-if=\"columnsFlag('尺寸')\" :rowspan=\"2\" style=\"width: 250px\" >尺寸</th>\r\n          <th v-if=\"columnsFlag('型号')\" :rowspan=\"2\" style=\"width: 150px\" >型号</th>\r\n          <th v-if=\"columnsFlag('供应商')\" :rowspan=\"2\" style=\"width: 150px\" >供应商</th>\r\n          <th v-if=\"columnsFlag('图片')\" :rowspan=\"2\" style=\"width: 300px\" >图片</th>\r\n          <th v-if=\"columnsFlag('COA/SPEC')\" :rowspan=\"2\" style=\"width: 300px\" >COA/SPEC</th>\r\n          <th v-if=\"columnsFlag('备注') && devStatus === '0'\" :rowspan=\"2\" style=\"width: 300px\" >备注</th>\r\n          <th v-if=\"devStatus === '1'\" :rowspan=\"2\" style=\"width: 120px\" >报价日期</th>\r\n          <th v-if=\"devStatus === '1'\" :rowspan=\"2\" style=\"width: 120px\" >阶段</th>\r\n          <th v-if=\"devStatus === '1'\" :rowspan=\"2\" class=\"nth0\" style=\"width: 120px\" >价格</th>\r\n          <th v-for=\"log in logArray\" :key=\"log.value\" :colspan=\"log.array.length\" :style=\"{width: log.array.length * 80 + 'px'}\">{{log.label}}</th>\r\n        </tr>\r\n        <tr>\r\n          <template v-for=\"log in logArray\" >\r\n            <th v-for=\"(l,index) in log.array\" :key=\"log.value  + '_' + index \" style=\"width: 80px\" >{{l.projectItemOrderCode.substring(l.projectItemOrderCode.indexOf(\"-\")+1)}}</th>\r\n          </template>\r\n        </tr>\r\n        </thead>\r\n        <tbody>\r\n        <tr v-for=\"(item,index) in materialList.slice((currentPage-1) * pageSize, currentPage * pageSize)\" :key=\"index\">\r\n          <td v-if=\"!readonly\" class=\"nth0\" >\r\n            <i class=\"el-icon-remove-outline\" @click=\"delItem(item)\" ></i>\r\n          </td>\r\n          <td v-if=\"columnsFlag('项目包材编码')\" >{{item.code}}</td>\r\n          <td v-if=\"columnsFlag('开发类型')\" >\r\n            <el-select v-model=\"item.opType\" size=\"mini\" >\r\n              <el-option\r\n                v-for=\"item in typeOptions\"\r\n                :key=\"item.value\"\r\n                :disabled=\"item.value==2\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              />\r\n            </el-select>\r\n          </td>\r\n          <td v-if=\"columnsFlag('名称')\" >\r\n            <el-input v-model.trim=\"item.name\" size=\"mini\" />\r\n          </td>\r\n          <td v-if=\"columnsFlag('类别')\" >\r\n            <span style=\"color: #00afff;cursor: pointer\" @click=\"showType(item)\" >{{materialText(item)}}</span>\r\n<!--            <el-select v-model=\"item.type\" size=\"mini\" >-->\r\n<!--              <el-option-->\r\n<!--                v-for=\"dict in bcTypeOptions\"-->\r\n<!--                :key=\"dict.dictValue\"-->\r\n<!--                :label=\"dict.dictLabel\"-->\r\n<!--                :value=\"dict.dictValue\"-->\r\n<!--              />-->\r\n<!--            </el-select>-->\r\n<!--            <template v-if=\"item.type === '0'\" >-->\r\n<!--              <el-select v-model=\"item.zrqType\" placeholder=\"主容器\" size=\"mini\" >-->\r\n<!--                <el-option-->\r\n<!--                  v-for=\"dict in zrqOptions\"-->\r\n<!--                  :key=\"dict.dictValue\"-->\r\n<!--                  :label=\"dict.dictLabel\"-->\r\n<!--                  :value=\"dict.dictValue\"-->\r\n<!--                />-->\r\n<!--              </el-select>-->\r\n<!--              <el-select v-model=\"item.allocator\" clearable multiple placeholder=\"分配器\" size=\"mini\" >-->\r\n<!--                <el-option-->\r\n<!--                  v-for=\"dict in allocatorOptions\"-->\r\n<!--                  :key=\"dict.dictValue\"-->\r\n<!--                  :label=\"dict.dictLabel\"-->\r\n<!--                  :value=\"dict.dictValue\"-->\r\n<!--                />-->\r\n<!--              </el-select>-->\r\n<!--            </template>-->\r\n          </td>\r\n          <td v-if=\"columnsFlag('物料属性')\" >\r\n            <el-select v-model=\"item.mb008\" size=\"mini\" >\r\n              <el-option\r\n                  v-for=\"d in attrOptions\"\r\n                  :key=\"d.dictValue\"\r\n                  :label=\"d.dictLabel\"\r\n                  :value=\"d.dictValue\"\r\n              />\r\n            </el-select>\r\n          </td>\r\n          <td v-if=\"columnsFlag('包装材料')\" >\r\n            <el-select v-model=\"item.materialType\" size=\"mini\" >\r\n              <el-option\r\n                v-for=\"dict in materialTypeOptions\"\r\n                :key=\"dict.dictValue\"\r\n                :label=\"dict.dictLabel\"\r\n                :value=\"dict.dictValue\"\r\n              />\r\n            </el-select>\r\n          </td>\r\n<!--          <td v-if=\"columnsFlag('包材库编码')\" >{{item.bcCode}}</td>-->\r\n          <td v-if=\"columnsFlag('包材ERP编码')\" >\r\n            <el-input v-model.trim=\"item.erpCode\" size=\"mini\" />\r\n          </td>\r\n          <td v-if=\"columnsFlag('规格')\" ><el-input v-model=\"item.spec\" size=\"mini\" /></td>\r\n          <td v-if=\"columnsFlag('尺寸')\" >\r\n            <div style=\"display: flex;align-items: center;\" >\r\n              <el-input v-model=\"item.length\" placeholder=\"长\" size=\"mini\" style=\"width: 80px\" type=\"number\" />\r\n              *\r\n              <el-input v-model=\"item.width\" placeholder=\"宽\" size=\"mini\" style=\"width: 80px\" type=\"number\" />\r\n              *\r\n              <el-input v-model=\"item.height\" placeholder=\"高\" size=\"mini\" style=\"width: 80px\" type=\"number\" />\r\n            </div>\r\n          </td>\r\n          <td v-if=\"columnsFlag('型号')\" ><el-input v-model=\"item.model\" size=\"mini\" /></td>\r\n          <td v-if=\"columnsFlag('供应商')\" >\r\n            <el-select v-if=\"devStatus === '1'\" v-model=\"item.supplierId\" clearable  filterable size=\"mini\" >\r\n              <el-option\r\n                v-for=\"item in supplierList\"\r\n                :key=\"item.id\"\r\n                :label=\"item.name\"\r\n                :value=\"item.id\"\r\n              />\r\n            </el-select>\r\n          </td>\r\n          <td v-if=\"columnsFlag('图片')\" ><ImageUpload v-model=\"item.imgs\" :is-show-tip=\"false\" /></td>\r\n          <td v-if=\"columnsFlag('COA/SPEC')\" ><FileUpload :id=\"item.id?item.id:item.key\" v-model=\"item.files\" :is-show-tip=\"false\" :view-type=\"1\" @change=\"fileChange\" /></td>\r\n          <td v-if=\"columnsFlag('备注') && devStatus === '0'\" >\r\n            <el-input v-model=\"item.remark\" autosize placeholder=\"客户指定供应商信息、联系方式,以及其它特殊要求\" size=\"mini\" type=\"textarea\" />\r\n          </td>\r\n          <td v-if=\"devStatus === '1'\" >{{item.priceDate}}</td>\r\n          <td v-if=\"devStatus === '1'\" >{{valueToLabel(stageOptions,item.stage)}}</td>\r\n          <td v-if=\"devStatus === '1'\" class=\"nth0\" >\r\n            <div v-if=\"item.id\" style=\"color: #00afff;cursor: pointer\" @click=\"handlePrice(item)\"  >\r\n                <span v-if=\"item.price\">\r\n                  {{item.price}}\r\n                </span>\r\n              <el-tooltip v-else content=\"阶梯价\" placement=\"top\">\r\n                <span class=\"el-icon-edit\" />\r\n              </el-tooltip>\r\n            </div>\r\n          </td>\r\n          <template v-for=\"log in logArray\" >\r\n            <td v-for=\"(l,z) in log.array\" :key=\"log.value  + '_' + z \" style=\"width: 80px\" >\r\n              <span :class=\"tdClass(l.projectBcIds,item.id)\"></span>\r\n            </td>\r\n          </template>\r\n        </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <el-pagination\r\n      layout=\"prev, pager, next\"\r\n      @current-change=\"handleCurrentChange\"\r\n      :current-page=\"currentPage\"\r\n      :page-size=\"pageSize\"\r\n      :total=\"materialList.length\">\r\n    </el-pagination>\r\n\r\n    <el-dialog :visible.sync=\"bomOpen\" append-to-body title=\"选择bom\" width=\"1200px\">\r\n      <el-input v-model=\"erpCode\" size=\"mini\" >\r\n        <template slot=\"append\" >\r\n          <el-button :loading=\"btnLoading\" icon=\"el-icon-search\" @click=\"getErpInfo\" />\r\n        </template>\r\n      </el-input>\r\n      <el-tree\r\n        ref=\"bomTree\"\r\n        :data=\"bomTree\"\r\n        check-strictly\r\n        default-expand-all\r\n        node-key=\"id\"\r\n        show-checkbox\r\n      >\r\n        <span slot-scope=\"{ node, data }\" class=\"custom-tree-node\" >\r\n          <span>{{ data.mb002 }}</span>\r\n          <span>{{ data.md003 }}</span>\r\n        </span>\r\n      </el-tree>\r\n      <div class=\"dialog-footer\" style=\"margin-top: 20px\">\r\n        <el-button :loading=\"btnLoading\" size=\"mini\" type=\"primary\" @click=\"confirmBom\" >确 定</el-button>\r\n        <el-button size=\"mini\" @click=\"bomOpen = false\" >取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog :visible.sync=\"bcOpen\" append-to-body title=\"选择包材\" width=\"1200px\">\r\n      <MaterialGoodsSelectTable @change=\"bcChange\" />\r\n      <div class=\"dialog-footer\" style=\"margin-top: 20px\">\r\n        <el-button :loading=\"btnLoading\" size=\"mini\" type=\"primary\" @click=\"confirmBc\" >确 定</el-button>\r\n        <el-button size=\"mini\" @click=\"bcOpen = false\" >取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog :close-on-click-modal=\"false\" :fullscreen=\"fullscreenFlag\" :visible.sync=\"open\" append-to-body width=\"1200px\">\r\n      <div slot=\"title\" class=\"dialog-title\">\r\n        阶梯价\r\n        <el-button :icon=\"fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'\" type=\"text\"\r\n                   @click=\"fullscreenFlag = !fullscreenFlag\"/>\r\n      </div>\r\n      <BcPriceTable\r\n        v-if=\"currentRow.id\"\r\n        :config-array=\"configArray\"\r\n        :files=\"files\"\r\n        :form=\"currentRow\"\r\n        :price-array=\"priceArray\"\r\n        :project-bc-id=\"currentRow.id\"\r\n        @saveSuccess=\"saveSuccess\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <CustomCols\r\n      ref=\"customCols\"\r\n      :default-columns=\"columns\"\r\n      name=\"projectBc\"\r\n      @success=\"colSuccess\"\r\n    />\r\n\r\n    <el-dialog :visible.sync=\"typeOpen\" append-to-body title=\"选择类别\" width=\"1200px\">\r\n      <el-form ref=\"form\" :model=\"currentRow\" :rules=\"rules\" label-width=\"120px\" size=\"mini\" >\r\n        <el-form-item prop=\"mb005\">\r\n          <template #label>\r\n            物料类型\r\n          </template>\r\n          <el-radio-group v-model=\"currentRow.mb005\" style=\"width: 90%;\" @input=\"mb005Change\" >\r\n            <el-row :gutter=\"20\" >\r\n              <el-col v-for=\"dict in mb005Options\" :key=\"dict.value\" :span=\"3\" >\r\n                <el-radio :label=\"dict.value\" style=\"padding-bottom: 10px\" >\r\n                  {{dict.label}}\r\n                </el-radio>\r\n              </el-col>\r\n            </el-row>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"currentRow.mb005 === '104'\" prop=\"type\">\r\n          <template #label>\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            包材类别\r\n          </template>\r\n          <el-radio-group v-model=\"currentRow.type\" @input=\"typeChange\" >\r\n            <el-row :gutter=\"20\" >\r\n              <el-col v-for=\"dict in bcTypeOptions\" :key=\"dict.dictValue\" :span=\"3\" >\r\n                <el-radio :label=\"dict.dictValue\" style=\"padding-bottom: 10px\">\r\n                  {{dict.dictLabel}}\r\n                </el-radio>\r\n              </el-col>\r\n            </el-row>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"currentRow.mb005 === '104' && currentRow.type === '0'\" >\r\n          <template #label>\r\n            <span style=\"color: #F56C6C\">*</span>\r\n            主容器-类别\r\n          </template>\r\n          <el-radio-group v-model=\"currentRow.zrqType\" >\r\n            <el-row :gutter=\"20\" >\r\n              <el-col v-for=\"dict in zrqOptions\" :key=\"dict.dictValue\" :span=\"3\" >\r\n                <el-radio :label=\"dict.dictValue\" style=\"padding-bottom: 10px\">\r\n                  {{dict.dictLabel}}\r\n                </el-radio>\r\n              </el-col>\r\n            </el-row>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"currentRow.mb005 === '104' && currentRow.allocator && currentRow.type === '0'\" label=\"分配器\" >\r\n          <el-checkbox-group v-model=\"currentRow.allocator\">\r\n            <el-row :gutter=\"20\" >\r\n              <el-col v-for=\"dict in allocatorOptions\" :key=\"dict.dictValue\" :span=\"3\" >\r\n                <el-checkbox\r\n                  :label=\"dict.dictValue\">\r\n                  {{dict.dictLabel}}\r\n                </el-checkbox>\r\n              </el-col>\r\n            </el-row>\r\n          </el-checkbox-group>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div class=\"dialog-footer\" style=\"margin-top: 20px\">\r\n        <el-button :loading=\"btnLoading\" size=\"mini\" type=\"primary\" @click=\"confirmMaterial\" >确 定</el-button>\r\n        <el-button size=\"mini\" @click=\"typeOpen = false\" >取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog :visible.sync=\"treeOpen\" append-to-body width=\"1200px\">\r\n      <template #title>\r\n        选择类别\r\n        <el-button\r\n          icon=\"el-icon-question\"\r\n          size=\"mini\"\r\n          type=\"text\"\r\n          @click=\"openDoc\"\r\n        >包材分类</el-button>\r\n      </template>\r\n\r\n      <el-radio-group v-model=\"currentRow.mb005\" style=\"width: 90%;\" @input=\"mb005Change\" >\r\n        <el-row :gutter=\"20\" >\r\n          <el-col v-for=\"dict in mb005Options.filter(i=>i.value !== '104')\" :key=\"dict.value\" :span=\"3\" >\r\n            <el-radio :label=\"dict.value\" style=\"font-size: 18px;font-weight: 700;margin-bottom: 10px\" >\r\n              {{dict.label}}\r\n            </el-radio>\r\n          </el-col>\r\n        </el-row>\r\n      </el-radio-group>\r\n      <div style=\"display: flex;margin-bottom: 10px\" >\r\n        <div v-for=\"dict in mb005Options.filter(i=>i.value === '104')\" :key=\"dict.value\" class=\"label\"  style=\"font-size: 15px;font-weight: 700;width: 100px\" >\r\n          {{dict.label}}\r\n        </div>\r\n        <div >\r\n          <div>\r\n            <div class=\"row-wrapper\">\r\n              <div class=\"label\" >主包材类别</div>\r\n              <div class=\"content\">\r\n                <el-radio-group v-model=\"currentRow.zrqType\" @input=\"zrqTypeChange\" >\r\n                  <el-row :gutter=\"20\" >\r\n                    <el-col v-for=\"dict in zrqOptions\" :key=\"dict.dictValue\" :span=\"4\" >\r\n                      <el-radio :label=\"dict.dictValue\" style=\"padding-bottom: 10px\">\r\n                        {{dict.dictLabel}}\r\n                      </el-radio>\r\n                    </el-col>\r\n                  </el-row>\r\n                </el-radio-group>\r\n              </div>\r\n            </div>\r\n            <div class=\"row-wrapper\">\r\n              <div class=\"label\">分配器</div>\r\n              <div class=\"content\">\r\n                <el-checkbox-group v-model=\"currentRow.allocator\" @input=\"allocatorChange\" >\r\n                  <el-row :gutter=\"20\" >\r\n                    <el-col v-for=\"dict in allocatorOptions\" :key=\"dict.dictValue\" :span=\"4\" >\r\n                      <el-checkbox\r\n                        :label=\"dict.dictValue\">\r\n                        {{dict.dictLabel}}\r\n                      </el-checkbox>\r\n                    </el-col>\r\n                  </el-row>\r\n                </el-checkbox-group>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div style=\"margin-top: 10px\">\r\n            <el-radio-group v-model=\"currentRow.type\" @input=\"typeChange\" >\r\n              <el-row :gutter=\"20\" >\r\n                <el-col v-for=\"dict in bcTypeOptions.filter(i=>i.dictValue !== '0')\" :key=\"dict.dictValue\" :span=\"4\" >\r\n                  <el-radio :label=\"dict.dictValue\" style=\"padding-bottom: 10px\">\r\n                    {{dict.dictLabel}}\r\n                    <el-tooltip v-if=\"dict.dictLabel === '辅助工具'\" content=\"(例如:粉扑、粉刷、勺子等)\" >\r\n                      <i class=\"el-icon-question\" />\r\n                    </el-tooltip>\r\n                  </el-radio>\r\n                </el-col>\r\n              </el-row>\r\n            </el-radio-group>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"dialog-footer\" style=\"margin-top: 20px\">\r\n        <el-button :loading=\"btnLoading\" size=\"mini\" type=\"primary\" @click=\"confirmTree\" >确 定</el-button>\r\n        <el-button size=\"mini\" @click=\"treeOpen = false\" >取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n<script>\r\nimport {getBomByErpCode, getProductByMd003} from \"@/api/common/erp\";\r\nimport MaterialGoodsSelectTable from \"@/views/resource/materialGoods/selectTable.vue\";\r\nimport {supplierAll} from \"@/api/supplier/supplier\";\r\nimport {allBcLog} from \"@/api/project/bcLog\";\r\nimport BcPriceTable from \"@/views/sop/bc/bcPriceTable.vue\";\r\nimport CustomCols from \"@/components/customCols.vue\";\r\nimport {allTreeData} from \"@/api/system/treeData\";\r\nimport {erpBom} from \"@/api/production/schedulePlan\";\r\nimport {delBc, updateBcForce} from \"@/api/project/bc\";\r\n\r\nexport default {\r\n  name: 'projectBcTable',\r\n  components: {\r\n    CustomCols,\r\n    BcPriceTable,\r\n    MaterialGoodsSelectTable,\r\n  },\r\n  props: {\r\n    form: {\r\n      type: Object,\r\n      required: true,\r\n    },\r\n    materialList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    devStatus: {\r\n      type: String,\r\n      default: '0',\r\n    },\r\n    readonly: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  watch: {\r\n    'form.id': {\r\n      async handler(v) {\r\n        this.resetQuery()\r\n        if(v && this.readonly) {//如果是视图模式\r\n          let bcLogList = await allBcLog({projectId: v, status: '3'})\r\n          let logArray = []\r\n          for (let item of this.logOptions) {\r\n            let bcLog = bcLogList.filter(i => i.projectId === v && i.type === item.dictValue)\r\n            if (bcLog && bcLog.length > 0) {\r\n              let array = []\r\n              let keys = []\r\n              for (let bc of bcLog) {\r\n                if (!keys.includes(bc.projectItemOrderCode)) {\r\n                  keys.push(bc.projectItemOrderCode)\r\n                }\r\n              }\r\n              for (let k of keys) {\r\n                let projectBcIds = bcLog.filter(i => i.projectItemOrderCode === k).map(i => i.projectBcId)\r\n                array.push({\r\n                  projectItemOrderCode: k,\r\n                  projectBcIds,\r\n                })\r\n              }\r\n              logArray.push({\r\n                label: item.dictLabel,\r\n                value: item.dictValue,\r\n                array,\r\n              })\r\n            }\r\n          }\r\n          this.logArray = logArray\r\n          this.bcLogList = bcLogList\r\n        }\r\n      },\r\n      immediate: true,\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      queryParams: {\r\n        name: null,\r\n      },\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      loading: false,\r\n      btnLoading: false,\r\n      bomOpen: false,\r\n      bcOpen: false,\r\n      open: false,\r\n      fullscreenFlag: false,\r\n      bcTypeOptions: [],\r\n      bomData: [],\r\n      bomTree: [],\r\n      currentBcArray: [],\r\n      typeOptions: [\r\n        {label: '包材开发',value: '0'},\r\n        {label: '客户开发',value: '1'},\r\n        {label: '采购开发',value: '2'},\r\n        {label: '自制',value: '3'},  //20241122研发开发改为自制\r\n      ],\r\n      resourceTypeOptions: [\r\n        {label: '普通添加',value: 'customer'},\r\n        {label: 'bom',value: 'bom'},\r\n        {label: '包材库',value: 'bc'},\r\n        {label: '包材开发',value: 'dev'},\r\n      ],\r\n      attrOptions: [],\r\n      zrqOptions: [],\r\n      materialTypeOptions: [],\r\n      supplierList: [],\r\n      title: null,\r\n      columns: [\r\n        {label: '开发类型',visible: true},\r\n        {label: '项目包材编码',visible: true},\r\n        {label: '类别',visible: true},\r\n        {label: '包装材料',visible: true},\r\n        {label: '包材库编码',visible: true},\r\n        {label: '包材ERP编码',visible: true},\r\n        {label: '物料属性',visible: true},\r\n        {label: '名称',visible: true},\r\n        {label: '规格',visible: true},\r\n        {label: '尺寸',visible: true},\r\n        {label: '型号',visible: true},\r\n        {label: '供应商',visible: true},\r\n        {label: '图片',visible: false},\r\n        {label: 'COA/SPEC',visible: false},\r\n        {label: '备注',visible: true},\r\n      ],\r\n      logArray: [],\r\n      bcLogList: [],\r\n      logOptions: [],\r\n      priceArray: [],\r\n      configArray: [],\r\n      files: [],\r\n      currentRow: {},\r\n      gradedTypeOptions: [\r\n        {label: '订单价',value: '0'},\r\n        {label: 'MOQ价',value: '1'},\r\n        {label: '梯度价(一档)',value: '2'},\r\n        {label: '梯度价(二档)',value: '3'},\r\n        {label: '梯度价(三档)',value: '4'},\r\n        {label: '梯度价(四档)',value: '5'},\r\n        {label: '梯度价(五档)',value: '6'},\r\n        {label: '梯度价(六档)',value: '7'},\r\n        {label: '梯度价(七档)',value: '8'},\r\n        {label: '梯度价(八档)',value: '9'},\r\n        {label: '梯度价(九档)',value: '10'},\r\n      ],\r\n      stageOptions: [\r\n        {label:'裸包价',value: '0'},\r\n        {label:'寻样阶段',value: '1'},\r\n        {label:'打样阶段',value: '2'},\r\n        {label:'订单阶段',value: '3'},\r\n      ],\r\n      allocatorOptions: [],\r\n      erpCode: [],\r\n      typeOpen: false,\r\n      treeOpen: false,\r\n      mb005Options: [\r\n        {label: '包材',value: '104'},\r\n        {label: '半成品',value: '103'},\r\n        {label: '裸装品',value: '102'},\r\n        {label: '成品',value: '101'},\r\n      ],\r\n      rules: {\r\n        mb005: [\r\n          {required: true,msg: '请选择物料类型'}\r\n        ]\r\n      },\r\n      materialArray: [],\r\n    }\r\n  },\r\n  async created() {\r\n    this.getDicts(\"PRODUCT_PROPERTIES\").then(response => {\r\n      const attrOptions = response.data\r\n      attrOptions.push({dictLabel: '自制',dictValue: '0'})\r\n      attrOptions.push({dictLabel: '外购',dictValue: '1'})\r\n      attrOptions.push({dictLabel: '客指代采',dictValue: '3'})\r\n      this.attrOptions = attrOptions\r\n    })\r\n    let logRes = await this.getDicts(\"project_bc_log\")\r\n    this.logOptions = logRes.data;\r\n    let bcTypeRes = await this.getDicts(\"project_bc_type\")\r\n    this.bcTypeOptions = bcTypeRes.data;\r\n    this.getDicts(\"bc-zrq\").then(response => {\r\n      this.zrqOptions = response.data;\r\n    })\r\n    this.getDicts(\"BZCL\").then(response => {\r\n      this.materialTypeOptions = response.data;\r\n    })\r\n    this.getDicts(\"bc-fpq\").then(response => {\r\n      this.allocatorOptions = response.data;\r\n    })\r\n    let supplierList = await supplierAll({supplierType: '1',reqType:1});\r\n    this.supplierList = supplierList;\r\n  },\r\n  methods: {\r\n    getList() {\r\n      let materialArray = this.materialList.filter(i => i.mb005 !== '103')\r\n      let params = Object.assign({}, this.queryParams)\r\n      if (params.name) {\r\n        materialArray = materialArray.filter(i => i.name == params.name)\r\n      }\r\n      this.materialArray = materialArray.slice((this.currentPage-1) * this.pageSize, this.currentPage * this.pageSize)\r\n    },\r\n    handleQuery() {\r\n      this.currentPage = 1\r\n      this.getList()\r\n    },\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.currentPage = val\r\n    },\r\n    openDoc() {\r\n      window.open('https://view.officeapps.live.com/op/view.aspx?src=https://enow.oss-cn-beijing.aliyuncs.com/images/20240909/1725847832537.pptx')\r\n    },\r\n    async confirmTree() {\r\n      const params = {\r\n        id: this.currentRow.id,\r\n        mb005: this.currentRow.mb005,\r\n        zrqType: this.currentRow.zrqType,\r\n        allocator: this.currentRow.allocator.join(','),\r\n        type: this.currentRow.type,\r\n      }\r\n      const res = await updateBcForce(params)\r\n      this.treeOpen = false\r\n    },\r\n    mb005Change() {\r\n      this.currentRow.type = null\r\n      this.currentRow.zrqType = null\r\n      this.currentRow.allocator = []\r\n    },\r\n    typeChange() {\r\n      this.currentRow.mb005 = '104'\r\n      this.currentRow.zrqType = null\r\n      this.currentRow.allocator = []\r\n    },\r\n    zrqTypeChange() {\r\n      this.currentRow.mb005 = '104'\r\n      this.currentRow.type = '0'\r\n    },\r\n    allocatorChange() {\r\n      this.currentRow.mb005 = '104'\r\n      this.currentRow.type = '0'\r\n    },\r\n    async confirmMaterial() {\r\n      await this.$refs[\"form\"].validate()\r\n      const form = this.currentRow\r\n      if(form.mb005 === '104') {\r\n        if(!form.type) {\r\n          this.msgError('请选择包材类型')\r\n          return\r\n        }\r\n        if(form.type==='0') { // 如果是主包材,没有选择主包材类型\r\n          if(!form.zrqType) {\r\n            this.msgError('请选择主包材类型')\r\n            return\r\n          }\r\n        }\r\n      }\r\n      this.typeOpen = false\r\n    },\r\n    allocatorText(allocator) {\r\n      const arr = this.allocatorOptions.filter(i=> allocator.includes(i.dictValue))\r\n      if(arr && arr[0]) {\r\n        return arr.map(i=> i.dictLabel).join(',')\r\n      }\r\n    },\r\n    mb005Text(mb005) {\r\n      const arr = this.mb005Options.filter(i=> mb005 === i.value)\r\n      if(arr && arr[0]) {\r\n        return arr[0].label\r\n      }\r\n    },\r\n    showType(row) {\r\n      this.currentRow = row\r\n      this.treeOpen = true\r\n    },\r\n    materialText(item) {\r\n      const array = []\r\n      if(item.mb005) {\r\n        array.push(this.mb005Text(item.mb005))\r\n        if(item.mb005 === '104') {\r\n          if(item.type) {\r\n            array.push(this.selectDictLabel(this.bcTypeOptions,item.type))\r\n          }\r\n          if(item.zrqType) {\r\n            array.push(this.selectDictLabel(this.zrqOptions,item.zrqType))\r\n          }\r\n          if(item.allocator.length) {\r\n            array.push(this.allocatorText(item.allocator))\r\n          }\r\n        }\r\n        return array.join('/')\r\n      } else {\r\n        return \"请选择\"\r\n      }\r\n    },\r\n    async showCol() {\r\n      await this.$nextTick()\r\n      this.$refs.customCols.columnsOpen = true\r\n    },\r\n    colSuccess() {\r\n\r\n    },\r\n    valueToLabel(options,value) {\r\n      const arr = options.filter(i=> i.value === value)\r\n      if(arr && arr[0]) {\r\n        return arr[0].label\r\n      }\r\n    },\r\n    priceText(row) {\r\n      if(row.priceArray) {\r\n        const o = row.priceArray[0]\r\n        const array = []\r\n        if(o.stage) {\r\n          array.push(this.valueToLabel(this.stageOptions,o.stage))\r\n        }\r\n        array.push(\"(\")\r\n        array.push(o.createDate)\r\n        array.push(\")\")\r\n        if(o.array && o.array[0]) {\r\n          const sub = o.array[0]\r\n          if(sub.gradedType) {\r\n            array.push(this.valueToLabel(this.gradedTypeOptions,sub.gradedType))\r\n          }\r\n          if(sub.price) {\r\n            array.push(sub.price)\r\n          }\r\n          if(sub.moq) {\r\n            array.push('起订量:' + sub.moq)\r\n          }\r\n        }\r\n        return array.join('')\r\n      }\r\n    },\r\n    validateTable() {\r\n      for (const item of this.materialList) {\r\n        if(!item.opType) {\r\n          throw new Error('请选择开发类型!')\r\n          return\r\n        }\r\n        if(!item.name) {\r\n          throw new Error('请输入名称!')\r\n          return\r\n        }\r\n        if(!item.mb005) {\r\n          throw new Error('请输入物料类型!')\r\n          return\r\n        }\r\n        if(!item.mb008) {\r\n          throw new Error('请输入物料属性!')\r\n          return\r\n        }\r\n        if(item.mb005 === '104') {\r\n          if(!item.type) {\r\n            throw new Error('请选择类别!')\r\n            return\r\n          }\r\n          if(item.type==='0' && (!item.zrqType && !item.allocator.length)) {//主容器或分配器二选一\r\n            throw new Error('请选择主容器类别或分配器!')\r\n            return\r\n          }\r\n        }\r\n      }\r\n    },\r\n    async saveSuccess() {\r\n      this.$emit(\"saveSuccess\")\r\n      this.open = false\r\n    },\r\n    resetPriceArray() {\r\n      this.configArray = [\r\n        '1'\r\n      ]\r\n      this.priceArray = []\r\n      this.files = []\r\n    },\r\n    async handlePrice(row) {\r\n      this.currentRow = row\r\n      this.resetPriceArray()\r\n      if(row.priceArray) {\r\n        this.priceArray = row.priceArray\r\n      }\r\n      if(row.configArray) {\r\n        this.configArray = row.configArray\r\n      }\r\n      if(row.files) {\r\n        this.files = row.files\r\n      }\r\n      this.open = true\r\n    },\r\n    resourceText(resourceType) {\r\n      const arr = this.resourceTypeOptions.filter(i=> i.value === resourceType)\r\n      if(arr && arr[0]) {\r\n        return arr[0].label\r\n      }\r\n    },\r\n    fileChange(fileList,id) {\r\n      for(let item of this.materialList) {\r\n        if(item.key === id || item.id === id) {\r\n          item.files = fileList\r\n        }\r\n      }\r\n    },\r\n    confirmBc() {\r\n      for (const item of this.currentBcArray) {\r\n        let o = {\r\n          resourceType: 'bc',\r\n          opType: null,\r\n          code: null,\r\n          bcCode: item.materialCode,\r\n          erpCode: item.erpCode,\r\n          name: item.materialName,\r\n          spec: item.capacity,\r\n          type: null,\r\n          zrqType: null,\r\n          allocator: [],\r\n          materialType: null,\r\n          length: item.length,\r\n          width: item.width,\r\n          height: item.height,\r\n          model: null,\r\n          vendor: null,\r\n          ecgy: null,\r\n          remark: null,\r\n          imgs: null,\r\n          files: [],\r\n          priceArray: [],\r\n          configArray: [],\r\n          supplierId: null,\r\n          devStatus: this.devStatus,\r\n        }\r\n        this.materialList.push(o)\r\n      }\r\n      this.bcOpen = false\r\n    },\r\n    bcChange(bcArray) {\r\n      this.currentBcArray = bcArray\r\n    },\r\n    selectBc() {\r\n      this.currentBcArray = []\r\n      this.bcOpen = true\r\n    },\r\n    async confirmBom() {\r\n      await this.$nextTick()\r\n      const ids = this.$refs.bomTree.getCheckedNodes().map(i => i.id)\r\n      const arr = this.bomData.filter(i=>ids.includes(i.id))\r\n      if(arr && arr[0]) {\r\n        for (const item of arr) {\r\n          let o = {\r\n            resourceType: 'bom',\r\n            opType: null,\r\n            code: null,\r\n            bcCode: null,\r\n            erpCode: item.md003,\r\n            name: item.mb002,\r\n            mb005: item.mb005,\r\n            mb008: item.mb008,\r\n            spec: null,\r\n            type: null,\r\n            zrqType: null,\r\n            allocator: [],\r\n            materialType: null,\r\n            length: 0,\r\n            width: 0,\r\n            height: 0,\r\n            model: null,\r\n            vendor: null,\r\n            ecgy: null,\r\n            remark: null,\r\n            imgs: null,\r\n            files: [],\r\n            priceArray: [],\r\n            configArray: [],\r\n            supplierId: null,\r\n            devStatus: this.devStatus,\r\n          }\r\n          this.materialList.push(o)\r\n        }\r\n      }\r\n      this.bomOpen = false\r\n    },\r\n    toBomTree(list, md001) {\r\n      return list.filter(item => {\r\n        if(md001) {\r\n          if (item.md001 === md001) {\r\n            let children = this.toBomTree(list, item.md003)\r\n            if(children && children.length > 0) {\r\n              item.children = children\r\n            }\r\n            return true\r\n          }\r\n        } else {\r\n          if ([undefined,null,''].includes(item.md001)) {\r\n            let children = this.toBomTree(list, item.md003)\r\n            if(children && children.length > 0) {\r\n              item.children = children\r\n            }\r\n            return true\r\n          }\r\n        }\r\n        return false\r\n      })\r\n    },\r\n    async getErpInfo() {\r\n      const erpNo = this.erpCode\r\n      if (erpNo) {\r\n        this.btnLoading = true\r\n        this.loading = true\r\n        let erpRes = await getProductByMd003(erpNo);\r\n        if(erpRes && erpRes.data) {\r\n          let data = erpRes.data\r\n          let form = this.form\r\n          let mb002 = data.MB002\r\n          if(mb002){\r\n            form.ma003 = data.MA003\r\n            form.mb002 = mb002\r\n            form.mb005 = data.MB005\r\n\r\n            let bomList = await erpBom({erpNo})\r\n            bomList = bomList.filter(i => i.mb005 !== '105')\r\n            bomList.push({\r\n              id: erpNo,\r\n              mb002: mb002,\r\n              md003: erpNo,\r\n              mb005: data.MB005,\r\n              mb008: data.mb008,\r\n            })\r\n            let bomTree = this.toBomTree(JSON.parse(JSON.stringify(bomList)), undefined)\r\n            this.bomTree = bomTree\r\n            this.bomData = bomList\r\n          }else{\r\n            this.msgError('erp代码输入有误');\r\n            form.ma003 = null;\r\n            form.mb002 = null;\r\n          }\r\n        } else {\r\n          this.msgError('erp代码输入有误');\r\n        }\r\n        this.btnLoading = false\r\n        this.loading = false\r\n      }\r\n    },\r\n    async showBom() {\r\n      this.bomOpen = true\r\n    },\r\n    addItem() {\r\n      let o = {\r\n        rid: this.$nanoid(),\r\n        resourceType: 'customer',\r\n        opType: null,\r\n        type: null,\r\n        code: null,\r\n        bcCode: null,\r\n        erpCode: null,\r\n        name: null,\r\n        spec: null,\r\n        zrqType: null,\r\n        allocator: [],\r\n        materialType: null,\r\n        length: 0,\r\n        width: 0,\r\n        height: 0,\r\n        model: null,\r\n        vendor: null,\r\n        ecgy: null,\r\n        remark: null,\r\n        imgs: null,\r\n        files: [],\r\n        priceArray: [],\r\n        configArray: [],\r\n        supplierId: null,\r\n        devStatus: this.devStatus,\r\n      }\r\n      this.materialList.push(o)\r\n    },\r\n    async delItem(row){\r\n      let index;\r\n      if(row.id) {\r\n        index = this.materialList.findIndex(i=> i.id === row.id)\r\n      } else if(row.rid) {\r\n        index = this.materialList.findIndex(i=> i.rid === row.rid)\r\n      }\r\n      this.materialList.splice(index,1)\r\n      if(row.id) {\r\n        await delBc(row.id)\r\n      }\r\n    },\r\n    tdClass(projectBcIds,projectBcId) {\r\n      return projectBcIds.includes(projectBcId) ? 'el-icon-check' : ''\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.row-wrapper {\r\n  display: flex;\r\n\r\n  .label {\r\n    width: 150px;\r\n    font-size: 13px;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .content {\r\n\r\n  }\r\n}\r\n.table-wrapper {\r\n\r\n  .base-table {\r\n    thead {\r\n      position: sticky;\r\n      top: 0;\r\n      z-index: 3;\r\n\r\n      .nth0 {\r\n        background-color: rgba(248,248,249,1);\r\n      }\r\n\r\n    }\r\n\r\n    tbody {\r\n      .nth0 {\r\n        background-color: rgba(255,255,255,1);\r\n      }\r\n    }\r\n\r\n    .nth0 {\r\n      position: sticky;\r\n      left: 0;\r\n      z-index: 1;\r\n    }\r\n\r\n  }\r\n\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsaA,IAAAA,IAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AACA,IAAAK,aAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,WAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,SAAA,GAAAP,OAAA;AACA,IAAAQ,aAAA,GAAAR,OAAA;AACA,IAAAS,GAAA,GAAAT,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAU,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,wBAAA,EAAAA;EACA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,YAAA;MACAH,IAAA,EAAAI,KAAA;MACAF,QAAA;IACA;IACAG,SAAA;MACAL,IAAA,EAAAM,MAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACAR,IAAA,EAAAS,OAAA;MACAF,OAAA;IACA;EACA;EACAG,KAAA;IACA;MACAC,OAAA,WAAAA,QAAAC,CAAA;QAAA,IAAAC,KAAA;QAAA,WAAAC,kBAAA,CAAAP,OAAA,mBAAAQ,oBAAA,CAAAR,OAAA,IAAAS,IAAA,UAAAC,QAAA;UAAA,IAAAC,SAAA,EAAAC,QAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,KAAA;UAAA,WAAAP,oBAAA,CAAAR,OAAA,IAAAgB,IAAA,UAAAC,SAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAC,IAAA,GAAAD,SAAA,CAAAE,IAAA;cAAA;gBACAd,KAAA,CAAAe,UAAA;gBAAA,MACAhB,CAAA,IAAAC,KAAA,CAAAL,QAAA;kBAAAiB,SAAA,CAAAE,IAAA;kBAAA;gBAAA;gBAAAF,SAAA,CAAAE,IAAA;gBAAA,OACA,IAAAE,eAAA;kBAAAC,SAAA,EAAAlB,CAAA;kBAAAmB,MAAA;gBAAA;cAAA;gBAAAb,SAAA,GAAAO,SAAA,CAAAO,IAAA;gBACAb,QAAA;gBAAAC,SAAA,OAAAa,2BAAA,CAAA1B,OAAA,EACAM,KAAA,CAAAqB,UAAA;gBAAAT,SAAA,CAAAC,IAAA;gBAAAJ,KAAA,oBAAAP,oBAAA,CAAAR,OAAA,IAAAS,IAAA,UAAAM,MAAA;kBAAA,IAAAa,IAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,IAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,EAAA,EAAAC,MAAA,EAAAC,EAAA,EAAAC,KAAA;kBAAA,WAAA7B,oBAAA,CAAAR,OAAA,IAAAgB,IAAA,UAAAsB,OAAAC,SAAA;oBAAA,kBAAAA,SAAA,CAAApB,IAAA,GAAAoB,SAAA,CAAAnB,IAAA;sBAAA;wBAAAQ,IAAA,GAAAd,KAAA,CAAA0B,KAAA;wBACAX,KAAA,GAAAlB,SAAA,CAAA8B,MAAA,WAAAC,CAAA;0BAAA,OAAAA,CAAA,CAAAnB,SAAA,KAAAlB,CAAA,IAAAqC,CAAA,CAAAjD,IAAA,KAAAmC,IAAA,CAAAe,SAAA;wBAAA;wBAAA,MACAd,KAAA,IAAAA,KAAA,CAAAe,MAAA;0BAAAL,SAAA,CAAAnB,IAAA;0BAAA;wBAAA;wBACAU,KAAA;wBACAC,IAAA;wBAAAC,UAAA,OAAAN,2BAAA,CAAA1B,OAAA,EACA6B,KAAA;wBAAA;0BAAA,KAAAG,UAAA,CAAAa,CAAA,MAAAZ,MAAA,GAAAD,UAAA,CAAAc,CAAA,IAAAC,IAAA;4BAAAb,EAAA,GAAAD,MAAA,CAAAO,KAAA;4BACA,KAAAT,IAAA,CAAAiB,QAAA,CAAAd,EAAA,CAAAe,oBAAA;8BACAlB,IAAA,CAAAmB,IAAA,CAAAhB,EAAA,CAAAe,oBAAA;4BACA;0BACA;wBAAA,SAAAE,GAAA;0BAAAnB,UAAA,CAAAoB,CAAA,CAAAD,GAAA;wBAAA;0BAAAnB,UAAA,CAAAqB,CAAA;wBAAA;wBAAAlB,MAAA,oBAAA3B,oBAAA,CAAAR,OAAA,IAAAS,IAAA,UAAA0B,OAAA;0BAAA,IAAAmB,CAAA,EAAAC,YAAA;0BAAA,WAAA/C,oBAAA,CAAAR,OAAA,IAAAgB,IAAA,UAAAwC,QAAAC,QAAA;4BAAA,kBAAAA,QAAA,CAAAtC,IAAA,GAAAsC,QAAA,CAAArC,IAAA;8BAAA;gCACAkC,CAAA,GAAAjB,KAAA,CAAAD,EAAA;gCACAmB,YAAA,GAAA1B,KAAA,CAAAY,MAAA,WAAAC,CAAA;kCAAA,OAAAA,CAAA,CAAAO,oBAAA,KAAAK,CAAA;gCAAA,GAAAI,GAAA,WAAAhB,CAAA;kCAAA,OAAAA,CAAA,CAAAiB,WAAA;gCAAA;gCACA7B,KAAA,CAAAoB,IAAA;kCACAD,oBAAA,EAAAK,CAAA;kCACAC,YAAA,EAAAA;gCACA;8BAAA;8BAAA;gCAAA,OAAAE,QAAA,CAAAG,IAAA;4BAAA;0BAAA,GAAAzB,MAAA;wBAAA;wBAAAC,EAAA,MAAAC,KAAA,GALAN,IAAA;sBAAA;wBAAA,MAAAK,EAAA,GAAAC,KAAA,CAAAO,MAAA;0BAAAL,SAAA,CAAAnB,IAAA;0BAAA;wBAAA;wBAAA,OAAAmB,SAAA,CAAAsB,aAAA,CAAA1B,MAAA;sBAAA;wBAAAC,EAAA;wBAAAG,SAAA,CAAAnB,IAAA;wBAAA;sBAAA;wBAOAR,QAAA,CAAAsC,IAAA;0BACAY,KAAA,EAAAlC,IAAA,CAAAmC,SAAA;0BACAvB,KAAA,EAAAZ,IAAA,CAAAe,SAAA;0BACAb,KAAA,EAAAA;wBACA;sBAAA;sBAAA;wBAAA,OAAAS,SAAA,CAAAqB,IAAA;oBAAA;kBAAA,GAAA7C,KAAA;gBAAA;gBAAAF,SAAA,CAAAgC,CAAA;cAAA;gBAAA,KAAA/B,KAAA,GAAAD,SAAA,CAAAiC,CAAA,IAAAC,IAAA;kBAAA7B,SAAA,CAAAE,IAAA;kBAAA;gBAAA;gBAAA,OAAAF,SAAA,CAAA2C,aAAA,CAAA9C,KAAA;cAAA;gBAAAG,SAAA,CAAAE,IAAA;gBAAA;cAAA;gBAAAF,SAAA,CAAAE,IAAA;gBAAA;cAAA;gBAAAF,SAAA,CAAAC,IAAA;gBAAAD,SAAA,CAAA8C,EAAA,GAAA9C,SAAA;gBAAAL,SAAA,CAAAuC,CAAA,CAAAlC,SAAA,CAAA8C,EAAA;cAAA;gBAAA9C,SAAA,CAAAC,IAAA;gBAAAN,SAAA,CAAAwC,CAAA;gBAAA,OAAAnC,SAAA,CAAA+C,MAAA;cAAA;gBAGA3D,KAAA,CAAAM,QAAA,GAAAA,QAAA;gBACAN,KAAA,CAAAK,SAAA,GAAAA,SAAA;cAAA;cAAA;gBAAA,OAAAO,SAAA,CAAA0C,IAAA;YAAA;UAAA,GAAAlD,OAAA;QAAA;MAEA;MACAwD,SAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;QACAlF,IAAA;MACA;MACAmF,WAAA;MACAC,QAAA;MACAC,OAAA;MACAC,UAAA;MACAC,OAAA;MACAC,MAAA;MACAC,IAAA;MACAC,cAAA;MACAC,aAAA;MACAC,OAAA;MACAC,OAAA;MACAC,cAAA;MACAC,WAAA,GACA;QAAAnB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA;MAAA,CACA;MACA0C,mBAAA,GACA;QAAApB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,EACA;MACA2C,WAAA;MACAC,UAAA;MACAC,mBAAA;MACAC,YAAA;MACAC,KAAA;MACAC,OAAA,GACA;QAAA1B,KAAA;QAAA2B,OAAA;MAAA,GACA;QAAA3B,KAAA;QAAA2B,OAAA;MAAA,GACA;QAAA3B,KAAA;QAAA2B,OAAA;MAAA,GACA;QAAA3B,KAAA;QAAA2B,OAAA;MAAA,GACA;QAAA3B,KAAA;QAAA2B,OAAA;MAAA,GACA;QAAA3B,KAAA;QAAA2B,OAAA;MAAA,GACA;QAAA3B,KAAA;QAAA2B,OAAA;MAAA,GACA;QAAA3B,KAAA;QAAA2B,OAAA;MAAA,GACA;QAAA3B,KAAA;QAAA2B,OAAA;MAAA,GACA;QAAA3B,KAAA;QAAA2B,OAAA;MAAA,GACA;QAAA3B,KAAA;QAAA2B,OAAA;MAAA,GACA;QAAA3B,KAAA;QAAA2B,OAAA;MAAA,GACA;QAAA3B,KAAA;QAAA2B,OAAA;MAAA,GACA;QAAA3B,KAAA;QAAA2B,OAAA;MAAA,GACA;QAAA3B,KAAA;QAAA2B,OAAA;MAAA,EACA;MACA7E,QAAA;MACAD,SAAA;MACAgB,UAAA;MACA+D,UAAA;MACAC,WAAA;MACAC,KAAA;MACAC,UAAA;MACAC,iBAAA,GACA;QAAAhC,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,EACA;MACAuD,YAAA,GACA;QAAAjC,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,EACA;MACAwD,gBAAA;MACAC,OAAA;MACAC,QAAA;MACAC,QAAA;MACAC,YAAA,GACA;QAAAtC,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,EACA;MACA6D,KAAA;QACAC,KAAA,GACA;UAAA3G,QAAA;UAAA4G,GAAA;QAAA;MAEA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,WAAAnG,kBAAA,CAAAP,OAAA,mBAAAQ,oBAAA,CAAAR,OAAA,IAAAS,IAAA,UAAAkG,SAAA;MAAA,IAAAC,MAAA,EAAAC,SAAA,EAAAvB,YAAA;MAAA,WAAA9E,oBAAA,CAAAR,OAAA,IAAAgB,IAAA,UAAA8F,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAA5F,IAAA,GAAA4F,SAAA,CAAA3F,IAAA;UAAA;YACAsF,MAAA,CAAAM,QAAA,uBAAAC,IAAA,WAAAC,QAAA;cACA,IAAA/B,WAAA,GAAA+B,QAAA,CAAA/C,IAAA;cACAgB,WAAA,CAAAjC,IAAA;gBAAAa,SAAA;gBAAApB,SAAA;cAAA;cACAwC,WAAA,CAAAjC,IAAA;gBAAAa,SAAA;gBAAApB,SAAA;cAAA;cACAwC,WAAA,CAAAjC,IAAA;gBAAAa,SAAA;gBAAApB,SAAA;cAAA;cACA+D,MAAA,CAAAvB,WAAA,GAAAA,WAAA;YACA;YAAA4B,SAAA,CAAA3F,IAAA;YAAA,OACAsF,MAAA,CAAAM,QAAA;UAAA;YAAAJ,MAAA,GAAAG,SAAA,CAAAtF,IAAA;YACAiF,MAAA,CAAA/E,UAAA,GAAAiF,MAAA,CAAAzC,IAAA;YAAA4C,SAAA,CAAA3F,IAAA;YAAA,OACAsF,MAAA,CAAAM,QAAA;UAAA;YAAAH,SAAA,GAAAE,SAAA,CAAAtF,IAAA;YACAiF,MAAA,CAAA7B,aAAA,GAAAgC,SAAA,CAAA1C,IAAA;YACAuC,MAAA,CAAAM,QAAA,WAAAC,IAAA,WAAAC,QAAA;cACAR,MAAA,CAAAtB,UAAA,GAAA8B,QAAA,CAAA/C,IAAA;YACA;YACAuC,MAAA,CAAAM,QAAA,SAAAC,IAAA,WAAAC,QAAA;cACAR,MAAA,CAAArB,mBAAA,GAAA6B,QAAA,CAAA/C,IAAA;YACA;YACAuC,MAAA,CAAAM,QAAA,WAAAC,IAAA,WAAAC,QAAA;cACAR,MAAA,CAAAV,gBAAA,GAAAkB,QAAA,CAAA/C,IAAA;YACA;YAAA4C,SAAA,CAAA3F,IAAA;YAAA,OACA,IAAA+F,qBAAA;cAAAC,YAAA;cAAAC,OAAA;YAAA;UAAA;YAAA/B,YAAA,GAAAyB,SAAA,CAAAtF,IAAA;YACAiF,MAAA,CAAApB,YAAA,GAAAA,YAAA;UAAA;UAAA;YAAA,OAAAyB,SAAA,CAAAnD,IAAA;QAAA;MAAA,GAAA+C,QAAA;IAAA;EACA;EACAW,OAAA;IACAC,OAAA,WAAAA,QAAA;MACA,IAAAf,aAAA,QAAA5G,YAAA,CAAA6C,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAA4D,KAAA;MAAA;MACA,IAAAkB,MAAA,GAAA9H,MAAA,CAAA+H,MAAA,UAAArD,WAAA;MACA,IAAAoD,MAAA,CAAAtI,IAAA;QACAsH,aAAA,GAAAA,aAAA,CAAA/D,MAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAxD,IAAA,IAAAsI,MAAA,CAAAtI,IAAA;QAAA;MACA;MACA,KAAAsH,aAAA,GAAAA,aAAA,CAAAkB,KAAA,OAAArD,WAAA,aAAAC,QAAA,OAAAD,WAAA,QAAAC,QAAA;IACA;IACAqD,WAAA,WAAAA,YAAA;MACA,KAAAtD,WAAA;MACA,KAAAkD,OAAA;IACA;IACAlG,UAAA,WAAAA,WAAA;MACA,KAAAuG,SAAA;MACA,KAAAD,WAAA;IACA;IACAE,mBAAA,WAAAA,oBAAAC,GAAA;MACA,KAAAzD,WAAA,GAAAyD,GAAA;IACA;IACAC,OAAA,WAAAA,QAAA;MACAC,MAAA,CAAArD,IAAA;IACA;IACAsD,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,WAAA3H,kBAAA,CAAAP,OAAA,mBAAAQ,oBAAA,CAAAR,OAAA,IAAAS,IAAA,UAAA0H,SAAA;QAAA,IAAAX,MAAA,EAAAY,GAAA;QAAA,WAAA5H,oBAAA,CAAAR,OAAA,IAAAgB,IAAA,UAAAqH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnH,IAAA,GAAAmH,SAAA,CAAAlH,IAAA;YAAA;cACAoG,MAAA;gBACAe,EAAA,EAAAL,MAAA,CAAArC,UAAA,CAAA0C,EAAA;gBACAjC,KAAA,EAAA4B,MAAA,CAAArC,UAAA,CAAAS,KAAA;gBACAkC,OAAA,EAAAN,MAAA,CAAArC,UAAA,CAAA2C,OAAA;gBACAC,SAAA,EAAAP,MAAA,CAAArC,UAAA,CAAA4C,SAAA,CAAAC,IAAA;gBACAjJ,IAAA,EAAAyI,MAAA,CAAArC,UAAA,CAAApG;cACA;cAAA6I,SAAA,CAAAlH,IAAA;cAAA,OACA,IAAAuH,iBAAA,EAAAnB,MAAA;YAAA;cAAAY,GAAA,GAAAE,SAAA,CAAA7G,IAAA;cACAyG,MAAA,CAAA/B,QAAA;YAAA;YAAA;cAAA,OAAAmC,SAAA,CAAA1E,IAAA;UAAA;QAAA,GAAAuE,QAAA;MAAA;IACA;IACAS,WAAA,WAAAA,YAAA;MACA,KAAA/C,UAAA,CAAApG,IAAA;MACA,KAAAoG,UAAA,CAAA2C,OAAA;MACA,KAAA3C,UAAA,CAAA4C,SAAA;IACA;IACAI,UAAA,WAAAA,WAAA;MACA,KAAAhD,UAAA,CAAAS,KAAA;MACA,KAAAT,UAAA,CAAA2C,OAAA;MACA,KAAA3C,UAAA,CAAA4C,SAAA;IACA;IACAK,aAAA,WAAAA,cAAA;MACA,KAAAjD,UAAA,CAAAS,KAAA;MACA,KAAAT,UAAA,CAAApG,IAAA;IACA;IACAsJ,eAAA,WAAAA,gBAAA;MACA,KAAAlD,UAAA,CAAAS,KAAA;MACA,KAAAT,UAAA,CAAApG,IAAA;IACA;IACAuJ,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MAAA,WAAA1I,kBAAA,CAAAP,OAAA,mBAAAQ,oBAAA,CAAAR,OAAA,IAAAS,IAAA,UAAAyI,SAAA;QAAA,IAAA1J,IAAA;QAAA,WAAAgB,oBAAA,CAAAR,OAAA,IAAAgB,IAAA,UAAAmI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjI,IAAA,GAAAiI,SAAA,CAAAhI,IAAA;YAAA;cAAAgI,SAAA,CAAAhI,IAAA;cAAA,OACA6H,MAAA,CAAAI,KAAA,SAAAC,QAAA;YAAA;cACA9J,IAAA,GAAAyJ,MAAA,CAAApD,UAAA;cAAA,MACArG,IAAA,CAAA8G,KAAA;gBAAA8C,SAAA,CAAAhI,IAAA;gBAAA;cAAA;cAAA,IACA5B,IAAA,CAAAC,IAAA;gBAAA2J,SAAA,CAAAhI,IAAA;gBAAA;cAAA;cACA6H,MAAA,CAAAM,QAAA;cAAA,OAAAH,SAAA,CAAAI,MAAA;YAAA;cAAA,MAGAhK,IAAA,CAAAC,IAAA;gBAAA2J,SAAA,CAAAhI,IAAA;gBAAA;cAAA;cAAA,IACA5B,IAAA,CAAAgJ,OAAA;gBAAAY,SAAA,CAAAhI,IAAA;gBAAA;cAAA;cACA6H,MAAA,CAAAM,QAAA;cAAA,OAAAH,SAAA,CAAAI,MAAA;YAAA;cAKAP,MAAA,CAAA/C,QAAA;YAAA;YAAA;cAAA,OAAAkD,SAAA,CAAAxF,IAAA;UAAA;QAAA,GAAAsF,QAAA;MAAA;IACA;IACAO,aAAA,WAAAA,cAAAhB,SAAA;MACA,IAAAiB,GAAA,QAAA1D,gBAAA,CAAAvD,MAAA,WAAAC,CAAA;QAAA,OAAA+F,SAAA,CAAAzF,QAAA,CAAAN,CAAA,CAAAC,SAAA;MAAA;MACA,IAAA+G,GAAA,IAAAA,GAAA;QACA,OAAAA,GAAA,CAAAhG,GAAA,WAAAhB,CAAA;UAAA,OAAAA,CAAA,CAAAqB,SAAA;QAAA,GAAA2E,IAAA;MACA;IACA;IACAiB,SAAA,WAAAA,UAAArD,KAAA;MACA,IAAAoD,GAAA,QAAAtD,YAAA,CAAA3D,MAAA,WAAAC,CAAA;QAAA,OAAA4D,KAAA,KAAA5D,CAAA,CAAAF,KAAA;MAAA;MACA,IAAAkH,GAAA,IAAAA,GAAA;QACA,OAAAA,GAAA,IAAA5F,KAAA;MACA;IACA;IACA8F,QAAA,WAAAA,SAAAC,GAAA;MACA,KAAAhE,UAAA,GAAAgE,GAAA;MACA,KAAA1D,QAAA;IACA;IACA2D,YAAA,WAAAA,aAAAlI,IAAA;MACA,IAAAE,KAAA;MACA,IAAAF,IAAA,CAAA0E,KAAA;QACAxE,KAAA,CAAAoB,IAAA,MAAAyG,SAAA,CAAA/H,IAAA,CAAA0E,KAAA;QACA,IAAA1E,IAAA,CAAA0E,KAAA;UACA,IAAA1E,IAAA,CAAAnC,IAAA;YACAqC,KAAA,CAAAoB,IAAA,MAAA6G,eAAA,MAAAlF,aAAA,EAAAjD,IAAA,CAAAnC,IAAA;UACA;UACA,IAAAmC,IAAA,CAAA4G,OAAA;YACA1G,KAAA,CAAAoB,IAAA,MAAA6G,eAAA,MAAA3E,UAAA,EAAAxD,IAAA,CAAA4G,OAAA;UACA;UACA,IAAA5G,IAAA,CAAA6G,SAAA,CAAA7F,MAAA;YACAd,KAAA,CAAAoB,IAAA,MAAAuG,aAAA,CAAA7H,IAAA,CAAA6G,SAAA;UACA;QACA;QACA,OAAA3G,KAAA,CAAA4G,IAAA;MACA;QACA;MACA;IACA;IACAsB,OAAA,WAAAA,QAAA;MAAA,IAAAC,MAAA;MAAA,WAAA1J,kBAAA,CAAAP,OAAA,mBAAAQ,oBAAA,CAAAR,OAAA,IAAAS,IAAA,UAAAyJ,SAAA;QAAA,WAAA1J,oBAAA,CAAAR,OAAA,IAAAgB,IAAA,UAAAmJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjJ,IAAA,GAAAiJ,SAAA,CAAAhJ,IAAA;YAAA;cAAAgJ,SAAA,CAAAhJ,IAAA;cAAA,OACA6I,MAAA,CAAAI,SAAA;YAAA;cACAJ,MAAA,CAAAZ,KAAA,CAAAiB,UAAA,CAAAC,WAAA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAxG,IAAA;UAAA;QAAA,GAAAsG,QAAA;MAAA;IACA;IACAM,UAAA,WAAAA,WAAA,GAEA;IACAC,YAAA,WAAAA,aAAAC,OAAA,EAAAlI,KAAA;MACA,IAAAkH,GAAA,GAAAgB,OAAA,CAAAjI,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAF,KAAA,KAAAA,KAAA;MAAA;MACA,IAAAkH,GAAA,IAAAA,GAAA;QACA,OAAAA,GAAA,IAAA5F,KAAA;MACA;IACA;IACA6G,SAAA,WAAAA,UAAAd,GAAA;MACA,IAAAA,GAAA,CAAAnE,UAAA;QACA,IAAAkF,CAAA,GAAAf,GAAA,CAAAnE,UAAA;QACA,IAAA5D,KAAA;QACA,IAAA8I,CAAA,CAAAC,KAAA;UACA/I,KAAA,CAAAoB,IAAA,MAAAuH,YAAA,MAAA1E,YAAA,EAAA6E,CAAA,CAAAC,KAAA;QACA;QACA/I,KAAA,CAAAoB,IAAA;QACApB,KAAA,CAAAoB,IAAA,CAAA0H,CAAA,CAAAE,UAAA;QACAhJ,KAAA,CAAAoB,IAAA;QACA,IAAA0H,CAAA,CAAA9I,KAAA,IAAA8I,CAAA,CAAA9I,KAAA;UACA,IAAAiJ,GAAA,GAAAH,CAAA,CAAA9I,KAAA;UACA,IAAAiJ,GAAA,CAAAC,UAAA;YACAlJ,KAAA,CAAAoB,IAAA,MAAAuH,YAAA,MAAA3E,iBAAA,EAAAiF,GAAA,CAAAC,UAAA;UACA;UACA,IAAAD,GAAA,CAAAE,KAAA;YACAnJ,KAAA,CAAAoB,IAAA,CAAA6H,GAAA,CAAAE,KAAA;UACA;UACA,IAAAF,GAAA,CAAAG,GAAA;YACApJ,KAAA,CAAAoB,IAAA,UAAA6H,GAAA,CAAAG,GAAA;UACA;QACA;QACA,OAAApJ,KAAA,CAAA4G,IAAA;MACA;IACA;IACAyC,aAAA,WAAAA,cAAA;MAAA,IAAAC,UAAA,OAAA1J,2BAAA,CAAA1B,OAAA,EACA,KAAAJ,YAAA;QAAAyL,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAvI,CAAA,MAAAwI,MAAA,GAAAD,UAAA,CAAAtI,CAAA,IAAAC,IAAA;UAAA,IAAAnB,IAAA,GAAAyJ,MAAA,CAAA7I,KAAA;UACA,KAAAZ,IAAA,CAAA0J,MAAA;YACA,UAAAC,KAAA;YACA;UACA;UACA,KAAA3J,IAAA,CAAA1C,IAAA;YACA,UAAAqM,KAAA;YACA;UACA;UACA,KAAA3J,IAAA,CAAA0E,KAAA;YACA,UAAAiF,KAAA;YACA;UACA;UACA,KAAA3J,IAAA,CAAA4J,KAAA;YACA,UAAAD,KAAA;YACA;UACA;UACA,IAAA3J,IAAA,CAAA0E,KAAA;YACA,KAAA1E,IAAA,CAAAnC,IAAA;cACA,UAAA8L,KAAA;cACA;YACA;YACA,IAAA3J,IAAA,CAAAnC,IAAA,aAAAmC,IAAA,CAAA4G,OAAA,KAAA5G,IAAA,CAAA6G,SAAA,CAAA7F,MAAA;cAAA;cACA,UAAA2I,KAAA;cACA;YACA;UACA;QACA;MAAA,SAAApI,GAAA;QAAAiI,UAAA,CAAAhI,CAAA,CAAAD,GAAA;MAAA;QAAAiI,UAAA,CAAA/H,CAAA;MAAA;IACA;IACAoI,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,WAAAnL,kBAAA,CAAAP,OAAA,mBAAAQ,oBAAA,CAAAR,OAAA,IAAAS,IAAA,UAAAkL,SAAA;QAAA,WAAAnL,oBAAA,CAAAR,OAAA,IAAAgB,IAAA,UAAA4K,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1K,IAAA,GAAA0K,SAAA,CAAAzK,IAAA;YAAA;cACAsK,MAAA,CAAAI,KAAA;cACAJ,MAAA,CAAA/G,IAAA;YAAA;YAAA;cAAA,OAAAkH,SAAA,CAAAjI,IAAA;UAAA;QAAA,GAAA+H,QAAA;MAAA;IACA;IACAI,eAAA,WAAAA,gBAAA;MACA,KAAApG,WAAA,IACA,IACA;MACA,KAAAD,UAAA;MACA,KAAAE,KAAA;IACA;IACAoG,WAAA,WAAAA,YAAAnC,GAAA;MAAA,IAAAoC,MAAA;MAAA,WAAA1L,kBAAA,CAAAP,OAAA,mBAAAQ,oBAAA,CAAAR,OAAA,IAAAS,IAAA,UAAAyL,SAAA;QAAA,WAAA1L,oBAAA,CAAAR,OAAA,IAAAgB,IAAA,UAAAmL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjL,IAAA,GAAAiL,SAAA,CAAAhL,IAAA;YAAA;cACA6K,MAAA,CAAApG,UAAA,GAAAgE,GAAA;cACAoC,MAAA,CAAAF,eAAA;cACA,IAAAlC,GAAA,CAAAnE,UAAA;gBACAuG,MAAA,CAAAvG,UAAA,GAAAmE,GAAA,CAAAnE,UAAA;cACA;cACA,IAAAmE,GAAA,CAAAlE,WAAA;gBACAsG,MAAA,CAAAtG,WAAA,GAAAkE,GAAA,CAAAlE,WAAA;cACA;cACA,IAAAkE,GAAA,CAAAjE,KAAA;gBACAqG,MAAA,CAAArG,KAAA,GAAAiE,GAAA,CAAAjE,KAAA;cACA;cACAqG,MAAA,CAAAtH,IAAA;YAAA;YAAA;cAAA,OAAAyH,SAAA,CAAAxI,IAAA;UAAA;QAAA,GAAAsI,QAAA;MAAA;IACA;IACAG,YAAA,WAAAA,aAAAC,YAAA;MACA,IAAA5C,GAAA,QAAAxE,mBAAA,CAAAzC,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAF,KAAA,KAAA8J,YAAA;MAAA;MACA,IAAA5C,GAAA,IAAAA,GAAA;QACA,OAAAA,GAAA,IAAA5F,KAAA;MACA;IACA;IACAyI,UAAA,WAAAA,WAAAC,QAAA,EAAAjE,EAAA;MAAA,IAAAkE,UAAA,OAAA/K,2BAAA,CAAA1B,OAAA,EACA,KAAAJ,YAAA;QAAA8M,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAA5J,CAAA,MAAA6J,MAAA,GAAAD,UAAA,CAAA3J,CAAA,IAAAC,IAAA;UAAA,IAAAnB,IAAA,GAAA8K,MAAA,CAAAlK,KAAA;UACA,IAAAZ,IAAA,CAAA+K,GAAA,KAAApE,EAAA,IAAA3G,IAAA,CAAA2G,EAAA,KAAAA,EAAA;YACA3G,IAAA,CAAAgE,KAAA,GAAA4G,QAAA;UACA;QACA;MAAA,SAAArJ,GAAA;QAAAsJ,UAAA,CAAArJ,CAAA,CAAAD,GAAA;MAAA;QAAAsJ,UAAA,CAAApJ,CAAA;MAAA;IACA;IACAuJ,SAAA,WAAAA,UAAA;MAAA,IAAAC,UAAA,OAAAnL,2BAAA,CAAA1B,OAAA,EACA,KAAAgF,cAAA;QAAA8H,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAhK,CAAA,MAAAiK,MAAA,GAAAD,UAAA,CAAA/J,CAAA,IAAAC,IAAA;UAAA,IAAAnB,IAAA,GAAAkL,MAAA,CAAAtK,KAAA;UACA,IAAAoI,CAAA;YACA0B,YAAA;YACAhB,MAAA;YACAyB,IAAA;YACAC,MAAA,EAAApL,IAAA,CAAAqL,YAAA;YACAhH,OAAA,EAAArE,IAAA,CAAAqE,OAAA;YACA/G,IAAA,EAAA0C,IAAA,CAAAsL,YAAA;YACAC,IAAA,EAAAvL,IAAA,CAAAwL,QAAA;YACA3N,IAAA;YACA+I,OAAA;YACAC,SAAA;YACA4E,YAAA;YACAzK,MAAA,EAAAhB,IAAA,CAAAgB,MAAA;YACA0K,KAAA,EAAA1L,IAAA,CAAA0L,KAAA;YACAC,MAAA,EAAA3L,IAAA,CAAA2L,MAAA;YACAC,KAAA;YACAC,MAAA;YACAC,IAAA;YACAC,MAAA;YACAC,IAAA;YACAhI,KAAA;YACAF,UAAA;YACAC,WAAA;YACAkI,UAAA;YACA/N,SAAA,OAAAA;UACA;UACA,KAAAF,YAAA,CAAAsD,IAAA,CAAA0H,CAAA;QACA;MAAA,SAAAzH,GAAA;QAAA0J,UAAA,CAAAzJ,CAAA,CAAAD,GAAA;MAAA;QAAA0J,UAAA,CAAAxJ,CAAA;MAAA;MACA,KAAAqB,MAAA;IACA;IACAoJ,QAAA,WAAAA,SAAAC,OAAA;MACA,KAAA/I,cAAA,GAAA+I,OAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA,KAAAhJ,cAAA;MACA,KAAAN,MAAA;IACA;IACAuJ,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MAAA,WAAA3N,kBAAA,CAAAP,OAAA,mBAAAQ,oBAAA,CAAAR,OAAA,IAAAS,IAAA,UAAA0N,SAAA;QAAA,IAAAC,GAAA,EAAA1E,GAAA,EAAA2E,UAAA,EAAAC,MAAA,EAAA1M,IAAA,EAAAgJ,CAAA;QAAA,WAAApK,oBAAA,CAAAR,OAAA,IAAAgB,IAAA,UAAAuN,UAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAArN,IAAA,GAAAqN,UAAA,CAAApN,IAAA;YAAA;cAAAoN,UAAA,CAAApN,IAAA;cAAA,OACA8M,MAAA,CAAA7D,SAAA;YAAA;cACA+D,GAAA,GAAAF,MAAA,CAAA7E,KAAA,CAAAtE,OAAA,CAAA0J,eAAA,GAAA/K,GAAA,WAAAhB,CAAA;gBAAA,OAAAA,CAAA,CAAA6F,EAAA;cAAA;cACAmB,GAAA,GAAAwE,MAAA,CAAApJ,OAAA,CAAArC,MAAA,WAAAC,CAAA;gBAAA,OAAA0L,GAAA,CAAApL,QAAA,CAAAN,CAAA,CAAA6F,EAAA;cAAA;cACA,IAAAmB,GAAA,IAAAA,GAAA;gBAAA2E,UAAA,OAAA3M,2BAAA,CAAA1B,OAAA,EACA0J,GAAA;gBAAA;kBAAA,KAAA2E,UAAA,CAAAxL,CAAA,MAAAyL,MAAA,GAAAD,UAAA,CAAAvL,CAAA,IAAAC,IAAA;oBAAAnB,IAAA,GAAA0M,MAAA,CAAA9L,KAAA;oBACAoI,CAAA;sBACA0B,YAAA;sBACAhB,MAAA;sBACAyB,IAAA;sBACAC,MAAA;sBACA/G,OAAA,EAAArE,IAAA,CAAA8M,KAAA;sBACAxP,IAAA,EAAA0C,IAAA,CAAA+M,KAAA;sBACArI,KAAA,EAAA1E,IAAA,CAAA0E,KAAA;sBACAkF,KAAA,EAAA5J,IAAA,CAAA4J,KAAA;sBACA2B,IAAA;sBACA1N,IAAA;sBACA+I,OAAA;sBACAC,SAAA;sBACA4E,YAAA;sBACAzK,MAAA;sBACA0K,KAAA;sBACAC,MAAA;sBACAC,KAAA;sBACAC,MAAA;sBACAC,IAAA;sBACAC,MAAA;sBACAC,IAAA;sBACAhI,KAAA;sBACAF,UAAA;sBACAC,WAAA;sBACAkI,UAAA;sBACA/N,SAAA,EAAAoO,MAAA,CAAApO;oBACA;oBACAoO,MAAA,CAAAtO,YAAA,CAAAsD,IAAA,CAAA0H,CAAA;kBACA;gBAAA,SAAAzH,GAAA;kBAAAkL,UAAA,CAAAjL,CAAA,CAAAD,GAAA;gBAAA;kBAAAkL,UAAA,CAAAhL,CAAA;gBAAA;cACA;cACA6K,MAAA,CAAAzJ,OAAA;YAAA;YAAA;cAAA,OAAA+J,UAAA,CAAA5K,IAAA;UAAA;QAAA,GAAAuK,QAAA;MAAA;IACA;IACAS,SAAA,WAAAA,UAAAC,IAAA,EAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,OAAAF,IAAA,CAAApM,MAAA,WAAAb,IAAA;QACA,IAAAkN,KAAA;UACA,IAAAlN,IAAA,CAAAkN,KAAA,KAAAA,KAAA;YACA,IAAAE,QAAA,GAAAD,MAAA,CAAAH,SAAA,CAAAC,IAAA,EAAAjN,IAAA,CAAA8M,KAAA;YACA,IAAAM,QAAA,IAAAA,QAAA,CAAApM,MAAA;cACAhB,IAAA,CAAAoN,QAAA,GAAAA,QAAA;YACA;YACA;UACA;QACA;UACA,KAAAC,SAAA,YAAAjM,QAAA,CAAApB,IAAA,CAAAkN,KAAA;YACA,IAAAE,SAAA,GAAAD,MAAA,CAAAH,SAAA,CAAAC,IAAA,EAAAjN,IAAA,CAAA8M,KAAA;YACA,IAAAM,SAAA,IAAAA,SAAA,CAAApM,MAAA;cACAhB,IAAA,CAAAoN,QAAA,GAAAA,SAAA;YACA;YACA;UACA;QACA;QACA;MACA;IACA;IACAE,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MAAA,WAAA5O,kBAAA,CAAAP,OAAA,mBAAAQ,oBAAA,CAAAR,OAAA,IAAAS,IAAA,UAAA2O,SAAA;QAAA,IAAAC,KAAA,EAAAC,MAAA,EAAAnL,IAAA,EAAA3E,IAAA,EAAAmP,KAAA,EAAAY,OAAA,EAAAxK,OAAA;QAAA,WAAAvE,oBAAA,CAAAR,OAAA,IAAAgB,IAAA,UAAAwO,UAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAtO,IAAA,GAAAsO,UAAA,CAAArO,IAAA;YAAA;cACAiO,KAAA,GAAAF,OAAA,CAAAlJ,OAAA;cAAA,KACAoJ,KAAA;gBAAAI,UAAA,CAAArO,IAAA;gBAAA;cAAA;cACA+N,OAAA,CAAA3K,UAAA;cACA2K,OAAA,CAAA5K,OAAA;cAAAkL,UAAA,CAAArO,IAAA;cAAA,OACA,IAAAsO,sBAAA,EAAAL,KAAA;YAAA;cAAAC,MAAA,GAAAG,UAAA,CAAAhO,IAAA;cAAA,MACA6N,MAAA,IAAAA,MAAA,CAAAnL,IAAA;gBAAAsL,UAAA,CAAArO,IAAA;gBAAA;cAAA;cACA+C,IAAA,GAAAmL,MAAA,CAAAnL,IAAA;cACA3E,IAAA,GAAA2P,OAAA,CAAA3P,IAAA;cACAmP,KAAA,GAAAxK,IAAA,CAAAwL,KAAA;cAAA,KACAhB,KAAA;gBAAAc,UAAA,CAAArO,IAAA;gBAAA;cAAA;cACA5B,IAAA,CAAAoQ,KAAA,GAAAzL,IAAA,CAAA0L,KAAA;cACArQ,IAAA,CAAAmP,KAAA,GAAAA,KAAA;cACAnP,IAAA,CAAA8G,KAAA,GAAAnC,IAAA,CAAA2L,KAAA;cAAAL,UAAA,CAAArO,IAAA;cAAA,OAEA,IAAA2O,oBAAA;gBAAAV,KAAA,EAAAA;cAAA;YAAA;cAAAE,OAAA,GAAAE,UAAA,CAAAhO,IAAA;cACA8N,OAAA,GAAAA,OAAA,CAAA9M,MAAA,WAAAC,CAAA;gBAAA,OAAAA,CAAA,CAAA4D,KAAA;cAAA;cACAiJ,OAAA,CAAArM,IAAA;gBACAqF,EAAA,EAAA8G,KAAA;gBACAV,KAAA,EAAAA,KAAA;gBACAD,KAAA,EAAAW,KAAA;gBACA/I,KAAA,EAAAnC,IAAA,CAAA2L,KAAA;gBACAtE,KAAA,EAAArH,IAAA,CAAAqH;cACA;cACAzG,OAAA,GAAAoK,OAAA,CAAAP,SAAA,CAAAoB,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAX,OAAA,IAAAN,SAAA;cACAE,OAAA,CAAApK,OAAA,GAAAA,OAAA;cACAoK,OAAA,CAAArK,OAAA,GAAAyK,OAAA;cAAAE,UAAA,CAAArO,IAAA;cAAA;YAAA;cAEA+N,OAAA,CAAA5F,QAAA;cACA/J,IAAA,CAAAoQ,KAAA;cACApQ,IAAA,CAAAmP,KAAA;YAAA;cAAAc,UAAA,CAAArO,IAAA;cAAA;YAAA;cAGA+N,OAAA,CAAA5F,QAAA;YAAA;cAEA4F,OAAA,CAAA3K,UAAA;cACA2K,OAAA,CAAA5K,OAAA;YAAA;YAAA;cAAA,OAAAkL,UAAA,CAAA7L,IAAA;UAAA;QAAA,GAAAwL,QAAA;MAAA;IAEA;IACAe,OAAA,WAAAA,QAAA;MAAA,IAAAC,OAAA;MAAA,WAAA7P,kBAAA,CAAAP,OAAA,mBAAAQ,oBAAA,CAAAR,OAAA,IAAAS,IAAA,UAAA4P,UAAA;QAAA,WAAA7P,oBAAA,CAAAR,OAAA,IAAAgB,IAAA,UAAAsP,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAApP,IAAA,GAAAoP,UAAA,CAAAnP,IAAA;YAAA;cACAgP,OAAA,CAAA3L,OAAA;YAAA;YAAA;cAAA,OAAA8L,UAAA,CAAA3M,IAAA;UAAA;QAAA,GAAAyM,SAAA;MAAA;IACA;IACAG,OAAA,WAAAA,QAAA;MACA,IAAA5F,CAAA;QACA6F,GAAA,OAAAC,OAAA;QACApE,YAAA;QACAhB,MAAA;QACA7L,IAAA;QACAsN,IAAA;QACAC,MAAA;QACA/G,OAAA;QACA/G,IAAA;QACAiO,IAAA;QACA3E,OAAA;QACAC,SAAA;QACA4E,YAAA;QACAzK,MAAA;QACA0K,KAAA;QACAC,MAAA;QACAC,KAAA;QACAC,MAAA;QACAC,IAAA;QACAC,MAAA;QACAC,IAAA;QACAhI,KAAA;QACAF,UAAA;QACAC,WAAA;QACAkI,UAAA;QACA/N,SAAA,OAAAA;MACA;MACA,KAAAF,YAAA,CAAAsD,IAAA,CAAA0H,CAAA;IACA;IACA+F,OAAA,WAAAA,QAAA9G,GAAA;MAAA,IAAA+G,OAAA;MAAA,WAAArQ,kBAAA,CAAAP,OAAA,mBAAAQ,oBAAA,CAAAR,OAAA,IAAAS,IAAA,UAAAoQ,UAAA;QAAA,IAAAC,KAAA;QAAA,WAAAtQ,oBAAA,CAAAR,OAAA,IAAAgB,IAAA,UAAA+P,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA7P,IAAA,GAAA6P,UAAA,CAAA5P,IAAA;YAAA;cAEA,IAAAyI,GAAA,CAAAtB,EAAA;gBACAuI,KAAA,GAAAF,OAAA,CAAAhR,YAAA,CAAAqR,SAAA,WAAAvO,CAAA;kBAAA,OAAAA,CAAA,CAAA6F,EAAA,KAAAsB,GAAA,CAAAtB,EAAA;gBAAA;cACA,WAAAsB,GAAA,CAAA4G,GAAA;gBACAK,KAAA,GAAAF,OAAA,CAAAhR,YAAA,CAAAqR,SAAA,WAAAvO,CAAA;kBAAA,OAAAA,CAAA,CAAA+N,GAAA,KAAA5G,GAAA,CAAA4G,GAAA;gBAAA;cACA;cACAG,OAAA,CAAAhR,YAAA,CAAAsR,MAAA,CAAAJ,KAAA;cAAA,KACAjH,GAAA,CAAAtB,EAAA;gBAAAyI,UAAA,CAAA5P,IAAA;gBAAA;cAAA;cAAA4P,UAAA,CAAA5P,IAAA;cAAA,OACA,IAAA+P,SAAA,EAAAtH,GAAA,CAAAtB,EAAA;YAAA;YAAA;cAAA,OAAAyI,UAAA,CAAApN,IAAA;UAAA;QAAA,GAAAiN,SAAA;MAAA;IAEA;IACAO,OAAA,WAAAA,QAAA7N,YAAA,EAAAI,WAAA;MACA,OAAAJ,YAAA,CAAAP,QAAA,CAAAW,WAAA;IACA;EACA;AACA", "ignoreList": []}]}