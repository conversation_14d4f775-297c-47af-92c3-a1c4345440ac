import request from '@/utils/request'

// 查询考勤记录列表
export function listAttendanceLog(query) {
  return request({
    url: '/hr/attendanceLog/list',
    method: 'get',
    params: query
  })
}
// 查询考勤记录详细
export function getAttendanceLog(id) {
  return request({
    url: '/hr/attendanceLog/' + id,
    method: 'get'
  })
}
// 导出考勤记录
export function exportAttendanceLog(query) {
  return request({
    url: '/hr/attendanceLog/export',
    method: 'get',
    params: query
  })
}

export function addAttendanceLog(data) {
  return request({
    url: '/hr/attendanceLog',
    method: 'post',
    data: data
  })
}

export function updateAttendanceLog(ids) {
  return request({
    url: '/hr/attendanceLog/'+ids,
    method: 'post'
  })
}

export function allAttendanceLog(data) {
  return request({
    url: '/hr/attendanceLog/all',
    method: 'post',
    data,
  })
}
