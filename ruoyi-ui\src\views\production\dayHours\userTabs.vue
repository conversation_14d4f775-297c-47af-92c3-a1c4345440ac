<template>
  <div>
    <el-tabs v-model="currentTab" >
      <el-tab-pane v-for="tab in tabOptions" :key="tab.value" :name="tab.value" :label="tab.label" >

        <DayHoursUserTable
          :day-hours="dayHours"
          :mes-hours-list="mesHoursList"
          :user-array="dayUserList.filter(i=>i.userType === tab.value)"
          :sum-hours="getSumHours(tab.value)"
          @sailingsChange="sailingsChange"
          @computeItemData="computeItemData"
        />

        <template v-if="tab.value === 'user'" >
          <DayHoursBaseTable
            title="称量"
            :attendance-log-list="attendanceLogList"
            :rest-list="restList"
            :user-list="userList"
            :user-array="weightMinutesList"
            :day-hours="dayHours"
            :sailings="sailings"
            @computeItemData="computeItemData('weight')"
          />

          <DayHoursBaseTable
            title="间接"
            :attendance-log-list="attendanceLogList"
            :rest-list="restList"
            :user-list="userList"
            :user-array="otherMinutesList"
            :day-hours="dayHours"
            :sailings="sailings"
            @computeItemData="computeItemData('other')"
          />

          <DayHoursBaseTable
            title="管理"
            :attendance-log-list="attendanceLogList"
            :rest-list="restList"
            :user-list="userList"
            :user-array="manageMinutesList"
            :day-hours="dayHours"
            :sailings="sailings"
            @computeItemData="computeItemData('manage')"
          />

          <DayHoursBaseTable
            title="质检"
            :attendance-log-list="attendanceLogList"
            :rest-list="restList"
            :user-list="userList"
            :user-array="qcMinutesList"
            :day-hours="dayHours"
            :sailings="sailings"
            @computeItemData="computeItemData('qc')"
          />

        </template>

      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script >
import DayHoursUserTable from "@/views/production/dayHours/userTable.vue";
import DayHoursBaseTable from "@/views/production/dayHours/baseTable.vue";

export default {
  name: 'dayHoursUserTabs',
  components: {DayHoursBaseTable, DayHoursUserTable},
  props: {
    sailings: {
      type: String,
      required: true,
    },
    dayUserList: {
      type: Array,
      required: true,
    },
    weightMinutesList: {
      type: Array,
      required: true,
    },
    otherMinutesList: {
      type: Array,
      required: true,
    },
    manageMinutesList: {
      type: Array,
      required: true,
    },
    qcMinutesList: {
      type: Array,
      required: true,
    },
    attendanceLogList: {
      type: Array,
      required: true,
    },
    mesHoursList: {
      type: Array,
      required: true,
    },
    dayHours: {
      type: Object,
      required: true,
    },
    userList: {
      type: Array,
      required: true,
    },
    restList: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      currentTab: 'user',
      tabOptions: [
        {label: '正式工',value: 'user'},
        {label: '劳务工',value: 'labor'},
        {label: '包干工',value: 'outer'},
      ],
    }
  },
  async created() {
  },
  methods: {
    sailingsChange(userCode) {
      this.$emit('sailingsChange',userCode)
    },
    computeItemData(type) {
      if(type) {
        this.$emit('computeItemData',type)
      } else {
        this.$emit('computeItemData')
      }
    },
    getSumHours(userType) {
      if(userType === 'user') {
        return this.dayHours.userHours
      } else if(userType === 'labor') {
        return this.dayHours.laborHours
      } else if(userType === 'outer') {
        return this.dayHours.outerHours
      }
    }
  },
}
</script>
<style scoped lang="scss">
::v-deep .el-divider {
  .el-divider__text {
    font-size: 18px;
    font-weight: 650;
  }
}
</style>
