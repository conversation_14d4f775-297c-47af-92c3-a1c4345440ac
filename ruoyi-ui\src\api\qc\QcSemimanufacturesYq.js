import request from '@/utils/request'

// 查询半成品检测项目-仪器设置列表
export function listQcSemimanufacturesYq(query) {
  return request({
    url: '/qc/QcSemimanufacturesYq/list',
    method: 'get',
    params: query
  })
}

// 查询半成品检测项目-仪器设置详细
export function getQcSemimanufacturesYq(id) {
  return request({
    url: '/qc/QcSemimanufacturesYq/' + id,
    method: 'get'
  })
}

// 新增半成品检测项目-仪器设置
export function addQcSemimanufacturesYq(data) {
  return request({
    url: '/qc/QcSemimanufacturesYq',
    method: 'post',
    data: data
  })
}

// 修改半成品检测项目-仪器设置
export function updateQcSemimanufacturesYq(data) {
  return request({
    url: '/qc/QcSemimanufacturesYq',
    method: 'put',
    data: data
  })
}

// 删除半成品检测项目-仪器设置
export function delQcSemimanufacturesYq(id) {
  return request({
    url: '/qc/QcSemimanufacturesYq/' + id,
    method: 'delete'
  })
}

// 导出半成品检测项目-仪器设置
export function exportQcSemimanufacturesYq(query) {
  return request({
    url: '/qc/QcSemimanufacturesYq/export',
    method: 'get',
    params: query
  })
}

export function confirmYqSetting(data) {
  return request({
    url: '/qc/QcSemimanufacturesYq/confirmYqSetting',
    method: 'get',
    params: data
  })
}

export function listBcpXmYqAll(query) {
  return request({
    url: '/qc/QcSemimanufacturesYq/all',
    method: 'get',
    params: query
  })
}
