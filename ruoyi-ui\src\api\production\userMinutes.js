import request from '@/utils/request'

// 查询产线工时明细列表
export function listUserMinutes(query) {
  return request({
    url: '/production/userMinutes/list',
    method: 'get',
    params: query
  })
}

// 查询产线工时明细详细
export function getUserMinutes(id) {
  return request({
    url: '/production/userMinutes/' + id,
    method: 'get'
  })
}

// 新增产线工时明细
export function addUserMinutes(data) {
  return request({
    url: '/production/userMinutes',
    method: 'post',
    data: data
  })
}

// 修改产线工时明细
export function updateUserMinutes(data) {
  return request({
    url: '/production/userMinutes',
    method: 'put',
    data: data
  })
}

// 删除产线工时明细
export function delUserMinutes(id) {
  return request({
    url: '/production/userMinutes/' + id,
    method: 'delete'
  })
}

// 导出产线工时明细
export function exportUserMinutes(query) {
  return request({
    url: '/production/userMinutes/export',
    method: 'get',
    params: query
  })
}

export function allUserMinutes(query) {
  return request({
    url: '/production/userMinutes/all',
    method: 'get',
    params: query
  })
}
