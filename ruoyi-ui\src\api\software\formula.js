import request from '@/utils/request'

//查询原料列表
export function formulaList(query) {
  return request({
    url: '/formula/list',
    method: 'get',
    params: query
  })
}


export function exportFormulaMaterial(query) {
  return request({
    url: '/formula/exportFormulaMaterial',
    method: 'get',
    params: query
  })
}

//研发释放编号
export function formulaBeianList(query) {
  return request({
    url: '/formula/formulaBeianList',
    method: 'get',
    params: query
  })
}
//研发释放编号
export function formulaBeianListNew(query) {
  return request({
    url: '/formula/formulaBeianListNew',
    method: 'get',
    params: query
  })
}

//研发释放编号
export function formulaWenanBoxList(query) {
  return request({
    url: '/formula/formulaWenanBoxList',
    method: 'get',
    params: query
  })
}

//查询原料列表
export function formulaMaterialPriceList(query) {
  return request({
    url: '/formula/formulaMaterialPricelist',
    method: 'get',
    params: query
  })
}

//查询原料列表
export function formulaMaterialPriceNewList(query) {
  return request({
    url: '/formula/formulaMaterialPriceNewList',
    method: 'get',
    params: query
  })
}


//查询原料供应商信息
export function formulaMaterialSupplierList(query) {
  return request({
    url: '/formula/formulaMaterialSupplierList',
    method: 'get',
    params: query
  })
}

//查询原料供应商信息
export function queryPackageMaterialSupplierList(query) {
  return request({
    url: '/formula/queryPackageMaterialSupplierList',
    method: 'get',
    params: query
  })
}


// 查询配方详细
export function getFormula(id) {
  return request({
    url: '/formula/' + id,
    method: 'get'
  })
}

// 新增原料
export function addFormula(data) {
  return request({
    url: '/formula',
    method: 'post',
    data: data
  })
}

// 修改原料
export function updateFormula(data) {
  return request({
    url: '/formula',
    method: 'put',
    data: data
  })
}

export function allFormula(query) {
  return request({
    url: '/formula/all',
    method: 'get',
    params: query
  })
}

export function allByLaboratoryCode(laboratoryCode) {
  return request({
    url: '/formula/allByLaboratoryCode/' + laboratoryCode,
    method: 'get',
  })
}
