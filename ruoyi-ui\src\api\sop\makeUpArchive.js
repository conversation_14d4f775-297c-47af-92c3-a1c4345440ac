import request from '@/utils/request'

// 查询配制工艺存档列表
export function listMakeUpArchive(query) {
  return request({
    url: '/sop/makeUpArchive/list',
    method: 'get',
    params: query
  })
}

// 查询配制工艺存档详细
export function getMakeUpArchive(id) {
  return request({
    url: '/sop/makeUpArchive/' + id,
    method: 'get'
  })
}

// 新增配制工艺存档
export function addMakeUpArchive(data) {
  return request({
    url: '/sop/makeUpArchive',
    method: 'post',
    data: data
  })
}

// 修改配制工艺存档
export function updateMakeUpArchive(data) {
  return request({
    url: '/sop/makeUpArchive',
    method: 'put',
    data: data
  })
}

// 删除配制工艺存档
export function delMakeUpArchive(id) {
  return request({
    url: '/sop/makeUpArchive/' + id,
    method: 'delete'
  })
}

// 导出配制工艺存档
export function exportMakeUpArchive(query) {
  return request({
    url: '/sop/makeUpArchive/export',
    method: 'get',
    params: query
  })
}

export function submitAudit(data) {
  return request({
    url: '/sop/makeUpArchive/submitAudit',
    method: 'put',
    data: data
  })
}

//撤销申请
export function cancelAudit(data) {
  return request({
    url: '/sop/makeUpArchive/cancelAudit',
    method: 'put',
    data: data
  })
}

export function getMakeUpByErpCode(erpCode) {
  return request({
    url: '/sop/makeUpArchive/getByErpCode/' + erpCode,
    method: 'get'
  })
}
