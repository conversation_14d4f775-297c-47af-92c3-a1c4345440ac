import request from '@/utils/request'

// 查询到账账户信息列表
export function listAccountInfo(query) {
  return request({
    url: '/finance/accountInfo/list',
    method: 'get',
    params: query
  })
}

// 查询到账账户信息详细
export function getAccountInfo(id) {
  return request({
    url: '/finance/accountInfo/' + id,
    method: 'get'
  })
}

// 新增到账账户信息
export function addAccountInfo(data) {
  return request({
    url: '/finance/accountInfo',
    method: 'post',
    data: data
  })
}

// 修改到账账户信息
export function updateAccountInfo(data) {
  return request({
    url: '/finance/accountInfo',
    method: 'put',
    data: data
  })
}

// 删除到账账户信息
export function delAccountInfo(id) {
  return request({
    url: '/finance/accountInfo/' + id,
    method: 'delete'
  })
}

// 导出到账账户信息
export function exportAccountInfo(query) {
  return request({
    url: '/finance/accountInfo/export',
    method: 'get',
    params: query
  })
}