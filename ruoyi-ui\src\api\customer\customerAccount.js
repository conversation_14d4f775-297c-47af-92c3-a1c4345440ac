import request from '@/utils/request'

// 查询客户账户列表
export function listCustomerAccount(query) {
  return request({
    url: '/customer/customerAccount/list',
    method: 'get',
    params: query
  })
}

// 查询客户账户详细
export function getCustomerAccount(id) {
  return request({
    url: '/customer/customerAccount/' + id,
    method: 'get'
  })
}

// 新增客户账户
export function addCustomerAccount(data) {
  return request({
    url: '/customer/customerAccount',
    method: 'post',
    data: data
  })
}

// 修改客户账户
export function updateCustomerAccount(data) {
  return request({
    url: '/customer/customerAccount',
    method: 'put',
    data: data
  })
}

// 删除客户账户
export function delCustomerAccount(id) {
  return request({
    url: '/customer/customerAccount/' + id,
    method: 'delete'
  })
}

// 导出客户账户
export function exportCustomerAccount(query) {
  return request({
    url: '/customer/customerAccount/export',
    method: 'get',
    params: query
  })
}