import request from '@/utils/request'

// 查询客户访问登记列表
export function listVisit(query) {
  return request({
    url: '/customer/visit/list',
    method: 'get',
    params: query
  })
}

// 查询客户访问登记详细
export function getVisit(id) {
  return request({
    url: '/customer/visit/' + id,
    method: 'get'
  })
}

// 新增客户访问登记
export function addVisit(data) {
  return request({
    url: '/customer/visit',
    method: 'post',
    data: data
  })
}

// 修改客户访问登记
export function updateVisit(data) {
  return request({
    url: '/customer/visit',
    method: 'put',
    data: data
  })
}

// 删除客户访问登记
export function delVisit(id) {
  return request({
    url: '/customer/visit/' + id,
    method: 'delete'
  })
}

// 导出客户访问登记
export function exportVisit(query) {
  return request({
    url: '/customer/visit/export',
    method: 'get',
    params: query
  })
}

//提交审核
export function submitAudit(data) {
  return request({
    url: '/customer/visit/submitAudit',
    method: 'put',
    data: data
  })
}

//撤销申请
export function cancelAudit(data) {
  return request({
    url: '/customer/visit/cancelAudit',
    method: 'put',
    data: data
  })
}

export function exemptCustomerVisitAudit(instanceId) {
  return request({
    url: '/customer/visit/exemptAudit/' + instanceId,
    method: 'put',
  })
}
