{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\project\\project\\makeBom.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\project\\project\\makeBom.vue", "mtime": 1753954679647}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\babel.config.js", "mtime": 1743382537964}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_bc", "require", "_bcSelectTable", "_interopRequireDefault", "_bom<PERSON><PERSON>s", "_productItem", "name", "components", "ProjectBomCharts", "BcSelectTable", "props", "projectId", "type", "Number", "default", "productName", "String", "bomTree", "Array", "required", "projectProductItemId", "erpCode", "readonly", "Boolean", "materialList", "data", "btnLoading", "open", "bom<PERSON>pen", "fullscreenFlag", "mb005Options", "label", "value", "currentRow", "mb008Options", "created", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "stop", "methods", "bc<PERSON>ields", "projectBcId", "f", "arr", "filter", "i", "id", "mb005Text", "mb005", "rowStyle", "scope", "row", "pid", "backgroundColor", "mb008Text", "mb008", "text", "includes", "submitForm", "_this", "_callee2", "tempArray", "bomArray", "_iterator", "_step", "item", "params", "_callee2$", "_context2", "translateTreeToData", "_createForOfIteratorHelper2", "s", "n", "done", "push", "_objectSpread2", "children", "mb002", "msgError", "abrupt", "md006", "md007", "t0", "e", "finish", "toTree", "undefined", "JSON", "stringify", "bom<PERSON>ame", "updateProductItem", "msgSuccess", "$emit", "t1", "initChart", "_this2", "_callee3", "_callee3$", "_context3", "$nextTick", "$refs", "projectBomCharts", "init", "emptyTree", "_this3", "_callee4", "_callee4$", "_context4", "$confirm", "splice", "length", "initLp", "_this4", "_callee5", "array", "mb008A<PERSON>y", "_iterator2", "_step2", "_callee5$", "_context5", "$nanoid", "md017", "mb003", "consumption", "mc004", "md008", "map", "err", "initCp", "_this5", "_callee6", "_iterator3", "_step3", "_callee6$", "_context6", "sent", "initBom", "_this6", "_callee7", "o", "_callee7$", "_context7", "nameStyle", "color", "checkBc", "bcArray", "sort", "pre", "curr", "_iterator4", "_step4", "showBcDialog", "parentName", "_iterator5", "_step5", "delItem", "removeItem", "_iterator6", "_step6", "b", "parentArray", "index", "findIndex", "addItem", "parentNode"], "sources": ["src/views/project/project/makeBom.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-table\r\n      :data=\"bomTree\"\r\n      :tree-props=\"{ children: 'children' }\"\r\n      default-expand-all\r\n      row-key=\"id\"\r\n      size=\"mini\"\r\n      :row-style=\"rowStyle\"\r\n    >\r\n      <el-table-column align=\"center\" width=\"120\" >\r\n        <template #header >上级</template>\r\n        <template v-slot=\"scope\" >\r\n<!--          {{parentName(bomTree,scope.row.pid)}}-->\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"mb002\" >\r\n        <template #header >品名<span style=\"color: #F56C6C\">*</span> </template>\r\n        <template v-slot=\"scope\" >\r\n          <div :class=\"readonly?'mask':''\" style=\"display: flex;align-items: center\" >\r\n            <span v-if=\"scope.row.mb005 !== '101'\">{{scope.row.mb002}}</span>\r\n            <el-input v-else v-model.trim=\"scope.row.mb002\" size=\"mini\" />\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"md003\" width=\"120\" >\r\n        <template #header >\r\n          品号\r\n          <el-tooltip content=\"成品是在bom新建的时候推送过来的,其他类型是物料总表关联过来的\" >\r\n            <i class=\"el-icon-question\" />\r\n          </el-tooltip>\r\n        </template>\r\n        <template v-slot=\"scope\" >\r\n          <span v-if=\"scope.row.mb005 === '101'\" >{{erpCode}}</span>\r\n          <span v-else >{{bcFields(scope.row.projectBcId,'erpCode')}}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" label=\"类别\" prop=\"mb005\"  width=\"100\" >\r\n        <template #header >类别 <span style=\"color: #F56C6C\">*</span> </template>\r\n        <template v-slot=\"scope\" >\r\n          <div :class=\"readonly?'mask':''\" >\r\n<!--            <el-select v-model=\"scope.row.mb005\" size=\"mini\" >-->\r\n<!--              <el-option v-for=\"item in mb005Options\"-->\r\n<!--                         :key=\"item.value\"-->\r\n<!--                         :label=\"item.label\"-->\r\n<!--                         :value=\"item.value\" />-->\r\n<!--            </el-select>-->\r\n            {{mb005Text(scope.row.mb005)}}\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n<!--      <el-table-column align=\"center\" label=\"物料类型\"  prop=\"md017\"  width=\"100\" >-->\r\n<!--        <template v-slot=\"scope\" >-->\r\n<!--          <el-input v-model=\"scope.row.md017\" size=\"mini\" />-->\r\n<!--        </template>-->\r\n<!--      </el-table-column>-->\r\n<!--      <el-table-column align=\"center\" label=\"物料规格\"  prop=\"mb003\"  width=\"100\" >-->\r\n<!--        <template v-slot=\"scope\" >-->\r\n<!--          <el-input v-model=\"scope.row.mb003\" size=\"mini\" />-->\r\n<!--        </template>-->\r\n<!--      </el-table-column>-->\r\n      <el-table-column align=\"center\" label=\"组成用量/底数\" prop=\"md006\" width=\"200\" >\r\n        <template #header >组成用量/底数 <span style=\"color: #F56C6C\">*</span> </template>\r\n        <template v-slot=\"scope\" >\r\n          <div :class=\"readonly?'mask':''\" style=\"display: flex;align-items: center\" >\r\n            <el-input v-model=\"scope.row.md006\" size=\"mini\" style=\"width: 80px\" type=\"number\"/>\r\n            /\r\n            <el-input v-model=\"scope.row.md007\" size=\"mini\" style=\"width: 80px\" type=\"number\" />\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n<!--      <el-table-column align=\"center\" label=\"用量\" prop=\"consumption\"  width=\"100\" >-->\r\n<!--        <template v-slot=\"scope\" >-->\r\n<!--          <el-input v-model=\"scope.row.consumption\" size=\"mini\" />-->\r\n<!--        </template>-->\r\n<!--      </el-table-column>-->\r\n<!--      <el-table-column align=\"center\" label=\"标准批量\" prop=\"mc004\"  width=\"100\" >-->\r\n<!--        <template v-slot=\"scope\" >-->\r\n<!--          <el-input v-model=\"scope.row.mc004\" size=\"mini\" />-->\r\n<!--        </template>-->\r\n<!--      </el-table-column>-->\r\n<!--      <el-table-column align=\"center\" label=\"损耗\" prop=\"md008\"  width=\"100\" >-->\r\n<!--        <template v-slot=\"scope\" >-->\r\n<!--          <el-input v-model=\"scope.row.md008\" size=\"mini\" />-->\r\n<!--        </template>-->\r\n<!--      </el-table-column>-->\r\n      <el-table-column align=\"center\" prop=\"mb008\" width=\"100\" >\r\n        <template #header >\r\n          物料属性\r\n          <el-tooltip content=\"此处用于项目总表中的 主包材属性、辅包材属性\" >\r\n            <i class=\"el-icon-question\" />\r\n          </el-tooltip>\r\n        </template>\r\n        <template v-slot=\"scope\" >\r\n          <el-select v-model=\"scope.row.mb008\" size=\"mini\" >\r\n            <el-option\r\n              v-for=\"d in mb008Options\"\r\n              :key=\"d.value\"\r\n              :label=\"d.label\"\r\n              :value=\"d.value\"\r\n            />\r\n          </el-select>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" label=\"操作\"  width=\"60\" >\r\n        <template #header >\r\n          <div style=\"display: flex;align-items: center\" >\r\n            <el-tooltip v-if=\"!bomTree.length && !readonly\" content=\"添加成品\" >\r\n              <i class=\"el-icon-plus\" @click=\"addItem()\" />\r\n            </el-tooltip>\r\n            <el-tooltip v-if=\"!readonly\" content=\"生成默认bom\" >\r\n              <i class=\"el-icon-news\" @click=\"initBom()\" />\r\n            </el-tooltip>\r\n            <el-tooltip content=\"树图\" >\r\n              <i class=\"el-icon-zoom-in\" @click=\"initChart()\" />\r\n            </el-tooltip>\r\n            <el-tooltip v-if=\"bomTree.length && !readonly\" content=\"清空\" >\r\n              <i class=\"el-icon-circle-close\" @click=\"emptyTree()\" />\r\n            </el-tooltip>\r\n          </div>\r\n        </template>\r\n        <template v-if=\"!readonly\" v-slot=\"scope\" >\r\n          <div style=\"display: flex;align-items: center\" >\r\n<!--            <el-tooltip content=\"普通添加\" >-->\r\n<!--              <i class=\"el-icon-plus\" @click=\"addItem(scope.row)\" />-->\r\n<!--            </el-tooltip>-->\r\n            <el-tooltip content=\"选自物料总表\" >\r\n<!--              <i class=\"el-icon-circle-plus-outline\" @click=\"selectGoods(scope.row.children,scope.row.id)\" />-->\r\n              <i class=\"el-icon-help\" @click=\"showBcDialog(scope.row)\" />\r\n            </el-tooltip>\r\n            <i class=\"el-icon-delete\" @click=\"delItem(scope.row)\" />\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <div v-if=\"!readonly\" class=\"dialog-footer\" style=\"margin-top: 20px\">\r\n      <el-button :loading=\"btnLoading\" size=\"mini\" type=\"primary\" @click=\"submitForm\" >保 存</el-button>\r\n    </div>\r\n\r\n    <el-dialog :close-on-click-modal=\"false\" :fullscreen=\"fullscreenFlag\" :visible.sync=\"open\" append-to-body width=\"1200px\">\r\n      <div slot=\"title\" class=\"dialog-title\">项目物料\r\n        <el-button :icon=\"fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'\" type=\"text\"\r\n                   @click=\"fullscreenFlag = !fullscreenFlag\"/>\r\n      </div>\r\n      <BcSelectTable v-if=\"projectId\" :project-id=\"projectId\" @checked=\"checkBc\" />\r\n    </el-dialog>\r\n\r\n    <el-dialog :close-on-click-modal=\"false\" :fullscreen=\"fullscreenFlag\" :visible.sync=\"bomOpen\" append-to-body width=\"1200px\">\r\n      <div slot=\"title\" class=\"dialog-title\">bom结构\r\n        <el-button :icon=\"fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'\" type=\"text\"\r\n                   @click=\"fullscreenFlag = !fullscreenFlag\"/>\r\n      </div>\r\n      <ProjectBomCharts ref=\"projectBomCharts\" :bom-tree=\"bomTree\"  :erp-code=\"erpCode\" />\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n<script >\r\nimport {allBc} from \"@/api/project/bc\";\r\nimport BcSelectTable from \"@/views/project/project/bcSelectTable.vue\";\r\nimport ProjectBomCharts from \"@/views/project/project/bomCharts.vue\";\r\nimport {updateProductItem} from \"@/api/project/productItem\";\r\n\r\nexport default {\r\n  name: \"projectMarkBom\",\r\n  components: {\r\n    ProjectBomCharts,\r\n    BcSelectTable\r\n  },\r\n  props: {\r\n    projectId: {\r\n      type: Number,\r\n      default: null,\r\n    },\r\n    productName: {\r\n      type: String,\r\n      default: '',\r\n    },\r\n    bomTree: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    projectProductItemId: {\r\n      type: Number,\r\n      required: true,\r\n    },\r\n    erpCode: {\r\n      type: String,\r\n      required: true,\r\n    },\r\n    readonly: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    materialList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      btnLoading: false,\r\n      open: false,\r\n      bomOpen: false,\r\n      fullscreenFlag: false,\r\n      mb005Options: [\r\n        {label: '原料',value: '105'},\r\n        {label: '包材',value: '104'},\r\n        {label: '半成品',value: '103'},\r\n        {label: '裸装品',value: '102'},\r\n        {label: '成品',value: '101'},\r\n      ],\r\n      currentRow: {},\r\n      mb008Options: [\r\n        {label: '自制',value: '0'},\r\n        {label: '外购',value: '1'},\r\n        {label: '客供',value: '405'},\r\n      ],\r\n    }\r\n  },\r\n  async created() {\r\n  },\r\n  methods: {\r\n    bcFields(projectBcId,f) {\r\n      const arr = this.materialList.filter(i=>i.id === projectBcId)\r\n      if(arr && arr[0]) {\r\n        return arr[0][f]\r\n      }\r\n    },\r\n    mb005Text(mb005) {\r\n      const arr = this.mb005Options.filter(i=> mb005 === i.value)\r\n      if(arr && arr[0]) {\r\n        return arr[0].label\r\n      }\r\n    },\r\n    rowStyle(scope) {\r\n      if(scope.row.pid !== this.bomTree[0].id && scope.row.id !== this.bomTree[0].id) {\r\n        return {\r\n          backgroundColor: '#E6A23C'\r\n        }\r\n      }\r\n    },\r\n    mb008Text(mb008) {\r\n      let text = \"\"\r\n      if(mb008) {\r\n        if([\"401\",\"402\",\"0\"].includes(mb008)) {\r\n          text = '0'\r\n        }\r\n        if([\"403\",\"404\",\"1\"].includes(mb008)) {\r\n          text = '1'\r\n        }\r\n        if([\"405\"].includes(mb008)) {\r\n          text = '405'\r\n        }\r\n      }\r\n      return text\r\n    },\r\n    async submitForm() {\r\n      const tempArray = this.translateTreeToData(this.bomTree)\r\n      const bomArray = []\r\n      for (const item of tempArray) {\r\n        bomArray.push({\r\n          ...item,\r\n          mb008: this.mb008Text(item.mb008),\r\n          children: [],\r\n        })\r\n        if(item.pid && !item.mb002) {\r\n          this.msgError('请输入品名')\r\n          return\r\n        }\r\n        if(!item.mb005) {\r\n          this.msgError('请输入类别')\r\n          return\r\n        }\r\n        if(!item.md006) {\r\n          this.msgError('请输入用量')\r\n          return\r\n        }\r\n        if(!item.md007) {\r\n          this.msgError('请输入底数')\r\n          return\r\n        }\r\n      }\r\n\r\n      const bomTree = this.toTree(bomArray,undefined)\r\n      const params = {\r\n        id: this.projectProductItemId,\r\n        bomTree: JSON.stringify(bomTree),\r\n        bomArray: JSON.stringify(bomArray),\r\n      }\r\n      if(bomTree[0] && bomTree[0].mb002) {\r\n        params.bomName = bomTree[0].mb002\r\n      }\r\n      if (this.projectProductItemId != null) {\r\n        try {\r\n          this.btnLoading = true\r\n          await updateProductItem(params)\r\n          this.btnLoading = false\r\n          this.msgSuccess(\"修改成功\")\r\n          this.$emit('saveSuccess')\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n    async initChart() {\r\n      this.bomOpen = true\r\n      await this.$nextTick()\r\n      await this.$refs.projectBomCharts.init()\r\n    },\r\n    async emptyTree() {\r\n      try {\r\n        await this.$confirm('是否确认清空?')\r\n        this.bomTree.splice(0,this.bomTree.length)\r\n      } catch (e) {\r\n      }\r\n    },\r\n    async initLp(pid) {\r\n      const array = []\r\n      const id = this.$nanoid()\r\n      array.push({\r\n        id,\r\n        pid,\r\n        mb002: this.productName + ' 半成品',\r\n        mb005: '103',\r\n        md017: null,\r\n        mb003: null,\r\n        consumption: null,\r\n        md006: null,\r\n        md007: null,\r\n        mc004: null,\r\n        md008: null,\r\n        mb008: null,\r\n        children: [],\r\n      })\r\n      const mb008Array = this.mb008Options.map(i=>i.value)\r\n      for (const item of this.materialList) {\r\n        if(item.type === '0') {//裸品下默认是主包材的\r\n          array.push({\r\n            id: this.$nanoid(),\r\n            pid,\r\n            mb005: '104',\r\n            mb002: item.name,\r\n            md017: null,\r\n            mb003: null,\r\n            consumption: null,\r\n            md006: null,\r\n            md007: null,\r\n            mc004: null,\r\n            md008: null,\r\n            mb008: this.mb008Text(item.mb008),\r\n            children: [],\r\n            projectBcId: item.id,\r\n          })\r\n        }\r\n      }\r\n      return array\r\n    },\r\n    async initCp(pid) {\r\n      const array = []\r\n      const id = this.$nanoid()\r\n      const children = await this.initLp(id)\r\n      array.push({\r\n        id,\r\n        pid,\r\n        mb002: this.productName + '裸品',\r\n        mb005: '102',\r\n        md017: null,\r\n        mb003: null,\r\n        consumption: 1,\r\n        md006: null,\r\n        md007: 1,\r\n        mc004: null,\r\n        md008: null,\r\n        mb008: null,\r\n        children,\r\n      })\r\n      const mb008Array = this.mb008Options.map(i=>i.value)\r\n      for (const item of this.materialList) {\r\n        if(item.type !== '0') {//成品下默认不是主包材的\r\n          array.push({\r\n            id: this.$nanoid(),\r\n            pid,\r\n            mb005: '104',\r\n            mb002: item.name,\r\n            md017: null,\r\n            mb003: null,\r\n            consumption: null,\r\n            md006: null,\r\n            md007: null,\r\n            mc004: null,\r\n            md008: null,\r\n            mb008: this.mb008Text(item.mb008),\r\n            children: [],\r\n            projectBcId: item.id,\r\n          })\r\n        }\r\n      }\r\n      return array\r\n    },\r\n    async initBom() {\r\n      try {\r\n        await this.$confirm('重新生成会覆盖当前子名称下已维护的bom,是否继续?')\r\n        this.bomTree.length = 0\r\n        const id = this.$nanoid()\r\n        const children = await this.initCp(id)\r\n        const o = {\r\n          id,\r\n          pid: undefined,\r\n          mb002: null,\r\n          mb005: '101',\r\n          md017: null,\r\n          mb003: null,\r\n          consumption: 1,\r\n          md006: 1,\r\n          md007: 1,\r\n          mc004: null,\r\n          md008: null,\r\n          mb008: null,\r\n          children,\r\n        }\r\n        this.bomTree.push(o)\r\n      } catch (e) {\r\n      }\r\n    },\r\n    nameStyle(mb005) {\r\n      let color = '';\r\n      switch (mb005) {\r\n        case '101': color = '#409EFF'; break;\r\n        case '102': color = '#67C23A'; break;\r\n        case '103': color = '#E6A23C'; break;\r\n        case '104': color = '#F56C6C'; break;\r\n        case '105': color = '#909399'; break;\r\n      }\r\n      return {\r\n        color: color\r\n      }\r\n    },\r\n    checkBc(bcArray) {\r\n      bcArray.sort((pre,curr)=>pre.mb005 - curr.mb005)\r\n      for (const item of bcArray) {\r\n        const o = {\r\n          id: this.$nanoid(),\r\n          pid: this.currentRow.id,\r\n          mb005: item.mb005,\r\n          mb002: item.name,\r\n          md017: null,\r\n          mb003: null,\r\n          consumption: null,\r\n          md006: null,\r\n          md007: null,\r\n          mc004: null,\r\n          md008: null,\r\n          mb008: this.mb008Text(item.mb008),\r\n          children: [],\r\n          projectBcId: item.id,\r\n        }\r\n        this.currentRow.children.push(o)\r\n      }\r\n      this.open = false\r\n    },\r\n    showBcDialog(row) {\r\n      this.currentRow = row\r\n      this.open = true\r\n    },\r\n    parentName(bomTree,pid) {\r\n      if(pid && bomTree.length) {\r\n        const arr = bomTree.filter(i=>i.id === pid)\r\n        if(arr && arr[0]) {\r\n          return arr[0].mb002\r\n        } else {\r\n          for (const item of bomTree) {\r\n            if(item.children.length) {\r\n              return this.parentName(item.children,pid)\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    delItem(row) {\r\n      this.removeItem(this.bomTree,row)\r\n    },\r\n    removeItem(bomTree,row) {\r\n      if(row.pid) {\r\n        for (const b of bomTree) {\r\n          if(b.id === row.pid && b.children && b.children.length) {\r\n            const parentArray = b.children\r\n            const index = parentArray.findIndex(i=> i.id === row.id)\r\n            parentArray.splice(index,1)\r\n          }\r\n          this.removeItem(b.children,row)\r\n        }\r\n      } else {\r\n        const index = bomTree.findIndex(i=> i.id === row.id)\r\n        bomTree.splice(index,1)\r\n      }\r\n    },\r\n    addItem(parentNode) {\r\n      const o = {\r\n        id: this.$nanoid(),\r\n        mb002: null,\r\n        md017: null,\r\n        mb003: null,\r\n        consumption: null,\r\n        md006: null,\r\n        md007: null,\r\n        mc004: null,\r\n        md008: null,\r\n        mb008: null,\r\n        children: [],\r\n      }\r\n      if(parentNode) {\r\n        o.pid = parentNode.id\r\n        if(!parentNode.pid) {\r\n          o.mb002 = this.productName + ' 裸品'\r\n          o.mb005 = '102'\r\n        } else if(parentNode.mb005 === '102') {\r\n          o.mb002 = this.productName + ' 半成品'\r\n          o.mb005 = '103'\r\n        } else {\r\n          o.mb005 = null\r\n        }\r\n        parentNode.children.push(o)\r\n      } else {\r\n        o.mb005 = '101'\r\n        o.md006 = 1\r\n        o.md007 = 1\r\n        o.mb002 = this.productName\r\n        this.bomTree.push(o)\r\n      }\r\n    },\r\n  },\r\n}\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AA+JA,IAAAA,GAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,UAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,YAAA,GAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,IAAA;EACAC,UAAA;IACAC,gBAAA,EAAAA,kBAAA;IACAC,aAAA,EAAAA;EACA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,WAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACAG,OAAA;MACAL,IAAA,EAAAM,KAAA;MACAC,QAAA;IACA;IACAC,oBAAA;MACAR,IAAA,EAAAC,MAAA;MACAM,QAAA;IACA;IACAE,OAAA;MACAT,IAAA,EAAAI,MAAA;MACAG,QAAA;IACA;IACAG,QAAA;MACAV,IAAA,EAAAW,OAAA;MACAT,OAAA;IACA;IACAU,YAAA;MACAZ,IAAA,EAAAM,KAAA;MACAC,QAAA;IACA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,IAAA;MACAC,OAAA;MACAC,cAAA;MACAC,YAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,UAAA;MACAC,YAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACAG,OAAA,WAAAA,QAAA;IAAA,WAAAC,kBAAA,CAAAtB,OAAA,mBAAAuB,oBAAA,CAAAvB,OAAA,IAAAwB,IAAA,UAAAC,QAAA;MAAA,WAAAF,oBAAA,CAAAvB,OAAA,IAAA0B,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;UAAA;YAAA,OAAAF,QAAA,CAAAG,IAAA;QAAA;MAAA,GAAAN,OAAA;IAAA;EACA;EACAO,OAAA;IACAC,QAAA,WAAAA,SAAAC,WAAA,EAAAC,CAAA;MACA,IAAAC,GAAA,QAAA1B,YAAA,CAAA2B,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,EAAA,KAAAL,WAAA;MAAA;MACA,IAAAE,GAAA,IAAAA,GAAA;QACA,OAAAA,GAAA,IAAAD,CAAA;MACA;IACA;IACAK,SAAA,WAAAA,UAAAC,KAAA;MACA,IAAAL,GAAA,QAAApB,YAAA,CAAAqB,MAAA,WAAAC,CAAA;QAAA,OAAAG,KAAA,KAAAH,CAAA,CAAApB,KAAA;MAAA;MACA,IAAAkB,GAAA,IAAAA,GAAA;QACA,OAAAA,GAAA,IAAAnB,KAAA;MACA;IACA;IACAyB,QAAA,WAAAA,SAAAC,KAAA;MACA,IAAAA,KAAA,CAAAC,GAAA,CAAAC,GAAA,UAAA1C,OAAA,IAAAoC,EAAA,IAAAI,KAAA,CAAAC,GAAA,CAAAL,EAAA,UAAApC,OAAA,IAAAoC,EAAA;QACA;UACAO,eAAA;QACA;MACA;IACA;IACAC,SAAA,WAAAA,UAAAC,KAAA;MACA,IAAAC,IAAA;MACA,IAAAD,KAAA;QACA,wBAAAE,QAAA,CAAAF,KAAA;UACAC,IAAA;QACA;QACA,wBAAAC,QAAA,CAAAF,KAAA;UACAC,IAAA;QACA;QACA,YAAAC,QAAA,CAAAF,KAAA;UACAC,IAAA;QACA;MACA;MACA,OAAAA,IAAA;IACA;IACAE,UAAA,WAAAA,WAAA;MAAA,IAAAC,KAAA;MAAA,WAAA9B,kBAAA,CAAAtB,OAAA,mBAAAuB,oBAAA,CAAAvB,OAAA,IAAAwB,IAAA,UAAA6B,SAAA;QAAA,IAAAC,SAAA,EAAAC,QAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,IAAA,EAAAvD,OAAA,EAAAwD,MAAA;QAAA,WAAApC,oBAAA,CAAAvB,OAAA,IAAA0B,IAAA,UAAAkC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhC,IAAA,GAAAgC,SAAA,CAAA/B,IAAA;YAAA;cACAwB,SAAA,GAAAF,KAAA,CAAAU,mBAAA,CAAAV,KAAA,CAAAjD,OAAA;cACAoD,QAAA;cAAAC,SAAA,OAAAO,2BAAA,CAAA/D,OAAA,EACAsD,SAAA;cAAAO,SAAA,CAAAhC,IAAA;cAAA2B,SAAA,CAAAQ,CAAA;YAAA;cAAA,KAAAP,KAAA,GAAAD,SAAA,CAAAS,CAAA,IAAAC,IAAA;gBAAAL,SAAA,CAAA/B,IAAA;gBAAA;cAAA;cAAA4B,IAAA,GAAAD,KAAA,CAAAvC,KAAA;cACAqC,QAAA,CAAAY,IAAA,KAAAC,cAAA,CAAApE,OAAA,MAAAoE,cAAA,CAAApE,OAAA,MACA0D,IAAA;gBACAV,KAAA,EAAAI,KAAA,CAAAL,SAAA,CAAAW,IAAA,CAAAV,KAAA;gBACAqB,QAAA;cAAA,EACA;cAAA,MACAX,IAAA,CAAAb,GAAA,KAAAa,IAAA,CAAAY,KAAA;gBAAAT,SAAA,CAAA/B,IAAA;gBAAA;cAAA;cACAsB,KAAA,CAAAmB,QAAA;cAAA,OAAAV,SAAA,CAAAW,MAAA;YAAA;cAAA,IAGAd,IAAA,CAAAjB,KAAA;gBAAAoB,SAAA,CAAA/B,IAAA;gBAAA;cAAA;cACAsB,KAAA,CAAAmB,QAAA;cAAA,OAAAV,SAAA,CAAAW,MAAA;YAAA;cAAA,IAGAd,IAAA,CAAAe,KAAA;gBAAAZ,SAAA,CAAA/B,IAAA;gBAAA;cAAA;cACAsB,KAAA,CAAAmB,QAAA;cAAA,OAAAV,SAAA,CAAAW,MAAA;YAAA;cAAA,IAGAd,IAAA,CAAAgB,KAAA;gBAAAb,SAAA,CAAA/B,IAAA;gBAAA;cAAA;cACAsB,KAAA,CAAAmB,QAAA;cAAA,OAAAV,SAAA,CAAAW,MAAA;YAAA;cAAAX,SAAA,CAAA/B,IAAA;cAAA;YAAA;cAAA+B,SAAA,CAAA/B,IAAA;cAAA;YAAA;cAAA+B,SAAA,CAAAhC,IAAA;cAAAgC,SAAA,CAAAc,EAAA,GAAAd,SAAA;cAAAL,SAAA,CAAAoB,CAAA,CAAAf,SAAA,CAAAc,EAAA;YAAA;cAAAd,SAAA,CAAAhC,IAAA;cAAA2B,SAAA,CAAArB,CAAA;cAAA,OAAA0B,SAAA,CAAAgB,MAAA;YAAA;cAKA1E,OAAA,GAAAiD,KAAA,CAAA0B,MAAA,CAAAvB,QAAA,EAAAwB,SAAA;cACApB,MAAA;gBACApB,EAAA,EAAAa,KAAA,CAAA9C,oBAAA;gBACAH,OAAA,EAAA6E,IAAA,CAAAC,SAAA,CAAA9E,OAAA;gBACAoD,QAAA,EAAAyB,IAAA,CAAAC,SAAA,CAAA1B,QAAA;cACA;cACA,IAAApD,OAAA,OAAAA,OAAA,IAAAmE,KAAA;gBACAX,MAAA,CAAAuB,OAAA,GAAA/E,OAAA,IAAAmE,KAAA;cACA;cAAA,MACAlB,KAAA,CAAA9C,oBAAA;gBAAAuD,SAAA,CAAA/B,IAAA;gBAAA;cAAA;cAAA+B,SAAA,CAAAhC,IAAA;cAEAuB,KAAA,CAAAxC,UAAA;cAAAiD,SAAA,CAAA/B,IAAA;cAAA,OACA,IAAAqD,8BAAA,EAAAxB,MAAA;YAAA;cACAP,KAAA,CAAAxC,UAAA;cACAwC,KAAA,CAAAgC,UAAA;cACAhC,KAAA,CAAAiC,KAAA;cAAAxB,SAAA,CAAA/B,IAAA;cAAA;YAAA;cAAA+B,SAAA,CAAAhC,IAAA;cAAAgC,SAAA,CAAAyB,EAAA,GAAAzB,SAAA;cAEAT,KAAA,CAAAxC,UAAA;YAAA;YAAA;cAAA,OAAAiD,SAAA,CAAA9B,IAAA;UAAA;QAAA,GAAAsB,QAAA;MAAA;IAGA;IACAkC,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MAAA,WAAAlE,kBAAA,CAAAtB,OAAA,mBAAAuB,oBAAA,CAAAvB,OAAA,IAAAwB,IAAA,UAAAiE,SAAA;QAAA,WAAAlE,oBAAA,CAAAvB,OAAA,IAAA0B,IAAA,UAAAgE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9D,IAAA,GAAA8D,SAAA,CAAA7D,IAAA;YAAA;cACA0D,MAAA,CAAA1E,OAAA;cAAA6E,SAAA,CAAA7D,IAAA;cAAA,OACA0D,MAAA,CAAAI,SAAA;YAAA;cAAAD,SAAA,CAAA7D,IAAA;cAAA,OACA0D,MAAA,CAAAK,KAAA,CAAAC,gBAAA,CAAAC,IAAA;YAAA;YAAA;cAAA,OAAAJ,SAAA,CAAA5D,IAAA;UAAA;QAAA,GAAA0D,QAAA;MAAA;IACA;IACAO,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MAAA,WAAA3E,kBAAA,CAAAtB,OAAA,mBAAAuB,oBAAA,CAAAvB,OAAA,IAAAwB,IAAA,UAAA0E,SAAA;QAAA,WAAA3E,oBAAA,CAAAvB,OAAA,IAAA0B,IAAA,UAAAyE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvE,IAAA,GAAAuE,SAAA,CAAAtE,IAAA;YAAA;cAAAsE,SAAA,CAAAvE,IAAA;cAAAuE,SAAA,CAAAtE,IAAA;cAAA,OAEAmE,MAAA,CAAAI,QAAA;YAAA;cACAJ,MAAA,CAAA9F,OAAA,CAAAmG,MAAA,IAAAL,MAAA,CAAA9F,OAAA,CAAAoG,MAAA;cAAAH,SAAA,CAAAtE,IAAA;cAAA;YAAA;cAAAsE,SAAA,CAAAvE,IAAA;cAAAuE,SAAA,CAAAzB,EAAA,GAAAyB,SAAA;YAAA;YAAA;cAAA,OAAAA,SAAA,CAAArE,IAAA;UAAA;QAAA,GAAAmE,QAAA;MAAA;IAGA;IACAM,MAAA,WAAAA,OAAA3D,GAAA;MAAA,IAAA4D,MAAA;MAAA,WAAAnF,kBAAA,CAAAtB,OAAA,mBAAAuB,oBAAA,CAAAvB,OAAA,IAAAwB,IAAA,UAAAkF,SAAA;QAAA,IAAAC,KAAA,EAAApE,EAAA,EAAAqE,UAAA,EAAAC,UAAA,EAAAC,MAAA,EAAApD,IAAA;QAAA,WAAAnC,oBAAA,CAAAvB,OAAA,IAAA0B,IAAA,UAAAqF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnF,IAAA,GAAAmF,SAAA,CAAAlF,IAAA;YAAA;cACA6E,KAAA;cACApE,EAAA,GAAAkE,MAAA,CAAAQ,OAAA;cACAN,KAAA,CAAAxC,IAAA;gBACA5B,EAAA,EAAAA,EAAA;gBACAM,GAAA,EAAAA,GAAA;gBACAyB,KAAA,EAAAmC,MAAA,CAAAxG,WAAA;gBACAwC,KAAA;gBACAyE,KAAA;gBACAC,KAAA;gBACAC,WAAA;gBACA3C,KAAA;gBACAC,KAAA;gBACA2C,KAAA;gBACAC,KAAA;gBACAtE,KAAA;gBACAqB,QAAA;cACA;cACAuC,UAAA,GAAAH,MAAA,CAAArF,YAAA,CAAAmG,GAAA,WAAAjF,CAAA;gBAAA,OAAAA,CAAA,CAAApB,KAAA;cAAA;cAAA2F,UAAA,OAAA9C,2BAAA,CAAA/D,OAAA,EACAyG,MAAA,CAAA/F,YAAA;cAAA;gBAAA,KAAAmG,UAAA,CAAA7C,CAAA,MAAA8C,MAAA,GAAAD,UAAA,CAAA5C,CAAA,IAAAC,IAAA;kBAAAR,IAAA,GAAAoD,MAAA,CAAA5F,KAAA;kBACA,IAAAwC,IAAA,CAAA5D,IAAA;oBAAA;oBACA6G,KAAA,CAAAxC,IAAA;sBACA5B,EAAA,EAAAkE,MAAA,CAAAQ,OAAA;sBACApE,GAAA,EAAAA,GAAA;sBACAJ,KAAA;sBACA6B,KAAA,EAAAZ,IAAA,CAAAlE,IAAA;sBACA0H,KAAA;sBACAC,KAAA;sBACAC,WAAA;sBACA3C,KAAA;sBACAC,KAAA;sBACA2C,KAAA;sBACAC,KAAA;sBACAtE,KAAA,EAAAyD,MAAA,CAAA1D,SAAA,CAAAW,IAAA,CAAAV,KAAA;sBACAqB,QAAA;sBACAnC,WAAA,EAAAwB,IAAA,CAAAnB;oBACA;kBACA;gBACA;cAAA,SAAAiF,GAAA;gBAAAX,UAAA,CAAAjC,CAAA,CAAA4C,GAAA;cAAA;gBAAAX,UAAA,CAAA1E,CAAA;cAAA;cAAA,OAAA6E,SAAA,CAAAxC,MAAA,WACAmC,KAAA;YAAA;YAAA;cAAA,OAAAK,SAAA,CAAAjF,IAAA;UAAA;QAAA,GAAA2E,QAAA;MAAA;IACA;IACAe,MAAA,WAAAA,OAAA5E,GAAA;MAAA,IAAA6E,MAAA;MAAA,WAAApG,kBAAA,CAAAtB,OAAA,mBAAAuB,oBAAA,CAAAvB,OAAA,IAAAwB,IAAA,UAAAmG,SAAA;QAAA,IAAAhB,KAAA,EAAApE,EAAA,EAAA8B,QAAA,EAAAuC,UAAA,EAAAgB,UAAA,EAAAC,MAAA,EAAAnE,IAAA;QAAA,WAAAnC,oBAAA,CAAAvB,OAAA,IAAA0B,IAAA,UAAAoG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlG,IAAA,GAAAkG,SAAA,CAAAjG,IAAA;YAAA;cACA6E,KAAA;cACApE,EAAA,GAAAmF,MAAA,CAAAT,OAAA;cAAAc,SAAA,CAAAjG,IAAA;cAAA,OACA4F,MAAA,CAAAlB,MAAA,CAAAjE,EAAA;YAAA;cAAA8B,QAAA,GAAA0D,SAAA,CAAAC,IAAA;cACArB,KAAA,CAAAxC,IAAA;gBACA5B,EAAA,EAAAA,EAAA;gBACAM,GAAA,EAAAA,GAAA;gBACAyB,KAAA,EAAAoD,MAAA,CAAAzH,WAAA;gBACAwC,KAAA;gBACAyE,KAAA;gBACAC,KAAA;gBACAC,WAAA;gBACA3C,KAAA;gBACAC,KAAA;gBACA2C,KAAA;gBACAC,KAAA;gBACAtE,KAAA;gBACAqB,QAAA,EAAAA;cACA;cACAuC,UAAA,GAAAc,MAAA,CAAAtG,YAAA,CAAAmG,GAAA,WAAAjF,CAAA;gBAAA,OAAAA,CAAA,CAAApB,KAAA;cAAA;cAAA0G,UAAA,OAAA7D,2BAAA,CAAA/D,OAAA,EACA0H,MAAA,CAAAhH,YAAA;cAAA;gBAAA,KAAAkH,UAAA,CAAA5D,CAAA,MAAA6D,MAAA,GAAAD,UAAA,CAAA3D,CAAA,IAAAC,IAAA;kBAAAR,IAAA,GAAAmE,MAAA,CAAA3G,KAAA;kBACA,IAAAwC,IAAA,CAAA5D,IAAA;oBAAA;oBACA6G,KAAA,CAAAxC,IAAA;sBACA5B,EAAA,EAAAmF,MAAA,CAAAT,OAAA;sBACApE,GAAA,EAAAA,GAAA;sBACAJ,KAAA;sBACA6B,KAAA,EAAAZ,IAAA,CAAAlE,IAAA;sBACA0H,KAAA;sBACAC,KAAA;sBACAC,WAAA;sBACA3C,KAAA;sBACAC,KAAA;sBACA2C,KAAA;sBACAC,KAAA;sBACAtE,KAAA,EAAA0E,MAAA,CAAA3E,SAAA,CAAAW,IAAA,CAAAV,KAAA;sBACAqB,QAAA;sBACAnC,WAAA,EAAAwB,IAAA,CAAAnB;oBACA;kBACA;gBACA;cAAA,SAAAiF,GAAA;gBAAAI,UAAA,CAAAhD,CAAA,CAAA4C,GAAA;cAAA;gBAAAI,UAAA,CAAAzF,CAAA;cAAA;cAAA,OAAA4F,SAAA,CAAAvD,MAAA,WACAmC,KAAA;YAAA;YAAA;cAAA,OAAAoB,SAAA,CAAAhG,IAAA;UAAA;QAAA,GAAA4F,QAAA;MAAA;IACA;IACAM,OAAA,WAAAA,QAAA;MAAA,IAAAC,MAAA;MAAA,WAAA5G,kBAAA,CAAAtB,OAAA,mBAAAuB,oBAAA,CAAAvB,OAAA,IAAAwB,IAAA,UAAA2G,SAAA;QAAA,IAAA5F,EAAA,EAAA8B,QAAA,EAAA+D,CAAA;QAAA,WAAA7G,oBAAA,CAAAvB,OAAA,IAAA0B,IAAA,UAAA2G,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzG,IAAA,GAAAyG,SAAA,CAAAxG,IAAA;YAAA;cAAAwG,SAAA,CAAAzG,IAAA;cAAAyG,SAAA,CAAAxG,IAAA;cAAA,OAEAoG,MAAA,CAAA7B,QAAA;YAAA;cACA6B,MAAA,CAAA/H,OAAA,CAAAoG,MAAA;cACAhE,EAAA,GAAA2F,MAAA,CAAAjB,OAAA;cAAAqB,SAAA,CAAAxG,IAAA;cAAA,OACAoG,MAAA,CAAAT,MAAA,CAAAlF,EAAA;YAAA;cAAA8B,QAAA,GAAAiE,SAAA,CAAAN,IAAA;cACAI,CAAA;gBACA7F,EAAA,EAAAA,EAAA;gBACAM,GAAA,EAAAkC,SAAA;gBACAT,KAAA;gBACA7B,KAAA;gBACAyE,KAAA;gBACAC,KAAA;gBACAC,WAAA;gBACA3C,KAAA;gBACAC,KAAA;gBACA2C,KAAA;gBACAC,KAAA;gBACAtE,KAAA;gBACAqB,QAAA,EAAAA;cACA;cACA6D,MAAA,CAAA/H,OAAA,CAAAgE,IAAA,CAAAiE,CAAA;cAAAE,SAAA,CAAAxG,IAAA;cAAA;YAAA;cAAAwG,SAAA,CAAAzG,IAAA;cAAAyG,SAAA,CAAA3D,EAAA,GAAA2D,SAAA;YAAA;YAAA;cAAA,OAAAA,SAAA,CAAAvG,IAAA;UAAA;QAAA,GAAAoG,QAAA;MAAA;IAGA;IACAI,SAAA,WAAAA,UAAA9F,KAAA;MACA,IAAA+F,KAAA;MACA,QAAA/F,KAAA;QACA;UAAA+F,KAAA;UAAA;QACA;UAAAA,KAAA;UAAA;QACA;UAAAA,KAAA;UAAA;QACA;UAAAA,KAAA;UAAA;QACA;UAAAA,KAAA;UAAA;MACA;MACA;QACAA,KAAA,EAAAA;MACA;IACA;IACAC,OAAA,WAAAA,QAAAC,OAAA;MACAA,OAAA,CAAAC,IAAA,WAAAC,GAAA,EAAAC,IAAA;QAAA,OAAAD,GAAA,CAAAnG,KAAA,GAAAoG,IAAA,CAAApG,KAAA;MAAA;MAAA,IAAAqG,UAAA,OAAA/E,2BAAA,CAAA/D,OAAA,EACA0I,OAAA;QAAAK,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAA9E,CAAA,MAAA+E,MAAA,GAAAD,UAAA,CAAA7E,CAAA,IAAAC,IAAA;UAAA,IAAAR,IAAA,GAAAqF,MAAA,CAAA7H,KAAA;UACA,IAAAkH,CAAA;YACA7F,EAAA,OAAA0E,OAAA;YACApE,GAAA,OAAA1B,UAAA,CAAAoB,EAAA;YACAE,KAAA,EAAAiB,IAAA,CAAAjB,KAAA;YACA6B,KAAA,EAAAZ,IAAA,CAAAlE,IAAA;YACA0H,KAAA;YACAC,KAAA;YACAC,WAAA;YACA3C,KAAA;YACAC,KAAA;YACA2C,KAAA;YACAC,KAAA;YACAtE,KAAA,OAAAD,SAAA,CAAAW,IAAA,CAAAV,KAAA;YACAqB,QAAA;YACAnC,WAAA,EAAAwB,IAAA,CAAAnB;UACA;UACA,KAAApB,UAAA,CAAAkD,QAAA,CAAAF,IAAA,CAAAiE,CAAA;QACA;MAAA,SAAAZ,GAAA;QAAAsB,UAAA,CAAAlE,CAAA,CAAA4C,GAAA;MAAA;QAAAsB,UAAA,CAAA3G,CAAA;MAAA;MACA,KAAAtB,IAAA;IACA;IACAmI,YAAA,WAAAA,aAAApG,GAAA;MACA,KAAAzB,UAAA,GAAAyB,GAAA;MACA,KAAA/B,IAAA;IACA;IACAoI,UAAA,WAAAA,WAAA9I,OAAA,EAAA0C,GAAA;MACA,IAAAA,GAAA,IAAA1C,OAAA,CAAAoG,MAAA;QACA,IAAAnE,GAAA,GAAAjC,OAAA,CAAAkC,MAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,EAAA,KAAAM,GAAA;QAAA;QACA,IAAAT,GAAA,IAAAA,GAAA;UACA,OAAAA,GAAA,IAAAkC,KAAA;QACA;UAAA,IAAA4E,UAAA,OAAAnF,2BAAA,CAAA/D,OAAA,EACAG,OAAA;YAAAgJ,MAAA;UAAA;YAAA,KAAAD,UAAA,CAAAlF,CAAA,MAAAmF,MAAA,GAAAD,UAAA,CAAAjF,CAAA,IAAAC,IAAA;cAAA,IAAAR,IAAA,GAAAyF,MAAA,CAAAjI,KAAA;cACA,IAAAwC,IAAA,CAAAW,QAAA,CAAAkC,MAAA;gBACA,YAAA0C,UAAA,CAAAvF,IAAA,CAAAW,QAAA,EAAAxB,GAAA;cACA;YACA;UAAA,SAAA2E,GAAA;YAAA0B,UAAA,CAAAtE,CAAA,CAAA4C,GAAA;UAAA;YAAA0B,UAAA,CAAA/G,CAAA;UAAA;QACA;MACA;IACA;IACAiH,OAAA,WAAAA,QAAAxG,GAAA;MACA,KAAAyG,UAAA,MAAAlJ,OAAA,EAAAyC,GAAA;IACA;IACAyG,UAAA,WAAAA,WAAAlJ,OAAA,EAAAyC,GAAA;MACA,IAAAA,GAAA,CAAAC,GAAA;QAAA,IAAAyG,UAAA,OAAAvF,2BAAA,CAAA/D,OAAA,EACAG,OAAA;UAAAoJ,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAAtF,CAAA,MAAAuF,MAAA,GAAAD,UAAA,CAAArF,CAAA,IAAAC,IAAA;YAAA,IAAAsF,CAAA,GAAAD,MAAA,CAAArI,KAAA;YACA,IAAAsI,CAAA,CAAAjH,EAAA,KAAAK,GAAA,CAAAC,GAAA,IAAA2G,CAAA,CAAAnF,QAAA,IAAAmF,CAAA,CAAAnF,QAAA,CAAAkC,MAAA;cACA,IAAAkD,WAAA,GAAAD,CAAA,CAAAnF,QAAA;cACA,IAAAqF,KAAA,GAAAD,WAAA,CAAAE,SAAA,WAAArH,CAAA;gBAAA,OAAAA,CAAA,CAAAC,EAAA,KAAAK,GAAA,CAAAL,EAAA;cAAA;cACAkH,WAAA,CAAAnD,MAAA,CAAAoD,KAAA;YACA;YACA,KAAAL,UAAA,CAAAG,CAAA,CAAAnF,QAAA,EAAAzB,GAAA;UACA;QAAA,SAAA4E,GAAA;UAAA8B,UAAA,CAAA1E,CAAA,CAAA4C,GAAA;QAAA;UAAA8B,UAAA,CAAAnH,CAAA;QAAA;MACA;QACA,IAAAuH,MAAA,GAAAvJ,OAAA,CAAAwJ,SAAA,WAAArH,CAAA;UAAA,OAAAA,CAAA,CAAAC,EAAA,KAAAK,GAAA,CAAAL,EAAA;QAAA;QACApC,OAAA,CAAAmG,MAAA,CAAAoD,MAAA;MACA;IACA;IACAE,OAAA,WAAAA,QAAAC,UAAA;MACA,IAAAzB,CAAA;QACA7F,EAAA,OAAA0E,OAAA;QACA3C,KAAA;QACA4C,KAAA;QACAC,KAAA;QACAC,WAAA;QACA3C,KAAA;QACAC,KAAA;QACA2C,KAAA;QACAC,KAAA;QACAtE,KAAA;QACAqB,QAAA;MACA;MACA,IAAAwF,UAAA;QACAzB,CAAA,CAAAvF,GAAA,GAAAgH,UAAA,CAAAtH,EAAA;QACA,KAAAsH,UAAA,CAAAhH,GAAA;UACAuF,CAAA,CAAA9D,KAAA,QAAArE,WAAA;UACAmI,CAAA,CAAA3F,KAAA;QACA,WAAAoH,UAAA,CAAApH,KAAA;UACA2F,CAAA,CAAA9D,KAAA,QAAArE,WAAA;UACAmI,CAAA,CAAA3F,KAAA;QACA;UACA2F,CAAA,CAAA3F,KAAA;QACA;QACAoH,UAAA,CAAAxF,QAAA,CAAAF,IAAA,CAAAiE,CAAA;MACA;QACAA,CAAA,CAAA3F,KAAA;QACA2F,CAAA,CAAA3D,KAAA;QACA2D,CAAA,CAAA1D,KAAA;QACA0D,CAAA,CAAA9D,KAAA,QAAArE,WAAA;QACA,KAAAE,OAAA,CAAAgE,IAAA,CAAAiE,CAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}