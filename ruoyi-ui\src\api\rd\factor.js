import request from '@/utils/request'

// 查询化妆品相对系数列表
export function listFactor(query) {
  return request({
    url: '/rd/factor/list',
    method: 'get',
    params: query
  })
}

// 查询化妆品相对系数详细
export function getFactor(id) {
  return request({
    url: '/rd/factor/' + id,
    method: 'get'
  })
}

// 新增化妆品相对系数
export function addFactor(data) {
  return request({
    url: '/rd/factor',
    method: 'post',
    data: data
  })
}

// 修改化妆品相对系数
export function updateFactor(data) {
  return request({
    url: '/rd/factor',
    method: 'put',
    data: data
  })
}

// 删除化妆品相对系数
export function delFactor(id) {
  return request({
    url: '/rd/factor/' + id,
    method: 'delete'
  })
}

// 导出化妆品相对系数
export function exportFactor(query) {
  return request({
    url: '/rd/factor/export',
    method: 'get',
    params: query
  })
}

export function allFactor(query) {
  return request({
    url: '/rd/factor/all',
    method: 'get',
    params: query
  })
}
