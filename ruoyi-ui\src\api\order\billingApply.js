import request from '@/utils/request'

// 查询开票申请列表
export function listBillingApply(query) {
  return request({
    url: '/order/billingApply/list',
    method: 'get',
    params: query
  })
}

// 查询开票申请详细
export function getBillingApply(id) {
  return request({
    url: '/order/billingApply/' + id,
    method: 'get'
  })
}

// 新增开票申请
export function addBillingApply(data) {
  return request({
    url: '/order/billingApply',
    method: 'post',
    data: data
  })
}

// 修改开票申请
export function updateBillingApply(data) {
  return request({
    url: '/order/billingApply',
    method: 'put',
    data: data
  })
}

// 删除开票申请
export function delBillingApply(id) {
  return request({
    url: '/order/billingApply/' + id,
    method: 'delete'
  })
}

// 导出开票申请
export function exportBillingApply(query) {
  return request({
    url: '/order/billingApply/export',
    method: 'get',
    params: query
  })
}

export function allBillingApply(query) {
  return request({
    url: '/order/billingApply/all',
    method: 'get',
    params: query
  })
}

export function revokeBillingApply(data) {
  return request({
    url: '/order/billingApply/revoke',
    method: 'put',
    data: data
  })
}

