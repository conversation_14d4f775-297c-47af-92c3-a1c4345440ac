{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\save.vue?vue&type=template&id=5ede9206&scoped=true", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\save.vue", "mtime": 1753954679644}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1744596530059}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uICgpIHsKICB2YXIgX3ZtID0gdGhpcwogIHZhciBfaCA9IF92bS4kY3JlYXRlRWxlbWVudAogIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogIHJldHVybiBfYygKICAgICJkaXYiLAogICAgewogICAgICBkaXJlY3RpdmVzOiBbCiAgICAgICAgewogICAgICAgICAgbmFtZTogImxvYWRpbmciLAogICAgICAgICAgcmF3TmFtZTogInYtbG9hZGluZyIsCiAgICAgICAgICB2YWx1ZTogX3ZtLmxvYWRpbmcsCiAgICAgICAgICBleHByZXNzaW9uOiAibG9hZGluZyIsCiAgICAgICAgfSwKICAgICAgXSwKICAgIH0sCiAgICBbCiAgICAgIF9jKAogICAgICAgICJlbC1yb3ciLAogICAgICAgIHsgYXR0cnM6IHsgZ3V0dGVyOiAyMCB9IH0sCiAgICAgICAgWwogICAgICAgICAgX2MoCiAgICAgICAgICAgICJlbC1jb2wiLAogICAgICAgICAgICB7IGF0dHJzOiB7IHNwYW46IDYgfSB9LAogICAgICAgICAgICBbCiAgICAgICAgICAgICAgX2MoImVsLXN0YXRpc3RpYyIsIHsKICAgICAgICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgICAgICAgICJncm91cC1zZXBhcmF0b3IiOiAiLCIsCiAgICAgICAgICAgICAgICAgIHByZWNpc2lvbjogMiwKICAgICAgICAgICAgICAgICAgdmFsdWU6IF92bS5mb3JtLnN1bU51bXMsCiAgICAgICAgICAgICAgICAgIHRpdGxlOiAi5LuK5pel5LiK5bel5oC75Lq65pWwIiwKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIDEKICAgICAgICAgICksCiAgICAgICAgICBfYygKICAgICAgICAgICAgImVsLWNvbCIsCiAgICAgICAgICAgIHsgYXR0cnM6IHsgc3BhbjogNiB9IH0sCiAgICAgICAgICAgIFsKICAgICAgICAgICAgICBfYygiZWwtc3RhdGlzdGljIiwgewogICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgImdyb3VwLXNlcGFyYXRvciI6ICIsIiwKICAgICAgICAgICAgICAgICAgcHJlY2lzaW9uOiAyLAogICAgICAgICAgICAgICAgICB2YWx1ZTogX3ZtLm1pbnV0ZXNUb0hvdXJzKF92bS5mb3JtLnN1bU1pbnV0ZXMpLAogICAgICAgICAgICAgICAgICB0aXRsZTogIuS7iuaXpeS4iuW3peaAu+W3peaXtiIsCiAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIH0pLAogICAgICAgICAgICBdLAogICAgICAgICAgICAxCiAgICAgICAgICApLAogICAgICAgICAgX2MoCiAgICAgICAgICAgICJlbC1jb2wiLAogICAgICAgICAgICB7IGF0dHJzOiB7IHNwYW46IDYgfSB9LAogICAgICAgICAgICBbCiAgICAgICAgICAgICAgX2MoImVsLXN0YXRpc3RpYyIsIHsKICAgICAgICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgICAgICAgICJncm91cC1zZXBhcmF0b3IiOiAiLCIsCiAgICAgICAgICAgICAgICAgIHByZWNpc2lvbjogMiwKICAgICAgICAgICAgICAgICAgdmFsdWU6IF92bS5taW51dGVzVG9Ib3Vycyhfdm0uZm9ybS53YWdlc01pbnV0ZXMpLAogICAgICAgICAgICAgICAgICB0aXRsZTogIuS7iuaXpeW3pei1hOaAu+W3peaXtiIsCiAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgc2NvcGVkU2xvdHM6IF92bS5fdShbCiAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICBrZXk6ICJzdWZmaXgiLAogICAgICAgICAgICAgICAgICAgIGZuOiBmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gWwogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdG9vbHRpcCIsIHsgYXR0cnM6IHsgY29udGVudDogIui0qOajgOmZpOWkliIgfSB9LCBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoImkiLCB7IHN0YXRpY0NsYXNzOiAiZWwtaWNvbi1xdWVzdGlvbiIgfSksCiAgICAgICAgICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgICAgICAgXQogICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgcHJveHk6IHRydWUsCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgXSwKICAgICAgICAgICAgMQogICAgICAgICAgKSwKICAgICAgICAgIF9jKAogICAgICAgICAgICAiZWwtY29sIiwKICAgICAgICAgICAgeyBhdHRyczogeyBzcGFuOiA2IH0gfSwKICAgICAgICAgICAgWwogICAgICAgICAgICAgIF9jKCJlbC1zdGF0aXN0aWMiLCB7CiAgICAgICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgICAgICAiZ3JvdXAtc2VwYXJhdG9yIjogIiwiLAogICAgICAgICAgICAgICAgICBwcmVjaXNpb246IDIsCiAgICAgICAgICAgICAgICAgIHZhbHVlOiBfdm0ubWludXRlc1RvSG91cnMoX3ZtLmZvcm0ubWVzTWludXRlcyksCiAgICAgICAgICAgICAgICAgIHRpdGxlOiAibWVz5LiK5bel5oC75bel5pe2IiwKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICBzY29wZWRTbG90czogX3ZtLl91KFsKICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIGtleTogInN1ZmZpeCIsCiAgICAgICAgICAgICAgICAgICAgZm46IGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBbCiAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJlbC10b29sdGlwIiwgeyBhdHRyczogeyBjb250ZW50OiAi6LSo5qOA6Zmk5aSWIiB9IH0sIFsKICAgICAgICAgICAgICAgICAgICAgICAgICBfYygiaSIsIHsgc3RhdGljQ2xhc3M6ICJlbC1pY29uLXF1ZXN0aW9uIiB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICBwcm94eTogdHJ1ZSwKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgIH0pLAogICAgICAgICAgICBdLAogICAgICAgICAgICAxCiAgICAgICAgICApLAogICAgICAgIF0sCiAgICAgICAgMQogICAgICApLAogICAgICBfYygKICAgICAgICAiZWwtcm93IiwKICAgICAgICB7IHN0YXRpY1N0eWxlOiB7ICJtYXJnaW4tdG9wIjogIjIwcHgiIH0sIGF0dHJzOiB7IGd1dHRlcjogMjAgfSB9LAogICAgICAgIFsKICAgICAgICAgIF9jKCJlbC1jb2wiLCB7IGF0dHJzOiB7IHNwYW46IDYgfSB9LCBbCiAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICAgIHsgc3RhdGljU3R5bGU6IHsgaGVpZ2h0OiAiNDIwcHgiLCB3aWR0aDogIjQyMHB4IiB9IH0sCiAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgX2MoIkJhc2VDaGFydCIsIHsKICAgICAgICAgICAgICAgICAgcmVmOiAidXNlck51bXNDaGFydCIsCiAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHN0eWxlT2JqOiB7IGhlaWdodDogIjQwMHB4Iiwgd2lkdGg6ICI0MDBweCIgfSB9LAogICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAxCiAgICAgICAgICAgICksCiAgICAgICAgICBdKSwKICAgICAgICAgIF9jKCJlbC1jb2wiLCB7IGF0dHJzOiB7IHNwYW46IDYgfSB9LCBbCiAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICAgIHsgc3RhdGljU3R5bGU6IHsgaGVpZ2h0OiAiNDIwcHgiLCB3aWR0aDogIjQyMHB4IiB9IH0sCiAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgX2MoIkJhc2VDaGFydCIsIHsKICAgICAgICAgICAgICAgICAgcmVmOiAidXNlck1pbnV0ZXNDaGFydCIsCiAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHN0eWxlT2JqOiB7IGhlaWdodDogIjQwMHB4Iiwgd2lkdGg6ICI0MDBweCIgfSB9LAogICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAxCiAgICAgICAgICAgICksCiAgICAgICAgICBdKSwKICAgICAgICAgIF9jKCJlbC1jb2wiLCB7IGF0dHJzOiB7IHNwYW46IDYgfSB9LCBbCiAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICAgIHsgc3RhdGljU3R5bGU6IHsgaGVpZ2h0OiAiNDIwcHgiLCB3aWR0aDogIjQyMHB4IiB9IH0sCiAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgX2MoIkJhc2VDaGFydCIsIHsKICAgICAgICAgICAgICAgICAgcmVmOiAiaG91cnNUeXBlQ2hhcnRzIiwKICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgc3R5bGVPYmo6IHsgaGVpZ2h0OiAiNDAwcHgiLCB3aWR0aDogIjQwMHB4IiB9IH0sCiAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgIDEKICAgICAgICAgICAgKSwKICAgICAgICAgIF0pLAogICAgICAgICAgX2MoImVsLWNvbCIsIHsgYXR0cnM6IHsgc3BhbjogNiB9IH0sIFsKICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgICAgeyBzdGF0aWNTdHlsZTogeyBoZWlnaHQ6ICI0MjBweCIsIHdpZHRoOiAiNDIwcHgiIH0gfSwKICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICBfYygiQmFzZUNoYXJ0IiwgewogICAgICAgICAgICAgICAgICByZWY6ICJob3Vyc0NvbXBvc2VDaGFydHMiLAogICAgICAgICAgICAgICAgICBhdHRyczogeyBzdHlsZU9iajogeyBoZWlnaHQ6ICI0MDBweCIsIHdpZHRoOiAiNDAwcHgiIH0gfSwKICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgMQogICAgICAgICAgICApLAogICAgICAgICAgXSksCiAgICAgICAgXSwKICAgICAgICAxCiAgICAgICksCiAgICAgIF9jKAogICAgICAgICJlbC10b29sdGlwIiwKICAgICAgICB7IGF0dHJzOiB7IGNvbnRlbnQ6ICLliLfmlrDkuqfnur/lt6Xml7YiIH0gfSwKICAgICAgICBbCiAgICAgICAgICBfYygiZWwtYnV0dG9uIiwgewogICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgIGxvYWRpbmc6IF92bS5idG5Mb2FkaW5nLAogICAgICAgICAgICAgIGljb246ICJlbC1pY29uLXJlZnJlc2giLAogICAgICAgICAgICAgIHNpemU6ICJtaW5pIiwKICAgICAgICAgICAgICB0eXBlOiAidGV4dCIsCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgY2xpY2s6IGZ1bmN0aW9uICgkZXZlbnQpIHsKICAgICAgICAgICAgICAgIHJldHVybiBfdm0ucmVmcmVzaEFyZWFIb3Vycyhfdm0uZm9ybS5mYWN0b3J5LCBfdm0uZm9ybS53b3JrRGF0ZSkKICAgICAgICAgICAgICB9LAogICAgICAgICAgICB9LAogICAgICAgICAgfSksCiAgICAgICAgXSwKICAgICAgICAxCiAgICAgICksCiAgICAgIF9jKAogICAgICAgICJlbC10YWJzIiwKICAgICAgICB7CiAgICAgICAgICBtb2RlbDogewogICAgICAgICAgICB2YWx1ZTogX3ZtLmN1cnJlbnRUYWIsCiAgICAgICAgICAgIGNhbGxiYWNrOiBmdW5jdGlvbiAoJCR2KSB7CiAgICAgICAgICAgICAgX3ZtLmN1cnJlbnRUYWIgPSAkJHYKICAgICAgICAgICAgfSwKICAgICAgICAgICAgZXhwcmVzc2lvbjogImN1cnJlbnRUYWIiLAogICAgICAgICAgfSwKICAgICAgICB9LAogICAgICAgIF92bS5fbChfdm0udGFiT3B0aW9ucywgZnVuY3Rpb24gKHRhYikgewogICAgICAgICAgcmV0dXJuIF9jKAogICAgICAgICAgICAiZWwtdGFiLXBhbmUiLAogICAgICAgICAgICB7IGtleTogdGFiLnZhbHVlLCBhdHRyczogeyBsYWJlbDogdGFiLmxhYmVsLCBuYW1lOiB0YWIudmFsdWUgfSB9LAogICAgICAgICAgICBbCiAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJ0YWJsZS13cmFwcGVyIiB9LCBbCiAgICAgICAgICAgICAgICBfYygidGFibGUiLCB7IHN0YXRpY0NsYXNzOiAiYmFzZS10YWJsZSBzbWFsbC10YWJsZSIgfSwgWwogICAgICAgICAgICAgICAgICBfYygidHIiLCBbCiAgICAgICAgICAgICAgICAgICAgX2MoInRoIiwgeyBzdGF0aWNTdHlsZTogeyB3aWR0aDogIjEyMHB4IiB9IH0sIFsKICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigi57u05bqmIiksCiAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgX2MoInRoIiwgeyBzdGF0aWNTdHlsZTogeyB3aWR0aDogIjEyMHB4IiB9IH0sIFsKICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigi5oC75pWwIiksCiAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgX2MoInRoIiwgeyBzdGF0aWNTdHlsZTogeyB3aWR0aDogIjEyMHB4IiB9IH0sIFsKICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigi5q2j5byP5belIiksCiAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgX2MoInRoIiwgeyBzdGF0aWNTdHlsZTogeyB3aWR0aDogIjEyMHB4IiB9IH0sIFsKICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigi56ew6YePIiksCiAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgX2MoInRoIiwgeyBzdGF0aWNTdHlsZTogeyB3aWR0aDogIjEyMHB4IiB9IH0sIFsKICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigi6Ze05o6lIiksCiAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgX2MoInRoIiwgeyBzdGF0aWNTdHlsZTogeyB3aWR0aDogIjEyMHB4IiB9IH0sIFsKICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigi566h55CGIiksCiAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgX2MoInRoIiwgeyBzdGF0aWNTdHlsZTogeyB3aWR0aDogIjEyMHB4IiB9IH0sIFsKICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigi6LSo5qOAIiksCiAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgX2MoInRoIiwgeyBzdGF0aWNTdHlsZTogeyB3aWR0aDogIjEyMHB4IiB9IH0sIFsKICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigi5Yqz5YqhIiksCiAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgX2MoInRoIiwgeyBzdGF0aWNTdHlsZTogeyB3aWR0aDogIjEyMHB4IiB9IH0sIFsKICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigi5YyF5bmyIiksCiAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgICBfYygidHIiLCBbCiAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAidGgiLAogICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoIiDlt6XotYTlt6Xml7YgIiksCiAgICAgICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICJlbC10b29sdGlwIiwKICAgICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250ZW50OgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICLmnInkv67mraPlt6Xml7bml7Ys5Lul5L+u5q2j5bel5pe25Li65YeGKOS4jeWMheWQq+i0qOajgCkiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgIFtfYygiaSIsIHsgc3RhdGljQ2xhc3M6ICJlbC1pY29uLXF1ZXN0aW9uIiB9KV0KICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICBfYygidGQiLCBbCiAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoCiAgICAgICAgICAgICAgICAgICAgICAgIF92bS5fcyhfdm0ubWludXRlc1RvSG91cnModGFiLndhZ2VzTWludXRlcykudG9GaXhlZCgyKSkKICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgX2MoInRkIiwgWwogICAgICAgICAgICAgICAgICAgICAgX3ZtLl92KAogICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLm1pbnV0ZXNUb0hvdXJzKHRhYi51c2VyV2FnZXNNaW51dGVzKS50b0ZpeGVkKDIpCiAgICAgICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgX2MoInRkIiwgWwogICAgICAgICAgICAgICAgICAgICAgX3ZtLl92KAogICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3MoX3ZtLm1pbnV0ZXNUb0hvdXJzKHRhYi53ZWlnaHRNaW51dGVzKS50b0ZpeGVkKDIpKQogICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgICBfYygidGQiLCBbCiAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoCiAgICAgICAgICAgICAgICAgICAgICAgIF92bS5fcyhfdm0ubWludXRlc1RvSG91cnModGFiLm90aGVyTWludXRlcykudG9GaXhlZCgyKSkKICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgX2MoInRkIiwgWwogICAgICAgICAgICAgICAgICAgICAgX3ZtLl92KAogICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3MoX3ZtLm1pbnV0ZXNUb0hvdXJzKHRhYi5tYW5hZ2VNaW51dGVzKS50b0ZpeGVkKDIpKQogICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgICBfYygidGQiLCBbCiAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoCiAgICAgICAgICAgICAgICAgICAgICAgIF92bS5fcyhfdm0ubWludXRlc1RvSG91cnModGFiLnFjTWludXRlcykudG9GaXhlZCgyKSkKICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgX2MoInRkIiwgWwogICAgICAgICAgICAgICAgICAgICAgX3ZtLl92KAogICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3MoX3ZtLm1pbnV0ZXNUb0hvdXJzKHRhYi5sYWJvck1pbnV0ZXMpLnRvRml4ZWQoMikpCiAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgICAgIF9jKCJ0ZCIsIFsKICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigKICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLl9zKF92bS5taW51dGVzVG9Ib3Vycyh0YWIub3V0ZXJNaW51dGVzKS50b0ZpeGVkKDIpKQogICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgIF9jKCJ0ciIsIFsKICAgICAgICAgICAgICAgICAgICBfYygidGgiLCBbX3ZtLl92KCLkurrmlbAiKV0pLAogICAgICAgICAgICAgICAgICAgIF9jKCJ0ZCIsIFtfdm0uX3YoX3ZtLl9zKHRhYi5zdW1OdW1zKSldKSwKICAgICAgICAgICAgICAgICAgICBfYygidGQiLCBbX3ZtLl92KF92bS5fcyh0YWIudXNlck51bXMpKV0pLAogICAgICAgICAgICAgICAgICAgIF9jKCJ0ZCIsIFtfdm0uX3YoX3ZtLl9zKHRhYi53ZWlnaHROdW1zKSldKSwKICAgICAgICAgICAgICAgICAgICBfYygidGQiLCBbX3ZtLl92KF92bS5fcyh0YWIub3RoZXJOdW1zKSldKSwKICAgICAgICAgICAgICAgICAgICBfYygidGQiLCBbX3ZtLl92KF92bS5fcyh0YWIubWFuYWdlTnVtcykpXSksCiAgICAgICAgICAgICAgICAgICAgX2MoInRkIiwgW192bS5fdihfdm0uX3ModGFiLnFjTnVtcykpXSksCiAgICAgICAgICAgICAgICAgICAgX2MoInRkIiwgW192bS5fdihfdm0uX3ModGFiLmxhYm9yTnVtcykpXSksCiAgICAgICAgICAgICAgICAgICAgX2MoInRkIiwgW192bS5fdihfdm0uX3ModGFiLm91dGVyTnVtcykpXSksCiAgICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgX2MoIkRheUhvdXJzVXNlclRhYnMiLCB7CiAgICAgICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgICAgICAiZGF5LWhvdXJzIjogX3ZtLmZvcm0sCiAgICAgICAgICAgICAgICAgICJhdHRlbmRhbmNlLWxvZy1saXN0IjogX3ZtLmF0dGVuZGFuY2VMb2dMaXN0LAogICAgICAgICAgICAgICAgICAibWVzLWhvdXJzLWxpc3QiOiBfdm0ubWVzSG91cnNMaXN0LAogICAgICAgICAgICAgICAgICAiZGF5LXVzZXItbGlzdCI6IF92bS5kYXlVc2VyTGlzdC5maWx0ZXIoZnVuY3Rpb24gKGkpIHsKICAgICAgICAgICAgICAgICAgICByZXR1cm4gaS5zYWlsaW5ncyA9PT0gdGFiLnZhbHVlCiAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAid2VpZ2h0LW1pbnV0ZXMtbGlzdCI6IF92bS53ZWlnaHRNaW51dGVzTGlzdCwKICAgICAgICAgICAgICAgICAgIm90aGVyLW1pbnV0ZXMtbGlzdCI6IF92bS5vdGhlck1pbnV0ZXNMaXN0LAogICAgICAgICAgICAgICAgICAibWFuYWdlLW1pbnV0ZXMtbGlzdCI6IF92bS5tYW5hZ2VNaW51dGVzTGlzdCwKICAgICAgICAgICAgICAgICAgInFjLW1pbnV0ZXMtbGlzdCI6IF92bS5xY01pbnV0ZXNMaXN0LAogICAgICAgICAgICAgICAgICAidXNlci1saXN0IjogX3ZtLnVzZXJMaXN0LAogICAgICAgICAgICAgICAgICAicmVzdC1saXN0IjogX3ZtLnJlc3RMaXN0LAogICAgICAgICAgICAgICAgICBzYWlsaW5nczogdGFiLnZhbHVlLAogICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICAgIGNvbXB1dGVJdGVtRGF0YTogX3ZtLmNvbXB1dGVJdGVtRGF0YSwKICAgICAgICAgICAgICAgICAgc2FpbGluZ3NDaGFuZ2U6IF92bS5zYWlsaW5nc0NoYW5nZSwKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIDEKICAgICAgICAgICkKICAgICAgICB9KSwKICAgICAgICAxCiAgICAgICksCiAgICAgIF9jKAogICAgICAgICJlbC1mb3JtIiwKICAgICAgICB7CiAgICAgICAgICByZWY6ICJmb3JtIiwKICAgICAgICAgIHN0YXRpY1N0eWxlOiB7ICJtYXJnaW4tdG9wIjogIjIwcHgiIH0sCiAgICAgICAgICBhdHRyczogewogICAgICAgICAgICBtb2RlbDogX3ZtLmZvcm0sCiAgICAgICAgICAgIHJ1bGVzOiBfdm0ucnVsZXMsCiAgICAgICAgICAgIHNpemU6ICJtaW5pIiwKICAgICAgICAgICAgImxhYmVsLXdpZHRoIjogIjgwcHgiLAogICAgICAgICAgfSwKICAgICAgICB9LAogICAgICAgIFsKICAgICAgICAgIF9jKAogICAgICAgICAgICAiZWwtZm9ybS1pdGVtIiwKICAgICAgICAgICAgeyBhdHRyczogeyBsYWJlbDogIuWkh+azqCIsIHByb3A6ICJyZW1hcmsiIH0gfSwKICAgICAgICAgICAgWwogICAgICAgICAgICAgIF9jKCJlbC1pbnB1dCIsIHsKICAgICAgICAgICAgICAgIGF0dHJzOiB7IHR5cGU6ICJ0ZXh0YXJlYSIgfSwKICAgICAgICAgICAgICAgIG1vZGVsOiB7CiAgICAgICAgICAgICAgICAgIHZhbHVlOiBfdm0uZm9ybS5yZW1hcmssCiAgICAgICAgICAgICAgICAgIGNhbGxiYWNrOiBmdW5jdGlvbiAoJCR2KSB7CiAgICAgICAgICAgICAgICAgICAgX3ZtLiRzZXQoX3ZtLmZvcm0sICJyZW1hcmsiLCAkJHYpCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgIGV4cHJlc3Npb246ICJmb3JtLnJlbWFyayIsCiAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIH0pLAogICAgICAgICAgICBdLAogICAgICAgICAgICAxCiAgICAgICAgICApLAogICAgICAgIF0sCiAgICAgICAgMQogICAgICApLAogICAgICBfYygKICAgICAgICAiZGl2IiwKICAgICAgICB7CiAgICAgICAgICBkaXJlY3RpdmVzOiBbCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICBuYW1lOiAiaGFzLXBlcm1pIiwKICAgICAgICAgICAgICByYXdOYW1lOiAidi1oYXMtcGVybWkiLAogICAgICAgICAgICAgIHZhbHVlOiBbIm1lczpwcm9kdWN0aW9uOmhvdXJzOnB1c2giXSwKICAgICAgICAgICAgICBleHByZXNzaW9uOiAiWydtZXM6cHJvZHVjdGlvbjpob3VyczpwdXNoJ10iLAogICAgICAgICAgICB9LAogICAgICAgICAgXSwKICAgICAgICAgIHN0YXRpY0NsYXNzOiAiZGlhbG9nLWZvb3RlciIsCiAgICAgICAgICBhdHRyczogeyBzbG90OiAiZm9vdGVyIiB9LAogICAgICAgICAgc2xvdDogImZvb3RlciIsCiAgICAgICAgfSwKICAgICAgICBbCiAgICAgICAgICBfYygKICAgICAgICAgICAgImVsLWJ1dHRvbiIsCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICBhdHRyczogeyB0eXBlOiAicHJpbWFyeSIsIHNpemU6ICJtaW5pIiwgbG9hZGluZzogX3ZtLmJ0bkxvYWRpbmcgfSwKICAgICAgICAgICAgICBvbjogeyBjbGljazogX3ZtLnN1Ym1pdEZvcm0gfSwKICAgICAgICAgICAgfSwKICAgICAgICAgICAgW192bS5fdigi56GuIOWumiIpXQogICAgICAgICAgKSwKICAgICAgICAgIF9jKAogICAgICAgICAgICAiZWwtYnV0dG9uIiwKICAgICAgICAgICAgewogICAgICAgICAgICAgIGF0dHJzOiB7IHR5cGU6ICJwcmltYXJ5Iiwgc2l6ZTogIm1pbmkiLCBsb2FkaW5nOiBfdm0uYnRuTG9hZGluZyB9LAogICAgICAgICAgICAgIG9uOiB7IGNsaWNrOiBfdm0ucHVzaEhyRGF0ZSB9LAogICAgICAgICAgICB9LAogICAgICAgICAgICBbX3ZtLl92KCLmjqjpgIHkurrkuoso54+t5YirKSIpXQogICAgICAgICAgKSwKICAgICAgICAgIF9jKAogICAgICAgICAgICAiZWwtYnV0dG9uIiwKICAgICAgICAgICAgeyBhdHRyczogeyBzaXplOiAibWluaSIgfSwgb246IHsgY2xpY2s6IF92bS5jYW5jZWwgfSB9LAogICAgICAgICAgICBbX3ZtLl92KCLlj5Yg5raIIildCiAgICAgICAgICApLAogICAgICAgIF0sCiAgICAgICAgMQogICAgICApLAogICAgXSwKICAgIDEKICApCn0KdmFyIHN0YXRpY1JlbmRlckZucyA9IFtdCnJlbmRlci5fd2l0aFN0cmlwcGVkID0gdHJ1ZQoKZXhwb3J0IHsgcmVuZGVyLCBzdGF0aWNSZW5kZXJGbnMgfQ=="}]}