import request from '@/utils/request'

// 查询劳务日排班列表
export function listLaborScheduleDay(query) {
  return request({
    url: '/hr/laborScheduleDay/list',
    method: 'get',
    params: query
  })
}

// 查询劳务日排班详细
export function getLaborScheduleDay(id) {
  return request({
    url: '/hr/laborScheduleDay/' + id,
    method: 'get'
  })
}

// 新增劳务日排班
export function addLaborScheduleDay(data) {
  return request({
    url: '/hr/laborScheduleDay',
    method: 'post',
    data: data
  })
}

// 修改劳务日排班
export function updateLaborScheduleDay(data) {
  return request({
    url: '/hr/laborScheduleDay',
    method: 'put',
    data: data
  })
}

// 删除劳务日排班
export function delLaborScheduleDay(id) {
  return request({
    url: '/hr/laborScheduleDay/' + id,
    method: 'delete'
  })
}

// 导出劳务日排班
export function exportLaborScheduleDay(query) {
  return request({
    url: '/hr/laborScheduleDay/export',
    method: 'get',
    params: query
  })
}