import request from '@/utils/request'

// 查询原料替换记录列表
export function listSoftwareMaterialRepalceLog(query) {
  return request({
    url: '/software/softwareMaterialRepalceLog/list',
    method: 'get',
    params: query
  })
}

// 查询原料替换记录详细
export function getSoftwareMaterialRepalceLog(id) {
  return request({
    url: '/software/softwareMaterialRepalceLog/' + id,
    method: 'get'
  })
}

// 新增原料替换记录
export function addSoftwareMaterialRepalceLog(data) {
  return request({
    url: '/software/softwareMaterialRepalceLog',
    method: 'post',
    data: data
  })
}

// 修改原料替换记录
export function updateSoftwareMaterialRepalceLog(data) {
  return request({
    url: '/software/softwareMaterialRepalceLog',
    method: 'put',
    data: data
  })
}

// 删除原料替换记录
export function delSoftwareMaterialRepalceLog(id) {
  return request({
    url: '/software/softwareMaterialRepalceLog/' + id,
    method: 'delete'
  })
}

// 导出原料替换记录
export function exportSoftwareMaterialRepalceLog(query) {
  return request({
    url: '/software/softwareMaterialRepalceLog/export',
    method: 'get',
    params: query
  })
}