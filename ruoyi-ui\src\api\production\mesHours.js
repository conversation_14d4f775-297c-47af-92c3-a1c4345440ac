import request from '@/utils/request'

// 查询mes工时同步列表
export function listMesHours(query) {
  return request({
    url: '/production/mesHours/list',
    method: 'get',
    params: query
  })
}

// 查询mes工时同步详细
export function getMesHours(id) {
  return request({
    url: '/production/mesHours/' + id,
    method: 'get'
  })
}

// 新增mes工时同步
export function addMesHours(data) {
  return request({
    url: '/production/mesHours',
    method: 'post',
    data: data
  })
}

// 修改mes工时同步
export function updateMesHours(data) {
  return request({
    url: '/production/mesHours',
    method: 'put',
    data: data
  })
}

// 删除mes工时同步
export function delMesHours(id) {
  return request({
    url: '/production/mesHours/' + id,
    method: 'delete'
  })
}

// 导出mes工时同步
export function exportMesHours(query) {
  return request({
    url: '/production/mesHours/export',
    method: 'get',
    params: query
  })
}

export function allMesHours(query) {
  return request({
    url: '/production/mesHours/all',
    method: 'get',
    params: query
  })
}
