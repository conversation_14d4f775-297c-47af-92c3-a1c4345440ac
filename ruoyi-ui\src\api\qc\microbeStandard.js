import request from '@/utils/request'

// 查询微生物标准列表
export function listMicrobeStandard(query) {
  return request({
    url: '/qc/microbeStandard/list',
    method: 'get',
    params: query
  })
}

// 查询微生物标准详细
export function getMicrobeStandard(md003) {
  return request({
    url: '/qc/microbeStandard/' + md003,
    method: 'get'
  })
}

// 新增微生物标准
export function addMicrobeStandard(data) {
  return request({
    url: '/qc/microbeStandard',
    method: 'post',
    data: data
  })
}

// 修改微生物标准
export function updateMicrobeStandard(data) {
  return request({
    url: '/qc/microbeStandard',
    method: 'put',
    data: data
  })
}

// 删除微生物标准
export function delMicrobeStandard(md003) {
  return request({
    url: '/qc/microbeStandard/' + md003,
    method: 'delete'
  })
}

// 导出微生物标准
export function exportMicrobeStandard(query) {
  return request({
    url: '/qc/microbeStandard/export',
    method: 'get',
    params: query
  })
}

export function asyncMicrobial(query) {
  return request({
    url: '/qc/microbeStandard/asyncMicrobial',
    method: 'get',
    params: query
  })
}

export function asyncBcp(query) {
  return request({
    url: '/qc/microbeStandard/asyncBcp',
    method: 'get',
    params: query
  })
}

export function asyncStandard(query) {
  return request({
    url: '/qc/microbeStandard/asyncStandard',
    method: 'get',
    params: query
  })
}
