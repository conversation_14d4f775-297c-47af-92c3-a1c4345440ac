import request from '@/utils/request'

// 查询进货单单身档列表
export function listErpPurth(query) {
  return request({
    url: '/order/erpPurth/list',
    method: 'get',
    params: query
  })
}

// 查询进货单单身档详细
export function getErpPurth(id) {
  return request({
    url: '/order/erpPurth/' + id,
    method: 'get'
  })
}

// 新增进货单单身档
export function addErpPurth(data) {
  return request({
    url: '/order/erpPurth',
    method: 'post',
    data: data
  })
}

// 修改进货单单身档
export function updateErpPurth(data) {
  return request({
    url: '/order/erpPurth',
    method: 'put',
    data: data
  })
}

// 删除进货单单身档
export function delErpPurth(id) {
  return request({
    url: '/order/erpPurth/' + id,
    method: 'delete'
  })
}

// 导出进货单单身档
export function exportErpPurth(query) {
  return request({
    url: '/order/erpPurth/export',
    method: 'get',
    params: query
  })
}


export function printLable(data) {
  return request({
    url: '/order/erpPurth/printLable',
    method: 'post',
    data,
    responseType: 'blob'
  })
}
