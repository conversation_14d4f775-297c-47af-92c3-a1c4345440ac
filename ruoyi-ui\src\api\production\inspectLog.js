import request from '@/utils/request'

// 查询生产检验记录列表
export function listInspectLog(query) {
  return request({
    url: '/production/inspectLog/list',
    method: 'get',
    params: query
  })
}

// 查询生产检验记录详细
export function getInspectLog(query) {
  return request({
    url: '/production/inspectLog',
    method: 'get',
    params: query,
  })
}

// 新增生产检验记录
export function addInspectLog(data) {
  return request({
    url: '/production/inspectLog',
    method: 'post',
    data: data
  })
}

// 修改生产检验记录
export function updateInspectLog(data) {
  return request({
    url: '/production/inspectLog',
    method: 'put',
    data: data
  })
}

// 删除生产检验记录
export function delInspectLog(id) {
  return request({
    url: '/production/inspectLog/' + id,
    method: 'delete'
  })
}

export function allInspectLog(query) {
  return request({
    url: '/production/inspectLog/all',
    method: 'get',
    params: query
  })
}
