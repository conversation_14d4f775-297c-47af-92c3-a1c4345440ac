import request from '@/utils/request'

// 查询包材订单列表
export function listMaterialOrder(query) {
  return request({
    url: '/resource/materialOrder/list',
    method: 'get',
    params: query
  })
}

// 查询包材订单详细
export function getMaterialOrder(id) {
  return request({
    url: '/resource/materialOrder/' + id,
    method: 'get'
  })
}

// 新增包材订单
export function addMaterialOrder(data) {
  return request({
    url: '/resource/materialOrder',
    method: 'post',
    data: data
  })
}

// 修改包材订单
export function updateMaterialOrder(data) {
  return request({
    url: '/resource/materialOrder',
    method: 'put',
    data: data
  })
}

// 删除包材订单
export function delMaterialOrder(id) {
  return request({
    url: '/resource/materialOrder/' + id,
    method: 'delete'
  })
}

//提交审核
export function submitAudit(data) {
  return request({
    url: '/resource/materialOrder/submitAudit',
    method: 'put',
    data: data
  })
}

//撤销申请
export function cancelAudit(data) {
  return request({
    url: '/resource/materialOrder/cancelAudit',
    method: 'put',
    data: data
  })
}

export function myMaterialOrderList(query) {
  return request({
    url: '/resource/materialOrder/myOrderList',
    method: 'get',
    params: query
  })
}

/**
 * 发货
 */
export function handOutOrder(data) {
  return request({
    url: '/resource/materialOrder/handOut',
    method: 'put',
    data,
  })
}
