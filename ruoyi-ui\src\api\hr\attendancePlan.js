import request from '@/utils/request'

// 查询考勤方案列表
export function listAttendancePlan(query) {
  return request({
    url: '/hr/attendancePlan/list',
    method: 'get',
    params: query
  })
}

// 查询考勤方案详细
export function getAttendancePlan(id) {
  return request({
    url: '/hr/attendancePlan/' + id,
    method: 'get'
  })
}

// 新增考勤方案
export function addAttendancePlan(data) {
  return request({
    url: '/hr/attendancePlan',
    method: 'post',
    data: data
  })
}

// 修改考勤方案
export function updateAttendancePlan(data) {
  return request({
    url: '/hr/attendancePlan',
    method: 'put',
    data: data
  })
}

// 删除考勤方案
export function delAttendancePlan(id) {
  return request({
    url: '/hr/attendancePlan/' + id,
    method: 'delete'
  })
}

// 导出考勤方案
export function exportAttendancePlan(query) {
  return request({
    url: '/hr/attendancePlan/export',
    method: 'get',
    params: query
  })
}
export function getAttendancePlanAll() {
  return request({
    url: '/hr/attendancePlan/all',
    method: 'get'
  })
}
