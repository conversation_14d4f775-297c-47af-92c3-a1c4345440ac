import request from '@/utils/request'

// 查询订单对账对冲日志列表
export function listReconciliaItemRevokeLog(query) {
  return request({
    url: '/order/reconciliaItemRevokeLog/list',
    method: 'get',
    params: query
  })
}

// 查询订单对账对冲日志详细
export function getReconciliaItemRevokeLog(id) {
  return request({
    url: '/order/reconciliaItemRevokeLog/' + id,
    method: 'get'
  })
}

// 新增订单对账对冲日志
export function addReconciliaItemRevokeLog(data) {
  return request({
    url: '/order/reconciliaItemRevokeLog',
    method: 'post',
    data: data
  })
}

// 修改订单对账对冲日志
export function updateReconciliaItemRevokeLog(data) {
  return request({
    url: '/order/reconciliaItemRevokeLog',
    method: 'put',
    data: data
  })
}

// 删除订单对账对冲日志
export function delReconciliaItemRevokeLog(id) {
  return request({
    url: '/order/reconciliaItemRevokeLog/' + id,
    method: 'delete'
  })
}

// 导出订单对账对冲日志
export function exportReconciliaItemRevokeLog(query) {
  return request({
    url: '/order/reconciliaItemRevokeLog/export',
    method: 'get',
    params: query
  })
}

export function allReconciliaItemRevokeLog(query) {
  return request({
    url: '/order/reconciliaItemRevokeLog/all',
    method: 'get',
    params: query
  })
}

export function revokeReconcilia(data) {
  return request({
    url: '/order/reconciliaItemRevokeLog/revoke',
    method: 'put',
    data: data
  })
}
