import request from '@/utils/request'

// 查询工程师工作记录列表
export function listEngineerWorkRecord(query) {
  return request({
    url: '/software/engineerWorkRecord/list',
    method: 'get',
    params: query
  })
}

// 查询工程师工作记录详细
export function getEngineerWorkRecord(id) {
  return request({
    url: '/software/engineerWorkRecord/' + id,
    method: 'get'
  })
}

// 新增工程师工作记录
export function addEngineerWorkRecord(data) {
  return request({
    url: '/software/engineerWorkRecord',
    method: 'post',
    data: data
  })
}

// 修改工程师工作记录
export function updateEngineerWorkRecord(data) {
  return request({
    url: '/software/engineerWorkRecord',
    method: 'put',
    data: data
  })
}

// 删除工程师工作记录
export function delEngineerWorkRecord(id) {
  return request({
    url: '/software/engineerWorkRecord/' + id,
    method: 'delete'
  })
}

// 导出工程师工作记录
export function exportEngineerWorkRecord(query) {
  return request({
    url: '/software/engineerWorkRecord/export',
    method: 'get',
    params: query
  })
}

// 获取工程师工时统计数据
export function getEngineerWorkStats(query) {
  return request({
    url: '/software/engineerWorkRecord/dashboardStats',
    method: 'get',
    params: query
  })
}

// 获取工程师近七天（七天前-今天）工作记录
export function getEngineerWeeklyWorkHours(userId) {
  return request({
    url: '/software/engineerWorkRecord/weekly/' + userId,
    method: 'get'
  })
}