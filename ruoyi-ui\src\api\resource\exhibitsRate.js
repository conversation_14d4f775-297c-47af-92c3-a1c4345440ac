import request from '@/utils/request'

// 查询展厅评价列表
export function listExhibitsRate(query) {
  return request({
    url: '/resource/exhibitsRate/list',
    method: 'get',
    params: query
  })
}

// 查询展厅评价详细
export function getExhibitsRate(id) {
  return request({
    url: '/resource/exhibitsRate/' + id,
    method: 'get'
  })
}

// 新增展厅评价
export function addExhibitsRate(data) {
  return request({
    url: '/resource/exhibitsRate',
    method: 'post',
    data: data
  })
}

// 修改展厅评价
export function updateExhibitsRate(data) {
  return request({
    url: '/resource/exhibitsRate',
    method: 'put',
    data: data
  })
}

// 删除展厅评价
export function delExhibitsRate(id) {
  return request({
    url: '/resource/exhibitsRate/' + id,
    method: 'delete'
  })
}

// 导出展厅评价
export function exportExhibitsRate(query) {
  return request({
    url: '/resource/exhibitsRate/export',
    method: 'get',
    params: query
  })
}