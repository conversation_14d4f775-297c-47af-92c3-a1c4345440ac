import request from '@/utils/request'

// 查询标准损耗率列表
export function listStandardLoss(query) {
  return request({
    url: '/order/standardLoss/list',
    method: 'get',
    params: query
  })
}

// 查询标准损耗率详细
export function getStandardLoss(id) {
  return request({
    url: '/order/standardLoss/' + id,
    method: 'get'
  })
}

// 新增标准损耗率
export function addStandardLoss(data) {
  return request({
    url: '/order/standardLoss',
    method: 'post',
    data: data
  })
}

// 修改标准损耗率
export function updateStandardLoss(data) {
  return request({
    url: '/order/standardLoss',
    method: 'put',
    data: data
  })
}

// 删除标准损耗率
export function delStandardLoss(id) {
  return request({
    url: '/order/standardLoss/' + id,
    method: 'delete'
  })
}

// 导出标准损耗率
export function exportStandardLoss(query) {
  return request({
    url: '/order/standardLoss/export',
    method: 'get',
    params: query
  })
}