{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\baseTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\baseTable.vue", "mtime": 1753954679642}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["baseTable.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "baseTable.vue", "sourceRoot": "src/views/production/dayHours", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-divider content-position=\"left\" >{{title}}</el-divider>\r\n\r\n    <div class=\"table-wrapper\" >\r\n      <table class=\"base-table small-table\" >\r\n        <tr>\r\n          <th style=\"width: 50px\" >\r\n            <i class=\"el-icon-circle-plus-outline\" @click=\"showUser\" />\r\n          </th>\r\n          <th style=\"width: 120px\" >工号</th>\r\n          <th style=\"width: 100px\" >姓名</th>\r\n          <th style=\"width: 200px\" >上工时间</th>\r\n          <th style=\"width: 200px\" >下工时间</th>\r\n          <th style=\"width: 120px\" >考勤时长</th>\r\n          <th style=\"width: 120px\" >休息工时</th>\r\n          <th style=\"width: 120px\" >工资工时</th>\r\n          <th style=\"width: 100px\" >\r\n            修正工时\r\n            <el-tooltip >\r\n              <div slot=\"content\">\r\n                修正过后以修正的工时为准\r\n              </div>\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </th>\r\n          <th >备注</th>\r\n        </tr>\r\n        <tr v-for=\"(item,z) in userArray.filter(i=>this.sailings === i.sailings)\" :key=\"z\">\r\n          <td>\r\n            <i class=\"el-icon-remove-outline\" @click=\"delItem(item.userCode)\" />\r\n          </td>\r\n          <td>\r\n            <span style=\"color: #00afff;cursor: pointer\" @click=\"attendanceLog(item.userId)\" >{{item.userCode}}</span>\r\n          </td>\r\n          <td>{{item.nickName}}</td>\r\n          <td>\r\n            <el-date-picker\r\n              clearable\r\n              v-model=\"item.startTime\"\r\n              type=\"datetime\"\r\n              value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n              size=\"mini\"\r\n              style=\"width: 180px\"\r\n              @change=\"computeMinutes(item)\"\r\n            />\r\n          </td>\r\n          <td>\r\n            <el-date-picker\r\n              clearable\r\n              v-model=\"item.endTime\"\r\n              type=\"datetime\"\r\n              value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n              size=\"mini\"\r\n              style=\"width: 180px\"\r\n              @change=\"computeMinutes(item)\"\r\n            />\r\n          </td>\r\n          <td>{{minutesToHours(item.minutes).toFixed(2)}}</td>\r\n          <td>\r\n            <span style=\"color: #00afff;cursor: pointer\" @click=\"showRestMinutes(item)\" >\r\n              {{minutesToHours(item.restMinutes).toFixed(2)}}\r\n            </span>\r\n          </td>\r\n          <td>{{minutesToHours(item.wagesMinutes).toFixed(2)}}</td>\r\n          <td >\r\n            <el-input v-model=\"item.finalMinutes\" autosize size=\"mini\" @input=\"$emit('computeItemData')\" />\r\n          </td>\r\n          <td>\r\n            <el-input v-model=\"item.remark\" autosize size=\"mini\" />\r\n          </td>\r\n        </tr>\r\n      </table>\r\n    </div>\r\n\r\n    <el-dialog title=\"选择用户\" :visible.sync=\"userOpen\" width=\"400px\" :close-on-click-modal=\"false\"  append-to-body>\r\n      <el-select v-model=\"currentUserId\" filterable @change=\"addItem\" >\r\n        <el-option\r\n          v-for=\"item in userList\"\r\n          :key=\"item.userId\"\r\n          :label=\"item.nickName\"          :value=\"item.userId\"\r\n        />\r\n      </el-select>\r\n    </el-dialog>\r\n\r\n    <el-dialog :close-on-click-modal=\"false\" :visible.sync=\"attendanceLogOpen\" append-to-body width=\"600px\">\r\n      <table class=\"base-table small-table\">\r\n        <tr>\r\n          <th style=\"width: 320px\">打卡地址</th>\r\n          <th style=\"width: 180px\">打卡时间</th>\r\n        </tr>\r\n        <tr v-for=\"item in attendanceLogList\" :key=\"item.id\" >\r\n          <td>{{item.userAddress}}</td>\r\n          <td>{{item.userCheckTime}}</td>\r\n        </tr>\r\n      </table>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"休息工时(请录入分钟数,会转成小时数)\" :visible.sync=\"minutesOpen\" width=\"400px\" :close-on-click-modal=\"false\"  append-to-body>\r\n      <el-input v-model=\"currentRow.restMinutes\" type=\"number\" size=\"mini\" @input=\"computeMinutes(currentRow)\" >\r\n        <template #append >分钟</template>\r\n      </el-input>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n<script >\r\nimport {\r\n  calculateIntersectionMinutes,\r\n  diffMinutes,\r\n  findClosestTimeString,\r\n  roundDownToHalfHour\r\n} from \"@/utils/production/time\";\r\nimport {allAttendanceLog} from \"@/api/hr/attendanceLog\";\r\nimport Template from \"@/views/filemanage/template/index.vue\";\r\n\r\nexport default {\r\n  name: 'dayHoursBaseTable',\r\n  components: {Template},\r\n  props: {\r\n    dayHours: {\r\n      type: Object,\r\n      required: true,\r\n    },\r\n    sailings: {\r\n      type: String,\r\n      required: true,\r\n    },\r\n    title: {\r\n      type: String,\r\n      required: true,\r\n    },\r\n    userArray: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    userList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    attendanceLogList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    restList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      userOpen: false,\r\n      attendanceLogOpen: false,\r\n      minutesOpen: false,\r\n      currentUserId: null,\r\n      currentRow: {},\r\n    }\r\n  },\r\n  async created() {\r\n  },\r\n  methods: {\r\n    async showRestMinutes(item) {\r\n      this.currentRow = item\r\n      this.minutesOpen = true\r\n    },\r\n    async attendanceLog(userId) {\r\n      const workDate = this.dayHours.workDate\r\n      if(workDate) {\r\n        const searchDateArray = [workDate,]\r\n        searchDateArray.push(this.moment(workDate).add(1, 'days').format('YYYY-MM-DD'))\r\n        const params = {\r\n          userId,\r\n          searchDateArray\r\n        }\r\n        try {\r\n          this.btnLoading = true\r\n          const attendanceLogList = await allAttendanceLog(params)\r\n          for (const item of attendanceLogList) {\r\n            if(item.userCheckTime) {\r\n              item.userCheckTime = this.moment(item.userCheckTime).format('YYYY-MM-DD HH:mm:ss')\r\n            }\r\n          }\r\n          this.attendanceLogList = attendanceLogList\r\n          this.btnLoading = false\r\n          this.attendanceLogOpen = true\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n    showUser() {\r\n      this.currentUserId = null\r\n      this.userOpen = true\r\n    },\r\n    addItem() {\r\n      const arr = this.userList.filter(i=> i.userId === this.currentUserId)\r\n      if(arr && arr[0]) {\r\n        if(!this.userArray.map(i=>i.userId).includes(this.currentUserId)) {\r\n          const attendanceArr = this.attendanceLogList.filter(i=> i.userId === this.currentUserId).sort((a,b)=> a.userCheckTime - b.userCheckTime)\r\n          if(attendanceArr && attendanceArr[1]){ //至少有两个\r\n            const timesArray = attendanceArr.map(i=> this.moment(i.userCheckTime).format('YYYY-MM-DD HH:mm:ss'))\r\n            const startDate = this.moment(this.dayHours.workDate).format('YYYY-MM-DD')\r\n            let upStandTime = startDate + ' 08:30:00'\r\n            let downStandTime =  startDate + ' 20:30:00'\r\n            if(this.sailings === '1') {\r\n              upStandTime = startDate + ' 20:30:00'\r\n              downStandTime = this.moment(startDate).add(1, 'days').format('YYYY-MM-DD') + ' 08:30:00'\r\n            }\r\n            let upTime = findClosestTimeString(timesArray,upStandTime)\r\n            const downTime = findClosestTimeString(timesArray,downStandTime)\r\n\r\n            if(upTime && downTime) {\r\n              if(upTime < upStandTime ) {//如果早于8点半,按8点半算\r\n                upTime = upStandTime\r\n              }\r\n              const minutes = this.moment(downTime).diff(upTime,'minutes')\r\n              const workPeriods = [\r\n                {\r\n                  start: this.moment(upTime).format('HH:mm'),\r\n                  end: this.moment(downTime).format('HH:mm'),\r\n                },\r\n              ]\r\n              const restMinutes = calculateIntersectionMinutes(workPeriods,this.restList)\r\n              const wagesMinutes = this.subtract(minutes,restMinutes).toNumber()\r\n              if(minutes > 0) {\r\n                this.userArray.push({\r\n                  userId: arr[0].userId,\r\n                  userCode: arr[0].userCode,\r\n                  nickName: arr[0].nickName,\r\n                  startTime: upTime,\r\n                  endTime: downTime,\r\n                  sailings: this.sailings,\r\n                  minutes,\r\n                  restMinutes,\r\n                  wagesMinutes: roundDownToHalfHour(wagesMinutes),\r\n                })\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      this.$emit('computeItemData')\r\n      this.userOpen = false\r\n    },\r\n    delItem(userCode) {\r\n      const index = this.userArray.findIndex(i=> i.userCode === userCode)\r\n      this.userArray.splice(index,1)\r\n      this.$emit('computeItemData')\r\n    },\r\n    computeMinutes(row) {\r\n      row.minutes = diffMinutes(row.endTime,row.startTime)\r\n      row.wagesMinutes = roundDownToHalfHour(this.subtract(row.minutes,row.restMinutes).toNumber())\r\n      this.$emit('computeItemData')\r\n    },\r\n  },\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n\r\n</style>\r\n"]}]}