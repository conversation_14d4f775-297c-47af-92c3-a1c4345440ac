{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\sop\\makeUp\\save.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\sop\\makeUp\\save.vue", "mtime": 1753954679648}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\babel.config.js", "mtime": 1743382537964}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_makeUp", "require", "_project", "_formula", "_materialFormula", "_equipment", "_base", "_interopRequireDefault", "_productionPg", "_list", "_bcpProductivityArray", "_save", "name", "components", "MakeUpBSave", "BcpProductivityArray", "StabilityList", "ProductionPg", "ProjectBaseView", "props", "readonly", "type", "Boolean", "default", "computed", "sumPercentage", "sum", "$big", "_iterator", "_createForOfIteratorHelper2", "materialArray", "_step", "s", "n", "done", "item", "value", "percentage", "add", "err", "e", "f", "toNumber", "data", "loading", "btnLoading", "open", "textOpen", "fullscreenFlag", "title", "form", "rules", "projectNo", "required", "message", "nums", "formulaId", "projectList", "formulaList", "currentProject", "currentTab", "equipmentList", "aidedEquipmentList", "processArray", "productivityArray", "<PERSON><PERSON><PERSON><PERSON>", "equipmentTypeOptions", "cycleOptions", "riskLevelOptions", "bcpCode", "currentDiagram", "bcpArray", "conclusionOptions", "label", "erpDataList", "typeOptions", "currentRow", "px<PERSON><PERSON>", "pxArray", "whetherOptions", "files", "stepForm", "stepRules", "processText", "equipmentId", "created", "_this", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "allEquipment", "typeId", "sent", "stop", "methods", "resetStepForm", "resetForm", "showDialog", "row", "buildStepArray", "Object", "assign", "array", "splitSteps", "_iterator2", "_step2", "addArgsItem", "equipmentArgs", "text", "split", "reduce", "acc", "line", "trimmedLine", "trim", "test", "push", "length", "close", "id", "_this2", "_callee2", "_callee2$", "_context2", "showBProcess", "_this3", "_callee3", "makeUpBSave", "_callee3$", "_context3", "materialCode", "$nextTick", "$refs", "reset", "makeUpId", "labCode", "init", "processToEquipment", "_this4", "_callee4", "equipmentIdSet", "_iterator3", "_step3", "p", "_iterator5", "_step5", "_iterator4", "_step4", "_callee4$", "_context4", "Set", "map", "i", "includes", "weight", "undefined", "max", "min", "personNums", "hours", "productivity", "hourRate", "searchFormulaList", "_this5", "_callee5", "laboratoryCode", "res", "_callee5$", "_context5", "allFormula", "countMakeUpByLabCode", "code", "msgError", "computeNums", "_iterator6", "_step6", "o", "multiply", "divide", "process", "step", "materialIds", "materialId", "keyPoints", "temperature", "mpa", "jz", "jzTime", "nj", "njTime", "wj", "zk", "time", "delItem", "splice", "addPxItem", "dept", "isLj", "suggestion", "pg", "generateProcessArray", "_this6", "_callee6", "tempArray", "_iterator7", "_step7", "m", "_iterator9", "_step9", "sub", "categorySet", "_i", "_tempArray", "temp", "_iterator8", "_step8", "_loop", "_callee6$", "_context7", "erpCode", "subItem", "category", "replace", "substring", "firstPercentage", "_loop$", "_context6", "filter", "sort", "a", "b", "processDesc", "aidedArray", "<PERSON><PERSON><PERSON>", "t1", "finish", "localeCompare", "percentageStyle", "_this7", "color", "addSubItem", "delSubItem", "formulaChange", "_this8", "_callee7", "formulaRes", "_iterator10", "_step10", "bcp", "materialIdSet", "_iterator11", "_step11", "_iterator12", "_step12", "_loop2", "_callee7$", "_context9", "getFormula", "formulaCode", "customerName", "productName", "jcXmJson", "JSON", "parse", "zsStandard", "inspectionArray", "allMaterialFormula", "arr", "subArray", "_loop2$", "_context8", "searchProject", "_this9", "_callee8", "_res", "project", "_callee8$", "_context10", "getProjectByNo", "projectId", "cancel", "$parent", "diagramId", "meshesNums", "pxUser", "pxDate", "pzUser", "pzDate", "qcCode", "byQr", "byStatus", "jyTime", "zlJy", "clFa", "cpJs", "cpTx", "tbYl", "zySx", "jy", "gxs", "summary", "_this10", "_callee9", "_callee9$", "_context11", "getMakeUp", "submitForm", "_this11", "_callee10", "_iterator13", "_step13", "_iterator14", "_step14", "_res2", "_callee10$", "_context12", "validate", "abrupt", "t0", "stringify", "updateMakeUp", "msgSuccess", "t2", "addMakeUp", "t3"], "sources": ["src/views/sop/makeUp/save.vue"], "sourcesContent": ["<template>\r\n  <div v-loading=\"loading\" >\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" size=\"mini\" label-width=\"100px\">\r\n      <div :class=\"readonly?'mask':''\" >\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <el-form-item prop=\"labCode\" label=\"实验室编码\" >\r\n              <el-input v-model=\"form.labCode\" >\r\n                <template #append >\r\n                  <el-tooltip content=\"根据实验室编码 带出 配方编码\" >\r\n                    <el-button icon=\"el-icon-search\" @click=\"searchFormulaList\" />\r\n                  </el-tooltip>\r\n                </template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item prop=\"formulaId\" >\r\n              <template #label>\r\n                配方编码\r\n                <el-tooltip content=\"根据配方编码 带出 称量记录、配方分相、半成品检验记录\" >\r\n                  <i class=\"el-icon-question\" />\r\n                </el-tooltip>\r\n              </template>\r\n              <el-select v-model=\"form.formulaId\" filterable @change=\"formulaChange\" >\r\n                <el-option\r\n                  v-for=\"item in formulaList\"\r\n                  :key=\"item.id\"\r\n                  :label=\"item.formulaCode\"\r\n                  :value=\"item.id\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item prop=\"type\">\r\n              <template #label>\r\n                工艺类型\r\n              </template>\r\n              <el-select v-model=\"form.type\" >\r\n                <el-option\r\n                  v-for=\"item in typeOptions\"\r\n                  :key=\"item.value\"\r\n                  :value=\"item.value\"\r\n                  :label=\"item.label\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row >\r\n        <el-row :gutter=\"20\" >\r\n          <el-col :span=\"8\">\r\n            <el-form-item prop=\"nums\">\r\n              <template #label>\r\n                配制量\r\n                <el-tooltip content=\"根据 此处的配制量 和 称量记录中的配制比例 计算 称量记录中 单个物料的配制量\" >\r\n                  <i class=\"el-icon-question\" />\r\n                </el-tooltip>\r\n              </template>\r\n              <el-input v-model=\"form.nums\" @input=\"computeNums\" >\r\n                <template #append>kg</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"客户\" prop=\"customerName\">\r\n              {{form.customerName}}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"产品名称\" prop=\"productName\">\r\n              {{form.productName}}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\" >\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"项目\" prop=\"projectNo\">\r\n              {{form.projectNo}}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"培训人\" prop=\"pxUser\">\r\n              <el-input v-model=\"form.pxUser\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"培训日期\" prop=\"pxDate\">\r\n              <el-date-picker\r\n                clearable\r\n                v-model=\"form.pxDate\"\r\n                type=\"date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\" >\r\n<!--          <el-col :span=\"8\">-->\r\n<!--            <el-form-item label=\"配制人\" prop=\"pzUser\">-->\r\n<!--              <el-input v-model=\"form.pzUser\" />-->\r\n<!--            </el-form-item>-->\r\n<!--          </el-col>-->\r\n<!--          <el-col :span=\"8\">-->\r\n<!--            <el-form-item label=\"配制日期\" prop=\"pzDate\">-->\r\n<!--              <el-date-picker-->\r\n<!--                clearable-->\r\n<!--                v-model=\"form.pzDate\"-->\r\n<!--                type=\"date\"-->\r\n<!--                value-format=\"yyyy-MM-dd\"-->\r\n<!--              />-->\r\n<!--            </el-form-item>-->\r\n<!--          </el-col>-->\r\n          <el-col :span=\"8\" v-if=\"form.qcCode\" >\r\n            <el-form-item prop=\"qcCode\" label=\"文件受控编号\" >\r\n              {{form.qcCode}}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <el-divider content-position=\"left\" >工艺详情</el-divider>\r\n\r\n      <el-tabs v-model=\"currentTab\" >\r\n        <el-tab-pane key=\"record\" label=\"称量记录\" lazy name=\"record\" >\r\n          <div class=\"table-wrapper\">\r\n            <table class=\"base-table small-table\">\r\n              <tr>\r\n                <th style=\"width: 60px\" >序号</th>\r\n                <th style=\"width: 100px\" >物料代码</th>\r\n                <th style=\"width: 180px\" >物料名称</th>\r\n                <th style=\"width: 180px\" >配制比例</th>\r\n                <th style=\"width: 180px\" >配制量(kg)</th>\r\n              </tr>\r\n              <tr v-for=\"(item,i) in materialArray\" :key=\"i\" >\r\n                <td >{{i+1}}</td>\r\n                <td>{{item.erpCode}}</td>\r\n                <td>{{item.materialCode}}</td>\r\n                <td><span :style=\"percentageStyle(item)\">{{item.percentage}}%</span></td>\r\n                <td>{{item.nums}}</td>\r\n              </tr>\r\n            </table>\r\n          </div>\r\n        </el-tab-pane>\r\n        <el-tab-pane key=\"splitting\" label=\"配方分相\" lazy name=\"splitting\" >\r\n          <div class=\"table-wrapper\">\r\n            <table class=\"base-table small-table\">\r\n              <tr>\r\n                <th style=\"width: 100px\" >物料代码</th>\r\n                <th style=\"width: 180px\" >物料名称</th>\r\n                <th style=\"width: 180px\" >比例</th>\r\n                <th >研发分相</th>\r\n              </tr>\r\n              <tr v-for=\"(item,i) in materialArray\" :key=\"i\" :class=\"readonly?'mask':''\">\r\n                <td>{{item.erpCode}}</td>\r\n                <td>\r\n                  {{item.materialCode}}\r\n                  (<span :style=\"percentageStyle(item)\">{{item.percentage}}%</span>)\r\n                  <i class=\"el-icon-circle-plus-outline\" @click=\"addSubItem(item)\" />\r\n                </td>\r\n                <td :colspan=\"2\" style=\"padding: 0\" >\r\n                  <table class=\"base-table\" style=\"margin: 0\">\r\n                    <tr v-for=\"(sub,i) in item.array\" :key=\"i\" >\r\n                      <td style=\"width: 180px\" >\r\n                        <div style=\"display: flex;align-items: center\">\r\n                          <i class=\"el-icon-remove-outline\" @click=\"delSubItem(item,i)\" />\r\n                          <el-input v-model=\"sub.percentage\" size=\"mini\" >\r\n                            <template #append >%</template>\r\n                          </el-input>\r\n                        </div>\r\n                      </td>\r\n                      <td>\r\n                        <el-input v-model=\"sub.subItem\" size=\"mini\" v-input.num_alp />\r\n                      </td>\r\n                    </tr>\r\n                  </table>\r\n                </td>\r\n              </tr>\r\n              <tr>\r\n                <th >合计</th>\r\n                <td >{{sumPercentage}}<span v-if=\"sumPercentage\">%</span></td>\r\n                <td ></td>\r\n                <td ></td>\r\n              </tr>\r\n            </table>\r\n          </div>\r\n        </el-tab-pane>\r\n        <el-tab-pane key=\"base\" label=\"工艺详情\" lazy name=\"base\" >\r\n          <el-tooltip v-if=\"!readonly\" content=\"重新生成工艺详情\" >\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" :loading=\"btnLoading\" @click=\"generateProcessArray\" />\r\n          </el-tooltip>\r\n\r\n          <div class=\"table-wrapper\">\r\n            <table class=\"base-table small-table\">\r\n              <thead >\r\n                <tr>\r\n                  <th style=\"width: 120px\" class=\"nth1\" >工段</th>\r\n                  <th style=\"width: 120px\" class=\"nth2\" >主分相</th>\r\n                  <th style=\"width: 120px\" class=\"nth3\" >子分相</th>\r\n                  <th style=\"width: 120px\" class=\"nth4\" >物料代码</th>\r\n                  <th style=\"width: 120px\" class=\"nth5\" >物料名称</th>\r\n                  <th style=\"width: 120px\" class=\"nth6\" >比例</th>\r\n                  <th style=\"width: 120px\" class=\"nth7\" >首次投料比例</th>\r\n                  <th style=\"width: 1560px;\" >设备参数</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                <tr v-for=\"(item,index) in processArray\" :key=\"index\" >\r\n                  <td v-if=\"index === 0\" :rowspan=\"processArray.length\" class=\"nth1\" >配制段</td>\r\n                  <td class=\"nth2\" >{{item.category}}</td>\r\n                  <td class=\"nth3\" :colspan=\"5\" style=\"padding: 0\">\r\n                    <table class=\"base-table small-table\" style=\"margin: 0\" >\r\n                      <tr v-for=\"(m,z) in item.materialArray\" :key=\"z\" >\r\n                        <td>{{m.subItem}}</td>\r\n                        <td>{{m.erpCode}}</td>\r\n                        <td>\r\n                          <span v-if=\"m.materialCode.startsWith('B') && form.id\"\r\n                                style=\"color: #00afff;cursor: pointer\"\r\n                                @click=\"showBProcess(m)\">{{m.materialCode}}</span>\r\n                          <span v-else >{{m.materialCode}}</span>\r\n                        </td>\r\n                        <td>{{m.percentage}}%</td>\r\n                        <td>\r\n                          <span v-if=\"readonly\" >{{m.firstPercentage}}%</span>\r\n                          <el-input v-else v-model=\"m.firstPercentage\" size=\"mini\" type=\"number\" >\r\n                            <template #append>%</template>\r\n                          </el-input>\r\n                        </td>\r\n                      </tr>\r\n                    </table>\r\n                  </td>\r\n                  <td style=\"padding: 0\">\r\n                    <table class=\"base-table small-table\" style=\"margin: 0\" :class=\"readonly?'mask':''\" >\r\n                      <tr>\r\n                        <th style=\"width: 50px\" >\r\n                          <i class=\"el-icon-circle-plus-outline\" v-if=\"!readonly\" @click=\"addArgsItem(item.equipmentArgs,item.materialArray)\" />\r\n                        </th>\r\n                        <th style=\"width: 80px\" >\r\n                          步骤\r\n                          <i class=\"el-icon-s-help\" v-if=\"!readonly\" @click=\"showDialog(item)\" />\r\n                        </th>\r\n                        <th style=\"width: 120px\" >原料</th>\r\n                        <th style=\"width: 300px\" >设备</th>\r\n                        <th style=\"width: 180px\" >工艺描述</th>\r\n                        <th style=\"width: 100px\" >温度(℃)</th>\r\n                        <th style=\"width: 100px\" >均质(rpm/min)</th>\r\n                        <th style=\"width: 100px\" >均质时间(min)</th>\r\n                        <th style=\"width: 100px\" >搅拌(转/min)</th>\r\n                        <th style=\"width: 100px\" >搅拌时间(min)</th>\r\n                        <th style=\"width: 100px\" >时长(min)</th>\r\n                        <th style=\"width: 100px\" >真空mpa</th>\r\n                      </tr>\r\n                      <tr v-for=\"(d,di) in item.equipmentArgs\" :key=\"di\">\r\n                        <td>\r\n                          <i class=\"el-icon-remove-outline\" @click=\"delItem(item.equipmentArgs,di)\" />\r\n                        </td>\r\n                        <td >\r\n                          <el-input v-model=\"d.step\" size=\"mini\" type=\"number\" />\r\n                        </td>\r\n                        <td >\r\n                          <el-select v-model=\"d.materialIds\" filterable multiple size=\"mini\" >\r\n                            <el-option\r\n                              v-for=\"m in item.materialArray\"\r\n                              :key=\"m.materialId\"\r\n                              :label=\"m.materialCode + '|' + m.subItem\"\r\n                              :value=\"m.materialId\" />\r\n                          </el-select>\r\n                        </td>\r\n                        <td>\r\n                          <el-select v-model=\"d.equipmentId\" filterable size=\"mini\" style=\"width: 280px\" >\r\n                            <el-option\r\n                              v-for=\"e in equipmentList\"\r\n                              :key=\"e.id\"\r\n                              :value=\"e.id\"\r\n                              :label=\"e.name\" />\r\n                          </el-select>\r\n                        </td>\r\n                        <td >\r\n                          <span v-if=\"readonly\">{{d.process}}</span>\r\n                          <el-input\r\n                            v-else\r\n                            v-model=\"d.process\"\r\n                            autosize\r\n                            size=\"mini\"\r\n                            type=\"textarea\"\r\n                          />\r\n                        </td>\r\n                        <td ><el-input v-model=\"d.temperature\" size=\"mini\" /></td>\r\n\r\n                        <td ><el-input v-model=\"d.jz\" size=\"mini\" /></td>\r\n                        <td ><el-input v-model=\"d.jzTime\" size=\"mini\" /></td>\r\n                        <td ><el-input v-model=\"d.nj\" size=\"mini\" /></td>\r\n                        <td ><el-input v-model=\"d.njTime\" size=\"mini\" /></td>\r\n                        <td ><el-input v-model=\"d.time\" size=\"mini\" /></td>\r\n                        <td ><el-input v-model=\"d.mpa\" size=\"mini\" /></td>\r\n                      </tr>\r\n                    </table>\r\n                  </td>\r\n                </tr>\r\n                <tr >\r\n                  <td >出料</td>\r\n                  <td :colspan=\"9\" >\r\n                    <el-row :gutter=\"20\" >\r\n                      <el-col :span=\"4\" >\r\n                        <div class=\"cell-wrapper\">\r\n                          <div class=\"label\">温度</div>\r\n                          <div class=\"content\">\r\n                            <el-input v-model=\"form.temperature\" size=\"mini\" />\r\n                          </div>\r\n                        </div>\r\n                      </el-col>\r\n                      <el-col :span=\"4\" >\r\n                        <div class=\"cell-wrapper\">\r\n                          <div class=\"label\">滤网目数</div>\r\n                          <div class=\"content\">\r\n                            <el-input v-model=\"form.meshesNums\" size=\"mini\" />\r\n                          </div>\r\n                        </div>\r\n                      </el-col>\r\n                      <el-col :span=\"4\" >\r\n                        <span style=\"color: #F56C6C\">加*号的为关键工艺控制点</span>\r\n                      </el-col>\r\n                      <el-col :span=\"12\" ></el-col>\r\n                    </el-row>\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-tab-pane>\r\n        <el-tab-pane key=\"productivity\" label=\"人工/产能\" lazy name=\"productivity\">\r\n          <el-tooltip v-if=\"!readonly\" content=\"工艺带入设备\" >\r\n            <i @click=\"processToEquipment\" style=\"margin-right: 5px\" class=\"el-icon-refresh\" />\r\n          </el-tooltip>\r\n          <BcpProductivityArray :data-array=\"productivityArray\" :equipment-list=\"equipmentList\" />\r\n        </el-tab-pane>\r\n        <el-tab-pane key=\"bcp\" label=\"半成品检验标准\" lazy name=\"bcp\" >\r\n\r\n          <div class=\"table-wrapper\" >\r\n            <table class=\"base-table small-table\">\r\n              <tr>\r\n                <th style=\"width: 120px\">类型</th>\r\n                <th style=\"width: 120px\">检测项目</th>\r\n                <th style=\"width: 240px\">研发标准</th>\r\n                <th style=\"width: 320px\">标准值</th>\r\n              </tr>\r\n              <tr v-for=\"(item,index) in bcpArray\" :key=\"item.id\" :class=\"readonly?'mask':''\" >\r\n                <td>{{item.type}}</td>\r\n                <td>{{item.label}}</td>\r\n                <td>{{item.standard}}</td>\r\n                <td>{{item.standardVal}}</td>\r\n              </tr>\r\n            </table>\r\n          </div>\r\n        </el-tab-pane>\r\n        <el-tab-pane key=\"assess\" label=\"配制可行性评估\" lazy name=\"assess\" >\r\n          <ProductionPg :pg-tabs=\"findingArray\" :form=\"form\" />\r\n        </el-tab-pane>\r\n        <el-tab-pane key=\"stability\" label=\"产品稳定性报告\" lazy name=\"stability\" >\r\n          <StabilityList v-if=\"form.labCode\" :lab-no=\"form.labCode\" />\r\n        </el-tab-pane>\r\n        <el-tab-pane key=\"new\" label=\"新品培训纪要\" lazy name=\"new\" >\r\n          <el-row :gutter=\"20\" >\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"标样确认\" prop=\"byQr\">\r\n                <el-input v-model=\"pxJson.byQr\" />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"标样状态\" prop=\"byStatus\">\r\n                <el-input v-model=\"pxJson.byStatus\" />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\" >\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"工程师\" prop=\"gxs\" >\r\n                <el-input v-model=\"pxJson.gxs\" />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"签样时间\" prop=\"jyTime\">\r\n                <el-date-picker\r\n                  clearable\r\n                  v-model=\"pxJson.jyTime\"\r\n                  type=\"datetime\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-form-item label=\"质量建议\" prop=\"zlJy\">\r\n            <el-input v-model=\"pxJson.zlJy\" type=\"textarea\" autosize />\r\n          </el-form-item>\r\n          <el-form-item label=\"中试结果及处理方案\" label-width=\"150px\" prop=\"clFa\">\r\n            <el-input v-model=\"pxJson.clFa\" type=\"textarea\" autosize />\r\n          </el-form-item>\r\n          <el-form-item label=\"产品介绍\" prop=\"cpJs\">\r\n            <el-input v-model=\"pxJson.cpJs\" type=\"textarea\" autosize />\r\n          </el-form-item>\r\n          <el-form-item label=\"产品特性\" prop=\"cpTx\">\r\n            <el-input v-model=\"pxJson.cpTx\" type=\"textarea\" autosize />\r\n          </el-form-item>\r\n          <el-form-item label=\"特别原料\" prop=\"tbYl\">\r\n            <el-input v-model=\"pxJson.tbYl\" type=\"textarea\" autosize />\r\n          </el-form-item>\r\n          <el-form-item label=\"重点工艺注意事项\" label-width=\"150px\" prop=\"zySx\">\r\n            <el-input v-model=\"pxJson.zySx\" type=\"textarea\" autosize />\r\n          </el-form-item>\r\n          <el-form-item label=\"工程师建议\" prop=\"jy\">\r\n            <el-input v-model=\"pxJson.jy\" type=\"textarea\" autosize />\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"兼容性报告\" prop=\"files\">\r\n            <FileUpload v-model=\"files\" :readonly=\"readonly\" />\r\n          </el-form-item>\r\n\r\n          <el-divider content-position=\"left\" >参与培训人员</el-divider>\r\n\r\n          <div class=\"table-wrapper\" >\r\n            <table class=\"base-table small-table\" >\r\n              <tr>\r\n                <th style=\"width: 50px\" >\r\n                  <i class=\"el-icon-circle-plus-outline\" @click=\"addPxItem()\" />\r\n                </th>\r\n                <th style=\"width: 120px\" >部门</th>\r\n                <th style=\"width: 120px\" >姓名</th>\r\n                <th style=\"width: 120px\" >是否理解</th>\r\n                <th style=\"width: 180px\" >合理建议</th>\r\n                <th >评估改善</th>\r\n              </tr>\r\n              <tr v-for=\"(item,index) in pxArray\" :key=\"index\" >\r\n                <td>\r\n                  <i class=\"el-icon-remove-outline\" @click=\"delItem(pxArray,index)\" />\r\n                </td>\r\n                <td>\r\n                  <el-input v-model=\"item.dept\" size=\"mini\" />\r\n                </td>\r\n                <td>\r\n                  <el-input v-model=\"item.name\" size=\"mini\" />\r\n                </td>\r\n                <td>\r\n                  <el-select v-model=\"item.isLj\" size=\"mini\" >\r\n                    <el-option\r\n                      v-for=\"dict in whetherOptions\"\r\n                      :key=\"dict.value\"\r\n                      :label=\"dict.label\"\r\n                      :value=\"dict.value\"\r\n                    />\r\n                  </el-select>\r\n                </td>\r\n                <td>\r\n                  <el-input v-model=\"item.suggestion\" size=\"mini\" type=\"textArea\" autosize />\r\n                </td>\r\n                <td>\r\n                  <el-input v-model=\"item.pg\" size=\"mini\" type=\"textArea\" autosize />\r\n                </td>\r\n              </tr>\r\n            </table>\r\n          </div>\r\n\r\n          <el-form-item label=\"培训总结\" prop=\"summary\">\r\n            <el-input v-model=\"pxJson.summary\" type=\"textarea\" autosize />\r\n          </el-form-item>\r\n\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n\r\n    </el-form>\r\n    <div v-if=\"!readonly\" class=\"dialog-footer\" style=\"margin-top: 20px\">\r\n      <el-button type=\"primary\" @click=\"submitForm\" size=\"mini\" :loading=\"btnLoading\" >确 定</el-button>\r\n      <el-button @click=\"cancel\" size=\"mini\" >取 消</el-button>\r\n    </div>\r\n\r\n    <el-dialog :fullscreen=\"fullscreenFlag\" :visible.sync=\"open\" width=\"1200px\" :close-on-click-modal=\"false\" append-to-body>\r\n      <div class=\"dialog-title\" slot=\"title\">\r\n        <span>{{ title }}</span>\r\n        <el-button @click=\"fullscreenFlag = !fullscreenFlag\" type=\"text\"\r\n                   :icon=\"fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'\"/>\r\n      </div>\r\n      <MakeUpBSave\r\n        ref=\"makeUpBSave\"\r\n        :readonly=\"readonly\"\r\n        :equipment-list=\"equipmentList\"\r\n        @close=\"close\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog :visible.sync=\"textOpen\" width=\"600px\" :close-on-click-modal=\"false\" append-to-body >\r\n      <el-form ref=\"stepForm\" :model=\"stepForm\" :rules=\"stepRules\" size=\"mini\" label-width=\"100px\" >\r\n        <el-form-item label=\"设备\" prop=\"equipmentId\" >\r\n          <el-select v-model=\"stepForm.equipmentId\" filterable size=\"mini\" style=\"width: 280px\" >\r\n            <el-option\r\n              v-for=\"e in equipmentList\"\r\n              :key=\"e.id\"\r\n              :value=\"e.id\"\r\n              :label=\"e.name\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"培训总结\" prop=\"processText\" >\r\n          <el-input v-model=\"stepForm.processText\" type=\"textarea\" autosize />\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div class=\"dialog-footer\" style=\"margin-top: 20px\" >\r\n        <el-button type=\"primary\" @click=\"buildStepArray\" size=\"mini\" >确 定</el-button>\r\n        <el-button @click=\"textOpen = false\" size=\"mini\" >取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n<script>\r\nimport { addMakeUp, getMakeUp, updateMakeUp,countMakeUpByLabCode } from \"@/api/sop/makeUp\";\r\nimport { getProjectByNo, } from \"@/api/project/project\";\r\nimport {allFormula, getFormula} from \"@/api/software/formula\";\r\nimport { allMaterialFormula } from \"@/api/software/materialFormula\";\r\nimport { allEquipment } from \"@/api/production/equipment\";\r\nimport ProjectBaseView from \"@/views/project/project/base.vue\";\r\nimport ProductionPg from \"@/views/sop/makeUp/productionPg.vue\";\r\nimport StabilityList from \"@/views/rd/stability/list.vue\";\r\nimport BcpProductivityArray from \"@/views/sop/makeUp/bcpProductivityArray.vue\";\r\nimport MakeUpBSave from \"@/views/sop/makeUpB/save.vue\";\r\n\r\nexport default {\r\n  name: \"makeUpSave\",\r\n  components: {\r\n    MakeUpBSave,\r\n    BcpProductivityArray,\r\n    StabilityList,\r\n    ProductionPg,\r\n    ProjectBaseView\r\n  },\r\n  props: {\r\n    readonly: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  computed: {\r\n    sumPercentage() {\r\n      let sum = this.$big(0)\r\n      for (const item of this.materialArray) {\r\n        if(item.percentage) {\r\n          sum = this.add(sum,item.percentage)\r\n        }\r\n      }\r\n      return sum.toNumber()\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      btnLoading: false,\r\n      open: false,\r\n      textOpen: false,\r\n      fullscreenFlag: false,\r\n      title: '',\r\n      form: {},\r\n      rules: {\r\n        projectNo: [\r\n          {required: true,message: '请输入正确的项目编号'},\r\n        ],\r\n        nums: [\r\n          {required: true,message: '请输入配置量'},\r\n        ],\r\n        formulaId: [\r\n          {required: true,message: '请选择配方编码'},\r\n        ],\r\n        type: [\r\n          {required: true,message: '请选择工艺类型'},\r\n        ],\r\n      },\r\n      projectList: [],\r\n      formulaList: [],\r\n      currentProject: {},\r\n      currentTab: 'splitting',\r\n      equipmentList: [],\r\n      aidedEquipmentList: [],\r\n      materialArray: [],\r\n      processArray: [],\r\n      productivityArray: [],\r\n      findingArray: [],\r\n      equipmentTypeOptions: [],\r\n      cycleOptions: [],\r\n      riskLevelOptions: [],\r\n      bcpCode: null,\r\n      currentDiagram: {},\r\n      bcpArray: [],\r\n      conclusionOptions: [\r\n        {label: '通过',value: '0'},\r\n        {label: '不通过',value: '1'},\r\n      ],\r\n      erpDataList: [],\r\n      typeOptions: [\r\n        {label: '标准工艺',value: '0'},\r\n        {label: '大货工艺',value: '1'},\r\n      ],\r\n      currentRow: {},\r\n      pxJson: {},\r\n      pxArray: [],\r\n      whetherOptions: [\r\n        {label: '是',value: '1'},\r\n        {label: '否',value: '0'},\r\n      ],\r\n      files: [],\r\n      stepForm: {},\r\n      stepRules: {\r\n        processText: [\r\n          {required: true,message: '请输入工艺'},\r\n        ],\r\n        equipmentId: [\r\n          {required: true,message: '请选择设备'},\r\n        ],\r\n      },\r\n    }\r\n  },\r\n  async created() {\r\n    this.equipmentList = await allEquipment({typeId: 59})\r\n  },\r\n  methods: {\r\n    resetStepForm() {\r\n      this.stepForm = {\r\n        processText: null,\r\n        equipmentId: null,\r\n      }\r\n      this.resetForm(\"stepForm\")\r\n    },\r\n    showDialog(row) {\r\n      this.currentRow = row\r\n      this.resetStepForm()\r\n      this.textOpen = true\r\n    },\r\n    buildStepArray() {\r\n      const row = this.currentRow\r\n      const form = Object.assign({},this.stepForm)\r\n\r\n      const array = this.splitSteps(form.processText)\r\n      for (const item of array) {\r\n        this.addArgsItem(row.equipmentArgs,row.materialArray,item,form.equipmentId)\r\n      }\r\n      this.textOpen = false\r\n    },\r\n    //切分步骤\r\n    splitSteps(text) {\r\n      return text.split('\\n').reduce((acc, line) => {\r\n        const trimmedLine = line.trim();\r\n        if (!trimmedLine) return acc; // 跳过空行\r\n\r\n        if (/^(\\d+\\.|★\\d+\\.)/.test(trimmedLine)) {\r\n          // 新步骤开始（以数字或★数字开头）\r\n          acc.push(trimmedLine);\r\n        } else if (acc.length > 0) {\r\n          // 延续上一步骤的内容\r\n          acc[acc.length - 1] += '\\n' + trimmedLine;\r\n        }\r\n        return acc;\r\n      }, []);\r\n    },\r\n    async close(id) {\r\n      this.open = false\r\n    },\r\n    async showBProcess(row) {\r\n      this.currentRow = row\r\n      this.title = row.materialCode\r\n      this.open = true\r\n      await this.$nextTick()\r\n      const makeUpBSave = this.$refs.makeUpBSave\r\n      if(makeUpBSave) {\r\n        makeUpBSave.reset()\r\n        makeUpBSave.form.makeUpId = this.form.id\r\n        makeUpBSave.form.labCode = row.materialCode\r\n        await makeUpBSave.init(row.materialCode)\r\n      }\r\n    },\r\n    async processToEquipment() {\r\n      const processArray = this.processArray\r\n      const productivityArray = this.productivityArray\r\n      const equipmentIdSet = new Set()\r\n      for (const p of processArray) {\r\n        for (const e of p.equipmentArgs) {\r\n          if(e.equipmentId) {\r\n            equipmentIdSet.add(e.equipmentId)\r\n          }\r\n        }\r\n      }\r\n      for (const equipmentId of equipmentIdSet) {\r\n        if(!productivityArray.map(i=>i.equipmentId).includes(equipmentId)) {\r\n          productivityArray.push({\r\n            equipmentId,\r\n            weight: undefined,\r\n            max: undefined,\r\n            min: undefined,\r\n            personNums: undefined,\r\n            hours: undefined,\r\n            productivity: 0,\r\n            hourRate: 0,\r\n          })\r\n        }\r\n      }\r\n    },\r\n    async searchFormulaList() {\r\n      this.loading = true\r\n      const laboratoryCode = this.form.labCode\r\n      const formulaList = await allFormula({laboratoryCode})\r\n      this.formulaList = formulaList\r\n\r\n      const res = await countMakeUpByLabCode({labCode: laboratoryCode})\r\n      if(res.code === 200 && res.data) {\r\n        this.msgError('实验室编码已存在!')\r\n      }\r\n      this.loading = false\r\n    },\r\n    computeNums() {\r\n      if(this.form.nums) {\r\n        for (const o of this.materialArray) {\r\n          if(this.form.nums && o.percentage) {\r\n            o.nums = this.multiply(this.form.nums,this.divide(o.percentage,100)).toNumber()\r\n          } else {\r\n            o.nums = 0\r\n          }\r\n        }\r\n      }\r\n    },\r\n    addArgsItem(array,materialArray,process,equipmentId) {\r\n      array.push({\r\n        step: array.length+1,\r\n        materialIds: materialArray.map(i=>i.materialId),\r\n        equipmentId,\r\n        process,\r\n        keyPoints: null,\r\n        temperature: null,\r\n        mpa: null,\r\n        jz: null,\r\n        jzTime: null,\r\n        nj: null,\r\n        njTime: null,\r\n        wj: null,\r\n        zk: null,\r\n        time: null,\r\n      })\r\n    },\r\n    delItem(array,i) {\r\n      array.splice(i,1)\r\n    },\r\n    addPxItem() {\r\n      this.pxArray.push({\r\n        dept: null,\r\n        name: null,\r\n        isLj: null,\r\n        suggestion: null,\r\n        pg: null,\r\n      })\r\n    },\r\n    async generateProcessArray() {\r\n      this.btnLoading = true\r\n      const tempArray = []\r\n      for (const m of this.materialArray) {\r\n        for (const sub of m.array) {\r\n          tempArray.push({\r\n            materialId: m.materialId,\r\n            erpCode: m.erpCode,\r\n            materialCode: m.materialCode,\r\n            percentage: sub.percentage,\r\n            subItem: sub.subItem,\r\n            category: sub.subItem.replace(/\\s*/g,\"\").substring(0, 1),\r\n            firstPercentage: null,\r\n            processArray: [],\r\n          })\r\n        }\r\n      }\r\n      const categorySet = new Set()\r\n      for (const temp of tempArray) {\r\n        categorySet.add(temp.category)\r\n      }\r\n      const processArray = []\r\n      for (const category of categorySet) {\r\n        const materialArray = tempArray.filter(i=>i.category === category).sort((a,b)=> a.subItem.replace(/[a-zA-Z]/g, '') - b.subItem.replace(/[a-zA-Z]/g, ''))\r\n        const equipmentArgs = []\r\n        this.addArgsItem(equipmentArgs,materialArray)\r\n        processArray.push({\r\n          category,\r\n          materialArray,\r\n          equipmentArgs,\r\n          processDesc: null,\r\n          keyPoints: null,\r\n          aidedArray: [],\r\n          files:[],\r\n          materialIds: [],//工艺图中选中的物料\r\n        })\r\n      }\r\n      this.processArray = processArray.sort((a,b)=>a.category.localeCompare(b.category))\r\n      this.btnLoading = false\r\n    },\r\n    percentageStyle(item) {\r\n      let percentage = item.array.filter(i=>i.percentage).map(i=>i.percentage).reduce((a,b)=> this.add(a, b),this.$big(0))\r\n      if(percentage.toNumber() === item.percentage) {\r\n        return {\r\n          color: '#67C23A'\r\n        }\r\n      } else {\r\n        return {\r\n          color: '#F56C6C'\r\n        }\r\n      }\r\n    },\r\n    addSubItem(item) {\r\n      item.array.push({\r\n        percentage: null,\r\n        subItem: null,\r\n      })\r\n    },\r\n    delSubItem(item,i) {\r\n      item.array.splice(i,1)\r\n    },\r\n    async formulaChange() {\r\n      const form = this.form\r\n      const formulaId = form.formulaId\r\n      if(formulaId) {\r\n        const formulaRes = await getFormula(formulaId)\r\n        if(formulaRes.code === 200) {\r\n          const data = formulaRes.data\r\n\r\n          form.formulaCode = data.formulaCode\r\n          form.labCode = data.laboratoryCode\r\n          form.customerName = data.customerName\r\n          form.productName = data.productName\r\n          form.projectNo = data.projectNo\r\n\r\n          if(data.jcXmJson) {\r\n            const bcpArray = JSON.parse(data.jcXmJson)\r\n            this.bcpArray = this.bcpArray.filter(i=>bcpArray.map(b=>b.id).includes(i.id))//过滤研发中已经删除的半成品\r\n            for (const bcp of bcpArray) {\r\n              if(!this.bcpArray.map(i=>i.id).includes(bcp.id)) {\r\n                bcp.zsStandard = null\r\n                bcp.inspectionArray = []\r\n                this.bcpArray.push(bcp)//补充研发新增的\r\n              }\r\n            }\r\n          }\r\n        }\r\n        const array = await allMaterialFormula({formulaId})\r\n        const materialIdSet = new Set()\r\n        for (const item of array) {\r\n          materialIdSet.add(item.materialId)\r\n        }\r\n        const materialArray = []\r\n        for (const materialId of materialIdSet) {\r\n          const arr = array.filter(i=> i.materialId === materialId)\r\n          if(arr && arr[0]) {\r\n            const o = {\r\n              materialId: arr[0].materialId,\r\n              erpCode: arr[0].erpCode,\r\n              materialCode: arr[0].materialCode,\r\n            }\r\n            const subArray = arr.map(i=> {\r\n              return {\r\n                percentage: i.percentage,\r\n                subItem: i.subItem,\r\n              }\r\n            })\r\n            o.array = subArray\r\n            o.percentage = subArray.map(i=>i.percentage).reduce((a,b)=> this.add(a, b),0).toNumber()\r\n            materialArray.push(o)\r\n          }\r\n        }\r\n        this.materialArray = materialArray\r\n\r\n        this.computeNums()\r\n      }\r\n    },\r\n    async searchProject() {\r\n      this.btnLoading = true\r\n      let projectNo = this.form.projectNo\r\n      if(projectNo) {\r\n        projectNo = projectNo.replace(/\\s*/g,\"\")\r\n        const res = await getProjectByNo(projectNo)\r\n        if(res.code === 200) {\r\n          const project = res.data\r\n          if(project) {\r\n            this.currentProject = project\r\n            this.form.projectId = project.id\r\n          }\r\n        }\r\n      }\r\n      this.btnLoading = false\r\n    },\r\n    cancel() {\r\n      this.$parent.$parent.open = false;\r\n      this.reset();\r\n    },\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        projectId: null,\r\n        erpCode: null,\r\n        formulaId: null,\r\n        formulaCode: null,\r\n        diagramId: null,\r\n        projectNo: null,\r\n        nums: 100,\r\n        type: '0',\r\n        temperature: null,\r\n        meshesNums: null,\r\n        labCode: null,\r\n        pxUser: null,\r\n        pxDate: null,\r\n        pzUser: null,\r\n        pzDate: null,\r\n        qcCode: null,\r\n      }\r\n      this.resetForm(\"form\")\r\n      this.currentProject = {}\r\n      this.formulaList = []\r\n      this.materialArray = []\r\n      this.processArray = []\r\n      this.productivityArray = []\r\n      this.findingArray = []\r\n      this.bcpArray = []\r\n      this.bcpCode = null\r\n      this.erpDataList = []\r\n      this.pxJson = {\r\n        byQr: null,\r\n        byStatus: null,\r\n        jyTime: null,\r\n        zlJy: null,\r\n        clFa: null,\r\n        cpJs: null,\r\n        cpTx: null,\r\n        tbYl: null,\r\n        zySx: null,\r\n        jy: null,\r\n        gxs: null,\r\n        summary: null,\r\n      }\r\n      this.pxArray = []\r\n      this.files = []\r\n    },\r\n    async init(id) {\r\n      const res = await getMakeUp(id)\r\n      const form = res.data\r\n\r\n      if(form.materialArray) {\r\n        this.materialArray = JSON.parse(form.materialArray)\r\n      }\r\n\r\n      if(form.processArray) {\r\n        this.processArray = JSON.parse(form.processArray)\r\n      }\r\n\r\n      if(form.productivityArray) {\r\n        this.productivityArray = JSON.parse(form.productivityArray)\r\n      }\r\n\r\n      if(form.bcpArray) {\r\n        this.bcpArray = JSON.parse(form.bcpArray)\r\n      }\r\n\r\n      if(form.findingArray) {\r\n        this.findingArray = JSON.parse(form.findingArray)\r\n      }\r\n\r\n      if(form.pxJson) {\r\n        this.pxJson = JSON.parse(form.pxJson)\r\n      }\r\n\r\n      if(form.pxArray) {\r\n        this.pxArray = JSON.parse(form.pxArray)\r\n      }\r\n\r\n      if(form.files) {\r\n        this.files = JSON.parse(form.files)\r\n      }\r\n\r\n      this.form = form\r\n      await this.searchProject()\r\n\r\n    },\r\n    async submitForm() {\r\n      await this.$refs[\"form\"].validate()\r\n      let form = Object.assign({},this.form)\r\n\r\n      for (const m of this.materialArray) {\r\n        let percentage = m.array.filter(i=>i.percentage).map(i=>i.percentage).reduce((a,b)=> this.add(a, b),0)\r\n        if(percentage.toNumber() !== m.percentage) {\r\n          this.msgError('配方比例不一致,请修改后提交')\r\n          return\r\n        }\r\n      }\r\n\r\n      for (const item of this.productivityArray) {\r\n        if(!item.equipmentId) {\r\n          this.msgError('请选择设备')\r\n          return\r\n        }\r\n        if(!item.weight) {\r\n          this.msgError('请输入标准配制量')\r\n          return\r\n        }\r\n        if(!item.personNums) {\r\n          this.msgError('请输入标准人数')\r\n          return\r\n        }\r\n        if(!item.hours) {\r\n          this.msgError('请输入标准工时')\r\n          return\r\n        }\r\n      }\r\n\r\n      form.materialArray = JSON.stringify(this.materialArray)\r\n      form.processArray = JSON.stringify(this.processArray)\r\n      form.productivityArray = JSON.stringify(this.productivityArray)\r\n      form.bcpArray = JSON.stringify(this.bcpArray)\r\n      form.findingArray = JSON.stringify(this.findingArray)\r\n      form.pxJson = JSON.stringify(this.pxJson)\r\n      form.pxArray = JSON.stringify(this.pxArray)\r\n      form.files = JSON.stringify(this.files)\r\n\r\n      if (form.id != null) {\r\n        try {\r\n          this.btnLoading = true\r\n          await updateMakeUp(form)\r\n          this.btnLoading = false\r\n          if(res.code === 200) {\r\n            this.msgSuccess(\"操作成功\")\r\n          }\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      } else {\r\n        try {\r\n          this.btnLoading = true\r\n          const res = await addMakeUp(form)\r\n          this.btnLoading = false\r\n          if(res.code === 200) {\r\n            this.msgSuccess(\"操作成功\")\r\n            await this.init(res.data.id)\r\n          }\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n\r\n.diagram-wrapper {\r\n  height: 0;\r\n  padding-top: 100%;\r\n  background-size: 100% auto;\r\n  background-position: center center;\r\n  background-repeat: no-repeat;\r\n  position: relative;\r\n\r\n  .pointer {\r\n    position: absolute;\r\n    width: 20px;\r\n    height: 20px;\r\n    border-radius: 50%;\r\n    border: 1px solid #DCDFE6;\r\n    cursor: pointer;\r\n    text-align: center;\r\n    line-height: 20px;\r\n    font-size: 12px;\r\n    white-space: nowrap;\r\n  }\r\n\r\n}\r\n\r\n.table-wrapper {\r\n  max-height: 90vh;\r\n\r\n  .base-table {\r\n    thead {\r\n      position: sticky;\r\n      top: 0;\r\n      z-index: 3;\r\n    }\r\n\r\n    .nth1 {\r\n      position: sticky;\r\n      left: 0px;\r\n      z-index: 1;\r\n    }\r\n\r\n    .nth2 {\r\n      position: sticky;\r\n      left: 120px;\r\n      z-index: 1;\r\n    }\r\n\r\n    .nth3 {\r\n      position: sticky;\r\n      left: 240px;\r\n      z-index: 1;\r\n    }\r\n\r\n    .nth4 {\r\n      position: sticky;\r\n      left: 360px;\r\n      z-index: 1;\r\n    }\r\n\r\n    .nth5 {\r\n      position: sticky;\r\n      left: 480px;\r\n      z-index: 1;\r\n    }\r\n\r\n    .nth6 {\r\n      position: sticky;\r\n      left: 600px;\r\n      z-index: 1;\r\n    }\r\n\r\n    .nth7 {\r\n      position: sticky;\r\n      left: 720px;\r\n      z-index: 1;\r\n    }\r\n\r\n    th:nth-child(-n+7) {\r\n      background: #f8f8f9;\r\n      color: #515a6e;\r\n    }\r\n\r\n    td:nth-child(-n+7) {\r\n      background: #fff;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAigBA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,gBAAA,GAAAH,OAAA;AACA,IAAAI,UAAA,GAAAJ,OAAA;AACA,IAAAK,KAAA,GAAAC,sBAAA,CAAAN,OAAA;AACA,IAAAO,aAAA,GAAAD,sBAAA,CAAAN,OAAA;AACA,IAAAQ,KAAA,GAAAF,sBAAA,CAAAN,OAAA;AACA,IAAAS,qBAAA,GAAAH,sBAAA,CAAAN,OAAA;AACA,IAAAU,KAAA,GAAAJ,sBAAA,CAAAN,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAW,IAAA;EACAC,UAAA;IACAC,WAAA,EAAAA,aAAA;IACAC,oBAAA,EAAAA,6BAAA;IACAC,aAAA,EAAAA,aAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,eAAA,EAAAA;EACA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;EACA;EACAC,QAAA;IACAC,aAAA,WAAAA,cAAA;MACA,IAAAC,GAAA,QAAAC,IAAA;MAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAAN,OAAA,EACA,KAAAO,aAAA;QAAAC,KAAA;MAAA;QAAA,KAAAH,SAAA,CAAAI,CAAA,MAAAD,KAAA,GAAAH,SAAA,CAAAK,CAAA,IAAAC,IAAA;UAAA,IAAAC,IAAA,GAAAJ,KAAA,CAAAK,KAAA;UACA,IAAAD,IAAA,CAAAE,UAAA;YACAX,GAAA,QAAAY,GAAA,CAAAZ,GAAA,EAAAS,IAAA,CAAAE,UAAA;UACA;QACA;MAAA,SAAAE,GAAA;QAAAX,SAAA,CAAAY,CAAA,CAAAD,GAAA;MAAA;QAAAX,SAAA,CAAAa,CAAA;MAAA;MACA,OAAAf,GAAA,CAAAgB,QAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACAC,IAAA;MACAC,QAAA;MACAC,cAAA;MACAC,KAAA;MACAC,IAAA;MACAC,KAAA;QACAC,SAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;QAAA,EACA;QACAC,IAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;QAAA,EACA;QACAE,SAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;QAAA,EACA;QACAjC,IAAA,GACA;UAAAgC,QAAA;UAAAC,OAAA;QAAA;MAEA;MACAG,WAAA;MACAC,WAAA;MACAC,cAAA;MACAC,UAAA;MACAC,aAAA;MACAC,kBAAA;MACAhC,aAAA;MACAiC,YAAA;MACAC,iBAAA;MACAC,YAAA;MACAC,oBAAA;MACAC,YAAA;MACAC,gBAAA;MACAC,OAAA;MACAC,cAAA;MACAC,QAAA;MACAC,iBAAA,GACA;QAAAC,KAAA;QAAArC,KAAA;MAAA,GACA;QAAAqC,KAAA;QAAArC,KAAA;MAAA,EACA;MACAsC,WAAA;MACAC,WAAA,GACA;QAAAF,KAAA;QAAArC,KAAA;MAAA,GACA;QAAAqC,KAAA;QAAArC,KAAA;MAAA,EACA;MACAwC,UAAA;MACAC,MAAA;MACAC,OAAA;MACAC,cAAA,GACA;QAAAN,KAAA;QAAArC,KAAA;MAAA,GACA;QAAAqC,KAAA;QAAArC,KAAA;MAAA,EACA;MACA4C,KAAA;MACAC,QAAA;MACAC,SAAA;QACAC,WAAA,GACA;UAAA9B,QAAA;UAAAC,OAAA;QAAA,EACA;QACA8B,WAAA,GACA;UAAA/B,QAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACA+B,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,WAAAC,kBAAA,CAAAhE,OAAA,mBAAAiE,oBAAA,CAAAjE,OAAA,IAAAkE,IAAA,UAAAC,QAAA;MAAA,WAAAF,oBAAA,CAAAjE,OAAA,IAAAoE,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACA,IAAAC,uBAAA;cAAAC,MAAA;YAAA;UAAA;YAAAX,KAAA,CAAAzB,aAAA,GAAAgC,QAAA,CAAAK,IAAA;UAAA;UAAA;YAAA,OAAAL,QAAA,CAAAM,IAAA;QAAA;MAAA,GAAAT,OAAA;IAAA;EACA;EACAU,OAAA;IACAC,aAAA,WAAAA,cAAA;MACA,KAAApB,QAAA;QACAE,WAAA;QACAC,WAAA;MACA;MACA,KAAAkB,SAAA;IACA;IACAC,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAA5B,UAAA,GAAA4B,GAAA;MACA,KAAAH,aAAA;MACA,KAAAtD,QAAA;IACA;IACA0D,cAAA,WAAAA,eAAA;MACA,IAAAD,GAAA,QAAA5B,UAAA;MACA,IAAA1B,IAAA,GAAAwD,MAAA,CAAAC,MAAA,UAAA1B,QAAA;MAEA,IAAA2B,KAAA,QAAAC,UAAA,CAAA3D,IAAA,CAAAiC,WAAA;MAAA,IAAA2B,UAAA,OAAAjF,2BAAA,CAAAN,OAAA,EACAqF,KAAA;QAAAG,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAA9E,CAAA,MAAA+E,MAAA,GAAAD,UAAA,CAAA7E,CAAA,IAAAC,IAAA;UAAA,IAAAC,IAAA,GAAA4E,MAAA,CAAA3E,KAAA;UACA,KAAA4E,WAAA,CAAAR,GAAA,CAAAS,aAAA,EAAAT,GAAA,CAAA1E,aAAA,EAAAK,IAAA,EAAAe,IAAA,CAAAkC,WAAA;QACA;MAAA,SAAA7C,GAAA;QAAAuE,UAAA,CAAAtE,CAAA,CAAAD,GAAA;MAAA;QAAAuE,UAAA,CAAArE,CAAA;MAAA;MACA,KAAAM,QAAA;IACA;IACA;IACA8D,UAAA,WAAAA,WAAAK,IAAA;MACA,OAAAA,IAAA,CAAAC,KAAA,OAAAC,MAAA,WAAAC,GAAA,EAAAC,IAAA;QACA,IAAAC,WAAA,GAAAD,IAAA,CAAAE,IAAA;QACA,KAAAD,WAAA,SAAAF,GAAA;;QAEA,sBAAAI,IAAA,CAAAF,WAAA;UACA;UACAF,GAAA,CAAAK,IAAA,CAAAH,WAAA;QACA,WAAAF,GAAA,CAAAM,MAAA;UACA;UACAN,GAAA,CAAAA,GAAA,CAAAM,MAAA,gBAAAJ,WAAA;QACA;QACA,OAAAF,GAAA;MACA;IACA;IACAO,KAAA,WAAAA,MAAAC,EAAA;MAAA,IAAAC,MAAA;MAAA,WAAAvC,kBAAA,CAAAhE,OAAA,mBAAAiE,oBAAA,CAAAjE,OAAA,IAAAkE,IAAA,UAAAsC,SAAA;QAAA,WAAAvC,oBAAA,CAAAjE,OAAA,IAAAoE,IAAA,UAAAqC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnC,IAAA,GAAAmC,SAAA,CAAAlC,IAAA;YAAA;cACA+B,MAAA,CAAAhF,IAAA;YAAA;YAAA;cAAA,OAAAmF,SAAA,CAAA9B,IAAA;UAAA;QAAA,GAAA4B,QAAA;MAAA;IACA;IACAG,YAAA,WAAAA,aAAA1B,GAAA;MAAA,IAAA2B,MAAA;MAAA,WAAA5C,kBAAA,CAAAhE,OAAA,mBAAAiE,oBAAA,CAAAjE,OAAA,IAAAkE,IAAA,UAAA2C,SAAA;QAAA,IAAAC,WAAA;QAAA,WAAA7C,oBAAA,CAAAjE,OAAA,IAAAoE,IAAA,UAAA2C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzC,IAAA,GAAAyC,SAAA,CAAAxC,IAAA;YAAA;cACAoC,MAAA,CAAAvD,UAAA,GAAA4B,GAAA;cACA2B,MAAA,CAAAlF,KAAA,GAAAuD,GAAA,CAAAgC,YAAA;cACAL,MAAA,CAAArF,IAAA;cAAAyF,SAAA,CAAAxC,IAAA;cAAA,OACAoC,MAAA,CAAAM,SAAA;YAAA;cACAJ,WAAA,GAAAF,MAAA,CAAAO,KAAA,CAAAL,WAAA;cAAA,KACAA,WAAA;gBAAAE,SAAA,CAAAxC,IAAA;gBAAA;cAAA;cACAsC,WAAA,CAAAM,KAAA;cACAN,WAAA,CAAAnF,IAAA,CAAA0F,QAAA,GAAAT,MAAA,CAAAjF,IAAA,CAAA2E,EAAA;cACAQ,WAAA,CAAAnF,IAAA,CAAA2F,OAAA,GAAArC,GAAA,CAAAgC,YAAA;cAAAD,SAAA,CAAAxC,IAAA;cAAA,OACAsC,WAAA,CAAAS,IAAA,CAAAtC,GAAA,CAAAgC,YAAA;YAAA;YAAA;cAAA,OAAAD,SAAA,CAAApC,IAAA;UAAA;QAAA,GAAAiC,QAAA;MAAA;IAEA;IACAW,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAzD,kBAAA,CAAAhE,OAAA,mBAAAiE,oBAAA,CAAAjE,OAAA,IAAAkE,IAAA,UAAAwD,SAAA;QAAA,IAAAlF,YAAA,EAAAC,iBAAA,EAAAkF,cAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,CAAA,EAAAC,UAAA,EAAAC,MAAA,EAAA/G,CAAA,EAAAgH,UAAA,EAAAC,MAAA,EAAArE,WAAA;QAAA,WAAAI,oBAAA,CAAAjE,OAAA,IAAAoE,IAAA,UAAA+D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7D,IAAA,GAAA6D,SAAA,CAAA5D,IAAA;YAAA;cACAhC,YAAA,GAAAiF,MAAA,CAAAjF,YAAA;cACAC,iBAAA,GAAAgF,MAAA,CAAAhF,iBAAA;cACAkF,cAAA,OAAAU,GAAA;cAAAT,UAAA,OAAAtH,2BAAA,CAAAN,OAAA,EACAwC,YAAA;cAAA;gBAAA,KAAAoF,UAAA,CAAAnH,CAAA,MAAAoH,MAAA,GAAAD,UAAA,CAAAlH,CAAA,IAAAC,IAAA;kBAAAmH,CAAA,GAAAD,MAAA,CAAAhH,KAAA;kBAAAkH,UAAA,OAAAzH,2BAAA,CAAAN,OAAA,EACA8H,CAAA,CAAApC,aAAA;kBAAA;oBAAA,KAAAqC,UAAA,CAAAtH,CAAA,MAAAuH,MAAA,GAAAD,UAAA,CAAArH,CAAA,IAAAC,IAAA;sBAAAM,CAAA,GAAA+G,MAAA,CAAAnH,KAAA;sBACA,IAAAI,CAAA,CAAA4C,WAAA;wBACA8D,cAAA,CAAA5G,GAAA,CAAAE,CAAA,CAAA4C,WAAA;sBACA;oBACA;kBAAA,SAAA7C,GAAA;oBAAA+G,UAAA,CAAA9G,CAAA,CAAAD,GAAA;kBAAA;oBAAA+G,UAAA,CAAA7G,CAAA;kBAAA;gBACA;cAAA,SAAAF,GAAA;gBAAA4G,UAAA,CAAA3G,CAAA,CAAAD,GAAA;cAAA;gBAAA4G,UAAA,CAAA1G,CAAA;cAAA;cAAA+G,UAAA,OAAA3H,2BAAA,CAAAN,OAAA,EACA2H,cAAA;cAAA;gBAAA,KAAAM,UAAA,CAAAxH,CAAA,MAAAyH,MAAA,GAAAD,UAAA,CAAAvH,CAAA,IAAAC,IAAA;kBAAAkD,WAAA,GAAAqE,MAAA,CAAArH,KAAA;kBACA,KAAA4B,iBAAA,CAAA6F,GAAA,WAAAC,CAAA;oBAAA,OAAAA,CAAA,CAAA1E,WAAA;kBAAA,GAAA2E,QAAA,CAAA3E,WAAA;oBACApB,iBAAA,CAAA0D,IAAA;sBACAtC,WAAA,EAAAA,WAAA;sBACA4E,MAAA,EAAAC,SAAA;sBACAC,GAAA,EAAAD,SAAA;sBACAE,GAAA,EAAAF,SAAA;sBACAG,UAAA,EAAAH,SAAA;sBACAI,KAAA,EAAAJ,SAAA;sBACAK,YAAA;sBACAC,QAAA;oBACA;kBACA;gBACA;cAAA,SAAAhI,GAAA;gBAAAiH,UAAA,CAAAhH,CAAA,CAAAD,GAAA;cAAA;gBAAAiH,UAAA,CAAA/G,CAAA;cAAA;YAAA;YAAA;cAAA,OAAAkH,SAAA,CAAAxD,IAAA;UAAA;QAAA,GAAA8C,QAAA;MAAA;IACA;IACAuB,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAlF,kBAAA,CAAAhE,OAAA,mBAAAiE,oBAAA,CAAAjE,OAAA,IAAAkE,IAAA,UAAAiF,SAAA;QAAA,IAAAC,cAAA,EAAAjH,WAAA,EAAAkH,GAAA;QAAA,WAAApF,oBAAA,CAAAjE,OAAA,IAAAoE,IAAA,UAAAkF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhF,IAAA,GAAAgF,SAAA,CAAA/E,IAAA;YAAA;cACA0E,MAAA,CAAA7H,OAAA;cACA+H,cAAA,GAAAF,MAAA,CAAAvH,IAAA,CAAA2F,OAAA;cAAAiC,SAAA,CAAA/E,IAAA;cAAA,OACA,IAAAgF,mBAAA;gBAAAJ,cAAA,EAAAA;cAAA;YAAA;cAAAjH,WAAA,GAAAoH,SAAA,CAAA5E,IAAA;cACAuE,MAAA,CAAA/G,WAAA,GAAAA,WAAA;cAAAoH,SAAA,CAAA/E,IAAA;cAAA,OAEA,IAAAiF,4BAAA;gBAAAnC,OAAA,EAAA8B;cAAA;YAAA;cAAAC,GAAA,GAAAE,SAAA,CAAA5E,IAAA;cACA,IAAA0E,GAAA,CAAAK,IAAA,YAAAL,GAAA,CAAAjI,IAAA;gBACA8H,MAAA,CAAAS,QAAA;cACA;cACAT,MAAA,CAAA7H,OAAA;YAAA;YAAA;cAAA,OAAAkI,SAAA,CAAA3E,IAAA;UAAA;QAAA,GAAAuE,QAAA;MAAA;IACA;IACAS,WAAA,WAAAA,YAAA;MACA,SAAAjI,IAAA,CAAAK,IAAA;QAAA,IAAA6H,UAAA,OAAAvJ,2BAAA,CAAAN,OAAA,EACA,KAAAO,aAAA;UAAAuJ,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAApJ,CAAA,MAAAqJ,MAAA,GAAAD,UAAA,CAAAnJ,CAAA,IAAAC,IAAA;YAAA,IAAAoJ,CAAA,GAAAD,MAAA,CAAAjJ,KAAA;YACA,SAAAc,IAAA,CAAAK,IAAA,IAAA+H,CAAA,CAAAjJ,UAAA;cACAiJ,CAAA,CAAA/H,IAAA,QAAAgI,QAAA,MAAArI,IAAA,CAAAK,IAAA,OAAAiI,MAAA,CAAAF,CAAA,CAAAjJ,UAAA,QAAAK,QAAA;YACA;cACA4I,CAAA,CAAA/H,IAAA;YACA;UACA;QAAA,SAAAhB,GAAA;UAAA6I,UAAA,CAAA5I,CAAA,CAAAD,GAAA;QAAA;UAAA6I,UAAA,CAAA3I,CAAA;QAAA;MACA;IACA;IACAuE,WAAA,WAAAA,YAAAJ,KAAA,EAAA9E,aAAA,EAAA2J,OAAA,EAAArG,WAAA;MACAwB,KAAA,CAAAc,IAAA;QACAgE,IAAA,EAAA9E,KAAA,CAAAe,MAAA;QACAgE,WAAA,EAAA7J,aAAA,CAAA+H,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAA8B,UAAA;QAAA;QACAxG,WAAA,EAAAA,WAAA;QACAqG,OAAA,EAAAA,OAAA;QACAI,SAAA;QACAC,WAAA;QACAC,GAAA;QACAC,EAAA;QACAC,MAAA;QACAC,EAAA;QACAC,MAAA;QACAC,EAAA;QACAC,EAAA;QACAC,IAAA;MACA;IACA;IACAC,OAAA,WAAAA,QAAA3F,KAAA,EAAAkD,CAAA;MACAlD,KAAA,CAAA4F,MAAA,CAAA1C,CAAA;IACA;IACA2C,SAAA,WAAAA,UAAA;MACA,KAAA3H,OAAA,CAAA4C,IAAA;QACAgF,IAAA;QACA9L,IAAA;QACA+L,IAAA;QACAC,UAAA;QACAC,EAAA;MACA;IACA;IACAC,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAxH,kBAAA,CAAAhE,OAAA,mBAAAiE,oBAAA,CAAAjE,OAAA,IAAAkE,IAAA,UAAAuH,SAAA;QAAA,IAAAC,SAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,CAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,GAAA,EAAAC,WAAA,EAAAC,EAAA,EAAAC,UAAA,EAAAC,IAAA,EAAA5J,YAAA,EAAA6J,UAAA,EAAAC,MAAA,EAAAC,KAAA;QAAA,WAAAtI,oBAAA,CAAAjE,OAAA,IAAAoE,IAAA,UAAAoI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlI,IAAA,GAAAkI,SAAA,CAAAjI,IAAA;YAAA;cACAgH,MAAA,CAAAlK,UAAA;cACAoK,SAAA;cAAAC,UAAA,OAAArL,2BAAA,CAAAN,OAAA,EACAwL,MAAA,CAAAjL,aAAA;cAAA;gBAAA,KAAAoL,UAAA,CAAAlL,CAAA,MAAAmL,MAAA,GAAAD,UAAA,CAAAjL,CAAA,IAAAC,IAAA;kBAAAkL,CAAA,GAAAD,MAAA,CAAA/K,KAAA;kBAAAiL,UAAA,OAAAxL,2BAAA,CAAAN,OAAA,EACA6L,CAAA,CAAAxG,KAAA;kBAAA;oBAAA,KAAAyG,UAAA,CAAArL,CAAA,MAAAsL,MAAA,GAAAD,UAAA,CAAApL,CAAA,IAAAC,IAAA;sBAAAqL,GAAA,GAAAD,MAAA,CAAAlL,KAAA;sBACA6K,SAAA,CAAAvF,IAAA;wBACAkE,UAAA,EAAAwB,CAAA,CAAAxB,UAAA;wBACAqC,OAAA,EAAAb,CAAA,CAAAa,OAAA;wBACAzF,YAAA,EAAA4E,CAAA,CAAA5E,YAAA;wBACAnG,UAAA,EAAAkL,GAAA,CAAAlL,UAAA;wBACA6L,OAAA,EAAAX,GAAA,CAAAW,OAAA;wBACAC,QAAA,EAAAZ,GAAA,CAAAW,OAAA,CAAAE,OAAA,aAAAC,SAAA;wBACAC,eAAA;wBACAvK,YAAA;sBACA;oBACA;kBAAA,SAAAxB,GAAA;oBAAA8K,UAAA,CAAA7K,CAAA,CAAAD,GAAA;kBAAA;oBAAA8K,UAAA,CAAA5K,CAAA;kBAAA;gBACA;cAAA,SAAAF,GAAA;gBAAA2K,UAAA,CAAA1K,CAAA,CAAAD,GAAA;cAAA;gBAAA2K,UAAA,CAAAzK,CAAA;cAAA;cACA+K,WAAA,OAAA5D,GAAA;cACA,KAAA6D,EAAA,MAAAC,UAAA,GAAAT,SAAA,EAAAQ,EAAA,GAAAC,UAAA,CAAA/F,MAAA,EAAA8F,EAAA;gBAAAE,IAAA,GAAAD,UAAA,CAAAD,EAAA;gBACAD,WAAA,CAAAlL,GAAA,CAAAqL,IAAA,CAAAQ,QAAA;cACA;cACApK,YAAA;cAAA6J,UAAA,OAAA/L,2BAAA,CAAAN,OAAA,EACAiM,WAAA;cAAAQ,SAAA,CAAAlI,IAAA;cAAAgI,KAAA,oBAAAtI,oBAAA,CAAAjE,OAAA,IAAAkE,IAAA,UAAAqI,MAAA;gBAAA,IAAAK,QAAA,EAAArM,aAAA,EAAAmF,aAAA;gBAAA,WAAAzB,oBAAA,CAAAjE,OAAA,IAAAoE,IAAA,UAAA4I,OAAAC,SAAA;kBAAA,kBAAAA,SAAA,CAAA1I,IAAA,GAAA0I,SAAA,CAAAzI,IAAA;oBAAA;sBAAAoI,QAAA,GAAAN,MAAA,CAAAzL,KAAA;sBACAN,aAAA,GAAAmL,SAAA,CAAAwB,MAAA,WAAA3E,CAAA;wBAAA,OAAAA,CAAA,CAAAqE,QAAA,KAAAA,QAAA;sBAAA,GAAAO,IAAA,WAAAC,CAAA,EAAAC,CAAA;wBAAA,OAAAD,CAAA,CAAAT,OAAA,CAAAE,OAAA,oBAAAQ,CAAA,CAAAV,OAAA,CAAAE,OAAA;sBAAA;sBACAnH,aAAA;sBACA8F,MAAA,CAAA/F,WAAA,CAAAC,aAAA,EAAAnF,aAAA;sBACAiC,YAAA,CAAA2D,IAAA;wBACAyG,QAAA,EAAAA,QAAA;wBACArM,aAAA,EAAAA,aAAA;wBACAmF,aAAA,EAAAA,aAAA;wBACA4H,WAAA;wBACAhD,SAAA;wBACAiD,UAAA;wBACA9J,KAAA;wBACA2G,WAAA;sBACA;oBAAA;oBAAA;sBAAA,OAAA6C,SAAA,CAAArI,IAAA;kBAAA;gBAAA,GAAA2H,KAAA;cAAA;cAAAF,UAAA,CAAA5L,CAAA;YAAA;cAAA,KAAA6L,MAAA,GAAAD,UAAA,CAAA3L,CAAA,IAAAC,IAAA;gBAAA8L,SAAA,CAAAjI,IAAA;gBAAA;cAAA;cAAA,OAAAiI,SAAA,CAAAe,aAAA,CAAAjB,KAAA;YAAA;cAAAE,SAAA,CAAAjI,IAAA;cAAA;YAAA;cAAAiI,SAAA,CAAAjI,IAAA;cAAA;YAAA;cAAAiI,SAAA,CAAAlI,IAAA;cAAAkI,SAAA,CAAAgB,EAAA,GAAAhB,SAAA;cAAAJ,UAAA,CAAApL,CAAA,CAAAwL,SAAA,CAAAgB,EAAA;YAAA;cAAAhB,SAAA,CAAAlI,IAAA;cAAA8H,UAAA,CAAAnL,CAAA;cAAA,OAAAuL,SAAA,CAAAiB,MAAA;YAAA;cAEAlC,MAAA,CAAAhJ,YAAA,GAAAA,YAAA,CAAA2K,IAAA,WAAAC,CAAA,EAAAC,CAAA;gBAAA,OAAAD,CAAA,CAAAR,QAAA,CAAAe,aAAA,CAAAN,CAAA,CAAAT,QAAA;cAAA;cACApB,MAAA,CAAAlK,UAAA;YAAA;YAAA;cAAA,OAAAmL,SAAA,CAAA7H,IAAA;UAAA;QAAA,GAAA6G,QAAA;MAAA;IACA;IACAmC,eAAA,WAAAA,gBAAAhN,IAAA;MAAA,IAAAiN,MAAA;MACA,IAAA/M,UAAA,GAAAF,IAAA,CAAAyE,KAAA,CAAA6H,MAAA,WAAA3E,CAAA;QAAA,OAAAA,CAAA,CAAAzH,UAAA;MAAA,GAAAwH,GAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAzH,UAAA;MAAA,GAAA+E,MAAA,WAAAuH,CAAA,EAAAC,CAAA;QAAA,OAAAQ,MAAA,CAAA9M,GAAA,CAAAqM,CAAA,EAAAC,CAAA;MAAA,QAAAjN,IAAA;MACA,IAAAU,UAAA,CAAAK,QAAA,OAAAP,IAAA,CAAAE,UAAA;QACA;UACAgN,KAAA;QACA;MACA;QACA;UACAA,KAAA;QACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAnN,IAAA;MACAA,IAAA,CAAAyE,KAAA,CAAAc,IAAA;QACArF,UAAA;QACA6L,OAAA;MACA;IACA;IACAqB,UAAA,WAAAA,WAAApN,IAAA,EAAA2H,CAAA;MACA3H,IAAA,CAAAyE,KAAA,CAAA4F,MAAA,CAAA1C,CAAA;IACA;IACA0F,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MAAA,WAAAlK,kBAAA,CAAAhE,OAAA,mBAAAiE,oBAAA,CAAAjE,OAAA,IAAAkE,IAAA,UAAAiK,SAAA;QAAA,IAAAxM,IAAA,EAAAM,SAAA,EAAAmM,UAAA,EAAAhN,IAAA,EAAA4B,QAAA,EAAAqL,WAAA,EAAAC,OAAA,EAAAC,GAAA,EAAAlJ,KAAA,EAAAmJ,aAAA,EAAAC,WAAA,EAAAC,OAAA,EAAA9N,IAAA,EAAAL,aAAA,EAAAoO,WAAA,EAAAC,OAAA,EAAAC,MAAA;QAAA,WAAA5K,oBAAA,CAAAjE,OAAA,IAAAoE,IAAA,UAAA0K,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxK,IAAA,GAAAwK,SAAA,CAAAvK,IAAA;YAAA;cACA7C,IAAA,GAAAuM,MAAA,CAAAvM,IAAA;cACAM,SAAA,GAAAN,IAAA,CAAAM,SAAA;cAAA,KACAA,SAAA;gBAAA8M,SAAA,CAAAvK,IAAA;gBAAA;cAAA;cAAAuK,SAAA,CAAAvK,IAAA;cAAA,OACA,IAAAwK,mBAAA,EAAA/M,SAAA;YAAA;cAAAmM,UAAA,GAAAW,SAAA,CAAApK,IAAA;cACA,IAAAyJ,UAAA,CAAA1E,IAAA;gBACAtI,IAAA,GAAAgN,UAAA,CAAAhN,IAAA;gBAEAO,IAAA,CAAAsN,WAAA,GAAA7N,IAAA,CAAA6N,WAAA;gBACAtN,IAAA,CAAA2F,OAAA,GAAAlG,IAAA,CAAAgI,cAAA;gBACAzH,IAAA,CAAAuN,YAAA,GAAA9N,IAAA,CAAA8N,YAAA;gBACAvN,IAAA,CAAAwN,WAAA,GAAA/N,IAAA,CAAA+N,WAAA;gBACAxN,IAAA,CAAAE,SAAA,GAAAT,IAAA,CAAAS,SAAA;gBAEA,IAAAT,IAAA,CAAAgO,QAAA;kBACApM,QAAA,GAAAqM,IAAA,CAAAC,KAAA,CAAAlO,IAAA,CAAAgO,QAAA;kBACAlB,MAAA,CAAAlL,QAAA,GAAAkL,MAAA,CAAAlL,QAAA,CAAAkK,MAAA,WAAA3E,CAAA;oBAAA,OAAAvF,QAAA,CAAAsF,GAAA,WAAA+E,CAAA;sBAAA,OAAAA,CAAA,CAAA/G,EAAA;oBAAA,GAAAkC,QAAA,CAAAD,CAAA,CAAAjC,EAAA;kBAAA;kBAAA+H,WAAA,OAAA/N,2BAAA,CAAAN,OAAA,EACAgD,QAAA;kBAAA;oBAAA,KAAAqL,WAAA,CAAA5N,CAAA,MAAA6N,OAAA,GAAAD,WAAA,CAAA3N,CAAA,IAAAC,IAAA;sBAAA4N,GAAA,GAAAD,OAAA,CAAAzN,KAAA;sBACA,KAAAqN,MAAA,CAAAlL,QAAA,CAAAsF,GAAA,WAAAC,CAAA;wBAAA,OAAAA,CAAA,CAAAjC,EAAA;sBAAA,GAAAkC,QAAA,CAAA+F,GAAA,CAAAjI,EAAA;wBACAiI,GAAA,CAAAgB,UAAA;wBACAhB,GAAA,CAAAiB,eAAA;wBACAtB,MAAA,CAAAlL,QAAA,CAAAmD,IAAA,CAAAoI,GAAA;sBACA;oBACA;kBAAA,SAAAvN,GAAA;oBAAAqN,WAAA,CAAApN,CAAA,CAAAD,GAAA;kBAAA;oBAAAqN,WAAA,CAAAnN,CAAA;kBAAA;gBACA;cACA;cAAA6N,SAAA,CAAAvK,IAAA;cAAA,OACA,IAAAiL,mCAAA;gBAAAxN,SAAA,EAAAA;cAAA;YAAA;cAAAoD,KAAA,GAAA0J,SAAA,CAAApK,IAAA;cACA6J,aAAA,OAAAnG,GAAA;cAAAoG,WAAA,OAAAnO,2BAAA,CAAAN,OAAA,EACAqF,KAAA;cAAA;gBAAA,KAAAoJ,WAAA,CAAAhO,CAAA,MAAAiO,OAAA,GAAAD,WAAA,CAAA/N,CAAA,IAAAC,IAAA;kBAAAC,IAAA,GAAA8N,OAAA,CAAA7N,KAAA;kBACA2N,aAAA,CAAAzN,GAAA,CAAAH,IAAA,CAAAyJ,UAAA;gBACA;cAAA,SAAArJ,GAAA;gBAAAyN,WAAA,CAAAxN,CAAA,CAAAD,GAAA;cAAA;gBAAAyN,WAAA,CAAAvN,CAAA;cAAA;cACAX,aAAA;cAAAoO,WAAA,OAAArO,2BAAA,CAAAN,OAAA,EACAwO,aAAA;cAAAO,SAAA,CAAAxK,IAAA;cAAAsK,MAAA,oBAAA5K,oBAAA,CAAAjE,OAAA,IAAAkE,IAAA,UAAA2K,OAAA;gBAAA,IAAAxE,UAAA,EAAAqF,GAAA,EAAA3F,CAAA,EAAA4F,QAAA;gBAAA,WAAA1L,oBAAA,CAAAjE,OAAA,IAAAoE,IAAA,UAAAwL,QAAAC,SAAA;kBAAA,kBAAAA,SAAA,CAAAtL,IAAA,GAAAsL,SAAA,CAAArL,IAAA;oBAAA;sBAAA6F,UAAA,GAAAuE,OAAA,CAAA/N,KAAA;sBACA6O,GAAA,GAAArK,KAAA,CAAA6H,MAAA,WAAA3E,CAAA;wBAAA,OAAAA,CAAA,CAAA8B,UAAA,KAAAA,UAAA;sBAAA;sBACA,IAAAqF,GAAA,IAAAA,GAAA;wBACA3F,CAAA;0BACAM,UAAA,EAAAqF,GAAA,IAAArF,UAAA;0BACAqC,OAAA,EAAAgD,GAAA,IAAAhD,OAAA;0BACAzF,YAAA,EAAAyI,GAAA,IAAAzI;wBACA;wBACA0I,QAAA,GAAAD,GAAA,CAAApH,GAAA,WAAAC,CAAA;0BACA;4BACAzH,UAAA,EAAAyH,CAAA,CAAAzH,UAAA;4BACA6L,OAAA,EAAApE,CAAA,CAAAoE;0BACA;wBACA;wBACA5C,CAAA,CAAA1E,KAAA,GAAAsK,QAAA;wBACA5F,CAAA,CAAAjJ,UAAA,GAAA6O,QAAA,CAAArH,GAAA,WAAAC,CAAA;0BAAA,OAAAA,CAAA,CAAAzH,UAAA;wBAAA,GAAA+E,MAAA,WAAAuH,CAAA,EAAAC,CAAA;0BAAA,OAAAa,MAAA,CAAAnN,GAAA,CAAAqM,CAAA,EAAAC,CAAA;wBAAA,MAAAlM,QAAA;wBACAZ,aAAA,CAAA4F,IAAA,CAAA4D,CAAA;sBACA;oBAAA;oBAAA;sBAAA,OAAA8F,SAAA,CAAAjL,IAAA;kBAAA;gBAAA,GAAAiK,MAAA;cAAA;cAAAF,WAAA,CAAAlO,CAAA;YAAA;cAAA,KAAAmO,OAAA,GAAAD,WAAA,CAAAjO,CAAA,IAAAC,IAAA;gBAAAoO,SAAA,CAAAvK,IAAA;gBAAA;cAAA;cAAA,OAAAuK,SAAA,CAAAvB,aAAA,CAAAqB,MAAA;YAAA;cAAAE,SAAA,CAAAvK,IAAA;cAAA;YAAA;cAAAuK,SAAA,CAAAvK,IAAA;cAAA;YAAA;cAAAuK,SAAA,CAAAxK,IAAA;cAAAwK,SAAA,CAAAtB,EAAA,GAAAsB,SAAA;cAAAJ,WAAA,CAAA1N,CAAA,CAAA8N,SAAA,CAAAtB,EAAA;YAAA;cAAAsB,SAAA,CAAAxK,IAAA;cAAAoK,WAAA,CAAAzN,CAAA;cAAA,OAAA6N,SAAA,CAAArB,MAAA;YAAA;cAEAQ,MAAA,CAAA3N,aAAA,GAAAA,aAAA;cAEA2N,MAAA,CAAAtE,WAAA;YAAA;YAAA;cAAA,OAAAmF,SAAA,CAAAnK,IAAA;UAAA;QAAA,GAAAuJ,QAAA;MAAA;IAEA;IACA2B,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MAAA,WAAA/L,kBAAA,CAAAhE,OAAA,mBAAAiE,oBAAA,CAAAjE,OAAA,IAAAkE,IAAA,UAAA8L,SAAA;QAAA,IAAAnO,SAAA,EAAAoO,IAAA,EAAAC,OAAA;QAAA,WAAAjM,oBAAA,CAAAjE,OAAA,IAAAoE,IAAA,UAAA+L,UAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA7L,IAAA,GAAA6L,UAAA,CAAA5L,IAAA;YAAA;cACAuL,MAAA,CAAAzO,UAAA;cACAO,SAAA,GAAAkO,MAAA,CAAApO,IAAA,CAAAE,SAAA;cAAA,KACAA,SAAA;gBAAAuO,UAAA,CAAA5L,IAAA;gBAAA;cAAA;cACA3C,SAAA,GAAAA,SAAA,CAAAgL,OAAA;cAAAuD,UAAA,CAAA5L,IAAA;cAAA,OACA,IAAA6L,uBAAA,EAAAxO,SAAA;YAAA;cAAAwH,IAAA,GAAA+G,UAAA,CAAAzL,IAAA;cACA,IAAA0E,IAAA,CAAAK,IAAA;gBACAwG,OAAA,GAAA7G,IAAA,CAAAjI,IAAA;gBACA,IAAA8O,OAAA;kBACAH,MAAA,CAAA3N,cAAA,GAAA8N,OAAA;kBACAH,MAAA,CAAApO,IAAA,CAAA2O,SAAA,GAAAJ,OAAA,CAAA5J,EAAA;gBACA;cACA;YAAA;cAEAyJ,MAAA,CAAAzO,UAAA;YAAA;YAAA;cAAA,OAAA8O,UAAA,CAAAxL,IAAA;UAAA;QAAA,GAAAoL,QAAA;MAAA;IACA;IACAO,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA,CAAAA,OAAA,CAAAjP,IAAA;MACA,KAAA6F,KAAA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAzF,IAAA;QACA2E,EAAA;QACAgK,SAAA;QACA5D,OAAA;QACAzK,SAAA;QACAgN,WAAA;QACAwB,SAAA;QACA5O,SAAA;QACAG,IAAA;QACAlC,IAAA;QACAyK,WAAA;QACAmG,UAAA;QACApJ,OAAA;QACAqJ,MAAA;QACAC,MAAA;QACAC,MAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACA,KAAAhM,SAAA;MACA,KAAA3C,cAAA;MACA,KAAAD,WAAA;MACA,KAAA5B,aAAA;MACA,KAAAiC,YAAA;MACA,KAAAC,iBAAA;MACA,KAAAC,YAAA;MACA,KAAAM,QAAA;MACA,KAAAF,OAAA;MACA,KAAAK,WAAA;MACA,KAAAG,MAAA;QACA0N,IAAA;QACAC,QAAA;QACAC,MAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,EAAA;QACAC,GAAA;QACAC,OAAA;MACA;MACA,KAAApO,OAAA;MACA,KAAAE,KAAA;IACA;IACA8D,IAAA,WAAAA,KAAAjB,EAAA;MAAA,IAAAsL,OAAA;MAAA,WAAA5N,kBAAA,CAAAhE,OAAA,mBAAAiE,oBAAA,CAAAjE,OAAA,IAAAkE,IAAA,UAAA2N,SAAA;QAAA,IAAAxI,GAAA,EAAA1H,IAAA;QAAA,WAAAsC,oBAAA,CAAAjE,OAAA,IAAAoE,IAAA,UAAA0N,UAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAxN,IAAA,GAAAwN,UAAA,CAAAvN,IAAA;YAAA;cAAAuN,UAAA,CAAAvN,IAAA;cAAA,OACA,IAAAwN,iBAAA,EAAA1L,EAAA;YAAA;cAAA+C,GAAA,GAAA0I,UAAA,CAAApN,IAAA;cACAhD,IAAA,GAAA0H,GAAA,CAAAjI,IAAA;cAEA,IAAAO,IAAA,CAAApB,aAAA;gBACAqR,OAAA,CAAArR,aAAA,GAAA8O,IAAA,CAAAC,KAAA,CAAA3N,IAAA,CAAApB,aAAA;cACA;cAEA,IAAAoB,IAAA,CAAAa,YAAA;gBACAoP,OAAA,CAAApP,YAAA,GAAA6M,IAAA,CAAAC,KAAA,CAAA3N,IAAA,CAAAa,YAAA;cACA;cAEA,IAAAb,IAAA,CAAAc,iBAAA;gBACAmP,OAAA,CAAAnP,iBAAA,GAAA4M,IAAA,CAAAC,KAAA,CAAA3N,IAAA,CAAAc,iBAAA;cACA;cAEA,IAAAd,IAAA,CAAAqB,QAAA;gBACA4O,OAAA,CAAA5O,QAAA,GAAAqM,IAAA,CAAAC,KAAA,CAAA3N,IAAA,CAAAqB,QAAA;cACA;cAEA,IAAArB,IAAA,CAAAe,YAAA;gBACAkP,OAAA,CAAAlP,YAAA,GAAA2M,IAAA,CAAAC,KAAA,CAAA3N,IAAA,CAAAe,YAAA;cACA;cAEA,IAAAf,IAAA,CAAA2B,MAAA;gBACAsO,OAAA,CAAAtO,MAAA,GAAA+L,IAAA,CAAAC,KAAA,CAAA3N,IAAA,CAAA2B,MAAA;cACA;cAEA,IAAA3B,IAAA,CAAA4B,OAAA;gBACAqO,OAAA,CAAArO,OAAA,GAAA8L,IAAA,CAAAC,KAAA,CAAA3N,IAAA,CAAA4B,OAAA;cACA;cAEA,IAAA5B,IAAA,CAAA8B,KAAA;gBACAmO,OAAA,CAAAnO,KAAA,GAAA4L,IAAA,CAAAC,KAAA,CAAA3N,IAAA,CAAA8B,KAAA;cACA;cAEAmO,OAAA,CAAAjQ,IAAA,GAAAA,IAAA;cAAAoQ,UAAA,CAAAvN,IAAA;cAAA,OACAoN,OAAA,CAAA9B,aAAA;YAAA;YAAA;cAAA,OAAAiC,UAAA,CAAAnN,IAAA;UAAA;QAAA,GAAAiN,QAAA;MAAA;IAEA;IACAI,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MAAA,WAAAlO,kBAAA,CAAAhE,OAAA,mBAAAiE,oBAAA,CAAAjE,OAAA,IAAAkE,IAAA,UAAAiO,UAAA;QAAA,IAAAxQ,IAAA,EAAAyQ,WAAA,EAAAC,OAAA,EAAAxG,CAAA,EAAA/K,UAAA,EAAAwR,WAAA,EAAAC,OAAA,EAAA3R,IAAA,EAAA4R,KAAA;QAAA,WAAAvO,oBAAA,CAAAjE,OAAA,IAAAoE,IAAA,UAAAqO,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAnO,IAAA,GAAAmO,UAAA,CAAAlO,IAAA;YAAA;cAAAkO,UAAA,CAAAlO,IAAA;cAAA,OACA0N,OAAA,CAAA/K,KAAA,SAAAwL,QAAA;YAAA;cACAhR,IAAA,GAAAwD,MAAA,CAAAC,MAAA,KAAA8M,OAAA,CAAAvQ,IAAA;cAAAyQ,WAAA,OAAA9R,2BAAA,CAAAN,OAAA,EAEAkS,OAAA,CAAA3R,aAAA;cAAAmS,UAAA,CAAAnO,IAAA;cAAA6N,WAAA,CAAA3R,CAAA;YAAA;cAAA,KAAA4R,OAAA,GAAAD,WAAA,CAAA1R,CAAA,IAAAC,IAAA;gBAAA+R,UAAA,CAAAlO,IAAA;gBAAA;cAAA;cAAAqH,CAAA,GAAAwG,OAAA,CAAAxR,KAAA;cACAC,UAAA,GAAA+K,CAAA,CAAAxG,KAAA,CAAA6H,MAAA,WAAA3E,CAAA;gBAAA,OAAAA,CAAA,CAAAzH,UAAA;cAAA,GAAAwH,GAAA,WAAAC,CAAA;gBAAA,OAAAA,CAAA,CAAAzH,UAAA;cAAA,GAAA+E,MAAA,WAAAuH,CAAA,EAAAC,CAAA;gBAAA,OAAA6E,OAAA,CAAAnR,GAAA,CAAAqM,CAAA,EAAAC,CAAA;cAAA;cAAA,MACAvM,UAAA,CAAAK,QAAA,OAAA0K,CAAA,CAAA/K,UAAA;gBAAA4R,UAAA,CAAAlO,IAAA;gBAAA;cAAA;cACA0N,OAAA,CAAAvI,QAAA;cAAA,OAAA+I,UAAA,CAAAE,MAAA;YAAA;cAAAF,UAAA,CAAAlO,IAAA;cAAA;YAAA;cAAAkO,UAAA,CAAAlO,IAAA;cAAA;YAAA;cAAAkO,UAAA,CAAAnO,IAAA;cAAAmO,UAAA,CAAAG,EAAA,GAAAH,UAAA;cAAAN,WAAA,CAAAnR,CAAA,CAAAyR,UAAA,CAAAG,EAAA;YAAA;cAAAH,UAAA,CAAAnO,IAAA;cAAA6N,WAAA,CAAAlR,CAAA;cAAA,OAAAwR,UAAA,CAAAhF,MAAA;YAAA;cAAA4E,WAAA,OAAAhS,2BAAA,CAAAN,OAAA,EAKAkS,OAAA,CAAAzP,iBAAA;cAAAiQ,UAAA,CAAAnO,IAAA;cAAA+N,WAAA,CAAA7R,CAAA;YAAA;cAAA,KAAA8R,OAAA,GAAAD,WAAA,CAAA5R,CAAA,IAAAC,IAAA;gBAAA+R,UAAA,CAAAlO,IAAA;gBAAA;cAAA;cAAA5D,IAAA,GAAA2R,OAAA,CAAA1R,KAAA;cAAA,IACAD,IAAA,CAAAiD,WAAA;gBAAA6O,UAAA,CAAAlO,IAAA;gBAAA;cAAA;cACA0N,OAAA,CAAAvI,QAAA;cAAA,OAAA+I,UAAA,CAAAE,MAAA;YAAA;cAAA,IAGAhS,IAAA,CAAA6H,MAAA;gBAAAiK,UAAA,CAAAlO,IAAA;gBAAA;cAAA;cACA0N,OAAA,CAAAvI,QAAA;cAAA,OAAA+I,UAAA,CAAAE,MAAA;YAAA;cAAA,IAGAhS,IAAA,CAAAiI,UAAA;gBAAA6J,UAAA,CAAAlO,IAAA;gBAAA;cAAA;cACA0N,OAAA,CAAAvI,QAAA;cAAA,OAAA+I,UAAA,CAAAE,MAAA;YAAA;cAAA,IAGAhS,IAAA,CAAAkI,KAAA;gBAAA4J,UAAA,CAAAlO,IAAA;gBAAA;cAAA;cACA0N,OAAA,CAAAvI,QAAA;cAAA,OAAA+I,UAAA,CAAAE,MAAA;YAAA;cAAAF,UAAA,CAAAlO,IAAA;cAAA;YAAA;cAAAkO,UAAA,CAAAlO,IAAA;cAAA;YAAA;cAAAkO,UAAA,CAAAnO,IAAA;cAAAmO,UAAA,CAAAjF,EAAA,GAAAiF,UAAA;cAAAJ,WAAA,CAAArR,CAAA,CAAAyR,UAAA,CAAAjF,EAAA;YAAA;cAAAiF,UAAA,CAAAnO,IAAA;cAAA+N,WAAA,CAAApR,CAAA;cAAA,OAAAwR,UAAA,CAAAhF,MAAA;YAAA;cAKA/L,IAAA,CAAApB,aAAA,GAAA8O,IAAA,CAAAyD,SAAA,CAAAZ,OAAA,CAAA3R,aAAA;cACAoB,IAAA,CAAAa,YAAA,GAAA6M,IAAA,CAAAyD,SAAA,CAAAZ,OAAA,CAAA1P,YAAA;cACAb,IAAA,CAAAc,iBAAA,GAAA4M,IAAA,CAAAyD,SAAA,CAAAZ,OAAA,CAAAzP,iBAAA;cACAd,IAAA,CAAAqB,QAAA,GAAAqM,IAAA,CAAAyD,SAAA,CAAAZ,OAAA,CAAAlP,QAAA;cACArB,IAAA,CAAAe,YAAA,GAAA2M,IAAA,CAAAyD,SAAA,CAAAZ,OAAA,CAAAxP,YAAA;cACAf,IAAA,CAAA2B,MAAA,GAAA+L,IAAA,CAAAyD,SAAA,CAAAZ,OAAA,CAAA5O,MAAA;cACA3B,IAAA,CAAA4B,OAAA,GAAA8L,IAAA,CAAAyD,SAAA,CAAAZ,OAAA,CAAA3O,OAAA;cACA5B,IAAA,CAAA8B,KAAA,GAAA4L,IAAA,CAAAyD,SAAA,CAAAZ,OAAA,CAAAzO,KAAA;cAAA,MAEA9B,IAAA,CAAA2E,EAAA;gBAAAoM,UAAA,CAAAlO,IAAA;gBAAA;cAAA;cAAAkO,UAAA,CAAAnO,IAAA;cAEA2N,OAAA,CAAA5Q,UAAA;cAAAoR,UAAA,CAAAlO,IAAA;cAAA,OACA,IAAAuO,oBAAA,EAAApR,IAAA;YAAA;cACAuQ,OAAA,CAAA5Q,UAAA;cACA,IAAA+H,GAAA,CAAAK,IAAA;gBACAwI,OAAA,CAAAc,UAAA;cACA;cAAAN,UAAA,CAAAlO,IAAA;cAAA;YAAA;cAAAkO,UAAA,CAAAnO,IAAA;cAAAmO,UAAA,CAAAO,EAAA,GAAAP,UAAA;cAEAR,OAAA,CAAA5Q,UAAA;YAAA;cAAAoR,UAAA,CAAAlO,IAAA;cAAA;YAAA;cAAAkO,UAAA,CAAAnO,IAAA;cAIA2N,OAAA,CAAA5Q,UAAA;cAAAoR,UAAA,CAAAlO,IAAA;cAAA,OACA,IAAA0O,iBAAA,EAAAvR,IAAA;YAAA;cAAA0H,KAAA,GAAAqJ,UAAA,CAAA/N,IAAA;cACAuN,OAAA,CAAA5Q,UAAA;cAAA,MACA+H,KAAA,CAAAK,IAAA;gBAAAgJ,UAAA,CAAAlO,IAAA;gBAAA;cAAA;cACA0N,OAAA,CAAAc,UAAA;cAAAN,UAAA,CAAAlO,IAAA;cAAA,OACA0N,OAAA,CAAA3K,IAAA,CAAA8B,KAAA,CAAAjI,IAAA,CAAAkF,EAAA;YAAA;cAAAoM,UAAA,CAAAlO,IAAA;cAAA;YAAA;cAAAkO,UAAA,CAAAnO,IAAA;cAAAmO,UAAA,CAAAS,EAAA,GAAAT,UAAA;cAGAR,OAAA,CAAA5Q,UAAA;YAAA;YAAA;cAAA,OAAAoR,UAAA,CAAA9N,IAAA;UAAA;QAAA,GAAAuN,SAAA;MAAA;IAGA;EACA;AACA", "ignoreList": []}]}