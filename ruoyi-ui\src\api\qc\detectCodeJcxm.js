import request from '@/utils/request'

// 查询检测编码配置列表
export function listDetectCodeJcxm(query) {
  return request({
    url: '/qc/detectCodeJcxm/list',
    method: 'get',
    params: query
  })
}

// 查询检测编码配置详细
export function getDetectCodeJcxm(id) {
  return request({
    url: '/qc/detectCodeJcxm/' + id,
    method: 'get'
  })
}

// 新增检测编码配置
export function addDetectCodeJcxm(data) {
  return request({
    url: '/qc/detectCodeJcxm',
    method: 'post',
    data: data
  })
}

// 修改检测编码配置
export function updateDetectCodeJcxm(data) {
  return request({
    url: '/qc/detectCodeJcxm',
    method: 'put',
    data: data
  })
}

// 删除检测编码配置
export function delDetectCodeJcxm(id) {
  return request({
    url: '/qc/detectCodeJcxm/' + id,
    method: 'delete'
  })
}

// 导出检测编码配置
export function exportDetectCodeJcxm(query) {
  return request({
    url: '/qc/detectCodeJcxm/export',
    method: 'get',
    params: query
  })
}

export function allDetectCodeJcxm(query) {
  return request({
    url: '/qc/detectCodeJcxm/all',
    method: 'get',
    params: query
  })
}

export function batchSaveDetectCodeJcxm(data) {
  return request({
    url: '/qc/detectCodeJcxm/batchSave',
    method: 'put',
    data: data
  })
}


