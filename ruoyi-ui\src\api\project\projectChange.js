import request from '@/utils/request'

// 查询项目状态更改记录列表
export function listProjectChange(query) {
  return request({
    url: '/project/projectChange/list',
    method: 'get',
    params: query
  })
}

// 查询项目状态更改记录详细
export function getProjectChange(id) {
  return request({
    url: '/project/projectChange/' + id,
    method: 'get'
  })
}

// 新增项目状态更改记录
export function addProjectChange(data) {
  return request({
    url: '/project/projectChange',
    method: 'post',
    data: data
  })
}

// 修改项目状态更改记录
export function updateProjectChange(data) {
  return request({
    url: '/project/projectChange',
    method: 'put',
    data: data
  })
}

// 删除项目状态更改记录
export function delProjectChange(id) {
  return request({
    url: '/project/projectChange/' + id,
    method: 'delete'
  })
}

// 导出项目状态更改记录
export function exportProjectChange(query) {
  return request({
    url: '/project/projectChange/export',
    method: 'get',
    params: query
  })
}