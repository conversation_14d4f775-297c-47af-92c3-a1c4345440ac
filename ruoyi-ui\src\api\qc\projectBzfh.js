import request from '@/utils/request'

// 查询包装防护列表
export function listProjectBzfh(query) {
  return request({
    url: '/qc/projectBzfh/list',
    method: 'get',
    params: query
  })
}

// 查询包装防护详细
export function getProjectBzfh(id) {
  return request({
    url: '/qc/projectBzfh/' + id,
    method: 'get'
  })
}

// 新增包装防护
export function addProjectBzfh(data) {
  return request({
    url: '/qc/projectBzfh',
    method: 'post',
    data: data
  })
}

// 修改包装防护
export function updateProjectBzfh(data) {
  return request({
    url: '/qc/projectBzfh',
    method: 'put',
    data: data
  })
}

// 删除包装防护
export function delProjectBzfh(id) {
  return request({
    url: '/qc/projectBzfh/' + id,
    method: 'delete'
  })
}

// 导出包装防护
export function exportProjectBzfh(query) {
  return request({
    url: '/qc/projectBzfh/export',
    method: 'get',
    params: query
  })
}

export function allProjectBzfh(query) {
  return request({
    url: '/qc/projectBzfh/all',
    method: 'get',
    params: query
  })
}
