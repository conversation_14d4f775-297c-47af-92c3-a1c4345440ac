import request from '@/utils/request'

// 查询生产可行性评估列表
export function listPg(query) {
  return request({
    url: '/sop/pg/list',
    method: 'get',
    params: query
  })
}

// 查询生产可行性评估详细
export function getPg(id) {
  return request({
    url: '/sop/pg/' + id,
    method: 'get'
  })
}

// 新增生产可行性评估
export function addPg(data) {
  return request({
    url: '/sop/pg',
    method: 'post',
    data: data
  })
}

// 修改生产可行性评估
export function updatePg(data) {
  return request({
    url: '/sop/pg',
    method: 'put',
    data: data
  })
}

// 删除生产可行性评估
export function delPg(id) {
  return request({
    url: '/sop/pg/' + id,
    method: 'delete'
  })
}

// 导出生产可行性评估
export function exportPg(id) {
  return request({
    url: '/sop/pg/export/' + id,
    method: 'get',
  })
}

export function allPg(query) {
  return request({
    url: '/sop/pg/all',
    method: 'get',
    params: query
  })
}
