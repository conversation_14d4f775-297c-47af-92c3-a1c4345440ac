import request from '@/utils/request'

// 查询生产包材损耗列表
export function listProjectOfferBcLoss(query) {
  return request({
    url: '/project/projectOfferBcLoss/list',
    method: 'get',
    params: query
  })
}

// 查询生产包材损耗详细
export function getProjectOfferBcLoss(id) {
  return request({
    url: '/project/projectOfferBcLoss/' + id,
    method: 'get'
  })
}

// 新增生产包材损耗
export function addProjectOfferBcLoss(data) {
  return request({
    url: '/project/projectOfferBcLoss',
    method: 'post',
    data: data
  })
}

// 修改生产包材损耗
export function updateProjectOfferBcLoss(data) {
  return request({
    url: '/project/projectOfferBcLoss',
    method: 'put',
    data: data
  })
}

// 删除生产包材损耗
export function delProjectOfferBcLoss(id) {
  return request({
    url: '/project/projectOfferBcLoss/' + id,
    method: 'delete'
  })
}

// 导出生产包材损耗
export function exportProjectOfferBcLoss(query) {
  return request({
    url: '/project/projectOfferBcLoss/export',
    method: 'get',
    params: query
  })
}


// 查询生产包材损耗列表
export function itemAll(query) {
  return request({
    url: '/project/projectOfferBcLoss/itemAll',
    method: 'get',
    params: query
  })
}
