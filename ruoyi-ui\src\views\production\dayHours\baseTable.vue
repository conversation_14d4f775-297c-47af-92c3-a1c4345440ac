<template>
  <div>
    <el-divider content-position="left" >{{title}}</el-divider>

    <div class="table-wrapper" >
      <table class="base-table small-table" >
        <tr>
          <th style="width: 50px" >
            <i class="el-icon-circle-plus-outline" @click="showUser" />
          </th>
          <th style="width: 120px" >工号</th>
          <th style="width: 100px" >姓名</th>
          <th style="width: 200px" >上工时间</th>
          <th style="width: 200px" >下工时间</th>
          <th style="width: 120px" >考勤时长</th>
          <th style="width: 120px" >休息工时</th>
          <th style="width: 120px" >工资工时</th>
          <th style="width: 100px" >
            修正工时
            <el-tooltip >
              <div slot="content">
                修正过后以修正的工时为准
              </div>
              <i class="el-icon-question" />
            </el-tooltip>
          </th>
          <th >备注</th>
        </tr>
        <tr v-for="(item,z) in userArray.filter(i=>this.sailings === i.sailings)" :key="z">
          <td>
            <i class="el-icon-remove-outline" @click="delItem(item.userCode)" />
          </td>
          <td>
            <span style="color: #00afff;cursor: pointer" @click="attendanceLog(item.userId)" >{{item.userCode}}</span>
          </td>
          <td>{{item.nickName}}</td>
          <td>
            <el-date-picker
              clearable
              v-model="item.startTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              size="mini"
              style="width: 180px"
              @change="computeMinutes(item)"
            />
          </td>
          <td>
            <el-date-picker
              clearable
              v-model="item.endTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              size="mini"
              style="width: 180px"
              @change="computeMinutes(item)"
            />
          </td>
          <td>{{minutesToHours(item.minutes).toFixed(2)}}</td>
          <td>
            <span style="color: #00afff;cursor: pointer" @click="showRestMinutes(item)" >
              {{minutesToHours(item.restMinutes).toFixed(2)}}
            </span>
          </td>
          <td>{{minutesToHours(item.wagesMinutes).toFixed(2)}}</td>
          <td >
            <el-input v-model="item.finalMinutes" autosize size="mini" @input="$emit('computeItemData')" />
          </td>
          <td>
            <el-input v-model="item.remark" autosize size="mini" />
          </td>
        </tr>
      </table>
    </div>

    <el-dialog title="选择用户" :visible.sync="userOpen" width="400px" :close-on-click-modal="false"  append-to-body>
      <el-select v-model="currentUserId" filterable @change="addItem" >
        <el-option
          v-for="item in userList"
          :key="item.userId"
          :label="item.nickName"          :value="item.userId"
        />
      </el-select>
    </el-dialog>

    <el-dialog :close-on-click-modal="false" :visible.sync="attendanceLogOpen" append-to-body width="600px">
      <table class="base-table small-table">
        <tr>
          <th style="width: 320px">打卡地址</th>
          <th style="width: 180px">打卡时间</th>
        </tr>
        <tr v-for="item in attendanceLogList" :key="item.id" >
          <td>{{item.userAddress}}</td>
          <td>{{item.userCheckTime}}</td>
        </tr>
      </table>
    </el-dialog>

    <el-dialog title="休息工时(请录入分钟数,会转成小时数)" :visible.sync="minutesOpen" width="400px" :close-on-click-modal="false"  append-to-body>
      <el-input v-model="currentRow.restMinutes" type="number" size="mini" @input="computeMinutes(currentRow)" >
        <template #append >分钟</template>
      </el-input>
    </el-dialog>

  </div>
</template>
<script >
import {
  calculateIntersectionMinutes,
  diffMinutes,
  findClosestTimeString,
  roundDownToHalfHour
} from "@/utils/production/time";
import {allAttendanceLog} from "@/api/hr/attendanceLog";
import Template from "@/views/filemanage/template/index.vue";

export default {
  name: 'dayHoursBaseTable',
  components: {Template},
  props: {
    dayHours: {
      type: Object,
      required: true,
    },
    sailings: {
      type: String,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    userArray: {
      type: Array,
      required: true,
    },
    userList: {
      type: Array,
      required: true,
    },
    attendanceLogList: {
      type: Array,
      required: true,
    },
    restList: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      userOpen: false,
      attendanceLogOpen: false,
      minutesOpen: false,
      currentUserId: null,
      currentRow: {},
    }
  },
  async created() {
  },
  methods: {
    async showRestMinutes(item) {
      this.currentRow = item
      this.minutesOpen = true
    },
    async attendanceLog(userId) {
      const workDate = this.dayHours.workDate
      if(workDate) {
        const searchDateArray = [workDate,]
        searchDateArray.push(this.moment(workDate).add(1, 'days').format('YYYY-MM-DD'))
        const params = {
          userId,
          searchDateArray
        }
        try {
          this.btnLoading = true
          const attendanceLogList = await allAttendanceLog(params)
          for (const item of attendanceLogList) {
            if(item.userCheckTime) {
              item.userCheckTime = this.moment(item.userCheckTime).format('YYYY-MM-DD HH:mm:ss')
            }
          }
          this.attendanceLogList = attendanceLogList
          this.btnLoading = false
          this.attendanceLogOpen = true
        } catch (e) {
          this.btnLoading = false
        }
      }
    },
    showUser() {
      this.currentUserId = null
      this.userOpen = true
    },
    addItem() {
      const arr = this.userList.filter(i=> i.userId === this.currentUserId)
      if(arr && arr[0]) {
        if(!this.userArray.map(i=>i.userId).includes(this.currentUserId)) {
          const attendanceArr = this.attendanceLogList.filter(i=> i.userId === this.currentUserId).sort((a,b)=> a.userCheckTime - b.userCheckTime)
          if(attendanceArr && attendanceArr[1]){ //至少有两个
            const timesArray = attendanceArr.map(i=> this.moment(i.userCheckTime).format('YYYY-MM-DD HH:mm:ss'))
            const startDate = this.moment(this.dayHours.workDate).format('YYYY-MM-DD')
            let upStandTime = startDate + ' 08:30:00'
            let downStandTime =  startDate + ' 20:30:00'
            if(this.sailings === '1') {
              upStandTime = startDate + ' 20:30:00'
              downStandTime = this.moment(startDate).add(1, 'days').format('YYYY-MM-DD') + ' 08:30:00'
            }
            let upTime = findClosestTimeString(timesArray,upStandTime)
            const downTime = findClosestTimeString(timesArray,downStandTime)

            if(upTime && downTime) {
              if(upTime < upStandTime ) {//如果早于8点半,按8点半算
                upTime = upStandTime
              }
              const minutes = this.moment(downTime).diff(upTime,'minutes')
              const workPeriods = [
                {
                  start: this.moment(upTime).format('HH:mm'),
                  end: this.moment(downTime).format('HH:mm'),
                },
              ]
              const restMinutes = calculateIntersectionMinutes(workPeriods,this.restList)
              const wagesMinutes = this.subtract(minutes,restMinutes).toNumber()
              if(minutes > 0) {
                this.userArray.push({
                  userId: arr[0].userId,
                  userCode: arr[0].userCode,
                  nickName: arr[0].nickName,
                  startTime: upTime,
                  endTime: downTime,
                  sailings: this.sailings,
                  minutes,
                  restMinutes,
                  wagesMinutes: roundDownToHalfHour(wagesMinutes),
                })
              }
            }
          }
        }
      }
      this.$emit('computeItemData')
      this.userOpen = false
    },
    delItem(userCode) {
      const index = this.userArray.findIndex(i=> i.userCode === userCode)
      this.userArray.splice(index,1)
      this.$emit('computeItemData')
    },
    computeMinutes(row) {
      row.minutes = diffMinutes(row.endTime,row.startTime)
      row.wagesMinutes = roundDownToHalfHour(this.subtract(row.minutes,row.restMinutes).toNumber())
      this.$emit('computeItemData')
    },
  },
}
</script>
<style scoped lang="scss">

</style>
