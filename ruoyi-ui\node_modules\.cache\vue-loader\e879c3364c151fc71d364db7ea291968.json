{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\save.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\save.vue", "mtime": 1753954679644}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQge2dldERheUhvdXJzLCB1cGRhdGVEYXlIb3Vyc30gZnJvbSAiQC9hcGkvcHJvZHVjdGlvbi9kYXlIb3VycyI7DQppbXBvcnQgRGF5SG91cnNVc2VyVGFicyBmcm9tICJAL3ZpZXdzL3Byb2R1Y3Rpb24vZGF5SG91cnMvdXNlclRhYnMudnVlIjsNCmltcG9ydCB7YWxsUGxhbkFyZWFVc2VySG91cnMsIGdldERheUFyZWFIb3Vyc30gZnJvbSAiQC9hcGkvcHJvZHVjdGlvbi9wbGFuQXJlYUhvdXJzIjsNCmltcG9ydCBCYXNlQ2hhcnQgZnJvbSAiLi4vLi4vLi4vLi4vYmFzZUNoYXJ0cy52dWUiOw0KaW1wb3J0IHt1c2VyQWxsfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlciI7DQppbXBvcnQge2FsbEF0dGVuZGFuY2VSZXN0VGltZX0gZnJvbSAiQC9hcGkvaHIvYXR0ZW5kYW5jZVJlc3RUaW1lIjsNCmltcG9ydCB7Z2V0T3RoZXJEYXlNaW51dGVzQnlQYXJhbXMsIH0gZnJvbSAiQC9hcGkvcHJvZHVjdGlvbi9vdGhlck1pbnV0ZXMiOw0KaW1wb3J0IHthbGxNZXNIb3Vyc30gZnJvbSAiQC9hcGkvcHJvZHVjdGlvbi9tZXNIb3VycyI7DQppbXBvcnQge2FsbEF0dGVuZGFuY2VMb2d9IGZyb20gIkAvYXBpL2hyL2F0dGVuZGFuY2VMb2ciOw0KaW1wb3J0IHthbGxQcm9kdWN0aW9uR3JvdXB9IGZyb20gIkAvYXBpL3Byb2R1Y3Rpb24vcHJvZHVjdGlvbkdyb3VwIjsNCmltcG9ydCB7DQogIGNhbGN1bGF0ZUludGVyc2VjdGlvbk1pbnV0ZXMsDQogIGZpbmRDbG9zZXN0VGltZVN0cmluZywNCiAgZmluZEludGVyc2VjdGlvbiwNCiAgcm91bmREb3duVG9IYWxmSG91cg0KfSBmcm9tICJAL3V0aWxzL3Byb2R1Y3Rpb24vdGltZSI7DQppbXBvcnQgZm9ybSBmcm9tICJAL3ZpZXdzL2d4L2Zvcm0vaW5kZXgudnVlIjsNCmltcG9ydCB7cHVzaEhyVXNlckFuRGF0ZX0gZnJvbSAiQC9hcGkvaHIvYXR0ZW5kYW5jZSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ2RheUhvdXJzU2F2ZScsDQogIGNvbXBvbmVudHM6IHtCYXNlQ2hhcnQsIERheUhvdXJzVXNlclRhYnN9LA0KICBwcm9wczogew0KICAgIHJlYWRvbmx5OiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UsDQogICAgfSwNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBidG5Mb2FkaW5nOiBmYWxzZSwNCiAgICAgIGZvcm06IHt9LA0KICAgICAgcnVsZXM6IHt9LA0KICAgICAgdGFiT3B0aW9uczogWw0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICfnmb3nj60nLA0KICAgICAgICAgIHZhbHVlOiAnMCcsDQogICAgICAgICAgc3VtTWludXRlczogMCwNCiAgICAgICAgICB1c2VyTWludXRlczogMCwNCiAgICAgICAgICBsYWJvck1pbnV0ZXM6IDAsDQogICAgICAgICAgb3V0ZXJNaW51dGVzOiAwLA0KICAgICAgICAgIHdlaWdodE1pbnV0ZXM6IDAsDQogICAgICAgICAgb3RoZXJNaW51dGVzOiAwLA0KICAgICAgICAgIG1hbmFnZU1pbnV0ZXM6IDAsDQogICAgICAgICAgcWNNaW51dGVzOiAwLA0KICAgICAgICAgIHN1bU51bXM6IDAsDQogICAgICAgICAgdXNlck51bXM6IDAsDQogICAgICAgICAgbGFib3JOdW1zOiAwLA0KICAgICAgICAgIG91dGVyTnVtczogMCwNCiAgICAgICAgICB3ZWlnaHROdW1zOiAwLA0KICAgICAgICAgIG90aGVyTnVtczogMCwNCiAgICAgICAgICBtYW5hZ2VOdW1zOiAwLA0KICAgICAgICAgIHFjTnVtczogMCwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAn5pma54+tJywNCiAgICAgICAgICB2YWx1ZTogJzEnLA0KICAgICAgICAgIHN1bU1pbnV0ZXM6IDAsDQogICAgICAgICAgdXNlck1pbnV0ZXM6IDAsDQogICAgICAgICAgbGFib3JNaW51dGVzOiAwLA0KICAgICAgICAgIG91dGVyTWludXRlczogMCwNCiAgICAgICAgICB3ZWlnaHRNaW51dGVzOiAwLA0KICAgICAgICAgIG90aGVyTWludXRlczogMCwNCiAgICAgICAgICBtYW5hZ2VNaW51dGVzOiAwLA0KICAgICAgICAgIHFjTWludXRlczogMCwNCiAgICAgICAgICBzdW1OdW1zOiAwLA0KICAgICAgICAgIHVzZXJOdW1zOiAwLA0KICAgICAgICAgIGxhYm9yTnVtczogMCwNCiAgICAgICAgICBvdXRlck51bXM6IDAsDQogICAgICAgICAgd2VpZ2h0TnVtczogMCwNCiAgICAgICAgICBvdGhlck51bXM6IDAsDQogICAgICAgICAgbWFuYWdlTnVtczogMCwNCiAgICAgICAgICBxY051bXM6IDAsDQogICAgICAgIH0sDQogICAgICBdLA0KICAgICAgY3VycmVudFRhYjogJzAnLA0KICAgICAgdXNlck51bXNPcHRpb25zOiB7fSwNCiAgICAgIHVzZXJNaW51dGVzT3B0aW9uczoge30sDQogICAgICBob3Vyc1R5cGVPcHRpb25zOiB7fSwNCiAgICAgIGhvdXJzQ29tcG9zZU9wdGlvbnM6IHt9LA0KICAgICAgZGF5VXNlckxpc3Q6IFtdLA0KICAgICAgYXR0ZW5kYW5jZUxvZ0xpc3Q6IFtdLA0KICAgICAgdXNlckxpc3Q6IFtdLA0KICAgICAgcmVzdExpc3Q6IFtdLA0KICAgICAgd2VpZ2h0TWludXRlc0xpc3Q6IFtdLA0KICAgICAgb3RoZXJNaW51dGVzTGlzdDogW10sDQogICAgICBtYW5hZ2VNaW51dGVzTGlzdDogW10sDQogICAgICBxY01pbnV0ZXNMaXN0OiBbXSwNCiAgICAgIG1lc0hvdXJzTGlzdDogW10sDQogICAgICBleGNlcHRpb25PcHRpb25zOiBbDQogICAgICAgIHtsYWJlbDogJ+S4iuW3peiAg+WLpOW8guW4uCcsdmFsdWU6IDF9LA0KICAgICAgICB7bGFiZWw6ICfkuIvlt6XogIPli6TlvILluLgnLHZhbHVlOiAyfSwNCiAgICAgICAge2xhYmVsOiAnbWVz5LiK5bel5byC5bi4Jyx2YWx1ZTogM30sDQogICAgICAgIHtsYWJlbDogJ21lc+S4i+W3peW8guW4uCcsdmFsdWU6IDR9LA0KICAgICAgICB7bGFiZWw6ICfovazlnLrlvILluLgnLHZhbHVlOiA1fSwNCiAgICAgICAge2xhYmVsOiAn5pyJ5pWI5bel5pe25byC5bi4Jyx2YWx1ZTogNn0sDQogICAgICAgIHtsYWJlbDogJ3NhcOS4iuW3peW8guW4uCcsdmFsdWU6IDd9LA0KICAgICAgICB7bGFiZWw6ICdzYXDkuIvlt6XlvILluLgnLHZhbHVlOiA4fSwNCiAgICAgIF0sDQogICAgfQ0KICB9LA0KICBhc3luYyBjcmVhdGVkKCkgew0KICAgIHRoaXMudXNlckxpc3QgPSBhd2FpdCB1c2VyQWxsKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGFzeW5jIHNhaWxpbmdzQ2hhbmdlKHVzZXJDb2RlKXsvL21lc+ePreWIq+WPmOabtCzliLfmlrBtZXPlt6Xml7YNCiAgICAgIGF3YWl0IHRoaXMucmVmcmVzaEFyZWFIb3Vycyh0aGlzLmZvcm0uZmFjdG9yeSx0aGlzLmZvcm0ud29ya0RhdGUpDQogICAgfSwNCiAgICBhc3luYyBidWlsZENoYXJ0cygpIHsNCiAgICAgIGNvbnN0IGZvcm0gPSB0aGlzLmZvcm0NCg0KICAgICAgdGhpcy51c2VyTnVtc09wdGlvbnMgPSB0aGlzLmJ1aWxkT3B0aW9ucygi5LiK5bel5Lq65ZGY5YiG5biDIixbDQogICAgICAgIHtuYW1lOiAn5q2j5byP5belJyx2YWx1ZTogZm9ybS51c2VyTnVtc30sDQogICAgICAgIHtuYW1lOiAn56ew6YePJyx2YWx1ZTogZm9ybS53ZWlnaHROdW1zfSwNCiAgICAgICAge25hbWU6ICfpl7TmjqUnLHZhbHVlOiBmb3JtLm90aGVyTnVtc30sDQogICAgICAgIHtuYW1lOiAn566h55CGJyx2YWx1ZTogZm9ybS5tYW5hZ2VOdW1zfSwNCiAgICAgICAge25hbWU6ICfotKjmo4AnLHZhbHVlOiBmb3JtLnFjTnVtc30sDQogICAgICAgIHtuYW1lOiAn5Yqz5Yqh5belJyx2YWx1ZTogZm9ybS5sYWJvck51bXN9LA0KICAgICAgICB7bmFtZTogJ+WkluWMheW3pScsdmFsdWU6IGZvcm0ub3V0ZXJOdW1zfSwNCiAgICAgIF0pDQogICAgICB0aGlzLnVzZXJNaW51dGVzT3B0aW9ucyA9IHRoaXMuYnVpbGRPcHRpb25zKCLkuqfnur/kuIrlt6Xlt6Xml7bliIbluIMiLFsNCiAgICAgICAge25hbWU6ICfmraPlvI/lt6UnLHZhbHVlOiB0aGlzLm1pbnV0ZXNUb0hvdXJzKGZvcm0udXNlck1pbnV0ZXMpLnRvRml4ZWQoMikgfSwNCiAgICAgICAge25hbWU6ICflirPliqHlt6UnLHZhbHVlOiB0aGlzLm1pbnV0ZXNUb0hvdXJzKGZvcm0ubGFib3JNaW51dGVzKS50b0ZpeGVkKDIpIH0sDQogICAgICAgIHtuYW1lOiAn5aSW5YyF5belJyx2YWx1ZTogdGhpcy5taW51dGVzVG9Ib3Vycyhmb3JtLm91dGVyTWludXRlcykudG9GaXhlZCgyKSB9LA0KICAgICAgXSkNCiAgICAgIHRoaXMuaG91cnNUeXBlT3B0aW9ucyA9IHRoaXMuYnVpbGRPcHRpb25zKCLkuqfnur/lt6Xml7bmgKfotKjliIbluIMiLFsNCiAgICAgICAge25hbWU6ICfmnInmlYgnLHZhbHVlOiB0aGlzLm1pbnV0ZXNUb0hvdXJzKGZvcm0uZWZmZWN0aXZlTWludXRlcykudG9GaXhlZCgyKX0sDQogICAgICAgIHtuYW1lOiAn5peg5pWIJyx2YWx1ZTogdGhpcy5taW51dGVzVG9Ib3Vycyhmb3JtLmludmFsaWRNaW51dGVzKS50b0ZpeGVkKDIpfSwNCiAgICAgICAge25hbWU6ICfkvJHmga8nLHZhbHVlOiB0aGlzLm1pbnV0ZXNUb0hvdXJzKGZvcm0ucmVzdE1pbnV0ZXMpLnRvRml4ZWQoMil9LA0KICAgICAgXSkNCiAgICAgIHRoaXMuaG91cnNDb21wb3NlT3B0aW9ucyA9IHRoaXMuYnVpbGRPcHRpb25zKCLlt6Xml7bnu4TmiJDliIbluIMiLFsNCiAgICAgICAge25hbWU6ICfkuqfnur8nLHZhbHVlOiB0aGlzLm1pbnV0ZXNUb0hvdXJzKGZvcm0uc3VtTWludXRlcykudG9GaXhlZCgyKX0sDQogICAgICAgIHtuYW1lOiAn56ew6YePJyx2YWx1ZTogdGhpcy5taW51dGVzVG9Ib3Vycyhmb3JtLndlaWdodE1pbnV0ZXMpLnRvRml4ZWQoMil9LA0KICAgICAgICB7bmFtZTogJ+mXtOaOpScsdmFsdWU6IHRoaXMubWludXRlc1RvSG91cnMoZm9ybS5vdGhlck1pbnV0ZXMpLnRvRml4ZWQoMil9LA0KICAgICAgICB7bmFtZTogJ+euoeeQhicsdmFsdWU6IHRoaXMubWludXRlc1RvSG91cnMoZm9ybS5tYW5hZ2VNaW51dGVzKS50b0ZpeGVkKDIpfSwNCiAgICAgICAgLy8ge25hbWU6ICfotKjmo4AnLHZhbHVlOiB0aGlzLm1pbnV0ZXNUb0hvdXJzKGZvcm0ucWNNaW51dGVzKS50b0ZpeGVkKDIpfSwNCiAgICAgIF0pDQoNCiAgICAgIGF3YWl0IHRoaXMuJG5leHRUaWNrKCkNCiAgICAgIGNvbnN0IHVzZXJOdW1zQ2hhcnQgPSB0aGlzLiRyZWZzLnVzZXJOdW1zQ2hhcnQNCiAgICAgIGlmKHVzZXJOdW1zQ2hhcnQgJiYgdGhpcy51c2VyTnVtc09wdGlvbnMpIHsNCiAgICAgICAgYXdhaXQgdXNlck51bXNDaGFydC5pbml0KHRoaXMudXNlck51bXNPcHRpb25zKQ0KICAgICAgfQ0KDQogICAgICBjb25zdCB1c2VyTWludXRlc0NoYXJ0ID0gdGhpcy4kcmVmcy51c2VyTWludXRlc0NoYXJ0DQogICAgICBpZih1c2VyTWludXRlc0NoYXJ0ICYmIHRoaXMudXNlck1pbnV0ZXNPcHRpb25zKSB7DQogICAgICAgIGF3YWl0IHVzZXJNaW51dGVzQ2hhcnQuaW5pdCh0aGlzLnVzZXJNaW51dGVzT3B0aW9ucykNCiAgICAgIH0NCg0KICAgICAgY29uc3QgaG91cnNUeXBlQ2hhcnRzID0gdGhpcy4kcmVmcy5ob3Vyc1R5cGVDaGFydHMNCiAgICAgIGlmKGhvdXJzVHlwZUNoYXJ0cyAmJiB0aGlzLmhvdXJzVHlwZU9wdGlvbnMpIHsNCiAgICAgICAgYXdhaXQgaG91cnNUeXBlQ2hhcnRzLmluaXQodGhpcy5ob3Vyc1R5cGVPcHRpb25zKQ0KICAgICAgfQ0KDQogICAgICBjb25zdCBob3Vyc0NvbXBvc2VDaGFydHMgPSB0aGlzLiRyZWZzLmhvdXJzQ29tcG9zZUNoYXJ0cw0KICAgICAgaWYoaG91cnNDb21wb3NlQ2hhcnRzICYmIHRoaXMuaG91cnNDb21wb3NlT3B0aW9ucykgew0KICAgICAgICBhd2FpdCBob3Vyc0NvbXBvc2VDaGFydHMuaW5pdCh0aGlzLmhvdXJzQ29tcG9zZU9wdGlvbnMpDQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyByZWZyZXNoQXJlYUhvdXJzKGZhY3Rvcnksd29ya0RhdGUpIHsNCiAgICAgIGNvbnN0IG9sZEFycmF5ID0gdGhpcy5kYXlVc2VyTGlzdC5tYXAoaT0+IHsvL+S/neWtmOWOn+aVsOe7hOeahOWkh+azqA0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIHVzZXJDb2RlOiBpLnVzZXJDb2RlLA0KICAgICAgICAgIHNhaWxpbmdzOiBpLnNhaWxpbmdzLA0KICAgICAgICAgIGZpbmFsTWludXRlczogaS5maW5hbE1pbnV0ZXMsDQogICAgICAgICAgcmVtYXJrOiBpLnJlbWFyaywNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIHRoaXMuYnRuTG9hZGluZyA9IHRydWUNCiAgICAgIGNvbnN0IGRheVVzZXJMaXN0ID0gYXdhaXQgYWxsUGxhbkFyZWFVc2VySG91cnMoe2ZhY3Rvcnksd29ya0RhdGUsfSkNCiAgICAgIGZvciAoY29uc3QgaXRlbSBvZiBkYXlVc2VyTGlzdCkgew0KICAgICAgICBjb25zdCBhdHRlbmRhbmNlQXJyID0gdGhpcy5hdHRlbmRhbmNlTG9nTGlzdC5maWx0ZXIoaT0+IGkudXNlcklkID09PSBpdGVtLnVzZXJJZCkuc29ydCgoYSxiKT0+IGEudXNlckNoZWNrVGltZSAtIGIudXNlckNoZWNrVGltZSkNCiAgICAgICAgY29uc3QgdGltZXNBcnJheSA9IGF0dGVuZGFuY2VBcnIubWFwKGk9PiB0aGlzLm1vbWVudChpLnVzZXJDaGVja1RpbWUpLmZvcm1hdCgnWVlZWS1NTS1ERCBISDptbTpzcycpKQ0KICAgICAgICBjb25zdCB1cFRpbWUgPSBmaW5kQ2xvc2VzdFRpbWVTdHJpbmcodGltZXNBcnJheSxpdGVtLm1lc01pblRpbWUpDQogICAgICAgIGNvbnN0IGRvd25UaW1lID0gZmluZENsb3Nlc3RUaW1lU3RyaW5nKHRpbWVzQXJyYXksaXRlbS5tZXNNYXhUaW1lKQ0KICAgICAgICBjb25zdCBzdGFydFRpbWVBcnJheSA9IFsNCiAgICAgICAgICBpdGVtLnNhcE1pblRpbWUsDQogICAgICAgICAgaXRlbS5tZXNNaW5UaW1lLA0KICAgICAgICBdDQogICAgICAgIGNvbnN0IGVuZFRpbWVBcnJheSA9IFsNCiAgICAgICAgICBpdGVtLnNhcE1heFRpbWUsDQogICAgICAgICAgaXRlbS5tZXNNYXhUaW1lLA0KICAgICAgICBdDQogICAgICAgIGlmKHVwVGltZSAmJiBkb3duVGltZSkgew0KICAgICAgICAgIGNvbnN0IGF0dGVuZGFuY2VNaW51dGVzID0gdGhpcy5tb21lbnQoZG93blRpbWUpLmRpZmYodXBUaW1lLCdtaW51dGVzJykNCg0KICAgICAgICAgIGl0ZW0uYXR0ZW5kYW5jZVN0YXJ0VGltZSA9IHVwVGltZQ0KICAgICAgICAgIGl0ZW0uYXR0ZW5kYW5jZUVuZFRpbWUgPSBkb3duVGltZQ0KICAgICAgICAgIGl0ZW0uYXR0ZW5kYW5jZU1pbnV0ZXMgPSBhdHRlbmRhbmNlTWludXRlcw0KICAgICAgICAgIGl0ZW0uYXR0ZW5kYW5jZUFycmF5ID0gW3tzdGFydFRpbWU6IHVwVGltZSxlbmRUaW1lOiBkb3duVGltZX1dDQogICAgICAgICAgc3RhcnRUaW1lQXJyYXkucHVzaCh1cFRpbWUpDQogICAgICAgICAgZW5kVGltZUFycmF5LnB1c2goZG93blRpbWUpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgaXRlbS5hdHRlbmRhbmNlQXJyYXkgPSBbXQ0KICAgICAgICB9DQoNCiAgICAgICAgY29uc3QgbWluVGltZSA9IHN0YXJ0VGltZUFycmF5LnJlZHVjZSgobWluLCBjdXJyZW50KSA9PiB7DQogICAgICAgICAgcmV0dXJuIG5ldyBEYXRlKGN1cnJlbnQpIDwgbmV3IERhdGUobWluKSA/IGN1cnJlbnQgOiBtaW47DQogICAgICAgIH0pDQoNCiAgICAgICAgY29uc3QgbWF4VGltZSA9IGVuZFRpbWVBcnJheS5yZWR1Y2UoKG1heCwgY3VycmVudCkgPT4gew0KICAgICAgICAgIHJldHVybiBuZXcgRGF0ZShjdXJyZW50KSA+IG5ldyBEYXRlKG1heCkgPyBjdXJyZW50IDogbWF4Ow0KICAgICAgICB9KQ0KICAgICAgICBpdGVtLm1pblRpbWUgPSBtaW5UaW1lDQogICAgICAgIGl0ZW0ubWF4VGltZSA9IG1heFRpbWUNCg0KICAgICAgICBsZXQgc3RhcnRUaW1lID0gbWluVGltZQ0KICAgICAgICBsZXQgdGltZUFycmF5ID0gW10NCiAgICAgICAgd2hpbGUgKHN0YXJ0VGltZSA8PSBtYXhUaW1lKSB7DQogICAgICAgICAgdGltZUFycmF5LnB1c2goc3RhcnRUaW1lKQ0KICAgICAgICAgIHN0YXJ0VGltZSA9IHRoaXMubW9tZW50KHN0YXJ0VGltZSwnWVlZWS1NTS1ERCBISDptbTpzcycpLmFkZCgwLjI1LCdob3VycycpLmZvcm1hdCgnWVlZWS1NTS1ERCBISDptbTpzcycpDQogICAgICAgIH0NCiAgICAgICAgaXRlbS50aW1lQXJyYXkgPSB0aW1lQXJyYXkNCg0KICAgICAgICBpdGVtLm1lc0FycmF5ID0gdGhpcy5tZXNIb3Vyc0xpc3QuZmlsdGVyKGk9PmkudXNlckNvZGUgPT09IGl0ZW0udXNlckNvZGUpLnNvcnQoKGEsYik9PiBhLnN0YXJ0VGltZSAtIGIuc3RhcnRUaW1lKQ0KICAgICAgICAvLyBpZihpdGVtLnVzZXJDb2RlPT09J0hSMjMwMDE5MzgnKSB7DQogICAgICAgIC8vICAgY29uc29sZS5sb2coaXRlbSkNCiAgICAgICAgLy8gfQ0KICAgICAgICBmb3IgKGNvbnN0IG8gb2Ygb2xkQXJyYXkpIHsvL+WMuemFjeWOn+adpeeahOWkh+azqA0KICAgICAgICAgIGlmKG8udXNlckNvZGUgPT09IGl0ZW0udXNlckNvZGUgJiYgby5zYWlsaW5ncyA9PT0gaXRlbS5zYWlsaW5ncykgew0KICAgICAgICAgICAgaXRlbS5yZW1hcmsgPSBvLnJlbWFyaw0KICAgICAgICAgICAgaXRlbS5maW5hbE1pbnV0ZXMgPSBvLmZpbmFsTWludXRlcw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgLy/lvILluLjmj5DphpINCiAgICAgIGZvciAoY29uc3QgdSBvZiBkYXlVc2VyTGlzdCkgew0KICAgICAgICAvLyBpZih1LnVzZXJDb2RlID09PSAnSFIxODAwMDA5NScpIHsNCiAgICAgICAgLy8gICBjb25zb2xlLmxvZyh1LmF0dGVuZGFuY2VFbmRUaW1lLHUubWVzTWF4VGltZSkNCiAgICAgICAgLy8gICBjb25zb2xlLmxvZyh0aGlzLm1vbWVudCh1LmF0dGVuZGFuY2VFbmRUaW1lKS5kaWZmKHUubWVzTWF4VGltZSwnbWludXRlcycpKQ0KICAgICAgICAvLyB9DQogICAgICAgIGNvbnN0IGV4Y2VwdGlvbkFycmF5ID0gW10NCiAgICAgICAgaWYodS5tZXNNaW5UaW1lIDwgdS5hdHRlbmRhbmNlU3RhcnRUaW1lKSB7DQogICAgICAgICAgZXhjZXB0aW9uQXJyYXkucHVzaCgxKS8v5LiK5bel6ICD5Yuk5byC5bi4DQogICAgICAgIH0gZWxzZSBpZih0aGlzLm1vbWVudCh1Lm1lc01pblRpbWUpLmRpZmYodS5hdHRlbmRhbmNlU3RhcnRUaW1lLCdtaW51dGVzJykgPiAzMCkgew0KICAgICAgICAgIGV4Y2VwdGlvbkFycmF5LnB1c2goMykvL+S4iuW3peW8guW4uA0KICAgICAgICB9DQoNCiAgICAgICAgaWYodS5tZXNNYXhUaW1lID4gdS5hdHRlbmRhbmNlRW5kVGltZSkgew0KICAgICAgICAgIGV4Y2VwdGlvbkFycmF5LnB1c2goMikvL+S4i+W3peiAg+WLpOW8guW4uA0KICAgICAgICB9IGVsc2UgaWYodGhpcy5tb21lbnQodS5hdHRlbmRhbmNlRW5kVGltZSkuZGlmZih1Lm1lc01heFRpbWUsJ21pbnV0ZXMnKSA+IDE1KSB7DQogICAgICAgICAgZXhjZXB0aW9uQXJyYXkucHVzaCg0KS8v5LiL5bel5byC5bi4DQogICAgICAgIH0NCiAgICAgICAgaWYodGhpcy5tb21lbnQodS5zYXBNaW5UaW1lKS5kaWZmKHUubWVzTWluVGltZSwnbWludXRlcycpID4gMzApIHsNCiAgICAgICAgICBleGNlcHRpb25BcnJheS5wdXNoKDcpDQogICAgICAgIH0NCiAgICAgICAgaWYodGhpcy5tb21lbnQodS5tZXNNYXhUaW1lKS5kaWZmKHUuc2FwTWF4VGltZSwnbWludXRlcycpID4gMTUpIHsNCiAgICAgICAgICBleGNlcHRpb25BcnJheS5wdXNoKDgpDQogICAgICAgIH0NCiAgICAgICAgbGV0IGZsYWcgPSBmYWxzZQ0KICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHUuc2FwQXJyYXkubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgICBpZihpPjApIHsNCiAgICAgICAgICAgIGlmKHRoaXMubW9tZW50KHUuc2FwQXJyYXlbaV0uc3RhcnRUaW1lKS5kaWZmKHUuc2FwQXJyYXlbaS0xXS5lbmRUaW1lLCdtaW51dGVzJykgPiAzMCkgew0KICAgICAgICAgICAgICBmbGFnID0gdHJ1ZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBpZihmbGFnKSB7DQogICAgICAgICAgZXhjZXB0aW9uQXJyYXkucHVzaCg1KS8v6L2s5Zy65byC5bi4DQogICAgICAgIH0NCiAgICAgICAgaWYodS53YWdlc01pbnV0ZXMgJiYgdGhpcy5kaXZpZGUodS5lZmZlY3RpdmVNaW51dGVzLHUud2FnZXNNaW51dGVzKSA8IDAuOCkgew0KICAgICAgICAgIGV4Y2VwdGlvbkFycmF5LnB1c2goNikvL+acieaViOW3peaXtuW8guW4uA0KICAgICAgICB9DQoNCiAgICAgICAgdS5leGNlcHRpb25BcnJheSA9IGV4Y2VwdGlvbkFycmF5DQogICAgICB9DQoNCiAgICAgIHRoaXMuZGF5VXNlckxpc3QgPSBkYXlVc2VyTGlzdA0KDQogICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQ0KICAgIH0sDQogICAgYnVpbGRPcHRpb25zKHRpdGxlLGRhdGEpIHsNCiAgICAgIHJldHVybiAgIHsNCiAgICAgICAgdGl0bGU6IHsNCiAgICAgICAgICB0ZXh0OiB0aXRsZSwNCiAgICAgICAgICBsZWZ0OiAnY2VudGVyJw0KICAgICAgICB9LA0KICAgICAgICBsZWdlbmQ6IHsNCiAgICAgICAgICBvcmllbnQ6ICd2ZXJ0aWNhbCcsDQogICAgICAgICAgbGVmdDogJ2xlZnQnDQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHR5cGU6ICdwaWUnLA0KICAgICAgICAgICAgcmFkaXVzOiAnNTAlJywNCiAgICAgICAgICAgIGRhdGEsDQogICAgICAgICAgfQ0KICAgICAgICBdLA0KICAgICAgICBsYWJlbDogew0KICAgICAgICAgIHNob3c6IHRydWUsDQogICAgICAgICAgZm9ybWF0dGVyOiAne2J9OiB7Y30nIC8vIHtifeihqOekuuWQjeensO+8jHtjfeihqOekuuaVsOWAvA0KICAgICAgICB9LA0KICAgICAgfQ0KICAgIH0sDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy4kcGFyZW50LiRwYXJlbnQub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICBmYWN0b3J5OiBudWxsLA0KICAgICAgICB3b3JrRGF0ZTogbnVsbCwNCiAgICAgICAgc3VtTnVtczogbnVsbCwNCiAgICAgICAgdXNlck51bXM6IG51bGwsDQogICAgICAgIGxhYm9yTnVtczogbnVsbCwNCiAgICAgICAgb3V0ZXJOdW1zOiBudWxsLA0KICAgICAgICB1c2VyTWludXRlczogbnVsbCwNCiAgICAgICAgbGFib3JNaW51dGVzOiBudWxsLA0KICAgICAgICBvdXRlck1pbnV0ZXM6IG51bGwsDQogICAgICAgIHN1bU1pbnV0ZXM6IG51bGwsDQogICAgICAgIGVmZmVjdGl2ZU1pbnV0ZXM6IG51bGwsDQogICAgICAgIGludmFsaWRNaW51dGVzOiBudWxsLA0KICAgICAgICByZXN0TWludXRlczogbnVsbCwNCiAgICAgICAgb3RoZXJNaW51dGVzOiBudWxsLA0KICAgICAgICBtYW5hZ2VNaW51dGVzOiBudWxsLA0KICAgICAgICB3YWdlc01pbnV0ZXM6IG51bGwsDQogICAgICAgIG1lc01pbnV0ZXM6IG51bGwsDQogICAgICAgIG1lc051bXM6IG51bGwsDQogICAgICAgIHJlbWFyazogbnVsbA0KICAgICAgfTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgICB0aGlzLmRheVVzZXJMaXN0ID0gW10NCiAgICAgIHRoaXMud2VpZ2h0TWludXRlc0xpc3QgPSBbXQ0KICAgICAgdGhpcy5vdGhlck1pbnV0ZXNMaXN0ID0gW10NCiAgICAgIHRoaXMubWFuYWdlTWludXRlc0xpc3QgPSBbXQ0KICAgICAgdGhpcy5xY01pbnV0ZXNMaXN0ID0gW10NCiAgICAgIHRoaXMudGFiT3B0aW9ucyA9IFsNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAn55m954+tJywNCiAgICAgICAgICB2YWx1ZTogJzAnLA0KICAgICAgICAgIHN1bU1pbnV0ZXM6IDAsDQogICAgICAgICAgdXNlck1pbnV0ZXM6IDAsDQogICAgICAgICAgbGFib3JNaW51dGVzOiAwLA0KICAgICAgICAgIG91dGVyTWludXRlczogMCwNCiAgICAgICAgICB3ZWlnaHRNaW51dGVzOiAwLA0KICAgICAgICAgIG90aGVyTWludXRlczogMCwNCiAgICAgICAgICBtYW5hZ2VNaW51dGVzOiAwLA0KICAgICAgICAgIHFjTWludXRlczogMCwNCiAgICAgICAgICBzdW1OdW1zOiAwLA0KICAgICAgICAgIHVzZXJOdW1zOiAwLA0KICAgICAgICAgIGxhYm9yTnVtczogMCwNCiAgICAgICAgICBvdXRlck51bXM6IDAsDQogICAgICAgICAgd2VpZ2h0TnVtczogMCwNCiAgICAgICAgICBvdGhlck51bXM6IDAsDQogICAgICAgICAgbWFuYWdlTnVtczogMCwNCiAgICAgICAgICBxY051bXM6IDAsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogJ+aZmuePrScsDQogICAgICAgICAgdmFsdWU6ICcxJywNCiAgICAgICAgICBzdW1NaW51dGVzOiAwLA0KICAgICAgICAgIHVzZXJNaW51dGVzOiAwLA0KICAgICAgICAgIGxhYm9yTWludXRlczogMCwNCiAgICAgICAgICBvdXRlck1pbnV0ZXM6IDAsDQogICAgICAgICAgd2VpZ2h0TWludXRlczogMCwNCiAgICAgICAgICBvdGhlck1pbnV0ZXM6IDAsDQogICAgICAgICAgbWFuYWdlTWludXRlczogMCwNCiAgICAgICAgICBxY01pbnV0ZXM6IDAsDQogICAgICAgICAgc3VtTnVtczogMCwNCiAgICAgICAgICB1c2VyTnVtczogMCwNCiAgICAgICAgICBsYWJvck51bXM6IDAsDQogICAgICAgICAgb3V0ZXJOdW1zOiAwLA0KICAgICAgICAgIHdlaWdodE51bXM6IDAsDQogICAgICAgICAgb3RoZXJOdW1zOiAwLA0KICAgICAgICAgIG1hbmFnZU51bXM6IDAsDQogICAgICAgICAgcWNOdW1zOiAwLA0KICAgICAgICB9LA0KICAgICAgXQ0KICAgIH0sDQogICAgYXN5bmMgaW5pdChpZCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgZ2V0RGF5SG91cnMoaWQpDQogICAgICBjb25zdCBmb3JtID0gcmVzLmRhdGENCg0KICAgICAgdGhpcy5yZXN0TGlzdCA9IGF3YWl0IGFsbEF0dGVuZGFuY2VSZXN0VGltZSh7Y29tcGFueUNvZGU6IGZvcm0uZmFjdG9yeX0pDQogICAgICBjb25zdCB3b3JrRGF0ZSA9IGZvcm0ud29ya0RhdGUNCiAgICAgIGlmKHdvcmtEYXRlKSB7DQogICAgICAgIGNvbnN0IHNlYXJjaERhdGVBcnJheSA9IFt3b3JrRGF0ZSxdDQogICAgICAgIHNlYXJjaERhdGVBcnJheS5wdXNoKHRoaXMubW9tZW50KHdvcmtEYXRlKS5hZGQoMSwgJ2RheXMnKS5mb3JtYXQoJ1lZWVktTU0tREQnKSkNCiAgICAgICAgY29uc3QgYXR0ZW5kYW5jZUxvZ0xpc3QgPSBhd2FpdCBhbGxBdHRlbmRhbmNlTG9nKHtzZWFyY2hEYXRlQXJyYXl9KQ0KICAgICAgICBmb3IgKGNvbnN0IGwgb2YgYXR0ZW5kYW5jZUxvZ0xpc3QpIHsNCiAgICAgICAgICBsLnVzZXJJZCA9IE51bWJlcihsLnVzZXJJZCkNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmF0dGVuZGFuY2VMb2dMaXN0ID0gYXR0ZW5kYW5jZUxvZ0xpc3QNCiAgICAgIH0NCg0KICAgICAgY29uc3QgbWVzSG91cnNMaXN0ID0gYXdhaXQgYWxsTWVzSG91cnMoe3dvcmtkYXRlOiB3b3JrRGF0ZSx9KQ0KICAgICAgdGhpcy5tZXNIb3Vyc0xpc3QgPSBtZXNIb3Vyc0xpc3QNCg0KICAgICAgdGhpcy5mb3JtID0gZm9ybQ0KDQogICAgICBpZihmb3JtLnVzZXJNaW51dGVzTGlzdCAmJiBmb3JtLnVzZXJNaW51dGVzTGlzdC5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy5kYXlVc2VyTGlzdCA9IGZvcm0udXNlck1pbnV0ZXNMaXN0DQogICAgICB9DQogICAgICBhd2FpdCB0aGlzLnJlZnJlc2hBcmVhSG91cnMoZm9ybS5mYWN0b3J5LGZvcm0ud29ya0RhdGUpDQoNCiAgICAgIGlmKGZvcm0ud2VpZ2h0TWludXRlc0xpc3QpIHsNCiAgICAgICAgdGhpcy53ZWlnaHRNaW51dGVzTGlzdCA9IGZvcm0ud2VpZ2h0TWludXRlc0xpc3QNCiAgICAgIH0NCg0KICAgICAgaWYoZm9ybS5vdGhlck1pbnV0ZXNMaXN0KSB7DQogICAgICAgIHRoaXMub3RoZXJNaW51dGVzTGlzdCA9IGZvcm0ub3RoZXJNaW51dGVzTGlzdA0KICAgICAgfQ0KDQogICAgICBpZihmb3JtLm1hbmFnZU1pbnV0ZXNMaXN0KSB7DQogICAgICAgIHRoaXMubWFuYWdlTWludXRlc0xpc3QgPSBmb3JtLm1hbmFnZU1pbnV0ZXNMaXN0DQogICAgICB9DQoNCiAgICAgIGlmKGZvcm0ucWNNaW51dGVzTGlzdCkgew0KICAgICAgICB0aGlzLnFjTWludXRlc0xpc3QgPSBmb3JtLnFjTWludXRlc0xpc3QNCiAgICAgIH0NCg0KICAgICAgY29uc3QgcHJvZHVjdGlvbkdyb3VwID0gYXdhaXQgYWxsUHJvZHVjdGlvbkdyb3VwKHtmYWN0b3J5OiBmb3JtLmZhY3Rvcnl9KS8v6K+75Y+W6buY6K6k5ZCN5Y2VDQogICAgICBpZighdGhpcy53ZWlnaHRNaW51dGVzTGlzdC5sZW5ndGgpIHsNCiAgICAgICAgZm9yIChjb25zdCBpdGVtIG9mIHByb2R1Y3Rpb25Hcm91cCkgew0KICAgICAgICAgIGlmKGl0ZW0udHlwZSA9PT0gJ3dlaWdodCcpIHsNCiAgICAgICAgICAgIHRoaXMuYWRkT3RoZXJVc2VyKHRoaXMud2VpZ2h0TWludXRlc0xpc3QsaXRlbSkNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgYXdhaXQgdGhpcy5jb21wdXRlSXRlbURhdGEoJ3dlaWdodCcpDQogICAgICB9DQoNCiAgICAgIGlmKCF0aGlzLm1hbmFnZU1pbnV0ZXNMaXN0Lmxlbmd0aCkgew0KICAgICAgICBmb3IgKGNvbnN0IGl0ZW0gb2YgcHJvZHVjdGlvbkdyb3VwKSB7DQogICAgICAgICAgaWYoaXRlbS50eXBlID09PSAnbWFuYWdlJykgew0KICAgICAgICAgICAgdGhpcy5hZGRPdGhlclVzZXIodGhpcy5tYW5hZ2VNaW51dGVzTGlzdCxpdGVtKQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBhd2FpdCB0aGlzLmNvbXB1dGVJdGVtRGF0YSgnbWFuYWdlJykNCiAgICAgIH0NCg0KICAgICAgaWYoIXRoaXMub3RoZXJNaW51dGVzTGlzdC5sZW5ndGgpIHsNCiAgICAgICAgZm9yIChjb25zdCBpdGVtIG9mIHByb2R1Y3Rpb25Hcm91cCkgew0KICAgICAgICAgIGlmKGl0ZW0udHlwZSA9PT0gJ290aGVyJykgew0KICAgICAgICAgICAgdGhpcy5hZGRPdGhlclVzZXIodGhpcy5vdGhlck1pbnV0ZXNMaXN0LGl0ZW0pDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGF3YWl0IHRoaXMuY29tcHV0ZUl0ZW1EYXRhKCdvdGhlcicpDQogICAgICB9DQoNCiAgICAgIGlmKCF0aGlzLnFjTWludXRlc0xpc3QubGVuZ3RoKSB7DQogICAgICAgIGZvciAoY29uc3QgaXRlbSBvZiBwcm9kdWN0aW9uR3JvdXApIHsNCiAgICAgICAgICBpZihpdGVtLnR5cGUgPT09ICdxYycpIHsNCiAgICAgICAgICAgIHRoaXMuYWRkT3RoZXJVc2VyKHRoaXMucWNNaW51dGVzTGlzdCxpdGVtKQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBhd2FpdCB0aGlzLmNvbXB1dGVJdGVtRGF0YSgncWMnKQ0KICAgICAgfQ0KDQogICAgICBhd2FpdCB0aGlzLmNvbXB1dGVEYXlEYXRhKCkNCiAgICAgIGF3YWl0IHRoaXMuYnVpbGRDaGFydHMoKQ0KDQogICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgIH0sDQogICAgYXN5bmMgY29tcHV0ZUl0ZW1EYXRhKHR5cGUpIHsNCiAgICAgIGlmKHR5cGUpIHsNCiAgICAgICAgbGV0IHdhZ2VzTWludXRlcyA9IHRoaXMuJGJpZygwKQ0KICAgICAgICBmb3IgKGNvbnN0IGl0ZW0gb2YgdGhpc1t0eXBlICsgJ01pbnV0ZXNMaXN0J10pIHsNCiAgICAgICAgICB3YWdlc01pbnV0ZXMgPSB0aGlzLmFkZCh3YWdlc01pbnV0ZXMsaXRlbS53YWdlc01pbnV0ZXMpDQogICAgICAgIH0NCiAgICAgICAgdGhpcy5mb3JtW3R5cGUgKyAnTWludXRlcyddID0gd2FnZXNNaW51dGVzLnRvTnVtYmVyKCkNCiAgICAgICAgdGhpcy5mb3JtW3R5cGUgKyAnTnVtcyddID0gdGhpc1t0eXBlICsgJ01pbnV0ZXNMaXN0J10ubGVuZ3RoDQogICAgICB9DQoNCiAgICAgIGF3YWl0IHRoaXMuY29tcHV0ZURheURhdGEoKQ0KICAgICAgYXdhaXQgdGhpcy4kbmV4dFRpY2soKQ0KICAgICAgYXdhaXQgdGhpcy5idWlsZENoYXJ0cygpDQogICAgfSwNCiAgICBhc3luYyBjb21wdXRlRGF5RGF0YSgpIHsNCiAgICAgIGNvbnN0IHRhYk9wdGlvbnMgPSB0aGlzLnRhYk9wdGlvbnMNCiAgICAgIGNvbnN0IGRheVVzZXJMaXN0ID0gdGhpcy5kYXlVc2VyTGlzdA0KICAgICAgZm9yIChjb25zdCB0IG9mIHRhYk9wdGlvbnMpIHsNCiAgICAgICAgY29uc3QgZGF5VXNlclNhaWxpbmdzTGlzdCA9IGRheVVzZXJMaXN0LmZpbHRlcihpPT5pLnNhaWxpbmdzID09PSB0LnZhbHVlKQ0KICAgICAgICB0LnJlc3RNaW51dGVzPSB0aGlzLnN1bU9mQXJyYXkoZGF5VXNlclNhaWxpbmdzTGlzdC5tYXAoaT0+aS5yZXN0TWludXRlcykpDQogICAgICAgIHQuZWZmZWN0aXZlTWludXRlcz0gdGhpcy5zdW1PZkFycmF5KGRheVVzZXJTYWlsaW5nc0xpc3QubWFwKGk9PmkuZWZmZWN0aXZlTWludXRlcykpDQogICAgICAgIHQuaW52YWxpZE1pbnV0ZXM9IHRoaXMuc3VtT2ZBcnJheShkYXlVc2VyU2FpbGluZ3NMaXN0Lm1hcChpPT5pLmludmFsaWRNaW51dGVzKSkNCiAgICAgICAgdC53ZWlnaHRNaW51dGVzID0gdGhpcy5zdW1PZkFycmF5KHRoaXMud2VpZ2h0TWludXRlc0xpc3QuZmlsdGVyKGk9Pmkuc2FpbGluZ3MgPT09IHQudmFsdWUpLm1hcChpPT5pLmZpbmFsTWludXRlcyA/IGkuZmluYWxNaW51dGVzICogNjAgOiBpLndhZ2VzTWludXRlcykpDQogICAgICAgIHQub3RoZXJNaW51dGVzID0gdGhpcy5zdW1PZkFycmF5KHRoaXMub3RoZXJNaW51dGVzTGlzdC5maWx0ZXIoaT0+aS5zYWlsaW5ncyA9PT0gdC52YWx1ZSkubWFwKGk9PmkuZmluYWxNaW51dGVzID8gaS5maW5hbE1pbnV0ZXMgKiA2MCA6IGkud2FnZXNNaW51dGVzKSkNCiAgICAgICAgdC5tYW5hZ2VNaW51dGVzID0gdGhpcy5zdW1PZkFycmF5KHRoaXMubWFuYWdlTWludXRlc0xpc3QuZmlsdGVyKGk9Pmkuc2FpbGluZ3MgPT09IHQudmFsdWUpLm1hcChpPT5pLmZpbmFsTWludXRlcyA/IGkuZmluYWxNaW51dGVzICogNjAgOiBpLndhZ2VzTWludXRlcykpDQogICAgICAgIHQucWNNaW51dGVzID0gdGhpcy5zdW1PZkFycmF5KHRoaXMucWNNaW51dGVzTGlzdC5maWx0ZXIoaT0+aS5zYWlsaW5ncyA9PT0gdC52YWx1ZSkubWFwKGk9PmkuZmluYWxNaW51dGVzID8gaS5maW5hbE1pbnV0ZXMgKiA2MCA6IGkud2FnZXNNaW51dGVzKSkNCiAgICAgICAgY29uc3Qgd2FnZXNNaW51dGVzID0gdGhpcy5zdW1PZkFycmF5KGRheVVzZXJTYWlsaW5nc0xpc3QubWFwKGk9PmkuZmluYWxNaW51dGVzID8gaS5maW5hbE1pbnV0ZXMgKiA2MCA6IGkud2FnZXNNaW51dGVzKSkNCg0KICAgICAgICB0LnVzZXJXYWdlc01pbnV0ZXMgPSB3YWdlc01pbnV0ZXMNCiAgICAgICAgdC53YWdlc01pbnV0ZXMgPSB0aGlzLnN1bU9mQXJyYXkoW3dhZ2VzTWludXRlcyx0LndlaWdodE1pbnV0ZXMsdC5vdGhlck1pbnV0ZXMsdC5tYW5hZ2VNaW51dGVzXSkvL+W3pei1hOW3peaXtuWOu+mZpOi0qOajgA0KICAgICAgICB0Lm1lc01pbnV0ZXMgPSB0aGlzLnN1bU9mQXJyYXkoZGF5VXNlclNhaWxpbmdzTGlzdC5maWx0ZXIoaT0+aS5zYWlsaW5ncyA9PT0gdC52YWx1ZSkubWFwKGk9PmkubWVzTWludXRlcykpDQogICAgICAgIHQudXNlck1pbnV0ZXMgPSB0Lm1lc01pbnV0ZXMNCiAgICAgICAgdC5zdW1NaW51dGVzID0gdGhpcy5zdW1PZkFycmF5KFt0Lm1lc01pbnV0ZXMsdC53ZWlnaHRNaW51dGVzLHQub3RoZXJNaW51dGVzLHQubWFuYWdlTWludXRlc10pLy/mgLvlt6Xml7bljrvpmaTotKjmo4ANCg0KICAgICAgICB0LnVzZXJOdW1zID0gWy4uLm5ldyBTZXQoZGF5VXNlclNhaWxpbmdzTGlzdC5maWx0ZXIoaT0+aS51c2VyVHlwZSA9PT0gJ3VzZXInKS5tYXAoaT0+aS51c2VyQ29kZSkpXS5sZW5ndGgNCiAgICAgICAgdC5sYWJvck51bXMgPSBbLi4ubmV3IFNldChkYXlVc2VyU2FpbGluZ3NMaXN0LmZpbHRlcihpPT5pLnVzZXJUeXBlID09PSAnbGFib3InKS5tYXAoaT0+aS51c2VyQ29kZSkpXS5sZW5ndGgNCiAgICAgICAgdC5vdXRlck51bXMgPSBbLi4ubmV3IFNldChkYXlVc2VyU2FpbGluZ3NMaXN0LmZpbHRlcihpPT5pLnVzZXJUeXBlID09PSAnb3V0ZXInKS5tYXAoaT0+aS51c2VyQ29kZSkpXS5sZW5ndGgNCiAgICAgICAgY29uc3Qgc3VtTnVtcyA9IFsuLi5uZXcgU2V0KGRheVVzZXJTYWlsaW5nc0xpc3QubWFwKGk9PmkudXNlckNvZGUpKV0ubGVuZ3RoDQogICAgICAgIHQud2VpZ2h0TnVtcyA9IHRoaXMud2VpZ2h0TWludXRlc0xpc3QuZmlsdGVyKGk9Pmkuc2FpbGluZ3MgPT09IHQudmFsdWUpLmxlbmd0aA0KICAgICAgICB0Lm90aGVyTnVtcyA9IHRoaXMub3RoZXJNaW51dGVzTGlzdC5maWx0ZXIoaT0+aS5zYWlsaW5ncyA9PT0gdC52YWx1ZSkubGVuZ3RoDQogICAgICAgIHQubWFuYWdlTnVtcyA9IHRoaXMubWFuYWdlTWludXRlc0xpc3QuZmlsdGVyKGk9Pmkuc2FpbGluZ3MgPT09IHQudmFsdWUpLmxlbmd0aA0KICAgICAgICB0LnFjTnVtcyA9IHRoaXMucWNNaW51dGVzTGlzdC5maWx0ZXIoaT0+aS5zYWlsaW5ncyA9PT0gdC52YWx1ZSkubGVuZ3RoDQogICAgICAgIHQuc3VtTnVtcyA9IHRoaXMuc3VtT2ZBcnJheShbc3VtTnVtcyx0LndlaWdodE51bXMsdC5vdGhlck51bXMsdC5tYW5hZ2VOdW1zXSkvL+aAu+S6uuaVsOWOu+mZpOi0qOajgA0KICAgICAgfQ0KDQogICAgICBjb25zdCBmb3JtID0gdGhpcy5mb3JtDQogICAgICBmb3JtLndhZ2VzTWludXRlcyA9IHRoaXMuc3VtT2ZBcnJheSh0YWJPcHRpb25zLm1hcChpPT5pLndhZ2VzTWludXRlcykpDQogICAgICBmb3JtLnJlc3RNaW51dGVzID0gdGhpcy5zdW1PZkFycmF5KHRhYk9wdGlvbnMubWFwKGk9PmkucmVzdE1pbnV0ZXMpKQ0KICAgICAgZm9ybS5lZmZlY3RpdmVNaW51dGVzID0gdGhpcy5zdW1PZkFycmF5KHRhYk9wdGlvbnMubWFwKGk9PmkuZWZmZWN0aXZlTWludXRlcykpDQogICAgICBmb3JtLmludmFsaWRNaW51dGVzID0gdGhpcy5zdW1PZkFycmF5KHRhYk9wdGlvbnMubWFwKGk9PmkuaW52YWxpZE1pbnV0ZXMpKQ0KICAgICAgZm9ybS53ZWlnaHRNaW51dGVzID0gdGhpcy5zdW1PZkFycmF5KHRhYk9wdGlvbnMubWFwKGk9Pmkud2VpZ2h0TWludXRlcykpDQogICAgICBmb3JtLm90aGVyTWludXRlcyA9IHRoaXMuc3VtT2ZBcnJheSh0YWJPcHRpb25zLm1hcChpPT5pLm90aGVyTWludXRlcykpDQogICAgICBmb3JtLm1hbmFnZU1pbnV0ZXMgPSB0aGlzLnN1bU9mQXJyYXkodGFiT3B0aW9ucy5tYXAoaT0+aS5tYW5hZ2VNaW51dGVzKSkNCiAgICAgIGZvcm0ucWNNaW51dGVzID0gdGhpcy5zdW1PZkFycmF5KHRhYk9wdGlvbnMubWFwKGk9PmkucWNNaW51dGVzKSkNCiAgICAgIGZvcm0ubWVzTWludXRlcyA9IHRoaXMuc3VtT2ZBcnJheSh0YWJPcHRpb25zLm1hcChpPT5pLm1lc01pbnV0ZXMpKQ0KICAgICAgZm9ybS5zdW1NaW51dGVzID0gdGhpcy5zdW1PZkFycmF5KHRhYk9wdGlvbnMubWFwKGk9Pmkuc3VtTWludXRlcykpDQoNCiAgICAgIGZvcm0udXNlck51bXMgPSB0aGlzLnN1bU9mQXJyYXkodGFiT3B0aW9ucy5tYXAoaT0+aS51c2VyTnVtcykpDQogICAgICBmb3JtLmxhYm9yTnVtcyA9IHRoaXMuc3VtT2ZBcnJheSh0YWJPcHRpb25zLm1hcChpPT5pLmxhYm9yTnVtcykpDQogICAgICBmb3JtLm91dGVyTnVtcyA9IHRoaXMuc3VtT2ZBcnJheSh0YWJPcHRpb25zLm1hcChpPT5pLm91dGVyTnVtcykpDQogICAgICBmb3JtLnN1bU51bXMgPSB0aGlzLnN1bU9mQXJyYXkodGFiT3B0aW9ucy5tYXAoaT0+aS5zdW1OdW1zKSkNCiAgICAgIGZvcm0ud2VpZ2h0TnVtcyA9IHRoaXMuc3VtT2ZBcnJheSh0YWJPcHRpb25zLm1hcChpPT5pLndlaWdodE51bXMpKQ0KICAgICAgZm9ybS5vdGhlck51bXMgPSB0aGlzLnN1bU9mQXJyYXkodGFiT3B0aW9ucy5tYXAoaT0+aS5vdGhlck51bXMpKQ0KICAgICAgZm9ybS5tYW5hZ2VOdW1zID0gdGhpcy5zdW1PZkFycmF5KHRhYk9wdGlvbnMubWFwKGk9PmkubWFuYWdlTnVtcykpDQogICAgICBmb3JtLnFjTnVtcyA9IHRoaXMuc3VtT2ZBcnJheSh0YWJPcHRpb25zLm1hcChpPT5pLnFjTnVtcykpDQoNCiAgICB9LA0KICAgIHN1bU9mQXJyYXkoYXJyYXkpIHsNCiAgICAgIGxldCBudW1zID0gdGhpcy4kYmlnKDApDQogICAgICBmb3IgKGNvbnN0IG4gb2YgYXJyYXkpIHsNCiAgICAgICAgbnVtcyA9IHRoaXMuYWRkKG51bXMsbikNCiAgICAgIH0NCiAgICAgIHJldHVybiBudW1zLnRvTnVtYmVyKCkNCiAgICB9LA0KICAgIGFkZE90aGVyVXNlcihhcnJheSxyb3cpIHsNCiAgICAgIGNvbnN0IGF0dGVuZGFuY2VBcnIgPSB0aGlzLmF0dGVuZGFuY2VMb2dMaXN0LmZpbHRlcihpPT4gaS51c2VySWQgPT09IHJvdy51c2VySWQpDQogICAgICBpZihhdHRlbmRhbmNlQXJyICYmIGF0dGVuZGFuY2VBcnJbMF0pIHsNCiAgICAgICAgaWYoIWFycmF5Lm1hcChpPT5pLnVzZXJJZCkuaW5jbHVkZXMocm93LnVzZXJJZCkpIHsNCiAgICAgICAgICBjb25zdCBhdHRlbmRhbmNlQXJyID0gdGhpcy5hdHRlbmRhbmNlTG9nTGlzdC5maWx0ZXIoaT0+IGkudXNlcklkID09PSByb3cudXNlcklkKS5zb3J0KChhLGIpPT4gYS51c2VyQ2hlY2tUaW1lIC0gYi51c2VyQ2hlY2tUaW1lKQ0KICAgICAgICAgIGlmKGF0dGVuZGFuY2VBcnIgJiYgYXR0ZW5kYW5jZUFyclsxXSl7IC8v6Iez5bCR5pyJ5Lik5LiqDQogICAgICAgICAgICBjb25zdCB0aW1lc0FycmF5ID0gYXR0ZW5kYW5jZUFyci5tYXAoaT0+IHRoaXMubW9tZW50KGkudXNlckNoZWNrVGltZSkuZm9ybWF0KCdZWVlZLU1NLUREIEhIOm1tOnNzJykpDQogICAgICAgICAgICBjb25zdCBzdGFydERhdGUgPSB0aGlzLm1vbWVudCh0aGlzLmZvcm0ud29ya0RhdGUpLmZvcm1hdCgnWVlZWS1NTS1ERCcpDQogICAgICAgICAgICBsZXQgdXBTdGFuZFRpbWUgPSBzdGFydERhdGUgKyAnIDA4OjMwOjAwJw0KICAgICAgICAgICAgbGV0IGRvd25TdGFuZFRpbWUgPSAgc3RhcnREYXRlICsgJyAyMDozMDowMCcNCiAgICAgICAgICAgIGlmKHJvdy5zYWlsaW5ncyA9PT0gJzEnKSB7DQogICAgICAgICAgICAgIHVwU3RhbmRUaW1lID0gc3RhcnREYXRlICsgJyAyMDozMDowMCcNCiAgICAgICAgICAgICAgZG93blN0YW5kVGltZSA9IHRoaXMubW9tZW50KHN0YXJ0RGF0ZSkuYWRkKDEsICdkYXlzJykuZm9ybWF0KCdZWVlZLU1NLUREJykgKyAnIDA4OjMwOjAwJw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgbGV0IHVwVGltZSA9IGZpbmRDbG9zZXN0VGltZVN0cmluZyh0aW1lc0FycmF5LHVwU3RhbmRUaW1lKQ0KICAgICAgICAgICAgY29uc3QgZG93blRpbWUgPSBmaW5kQ2xvc2VzdFRpbWVTdHJpbmcodGltZXNBcnJheSxkb3duU3RhbmRUaW1lKQ0KICAgICAgICAgICAgaWYodXBUaW1lICYmIGRvd25UaW1lKSB7DQogICAgICAgICAgICAgIGlmKHVwVGltZSA8IHVwU3RhbmRUaW1lICkgey8v5aaC5p6c5pep5LqOOOeCueWNiizmjIk454K55Y2K566XDQogICAgICAgICAgICAgICAgdXBUaW1lID0gdXBTdGFuZFRpbWUNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBjb25zdCBtaW51dGVzID0gdGhpcy5tb21lbnQoZG93blRpbWUpLmRpZmYodXBUaW1lLCdtaW51dGVzJykNCiAgICAgICAgICAgICAgY29uc3Qgd29ya1BlcmlvZHMgPSBbDQogICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgc3RhcnQ6IHRoaXMubW9tZW50KHVwVGltZSkuZm9ybWF0KCdISDptbScpLA0KICAgICAgICAgICAgICAgICAgZW5kOiB0aGlzLm1vbWVudChkb3duVGltZSkuZm9ybWF0KCdISDptbScpLA0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgIF0NCiAgICAgICAgICAgICAgY29uc3QgcmVzdE1pbnV0ZXMgPSBjYWxjdWxhdGVJbnRlcnNlY3Rpb25NaW51dGVzKHdvcmtQZXJpb2RzLHRoaXMucmVzdExpc3QpDQogICAgICAgICAgICAgIGNvbnN0IHdhZ2VzTWludXRlcyA9IHRoaXMuc3VidHJhY3QobWludXRlcyxyZXN0TWludXRlcykudG9OdW1iZXIoKQ0KICAgICAgICAgICAgICBpZihtaW51dGVzID4gMCkgew0KICAgICAgICAgICAgICAgIGFycmF5LnB1c2goew0KICAgICAgICAgICAgICAgICAgdXNlcklkOiByb3cudXNlcklkLA0KICAgICAgICAgICAgICAgICAgdXNlckNvZGU6IHJvdy51c2VyQ29kZSwNCiAgICAgICAgICAgICAgICAgIG5pY2tOYW1lOiByb3cubmlja05hbWUsDQogICAgICAgICAgICAgICAgICBzdGFydFRpbWU6IHVwVGltZSwNCiAgICAgICAgICAgICAgICAgIGVuZFRpbWU6IGRvd25UaW1lLA0KICAgICAgICAgICAgICAgICAgc2FpbGluZ3M6IHJvdy5zYWlsaW5ncywNCiAgICAgICAgICAgICAgICAgIG1pbnV0ZXMsDQogICAgICAgICAgICAgICAgICByZXN0TWludXRlcywNCiAgICAgICAgICAgICAgICAgIHdhZ2VzTWludXRlczogcm93LnR5cGUgPT09ICdtYW5hZ2UnID8gNDgwIDogcm91bmREb3duVG9IYWxmSG91cih3YWdlc01pbnV0ZXMpLC8v5aaC5p6c5piv566h55CG5bel5pe26buY6K6k5pivOOWwj+aXtg0KICAgICAgICAgICAgICAgICAgZmluYWxNaW51dGVzOiB1bmRlZmluZWQsDQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgcHVzaEhyRGF0ZSgpIHsNCiAgICAgIGF3YWl0IHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSgpDQogICAgICBpZih0aGlzLmRheVVzZXJMaXN0Lmxlbmd0aCkgew0KICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlDQogICAgICAgIGxldCBmb3JtID0gT2JqZWN0LmFzc2lnbih7fSwgdGhpcy5mb3JtKQ0KICAgICAgICBsZXQgcGFyYW1zID0gew0KICAgICAgICAgIHdvcmtEYXRlOiBmb3JtLndvcmtEYXRlLA0KICAgICAgICAgIGZhY3Rvcnk6IGZvcm0uZmFjdG9yeSwNCiAgICAgICAgICB1c2VyTWludXRlc0xpc3Q6IHRoaXMuZGF5VXNlckxpc3QsDQogICAgICAgICAgb3RoZXJNaW51dGVzTGlzdDogWw0KICAgICAgICAgICAgLi4udGhpcy53ZWlnaHRNaW51dGVzTGlzdCwNCiAgICAgICAgICAgIC4uLnRoaXMub3RoZXJNaW51dGVzTGlzdCwNCiAgICAgICAgICAgIC4uLnRoaXMubWFuYWdlTWludXRlc0xpc3QsDQogICAgICAgICAgICAuLi50aGlzLnFjTWludXRlc0xpc3QsDQogICAgICAgICAgXSwNCiAgICAgICAgfQ0KICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBwdXNoSHJVc2VyQW5EYXRlKHBhcmFtcykNCiAgICAgICAgaWYocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygn5o6o6YCB5oiQ5YqfIScpDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlDQogICAgfSwNCiAgICBhc3luYyBzdWJtaXRGb3JtKCkgew0KICAgICAgbGV0IGZvcm0gPSBPYmplY3QuYXNzaWduKHt9LCB0aGlzLmZvcm0pDQoNCiAgICAgIGNvbnN0IGRheVVzZXJMaXN0ID0gdGhpcy5kYXlVc2VyTGlzdA0KICAgICAgZm9yIChjb25zdCBpdGVtIG9mIGRheVVzZXJMaXN0KSB7DQogICAgICAgIGl0ZW0uZXhjZXB0aW9uVGlwcyA9IHRoaXMuZXhjZXB0aW9uT3B0aW9ucy5maWx0ZXIoaT0+IGl0ZW0uZXhjZXB0aW9uQXJyYXkuaW5jbHVkZXMoaS52YWx1ZSkpLm1hcChpPT5pLmxhYmVsKS5qb2luKCd8JykNCiAgICAgIH0NCiAgICAgIGZvcm0udXNlck1pbnV0ZXNMaXN0ID0gZGF5VXNlckxpc3QNCg0KICAgICAgZm9ybS53ZWlnaHRNaW51dGVzTGlzdCA9IHRoaXMud2VpZ2h0TWludXRlc0xpc3QNCiAgICAgIGZvcm0ub3RoZXJNaW51dGVzTGlzdCA9IHRoaXMub3RoZXJNaW51dGVzTGlzdA0KICAgICAgZm9ybS5tYW5hZ2VNaW51dGVzTGlzdCA9IHRoaXMubWFuYWdlTWludXRlc0xpc3QNCiAgICAgIGZvcm0ucWNNaW51dGVzTGlzdCA9IHRoaXMucWNNaW51dGVzTGlzdA0KDQogICAgICBpZiAoZm9ybS5pZCAhPSBudWxsKSB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gdHJ1ZQ0KICAgICAgICAgIGF3YWl0IHVwZGF0ZURheUhvdXJzKGZvcm0pDQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpDQogICAgICAgICAgLy8gdGhpcy4kcGFyZW50LiRwYXJlbnQub3BlbiA9IGZhbHNlDQogICAgICAgICAgLy8gYXdhaXQgdGhpcy4kcGFyZW50LiRwYXJlbnQuZ2V0TGlzdCgpDQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgfSwNCn0NCg=="}, {"version": 3, "sources": ["save.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAu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file": "save.vue", "sourceRoot": "src/views/production/dayHours", "sourcesContent": ["<template>\r\n  <div v-loading=\"loading\" >\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"6\" >\r\n        <el-statistic\r\n          group-separator=\",\"\r\n          :precision=\"2\"\r\n          :value=\"form.sumNums\"\r\n          title=\"今日上工总人数\"\r\n        />\r\n      </el-col>\r\n      <el-col :span=\"6\" >\r\n        <el-statistic\r\n          group-separator=\",\"\r\n          :precision=\"2\"\r\n          :value=\"minutesToHours(form.sumMinutes)\"\r\n          title=\"今日上工总工时\"\r\n        />\r\n      </el-col>\r\n      <el-col :span=\"6\" >\r\n        <el-statistic\r\n          group-separator=\",\"\r\n          :precision=\"2\"\r\n          :value=\"minutesToHours(form.wagesMinutes)\"\r\n          title=\"今日工资总工时\"\r\n        >\r\n          <template #suffix >\r\n            <el-tooltip content=\"质检除外\" >\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </template>\r\n        </el-statistic>\r\n      </el-col>\r\n      <el-col :span=\"6\" >\r\n        <el-statistic\r\n          group-separator=\",\"\r\n          :precision=\"2\"\r\n          :value=\"minutesToHours(form.mesMinutes)\"\r\n          title=\"mes上工总工时\"\r\n        >\r\n          <template #suffix >\r\n            <el-tooltip content=\"质检除外\" >\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </template>\r\n        </el-statistic>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-row :gutter=\"20\" style=\"margin-top: 20px\" >\r\n      <el-col :span=\"6\" >\r\n        <div style=\"height: 420px;width: 420px;\">\r\n          <BaseChart ref=\"userNumsChart\" :styleObj=\"{height: '400px',width: '400px'}\" />\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"6\" >\r\n        <div style=\"height: 420px;width: 420px;\">\r\n          <BaseChart ref=\"userMinutesChart\" :styleObj=\"{height: '400px',width: '400px'}\" />\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"6\" >\r\n        <div style=\"height: 420px;width: 420px;\">\r\n          <BaseChart ref=\"hoursTypeCharts\" :styleObj=\"{height: '400px',width: '400px'}\" />\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"6\" >\r\n        <div style=\"height: 420px;width: 420px;\">\r\n          <BaseChart ref=\"hoursComposeCharts\" :styleObj=\"{height: '400px',width: '400px'}\" />\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-tooltip content=\"刷新产线工时\" >\r\n      <el-button :loading=\"btnLoading\" icon=\"el-icon-refresh\" size=\"mini\" type=\"text\" @click=\"refreshAreaHours(form.factory,form.workDate)\" />\r\n    </el-tooltip>\r\n\r\n    <el-tabs v-model=\"currentTab\" >\r\n      <el-tab-pane v-for=\"tab in tabOptions\" :key=\"tab.value\" :label=\"tab.label\" :name=\"tab.value\" >\r\n        <div class=\"table-wrapper\">\r\n          <table class=\"base-table small-table\" >\r\n            <tr >\r\n              <th style=\"width: 120px\" >维度</th>\r\n              <th style=\"width: 120px\" >总数</th>\r\n              <th style=\"width: 120px\" >正式工</th>\r\n              <th style=\"width: 120px\" >称量</th>\r\n              <th style=\"width: 120px\" >间接</th>\r\n              <th style=\"width: 120px\" >管理</th>\r\n              <th style=\"width: 120px\" >质检</th>\r\n              <th style=\"width: 120px\" >劳务</th>\r\n              <th style=\"width: 120px\" >包干</th>\r\n            </tr>\r\n            <tr>\r\n              <th>\r\n                工资工时\r\n                <el-tooltip content=\"有修正工时时,以修正工时为准(不包含质检)\" >\r\n                  <i class=\"el-icon-question\" />\r\n                </el-tooltip>\r\n              </th>\r\n              <td>{{minutesToHours(tab.wagesMinutes).toFixed(2)}}</td>\r\n              <td>{{minutesToHours(tab.userWagesMinutes).toFixed(2)}}</td>\r\n              <td>{{minutesToHours(tab.weightMinutes).toFixed(2)}}</td>\r\n              <td>{{minutesToHours(tab.otherMinutes).toFixed(2)}}</td>\r\n              <td>{{minutesToHours(tab.manageMinutes).toFixed(2)}}</td>\r\n              <td>{{minutesToHours(tab.qcMinutes).toFixed(2)}}</td>\r\n              <td>{{minutesToHours(tab.laborMinutes).toFixed(2)}}</td>\r\n              <td>{{minutesToHours(tab.outerMinutes).toFixed(2)}}</td>\r\n            </tr>\r\n            <tr>\r\n              <th>人数</th>\r\n              <td>{{tab.sumNums}}</td>\r\n              <td>{{tab.userNums}}</td>\r\n              <td>{{tab.weightNums}}</td>\r\n              <td>{{tab.otherNums}}</td>\r\n              <td>{{tab.manageNums}}</td>\r\n              <td>{{tab.qcNums}}</td>\r\n              <td>{{tab.laborNums}}</td>\r\n              <td>{{tab.outerNums}}</td>\r\n            </tr>\r\n          </table>\r\n        </div>\r\n        <DayHoursUserTabs\r\n          :day-hours=\"form\"\r\n          :attendance-log-list=\"attendanceLogList\"\r\n          :mes-hours-list=\"mesHoursList\"\r\n          :day-user-list=\"dayUserList.filter(i=>i.sailings === tab.value)\"\r\n          :weight-minutes-list=\"weightMinutesList\"\r\n          :other-minutes-list=\"otherMinutesList\"\r\n          :manage-minutes-list=\"manageMinutesList\"\r\n          :qc-minutes-list=\"qcMinutesList\"\r\n          :user-list=\"userList\"\r\n          :rest-list=\"restList\"\r\n          :sailings=\"tab.value\"\r\n          @computeItemData=\"computeItemData\"\r\n          @sailingsChange=\"sailingsChange\"\r\n        />\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" size=\"mini\" style=\"margin-top: 20px\" label-width=\"80px\">\r\n      <el-form-item label=\"备注\" prop=\"remark\">\r\n        <el-input v-model=\"form.remark\" type=\"textarea\"/>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div slot=\"footer\" class=\"dialog-footer\" v-has-permi=\"['mes:production:hours:push']\" >\r\n      <el-button type=\"primary\" @click=\"submitForm\" size=\"mini\" :loading=\"btnLoading\">确 定</el-button>\r\n      <el-button type=\"primary\" @click=\"pushHrDate\" size=\"mini\" :loading=\"btnLoading\">推送人事(班别)</el-button>\r\n      <el-button @click=\"cancel\" size=\"mini\">取 消</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script >\r\nimport {getDayHours, updateDayHours} from \"@/api/production/dayHours\";\r\nimport DayHoursUserTabs from \"@/views/production/dayHours/userTabs.vue\";\r\nimport {allPlanAreaUserHours, getDayAreaHours} from \"@/api/production/planAreaHours\";\r\nimport BaseChart from \"../../../../baseCharts.vue\";\r\nimport {userAll} from \"@/api/system/user\";\r\nimport {allAttendanceRestTime} from \"@/api/hr/attendanceRestTime\";\r\nimport {getOtherDayMinutesByParams, } from \"@/api/production/otherMinutes\";\r\nimport {allMesHours} from \"@/api/production/mesHours\";\r\nimport {allAttendanceLog} from \"@/api/hr/attendanceLog\";\r\nimport {allProductionGroup} from \"@/api/production/productionGroup\";\r\nimport {\r\n  calculateIntersectionMinutes,\r\n  findClosestTimeString,\r\n  findIntersection,\r\n  roundDownToHalfHour\r\n} from \"@/utils/production/time\";\r\nimport form from \"@/views/gx/form/index.vue\";\r\nimport {pushHrUserAnDate} from \"@/api/hr/attendance\";\r\n\r\nexport default {\r\n  name: 'dayHoursSave',\r\n  components: {BaseChart, DayHoursUserTabs},\r\n  props: {\r\n    readonly: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      btnLoading: false,\r\n      form: {},\r\n      rules: {},\r\n      tabOptions: [\r\n        {\r\n          label: '白班',\r\n          value: '0',\r\n          sumMinutes: 0,\r\n          userMinutes: 0,\r\n          laborMinutes: 0,\r\n          outerMinutes: 0,\r\n          weightMinutes: 0,\r\n          otherMinutes: 0,\r\n          manageMinutes: 0,\r\n          qcMinutes: 0,\r\n          sumNums: 0,\r\n          userNums: 0,\r\n          laborNums: 0,\r\n          outerNums: 0,\r\n          weightNums: 0,\r\n          otherNums: 0,\r\n          manageNums: 0,\r\n          qcNums: 0,\r\n        },\r\n        {\r\n          label: '晚班',\r\n          value: '1',\r\n          sumMinutes: 0,\r\n          userMinutes: 0,\r\n          laborMinutes: 0,\r\n          outerMinutes: 0,\r\n          weightMinutes: 0,\r\n          otherMinutes: 0,\r\n          manageMinutes: 0,\r\n          qcMinutes: 0,\r\n          sumNums: 0,\r\n          userNums: 0,\r\n          laborNums: 0,\r\n          outerNums: 0,\r\n          weightNums: 0,\r\n          otherNums: 0,\r\n          manageNums: 0,\r\n          qcNums: 0,\r\n        },\r\n      ],\r\n      currentTab: '0',\r\n      userNumsOptions: {},\r\n      userMinutesOptions: {},\r\n      hoursTypeOptions: {},\r\n      hoursComposeOptions: {},\r\n      dayUserList: [],\r\n      attendanceLogList: [],\r\n      userList: [],\r\n      restList: [],\r\n      weightMinutesList: [],\r\n      otherMinutesList: [],\r\n      manageMinutesList: [],\r\n      qcMinutesList: [],\r\n      mesHoursList: [],\r\n      exceptionOptions: [\r\n        {label: '上工考勤异常',value: 1},\r\n        {label: '下工考勤异常',value: 2},\r\n        {label: 'mes上工异常',value: 3},\r\n        {label: 'mes下工异常',value: 4},\r\n        {label: '转场异常',value: 5},\r\n        {label: '有效工时异常',value: 6},\r\n        {label: 'sap上工异常',value: 7},\r\n        {label: 'sap下工异常',value: 8},\r\n      ],\r\n    }\r\n  },\r\n  async created() {\r\n    this.userList = await userAll()\r\n  },\r\n  methods: {\r\n    async sailingsChange(userCode){//mes班别变更,刷新mes工时\r\n      await this.refreshAreaHours(this.form.factory,this.form.workDate)\r\n    },\r\n    async buildCharts() {\r\n      const form = this.form\r\n\r\n      this.userNumsOptions = this.buildOptions(\"上工人员分布\",[\r\n        {name: '正式工',value: form.userNums},\r\n        {name: '称量',value: form.weightNums},\r\n        {name: '间接',value: form.otherNums},\r\n        {name: '管理',value: form.manageNums},\r\n        {name: '质检',value: form.qcNums},\r\n        {name: '劳务工',value: form.laborNums},\r\n        {name: '外包工',value: form.outerNums},\r\n      ])\r\n      this.userMinutesOptions = this.buildOptions(\"产线上工工时分布\",[\r\n        {name: '正式工',value: this.minutesToHours(form.userMinutes).toFixed(2) },\r\n        {name: '劳务工',value: this.minutesToHours(form.laborMinutes).toFixed(2) },\r\n        {name: '外包工',value: this.minutesToHours(form.outerMinutes).toFixed(2) },\r\n      ])\r\n      this.hoursTypeOptions = this.buildOptions(\"产线工时性质分布\",[\r\n        {name: '有效',value: this.minutesToHours(form.effectiveMinutes).toFixed(2)},\r\n        {name: '无效',value: this.minutesToHours(form.invalidMinutes).toFixed(2)},\r\n        {name: '休息',value: this.minutesToHours(form.restMinutes).toFixed(2)},\r\n      ])\r\n      this.hoursComposeOptions = this.buildOptions(\"工时组成分布\",[\r\n        {name: '产线',value: this.minutesToHours(form.sumMinutes).toFixed(2)},\r\n        {name: '称量',value: this.minutesToHours(form.weightMinutes).toFixed(2)},\r\n        {name: '间接',value: this.minutesToHours(form.otherMinutes).toFixed(2)},\r\n        {name: '管理',value: this.minutesToHours(form.manageMinutes).toFixed(2)},\r\n        // {name: '质检',value: this.minutesToHours(form.qcMinutes).toFixed(2)},\r\n      ])\r\n\r\n      await this.$nextTick()\r\n      const userNumsChart = this.$refs.userNumsChart\r\n      if(userNumsChart && this.userNumsOptions) {\r\n        await userNumsChart.init(this.userNumsOptions)\r\n      }\r\n\r\n      const userMinutesChart = this.$refs.userMinutesChart\r\n      if(userMinutesChart && this.userMinutesOptions) {\r\n        await userMinutesChart.init(this.userMinutesOptions)\r\n      }\r\n\r\n      const hoursTypeCharts = this.$refs.hoursTypeCharts\r\n      if(hoursTypeCharts && this.hoursTypeOptions) {\r\n        await hoursTypeCharts.init(this.hoursTypeOptions)\r\n      }\r\n\r\n      const hoursComposeCharts = this.$refs.hoursComposeCharts\r\n      if(hoursComposeCharts && this.hoursComposeOptions) {\r\n        await hoursComposeCharts.init(this.hoursComposeOptions)\r\n      }\r\n    },\r\n    async refreshAreaHours(factory,workDate) {\r\n      const oldArray = this.dayUserList.map(i=> {//保存原数组的备注\r\n        return {\r\n          userCode: i.userCode,\r\n          sailings: i.sailings,\r\n          finalMinutes: i.finalMinutes,\r\n          remark: i.remark,\r\n        }\r\n      })\r\n      this.btnLoading = true\r\n      const dayUserList = await allPlanAreaUserHours({factory,workDate,})\r\n      for (const item of dayUserList) {\r\n        const attendanceArr = this.attendanceLogList.filter(i=> i.userId === item.userId).sort((a,b)=> a.userCheckTime - b.userCheckTime)\r\n        const timesArray = attendanceArr.map(i=> this.moment(i.userCheckTime).format('YYYY-MM-DD HH:mm:ss'))\r\n        const upTime = findClosestTimeString(timesArray,item.mesMinTime)\r\n        const downTime = findClosestTimeString(timesArray,item.mesMaxTime)\r\n        const startTimeArray = [\r\n          item.sapMinTime,\r\n          item.mesMinTime,\r\n        ]\r\n        const endTimeArray = [\r\n          item.sapMaxTime,\r\n          item.mesMaxTime,\r\n        ]\r\n        if(upTime && downTime) {\r\n          const attendanceMinutes = this.moment(downTime).diff(upTime,'minutes')\r\n\r\n          item.attendanceStartTime = upTime\r\n          item.attendanceEndTime = downTime\r\n          item.attendanceMinutes = attendanceMinutes\r\n          item.attendanceArray = [{startTime: upTime,endTime: downTime}]\r\n          startTimeArray.push(upTime)\r\n          endTimeArray.push(downTime)\r\n        } else {\r\n          item.attendanceArray = []\r\n        }\r\n\r\n        const minTime = startTimeArray.reduce((min, current) => {\r\n          return new Date(current) < new Date(min) ? current : min;\r\n        })\r\n\r\n        const maxTime = endTimeArray.reduce((max, current) => {\r\n          return new Date(current) > new Date(max) ? current : max;\r\n        })\r\n        item.minTime = minTime\r\n        item.maxTime = maxTime\r\n\r\n        let startTime = minTime\r\n        let timeArray = []\r\n        while (startTime <= maxTime) {\r\n          timeArray.push(startTime)\r\n          startTime = this.moment(startTime,'YYYY-MM-DD HH:mm:ss').add(0.25,'hours').format('YYYY-MM-DD HH:mm:ss')\r\n        }\r\n        item.timeArray = timeArray\r\n\r\n        item.mesArray = this.mesHoursList.filter(i=>i.userCode === item.userCode).sort((a,b)=> a.startTime - b.startTime)\r\n        // if(item.userCode==='HR23001938') {\r\n        //   console.log(item)\r\n        // }\r\n        for (const o of oldArray) {//匹配原来的备注\r\n          if(o.userCode === item.userCode && o.sailings === item.sailings) {\r\n            item.remark = o.remark\r\n            item.finalMinutes = o.finalMinutes\r\n          }\r\n        }\r\n      }\r\n      //异常提醒\r\n      for (const u of dayUserList) {\r\n        // if(u.userCode === 'HR18000095') {\r\n        //   console.log(u.attendanceEndTime,u.mesMaxTime)\r\n        //   console.log(this.moment(u.attendanceEndTime).diff(u.mesMaxTime,'minutes'))\r\n        // }\r\n        const exceptionArray = []\r\n        if(u.mesMinTime < u.attendanceStartTime) {\r\n          exceptionArray.push(1)//上工考勤异常\r\n        } else if(this.moment(u.mesMinTime).diff(u.attendanceStartTime,'minutes') > 30) {\r\n          exceptionArray.push(3)//上工异常\r\n        }\r\n\r\n        if(u.mesMaxTime > u.attendanceEndTime) {\r\n          exceptionArray.push(2)//下工考勤异常\r\n        } else if(this.moment(u.attendanceEndTime).diff(u.mesMaxTime,'minutes') > 15) {\r\n          exceptionArray.push(4)//下工异常\r\n        }\r\n        if(this.moment(u.sapMinTime).diff(u.mesMinTime,'minutes') > 30) {\r\n          exceptionArray.push(7)\r\n        }\r\n        if(this.moment(u.mesMaxTime).diff(u.sapMaxTime,'minutes') > 15) {\r\n          exceptionArray.push(8)\r\n        }\r\n        let flag = false\r\n        for (let i = 0; i < u.sapArray.length; i++) {\r\n          if(i>0) {\r\n            if(this.moment(u.sapArray[i].startTime).diff(u.sapArray[i-1].endTime,'minutes') > 30) {\r\n              flag = true\r\n            }\r\n          }\r\n        }\r\n        if(flag) {\r\n          exceptionArray.push(5)//转场异常\r\n        }\r\n        if(u.wagesMinutes && this.divide(u.effectiveMinutes,u.wagesMinutes) < 0.8) {\r\n          exceptionArray.push(6)//有效工时异常\r\n        }\r\n\r\n        u.exceptionArray = exceptionArray\r\n      }\r\n\r\n      this.dayUserList = dayUserList\r\n\r\n      this.btnLoading = false\r\n    },\r\n    buildOptions(title,data) {\r\n      return   {\r\n        title: {\r\n          text: title,\r\n          left: 'center'\r\n        },\r\n        legend: {\r\n          orient: 'vertical',\r\n          left: 'left'\r\n        },\r\n        series: [\r\n          {\r\n            type: 'pie',\r\n            radius: '50%',\r\n            data,\r\n          }\r\n        ],\r\n        label: {\r\n          show: true,\r\n          formatter: '{b}: {c}' // {b}表示名称，{c}表示数值\r\n        },\r\n      }\r\n    },\r\n    cancel() {\r\n      this.$parent.$parent.open = false;\r\n      this.reset();\r\n    },\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        factory: null,\r\n        workDate: null,\r\n        sumNums: null,\r\n        userNums: null,\r\n        laborNums: null,\r\n        outerNums: null,\r\n        userMinutes: null,\r\n        laborMinutes: null,\r\n        outerMinutes: null,\r\n        sumMinutes: null,\r\n        effectiveMinutes: null,\r\n        invalidMinutes: null,\r\n        restMinutes: null,\r\n        otherMinutes: null,\r\n        manageMinutes: null,\r\n        wagesMinutes: null,\r\n        mesMinutes: null,\r\n        mesNums: null,\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n      this.dayUserList = []\r\n      this.weightMinutesList = []\r\n      this.otherMinutesList = []\r\n      this.manageMinutesList = []\r\n      this.qcMinutesList = []\r\n      this.tabOptions = [\r\n        {\r\n          label: '白班',\r\n          value: '0',\r\n          sumMinutes: 0,\r\n          userMinutes: 0,\r\n          laborMinutes: 0,\r\n          outerMinutes: 0,\r\n          weightMinutes: 0,\r\n          otherMinutes: 0,\r\n          manageMinutes: 0,\r\n          qcMinutes: 0,\r\n          sumNums: 0,\r\n          userNums: 0,\r\n          laborNums: 0,\r\n          outerNums: 0,\r\n          weightNums: 0,\r\n          otherNums: 0,\r\n          manageNums: 0,\r\n          qcNums: 0,\r\n        },\r\n        {\r\n          label: '晚班',\r\n          value: '1',\r\n          sumMinutes: 0,\r\n          userMinutes: 0,\r\n          laborMinutes: 0,\r\n          outerMinutes: 0,\r\n          weightMinutes: 0,\r\n          otherMinutes: 0,\r\n          manageMinutes: 0,\r\n          qcMinutes: 0,\r\n          sumNums: 0,\r\n          userNums: 0,\r\n          laborNums: 0,\r\n          outerNums: 0,\r\n          weightNums: 0,\r\n          otherNums: 0,\r\n          manageNums: 0,\r\n          qcNums: 0,\r\n        },\r\n      ]\r\n    },\r\n    async init(id) {\r\n      this.loading = true\r\n      const res = await getDayHours(id)\r\n      const form = res.data\r\n\r\n      this.restList = await allAttendanceRestTime({companyCode: form.factory})\r\n      const workDate = form.workDate\r\n      if(workDate) {\r\n        const searchDateArray = [workDate,]\r\n        searchDateArray.push(this.moment(workDate).add(1, 'days').format('YYYY-MM-DD'))\r\n        const attendanceLogList = await allAttendanceLog({searchDateArray})\r\n        for (const l of attendanceLogList) {\r\n          l.userId = Number(l.userId)\r\n        }\r\n        this.attendanceLogList = attendanceLogList\r\n      }\r\n\r\n      const mesHoursList = await allMesHours({workdate: workDate,})\r\n      this.mesHoursList = mesHoursList\r\n\r\n      this.form = form\r\n\r\n      if(form.userMinutesList && form.userMinutesList.length) {\r\n        this.dayUserList = form.userMinutesList\r\n      }\r\n      await this.refreshAreaHours(form.factory,form.workDate)\r\n\r\n      if(form.weightMinutesList) {\r\n        this.weightMinutesList = form.weightMinutesList\r\n      }\r\n\r\n      if(form.otherMinutesList) {\r\n        this.otherMinutesList = form.otherMinutesList\r\n      }\r\n\r\n      if(form.manageMinutesList) {\r\n        this.manageMinutesList = form.manageMinutesList\r\n      }\r\n\r\n      if(form.qcMinutesList) {\r\n        this.qcMinutesList = form.qcMinutesList\r\n      }\r\n\r\n      const productionGroup = await allProductionGroup({factory: form.factory})//读取默认名单\r\n      if(!this.weightMinutesList.length) {\r\n        for (const item of productionGroup) {\r\n          if(item.type === 'weight') {\r\n            this.addOtherUser(this.weightMinutesList,item)\r\n          }\r\n        }\r\n        await this.computeItemData('weight')\r\n      }\r\n\r\n      if(!this.manageMinutesList.length) {\r\n        for (const item of productionGroup) {\r\n          if(item.type === 'manage') {\r\n            this.addOtherUser(this.manageMinutesList,item)\r\n          }\r\n        }\r\n        await this.computeItemData('manage')\r\n      }\r\n\r\n      if(!this.otherMinutesList.length) {\r\n        for (const item of productionGroup) {\r\n          if(item.type === 'other') {\r\n            this.addOtherUser(this.otherMinutesList,item)\r\n          }\r\n        }\r\n        await this.computeItemData('other')\r\n      }\r\n\r\n      if(!this.qcMinutesList.length) {\r\n        for (const item of productionGroup) {\r\n          if(item.type === 'qc') {\r\n            this.addOtherUser(this.qcMinutesList,item)\r\n          }\r\n        }\r\n        await this.computeItemData('qc')\r\n      }\r\n\r\n      await this.computeDayData()\r\n      await this.buildCharts()\r\n\r\n      this.loading = false\r\n    },\r\n    async computeItemData(type) {\r\n      if(type) {\r\n        let wagesMinutes = this.$big(0)\r\n        for (const item of this[type + 'MinutesList']) {\r\n          wagesMinutes = this.add(wagesMinutes,item.wagesMinutes)\r\n        }\r\n        this.form[type + 'Minutes'] = wagesMinutes.toNumber()\r\n        this.form[type + 'Nums'] = this[type + 'MinutesList'].length\r\n      }\r\n\r\n      await this.computeDayData()\r\n      await this.$nextTick()\r\n      await this.buildCharts()\r\n    },\r\n    async computeDayData() {\r\n      const tabOptions = this.tabOptions\r\n      const dayUserList = this.dayUserList\r\n      for (const t of tabOptions) {\r\n        const dayUserSailingsList = dayUserList.filter(i=>i.sailings === t.value)\r\n        t.restMinutes= this.sumOfArray(dayUserSailingsList.map(i=>i.restMinutes))\r\n        t.effectiveMinutes= this.sumOfArray(dayUserSailingsList.map(i=>i.effectiveMinutes))\r\n        t.invalidMinutes= this.sumOfArray(dayUserSailingsList.map(i=>i.invalidMinutes))\r\n        t.weightMinutes = this.sumOfArray(this.weightMinutesList.filter(i=>i.sailings === t.value).map(i=>i.finalMinutes ? i.finalMinutes * 60 : i.wagesMinutes))\r\n        t.otherMinutes = this.sumOfArray(this.otherMinutesList.filter(i=>i.sailings === t.value).map(i=>i.finalMinutes ? i.finalMinutes * 60 : i.wagesMinutes))\r\n        t.manageMinutes = this.sumOfArray(this.manageMinutesList.filter(i=>i.sailings === t.value).map(i=>i.finalMinutes ? i.finalMinutes * 60 : i.wagesMinutes))\r\n        t.qcMinutes = this.sumOfArray(this.qcMinutesList.filter(i=>i.sailings === t.value).map(i=>i.finalMinutes ? i.finalMinutes * 60 : i.wagesMinutes))\r\n        const wagesMinutes = this.sumOfArray(dayUserSailingsList.map(i=>i.finalMinutes ? i.finalMinutes * 60 : i.wagesMinutes))\r\n\r\n        t.userWagesMinutes = wagesMinutes\r\n        t.wagesMinutes = this.sumOfArray([wagesMinutes,t.weightMinutes,t.otherMinutes,t.manageMinutes])//工资工时去除质检\r\n        t.mesMinutes = this.sumOfArray(dayUserSailingsList.filter(i=>i.sailings === t.value).map(i=>i.mesMinutes))\r\n        t.userMinutes = t.mesMinutes\r\n        t.sumMinutes = this.sumOfArray([t.mesMinutes,t.weightMinutes,t.otherMinutes,t.manageMinutes])//总工时去除质检\r\n\r\n        t.userNums = [...new Set(dayUserSailingsList.filter(i=>i.userType === 'user').map(i=>i.userCode))].length\r\n        t.laborNums = [...new Set(dayUserSailingsList.filter(i=>i.userType === 'labor').map(i=>i.userCode))].length\r\n        t.outerNums = [...new Set(dayUserSailingsList.filter(i=>i.userType === 'outer').map(i=>i.userCode))].length\r\n        const sumNums = [...new Set(dayUserSailingsList.map(i=>i.userCode))].length\r\n        t.weightNums = this.weightMinutesList.filter(i=>i.sailings === t.value).length\r\n        t.otherNums = this.otherMinutesList.filter(i=>i.sailings === t.value).length\r\n        t.manageNums = this.manageMinutesList.filter(i=>i.sailings === t.value).length\r\n        t.qcNums = this.qcMinutesList.filter(i=>i.sailings === t.value).length\r\n        t.sumNums = this.sumOfArray([sumNums,t.weightNums,t.otherNums,t.manageNums])//总人数去除质检\r\n      }\r\n\r\n      const form = this.form\r\n      form.wagesMinutes = this.sumOfArray(tabOptions.map(i=>i.wagesMinutes))\r\n      form.restMinutes = this.sumOfArray(tabOptions.map(i=>i.restMinutes))\r\n      form.effectiveMinutes = this.sumOfArray(tabOptions.map(i=>i.effectiveMinutes))\r\n      form.invalidMinutes = this.sumOfArray(tabOptions.map(i=>i.invalidMinutes))\r\n      form.weightMinutes = this.sumOfArray(tabOptions.map(i=>i.weightMinutes))\r\n      form.otherMinutes = this.sumOfArray(tabOptions.map(i=>i.otherMinutes))\r\n      form.manageMinutes = this.sumOfArray(tabOptions.map(i=>i.manageMinutes))\r\n      form.qcMinutes = this.sumOfArray(tabOptions.map(i=>i.qcMinutes))\r\n      form.mesMinutes = this.sumOfArray(tabOptions.map(i=>i.mesMinutes))\r\n      form.sumMinutes = this.sumOfArray(tabOptions.map(i=>i.sumMinutes))\r\n\r\n      form.userNums = this.sumOfArray(tabOptions.map(i=>i.userNums))\r\n      form.laborNums = this.sumOfArray(tabOptions.map(i=>i.laborNums))\r\n      form.outerNums = this.sumOfArray(tabOptions.map(i=>i.outerNums))\r\n      form.sumNums = this.sumOfArray(tabOptions.map(i=>i.sumNums))\r\n      form.weightNums = this.sumOfArray(tabOptions.map(i=>i.weightNums))\r\n      form.otherNums = this.sumOfArray(tabOptions.map(i=>i.otherNums))\r\n      form.manageNums = this.sumOfArray(tabOptions.map(i=>i.manageNums))\r\n      form.qcNums = this.sumOfArray(tabOptions.map(i=>i.qcNums))\r\n\r\n    },\r\n    sumOfArray(array) {\r\n      let nums = this.$big(0)\r\n      for (const n of array) {\r\n        nums = this.add(nums,n)\r\n      }\r\n      return nums.toNumber()\r\n    },\r\n    addOtherUser(array,row) {\r\n      const attendanceArr = this.attendanceLogList.filter(i=> i.userId === row.userId)\r\n      if(attendanceArr && attendanceArr[0]) {\r\n        if(!array.map(i=>i.userId).includes(row.userId)) {\r\n          const attendanceArr = this.attendanceLogList.filter(i=> i.userId === row.userId).sort((a,b)=> a.userCheckTime - b.userCheckTime)\r\n          if(attendanceArr && attendanceArr[1]){ //至少有两个\r\n            const timesArray = attendanceArr.map(i=> this.moment(i.userCheckTime).format('YYYY-MM-DD HH:mm:ss'))\r\n            const startDate = this.moment(this.form.workDate).format('YYYY-MM-DD')\r\n            let upStandTime = startDate + ' 08:30:00'\r\n            let downStandTime =  startDate + ' 20:30:00'\r\n            if(row.sailings === '1') {\r\n              upStandTime = startDate + ' 20:30:00'\r\n              downStandTime = this.moment(startDate).add(1, 'days').format('YYYY-MM-DD') + ' 08:30:00'\r\n            }\r\n            let upTime = findClosestTimeString(timesArray,upStandTime)\r\n            const downTime = findClosestTimeString(timesArray,downStandTime)\r\n            if(upTime && downTime) {\r\n              if(upTime < upStandTime ) {//如果早于8点半,按8点半算\r\n                upTime = upStandTime\r\n              }\r\n              const minutes = this.moment(downTime).diff(upTime,'minutes')\r\n              const workPeriods = [\r\n                {\r\n                  start: this.moment(upTime).format('HH:mm'),\r\n                  end: this.moment(downTime).format('HH:mm'),\r\n                },\r\n              ]\r\n              const restMinutes = calculateIntersectionMinutes(workPeriods,this.restList)\r\n              const wagesMinutes = this.subtract(minutes,restMinutes).toNumber()\r\n              if(minutes > 0) {\r\n                array.push({\r\n                  userId: row.userId,\r\n                  userCode: row.userCode,\r\n                  nickName: row.nickName,\r\n                  startTime: upTime,\r\n                  endTime: downTime,\r\n                  sailings: row.sailings,\r\n                  minutes,\r\n                  restMinutes,\r\n                  wagesMinutes: row.type === 'manage' ? 480 : roundDownToHalfHour(wagesMinutes),//如果是管理工时默认是8小时\r\n                  finalMinutes: undefined,\r\n                })\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    async pushHrDate() {\r\n      await this.$refs[\"form\"].validate()\r\n      if(this.dayUserList.length) {\r\n        this.btnLoading = true\r\n        let form = Object.assign({}, this.form)\r\n        let params = {\r\n          workDate: form.workDate,\r\n          factory: form.factory,\r\n          userMinutesList: this.dayUserList,\r\n          otherMinutesList: [\r\n            ...this.weightMinutesList,\r\n            ...this.otherMinutesList,\r\n            ...this.manageMinutesList,\r\n            ...this.qcMinutesList,\r\n          ],\r\n        }\r\n        const res = await pushHrUserAnDate(params)\r\n        if(res.code === 200) {\r\n          this.msgSuccess('推送成功!')\r\n        }\r\n      }\r\n      this.btnLoading = false\r\n    },\r\n    async submitForm() {\r\n      let form = Object.assign({}, this.form)\r\n\r\n      const dayUserList = this.dayUserList\r\n      for (const item of dayUserList) {\r\n        item.exceptionTips = this.exceptionOptions.filter(i=> item.exceptionArray.includes(i.value)).map(i=>i.label).join('|')\r\n      }\r\n      form.userMinutesList = dayUserList\r\n\r\n      form.weightMinutesList = this.weightMinutesList\r\n      form.otherMinutesList = this.otherMinutesList\r\n      form.manageMinutesList = this.manageMinutesList\r\n      form.qcMinutesList = this.qcMinutesList\r\n\r\n      if (form.id != null) {\r\n        try {\r\n          this.btnLoading = true\r\n          await updateDayHours(form)\r\n          this.btnLoading = false\r\n          this.msgSuccess(\"修改成功\")\r\n          // this.$parent.$parent.open = false\r\n          // await this.$parent.$parent.getList()\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n  },\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n\r\n</style>\r\n"]}]}