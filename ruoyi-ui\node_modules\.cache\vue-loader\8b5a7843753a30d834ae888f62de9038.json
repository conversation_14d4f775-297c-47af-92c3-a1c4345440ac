{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\baseTable.vue?vue&type=template&id=56fae32d&scoped=true", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\baseTable.vue", "mtime": 1753954679642}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1744596530059}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}