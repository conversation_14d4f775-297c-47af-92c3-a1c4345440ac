import request from '@/utils/request'

// 查询办公用品订单商品列表
export function listOfficeOrderGoods(query) {
  return request({
    url: '/resource/officeOrderGoods/list',
    method: 'get',
    params: query
  })
}

// 查询办公用品订单商品详细
export function getOfficeOrderGoods(id) {
  return request({
    url: '/resource/officeOrderGoods/' + id,
    method: 'get'
  })
}

// 新增办公用品订单商品
export function addOfficeOrderGoods(data) {
  return request({
    url: '/resource/officeOrderGoods',
    method: 'post',
    data: data
  })
}

// 修改办公用品订单商品
export function updateOfficeOrderGoods(data) {
  return request({
    url: '/resource/officeOrderGoods',
    method: 'put',
    data: data
  })
}

// 删除办公用品订单商品
export function delOfficeOrderGoods(id) {
  return request({
    url: '/resource/officeOrderGoods/' + id,
    method: 'delete'
  })
}

// 导出办公用品订单商品
export function exportOfficeOrderGoods(query) {
  return request({
    url: '/resource/officeOrderGoods/export',
    method: 'get',
    params: query
  })
}