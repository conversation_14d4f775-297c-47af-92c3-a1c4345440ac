import request from '@/utils/request'

// 查询公司人员成本列表
export function listPersonnelCostDay(query) {
  return request({
    url: '/hr/personnelCostDay/list',
    method: 'get',
    params: query
  })
}// 查询公司人员成本列表
export function dataListPersonnelCostDay(query) {
  return request({
    url: '/hr/personnelCostDay/dataList',
    method: 'get',
    params: query
  })
}

// 查询公司人员成本详细
export function getPersonnelCostDay(id) {
  return request({
    url: '/hr/personnelCostDay/' + id,
    method: 'get'
  })
}

// 新增公司人员成本
export function addPersonnelCostDay(data) {
  return request({
    url: '/hr/personnelCostDay',
    method: 'post',
    data: data
  })
}

// 修改公司人员成本
export function updatePersonnelCostDay(data) {
  return request({
    url: '/hr/personnelCostDay',
    method: 'put',
    data: data
  })
}

// 删除公司人员成本
export function delPersonnelCostDay(id) {
  return request({
    url: '/hr/personnelCostDay/' + id,
    method: 'delete'
  })
}

// 导出公司人员成本
export function exportPersonnelCostDay(query) {
  return request({
    url: '/hr/personnelCostDay/export',
    method: 'get',
    params: query
  })
}

// 刷新人员工资成本
export function handleHrCostRefreshData(query) {
  return request({
    url: '/hr/personnelCostDay/handleRefreshData',
    method: 'get',
    params: query
  })
}
