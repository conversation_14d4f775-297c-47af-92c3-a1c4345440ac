{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\engineerSampleOrder\\index.vue?vue&type=template&id=b582a56c&scoped=true", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\engineerSampleOrder\\index.vue", "mtime": 1753954184863}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1744596530059}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}