{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\engineerSampleOrder\\index.vue?vue&type=template&id=b582a56c&scoped=true", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\engineerSampleOrder\\index.vue", "mtime": 1753927608484}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1744596530059}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}