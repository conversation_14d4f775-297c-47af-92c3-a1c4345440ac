import request from '@/utils/request'

// 查询计划区域列表
export function listPlanArea(query) {
  return request({
    url: '/production/planArea/list',
    method: 'get',
    params: query
  })
}

// 查询计划区域详细
export function getPlanArea(id) {
  return request({
    url: '/production/planArea/' + id,
    method: 'get'
  })
}

// 新增计划区域
export function addPlanArea(data) {
  return request({
    url: '/production/planArea',
    method: 'post',
    data: data
  })
}

// 修改计划区域
export function updatePlanArea(data) {
  return request({
    url: '/production/planArea',
    method: 'put',
    data: data
  })
}

// 删除计划区域
export function delPlanArea(id) {
  return request({
    url: '/production/planArea/' + id,
    method: 'delete'
  })
}

// 导出计划区域
export function exportPlanArea(query) {
  return request({
    url: '/production/planArea/export',
    method: 'get',
    params: query
  })
}

export function allPlanArea(query) {
  return request({
    url: '/production/planArea/all',
    method: 'get',
    params: query
  })
}
