import request from '@/utils/request'

// 查询开票申请明细列表
export function listBillingApplyItem(query) {
  return request({
    url: '/order/billingApplyItem/list',
    method: 'get',
    params: query
  })
}

// 查询开票申请明细详细
export function getBillingApplyItem(id) {
  return request({
    url: '/order/billingApplyItem/' + id,
    method: 'get'
  })
}

// 新增开票申请明细
export function addBillingApplyItem(data) {
  return request({
    url: '/order/billingApplyItem',
    method: 'post',
    data: data
  })
}

// 修改开票申请明细
export function updateBillingApplyItem(data) {
  return request({
    url: '/order/billingApplyItem',
    method: 'put',
    data: data
  })
}

// 删除开票申请明细
export function delBillingApplyItem(id) {
  return request({
    url: '/order/billingApplyItem/' + id,
    method: 'delete'
  })
}

// 导出开票申请明细
export function exportBillingApplyItem(billingApplyId) {
  return request({
    url: '/order/billingApplyItem/export/' + billingApplyId,
    method: 'get',
  })
}

export function allBillingApplyItem(query) {
  return request({
    url: '/order/billingApplyItem/all',
    method: 'get',
    params: query
  })
}
