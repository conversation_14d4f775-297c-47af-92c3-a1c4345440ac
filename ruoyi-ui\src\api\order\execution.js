import request from '@/utils/request'

// 查询订单流程进行列表
export function listExecution(query) {
  return request({
    url: '/order/execution/list',
    method: 'get',
    params: query
  })
}

// 查询订单流程进行详细
export function getExecution(id) {
  return request({
    url: '/order/execution/' + id,
    method: 'get'
  })
}

// 新增订单流程进行
export function addExecution(data) {
  return request({
    url: '/order/execution',
    method: 'post',
    data: data
  })
}

// 修改订单流程进行
export function updateExecution(data) {
  return request({
    url: '/order/execution',
    method: 'put',
    data: data
  })
}

// 删除订单流程进行
export function delExecution(id) {
  return request({
    url: '/order/execution/' + id,
    method: 'delete'
  })
}

// 导出订单流程进行
export function exportExecution(query) {
  return request({
    url: '/order/execution/export',
    method: 'get',
    params: query
  })
}

//获取出库单 对应订单信息
export function allLibraryOrderIdsData(query) {
  return request({
    url: '/order/execution/allLibraryOrderIdsData',
    method: 'get',
    params: query
  })
}


// 查询订单流程进行列表
export function allOrdreExecutionDataList(query) {
  return request({
    url: '/order/execution/allOrdreExecutionDataList',
    method: 'get',
    params: query
  })
}



export function orderBillingApplyNoPass(data) {
  return request({
    url: '/order/billingApply/orderBillingApplyNoPass',
    method: 'put',
    data: data
  })
}
