import request from '@/utils/request'

// 查询客户委托加工合同列表
export function listContract(query) {
  return request({
    url: '/customer/contract/list',
    method: 'get',
    params: query
  })
}

// 查询客户委托加工合同详细
export function getContract(id) {
  return request({
    url: '/customer/contract/' + id,
    method: 'get'
  })
}

// 新增客户委托加工合同
export function addContract(data) {
  return request({
    url: '/customer/contract',
    method: 'post',
    data: data
  })
}

// 修改客户委托加工合同
export function updateContract(data) {
  return request({
    url: '/customer/contract',
    method: 'put',
    data: data
  })
}

// 删除客户委托加工合同
export function delContract(id) {
  return request({
    url: '/customer/contract/' + id,
    method: 'delete'
  })
}

// 导出客户委托加工合同
export function exportContract(query) {
  return request({
    url: '/customer/contract/export',
    method: 'get',
    params: query
  })
}