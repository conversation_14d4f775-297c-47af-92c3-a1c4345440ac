import request from '@/utils/request'

// 查询ERP交易信息列表
export function listErpTransInvtb(query) {
  return request({
    url: '/order/erpTransInvtb/list',
    method: 'get',
    params: query
  })
}

// 查询ERP交易信息详细
export function getErpTransInvtb(id) {
  return request({
    url: '/order/erpTransInvtb/' + id,
    method: 'get'
  })
}

// 新增ERP交易信息
export function addErpTransInvtb(data) {
  return request({
    url: '/order/erpTransInvtb',
    method: 'post',
    data: data
  })
}

// 修改ERP交易信息
export function updateErpTransInvtb(data) {
  return request({
    url: '/order/erpTransInvtb',
    method: 'put',
    data: data
  })
}

// 导出ERP交易信息
export function exportErpTransInvtb(query) {
  return request({
    url: '/order/erpTransInvtb/export',
    method: 'get',
    params: query
  })
}
