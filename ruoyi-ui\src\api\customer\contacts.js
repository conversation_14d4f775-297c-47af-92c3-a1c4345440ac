import request from '@/utils/request'

// 查询客户联系人列表
export function listContacts(query) {
  return request({
    url: '/customer/contacts/list',
    method: 'get',
    params: query
  })
}

// 查询客户联系人详细
export function getContacts(id) {
  return request({
    url: '/customer/contacts/' + id,
    method: 'get'
  })
}

// 新增客户联系人
export function addContacts(data) {
  return request({
    url: '/customer/contacts',
    method: 'post',
    data: data
  })
}

// 修改客户联系人
export function updateContacts(data) {
  return request({
    url: '/customer/contacts',
    method: 'put',
    data: data
  })
}

// 删除客户联系人
export function delContacts(id) {
  return request({
    url: '/customer/contacts/' + id,
    method: 'delete'
  })
}

// 导出客户联系人
export function exportContacts(query) {
  return request({
    url: '/customer/contacts/export',
    method: 'get',
    params: query
  })
}

export function allContacts(query) {
  return request({
    url: '/customer/contacts/all',
    method: 'get',
    params: query
  })
}
