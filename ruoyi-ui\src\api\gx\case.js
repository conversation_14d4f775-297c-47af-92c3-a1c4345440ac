import request from '@/utils/request'

// 查询功效打卡方案列表
export function listCase(query) {
  return request({
    url: '/gx/case/list',
    method: 'get',
    params: query
  })
}

// 查询功效打卡方案详细
export function getCase(id) {
  return request({
    url: '/gx/case/' + id,
    method: 'get'
  })
}

// 新增功效打卡方案
export function addCase(data) {
  return request({
    url: '/gx/case',
    method: 'post',
    data: data
  })
}

// 修改功效打卡方案
export function updateCase(data) {
  return request({
    url: '/gx/case',
    method: 'put',
    data: data
  })
}

// 删除功效打卡方案
export function delCase(id) {
  return request({
    url: '/gx/case/' + id,
    method: 'delete'
  })
}

// 导出功效打卡方案
export function exportCaseLogs(id) {
  return request({
    url: '/gx/case/exportLogs/' + id,
    method: 'get'
  })
}

export function finishCase(data) {
  return request({
    url: '/gx/case/finish',
    method: 'put',
    data,
  })
}

export function restoreCase(id) {
  return request({
    url: '/gx/case/restore/' + id,
    method: 'put',
  })
}

export function importTemplateCase(id) {
  return request({
    url: '/gx/case/importTemplate/' + id,
    method: 'get'
  })
}

export function asyncGxUserValue(id) {
  return request({
    url: '/gx/case/asyncGxUserValue/' + id,
    method: 'get'
  })
}

export function truncationGxCaseDk(data) {
  return request({
    url: '/gx/case/truncationDk',
    method: 'put',
    data,
  })
}
