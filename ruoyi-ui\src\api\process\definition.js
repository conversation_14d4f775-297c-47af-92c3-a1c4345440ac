import request from '@/utils/request'

export function listDefinition(query) {
  return request({
    url: '/process/definition/list',
    method: 'get',
    params: query
  })
}

// 查询请假详细
export function getDefinitionsByInstanceId(instanceId) {
  return request({
    url: '/process/definition/getDefinitions/' + instanceId,
    method: 'get'
  })
}

export function exportDefinitionXML(query) {
  return request({
    url: '/project/formula/export',
    method: 'get',
    params: query
  })
}
export function getDefinitionXML(query) {
  return request({
    url: '/process/definition/getDefinitionXML',
    method: 'get',
    params: query
  })
}

// 挂起激活转换
export function suspendOrActiveApply(data) {
  return request({
    url: '/process/definition/suspendOrActiveApply',
    method: 'post',
    data:data
  })
}

export function delDefinition(id) {
  return request({
    url: '/process/definition/remove/' + id,
    method: 'delete'
  })
}
