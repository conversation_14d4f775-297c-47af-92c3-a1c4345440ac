import request from '@/utils/request'

// 查询退料单申请列表
export function listReturnMaterial(query) {
  return request({
    url: '/production/returnMaterial/list',
    method: 'get',
    params: query
  })
}

// 查询退料单申请详细
export function getReturnMaterial(id) {
  return request({
    url: '/production/returnMaterial/' + id,
    method: 'get'
  })
}

// 新增退料单申请
export function addReturnMaterial(data) {
  return request({
    url: '/production/returnMaterial',
    method: 'post',
    data: data
  })
}

// 修改退料单申请
export function updateReturnMaterial(data) {
  return request({
    url: '/production/returnMaterial',
    method: 'put',
    data: data
  })
}

// 删除退料单申请
export function delReturnMaterial(id) {
  return request({
    url: '/production/returnMaterial/' + id,
    method: 'delete'
  })
}

// 导出退料单申请
export function exportReturnMaterial(query) {
  return request({
    url: '/production/returnMaterial/export',
    method: 'get',
    params: query
  })
}