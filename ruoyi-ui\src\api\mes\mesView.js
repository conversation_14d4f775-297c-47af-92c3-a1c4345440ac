import request from '@/utils/request'

export function allWipMaterialLog(query) {
  return request({
    url: '/mes/view/allWipMaterialLog',
    method: 'get',
    params: query
  })
}

export function allWipLotLog(query) {
  return request({
    url: '/mes/view/allWipLotLog',
    method: 'get',
    params: query
  })
}

export function allWipLotUser(query) {
  return request({
    url: '/mes/view/allWipLotUser',
    method: 'get',
    params: query
  })
}

export function allMesLotAreaUserVo(query) {
  return request({
    url: '/mes/view/allMesLotAreaUserVo',
    method: 'get',
    params: query
  })
}

export function allMesLotEventVo(query) {
  return request({
    url: '/mes/view/allMesLotEventVo',
    method: 'get',
    params: query
  })
}

export function allMesLotWaitVo(query) {
  return request({
    url: '/mes/view/allMesLotWaitVo',
    method: 'get',
    params: query
  })
}
