import request from '@/utils/request'

// 查询其它账户到账记录列表
export function listFinanceArrivalOtherRecord(query) {
  return request({
    url: '/order/financeArrivalOtherRecord/list',
    method: 'get',
    params: query
  })
}

// 查询其它账户到账记录详细
export function getFinanceArrivalOtherRecord(id) {
  return request({
    url: '/order/financeArrivalOtherRecord/' + id,
    method: 'get'
  })
}


// 撤销子项目订单
export function revokeFinanceArrivalOtherRecord(data) {
  return request({
    url: '/order/financeArrivalOtherRecord/revoke',
    method: 'post',
    data: data
  })
}


// 新增其它账户到账记录
export function addFinanceArrivalOtherRecord(data) {
  return request({
    url: '/order/financeArrivalOtherRecord',
    method: 'post',
    data: data
  })
}

// 修改其它账户到账记录
export function updateFinanceArrivalOtherRecord(data) {
  return request({
    url: '/order/financeArrivalOtherRecord',
    method: 'put',
    data: data
  })
}

// 删除其它账户到账记录
export function delFinanceArrivalOtherRecord(id) {
  return request({
    url: '/order/financeArrivalOtherRecord/' + id,
    method: 'delete'
  })
}

// 导出其它账户到账记录
export function exportFinanceArrivalOtherRecord(query) {
  return request({
    url: '/order/financeArrivalOtherRecord/export',
    method: 'get',
    params: query
  })
}
