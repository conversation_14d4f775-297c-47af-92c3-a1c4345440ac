import request from '@/utils/request'

// 查询包材检验项目模板列表
export function listMaterialProjectModel(query) {
  return request({
    url: '/qc/materialProjectModel/list',
    method: 'get',
    params: query
  })
}

// 查询包材检验项目模板列表
export function listMaterialProjectModelAll(query) {
  return request({
    url: '/qc/materialProjectModel/all',
    method: 'get',
    params: query
  })
}

// 查询包材检验项目模板详细
export function getMaterialProjectModel(id) {
  return request({
    url: '/qc/materialProjectModel/' + id,
    method: 'get'
  })
}

// 新增包材检验项目模板
export function addMaterialProjectModel(data) {
  return request({
    url: '/qc/materialProjectModel',
    method: 'post',
    data: data
  })
}

// 修改包材检验项目模板
export function updateMaterialProjectModel(data) {
  return request({
    url: '/qc/materialProjectModel',
    method: 'put',
    data: data
  })
}

// 删除包材检验项目模板
export function delMaterialProjectModel(id) {
  return request({
    url: '/qc/materialProjectModel/' + id,
    method: 'delete'
  })
}

// 导出包材检验项目模板
export function exportMaterialProjectModel(query) {
  return request({
    url: '/qc/materialProjectModel/export',
    method: 'get',
    params: query
  })
}
