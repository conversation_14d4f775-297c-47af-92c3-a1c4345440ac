import request from "@/utils/request";

export function listReportUser(query) {
  return request({
    url: '/report/user/list',
    method: 'get',
    params: query
  })
}
export function listReportUserUser(query) {
  return request({
    url: '/report/user/userList',
    method: 'get',
    params: query
  })
}
export function listReportUserLeave(query) {
  return request({
    url: '/report/user/leave',
    method: 'get',
    params: query
  })
}
export function exportReportUserLeave(query) {
  return request({
    url: '/report/user/leave/export',
    method: 'get',
    params: query
  })
}
export function listReportUserAttendanceLeaveOvertimeOut(query) {
  return request({
    url: '/report/user/userAttendanceLeaveOvertimeOut/list',
    method: 'get',
    params: query
  })
}
export function exportReportUserAttendanceLeaveOvertimeOut(query) {
  return request({
    url: '/report/user/userAttendanceLeaveOvertimeOut/export',
    method: 'get',
    params: query
  })
}
