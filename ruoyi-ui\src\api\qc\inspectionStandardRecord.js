import request from '@/utils/request'

// 查询包材物料检验标准记录列表
export function listInspectionStandardRecord(query) {
  return request({
    url: '/qc/inspectionStandardRecord/list',
    method: 'get',
    params: query
  })
}

// 查询包材物料检验标准记录详细
export function getInspectionStandardRecord(id) {
  return request({
    url: '/qc/inspectionStandardRecord/' + id,
    method: 'get'
  })
}

// 新增包材物料检验标准记录
export function addInspectionStandardRecord(data) {
  return request({
    url: '/qc/inspectionStandardRecord',
    method: 'post',
    data: data
  })
}

// 修改包材物料检验标准记录
export function updateInspectionStandardRecord(data) {
  return request({
    url: '/qc/inspectionStandardRecord',
    method: 'put',
    data: data
  })
}

// 删除包材物料检验标准记录
export function delInspectionStandardRecord(id) {
  return request({
    url: '/qc/inspectionStandardRecord/' + id,
    method: 'delete'
  })
}

// 导出包材物料检验标准记录
export function exportInspectionStandardRecord(query) {
  return request({
    url: '/qc/inspectionStandardRecord/export',
    method: 'get',
    params: query
  })
}

export function allInspectionStandardRecord(query) {
  return request({
    url: '/qc/inspectionStandardRecord/all',
    method: 'get',
    params: query
  })
}

export function allInspectionPackagingType(query) {
  return request({
    url: '/qc/inspectionStandardRecord/allPackagingType',
    method: 'get',
    params: query
  })
}
