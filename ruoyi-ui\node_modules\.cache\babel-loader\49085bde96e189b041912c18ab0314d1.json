{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\customer\\contract\\save.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\customer\\contract\\save.vue", "mtime": 1753954679641}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\babel.config.js", "mtime": 1743382537964}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9zZWFuL3dvcmtzcGFjZS9lbm93X3Byb2plY3QvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfcmVnZW5lcmF0b3JSdW50aW1lMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQzovc2Vhbi93b3Jrc3BhY2UvZW5vd19wcm9qZWN0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL3JlZ2VuZXJhdG9yUnVudGltZS5qcyIpKTsKdmFyIF9hc3luY1RvR2VuZXJhdG9yMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQzovc2Vhbi93b3Jrc3BhY2UvZW5vd19wcm9qZWN0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2FzeW5jVG9HZW5lcmF0b3IuanMiKSk7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5wdXNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5zcGxpY2UuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmZ1bmN0aW9uLm5hbWUuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmpzb24uc3RyaW5naWZ5LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5udW1iZXIuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC5rZXlzLmpzIik7CnZhciBfY29udHJhY3QgPSByZXF1aXJlKCJAL2FwaS9jdXN0b21lci9jb250cmFjdCIpOwp2YXIgX2FpID0gcmVxdWlyZSgiQC9hcGkvY29tbW9uL2FpIik7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAiY3VzdG9tZXJDb250cmFjdFNhdmUiLAogIHByb3BzOiB7CiAgICBjdXN0b21lcklkOiB7CiAgICAgIHR5cGU6IE51bWJlciwKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0sCiAgICByZWFkb25seTogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICBidG5Mb2FkaW5nOiBmYWxzZSwKICAgICAgZm9ybToge30sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgbmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+mXrui+k+WFpeaWh+S7tuWQjScKICAgICAgICB9XQogICAgICB9LAogICAgICBkZWxpdmVyeVR5cGVPcHRpb25zOiBbJysnLCAnLScsICfCsSddLAogICAgICBmaWxlczogW10sCiAgICAgIHF1YWxpdHlGaWxlczogW10sCiAgICAgIHNob3dEb2M6IGZhbHNlLAogICAgICBkYXRhQXJyYXk6IFtdLAogICAgICBvOiB7CiAgICAgICAgInBhcnR5X2EiOiAi55Sy5pa55ZCN56ewIiwKICAgICAgICAicGFydHlfYiI6ICLkuZnmlrnlkI3np7AiLAogICAgICAgICJzaWduZWRfZGF0ZSI6ICLnrb7orqLml6XmnJ8iLAogICAgICAgICJvbl9zZXRfZGF0ZSI6ICLotbfmlYjml6XmnJ8iLAogICAgICAgICJleHBpcmVfZGF0ZSI6ICLliLDmnJ/ml6XmnJ8iLAogICAgICAgICJwYXltZW50X3Rlcm0iOiAi6LSm5pyf5o+P6L+wIiwKICAgICAgICAiZmluYW5jaWFsX3Jpc2siOiAi6LSi5Yqh6aOO6Zmp5o+P6L+wIiwKICAgICAgICAiZGVsaXZlcnlfY29udHJvbCI6ICLkuqTotKfmlbDph4/mjqfliLbmnaHmrL4iLAogICAgICAgICJkZWxheV9yaXNrIjogIuS6pOi0p+W7tuivr+mjjumZqeadoeasviIsCiAgICAgICAgInF1YWxpdHlfcmVzcG9uc2liaWxpdHkiOiAi6LSo6YeP6LSj5Lu75Y2P6K6uIiwKICAgICAgICAicXVhbGl0eV9yaXNrIjogIui0qOmHj+mjjumZqeaPj+i/sCIsCiAgICAgICAgInByb2R1Y3Rpb25fcmlzayI6ICLnlJ/kuqfmjZ/ogJfkuI7miJDmnKzpo47pmakiLAogICAgICAgICJpcF9yaXNrIjogIuefpeivhuS6p+adg+S4juS/neWvhumjjumZqSIsCiAgICAgICAgImJyZWFjaF9saWFiaWxpdHkiOiAi6L+d57qm6LSj5Lu75p2h5qy+IiwKICAgICAgICAiZGF0YV9yaXNrIjogIuaVsOaNruS4juiusOW9lemjjumZqSIsCiAgICAgICAgIm90aGVyX3Jpc2tzIjogIuWFtuS7lumjjumZqeaPj+i/sCIsCiAgICAgICAgInBhcnR5X2Jfc3VnZ2VzdGlvbnMiOiAi5LmZ5pa55bu66K6uIgogICAgICB9LAogICAgICBwYXJ0eUJTdWdnZXN0aW9uczogW10KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZSgpIHsKICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZSQoX2NvbnRleHQpIHsKICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgY2FzZSAwOgogICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LnN0b3AoKTsKICAgICAgICB9CiAgICAgIH0sIF9jYWxsZWUpOwogICAgfSkpKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBmaWxlQ2hhbmdlOiBmdW5jdGlvbiBmaWxlQ2hhbmdlKGZpbGVzKSB7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUyKCkgewogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUyJChfY29udGV4dDIpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0Mi5wcmV2ID0gX2NvbnRleHQyLm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIGNvbnNvbGUubG9nKGZpbGVzKTsKICAgICAgICAgICAgICAvLyBmb3IgKGNvbnN0IGZpbGUgb2YgZmlsZXMpIHsKICAgICAgICAgICAgICAvLyAgIGF3YWl0IHRoaXMuc2VsZWN0RmlsZShmaWxlKQogICAgICAgICAgICAgIC8vIH0KICAgICAgICAgICAgY2FzZSAxOgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUyKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgYWRkSXRlbTogZnVuY3Rpb24gYWRkSXRlbSgpIHsKICAgICAgdGhpcy5wYXJ0eUJTdWdnZXN0aW9ucy5wdXNoKCcnKTsKICAgIH0sCiAgICBkZWxJdGVtOiBmdW5jdGlvbiBkZWxJdGVtKGkpIHsKICAgICAgdGhpcy5wYXJ0eUJTdWdnZXN0aW9ucy5zcGxpY2UoaSwgMSk7CiAgICB9LAogICAgc2VsZWN0RmlsZTogZnVuY3Rpb24gc2VsZWN0RmlsZShmaWxlKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUzKCkgewogICAgICAgIHZhciByZXMsIGRhdGEsIG8sIF9pLCBfT2JqZWN0JGtleXMsIGs7CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3JSdW50aW1lMi5kZWZhdWx0KSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTMkKF9jb250ZXh0MykgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQzLnByZXYgPSBfY29udGV4dDMubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX3RoaXMubG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgICAgaWYgKCFmaWxlLnVybCkgewogICAgICAgICAgICAgICAgX2NvbnRleHQzLm5leHQgPSA3OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzLmRhdGFBcnJheSA9IFtdOwogICAgICAgICAgICAgIF9jb250ZXh0My5uZXh0ID0gNTsKICAgICAgICAgICAgICByZXR1cm4gKDAsIF9haS5nZXRBaVJlc29sdmVDb250cmFjdCkoZmlsZSk7CiAgICAgICAgICAgIGNhc2UgNToKICAgICAgICAgICAgICByZXMgPSBfY29udGV4dDMuc2VudDsKICAgICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgICAgICAgZGF0YSA9IHJlcy5kYXRhOwogICAgICAgICAgICAgICAgaWYgKGRhdGEpIHsKICAgICAgICAgICAgICAgICAgbyA9IGRhdGEuZXh0cmFjdGVkX2RhdGE7CiAgICAgICAgICAgICAgICAgIGlmIChvKSB7CiAgICAgICAgICAgICAgICAgICAgZm9yIChfaSA9IDAsIF9PYmplY3Qka2V5cyA9IE9iamVjdC5rZXlzKG8pOyBfaSA8IF9PYmplY3Qka2V5cy5sZW5ndGg7IF9pKyspIHsKICAgICAgICAgICAgICAgICAgICAgIGsgPSBfT2JqZWN0JGtleXNbX2ldOwogICAgICAgICAgICAgICAgICAgICAgX3RoaXMuZGF0YUFycmF5LnB1c2goewogICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogX3RoaXMub1trXSwKICAgICAgICAgICAgICAgICAgICAgICAga2V5OiBrLAogICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogb1trXQogICAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIF90aGlzLmZvcm0ubmFtZSA9IGZpbGUubmFtZTsKICAgICAgICAgICAgICAgICAgX3RoaXMuZm9ybS5wYXJ0eUEgPSBvLnBhcnR5X2E7CiAgICAgICAgICAgICAgICAgIF90aGlzLmZvcm0ucGFydHlCID0gby5wYXJ0eV9iOwogICAgICAgICAgICAgICAgICBfdGhpcy5mb3JtLnNpZ25lZERhdGUgPSBvLnNpZ25lZF9kYXRlOwogICAgICAgICAgICAgICAgICBfdGhpcy5mb3JtLm9uU2V0RGF0ZSA9IG8ub25fc2V0X2RhdGU7CiAgICAgICAgICAgICAgICAgIF90aGlzLmZvcm0uZXhwaXJlRGF0ZSA9IG8uZXhwaXJlX2RhdGU7CiAgICAgICAgICAgICAgICAgIF90aGlzLmZvcm0ucGF5bWVudFRlcm0gPSBvLnBheW1lbnRfdGVybTsKICAgICAgICAgICAgICAgICAgX3RoaXMuZm9ybS5maW5hbmNpYWxSaXNrID0gby5maW5hbmNpYWxfcmlzazsKICAgICAgICAgICAgICAgICAgX3RoaXMuZm9ybS5kZWxpdmVyeUNvbnRyb2wgPSBvLmRlbGl2ZXJ5X2NvbnRyb2w7CiAgICAgICAgICAgICAgICAgIF90aGlzLmZvcm0uZGVsYXlSaXNrID0gby5kZWxheV9yaXNrOwogICAgICAgICAgICAgICAgICBfdGhpcy5mb3JtLnF1YWxpdHlSZXNwb25zaWJpbGl0eSA9IG8ucXVhbGl0eV9yZXNwb25zaWJpbGl0eTsKICAgICAgICAgICAgICAgICAgX3RoaXMuZm9ybS5xdWFsaXR5UmlzayA9IG8ucXVhbGl0eV9yaXNrOwogICAgICAgICAgICAgICAgICBfdGhpcy5mb3JtLnByb2R1Y3Rpb25SaXNrID0gby5wcm9kdWN0aW9uX3Jpc2s7CiAgICAgICAgICAgICAgICAgIF90aGlzLmZvcm0uaXBSaXNrID0gby5pcF9yaXNrOwogICAgICAgICAgICAgICAgICBfdGhpcy5mb3JtLmJyZWFjaExpYWJpbGl0eSA9IG8uYnJlYWNoX2xpYWJpbGl0eTsKICAgICAgICAgICAgICAgICAgX3RoaXMuZm9ybS5kYXRhUmlzayA9IG8uZGF0YV9yaXNrOwogICAgICAgICAgICAgICAgICBfdGhpcy5mb3JtLm90aGVyID0gby5vdGhlcl9yaXNrczsKICAgICAgICAgICAgICAgICAgX3RoaXMuZm9ybS50ZXh0ID0gSlNPTi5zdHJpbmdpZnkoZGF0YSk7CiAgICAgICAgICAgICAgICAgIF90aGlzLnNob3dEb2MgPSB0cnVlOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgaWYgKGRhdGEuYWR2aWNlKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzLnBhcnR5QlN1Z2dlc3Rpb25zID0gZGF0YS5hZHZpY2UucGFydHlfYl9zdWdnZXN0aW9uczsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgIGNhc2UgODoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQzLnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMyk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGNhbmNlbDogZnVuY3Rpb24gY2FuY2VsKCkgewogICAgICB0aGlzLiRwYXJlbnQuJHBhcmVudC5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICByZXNldDogZnVuY3Rpb24gcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBpZDogbnVsbCwKICAgICAgICBjdXN0b21lcklkOiBudWxsLAogICAgICAgIGNvZGU6IG51bGwsCiAgICAgICAgbmFtZTogbnVsbCwKICAgICAgICBwZXJpb2RUeXBlOiBudWxsLAogICAgICAgIHBlcmlvZERheXM6IG51bGwsCiAgICAgICAgbnVtczogbnVsbCwKICAgICAgICBkZWxpdmVyeVR5cGU6IG51bGwsCiAgICAgICAgb3ZlcmR1ZTogbnVsbCwKICAgICAgICBxdWFsaXR5UmVzcG9uc2liaWxpdHk6IG51bGwsCiAgICAgICAgb3RoZXI6IG51bGwsCiAgICAgICAgc2lnbmVkRGF0ZTogbnVsbCwKICAgICAgICBvblNldERhdGU6IG51bGwsCiAgICAgICAgZXhwaXJlRGF0ZTogbnVsbCwKICAgICAgICBsb2NhdGlvbkNvZGU6IG51bGwsCiAgICAgICAgcmVtYXJrOiBudWxsLAogICAgICAgIHBhcnR5QTogbnVsbCwKICAgICAgICBwYXJ0eUI6IG51bGwsCiAgICAgICAgZmluYW5jaWFsUmlzazogbnVsbCwKICAgICAgICBkZWxpdmVyeUNvbnRyb2w6IG51bGwsCiAgICAgICAgZGVsYXlSaXNrOiBudWxsLAogICAgICAgIHF1YWxpdHlSaXNrOiBudWxsLAogICAgICAgIHByb2R1Y3Rpb25SaXNrOiBudWxsLAogICAgICAgIGlwUmlzazogbnVsbCwKICAgICAgICBicmVhY2hMaWFiaWxpdHk6IG51bGwsCiAgICAgICAgZGF0YVJpc2s6IG51bGwsCiAgICAgICAgdGV4dDogbnVsbAogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgICB0aGlzLmZpbGVzID0gW107CiAgICAgIHRoaXMucXVhbGl0eUZpbGVzID0gW107CiAgICAgIHRoaXMuZGF0YUFycmF5ID0gW107CiAgICAgIHRoaXMucGFydHlCU3VnZ2VzdGlvbnMgPSBbXTsKICAgICAgdGhpcy5zaG93RG9jID0gdHJ1ZTsKICAgIH0sCiAgICBpbml0OiBmdW5jdGlvbiBpbml0KGlkKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlNCgpIHsKICAgICAgICB2YXIgcmVzLCBmb3JtOwogICAgICAgIHJldHVybiAoMCwgX3JlZ2VuZXJhdG9yUnVudGltZTIuZGVmYXVsdCkoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWU0JChfY29udGV4dDQpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0NC5wcmV2ID0gX2NvbnRleHQ0Lm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF90aGlzMi5sb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgICAgICBfY29udGV4dDQubmV4dCA9IDM7CiAgICAgICAgICAgICAgcmV0dXJuICgwLCBfY29udHJhY3QuZ2V0Q29udHJhY3QpKGlkKTsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIHJlcyA9IF9jb250ZXh0NC5zZW50OwogICAgICAgICAgICAgIGZvcm0gPSByZXMuZGF0YTsKICAgICAgICAgICAgICBpZiAoZm9ybS5maWxlcykgewogICAgICAgICAgICAgICAgX3RoaXMyLmZpbGVzID0gSlNPTi5wYXJzZShmb3JtLmZpbGVzKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgaWYgKGZvcm0ucXVhbGl0eUZpbGVzKSB7CiAgICAgICAgICAgICAgICBfdGhpczIucXVhbGl0eUZpbGVzID0gSlNPTi5wYXJzZShmb3JtLnF1YWxpdHlGaWxlcyk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGlmIChmb3JtLnBhcnR5QlN1Z2dlc3Rpb25zKSB7CiAgICAgICAgICAgICAgICBfdGhpczIucGFydHlCU3VnZ2VzdGlvbnMgPSBKU09OLnBhcnNlKGZvcm0ucGFydHlCU3VnZ2VzdGlvbnMpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfdGhpczIuZm9ybSA9IGZvcm07CiAgICAgICAgICAgICAgX3RoaXMyLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgY2FzZSAxMDoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ0LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNCk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlNSgpIHsKICAgICAgICB2YXIgZm9ybTsKICAgICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvclJ1bnRpbWUyLmRlZmF1bHQpKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlNSQoX2NvbnRleHQ1KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDUucHJldiA9IF9jb250ZXh0NS5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfY29udGV4dDUubmV4dCA9IDI7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKCk7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBmb3JtID0gT2JqZWN0LmFzc2lnbih7fSwgX3RoaXMzLmZvcm0pOwogICAgICAgICAgICAgIGZvcm0uZmlsZXMgPSBKU09OLnN0cmluZ2lmeShfdGhpczMuZmlsZXMpOwogICAgICAgICAgICAgIGZvcm0ucXVhbGl0eUZpbGVzID0gSlNPTi5zdHJpbmdpZnkoX3RoaXMzLnF1YWxpdHlGaWxlcyk7CiAgICAgICAgICAgICAgZm9ybS5wYXJ0eUJTdWdnZXN0aW9ucyA9IEpTT04uc3RyaW5naWZ5KF90aGlzMy5wYXJ0eUJTdWdnZXN0aW9ucyk7CiAgICAgICAgICAgICAgaWYgKCEoZm9ybS5pZCAhPSBudWxsKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQ1Lm5leHQgPSAyMzsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dDUucHJldiA9IDc7CiAgICAgICAgICAgICAgX3RoaXMzLmJ0bkxvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgIF9jb250ZXh0NS5uZXh0ID0gMTE7CiAgICAgICAgICAgICAgcmV0dXJuICgwLCBfY29udHJhY3QudXBkYXRlQ29udHJhY3QpKGZvcm0pOwogICAgICAgICAgICBjYXNlIDExOgogICAgICAgICAgICAgIF90aGlzMy5idG5Mb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXMzLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIF90aGlzMy4kcGFyZW50LiRwYXJlbnQub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIF9jb250ZXh0NS5uZXh0ID0gMTY7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMy4kcGFyZW50LiRwYXJlbnQuZ2V0TGlzdCgpOwogICAgICAgICAgICBjYXNlIDE2OgogICAgICAgICAgICAgIF9jb250ZXh0NS5uZXh0ID0gMjE7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMTg6CiAgICAgICAgICAgICAgX2NvbnRleHQ1LnByZXYgPSAxODsKICAgICAgICAgICAgICBfY29udGV4dDUudDAgPSBfY29udGV4dDVbImNhdGNoIl0oNyk7CiAgICAgICAgICAgICAgX3RoaXMzLmJ0bkxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgY2FzZSAyMToKICAgICAgICAgICAgICBfY29udGV4dDUubmV4dCA9IDM4OwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDIzOgogICAgICAgICAgICAgIGZvcm0uY3VzdG9tZXJJZCA9IF90aGlzMy5jdXN0b21lcklkOwogICAgICAgICAgICAgIF9jb250ZXh0NS5wcmV2ID0gMjQ7CiAgICAgICAgICAgICAgX3RoaXMzLmJ0bkxvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgIF9jb250ZXh0NS5uZXh0ID0gMjg7CiAgICAgICAgICAgICAgcmV0dXJuICgwLCBfY29udHJhY3QuYWRkQ29udHJhY3QpKGZvcm0pOwogICAgICAgICAgICBjYXNlIDI4OgogICAgICAgICAgICAgIF90aGlzMy5idG5Mb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXMzLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIF90aGlzMy4kcGFyZW50LiRwYXJlbnQub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIF9jb250ZXh0NS5uZXh0ID0gMzM7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMy4kcGFyZW50LiRwYXJlbnQuZ2V0TGlzdCgpOwogICAgICAgICAgICBjYXNlIDMzOgogICAgICAgICAgICAgIF9jb250ZXh0NS5uZXh0ID0gMzg7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMzU6CiAgICAgICAgICAgICAgX2NvbnRleHQ1LnByZXYgPSAzNTsKICAgICAgICAgICAgICBfY29udGV4dDUudDEgPSBfY29udGV4dDVbImNhdGNoIl0oMjQpOwogICAgICAgICAgICAgIF90aGlzMy5idG5Mb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgIGNhc2UgMzg6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0NS5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTUsIG51bGwsIFtbNywgMThdLCBbMjQsIDM1XV0pOwogICAgICB9KSkoKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_contract", "require", "_ai", "name", "props", "customerId", "type", "Number", "required", "readonly", "Boolean", "default", "data", "loading", "btnLoading", "form", "rules", "message", "deliveryTypeOptions", "files", "qualityFiles", "showDoc", "dataArray", "o", "partyBSuggestions", "created", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "stop", "methods", "fileChange", "_callee2", "_callee2$", "_context2", "console", "log", "addItem", "push", "delItem", "i", "splice", "selectFile", "file", "_this", "_callee3", "res", "_i", "_Object$keys", "k", "_callee3$", "_context3", "url", "getAiResolveContract", "sent", "code", "extracted_data", "Object", "keys", "length", "label", "key", "value", "partyA", "party_a", "partyB", "party_b", "signedDate", "signed_date", "onSetDate", "on_set_date", "expireDate", "expire_date", "paymentTerm", "payment_term", "financialRisk", "financial_risk", "deliveryControl", "delivery_control", "delayRisk", "delay_risk", "qualityResponsibility", "quality_responsibility", "qualityRisk", "quality_risk", "productionRisk", "production_risk", "ipRisk", "ip_risk", "breachLiability", "breach_liability", "dataRisk", "data_risk", "other", "other_risks", "text", "JSON", "stringify", "advice", "party_b_suggestions", "cancel", "$parent", "open", "reset", "id", "periodType", "periodDays", "nums", "deliveryType", "overdue", "locationCode", "remark", "resetForm", "init", "_this2", "_callee4", "_callee4$", "_context4", "getContract", "parse", "submitForm", "_this3", "_callee5", "_callee5$", "_context5", "$refs", "validate", "assign", "updateContract", "msgSuccess", "getList", "t0", "addContract", "t1"], "sources": ["src/views/customer/contract/save.vue"], "sourcesContent": ["<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" size=\"mini\" label-width=\"150px\" >\r\n      <div :class=\"readonly ? 'mask' : ''\" >\r\n        <el-row :gutter=\"20\" >\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"文件名称\" prop=\"name\">\r\n              <el-input v-model=\"form.name\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"甲方\" prop=\"partyA\">\r\n              <el-input v-model=\"form.partyA\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"乙方\" prop=\"partyB\">\r\n              <el-input v-model=\"form.partyB\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"签订日期\" prop=\"signedDate\">\r\n              <el-date-picker\r\n                v-model=\"form.signedDate\"\r\n                clearable\r\n                type=\"date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"起效日期\" prop=\"onSetDate\">\r\n              <el-date-picker\r\n                v-model=\"form.onSetDate\"\r\n                clearable\r\n                type=\"date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"到期日期\" prop=\"expireDate\">\r\n              <el-date-picker\r\n                v-model=\"form.expireDate\"\r\n                clearable\r\n                type=\"date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"账期描述\" prop=\"paymentTerm\">\r\n          <el-input v-model=\"form.paymentTerm\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"财务风险描述\" prop=\"financialRisk\">\r\n          <el-input v-model=\"form.financialRisk\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"交货数量控制条款\" prop=\"deliveryControl\">\r\n          <el-input v-model=\"form.deliveryControl\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"交货延误风险条款\" prop=\"delayRisk\">\r\n          <el-input v-model=\"form.delayRisk\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"质量责任协议\" prop=\"qualityResponsibility\">\r\n          <el-input v-model=\"form.qualityResponsibility\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"质量风险描述\" prop=\"qualityRisk\">\r\n          <el-input v-model=\"form.qualityRisk\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"生产损耗与成本风险\" prop=\"productionRisk\">\r\n          <el-input v-model=\"form.productionRisk\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"知识产权与保密风险\" prop=\"ipRisk\">\r\n          <el-input v-model=\"form.ipRisk\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"违约责任条款\" prop=\"breachLiability\">\r\n          <el-input v-model=\"form.breachLiability\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"数据与记录风险\" prop=\"dataRisk\">\r\n          <el-input v-model=\"form.dataRisk\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"其他风险描述\" prop=\"other\">\r\n          <el-input v-model=\"form.other\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n      </div>\r\n\r\n      <el-divider content-position=\"left\" >加工合同</el-divider>\r\n\r\n      <FileUpload v-model=\"files\" :readonly=\"readonly\" @change=\"fileChange\" />\r\n\r\n      <el-divider content-position=\"left\" >质量协议</el-divider>\r\n\r\n      <FileUpload v-model=\"qualityFiles\" :readonly=\"readonly\" />\r\n\r\n      <template v-if=\"showDoc\" >\r\n        <el-divider content-position=\"left\" >解析结果 <span style=\"color: #F56C6C\" >解析时间较长,请耐心等待(请尽量上传原件,提高解析识别率,缩短等待时间).所有解析结果需人工重新确认,此处解析只做辅助使用</span> </el-divider>\r\n\r\n        <div class=\"table-wrapper\" >\r\n          <table class=\"base-table small-table\" >\r\n            <tr>\r\n              <th ><i class=\"el-icon-circle-plus-outline\" @click=\"addItem\" /></th>\r\n              <th >乙方建议</th>\r\n            </tr>\r\n            <tr v-for=\"(item,index) in partyBSuggestions\" :key=\"index\" >\r\n              <td><i class=\"el-icon-remove-outline\" @click=\"delItem(index)\" /></td>\r\n              <td>\r\n                <el-input v-model=\"partyBSuggestions[index]\" size=\"mini\" />\r\n              </td>\r\n            </tr>\r\n          </table>\r\n        </div>\r\n<!--        <el-row :gutter=\"20\" >-->\r\n<!--          <el-col :col=\"16\" >-->\r\n<!--            <div class=\"table-wrapper\" >-->\r\n<!--              <table class=\"base-table small-table\" >-->\r\n<!--                <tr>-->\r\n<!--                  <th style=\"width: 120px\" >标签</th>-->\r\n<!--                  <th >解析内容</th>-->\r\n<!--                </tr>-->\r\n<!--                <tr v-for=\"(item,index) in dataArray\" :key=\"index\" >-->\r\n<!--                  <td>{{item.label}}</td>-->\r\n<!--                  <td>{{item.value}}</td>-->\r\n<!--                </tr>-->\r\n<!--              </table>-->\r\n<!--            </div>-->\r\n<!--          </el-col>-->\r\n<!--          <el-col :col=\"8\" >-->\r\n<!--            -->\r\n<!--          </el-col>-->\r\n<!--        </el-row>-->\r\n      </template>\r\n\r\n    </el-form>\r\n    <div v-if=\"!readonly\" slot=\"footer\" class=\"dialog-footer\" style=\"margin-top: 20px\">\r\n      <el-button type=\"primary\" @click=\"submitForm\" size=\"mini\" :loading=\"btnLoading\" >确 定</el-button>\r\n      <el-button @click=\"cancel\" size=\"mini\" >取 消</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport {addContract, getContract, updateContract} from \"@/api/customer/contract\";\r\nimport {getAiResolveContract} from \"@/api/common/ai\";\r\n\r\nexport default {\r\n  name: \"customerContractSave\",\r\n  props: {\r\n    customerId: {\r\n      type: Number,\r\n      required: true,\r\n    },\r\n    readonly: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      btnLoading: false,\r\n      form: {},\r\n      rules: {\r\n        name: [\r\n          {required: true,message: '请问输入文件名'},\r\n        ]\r\n      },\r\n      deliveryTypeOptions: ['+','-','±'],\r\n      files: [],\r\n      qualityFiles: [],\r\n      showDoc: false,\r\n      dataArray: [],\r\n      o : {\r\n        \"party_a\": \"甲方名称\",\r\n        \"party_b\": \"乙方名称\",\r\n        \"signed_date\": \"签订日期\",\r\n        \"on_set_date\": \"起效日期\",\r\n        \"expire_date\": \"到期日期\",\r\n        \"payment_term\": \"账期描述\",\r\n        \"financial_risk\": \"财务风险描述\",\r\n        \"delivery_control\": \"交货数量控制条款\",\r\n        \"delay_risk\": \"交货延误风险条款\",\r\n        \"quality_responsibility\": \"质量责任协议\",\r\n        \"quality_risk\": \"质量风险描述\",\r\n        \"production_risk\": \"生产损耗与成本风险\",\r\n        \"ip_risk\": \"知识产权与保密风险\",\r\n        \"breach_liability\": \"违约责任条款\",\r\n        \"data_risk\": \"数据与记录风险\",\r\n        \"other_risks\": \"其他风险描述\",\r\n        \"party_b_suggestions\": \"乙方建议\",\r\n      },\r\n      partyBSuggestions: [],\r\n    }\r\n  },\r\n  async created() {\r\n  },\r\n  methods: {\r\n    async fileChange(files) {\r\n      console.log(files)\r\n      // for (const file of files) {\r\n      //   await this.selectFile(file)\r\n      // }\r\n    },\r\n    addItem() {\r\n      this.partyBSuggestions.push('')\r\n    },\r\n    delItem(i) {\r\n      this.partyBSuggestions.splice(i,1)\r\n    },\r\n    async selectFile(file) {\r\n      this.loading = true\r\n      if(file.url) {\r\n        this.dataArray = []\r\n        const res = await getAiResolveContract(file)\r\n        if(res.code === 200 ) {\r\n          const data = res.data\r\n          if(data) {\r\n            const o = data.extracted_data\r\n            if(o) {\r\n              for (const k of Object.keys(o)) {\r\n                this.dataArray.push({\r\n                  label: this.o[k],\r\n                  key: k,\r\n                  value: o[k]\r\n                })\r\n              }\r\n            }\r\n            this.form.name = file.name\r\n            this.form.partyA = o.party_a\r\n            this.form.partyB = o.party_b\r\n            this.form.signedDate = o.signed_date\r\n            this.form.onSetDate = o.on_set_date\r\n            this.form.expireDate = o.expire_date\r\n            this.form.paymentTerm = o.payment_term\r\n            this.form.financialRisk = o.financial_risk\r\n            this.form.deliveryControl = o.delivery_control\r\n            this.form.delayRisk = o.delay_risk\r\n            this.form.qualityResponsibility = o.quality_responsibility\r\n            this.form.qualityRisk = o.quality_risk\r\n            this.form.productionRisk = o.production_risk\r\n            this.form.ipRisk = o.ip_risk\r\n            this.form.breachLiability = o.breach_liability\r\n            this.form.dataRisk = o.data_risk\r\n            this.form.other = o.other_risks\r\n            this.form.text = JSON.stringify(data)\r\n            this.showDoc = true\r\n          }\r\n          if(data.advice) {\r\n            this.partyBSuggestions = data.advice.party_b_suggestions\r\n          }\r\n        }\r\n      }\r\n      this.loading = false\r\n    },\r\n    cancel() {\r\n      this.$parent.$parent.open = false;\r\n      this.reset();\r\n    },\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        customerId: null,\r\n        code: null,\r\n        name: null,\r\n        periodType: null,\r\n        periodDays: null,\r\n        nums: null,\r\n        deliveryType: null,\r\n        overdue: null,\r\n        qualityResponsibility: null,\r\n        other: null,\r\n        signedDate: null,\r\n        onSetDate: null,\r\n        expireDate: null,\r\n        locationCode: null,\r\n        remark: null,\r\n        partyA: null,\r\n        partyB: null,\r\n        financialRisk: null,\r\n        deliveryControl: null,\r\n        delayRisk: null,\r\n        qualityRisk: null,\r\n        productionRisk: null,\r\n        ipRisk: null,\r\n        breachLiability: null,\r\n        dataRisk: null,\r\n        text: null,\r\n      };\r\n      this.resetForm(\"form\")\r\n      this.files = []\r\n      this.qualityFiles = []\r\n      this.dataArray = []\r\n      this.partyBSuggestions = []\r\n      this.showDoc = true\r\n    },\r\n    async init(id) {\r\n      this.loading = true\r\n      const res = await getContract(id)\r\n      const form = res.data\r\n\r\n      if(form.files) {\r\n        this.files = JSON.parse(form.files)\r\n      }\r\n\r\n      if(form.qualityFiles) {\r\n        this.qualityFiles = JSON.parse(form.qualityFiles)\r\n      }\r\n\r\n      if(form.partyBSuggestions) {\r\n        this.partyBSuggestions = JSON.parse(form.partyBSuggestions)\r\n      }\r\n\r\n      this.form = form\r\n      this.loading = false\r\n    },\r\n    async submitForm() {\r\n      await this.$refs[\"form\"].validate()\r\n      let form = Object.assign({},this.form)\r\n\r\n      form.files = JSON.stringify(this.files)\r\n      form.qualityFiles = JSON.stringify(this.qualityFiles)\r\n      form.partyBSuggestions = JSON.stringify(this.partyBSuggestions)\r\n\r\n      if (form.id != null) {\r\n        try {\r\n          this.btnLoading = true\r\n          await updateContract(form)\r\n          this.btnLoading = false\r\n          this.msgSuccess(\"修改成功\")\r\n          this.$parent.$parent.open = false\r\n          await this.$parent.$parent.getList()\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      } else {\r\n        form.customerId = this.customerId\r\n        try {\r\n          this.btnLoading = true\r\n          await addContract(form)\r\n          this.btnLoading = false\r\n          this.msgSuccess(\"新增成功\")\r\n          this.$parent.$parent.open = false;\r\n          await this.$parent.$parent.getList()\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;AA8IA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,GAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAI,OAAA;MACAC,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACAC,IAAA;MACAC,KAAA;QACAb,IAAA,GACA;UAAAK,QAAA;UAAAS,OAAA;QAAA;MAEA;MACAC,mBAAA;MACAC,KAAA;MACAC,YAAA;MACAC,OAAA;MACAC,SAAA;MACAC,CAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACAC,iBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,WAAAC,kBAAA,CAAAf,OAAA,mBAAAgB,oBAAA,CAAAhB,OAAA,IAAAiB,IAAA,UAAAC,QAAA;MAAA,WAAAF,oBAAA,CAAAhB,OAAA,IAAAmB,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;UAAA;YAAA,OAAAF,QAAA,CAAAG,IAAA;QAAA;MAAA,GAAAN,OAAA;IAAA;EACA;EACAO,OAAA;IACAC,UAAA,WAAAA,WAAAlB,KAAA;MAAA,WAAAO,kBAAA,CAAAf,OAAA,mBAAAgB,oBAAA,CAAAhB,OAAA,IAAAiB,IAAA,UAAAU,SAAA;QAAA,WAAAX,oBAAA,CAAAhB,OAAA,IAAAmB,IAAA,UAAAS,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAP,IAAA,GAAAO,SAAA,CAAAN,IAAA;YAAA;cACAO,OAAA,CAAAC,GAAA,CAAAvB,KAAA;cACA;cACA;cACA;YAAA;YAAA;cAAA,OAAAqB,SAAA,CAAAL,IAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IACAK,OAAA,WAAAA,QAAA;MACA,KAAAnB,iBAAA,CAAAoB,IAAA;IACA;IACAC,OAAA,WAAAA,QAAAC,CAAA;MACA,KAAAtB,iBAAA,CAAAuB,MAAA,CAAAD,CAAA;IACA;IACAE,UAAA,WAAAA,WAAAC,IAAA;MAAA,IAAAC,KAAA;MAAA,WAAAxB,kBAAA,CAAAf,OAAA,mBAAAgB,oBAAA,CAAAhB,OAAA,IAAAiB,IAAA,UAAAuB,SAAA;QAAA,IAAAC,GAAA,EAAAxC,IAAA,EAAAW,CAAA,EAAA8B,EAAA,EAAAC,YAAA,EAAAC,CAAA;QAAA,WAAA5B,oBAAA,CAAAhB,OAAA,IAAAmB,IAAA,UAAA0B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxB,IAAA,GAAAwB,SAAA,CAAAvB,IAAA;YAAA;cACAgB,KAAA,CAAArC,OAAA;cAAA,KACAoC,IAAA,CAAAS,GAAA;gBAAAD,SAAA,CAAAvB,IAAA;gBAAA;cAAA;cACAgB,KAAA,CAAA5B,SAAA;cAAAmC,SAAA,CAAAvB,IAAA;cAAA,OACA,IAAAyB,wBAAA,EAAAV,IAAA;YAAA;cAAAG,GAAA,GAAAK,SAAA,CAAAG,IAAA;cACA,IAAAR,GAAA,CAAAS,IAAA;gBACAjD,IAAA,GAAAwC,GAAA,CAAAxC,IAAA;gBACA,IAAAA,IAAA;kBACAW,CAAA,GAAAX,IAAA,CAAAkD,cAAA;kBACA,IAAAvC,CAAA;oBACA,KAAA8B,EAAA,MAAAC,YAAA,GAAAS,MAAA,CAAAC,IAAA,CAAAzC,CAAA,GAAA8B,EAAA,GAAAC,YAAA,CAAAW,MAAA,EAAAZ,EAAA;sBAAAE,CAAA,GAAAD,YAAA,CAAAD,EAAA;sBACAH,KAAA,CAAA5B,SAAA,CAAAsB,IAAA;wBACAsB,KAAA,EAAAhB,KAAA,CAAA3B,CAAA,CAAAgC,CAAA;wBACAY,GAAA,EAAAZ,CAAA;wBACAa,KAAA,EAAA7C,CAAA,CAAAgC,CAAA;sBACA;oBACA;kBACA;kBACAL,KAAA,CAAAnC,IAAA,CAAAZ,IAAA,GAAA8C,IAAA,CAAA9C,IAAA;kBACA+C,KAAA,CAAAnC,IAAA,CAAAsD,MAAA,GAAA9C,CAAA,CAAA+C,OAAA;kBACApB,KAAA,CAAAnC,IAAA,CAAAwD,MAAA,GAAAhD,CAAA,CAAAiD,OAAA;kBACAtB,KAAA,CAAAnC,IAAA,CAAA0D,UAAA,GAAAlD,CAAA,CAAAmD,WAAA;kBACAxB,KAAA,CAAAnC,IAAA,CAAA4D,SAAA,GAAApD,CAAA,CAAAqD,WAAA;kBACA1B,KAAA,CAAAnC,IAAA,CAAA8D,UAAA,GAAAtD,CAAA,CAAAuD,WAAA;kBACA5B,KAAA,CAAAnC,IAAA,CAAAgE,WAAA,GAAAxD,CAAA,CAAAyD,YAAA;kBACA9B,KAAA,CAAAnC,IAAA,CAAAkE,aAAA,GAAA1D,CAAA,CAAA2D,cAAA;kBACAhC,KAAA,CAAAnC,IAAA,CAAAoE,eAAA,GAAA5D,CAAA,CAAA6D,gBAAA;kBACAlC,KAAA,CAAAnC,IAAA,CAAAsE,SAAA,GAAA9D,CAAA,CAAA+D,UAAA;kBACApC,KAAA,CAAAnC,IAAA,CAAAwE,qBAAA,GAAAhE,CAAA,CAAAiE,sBAAA;kBACAtC,KAAA,CAAAnC,IAAA,CAAA0E,WAAA,GAAAlE,CAAA,CAAAmE,YAAA;kBACAxC,KAAA,CAAAnC,IAAA,CAAA4E,cAAA,GAAApE,CAAA,CAAAqE,eAAA;kBACA1C,KAAA,CAAAnC,IAAA,CAAA8E,MAAA,GAAAtE,CAAA,CAAAuE,OAAA;kBACA5C,KAAA,CAAAnC,IAAA,CAAAgF,eAAA,GAAAxE,CAAA,CAAAyE,gBAAA;kBACA9C,KAAA,CAAAnC,IAAA,CAAAkF,QAAA,GAAA1E,CAAA,CAAA2E,SAAA;kBACAhD,KAAA,CAAAnC,IAAA,CAAAoF,KAAA,GAAA5E,CAAA,CAAA6E,WAAA;kBACAlD,KAAA,CAAAnC,IAAA,CAAAsF,IAAA,GAAAC,IAAA,CAAAC,SAAA,CAAA3F,IAAA;kBACAsC,KAAA,CAAA7B,OAAA;gBACA;gBACA,IAAAT,IAAA,CAAA4F,MAAA;kBACAtD,KAAA,CAAA1B,iBAAA,GAAAZ,IAAA,CAAA4F,MAAA,CAAAC,mBAAA;gBACA;cACA;YAAA;cAEAvD,KAAA,CAAArC,OAAA;YAAA;YAAA;cAAA,OAAA4C,SAAA,CAAAtB,IAAA;UAAA;QAAA,GAAAgB,QAAA;MAAA;IACA;IACAuD,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA,CAAAA,OAAA,CAAAC,IAAA;MACA,KAAAC,KAAA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA9F,IAAA;QACA+F,EAAA;QACAzG,UAAA;QACAwD,IAAA;QACA1D,IAAA;QACA4G,UAAA;QACAC,UAAA;QACAC,IAAA;QACAC,YAAA;QACAC,OAAA;QACA5B,qBAAA;QACAY,KAAA;QACA1B,UAAA;QACAE,SAAA;QACAE,UAAA;QACAuC,YAAA;QACAC,MAAA;QACAhD,MAAA;QACAE,MAAA;QACAU,aAAA;QACAE,eAAA;QACAE,SAAA;QACAI,WAAA;QACAE,cAAA;QACAE,MAAA;QACAE,eAAA;QACAE,QAAA;QACAI,IAAA;MACA;MACA,KAAAiB,SAAA;MACA,KAAAnG,KAAA;MACA,KAAAC,YAAA;MACA,KAAAE,SAAA;MACA,KAAAE,iBAAA;MACA,KAAAH,OAAA;IACA;IACAkG,IAAA,WAAAA,KAAAT,EAAA;MAAA,IAAAU,MAAA;MAAA,WAAA9F,kBAAA,CAAAf,OAAA,mBAAAgB,oBAAA,CAAAhB,OAAA,IAAAiB,IAAA,UAAA6F,SAAA;QAAA,IAAArE,GAAA,EAAArC,IAAA;QAAA,WAAAY,oBAAA,CAAAhB,OAAA,IAAAmB,IAAA,UAAA4F,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1F,IAAA,GAAA0F,SAAA,CAAAzF,IAAA;YAAA;cACAsF,MAAA,CAAA3G,OAAA;cAAA8G,SAAA,CAAAzF,IAAA;cAAA,OACA,IAAA0F,qBAAA,EAAAd,EAAA;YAAA;cAAA1D,GAAA,GAAAuE,SAAA,CAAA/D,IAAA;cACA7C,IAAA,GAAAqC,GAAA,CAAAxC,IAAA;cAEA,IAAAG,IAAA,CAAAI,KAAA;gBACAqG,MAAA,CAAArG,KAAA,GAAAmF,IAAA,CAAAuB,KAAA,CAAA9G,IAAA,CAAAI,KAAA;cACA;cAEA,IAAAJ,IAAA,CAAAK,YAAA;gBACAoG,MAAA,CAAApG,YAAA,GAAAkF,IAAA,CAAAuB,KAAA,CAAA9G,IAAA,CAAAK,YAAA;cACA;cAEA,IAAAL,IAAA,CAAAS,iBAAA;gBACAgG,MAAA,CAAAhG,iBAAA,GAAA8E,IAAA,CAAAuB,KAAA,CAAA9G,IAAA,CAAAS,iBAAA;cACA;cAEAgG,MAAA,CAAAzG,IAAA,GAAAA,IAAA;cACAyG,MAAA,CAAA3G,OAAA;YAAA;YAAA;cAAA,OAAA8G,SAAA,CAAAxF,IAAA;UAAA;QAAA,GAAAsF,QAAA;MAAA;IACA;IACAK,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MAAA,WAAArG,kBAAA,CAAAf,OAAA,mBAAAgB,oBAAA,CAAAhB,OAAA,IAAAiB,IAAA,UAAAoG,SAAA;QAAA,IAAAjH,IAAA;QAAA,WAAAY,oBAAA,CAAAhB,OAAA,IAAAmB,IAAA,UAAAmG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjG,IAAA,GAAAiG,SAAA,CAAAhG,IAAA;YAAA;cAAAgG,SAAA,CAAAhG,IAAA;cAAA,OACA6F,MAAA,CAAAI,KAAA,SAAAC,QAAA;YAAA;cACArH,IAAA,GAAAgD,MAAA,CAAAsE,MAAA,KAAAN,MAAA,CAAAhH,IAAA;cAEAA,IAAA,CAAAI,KAAA,GAAAmF,IAAA,CAAAC,SAAA,CAAAwB,MAAA,CAAA5G,KAAA;cACAJ,IAAA,CAAAK,YAAA,GAAAkF,IAAA,CAAAC,SAAA,CAAAwB,MAAA,CAAA3G,YAAA;cACAL,IAAA,CAAAS,iBAAA,GAAA8E,IAAA,CAAAC,SAAA,CAAAwB,MAAA,CAAAvG,iBAAA;cAAA,MAEAT,IAAA,CAAA+F,EAAA;gBAAAoB,SAAA,CAAAhG,IAAA;gBAAA;cAAA;cAAAgG,SAAA,CAAAjG,IAAA;cAEA8F,MAAA,CAAAjH,UAAA;cAAAoH,SAAA,CAAAhG,IAAA;cAAA,OACA,IAAAoG,wBAAA,EAAAvH,IAAA;YAAA;cACAgH,MAAA,CAAAjH,UAAA;cACAiH,MAAA,CAAAQ,UAAA;cACAR,MAAA,CAAApB,OAAA,CAAAA,OAAA,CAAAC,IAAA;cAAAsB,SAAA,CAAAhG,IAAA;cAAA,OACA6F,MAAA,CAAApB,OAAA,CAAAA,OAAA,CAAA6B,OAAA;YAAA;cAAAN,SAAA,CAAAhG,IAAA;cAAA;YAAA;cAAAgG,SAAA,CAAAjG,IAAA;cAAAiG,SAAA,CAAAO,EAAA,GAAAP,SAAA;cAEAH,MAAA,CAAAjH,UAAA;YAAA;cAAAoH,SAAA,CAAAhG,IAAA;cAAA;YAAA;cAGAnB,IAAA,CAAAV,UAAA,GAAA0H,MAAA,CAAA1H,UAAA;cAAA6H,SAAA,CAAAjG,IAAA;cAEA8F,MAAA,CAAAjH,UAAA;cAAAoH,SAAA,CAAAhG,IAAA;cAAA,OACA,IAAAwG,qBAAA,EAAA3H,IAAA;YAAA;cACAgH,MAAA,CAAAjH,UAAA;cACAiH,MAAA,CAAAQ,UAAA;cACAR,MAAA,CAAApB,OAAA,CAAAA,OAAA,CAAAC,IAAA;cAAAsB,SAAA,CAAAhG,IAAA;cAAA,OACA6F,MAAA,CAAApB,OAAA,CAAAA,OAAA,CAAA6B,OAAA;YAAA;cAAAN,SAAA,CAAAhG,IAAA;cAAA;YAAA;cAAAgG,SAAA,CAAAjG,IAAA;cAAAiG,SAAA,CAAAS,EAAA,GAAAT,SAAA;cAEAH,MAAA,CAAAjH,UAAA;YAAA;YAAA;cAAA,OAAAoH,SAAA,CAAA/F,IAAA;UAAA;QAAA,GAAA6F,QAAA;MAAA;IAGA;EACA;AACA", "ignoreList": []}]}