import request from '@/utils/request'

// 查询成分管理列表
export function listProductNewSafety(query) {
  return request({
    url: '/rd/productNewSafety/list',
    method: 'get',
    params: query
  })
}

// 查询成分管理详细
export function getProductNewSafety(id) {
  return request({
    url: '/rd/productNewSafety/' + id,
    method: 'get'
  })
}

// 新增成分管理
export function addProductNewSafety(data) {
  return request({
    url: '/rd/productNewSafety',
    method: 'post',
    data: data
  })
}

// 修改成分管理
export function updateProductNewSafety(data) {
  return request({
    url: '/rd/productNewSafety',
    method: 'put',
    data: data
  })
}

export function updateForcedProductNewSafety(data) {
  return request({
    url: '/rd/productNewSafety/updateForced',
    method: 'put',
    data: data
  })
}

export function updateProductNewSafetyEwg(data) {
  return request({
    url: '/rd/productNewSafety/editEwg',
    method: 'put',
    data: data
  })
}

// 删除成分管理
export function delProductNewSafety(id) {
  return request({
    url: '/rd/productNewSafety/' + id,
    method: 'delete'
  })
}

// 导出成分管理
export function exportProductNewSafety(query) {
  return request({
    url: '/rd/productNewSafety/export',
    method: 'get',
    params: query
  })
}

export function allIngredients(query) {
  return request({
    url: '/rd/productNewSafety/ingredients',
    method: 'get',
    params: query
  })
}

export function exportSafetyHistory(query) {
  return request({
    url: '/rd/productNewSafety/exportHistory',
    method: 'get',
    params: query
  })
}

export function exportEwg(query) {
  return request({
    url: '/rd/productNewSafety/exportEwg',
    method: 'get',
    params: query
  })
}

export function asyncProductNewSafetyCirFiles(data) {
  return request({
    url: '/rd/productNewSafety/asyncCirFiles',
    method: 'put',
    data: data
  })
}

export function exportCirIngredientsFiles(query) {
  return request({
    url: '/rd/productNewSafety/exportCirFiles',
    method: 'get',
    params: query
  })
}

export function allBaseIngredients(query) {
  return request({
    url: '/rd/productNewSafety/baseAll',
    method: 'get',
    params: query
  })
}

export function replaceSafety() {
  return request({
    url: '/rd/productNewSafety/replaceSafety',
    method: 'get'
  })
}

export function countryAll() {
  return request({
    url: '/rd/productNewSafety/countryAll',
    method: 'get'
  })
}

const toCountryTree = (list, parentId) => {
  return list.filter(item => {
    if (item.parentId === parentId) {
      let children = toCountryTree(list, item.id)
      if(children && children.length > 0) {
        item.children = children
      }
      return true
    }
    return false
  })
}


export function getCountryTree(res) {
  let list = res
  toCountryTree(list, -1)
  list = list.filter(i=> i.parentId === -1)
  return list
}
