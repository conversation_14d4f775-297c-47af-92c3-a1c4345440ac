import request from '@/utils/request'

// 查询订单商品完结明细列表
export function listOrderGoodsProfitDetail(query) {
  return request({
    url: '/order/orderGoodsProfitDetail/list',
    method: 'get',
    params: query
  })
}

// 查询订单商品完结明细详细
export function getOrderGoodsProfitDetail(id) {
  return request({
    url: '/order/orderGoodsProfitDetail/' + id,
    method: 'get'
  })
}

// 新增订单商品完结明细
export function addOrderGoodsProfitDetail(data) {
  return request({
    url: '/order/orderGoodsProfitDetail',
    method: 'post',
    data: data
  })
}

// 修改订单商品完结明细
export function updateOrderGoodsProfitDetail(data) {
  return request({
    url: '/order/orderGoodsProfitDetail',
    method: 'put',
    data: data
  })
}
// 修改订单商品完结明细
export function batchUpdateOrderGoodsProfitDetail(data) {
  return request({
    url: '/order/orderGoodsProfitDetail/batchUpdate',
    method: 'put',
    data: data
  })
}

// 删除订单商品完结明细
export function delOrderGoodsProfitDetail(id) {
  return request({
    url: '/order/orderGoodsProfitDetail/' + id,
    method: 'delete'
  })
}

// 导出订单商品完结明细
export function exportOrderGoodsProfitDetail(query) {
  return request({
    url: '/order/orderGoodsProfitDetail/export',
    method: 'get',
    params: query
  })
}
