import request from '@/utils/request'

// 查询法务备案列表
export function listIcp(query) {
  return request({
    url: '/legal/icp/list',
    method: 'get',
    params: query
  })
}

// 查询法务备案详细
export function getIcp(id) {
  return request({
    url: '/legal/icp/' + id,
    method: 'get'
  })
}

// 新增法务备案
export function addIcp(data) {
  return request({
    url: '/legal/icp',
    method: 'post',
    data: data
  })
}

// 修改法务备案
export function updateIcp(data) {
  return request({
    url: '/legal/icp',
    method: 'put',
    data: data
  })
}

// 删除法务备案
export function delIcp(id) {
  return request({
    url: '/legal/icp/' + id,
    method: 'delete'
  })
}

export function exportFormula(data) {
  return request({
    url: '/legal/icp/exportFormula',
    method: 'post',
    data,
  })
}

export function allIcp(query) {
  return request({
    url: '/legal/icp/all',
    method: 'get',
    params: query
  })
}

export function getIcpByParams(params) {
  return request({
    url: '/legal/icp',
    method: 'get',
    params
  })
}

export function importTemplateIcp() {
  return request({
    url: '/legal/icp/importTemplate',
    method: 'get'
  })
}

export function exportIcpMeasure(data) {
  return request({
    url: '/legal/icp/exportIcpMeasure',
    method: 'post',
    data: data
  })
}

export function importIcpFormula() {
  return request({
    url: '/legal/icp/importFormula',
    method: 'get'
  })
}

export function exportIcp(query) {
  return request({
    url: '/legal/icp/export',
    method: 'get',
    params: query
  })
}

//提交审核
export function submitAudit(data) {
  return request({
    url: '/legal/icp/submitAudit',
    method: 'put',
    data: data
  })
}

//撤销申请
export function cancelAudit(data) {
  return request({
    url: '/legal/icp/cancelAudit',
    method: 'put',
    data: data
  })
}

//提交变更审核
export function submitChangeAudit(data) {
  return request({
    url: '/legal/icp/submitChangeAudit',
    method: 'put',
    data: data
  })
}

//撤销变更申请
export function cancelChangeAudit(data) {
  return request({
    url: '/legal/icp/cancelChangeAudit',
    method: 'put',
    data: data
  })
}

//导出排序前的配方列表
export function exportFormulaLog(data) {
  return request({
    url: '/legal/icp/exportFormulaLog',
    method: 'post',
    data,
  })
}

export function exportFlower(data) {
  return request({
    url: '/legal/icp/exportFlower',
    method: 'post',
    data,
  })
}

export function exportSjCountryFormula(data) {
  return request({
    url: '/legal/icp/exportSjCountryFormula',
    method: 'post',
    data,
  })
}

export function exportBaCountryFormula(data) {
  return request({
    url: '/legal/icp/exportBaCountryFormula',
    method: 'post',
    data,
  })
}

export function sjToIcp(data) {
  return request({
    url: '/legal/icp/sjToIcp',
    method: 'put',
    data: data
  })
}

export function exportSjFormula(data) {
  return request({
    url: '/legal/icp/exportSjFormula',
    method: 'post',
    data,
  })
}

export function listSjCosts(query) {
  return request({
    url: '/legal/icp/listSjCosts',
    method: 'get',
    params: query
  })
}

export function exportSjCost(params) {
  return request({
    url: '/legal/icp/exportSjCost',
    method: 'get',
    params,
  })
}
