import request from '@/utils/request'

// 查询客户跟进列表
export function listFollow(query) {
  return request({
    url: '/customer/follow/list',
    method: 'get',
    params: query
  })
}

export function allFollow(query) {
  return request({
    url: '/customer/follow/all',
    method: 'get',
    params: query
  })
}
// 查询客户跟进详细
export function getFollow(id) {
  return request({
    url: '/customer/follow/' + id,
    method: 'get'
  })
}

// 新增客户跟进
export function addFollow(data) {
  return request({
    url: '/customer/follow',
    method: 'post',
    data: data
  })
}

// 修改客户跟进
export function updateFollow(data) {
  return request({
    url: '/customer/follow',
    method: 'put',
    data: data
  })
}

// 删除客户跟进
export function delFollow(id) {
  return request({
    url: '/customer/follow/' + id,
    method: 'delete'
  })
}

// 导出客户跟进
export function exportFollow(query) {
  return request({
    url: '/customer/follow/export',
    method: 'get',
    params: query
  })
}

export function exportFollowById(id) {
  return request({
    url: '/customer/follow/exportFollowById/' + id,
    method: 'get',
  })
}
