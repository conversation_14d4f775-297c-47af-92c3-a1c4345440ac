import request from '@/utils/request'

// 新增发票
export function addInfo(data) {
  return request({
    url: '/finance/invoice',
    method: 'post',
    data: data
  })
}

// 修改发票
export function updateInfo(data) {
  return request({
    url: '/finance/invoice',
    method: 'put',
    data: data
  })
}

// 删除发票
export function delInfo(id) {
  return request({
    url: '/finance/invoice/' + id,
    method: 'delete'
  })
}
