{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\qc\\audit\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\qc\\audit\\index.vue", "mtime": 1753956861417}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\babel.config.js", "mtime": 1743382537964}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9zZWFuL3dvcmtzcGFjZS9lbm93X3Byb2plY3QvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfb2JqZWN0U3ByZWFkMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQzovc2Vhbi93b3Jrc3BhY2UvZW5vd19wcm9qZWN0L3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL29iamVjdFNwcmVhZDIuanMiKSk7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKdmFyIF9hdWRpdCA9IHJlcXVpcmUoIkAvYXBpL3FjL2F1ZGl0Iik7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAiQXVkaXQiLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g5Lqn5ZOB5YeG5YWl5qOA5p+l6KGo5qC85pWw5o2uCiAgICAgIGF1ZGl0TGlzdDogW10sCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgcHJvZHVjdENvZGU6IG51bGwsCiAgICAgICAgbGFib3JhdG9yeUNvZGU6IG51bGwsCiAgICAgICAgbWFudWZhY3R1cmVyOiBudWxsLAogICAgICAgIHByb2R1Y3ROYW1lOiBudWxsLAogICAgICAgIHBsYW5uZWRQcm9kdWN0aW9uRGF0ZTogbnVsbCwKICAgICAgICBkZWxpdmVyeURhdGU6IG51bGwsCiAgICAgICAgcHJvZHVjdFR5cGU6IG51bGwsCiAgICAgICAgcmVnaXN0cmF0aW9uQ29tcGxldGlvbkhvdmVyVGlwOiBudWxsLAogICAgICAgIGNhblByb2R1Y2U6IG51bGwsCiAgICAgICAgY2FuRGVsaXZlcjogbnVsbAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIHByb2R1Y3RDb2RlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5Lqn5ZOB5Luj56CB5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIHByb2R1Y3ROYW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5Lqn5ZOB5ZCN56ew5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i5Lqn5ZOB5YeG5YWl5qOA5p+l5YiX6KGoICovZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgKDAsIF9hdWRpdC5saXN0QXVkaXQpKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMuYXVkaXRMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICBfdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIF90aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWw6IGZ1bmN0aW9uIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0OiBmdW5jdGlvbiByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGlkOiBudWxsLAogICAgICAgIHByb2R1Y3RJZDogbnVsbCwKICAgICAgICBwcm9kdWN0Q29kZTogbnVsbCwKICAgICAgICBsYWJvcmF0b3J5Q29kZTogbnVsbCwKICAgICAgICBtYW51ZmFjdHVyZXI6IG51bGwsCiAgICAgICAgcHJvZHVjdE5hbWU6IG51bGwsCiAgICAgICAgc3BlYzogbnVsbCwKICAgICAgICBwbGFubmVkUHJvZHVjdGlvbkRhdGU6IG51bGwsCiAgICAgICAgb3JkZXJRdWFudGl0eTogbnVsbCwKICAgICAgICBkZWxpdmVyeURhdGU6IG51bGwsCiAgICAgICAgcHJvZHVjdFR5cGU6IG51bGwsCiAgICAgICAgZm9ybXVsYVN0YWJpbGl0eVJlcG9ydDogbnVsbCwKICAgICAgICBmb3JtdWxhU3RhYmlsaXR5UmVwb3J0SG92ZXJUaXA6IG51bGwsCiAgICAgICAgZm9ybXVsYUZlYXNpYmlsaXR5QXNzZXNzbWVudDogbnVsbCwKICAgICAgICBmb3JtdWxhRmVhc2liaWxpdHlBc3Nlc3NtZW50SG92ZXJUaXA6IG51bGwsCiAgICAgICAgc3RhbmRhcmRGb3JtdWxhUHJvY2VzczogbnVsbCwKICAgICAgICBtb2xkVG9vbENvbmZpcm1hdGlvbjogbnVsbCwKICAgICAgICBtb2xkVG9vbENvbmZpcm1hdGlvbkhvdmVyVGlwOiBudWxsLAogICAgICAgIGZpbGxpbmdQYWNrYWdpbmdGZWFzaWJpbGl0eTogbnVsbCwKICAgICAgICBmaWxsaW5nUGFja2FnaW5nRmVhc2liaWxpdHlIb3ZlclRpcDogbnVsbCwKICAgICAgICBmaWxsaW5nUGFja2FnaW5nU29wOiBudWxsLAogICAgICAgIGZpbmlzaGVkUHJvZHVjdFN0YW5kYXJkOiBudWxsLAogICAgICAgIHF1YWxpdHlBZ3JlZW1lbnQ6IG51bGwsCiAgICAgICAgcXVhbGl0eUFncmVlbWVudEhvdmVyVGlwOiBudWxsLAogICAgICAgIHBhY2thZ2luZ01hdGVyaWFsU3RhbmRhcmQ6IG51bGwsCiAgICAgICAgbGlxdWlkU2FtcGxlOiBudWxsLAogICAgICAgIHBhY2thZ2luZ01hdGVyaWFsU2FtcGxlOiBudWxsLAogICAgICAgIGZpbmlzaGVkUHJvZHVjdFNhbXBsZTogbnVsbCwKICAgICAgICBleGNlc3NpdmVQYWNrYWdpbmdDb25maXJtYXRpb246IG51bGwsCiAgICAgICAgZXhjZXNzaXZlUGFja2FnaW5nQ29uZmlybWF0aW9uSG92ZXJUaXA6IG51bGwsCiAgICAgICAgcmVnaXN0cmF0aW9uQ29tcGxldGlvbjogbnVsbCwKICAgICAgICByZWdpc3RyYXRpb25Db21wbGV0aW9uSG92ZXJUaXA6IG51bGwsCiAgICAgICAgZm9ybXVsYVByb2Nlc3NDb25zaXN0ZW5jeTogbnVsbCwKICAgICAgICBmb3JtdWxhUHJvY2Vzc0NvbnNpc3RlbmN5SG92ZXJUaXA6IG51bGwsCiAgICAgICAgZG9jdW1lbnRhdGlvbkNvbnNpc3RlbmN5OiBudWxsLAogICAgICAgIGRvY3VtZW50YXRpb25Db25zaXN0ZW5jeUhvdmVyVGlwOiBudWxsLAogICAgICAgIGludGVybmFsU3RhbmRhcmRDb21wbGlhbmNlOiBudWxsLAogICAgICAgIGRlbEZsYWc6IG51bGwsCiAgICAgICAgY3JlYXRlQnk6IG51bGwsCiAgICAgICAgY3JlYXRlVGltZTogbnVsbCwKICAgICAgICB1cGRhdGVCeTogbnVsbCwKICAgICAgICB1cGRhdGVUaW1lOiBudWxsLAogICAgICAgIHJlbWFyazogbnVsbCwKICAgICAgICBjYW5Qcm9kdWNlOiBudWxsLAogICAgICAgIGNhblByb2R1Y2VSZW1hcms6IG51bGwsCiAgICAgICAgY2FuRGVsaXZlcjogbnVsbCwKICAgICAgICBjYW5EZWxpdmVyUmVtYXJrOiBudWxsCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqL2hhbmRsZVF1ZXJ5OiBmdW5jdGlvbiBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqL3Jlc2V0UXVlcnk6IGZ1bmN0aW9uIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLmlkOwogICAgICB9KTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOwogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqL2hhbmRsZUFkZDogZnVuY3Rpb24gaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5Lqn5ZOB5YeG5YWl5qOA5p+lIjsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovaGFuZGxlVXBkYXRlOiBmdW5jdGlvbiBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHZhciBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkczsKICAgICAgKDAsIF9hdWRpdC5nZXRBdWRpdCkoaWQpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMyLmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIF90aGlzMi5vcGVuID0gdHJ1ZTsKICAgICAgICBfdGhpczIudGl0bGUgPSAi5L+u5pS55Lqn5ZOB5YeG5YWl5qOA5p+lIjsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqL3N1Ym1pdEZvcm06IGZ1bmN0aW9uIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBpZiAoX3RoaXMzLmZvcm0uaWQgIT0gbnVsbCkgewogICAgICAgICAgICAoMCwgX2F1ZGl0LnVwZGF0ZUF1ZGl0KShfdGhpczMuZm9ybSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgICBfdGhpczMubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgX3RoaXMzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICBfdGhpczMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICgwLCBfYXVkaXQuYWRkQXVkaXQpKF90aGlzMy5mb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIF90aGlzMy5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICBfdGhpczMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIF90aGlzMy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqL2hhbmRsZURlbGV0ZTogZnVuY3Rpb24gaGFuZGxlRGVsZXRlKHJvdykgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgdmFyIGlkcyA9IHJvdy5pZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5Lqn5ZOB5YeG5YWl5qOA5p+l57yW5Y+35Li6IicgKyBpZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiAoMCwgX2F1ZGl0LmRlbEF1ZGl0KShpZHMpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczQuZ2V0TGlzdCgpOwogICAgICAgIF90aGlzNC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkge30pOwogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi9oYW5kbGVFeHBvcnQ6IGZ1bmN0aW9uIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgicWMvYXVkaXQvZXhwb3J0IiwgKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCB0aGlzLnF1ZXJ5UGFyYW1zKSwgImF1ZGl0XyIuY29uY2F0KG5ldyBEYXRlKCkuZ2V0VGltZSgpLCAiLnhsc3giKSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_audit", "require", "name", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "auditList", "title", "open", "queryParams", "pageNum", "pageSize", "productCode", "laboratoryCode", "manufacturer", "productName", "plannedProductionDate", "deliveryDate", "productType", "registrationCompletionHoverTip", "canProduce", "canDeliver", "form", "rules", "required", "message", "trigger", "created", "getList", "methods", "_this", "listAudit", "then", "response", "rows", "cancel", "reset", "id", "productId", "spec", "orderQuantity", "formulaStabilityReport", "formulaStabilityReportHoverTip", "formulaFeasibilityAssessment", "formulaFeasibilityAssessmentHoverTip", "standardFormulaProcess", "moldToolConfirmation", "moldToolConfirmationHoverTip", "fillingPackagingFeasibility", "fillingPackagingFeasibilityHoverTip", "fillingPackagingSop", "finishedProductStandard", "qualityAgreement", "qualityAgreementHoverTip", "packagingMaterialStandard", "liquidSample", "packagingMaterialSample", "finishedProductSample", "excessivePackagingConfirmation", "excessivePackagingConfirmationHoverTip", "registrationCompletion", "formulaProcessConsistency", "formulaProcessConsistencyHoverTip", "documentationConsistency", "documentationConsistencyHoverTip", "internalStandardCompliance", "delFlag", "createBy", "createTime", "updateBy", "updateTime", "remark", "canProduceRemark", "canDeliverRemark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "get<PERSON><PERSON><PERSON>", "submitForm", "_this3", "$refs", "validate", "valid", "updateAudit", "msgSuccess", "add<PERSON><PERSON><PERSON>", "handleDelete", "_this4", "$confirm", "<PERSON><PERSON><PERSON><PERSON>", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/qc/audit/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      size=\"small\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n      label-width=\"68px\"\r\n    >\r\n      <el-form-item label=\"产品代码\" prop=\"productCode\">\r\n        <el-input\r\n          v-model=\"queryParams.productCode\"\r\n          placeholder=\"请输入产品代码\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"实验室编号\" label-width=\"100px\" prop=\"laboratoryCode\">\r\n        <el-select\r\n          v-model=\"queryParams.laboratoryCode\"\r\n          placeholder=\"请选择实验室编号\"\r\n          clearable\r\n        >\r\n          <el-option label=\"请选择字典生成\" value=\"\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"生产企业\" prop=\"manufacturer\">\r\n        <el-select\r\n          v-model=\"queryParams.manufacturer\"\r\n          placeholder=\"请选择生产企业\"\r\n          clearable\r\n        >\r\n          <el-option label=\"请选择字典生成\" value=\"\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"产品名称\" prop=\"productName\">\r\n        <el-input\r\n          v-model=\"queryParams.productName\"\r\n          placeholder=\"请输入产品名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"预计生产日期\" label-width=\"110px\" prop=\"plannedProductionDate\">\r\n        <el-date-picker\r\n          clearable\r\n          v-model=\"queryParams.plannedProductionDate\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择预计生产日期\"\r\n        >\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"产品交期\" prop=\"deliveryDate\">\r\n        <el-date-picker\r\n          clearable\r\n          v-model=\"queryParams.deliveryDate\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择产品交期\"\r\n        >\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"产品类型\" prop=\"productType\">\r\n        <el-select\r\n          v-model=\"queryParams.productType\"\r\n          placeholder=\"请选择产品类型\"\r\n          clearable\r\n        >\r\n          <el-option label=\"请选择字典生成\" value=\"\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"备案号\" prop=\"registrationCompletionHoverTip\">\r\n        <el-input\r\n          v-model=\"queryParams.registrationCompletionHoverTip\"\r\n          placeholder=\"请输入备案号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"是否可生产\" prop=\"canProduce\">\r\n        <el-input\r\n          v-model=\"queryParams.canProduce\"\r\n          placeholder=\"请输入是否可生产\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"是否可出库\" prop=\"canDeliver\">\r\n        <el-input\r\n          v-model=\"queryParams.canDeliver\"\r\n          placeholder=\"请输入是否可出库\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['qc:audit:add']\"\r\n          >新增</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['qc:audit:edit']\"\r\n          >修改</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['qc:audit:remove']\"\r\n          >删除</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['qc:audit:export']\"\r\n          >导出</el-button\r\n        >\r\n      </el-col>\r\n      <right-toolbar\r\n        :showSearch.sync=\"showSearch\"\r\n        @queryTable=\"getList\"\r\n      ></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"auditList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <!-- <el-table-column label=\"主键ID\" align=\"center\" prop=\"id\" /> -->\r\n      <el-table-column label=\"产品ID\" align=\"center\" prop=\"productId\" />\r\n      <el-table-column label=\"产品代码\" align=\"center\" prop=\"productCode\" />\r\n      <el-table-column\r\n        label=\"实验室编号\"\r\n        align=\"center\"\r\n        prop=\"laboratoryCode\"\r\n      />\r\n      <el-table-column label=\"生产企业\" align=\"center\" prop=\"manufacturer\" />\r\n      <el-table-column label=\"产品名称\" align=\"center\" prop=\"productName\" />\r\n      <el-table-column label=\"规格\" align=\"center\" prop=\"spec\" />\r\n      <el-table-column\r\n        label=\"预计生产日期\"\r\n        align=\"center\"\r\n        prop=\"plannedProductionDate\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{\r\n            parseTime(scope.row.plannedProductionDate, \"{y}-{m}-{d}\")\r\n          }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"订单数量\" align=\"center\" prop=\"orderQuantity\" />\r\n      <el-table-column\r\n        label=\"产品交期\"\r\n        align=\"center\"\r\n        prop=\"deliveryDate\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.deliveryDate, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"产品类型\" align=\"center\" prop=\"productType\" />\r\n      <el-table-column\r\n        label=\"配方稳定性报告状态\"\r\n        align=\"center\"\r\n        prop=\"formulaStabilityReport\"\r\n      />\r\n      <el-table-column\r\n        label=\"配方稳定性报告风险内容\"\r\n        align=\"center\"\r\n        prop=\"formulaStabilityReportHoverTip\"\r\n      />\r\n      <el-table-column\r\n        label=\"配制可行性评估状态\"\r\n        align=\"center\"\r\n        prop=\"formulaFeasibilityAssessment\"\r\n      />\r\n      <el-table-column\r\n        label=\"配制可行性评估风险内容\"\r\n        align=\"center\"\r\n        prop=\"formulaFeasibilityAssessmentHoverTip\"\r\n      />\r\n      <el-table-column\r\n        label=\"标准配制工艺单状态\"\r\n        align=\"center\"\r\n        prop=\"standardFormulaProcess\"\r\n      />\r\n      <el-table-column\r\n        label=\"生产模具治具确认状态\"\r\n        align=\"center\"\r\n        prop=\"moldToolConfirmation\"\r\n      />\r\n      <el-table-column\r\n        label=\"生产模具治具确认预计时间\"\r\n        align=\"center\"\r\n        prop=\"moldToolConfirmationHoverTip\"\r\n      />\r\n      <el-table-column\r\n        label=\"灌包可行性评估状态\"\r\n        align=\"center\"\r\n        prop=\"fillingPackagingFeasibility\"\r\n      />\r\n      <el-table-column\r\n        label=\"灌包可行性评估风险内容\"\r\n        align=\"center\"\r\n        prop=\"fillingPackagingFeasibilityHoverTip\"\r\n      />\r\n      <el-table-column\r\n        label=\"灌装/包装SOP状态\"\r\n        align=\"center\"\r\n        prop=\"fillingPackagingSop\"\r\n      />\r\n      <el-table-column\r\n        label=\"成品检验标准状态\"\r\n        align=\"center\"\r\n        prop=\"finishedProductStandard\"\r\n      />\r\n      <el-table-column\r\n        label=\"质量协议状态\"\r\n        align=\"center\"\r\n        prop=\"qualityAgreement\"\r\n      />\r\n      <el-table-column\r\n        label=\"质量协议合同类型是独立还是主框架合同\"\r\n        align=\"center\"\r\n        prop=\"qualityAgreementHoverTip\"\r\n      />\r\n      <el-table-column\r\n        label=\"包材标准状态\"\r\n        align=\"center\"\r\n        prop=\"packagingMaterialStandard\"\r\n      />\r\n      <el-table-column\r\n        label=\"料体标样状态\"\r\n        align=\"center\"\r\n        prop=\"liquidSample\"\r\n      />\r\n      <el-table-column\r\n        label=\"包材标准样状态\"\r\n        align=\"center\"\r\n        prop=\"packagingMaterialSample\"\r\n      />\r\n      <el-table-column\r\n        label=\"成品标样状态\"\r\n        align=\"center\"\r\n        prop=\"finishedProductSample\"\r\n      />\r\n      <el-table-column\r\n        label=\"过度包装确认状态\"\r\n        align=\"center\"\r\n        prop=\"excessivePackagingConfirmation\"\r\n      />\r\n      <el-table-column\r\n        label=\"过度包装风险内容\"\r\n        align=\"center\"\r\n        prop=\"excessivePackagingConfirmationHoverTip\"\r\n      />\r\n      <el-table-column\r\n        label=\"注册备案是否完成状态\"\r\n        align=\"center\"\r\n        prop=\"registrationCompletion\"\r\n      />\r\n      <el-table-column\r\n        label=\"备案号\"\r\n        align=\"center\"\r\n        prop=\"registrationCompletionHoverTip\"\r\n      />\r\n      <el-table-column\r\n        label=\"大货标准配方工艺单与注册/备案一致性状态\"\r\n        align=\"center\"\r\n        prop=\"formulaProcessConsistency\"\r\n      />\r\n      <el-table-column\r\n        label=\"风险内容\"\r\n        align=\"center\"\r\n        prop=\"formulaProcessConsistencyHoverTip\"\r\n      />\r\n      <el-table-column\r\n        label=\"大货文案与备案资料一致性状态\"\r\n        align=\"center\"\r\n        prop=\"documentationConsistency\"\r\n      />\r\n      <el-table-column\r\n        label=\"风险内容\"\r\n        align=\"center\"\r\n        prop=\"documentationConsistencyHoverTip\"\r\n      />\r\n      <el-table-column\r\n        label=\"产品内控标准符合备案执行标准状态\"\r\n        align=\"center\"\r\n        prop=\"internalStandardCompliance\"\r\n      />\r\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" />\r\n      <el-table-column label=\"是否可生产\" align=\"center\" prop=\"canProduce\" />\r\n      <el-table-column\r\n        label=\"是否可生产备注\"\r\n        align=\"center\"\r\n        prop=\"canProduceRemark\"\r\n      />\r\n      <el-table-column label=\"是否可出库\" align=\"center\" prop=\"canDeliver\" />\r\n      <el-table-column\r\n        label=\"是否可出库备注\"\r\n        align=\"center\"\r\n        prop=\"canDeliverRemark\"\r\n      />\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['qc:audit:edit']\"\r\n            >修改</el-button\r\n          >\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['qc:audit:remove']\"\r\n            >删除</el-button\r\n          >\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改产品准入检查对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"产品ID\" prop=\"productId\">\r\n          <el-input v-model=\"form.productId\" placeholder=\"请输入产品ID\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"产品代码\" prop=\"productCode\">\r\n          <el-input v-model=\"form.productCode\" placeholder=\"请输入产品代码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"实验室编号\" prop=\"laboratoryCode\">\r\n          <el-select\r\n            v-model=\"form.laboratoryCode\"\r\n            placeholder=\"请选择实验室编号\"\r\n          >\r\n            <el-option label=\"请选择字典生成\" value=\"\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"生产企业\" prop=\"manufacturer\">\r\n          <el-select v-model=\"form.manufacturer\" placeholder=\"请选择生产企业\">\r\n            <el-option label=\"请选择字典生成\" value=\"\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"产品名称\" prop=\"productName\">\r\n          <el-input v-model=\"form.productName\" placeholder=\"请输入产品名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"规格\" prop=\"spec\">\r\n          <el-input v-model=\"form.spec\" placeholder=\"请输入规格\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"预计生产日期\" prop=\"plannedProductionDate\">\r\n          <el-date-picker\r\n            clearable\r\n            v-model=\"form.plannedProductionDate\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"请选择预计生产日期\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"订单数量\" prop=\"orderQuantity\">\r\n          <el-input v-model=\"form.orderQuantity\" placeholder=\"请输入订单数量\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"产品交期\" prop=\"deliveryDate\">\r\n          <el-date-picker\r\n            clearable\r\n            v-model=\"form.deliveryDate\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"请选择产品交期\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"产品类型\" prop=\"productType\">\r\n          <el-select v-model=\"form.productType\" placeholder=\"请选择产品类型\">\r\n            <el-option label=\"请选择字典生成\" value=\"\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"配方稳定性报告状态\" prop=\"formulaStabilityReport\">\r\n          <el-input\r\n            v-model=\"form.formulaStabilityReport\"\r\n            placeholder=\"请输入配方稳定性报告状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"配方稳定性报告风险内容\"\r\n          prop=\"formulaStabilityReportHoverTip\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.formulaStabilityReportHoverTip\"\r\n            placeholder=\"请输入配方稳定性报告风险内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"配制可行性评估状态\"\r\n          prop=\"formulaFeasibilityAssessment\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.formulaFeasibilityAssessment\"\r\n            placeholder=\"请输入配制可行性评估状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"配制可行性评估风险内容\"\r\n          prop=\"formulaFeasibilityAssessmentHoverTip\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.formulaFeasibilityAssessmentHoverTip\"\r\n            placeholder=\"请输入配制可行性评估风险内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"标准配制工艺单状态\" prop=\"standardFormulaProcess\">\r\n          <el-input\r\n            v-model=\"form.standardFormulaProcess\"\r\n            placeholder=\"请输入标准配制工艺单状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"生产模具治具确认状态\" prop=\"moldToolConfirmation\">\r\n          <el-input\r\n            v-model=\"form.moldToolConfirmation\"\r\n            placeholder=\"请输入生产模具治具确认状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"生产模具治具确认预计时间\"\r\n          prop=\"moldToolConfirmationHoverTip\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.moldToolConfirmationHoverTip\"\r\n            placeholder=\"请输入生产模具治具确认预计时间\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"灌包可行性评估状态\"\r\n          prop=\"fillingPackagingFeasibility\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.fillingPackagingFeasibility\"\r\n            placeholder=\"请输入灌包可行性评估状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"灌包可行性评估风险内容\"\r\n          prop=\"fillingPackagingFeasibilityHoverTip\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.fillingPackagingFeasibilityHoverTip\"\r\n            placeholder=\"请输入灌包可行性评估风险内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"灌装/包装SOP状态\" prop=\"fillingPackagingSop\">\r\n          <el-input\r\n            v-model=\"form.fillingPackagingSop\"\r\n            placeholder=\"请输入灌装/包装SOP状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"成品检验标准状态\" prop=\"finishedProductStandard\">\r\n          <el-input\r\n            v-model=\"form.finishedProductStandard\"\r\n            placeholder=\"请输入成品检验标准状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"质量协议状态\" prop=\"qualityAgreement\">\r\n          <el-input\r\n            v-model=\"form.qualityAgreement\"\r\n            placeholder=\"请输入质量协议状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"质量协议合同类型是独立还是主框架合同\"\r\n          prop=\"qualityAgreementHoverTip\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.qualityAgreementHoverTip\"\r\n            placeholder=\"请输入质量协议合同类型是独立还是主框架合同\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"包材标准状态\" prop=\"packagingMaterialStandard\">\r\n          <el-input\r\n            v-model=\"form.packagingMaterialStandard\"\r\n            placeholder=\"请输入包材标准状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"料体标样状态\" prop=\"liquidSample\">\r\n          <el-input\r\n            v-model=\"form.liquidSample\"\r\n            placeholder=\"请输入料体标样状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"包材标准样状态\" prop=\"packagingMaterialSample\">\r\n          <el-input\r\n            v-model=\"form.packagingMaterialSample\"\r\n            placeholder=\"请输入包材标准样状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"成品标样状态\" prop=\"finishedProductSample\">\r\n          <el-input\r\n            v-model=\"form.finishedProductSample\"\r\n            placeholder=\"请输入成品标样状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"过度包装确认状态\"\r\n          prop=\"excessivePackagingConfirmation\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.excessivePackagingConfirmation\"\r\n            placeholder=\"请输入过度包装确认状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"过度包装风险内容\"\r\n          prop=\"excessivePackagingConfirmationHoverTip\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.excessivePackagingConfirmationHoverTip\"\r\n            placeholder=\"请输入过度包装风险内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"注册备案是否完成状态\"\r\n          prop=\"registrationCompletion\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.registrationCompletion\"\r\n            placeholder=\"请输入注册备案是否完成状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"备案号\" prop=\"registrationCompletionHoverTip\">\r\n          <el-input\r\n            v-model=\"form.registrationCompletionHoverTip\"\r\n            placeholder=\"请输入备案号\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"大货标准配方工艺单与注册/备案一致性状态\"\r\n          prop=\"formulaProcessConsistency\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.formulaProcessConsistency\"\r\n            placeholder=\"请输入大货标准配方工艺单与注册/备案一致性状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"风险内容\" prop=\"formulaProcessConsistencyHoverTip\">\r\n          <el-input\r\n            v-model=\"form.formulaProcessConsistencyHoverTip\"\r\n            placeholder=\"请输入风险内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"大货文案与备案资料一致性状态\"\r\n          prop=\"documentationConsistency\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.documentationConsistency\"\r\n            placeholder=\"请输入大货文案与备案资料一致性状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"风险内容\" prop=\"documentationConsistencyHoverTip\">\r\n          <el-input\r\n            v-model=\"form.documentationConsistencyHoverTip\"\r\n            placeholder=\"请输入风险内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"产品内控标准符合备案执行标准状态\"\r\n          prop=\"internalStandardCompliance\"\r\n        >\r\n          <el-input\r\n            v-model=\"form.internalStandardCompliance\"\r\n            placeholder=\"请输入产品内控标准符合备案执行标准状态\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"删除标志\" prop=\"delFlag\">\r\n          <el-input v-model=\"form.delFlag\" placeholder=\"请输入删除标志\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input\r\n            v-model=\"form.remark\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"是否可生产\" prop=\"canProduce\">\r\n          <el-input v-model=\"form.canProduce\" placeholder=\"请输入是否可生产\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"是否可生产备注\" prop=\"canProduceRemark\">\r\n          <el-input\r\n            v-model=\"form.canProduceRemark\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"是否可出库\" prop=\"canDeliver\">\r\n          <el-input v-model=\"form.canDeliver\" placeholder=\"请输入是否可出库\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"是否可出库备注\" prop=\"canDeliverRemark\">\r\n          <el-input\r\n            v-model=\"form.canDeliverRemark\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listAudit,\r\n  getAudit,\r\n  delAudit,\r\n  addAudit,\r\n  updateAudit,\r\n} from \"@/api/qc/audit\";\r\n\r\nexport default {\r\n  name: \"Audit\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 产品准入检查表格数据\r\n      auditList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        productCode: null,\r\n        laboratoryCode: null,\r\n        manufacturer: null,\r\n        productName: null,\r\n        plannedProductionDate: null,\r\n        deliveryDate: null,\r\n        productType: null,\r\n        registrationCompletionHoverTip: null,\r\n        canProduce: null,\r\n        canDeliver: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        productCode: [\r\n          { required: true, message: \"产品代码不能为空\", trigger: \"blur\" },\r\n        ],\r\n        productName: [\r\n          { required: true, message: \"产品名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询产品准入检查列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listAudit(this.queryParams).then((response) => {\r\n        this.auditList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        productId: null,\r\n        productCode: null,\r\n        laboratoryCode: null,\r\n        manufacturer: null,\r\n        productName: null,\r\n        spec: null,\r\n        plannedProductionDate: null,\r\n        orderQuantity: null,\r\n        deliveryDate: null,\r\n        productType: null,\r\n        formulaStabilityReport: null,\r\n        formulaStabilityReportHoverTip: null,\r\n        formulaFeasibilityAssessment: null,\r\n        formulaFeasibilityAssessmentHoverTip: null,\r\n        standardFormulaProcess: null,\r\n        moldToolConfirmation: null,\r\n        moldToolConfirmationHoverTip: null,\r\n        fillingPackagingFeasibility: null,\r\n        fillingPackagingFeasibilityHoverTip: null,\r\n        fillingPackagingSop: null,\r\n        finishedProductStandard: null,\r\n        qualityAgreement: null,\r\n        qualityAgreementHoverTip: null,\r\n        packagingMaterialStandard: null,\r\n        liquidSample: null,\r\n        packagingMaterialSample: null,\r\n        finishedProductSample: null,\r\n        excessivePackagingConfirmation: null,\r\n        excessivePackagingConfirmationHoverTip: null,\r\n        registrationCompletion: null,\r\n        registrationCompletionHoverTip: null,\r\n        formulaProcessConsistency: null,\r\n        formulaProcessConsistencyHoverTip: null,\r\n        documentationConsistency: null,\r\n        documentationConsistencyHoverTip: null,\r\n        internalStandardCompliance: null,\r\n        delFlag: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null,\r\n        canProduce: null,\r\n        canProduceRemark: null,\r\n        canDeliver: null,\r\n        canDeliverRemark: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加产品准入检查\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      getAudit(id).then((response) => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改产品准入检查\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateAudit(this.form).then((response) => {\r\n              this.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addAudit(this.form).then((response) => {\r\n              this.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$confirm('是否确认删除产品准入检查编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delAudit(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"qc/audit/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `audit_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n  },\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;AAiqBA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAQA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,SAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,WAAA;QACAC,cAAA;QACAC,YAAA;QACAC,WAAA;QACAC,qBAAA;QACAC,YAAA;QACAC,WAAA;QACAC,8BAAA;QACAC,UAAA;QACAC,UAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAX,WAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,WAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,iBACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA9B,OAAA;MACA,IAAA+B,gBAAA,OAAAtB,WAAA,EAAAuB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAxB,SAAA,GAAA2B,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAzB,KAAA,GAAA4B,QAAA,CAAA5B,KAAA;QACAyB,KAAA,CAAA9B,OAAA;MACA;IACA;IACA;IACAmC,MAAA,WAAAA,OAAA;MACA,KAAA3B,IAAA;MACA,KAAA4B,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAd,IAAA;QACAe,EAAA;QACAC,SAAA;QACA1B,WAAA;QACAC,cAAA;QACAC,YAAA;QACAC,WAAA;QACAwB,IAAA;QACAvB,qBAAA;QACAwB,aAAA;QACAvB,YAAA;QACAC,WAAA;QACAuB,sBAAA;QACAC,8BAAA;QACAC,4BAAA;QACAC,oCAAA;QACAC,sBAAA;QACAC,oBAAA;QACAC,4BAAA;QACAC,2BAAA;QACAC,mCAAA;QACAC,mBAAA;QACAC,uBAAA;QACAC,gBAAA;QACAC,wBAAA;QACAC,yBAAA;QACAC,YAAA;QACAC,uBAAA;QACAC,qBAAA;QACAC,8BAAA;QACAC,sCAAA;QACAC,sBAAA;QACAzC,8BAAA;QACA0C,yBAAA;QACAC,iCAAA;QACAC,wBAAA;QACAC,gCAAA;QACAC,0BAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;QACAnD,UAAA;QACAoD,gBAAA;QACAnD,UAAA;QACAoD,gBAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAlE,WAAA,CAAAC,OAAA;MACA,KAAAkB,OAAA;IACA;IACA,aACAgD,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA7E,GAAA,GAAA6E,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA3C,EAAA;MAAA;MACA,KAAAnC,MAAA,GAAA4E,SAAA,CAAAG,MAAA;MACA,KAAA9E,QAAA,IAAA2E,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAA9C,KAAA;MACA,KAAA5B,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACA4E,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAjD,KAAA;MACA,IAAAC,EAAA,GAAA+C,GAAA,CAAA/C,EAAA,SAAApC,GAAA;MACA,IAAAqF,eAAA,EAAAjD,EAAA,EAAAL,IAAA,WAAAC,QAAA;QACAoD,MAAA,CAAA/D,IAAA,GAAAW,QAAA,CAAAlC,IAAA;QACAsF,MAAA,CAAA7E,IAAA;QACA6E,MAAA,CAAA9E,KAAA;MACA;IACA;IACA,WACAgF,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAlE,IAAA,CAAAe,EAAA;YACA,IAAAuD,kBAAA,EAAAJ,MAAA,CAAAlE,IAAA,EAAAU,IAAA,WAAAC,QAAA;cACAuD,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAAhF,IAAA;cACAgF,MAAA,CAAA5D,OAAA;YACA;UACA;YACA,IAAAkE,eAAA,EAAAN,MAAA,CAAAlE,IAAA,EAAAU,IAAA,WAAAC,QAAA;cACAuD,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAAhF,IAAA;cACAgF,MAAA,CAAA5D,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAmE,YAAA,WAAAA,aAAAX,GAAA;MAAA,IAAAY,MAAA;MACA,IAAA/F,GAAA,GAAAmF,GAAA,CAAA/C,EAAA,SAAApC,GAAA;MACA,KAAAgG,QAAA,sBAAAhG,GAAA,aACA+B,IAAA;QACA,WAAAkE,eAAA,EAAAjG,GAAA;MACA,GACA+B,IAAA;QACAgE,MAAA,CAAApE,OAAA;QACAoE,MAAA,CAAAH,UAAA;MACA,GACAM,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,CACA,uBAAAC,cAAA,CAAAC,OAAA,MAEA,KAAA9F,WAAA,YAAA+F,MAAA,CAEA,IAAAC,IAAA,GAAAC,OAAA,YACA;IACA;EACA;AACA", "ignoreList": []}]}