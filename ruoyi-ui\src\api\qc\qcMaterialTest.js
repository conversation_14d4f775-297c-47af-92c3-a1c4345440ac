import request from '@/utils/request'

// 查询原料检测标准列表
export function listQcMaterialTest(query) {
  return request({
    url: '/qc/qcMaterialTest/list',
    method: 'get',
    params: query
  })
}

// 查询应聘需求列表
export function auditQcMaterialTest(data) {
  return request({
    url: '/qc/qcMaterialTest/audit',
    method: 'post',
    data: data
  })
}


// 查询原料检测标准详细
export function getQcMaterialTest(id) {
  return request({
    url: '/qc/qcMaterialTest/' + id,
    method: 'get'
  })
}

// 查询原料检测标准详细
export function getQcMaterialTestIdInfo(query) {
  return request({
    url: '/qc/qcMaterialTest/getQcMaterialTestIdInfo',
    method: 'get',
    params: query
  })
}

// 新增原料检测标准
export function addQcMaterialTest(data) {
  return request({
    url: '/qc/qcMaterialTest',
    method: 'post',
    data: data
  })
}

// 修改原料检测标准
export function updateQcMaterialTest(data) {
  return request({
    url: '/qc/qcMaterialTest',
    method: 'put',
    data: data
  })
}
// 修改原料检测标准
export function cancelQcMaterialTest(data) {
  return request({
    url: '/qc/qcMaterialTest/cancel',
    method: 'put',
    data: data
  })
}

// 删除原料检测标准
export function delQcMaterialTest(id) {
  return request({
    url: '/qc/qcMaterialTest/' + id,
    method: 'delete'
  })
}

// 导出原料检测标准
export function exportQcMaterialTest(query) {
  return request({
    url: '/qc/qcMaterialTest/export',
    method: 'get',
    params: query
  })
}
// 导出原料检测标准
export function importQcMaterialTest(query) {
  return request({
    url: '/qc/qcMaterialTest/importInitData',
    method: 'get',
    params: query
  })
}

export function materialTestAll(query) {
  return request({
    url: '/qc/qcMaterialTest/all',
    method: 'get',
    params: query
  })
}

export function exportMaterialTest(id) {
  return request({
    url: '/qc/qcMaterialTest/exportMaterialTest/' + id,
    method: 'get'
  })
}

export function batchExportQcMaterialTest(query) {
  return request({
    url: '/qc/qcMaterialTest/batchExport',
    method: 'get',
    params: query
  })
}

export function getQcMaterialTestByCode(code) {
  return request({
    url: '/qc/qcMaterialTest/getByCode/' + code,
    method: 'get',
  })
}

export function asyncErpCodeMaterialTest() {
  return request({
    url: '/qc/qcMaterialTest/asyncErpCode',
    method: 'get',
  })
}

export function submitMaterialAudit(data) {
  return request({
    url: '/qc/qcMaterialTest/submitMaterialAudit',
    method: 'put',
    data: data
  })
}

export function cancelMaterialAudit(data) {
  return request({
    url: '/qc/qcMaterialTest/cancelMaterialAudit',
    method: 'put',
    data: data
  })
}

export function exportAllMaterialTest(query) {
  return request({
    url: '/qc/qcMaterialTest/exportAll',
    method: 'get',
    params: query,
  })
}
