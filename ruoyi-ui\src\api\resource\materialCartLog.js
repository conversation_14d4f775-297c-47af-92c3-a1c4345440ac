import request from '@/utils/request'

// 查询包材推荐记录列表
export function listMaterialCartLog(query) {
  return request({
    url: '/resource/materialCartLog/list',
    method: 'get',
    params: query
  })
}

// 查询包材推荐记录详细
export function getMaterialCartLog(id) {
  return request({
    url: '/resource/materialCartLog/' + id,
    method: 'get'
  })
}

// 新增包材推荐记录
export function addMaterialCartLog(data) {
  return request({
    url: '/resource/materialCartLog',
    method: 'post',
    data: data
  })
}

// 修改包材推荐记录
export function updateMaterialCartLog(data) {
  return request({
    url: '/resource/materialCartLog',
    method: 'put',
    data: data
  })
}

// 删除包材推荐记录
export function delMaterialCartLog(id) {
  return request({
    url: '/resource/materialCartLog/' + id,
    method: 'delete'
  })
}

// 导出包材推荐记录
export function exportMaterialCartLog(query) {
  return request({
    url: '/resource/materialCartLog/export',
    method: 'get',
    params: query
  })
}