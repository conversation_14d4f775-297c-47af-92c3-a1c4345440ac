import request from '@/utils/request'

// 查询办公用品列表
export function listOffice(query) {
  return request({
    url: '/resource/office/list',
    method: 'get',
    params: query
  })
}

export function listIncludeGoodsOffice(query) {
  return request({
    url: '/resource/office/listIncludeGoods',
    method: 'get',
    params: query
  })
}

// 查询办公用品详细
export function getOffice(id) {
  return request({
    url: '/resource/office/' + id,
    method: 'get'
  })
}

// 新增办公用品
export function addOffice(data) {
  return request({
    url: '/resource/office',
    method: 'post',
    data: data
  })
}

// 修改办公用品
export function updateOffice(data) {
  return request({
    url: '/resource/office',
    method: 'put',
    data: data
  })
}

// 删除办公用品
export function delOffice(id) {
  return request({
    url: '/resource/office/' + id,
    method: 'delete'
  })
}

// 导出办公用品
export function exportOffice(query) {
  return request({
    url: '/resource/office/export',
    method: 'get',
    params: query
  })
}

export function allOffice(query) {
  return request({
    url: '/resource/office/all',
    method: 'get',
    params: query
  })
}
