import request from '@/utils/request'

// 查询订单异常列表
export function listException(query) {
  return request({
    url: '/order/exception/list',
    method: 'get',
    params: query
  })
}

// 查询订单异常详细
export function getException(id) {
  return request({
    url: '/order/exception/' + id,
    method: 'get'
  })
}

// 新增订单异常
export function addException(data) {
  return request({
    url: '/order/exception',
    method: 'post',
    data: data
  })
}

// 修改订单异常
export function updateException(data) {
  return request({
    url: '/order/exception',
    method: 'put',
    data: data
  })
}

// 删除订单异常
export function delException(id) {
  return request({
    url: '/order/exception/' + id,
    method: 'delete'
  })
}

// 导出订单异常
export function exportException(query) {
  return request({
    url: '/order/exception/export',
    method: 'get',
    params: query
  })
}

export function billingListException(query) {
  return request({
    url: '/order/exception/billingList',
    method: 'get',
    params: query
  })
}

export function addBillingRevoke(data) {
  return request({
    url: '/order/exception/addBillingRevoke',
    method: 'post',
    data: data
  })
}

export function editBillingRevoke(data) {
  return request({
    url: '/order/exception/editBillingRevoke',
    method: 'put',
    data: data
  })
}

export function billingExceptionRevoke(data) {
  return request({
    url: '/order/exception/billingExceptionRevoke',
    method: 'put',
    data: data
  })
}

export function billingExceptionNotPass(data) {
  return request({
    url: '/order/exception/billingExceptionNotPass',
    method: 'put',
    data: data
  })
}

export function finishBillingRevoke(data) {
  return request({
    url: '/order/exception/finishBillingRevoke',
    method: 'put',
    data: data
  })
}

export function finishBillingRedRevoke(data) {
  return request({
    url: '/order/billingRevoke/finishBillingRevoke',
    method: 'put',
    data: data
  })
}

// 查询订单异常列表
export function allCalcGoods(query) {
  return request({
    url: '/order/goods/allCalcGoods',
    method: 'get',
    params: query
  })
}

// 查询订单异常列表
export function allOtherGoodsList(query) {
  return request({
    url: '/order/goods/allOtherGoodsList',
    method: 'get',
    params: query
  })
}

// 查询申请历史记录数据
export function allOrderExceptionList(query) {
  return request({
    url: '/order/exception/allOrderExceptionList',
    method: 'get',
    params: query
  })
}

// 查询订单历史运费相关
export function allOrderFreightCalcList(query) {
  return request({
    url: '/order/exception/allOrderFreightCalcList',
    method: 'get',
    params: query
  })
}

// 查询订单历史运费相关
export function allOrderFreightList(query) {
  return request({
    url: '/order/exception/allOrderFreightList',
    method: 'get',
    params: query
  })
}

// 撤销子项目订单
export function revokeException(data) {
  return request({
    url: '/order/exception/revoke',
    method: 'post',
    data: data
  })
}

// 重启子项目订单
export function reloadException(data) {
  return request({
    url: '/order/exception/reloadException',
    method: 'post',
    data: data
  })
}
