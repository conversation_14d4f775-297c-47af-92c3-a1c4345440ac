import request from '@/utils/request'

// 查询子项目订单列表
export function listProjectItemOrder(query) {
  return request({
    url: '/project/projectItemOrder/list',
    method: 'get',
    params: query
  })
}

// 查询子项目订单列表（报价使用）
export function listProjectItemOrderBySamplePrice(query) {
  return request({
    url: '/project/projectItemOrder/samplePriceList',
    method: 'get',
    params: query
  })
}
// 查询子项目订单列表
export function releasListProjectItemOrder(query) {
  return request({
    url: '/project/projectItemOrder/releasList',
    method: 'get',
    params: query
  })
}

//报价申请列表 查看利润表数据
export function queryProjectItemOrderOfferDataList(query) {
  return request({
    url: '/project/projectItemOrder/queryProjectItemOrderOfferDataList',
    method: 'get',
    params: query
  })
}

// 查询子项目订单详细
export function getProjectItemOrder(id) {
  return request({
    url: '/project/projectItemOrder/' + id,
    method: 'get'
  })
}

//查询报价包装信息
export function getPackagingMaterialDatas(id) {
  return request({
    url: '/project/projectItemOrder/getPackagingMaterialDatas/' + id,
    method: 'get'
  })
}

//多商品 查询报价包装信息
export function getMultiPackagingMaterialDatas(query) {
  return request({
    url: '/project/projectItemOrder/getMultiPackagingMaterialDatas',
    method: 'get',
    params: query
  })
}


// 查询子项目订单详细
export function queryProjectOfferFormulaDataList(id) {
  return request({
    url: '/project/projectItemOrder/queryProjectOfferFormulaDataList/' + id,
    method: 'get'
  })
}
// 查询子项目订单详细
export function queryProjectOfferFormulaDetailsDataList(id) {
  return request({
    url: '/project/projectItemOrder/queryProjectOfferFormulaDataDetailsList/' + id,
    method: 'get'
  })
}
// 查询多名称子项目订单详细
export function queryMultiProjectOfferFormulaDetailsDataList(query) {
  return request({
    url: '/project/projectItemOrder/queryMultiProjectOfferFormulaDetailsDataList',
    method: 'get',
    params: query
  })
}
// 查询订单价格变更记录
export function queryProjectOfferChangeRecordDataList(id) {
  return request({
    url: '/project/projectItemOrder/queryProjectOfferChangeRecordDataList/' + id,
    method: 'get'
  })
}
// 查询多商品 订单价格变更记录
export function queryMultiProjectOfferChangeRecordDataList(query) {
  return request({
    url: '/project/projectItemOrder/queryMultiProjectOfferChangeRecordDataList',
    method: 'get',
    params: query
  })
}
// 查询多商品 订单价格变更记录
export function processPackagingMaterialTypePriceInfo(data) {
  return request({
    url: '/project/projectItemOrder/processPackagingMaterialTypePriceInfo',
    method: 'post',
    data: data
  })
}

//查询订单报价包材相关信息
export function queryProjectOfferPackagingMaterialDataList(id) {
  return request({
    url: '/project/projectItemOrder/queryProjectOfferPackagingMaterialDataList/' + id,
    method: 'get'
  })
}

// 新增子项目订单
export function addProjectItemOrder(data) {
  return request({
    url: '/project/projectItemOrder',
    method: 'post',
    data: data
  })
}

// 修改子项目订单
export function updateProjectItemOrder(data) {
  return request({
    url: '/project/projectItemOrder',
    method: 'put',
    data: data
  })
}

//刷新订单价格
export function refreshProjectItemOrderOffer(data) {
  return request({
    url: '/project/projectItemOrder/refreshProjectItemOrderOffer',
    method: 'put',
    data: data
  })
}


//保存价格
export function saveProjectItemOrderOffer(data) {
  return request({
    url: '/project/projectItemOrder/saveProjectItemOrderOffer',
    method: 'put',
    data: data
  })
}

//保存包装信息记录
export function savePackagingMaterialDatas(data) {
  return request({
    url: '/project/projectItemOrder/savePackagingMaterialDatas',
    method: 'put',
    data: data
  })
}

// 删除子项目订单
export function delProjectItemOrder(id) {
  return request({
    url: '/project/projectItemOrder/' + id,
    method: 'delete'
  })
}

// 撤销子项目订单
export function revokeProjectItemOrder(data) {
  return request({
    url: '/project/projectItemOrder/revoke',
    method: 'post',
    data: data
  })
}

// 导出子项目订单
export function exportProjectItemOrder(query) {
  return request({
    url: '/project/projectItemOrder/export',
    method: 'get',
    params: query
  })
}

// 导出子项目订单
export function exportProjectItemOrderList(query) {
  return request({
    url: '/project/projectItemOrder/exportOrderList',
    method: 'get',
    params: query
  })
}

// 导出三级内容物(新)
export function exportNrwItem(query) {
  return request({
    url: '/project/projectItemOrder/exportNrw',
    method: 'get',
    params: query
  })
}
// 导出三级内容物(新)
export function exportNrwExcel(query) {
  return request({
    url: '/project/projectItemOrder/exportNrwExcel',
    method: 'get',
    params: query
  })
}

// 批量导出多个项目的内容物信息到多个Sheet页
export function exportMultipleNrw(data) {
  return request({
    url: '/project/projectItemOrder/exportMultipleNrw',
    method: 'post',
    data: data
  })
}

// 导出报价预览信息
export function exportProjectOffer(data) {
  return request({
    url: '/project/projectItemOrder/exportProjectOffer',
    method: 'post',
    data: data
  })
}

// 导出子项目订单
export function exportProjectItemOrderNrwArrangement(query) {
  return request({
    url: '/project/projectItemOrder/exportNrwArrangement',
    method: 'get',
    params: query
  })
}

export function projectItemOrderAll(query) {
  return request({
    url: '/project/projectItemOrder/all',
    method: 'get',
    params: query
  })
}

export function projectItemOrderFeedbackAll(query) {
  return request({
    url: '/project/projectItemOrderFeedback/all',
    method: 'get',
    params: query
  })
}

export function projectItemOrderConfirmCodeByItemId(query) {
  return request({
    url: '/project/projectItemOrder/projectItemOrderConfirmCodeByItemId',
    method: 'get',
    params: query
  })
}


export function projectItemOfferDataList(query) {
  return request({
    url: '/project/projectItemOrder/projectItemOfferDataList',
    method: 'get',
    params: query
  })
}

export function projectItemOfferDataListNew(query) {
  return request({
    url: '/project/projectItemOrder/projectItemOfferDataListNew',
    method: 'get',
    params: query
  })
}

export function projectItemOfferJgfDataList(query) {
  return request({
    url: '/project/projectItemOrder/projectItemOfferJgfDataList',
    method: 'get',
    params: query
  })
}

export function projectItemOfferBcDataList(query) {
  return request({
    url: '/project/projectItemOrder/projectItemOfferBcDataList',
    method: 'get',
    params: query
  })
}

export function projectItemOfferDataListFinidshNew(query) {
  return request({
    url: '/project/projectItemOrder/projectItemOfferDataListFinidshNew',
    method: 'get',
    params: query
  })
}


export function projectItemOrderBcBomDataList(query) {
  return request({
    url: '/project/projectItemOrder/projectItemOrderBcBomDataList',
    method: 'get',
    params: query
  })
}

//工位图
export function projectItemOrderSckxxGwtDataList(query) {
  return request({
    url: '/project/projectItemOrder/projectItemOrderSckxxGwtDataList',
    method: 'get',
    params: query
  })
}



export function projectItemBomDataList(query) {
  return request({
    url: '/project/projectItemOrder/projectItemBomDataList',
    method: 'get',
    params: query
  })
}
export function prjectItemOrderOfferNrwPriceInfo(query) {
  return request({
    url: '/project/projectItemOrder/prjectItemOrderOfferNrwPriceInfo',
    method: 'get',
    params: query
  })
}

export function prjectItemOrderLegalSubmissionData(query) {
  return request({
    url: '/project/projectItemOrder/prjectItemOrderLegalSubmissionData',
    method: 'get',
    params: query
  })
}

export function queryFormulaProductName(query) {
  return request({
    url: '/project/projectItemOrder/queryFormulaProductName',
    method: 'get',
    params: query
  })
}

export function projectItemOrderInfoBySysbm(query) {
  return request({
    url: '/project/projectItemOrder/projectItemOrderInfoBySysbm',
    method: 'get',
    params: query
  })
}

export function shipmentsOrder(data) {
  return request({
    url: '/project/projectItemOrder/getShipmentsOrder',
    method: 'post',
    data: data
  })
}

export function updateProjectItemOrderInfo(data) {
  return request({
    url: '/project/projectItemOrder/editInfo',
    method: 'put',
    data: data
  })
}

export function getBaOrderByLabCode(labCode) {
  return request({
    url: '/project/projectItemOrder/baOrderByLabCode/' + labCode,
    method: 'get'
  })
}

export function baseAllProjectItemOrder(query) {
  return request({
    url: '/project/projectItemOrder/baseAll',
    method: 'get',
    params: query
  })
}

export function allLabCode(query) {
  return request({
    url: '/project/projectItemOrder/allLabCode',
    method: 'get',
    params: query
  })
}

// 查询子项目订单列表
export function listProjectItemOrderFee(query) {
  return request({
    url: '/project/projectItemOrder/feeList',
    method: 'get',
    params: query
  })
}

export function saveBcReply(data) {
  return request({
    url: '/project/projectItemOrder/saveBcReply',
    method: 'put',
    data: data
  })
}
export function saveGongyiReply(data) {
  return request({
    url: '/project/projectItemOrder/saveGongyiReply',
    method: 'put',
    data: data
  })
}

export function bcOrderListProjectItemOrder(query) {
  return request({
    url: '/project/projectItemOrder/bcOrderList',
    method: 'get',
    params: query
  })
}

export function sopOrderListProjectItemOrder(query) {
  return request({
    url: '/project/projectItemOrder/sopOrderList',
    method: 'get',
    params: query
  })
}

export function allConfirmCodeListProjectItemOrder(projectId) {
  return request({
    url: '/project/projectItemOrder/allConfirmCodeOrderList/' + projectId,
    method: 'get',
  })
}

// 更新项目订单样品报价
export function updateProjectItemOrderSamplePrice(projectItemOrderId, samplePrice) {
  return request({
    url: '/project/projectItemOrder/updateSamplePrice',
    method: 'put',
    params: {
      projectItemOrderId: projectItemOrderId,
      samplePrice: samplePrice
    }
  })
}


//导入排单操作
export function importSchedulingOpr(data) {
  return request({
    url: '/project/projectItemOrder/importScheduling',
    method: 'post',
    data: data
  })
}
