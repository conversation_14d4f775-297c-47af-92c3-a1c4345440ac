import request from '@/utils/request'

// 查询劳务员工信息列表
export function listLaborCompaniesUser(query) {
  return request({
    url: '/hr/laborCompaniesUser/list',
    method: 'get',
    params: query
  })
}

// 查询劳务员工信息详细
export function getLaborCompaniesUser(id) {
  return request({
    url: '/hr/laborCompaniesUser/' + id,
    method: 'get'
  })
}

// 新增劳务员工信息
export function addLaborCompaniesUser(data) {
  return request({
    url: '/hr/laborCompaniesUser',
    method: 'post',
    data: data
  })
}

// 修改劳务员工信息
export function updateLaborCompaniesUser(data) {
  return request({
    url: '/hr/laborCompaniesUser',
    method: 'put',
    data: data
  })
}
export function ddUserIdLaborCompaniesUser(data) {
  return request({
    url: '/hr/laborCompaniesUser/ddUserId',
    method: 'post',
    data: data
  })
}
// 删除劳务员工信息
export function delLaborCompaniesUser(id) {
  return request({
    url: '/hr/laborCompaniesUser/' + id,
    method: 'delete'
  })
}

// 导出劳务员工信息
export function exportLaborCompaniesUser(query) {
  return request({
    url: '/hr/laborCompaniesUser/export',
    method: 'get',
    params: query
  })
}

export function allLaborCompaniesUser(query) {
  return request({
    url: '/hr/laborCompaniesUser/all',
    method: 'get',
    params: query
  })
}


export function printLableCompaniesUser(data) {
  return request({
    url: '/hr/laborCompaniesUser/printLable',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
