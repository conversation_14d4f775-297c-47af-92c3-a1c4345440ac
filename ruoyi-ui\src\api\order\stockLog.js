import request from '@/utils/request'

// 查询成品订单商品库存记录列表
export function listStockLog(query) {
  return request({
    url: '/order/stockLog/list',
    method: 'get',
    params: query
  })
}

// 查询成品订单商品库存记录详细
export function getStockLog(id) {
  return request({
    url: '/order/stockLog/' + id,
    method: 'get'
  })
}

// 新增成品订单商品库存记录
export function addStockLog(data) {
  return request({
    url: '/order/stockLog',
    method: 'post',
    data: data
  })
}

// 修改成品订单商品库存记录
export function updateStockLog(data) {
  return request({
    url: '/order/stockLog',
    method: 'put',
    data: data
  })
}

// 删除成品订单商品库存记录
export function delStockLog(id) {
  return request({
    url: '/order/stockLog/' + id,
    method: 'delete'
  })
}

// 导出成品订单商品库存记录
export function exportStockLog(query) {
  return request({
    url: '/order/stockLog/export',
    method: 'get',
    params: query
  })
}

export function allStockLog(query) {
  return request({
    url: '/order/stockLog/all',
    method: 'get',
    params: query
  })
}

export function allStockRefundLog(query) {
  return request({
    url: '/order/stockLog/refundAll',
    method: 'get',
    params: query
  })
}

export function ckAllStockLog(query) {
  return request({
    url: '/order/stockLog/ckAll',
    method: 'get',
    params: query
  })
}

/**
 * 查询所有未对账出库记录明细,包括在途
 */
export function unDzCkAllStockLog(query) {
  return request({
    url: '/order/stockLog/unDzCkAll',
    method: 'get',
    params: query
  })
}

export function undoCkStockLog(data) {
  return request({
    url: '/order/stockLog/undoCk',
    method: 'put',
    data: data
  })
}
