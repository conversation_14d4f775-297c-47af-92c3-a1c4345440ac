import request from '@/utils/request'

// 查询购物车推荐清单列表
export function listExhibitsCartItem(query) {
  return request({
    url: '/resource/exhibitsCartItem/list',
    method: 'get',
    params: query
  })
}

// 查询购物车推荐清单详细
export function getExhibitsCartItem(id) {
  return request({
    url: '/resource/exhibitsCartItem/' + id,
    method: 'get'
  })
}

// 新增购物车推荐清单
export function addExhibitsCartItem(data) {
  return request({
    url: '/resource/exhibitsCartItem',
    method: 'post',
    data: data
  })
}

// 修改购物车推荐清单
export function updateExhibitsCartItem(data) {
  return request({
    url: '/resource/exhibitsCartItem',
    method: 'put',
    data: data
  })
}

// 删除购物车推荐清单
export function delExhibitsCartItem(id) {
  return request({
    url: '/resource/exhibitsCartItem/' + id,
    method: 'delete'
  })
}

// 导出购物车推荐清单
export function exportExhibitsCartItem(query) {
  return request({
    url: '/resource/exhibitsCartItem/export',
    method: 'get',
    params: query
  })
}

export function allExhibitsCartItem(query) {
  return request({
    url: '/resource/exhibitsCartItem/all',
    method: 'get',
    params: query
  })
}
