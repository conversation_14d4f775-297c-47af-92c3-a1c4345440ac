import request from '@/utils/request'

// 查询礼盒备案列表
export function listBoxIcp(query) {
  return request({
    url: '/legal/boxIcp/list',
    method: 'get',
    params: query
  })
}

// 查询礼盒备案详细
export function getBoxIcp(id) {
  return request({
    url: '/legal/boxIcp/' + id,
    method: 'get'
  })
}

// 新增礼盒备案
export function addBoxIcp(data) {
  return request({
    url: '/legal/boxIcp',
    method: 'post',
    data: data
  })
}

// 修改礼盒备案
export function updateBoxIcp(data) {
  return request({
    url: '/legal/boxIcp',
    method: 'put',
    data: data
  })
}

// 删除礼盒备案
export function delBoxIcp(id) {
  return request({
    url: '/legal/boxIcp/' + id,
    method: 'delete'
  })
}

// 导出礼盒备案
export function exportBoxIcp(query) {
  return request({
    url: '/legal/boxIcp/export',
    method: 'get',
    params: query
  })
}

export function allBoxIcp(query) {
  return request({
    url: '/legal/boxIcp/all',
    method: 'get',
    params: query
  })
}

//提交审核
export function submitAudit(data) {
  return request({
    url: '/legal/boxIcp/submitAudit',
    method: 'put',
    data: data
  })
}

//撤销申请
export function cancelAudit(data) {
  return request({
    url: '/legal/boxIcp/cancelAudit',
    method: 'put',
    data: data
  })
}

//提交变更审核
export function submitChangeAudit(data) {
  return request({
    url: '/legal/boxIcp/submitChangeAudit',
    method: 'put',
    data: data
  })
}

//撤销变更申请
export function cancelChangeAudit(data) {
  return request({
    url: '/legal/boxIcp/cancelChangeAudit',
    method: 'put',
    data: data
  })
}
