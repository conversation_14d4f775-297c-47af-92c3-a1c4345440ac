import request from '@/utils/request'

// 查询订单商品成本记录列表
export function listOrderGoodsCost(query) {
  return request({
    url: '/order/orderGoodsCost/list',
    method: 'get',
    params: query
  })
}


// 查询订单生产
export function queryOrderProductionDetailData(query) {
  return request({
    url: '/order/orderGoodsCost/queryOrderProductionDetailData',
    method: 'get',
    params: query
  })
}

// 查询接单订单商品成本记录列表
export function listOrderGoodsCostDataList(query) {
  return request({
    url: '/order/orderGoodsCost/orderCostList',
    method: 'get',
    params: query
  })
}

// 查询订单商品成本记录详细
export function getOrderGoodsCost(id) {
  return request({
    url: '/order/orderGoodsCost/' + id,
    method: 'get'
  })
}

// 新增订单商品成本记录
export function addOrderGoodsCost(data) {
  return request({
    url: '/order/orderGoodsCost',
    method: 'post',
    data: data
  })
}

// 修改订单商品成本记录
export function updateOrderGoodsCost(data) {
  return request({
    url: '/order/orderGoodsCost',
    method: 'put',
    data: data
  })
}

// 删除订单商品成本记录
export function delOrderGoodsCost(id) {
  return request({
    url: '/order/orderGoodsCost/' + id,
    method: 'delete'
  })
}

// 导出订单商品成本记录
export function exportOrderGoodsCost(query) {
  return request({
    url: '/order/orderGoodsCost/export',
    method: 'get',
    params: query
  })
}
