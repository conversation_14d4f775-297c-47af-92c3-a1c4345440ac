import request from '@/utils/request'

// 查询产品类别列表
export function listCategory(query) {
  return request({
    url: '/product/category/list',
    method: 'get',
    params: query
  })
}

// 查询产品类别详细
export function getCategory(categoryId) {
  return request({
    url: '/product/category/' + categoryId,
    method: 'get'
  })
}

// 新增产品类别
export function addCategory(data) {
  return request({
    url: '/product/category',
    method: 'post',
    data: data
  })
}

// 修改产品类别
export function updateCategory(data) {
  return request({
    url: '/product/category',
    method: 'put',
    data: data
  })
}

// 删除产品类别
export function delCategory(categoryId) {
  return request({
    url: '/product/category/' + categoryId,
    method: 'delete'
  })
}

// 导出产品类别
export function exportCategory(query) {
  return request({
    url: '/product/category/export',
    method: 'get',
    params: query
  })
}

const toTree = (list, parentId) => {
  let o = {}
  list.forEach(item => {
    item.children = []
    o[item.categoryId] = item
  })
  return list.filter(item => {
    if (item.parentId !== parentId) {
      o[item.parentId].children.push(item)
      return false
    }
    return true
  })
}

const toCategoryTree = (list, parentId) => {
  return list.filter(item => {
    if (item.parentId === parentId) {
      let children = toCategoryTree(list, item.categoryId)
      if(children && children.length > 0) {
        item.children = children
      }
      return true
    }
    return false
  })
}

export function getCategoryTree(res) {
  let list = res
  toCategoryTree(list, 0)
  list = list.filter(i=> i.parentId === 0)
  return list
}

export function categoryAll() {
  return request({
    url: '/product/category/all',
    method: 'get'
  })
}
