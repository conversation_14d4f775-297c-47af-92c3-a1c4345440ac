import request from '@/utils/request'

// 查询项目包材选择记录列表
export function listBcLog(query) {
  return request({
    url: '/project/bcLog/list',
    method: 'get',
    params: query
  })
}

// 查询项目包材选择记录详细
export function getBcLog(id) {
  return request({
    url: '/project/bcLog/' + id,
    method: 'get'
  })
}

// 新增项目包材选择记录
export function addBcLog(data) {
  return request({
    url: '/project/bcLog',
    method: 'post',
    data: data
  })
}

// 修改项目包材选择记录
export function updateBcLog(data) {
  return request({
    url: '/project/bcLog',
    method: 'put',
    data: data
  })
}

// 导出项目包材选择记录
export function exportBcLog(query) {
  return request({
    url: '/project/bcLog/export',
    method: 'get',
    params: query
  })
}

export function allBcLog(query) {
  return request({
    url: '/project/bcLog/all',
    method: 'get',
    params: query
  })
}
