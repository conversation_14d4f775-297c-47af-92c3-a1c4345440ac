import request from '@/utils/request'

// 查询称量日计划列表
export function listWeightDay(query) {
  return request({
    url: '/production/weightDay/list',
    method: 'get',
    params: query
  })
}

// 查询称量日计划详细
export function getWeightDay(id) {
  return request({
    url: '/production/weightDay/' + id,
    method: 'get'
  })
}

export function weightDaySubmitAudit(data) {
  return request({
    url: '/production/weightDay/submitAudit',
    method: 'put',
    data,
  })
}

export function weightDayCancelAudit(data) {
  return request({
    url: '/production/weightDay/cancelAudit',
    method: 'put',
    data,
  })
}

export function weightDaySubmitChangeAudit(data) {
  return request({
    url: '/production/weightDay/submitChangeAudit',
    method: 'put',
    data,
  })
}

export function weightDayCancelChangeAudit(data) {
  return request({
    url: '/production/weightDay/cancelChangeAudit',
    method: 'put',
    data,
  })
}

export function updateWeightDay(data) {
  return request({
    url: '/production/weightDay',
    method: 'put',
    data,
  })
}

export function finishWeightDay(data) {
  return request({
    url: '/production/weightDay/finish',
    method: 'put',
    data,
  })
}

export function asyncWeightDayHours(data) {
  return request({
    url: '/production/weightDay/asyncHours',
    method: 'put',
    data,
  })
}
