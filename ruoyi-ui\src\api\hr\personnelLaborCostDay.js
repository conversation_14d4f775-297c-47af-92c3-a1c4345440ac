import request from '@/utils/request'

// 查询每日公司成本数据-日结工列表
export function listPersonnelLaborCostDay(query) {
  return request({
    url: '/hr/personnelLaborCostDay/list',
    method: 'get',
    params: query
  })
}

// 查询每日公司成本数据-日结工详细
export function getPersonnelLaborCostDay(id) {
  return request({
    url: '/hr/personnelLaborCostDay/' + id,
    method: 'get'
  })
}

// 新增每日公司成本数据-日结工
export function addPersonnelLaborCostDay(data) {
  return request({
    url: '/hr/personnelLaborCostDay',
    method: 'post',
    data: data
  })
}

// 修改每日公司成本数据-日结工
export function updatePersonnelLaborCostDay(data) {
  return request({
    url: '/hr/personnelLaborCostDay',
    method: 'put',
    data: data
  })
}

// 删除每日公司成本数据-日结工
export function delPersonnelLaborCostDay(id) {
  return request({
    url: '/hr/personnelLaborCostDay/' + id,
    method: 'delete'
  })
}

// 导出每日公司成本数据-日结工
export function exportPersonnelLaborCostDay(query) {
  return request({
    url: '/hr/personnelLaborCostDay/export',
    method: 'get',
    params: query
  })
}