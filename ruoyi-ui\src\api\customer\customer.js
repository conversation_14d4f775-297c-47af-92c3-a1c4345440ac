import request from '@/utils/request'

export function listCustomerByProject(query) {
  return request({
    url: '/customer/customer/listByProject',
    method: 'get',
    params: query
  })
}

// 查询客户 列表
export function listCustomer(query) {
  return request({
    url: '/customer/customer/list',
    method: 'get',
    params: query
  })
}

// 查询客户 列表
export function listCustomerAll(query) {
  return request({
    url: '/customer/customer/listAll',
    method: 'get',
    params: query
  })
}

// 查询用户列表
export function listAllUser(query) {
  return request({
    url: '/customer/customer/queryCustomerAllUser',
    method: 'get',
    params: query
  })
}

// 查询用户列表
export function listAllOrderUser(query) {
  return request({
    url: '/customer/customer/queryCustomerAllOrderUser',
    method: 'get',
    params: query
  })
}

// 查询客户品牌 列表
export function listCustomerBrand(query) {
  return request({
    url: '/customer/brand/listAll',
    method: 'get',
    params: query
  })
}

// 新增客户
export function saveCustomerAssist(data) {
  return request({
    url: '/customer/assist/addAssists',
    method: 'post',
    data: data
  })
}

// 新增客户
export function addOrderCustomerAssists(data) {
  return request({
    url: '/customer/assist/addOrderCustomerAssists',
    method: 'post',
    data: data
  })
}


// 删除客户联系人
export function delAssistInfo(data) {
  return request({
    url: '/customer/assist/delAssistInfo',
    method: 'post',
    data: data
  })
}

// 设置为业务担当
export function setAssistInfo(data) {
  return request({
    url: '/customer/assist/setAssistInfo',
    method: 'post',
    data: data
  })
}


// 查询客户 详细
export function getCustomer(id) {
  return request({
    url: '/customer/customer/' + id,
    method: 'get'
  })
}

// 新增客户
export function addCustomer(data) {
  return request({
    url: '/customer/customer',
    method: 'post',
    data: data
  })
}

// 修改客户
export function updateCustomer(data) {
  return request({
    url: '/customer/customer',
    method: 'put',
    data: data
  })
}

// 删除客户
export function delCustomer(id) {
  return request({
    url: '/customer/customer/' + id,
    method: 'delete'
  })
}

// 导出客户
export function exportCustomer(query) {
  return request({
    url: '/customer/customer/export',
    method: 'get',
    params: query
  })
}

/**
 * 查询自己身份下的所有客户
 */
export function customerAll(query) {
  return request({
    url: '/customer/customer/all',
    method: 'get',
    params: query
  })
}

/**
 * 不带身份验证获取所有客户
 */
export function customerBaseAll(query) {
  return request({
    url: '/customer/customer/baseAll',
    method: 'get',
    params: query
  })
}

// 查询跟单所有客户
export function customerOrderFollowBaseAll(query) {
  return request({
    url: '/customer/customer/orderCustomerAll',
    method: 'get',
    params: query
  })
}

// 查询生产企业
export function customerAllScqy() {
  return request({
    url: '/customer/customer/allScqy',
    method: 'get'
  })
}

// 用户状态修改
export function changeCustomerAssistStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/customer/customer/changeCustomerAssistStatus',
    method: 'put',
    data: data
  })
}

// 跟单用户状态修改
export function changeOrderCustomerAssistStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/customer/customer/changeOrderCustomerAssistStatus',
    method: 'put',
    data: data
  })
}

export function importTemplate() {
  return request({
    url: '/customer/customer/importTemplate',
    method: 'get'
  })
}

export function getMyCustomerProjectCount(query) {
  return request({
    url: '/customer/customer/getMyCustomerProjectCount',
    method: 'get',
    params: query
  })
}

export function groupByLevelCustomer(query) {
  return request({
    url: '/customer/customer/groupByLevel',
    method: 'get',
    params: query
  })
}

export function groupByYwCustomer(query) {
  return request({
    url: '/customer/customer/groupByYw',
    method: 'get',
    params: query
  })
}

export function groupNewByYwCustomer(query) {
  return request({
    url: '/customer/customer/groupNewByYw',
    method: 'get',
    params: query
  })
}


export function groupByYwLevelCustomer(query) {
  return request({
    url: '/customer/customer/groupByYwLevel',
    method: 'get',
    params: query
  })
}

export function orderCountAllCustomer(query) {
  return request({
    url: '/customer/customer/orderCountAll',
    method: 'get',
    params: query
  })
}

export function listCustomerByItemTypes(query) {
  return request({
    url: '/customer/customer/listByItemTypes',
    method: 'get',
    params: query
  })
}

export function listCustomerByUserId(query) {
  return request({
    url: '/customer/customer/listByUserId',
    method: 'get',
    params: query
  })
}

export function allCustomerByUserId(query) {
  return request({
    url: '/customer/customer/allByUserId',
    method: 'get',
    params: query
  })
}
