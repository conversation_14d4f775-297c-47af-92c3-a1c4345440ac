import request from '@/utils/request'

// 查询成品库商品列表
export function listResourceFinishedGoods(query) {
  return request({
    url: '/resource/resourceFinishedGoods/list',
    method: 'get',
    params: query
  })
}

// 查询成品库商品详细
export function getResourceFinishedGoods(id) {
  return request({
    url: '/resource/resourceFinishedGoods/' + id,
    method: 'get'
  })
}

// 新增成品库商品
export function addResourceFinishedGoods(data) {
  return request({
    url: '/resource/resourceFinishedGoods',
    method: 'post',
    data: data
  })
}

// 修改成品库商品
export function updateResourceFinishedGoods(data) {
  return request({
    url: '/resource/resourceFinishedGoods',
    method: 'put',
    data: data
  })
}

// 删除成品库商品
export function delResourceFinishedGoods(id) {
  return request({
    url: '/resource/resourceFinishedGoods/' + id,
    method: 'delete'
  })
}

// 导出成品库商品
export function exportResourceFinishedGoods(query) {
  return request({
    url: '/resource/resourceFinishedGoods/export',
    method: 'get',
    params: query
  })
}

// 导出半成品匹配商品
export function exportResourceFinishedBcpGoods(query) {
  return request({
    url: '/resource/resourceFinishedGoods/exportBcp',
    method: 'get',
    params: query
  })
}

export function allResourceFinishedGoods(query) {
  return request({
    url: '/resource/resourceFinishedGoods/all',
    method: 'get',
    params: query
  })
}

export function draftResourceFinishedGoods(query) {
  return request({
    url: '/resource/resourceFinishedGoods/draft',
    method: 'get',
    params: query
  })
}

export function baseAllResourceFinishedGoods(query) {
  return request({
    url: '/resource/resourceFinishedGoods/baseAll',
    method: 'get',
    params: query
  })
}

export function getSimilarListByProductName(query) {
  return request({
    url: '/resource/resourceFinishedGoods/getSimilarListByProductName',
    method: 'get',
    params: query
  })
}
