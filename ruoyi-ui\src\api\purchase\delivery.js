import request from '@/utils/request'

// 查询采购订单交期列表
export function listDelivery(query) {
  return request({
    url: '/purchase/delivery/list',
    method: 'get',
    params: query
  })
}

// 查询采购订单交期详细
export function getDelivery(id) {
  return request({
    url: '/purchase/delivery/' + id,
    method: 'get'
  })
}

// 查询采购订单交期详细
export function relationList(query) {
  return request({
    url: '/purchase/delivery/relationList',
    method: 'get',
    params: query
  })
}

// 新增采购订单交期
export function addDelivery(data) {
  return request({
    url: '/purchase/delivery',
    method: 'post',
    data: data
  })
}

// 修改采购订单交期
export function updateDelivery(data) {
  return request({
    url: '/purchase/delivery',
    method: 'put',
    data: data
  })
}

// 修改采购订单交期
export function purchaseOrderRelationOrderGoodsInfo(data) {
  return request({
    url: '/purchase/delivery/relationOrder',
    method: 'put',
    data: data
  })
}

// 删除采购订单交期
export function delDelivery(id) {
  return request({
    url: '/purchase/delivery/' + id,
    method: 'delete'
  })
}

// 导出采购订单交期
export function exportDelivery(query) {
  return request({
    url: '/purchase/delivery/export',
    method: 'get',
    params: query
  })
}
