import request from '@/utils/request'

// 查询退货单列表
export function listPurtj(query) {
  return request({
    url: '/order/purtj/list',
    method: 'get',
    params: query
  })
}

// 查询退货单详细
export function getPurtj(id) {
  return request({
    url: '/order/purtj/' + id,
    method: 'get'
  })
}

// 新增退货单
export function addPurtj(data) {
  return request({
    url: '/order/purtj',
    method: 'post',
    data: data
  })
}

// 修改退货单
export function updatePurtj(data) {
  return request({
    url: '/order/purtj',
    method: 'put',
    data: data
  })
}

// 删除退货单
export function delPurtj(id) {
  return request({
    url: '/order/purtj/' + id,
    method: 'delete'
  })
}

// 导出退货单
export function exportPurtj(query) {
  return request({
    url: '/order/purtj/export',
    method: 'get',
    params: query
  })
}