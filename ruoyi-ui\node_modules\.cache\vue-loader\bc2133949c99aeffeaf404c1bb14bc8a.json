{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\customer\\contract\\save.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\customer\\contract\\save.vue", "mtime": 1753954679641}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQge2FkZENvbnRyYWN0LCBnZXRDb250cmFjdCwgdXBkYXRlQ29udHJhY3R9IGZyb20gIkAvYXBpL2N1c3RvbWVyL2NvbnRyYWN0IjsNCmltcG9ydCB7Z2V0QWlSZXNvbHZlQ29udHJhY3R9IGZyb20gIkAvYXBpL2NvbW1vbi9haSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogImN1c3RvbWVyQ29udHJhY3RTYXZlIiwNCiAgcHJvcHM6IHsNCiAgICBjdXN0b21lcklkOiB7DQogICAgICB0eXBlOiBOdW1iZXIsDQogICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICB9LA0KICAgIHJlYWRvbmx5OiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UsDQogICAgfSwNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBidG5Mb2FkaW5nOiBmYWxzZSwNCiAgICAgIGZvcm06IHt9LA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgbmFtZTogWw0KICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSxtZXNzYWdlOiAn6K+36Zeu6L6T5YWl5paH5Lu25ZCNJ30sDQogICAgICAgIF0NCiAgICAgIH0sDQogICAgICBkZWxpdmVyeVR5cGVPcHRpb25zOiBbJysnLCctJywnwrEnXSwNCiAgICAgIGZpbGVzOiBbXSwNCiAgICAgIHF1YWxpdHlGaWxlczogW10sDQogICAgICBzaG93RG9jOiBmYWxzZSwNCiAgICAgIGRhdGFBcnJheTogW10sDQogICAgICBvIDogew0KICAgICAgICAicGFydHlfYSI6ICLnlLLmlrnlkI3np7AiLA0KICAgICAgICAicGFydHlfYiI6ICLkuZnmlrnlkI3np7AiLA0KICAgICAgICAic2lnbmVkX2RhdGUiOiAi562+6K6i5pel5pyfIiwNCiAgICAgICAgIm9uX3NldF9kYXRlIjogIui1t+aViOaXpeacnyIsDQogICAgICAgICJleHBpcmVfZGF0ZSI6ICLliLDmnJ/ml6XmnJ8iLA0KICAgICAgICAicGF5bWVudF90ZXJtIjogIui0puacn+aPj+i/sCIsDQogICAgICAgICJmaW5hbmNpYWxfcmlzayI6ICLotKLliqHpo47pmanmj4/ov7AiLA0KICAgICAgICAiZGVsaXZlcnlfY29udHJvbCI6ICLkuqTotKfmlbDph4/mjqfliLbmnaHmrL4iLA0KICAgICAgICAiZGVsYXlfcmlzayI6ICLkuqTotKflu7bor6/po47pmanmnaHmrL4iLA0KICAgICAgICAicXVhbGl0eV9yZXNwb25zaWJpbGl0eSI6ICLotKjph4/otKPku7vljY/orq4iLA0KICAgICAgICAicXVhbGl0eV9yaXNrIjogIui0qOmHj+mjjumZqeaPj+i/sCIsDQogICAgICAgICJwcm9kdWN0aW9uX3Jpc2siOiAi55Sf5Lqn5o2f6ICX5LiO5oiQ5pys6aOO6ZmpIiwNCiAgICAgICAgImlwX3Jpc2siOiAi55+l6K+G5Lqn5p2D5LiO5L+d5a+G6aOO6ZmpIiwNCiAgICAgICAgImJyZWFjaF9saWFiaWxpdHkiOiAi6L+d57qm6LSj5Lu75p2h5qy+IiwNCiAgICAgICAgImRhdGFfcmlzayI6ICLmlbDmja7kuI7orrDlvZXpo47pmakiLA0KICAgICAgICAib3RoZXJfcmlza3MiOiAi5YW25LuW6aOO6Zmp5o+P6L+wIiwNCiAgICAgICAgInBhcnR5X2Jfc3VnZ2VzdGlvbnMiOiAi5LmZ5pa55bu66K6uIiwNCiAgICAgIH0sDQogICAgICBwYXJ0eUJTdWdnZXN0aW9uczogW10sDQogICAgfQ0KICB9LA0KICBhc3luYyBjcmVhdGVkKCkgew0KICB9LA0KICBtZXRob2RzOiB7DQogICAgYXN5bmMgZmlsZUNoYW5nZShmaWxlcykgew0KICAgICAgY29uc29sZS5sb2coZmlsZXMpDQogICAgICAvLyBmb3IgKGNvbnN0IGZpbGUgb2YgZmlsZXMpIHsNCiAgICAgIC8vICAgYXdhaXQgdGhpcy5zZWxlY3RGaWxlKGZpbGUpDQogICAgICAvLyB9DQogICAgfSwNCiAgICBhZGRJdGVtKCkgew0KICAgICAgdGhpcy5wYXJ0eUJTdWdnZXN0aW9ucy5wdXNoKCcnKQ0KICAgIH0sDQogICAgZGVsSXRlbShpKSB7DQogICAgICB0aGlzLnBhcnR5QlN1Z2dlc3Rpb25zLnNwbGljZShpLDEpDQogICAgfSwNCiAgICBhc3luYyBzZWxlY3RGaWxlKGZpbGUpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIGlmKGZpbGUudXJsKSB7DQogICAgICAgIHRoaXMuZGF0YUFycmF5ID0gW10NCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgZ2V0QWlSZXNvbHZlQ29udHJhY3QoZmlsZSkNCiAgICAgICAgaWYocmVzLmNvZGUgPT09IDIwMCApIHsNCiAgICAgICAgICBjb25zdCBkYXRhID0gcmVzLmRhdGENCiAgICAgICAgICBpZihkYXRhKSB7DQogICAgICAgICAgICBjb25zdCBvID0gZGF0YS5leHRyYWN0ZWRfZGF0YQ0KICAgICAgICAgICAgaWYobykgew0KICAgICAgICAgICAgICBmb3IgKGNvbnN0IGsgb2YgT2JqZWN0LmtleXMobykpIHsNCiAgICAgICAgICAgICAgICB0aGlzLmRhdGFBcnJheS5wdXNoKHsNCiAgICAgICAgICAgICAgICAgIGxhYmVsOiB0aGlzLm9ba10sDQogICAgICAgICAgICAgICAgICBrZXk6IGssDQogICAgICAgICAgICAgICAgICB2YWx1ZTogb1trXQ0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHRoaXMuZm9ybS5uYW1lID0gZmlsZS5uYW1lDQogICAgICAgICAgICB0aGlzLmZvcm0ucGFydHlBID0gby5wYXJ0eV9hDQogICAgICAgICAgICB0aGlzLmZvcm0ucGFydHlCID0gby5wYXJ0eV9iDQogICAgICAgICAgICB0aGlzLmZvcm0uc2lnbmVkRGF0ZSA9IG8uc2lnbmVkX2RhdGUNCiAgICAgICAgICAgIHRoaXMuZm9ybS5vblNldERhdGUgPSBvLm9uX3NldF9kYXRlDQogICAgICAgICAgICB0aGlzLmZvcm0uZXhwaXJlRGF0ZSA9IG8uZXhwaXJlX2RhdGUNCiAgICAgICAgICAgIHRoaXMuZm9ybS5wYXltZW50VGVybSA9IG8ucGF5bWVudF90ZXJtDQogICAgICAgICAgICB0aGlzLmZvcm0uZmluYW5jaWFsUmlzayA9IG8uZmluYW5jaWFsX3Jpc2sNCiAgICAgICAgICAgIHRoaXMuZm9ybS5kZWxpdmVyeUNvbnRyb2wgPSBvLmRlbGl2ZXJ5X2NvbnRyb2wNCiAgICAgICAgICAgIHRoaXMuZm9ybS5kZWxheVJpc2sgPSBvLmRlbGF5X3Jpc2sNCiAgICAgICAgICAgIHRoaXMuZm9ybS5xdWFsaXR5UmVzcG9uc2liaWxpdHkgPSBvLnF1YWxpdHlfcmVzcG9uc2liaWxpdHkNCiAgICAgICAgICAgIHRoaXMuZm9ybS5xdWFsaXR5UmlzayA9IG8ucXVhbGl0eV9yaXNrDQogICAgICAgICAgICB0aGlzLmZvcm0ucHJvZHVjdGlvblJpc2sgPSBvLnByb2R1Y3Rpb25fcmlzaw0KICAgICAgICAgICAgdGhpcy5mb3JtLmlwUmlzayA9IG8uaXBfcmlzaw0KICAgICAgICAgICAgdGhpcy5mb3JtLmJyZWFjaExpYWJpbGl0eSA9IG8uYnJlYWNoX2xpYWJpbGl0eQ0KICAgICAgICAgICAgdGhpcy5mb3JtLmRhdGFSaXNrID0gby5kYXRhX3Jpc2sNCiAgICAgICAgICAgIHRoaXMuZm9ybS5vdGhlciA9IG8ub3RoZXJfcmlza3MNCiAgICAgICAgICAgIHRoaXMuZm9ybS50ZXh0ID0gSlNPTi5zdHJpbmdpZnkoZGF0YSkNCiAgICAgICAgICAgIHRoaXMuc2hvd0RvYyA9IHRydWUNCiAgICAgICAgICB9DQogICAgICAgICAgaWYoZGF0YS5hZHZpY2UpIHsNCiAgICAgICAgICAgIHRoaXMucGFydHlCU3VnZ2VzdGlvbnMgPSBkYXRhLmFkdmljZS5wYXJ0eV9iX3N1Z2dlc3Rpb25zDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgIH0sDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy4kcGFyZW50LiRwYXJlbnQub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICBjdXN0b21lcklkOiBudWxsLA0KICAgICAgICBjb2RlOiBudWxsLA0KICAgICAgICBuYW1lOiBudWxsLA0KICAgICAgICBwZXJpb2RUeXBlOiBudWxsLA0KICAgICAgICBwZXJpb2REYXlzOiBudWxsLA0KICAgICAgICBudW1zOiBudWxsLA0KICAgICAgICBkZWxpdmVyeVR5cGU6IG51bGwsDQogICAgICAgIG92ZXJkdWU6IG51bGwsDQogICAgICAgIHF1YWxpdHlSZXNwb25zaWJpbGl0eTogbnVsbCwNCiAgICAgICAgb3RoZXI6IG51bGwsDQogICAgICAgIHNpZ25lZERhdGU6IG51bGwsDQogICAgICAgIG9uU2V0RGF0ZTogbnVsbCwNCiAgICAgICAgZXhwaXJlRGF0ZTogbnVsbCwNCiAgICAgICAgbG9jYXRpb25Db2RlOiBudWxsLA0KICAgICAgICByZW1hcms6IG51bGwsDQogICAgICAgIHBhcnR5QTogbnVsbCwNCiAgICAgICAgcGFydHlCOiBudWxsLA0KICAgICAgICBmaW5hbmNpYWxSaXNrOiBudWxsLA0KICAgICAgICBkZWxpdmVyeUNvbnRyb2w6IG51bGwsDQogICAgICAgIGRlbGF5UmlzazogbnVsbCwNCiAgICAgICAgcXVhbGl0eVJpc2s6IG51bGwsDQogICAgICAgIHByb2R1Y3Rpb25SaXNrOiBudWxsLA0KICAgICAgICBpcFJpc2s6IG51bGwsDQogICAgICAgIGJyZWFjaExpYWJpbGl0eTogbnVsbCwNCiAgICAgICAgZGF0YVJpc2s6IG51bGwsDQogICAgICAgIHRleHQ6IG51bGwsDQogICAgICB9Ow0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKQ0KICAgICAgdGhpcy5maWxlcyA9IFtdDQogICAgICB0aGlzLnF1YWxpdHlGaWxlcyA9IFtdDQogICAgICB0aGlzLmRhdGFBcnJheSA9IFtdDQogICAgICB0aGlzLnBhcnR5QlN1Z2dlc3Rpb25zID0gW10NCiAgICAgIHRoaXMuc2hvd0RvYyA9IHRydWUNCiAgICB9LA0KICAgIGFzeW5jIGluaXQoaWQpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGdldENvbnRyYWN0KGlkKQ0KICAgICAgY29uc3QgZm9ybSA9IHJlcy5kYXRhDQoNCiAgICAgIGlmKGZvcm0uZmlsZXMpIHsNCiAgICAgICAgdGhpcy5maWxlcyA9IEpTT04ucGFyc2UoZm9ybS5maWxlcykNCiAgICAgIH0NCg0KICAgICAgaWYoZm9ybS5xdWFsaXR5RmlsZXMpIHsNCiAgICAgICAgdGhpcy5xdWFsaXR5RmlsZXMgPSBKU09OLnBhcnNlKGZvcm0ucXVhbGl0eUZpbGVzKQ0KICAgICAgfQ0KDQogICAgICBpZihmb3JtLnBhcnR5QlN1Z2dlc3Rpb25zKSB7DQogICAgICAgIHRoaXMucGFydHlCU3VnZ2VzdGlvbnMgPSBKU09OLnBhcnNlKGZvcm0ucGFydHlCU3VnZ2VzdGlvbnMpDQogICAgICB9DQoNCiAgICAgIHRoaXMuZm9ybSA9IGZvcm0NCiAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgfSwNCiAgICBhc3luYyBzdWJtaXRGb3JtKCkgew0KICAgICAgYXdhaXQgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKCkNCiAgICAgIGxldCBmb3JtID0gT2JqZWN0LmFzc2lnbih7fSx0aGlzLmZvcm0pDQoNCiAgICAgIGZvcm0uZmlsZXMgPSBKU09OLnN0cmluZ2lmeSh0aGlzLmZpbGVzKQ0KICAgICAgZm9ybS5xdWFsaXR5RmlsZXMgPSBKU09OLnN0cmluZ2lmeSh0aGlzLnF1YWxpdHlGaWxlcykNCiAgICAgIGZvcm0ucGFydHlCU3VnZ2VzdGlvbnMgPSBKU09OLnN0cmluZ2lmeSh0aGlzLnBhcnR5QlN1Z2dlc3Rpb25zKQ0KDQogICAgICBpZiAoZm9ybS5pZCAhPSBudWxsKSB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gdHJ1ZQ0KICAgICAgICAgIGF3YWl0IHVwZGF0ZUNvbnRyYWN0KGZvcm0pDQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpDQogICAgICAgICAgdGhpcy4kcGFyZW50LiRwYXJlbnQub3BlbiA9IGZhbHNlDQogICAgICAgICAgYXdhaXQgdGhpcy4kcGFyZW50LiRwYXJlbnQuZ2V0TGlzdCgpDQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICBmb3JtLmN1c3RvbWVySWQgPSB0aGlzLmN1c3RvbWVySWQNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlDQogICAgICAgICAgYXdhaXQgYWRkQ29udHJhY3QoZm9ybSkNCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIikNCiAgICAgICAgICB0aGlzLiRwYXJlbnQuJHBhcmVudC5vcGVuID0gZmFsc2U7DQogICAgICAgICAgYXdhaXQgdGhpcy4kcGFyZW50LiRwYXJlbnQuZ2V0TGlzdCgpDQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["save.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8IA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "save.vue", "sourceRoot": "src/views/customer/contract", "sourcesContent": ["<template>\r\n  <div v-loading=\"loading\">\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" size=\"mini\" label-width=\"150px\" >\r\n      <div :class=\"readonly ? 'mask' : ''\" >\r\n        <el-row :gutter=\"20\" >\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"文件名称\" prop=\"name\">\r\n              <el-input v-model=\"form.name\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"甲方\" prop=\"partyA\">\r\n              <el-input v-model=\"form.partyA\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"乙方\" prop=\"partyB\">\r\n              <el-input v-model=\"form.partyB\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"签订日期\" prop=\"signedDate\">\r\n              <el-date-picker\r\n                v-model=\"form.signedDate\"\r\n                clearable\r\n                type=\"date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"起效日期\" prop=\"onSetDate\">\r\n              <el-date-picker\r\n                v-model=\"form.onSetDate\"\r\n                clearable\r\n                type=\"date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"到期日期\" prop=\"expireDate\">\r\n              <el-date-picker\r\n                v-model=\"form.expireDate\"\r\n                clearable\r\n                type=\"date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"账期描述\" prop=\"paymentTerm\">\r\n          <el-input v-model=\"form.paymentTerm\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"财务风险描述\" prop=\"financialRisk\">\r\n          <el-input v-model=\"form.financialRisk\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"交货数量控制条款\" prop=\"deliveryControl\">\r\n          <el-input v-model=\"form.deliveryControl\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"交货延误风险条款\" prop=\"delayRisk\">\r\n          <el-input v-model=\"form.delayRisk\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"质量责任协议\" prop=\"qualityResponsibility\">\r\n          <el-input v-model=\"form.qualityResponsibility\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"质量风险描述\" prop=\"qualityRisk\">\r\n          <el-input v-model=\"form.qualityRisk\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"生产损耗与成本风险\" prop=\"productionRisk\">\r\n          <el-input v-model=\"form.productionRisk\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"知识产权与保密风险\" prop=\"ipRisk\">\r\n          <el-input v-model=\"form.ipRisk\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"违约责任条款\" prop=\"breachLiability\">\r\n          <el-input v-model=\"form.breachLiability\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"数据与记录风险\" prop=\"dataRisk\">\r\n          <el-input v-model=\"form.dataRisk\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"其他风险描述\" prop=\"other\">\r\n          <el-input v-model=\"form.other\" autosize type=\"textarea\" />\r\n        </el-form-item>\r\n      </div>\r\n\r\n      <el-divider content-position=\"left\" >加工合同</el-divider>\r\n\r\n      <FileUpload v-model=\"files\" :readonly=\"readonly\" @change=\"fileChange\" />\r\n\r\n      <el-divider content-position=\"left\" >质量协议</el-divider>\r\n\r\n      <FileUpload v-model=\"qualityFiles\" :readonly=\"readonly\" />\r\n\r\n      <template v-if=\"showDoc\" >\r\n        <el-divider content-position=\"left\" >解析结果 <span style=\"color: #F56C6C\" >解析时间较长,请耐心等待(请尽量上传原件,提高解析识别率,缩短等待时间).所有解析结果需人工重新确认,此处解析只做辅助使用</span> </el-divider>\r\n\r\n        <div class=\"table-wrapper\" >\r\n          <table class=\"base-table small-table\" >\r\n            <tr>\r\n              <th ><i class=\"el-icon-circle-plus-outline\" @click=\"addItem\" /></th>\r\n              <th >乙方建议</th>\r\n            </tr>\r\n            <tr v-for=\"(item,index) in partyBSuggestions\" :key=\"index\" >\r\n              <td><i class=\"el-icon-remove-outline\" @click=\"delItem(index)\" /></td>\r\n              <td>\r\n                <el-input v-model=\"partyBSuggestions[index]\" size=\"mini\" />\r\n              </td>\r\n            </tr>\r\n          </table>\r\n        </div>\r\n<!--        <el-row :gutter=\"20\" >-->\r\n<!--          <el-col :col=\"16\" >-->\r\n<!--            <div class=\"table-wrapper\" >-->\r\n<!--              <table class=\"base-table small-table\" >-->\r\n<!--                <tr>-->\r\n<!--                  <th style=\"width: 120px\" >标签</th>-->\r\n<!--                  <th >解析内容</th>-->\r\n<!--                </tr>-->\r\n<!--                <tr v-for=\"(item,index) in dataArray\" :key=\"index\" >-->\r\n<!--                  <td>{{item.label}}</td>-->\r\n<!--                  <td>{{item.value}}</td>-->\r\n<!--                </tr>-->\r\n<!--              </table>-->\r\n<!--            </div>-->\r\n<!--          </el-col>-->\r\n<!--          <el-col :col=\"8\" >-->\r\n<!--            -->\r\n<!--          </el-col>-->\r\n<!--        </el-row>-->\r\n      </template>\r\n\r\n    </el-form>\r\n    <div v-if=\"!readonly\" slot=\"footer\" class=\"dialog-footer\" style=\"margin-top: 20px\">\r\n      <el-button type=\"primary\" @click=\"submitForm\" size=\"mini\" :loading=\"btnLoading\" >确 定</el-button>\r\n      <el-button @click=\"cancel\" size=\"mini\" >取 消</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport {addContract, getContract, updateContract} from \"@/api/customer/contract\";\r\nimport {getAiResolveContract} from \"@/api/common/ai\";\r\n\r\nexport default {\r\n  name: \"customerContractSave\",\r\n  props: {\r\n    customerId: {\r\n      type: Number,\r\n      required: true,\r\n    },\r\n    readonly: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      btnLoading: false,\r\n      form: {},\r\n      rules: {\r\n        name: [\r\n          {required: true,message: '请问输入文件名'},\r\n        ]\r\n      },\r\n      deliveryTypeOptions: ['+','-','±'],\r\n      files: [],\r\n      qualityFiles: [],\r\n      showDoc: false,\r\n      dataArray: [],\r\n      o : {\r\n        \"party_a\": \"甲方名称\",\r\n        \"party_b\": \"乙方名称\",\r\n        \"signed_date\": \"签订日期\",\r\n        \"on_set_date\": \"起效日期\",\r\n        \"expire_date\": \"到期日期\",\r\n        \"payment_term\": \"账期描述\",\r\n        \"financial_risk\": \"财务风险描述\",\r\n        \"delivery_control\": \"交货数量控制条款\",\r\n        \"delay_risk\": \"交货延误风险条款\",\r\n        \"quality_responsibility\": \"质量责任协议\",\r\n        \"quality_risk\": \"质量风险描述\",\r\n        \"production_risk\": \"生产损耗与成本风险\",\r\n        \"ip_risk\": \"知识产权与保密风险\",\r\n        \"breach_liability\": \"违约责任条款\",\r\n        \"data_risk\": \"数据与记录风险\",\r\n        \"other_risks\": \"其他风险描述\",\r\n        \"party_b_suggestions\": \"乙方建议\",\r\n      },\r\n      partyBSuggestions: [],\r\n    }\r\n  },\r\n  async created() {\r\n  },\r\n  methods: {\r\n    async fileChange(files) {\r\n      console.log(files)\r\n      // for (const file of files) {\r\n      //   await this.selectFile(file)\r\n      // }\r\n    },\r\n    addItem() {\r\n      this.partyBSuggestions.push('')\r\n    },\r\n    delItem(i) {\r\n      this.partyBSuggestions.splice(i,1)\r\n    },\r\n    async selectFile(file) {\r\n      this.loading = true\r\n      if(file.url) {\r\n        this.dataArray = []\r\n        const res = await getAiResolveContract(file)\r\n        if(res.code === 200 ) {\r\n          const data = res.data\r\n          if(data) {\r\n            const o = data.extracted_data\r\n            if(o) {\r\n              for (const k of Object.keys(o)) {\r\n                this.dataArray.push({\r\n                  label: this.o[k],\r\n                  key: k,\r\n                  value: o[k]\r\n                })\r\n              }\r\n            }\r\n            this.form.name = file.name\r\n            this.form.partyA = o.party_a\r\n            this.form.partyB = o.party_b\r\n            this.form.signedDate = o.signed_date\r\n            this.form.onSetDate = o.on_set_date\r\n            this.form.expireDate = o.expire_date\r\n            this.form.paymentTerm = o.payment_term\r\n            this.form.financialRisk = o.financial_risk\r\n            this.form.deliveryControl = o.delivery_control\r\n            this.form.delayRisk = o.delay_risk\r\n            this.form.qualityResponsibility = o.quality_responsibility\r\n            this.form.qualityRisk = o.quality_risk\r\n            this.form.productionRisk = o.production_risk\r\n            this.form.ipRisk = o.ip_risk\r\n            this.form.breachLiability = o.breach_liability\r\n            this.form.dataRisk = o.data_risk\r\n            this.form.other = o.other_risks\r\n            this.form.text = JSON.stringify(data)\r\n            this.showDoc = true\r\n          }\r\n          if(data.advice) {\r\n            this.partyBSuggestions = data.advice.party_b_suggestions\r\n          }\r\n        }\r\n      }\r\n      this.loading = false\r\n    },\r\n    cancel() {\r\n      this.$parent.$parent.open = false;\r\n      this.reset();\r\n    },\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        customerId: null,\r\n        code: null,\r\n        name: null,\r\n        periodType: null,\r\n        periodDays: null,\r\n        nums: null,\r\n        deliveryType: null,\r\n        overdue: null,\r\n        qualityResponsibility: null,\r\n        other: null,\r\n        signedDate: null,\r\n        onSetDate: null,\r\n        expireDate: null,\r\n        locationCode: null,\r\n        remark: null,\r\n        partyA: null,\r\n        partyB: null,\r\n        financialRisk: null,\r\n        deliveryControl: null,\r\n        delayRisk: null,\r\n        qualityRisk: null,\r\n        productionRisk: null,\r\n        ipRisk: null,\r\n        breachLiability: null,\r\n        dataRisk: null,\r\n        text: null,\r\n      };\r\n      this.resetForm(\"form\")\r\n      this.files = []\r\n      this.qualityFiles = []\r\n      this.dataArray = []\r\n      this.partyBSuggestions = []\r\n      this.showDoc = true\r\n    },\r\n    async init(id) {\r\n      this.loading = true\r\n      const res = await getContract(id)\r\n      const form = res.data\r\n\r\n      if(form.files) {\r\n        this.files = JSON.parse(form.files)\r\n      }\r\n\r\n      if(form.qualityFiles) {\r\n        this.qualityFiles = JSON.parse(form.qualityFiles)\r\n      }\r\n\r\n      if(form.partyBSuggestions) {\r\n        this.partyBSuggestions = JSON.parse(form.partyBSuggestions)\r\n      }\r\n\r\n      this.form = form\r\n      this.loading = false\r\n    },\r\n    async submitForm() {\r\n      await this.$refs[\"form\"].validate()\r\n      let form = Object.assign({},this.form)\r\n\r\n      form.files = JSON.stringify(this.files)\r\n      form.qualityFiles = JSON.stringify(this.qualityFiles)\r\n      form.partyBSuggestions = JSON.stringify(this.partyBSuggestions)\r\n\r\n      if (form.id != null) {\r\n        try {\r\n          this.btnLoading = true\r\n          await updateContract(form)\r\n          this.btnLoading = false\r\n          this.msgSuccess(\"修改成功\")\r\n          this.$parent.$parent.open = false\r\n          await this.$parent.$parent.getList()\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      } else {\r\n        form.customerId = this.customerId\r\n        try {\r\n          this.btnLoading = true\r\n          await addContract(form)\r\n          this.btnLoading = false\r\n          this.msgSuccess(\"新增成功\")\r\n          this.$parent.$parent.open = false;\r\n          await this.$parent.$parent.getList()\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n\r\n</style>\r\n"]}]}