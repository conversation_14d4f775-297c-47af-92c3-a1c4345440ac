import request from '@/utils/request'

// 查询生产批序号列表
export function listWipcontPartialout(query) {
  return request({
    url: '/mes/wipcontPartialout/list',
    method: 'get',
    params: query
  })
}

// 查询生产批序号详细
export function getWipcontPartialout(lotno) {
  return request({
    url: '/mes/wipcontPartialout/' + lotno,
    method: 'get'
  })
}

// 新增生产批序号
export function addWipcontPartialout(data) {
  return request({
    url: '/mes/wipcontPartialout',
    method: 'post',
    data: data
  })
}

// 修改生产批序号
export function updateWipcontPartialout(data) {
  return request({
    url: '/mes/wipcontPartialout',
    method: 'put',
    data: data
  })
}

// 删除生产批序号
export function delWipcontPartialout(lotno) {
  return request({
    url: '/mes/wipcontPartialout/' + lotno,
    method: 'delete'
  })
}

// 导出生产批序号
export function exportWipcontPartialout(query) {
  return request({
    url: '/mes/wipcontPartialout/export',
    method: 'get',
    params: query
  })
}

export function allWipContPartialOut(query) {
  return request({
    url: '/mes/wipcontPartialout/all',
    method: 'get',
    params: query
  })
}
