import request from '@/utils/request'

// 查询采购单到货记录列表
export function listErpPurtdRecord(query) {
  return request({
    url: '/order/erpPurtdRecord/list',
    method: 'get',
    params: query
  })
}

// 查询采购单到货记录详细
export function getErpPurtdRecord(id) {
  return request({
    url: '/order/erpPurtdRecord/' + id,
    method: 'get'
  })
}

// 新增采购单到货记录
export function addErpPurtdRecord(data) {
  return request({
    url: '/order/erpPurtdRecord',
    method: 'post',
    data: data
  })
}

// 修改采购单到货记录
export function updateErpPurtdRecord(data) {
  return request({
    url: '/order/erpPurtdRecord',
    method: 'put',
    data: data
  })
}

// 删除采购单到货记录
export function delErpPurtdRecord(id) {
  return request({
    url: '/order/erpPurtdRecord/' + id,
    method: 'delete'
  })
}

// 导出采购单到货记录
export function exportErpPurtdRecord(query) {
  return request({
    url: '/order/erpPurtdRecord/export',
    method: 'get',
    params: query
  })
}