import request from '@/utils/request'

// 查询研发系统配方记录列表
export function listFormula(query) {
  return request({
    url: '/project/formula/list',
    method: 'get',
    params: query
  })
}

// 查询研发系统配方记录详细
export function getFormula(id) {
  return request({
    url: '/project/formula/' + id,
    method: 'get'
  })
}

// 新增研发系统配方记录
export function addFormula(data) {
  return request({
    url: '/project/formula',
    method: 'post',
    data: data
  })
}

// 修改研发系统配方记录
export function updateFormula(data) {
  return request({
    url: '/project/formula',
    method: 'put',
    data: data
  })
}

// 删除研发系统配方记录
export function delFormula(id) {
  return request({
    url: '/project/formula/' + id,
    method: 'delete'
  })
}

// 导出研发系统配方记录
export function exportFormula(query) {
  return request({
    url: '/project/formula/export',
    method: 'get',
    params: query
  })
}