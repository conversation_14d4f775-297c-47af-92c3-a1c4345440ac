import request from '@/utils/request'

// 查询绩效考核列表
export function listPa(query) {
  return request({
    url: '/hr/pa/list',
    method: 'get',
    params: query
  })
}

// 查询绩效考核详细
export function getPa(id) {
  return request({
    url: '/hr/pa/' + id,
    method: 'get'
  })
}

// 新增绩效考核
export function addPa(data) {
  return request({
    url: '/hr/pa',
    method: 'post',
    data: data
  })
}

// 修改绩效考核
export function updatePa(data) {
  return request({
    url: '/hr/pa',
    method: 'put',
    data: data
  })
}

// 删除绩效考核
export function delPa(id) {
  return request({
    url: '/hr/pa/' + id,
    method: 'delete'
  })
}

// 导出绩效考核
export function exportPa(query) {
  return request({
    url: '/hr/pa/export',
    method: 'get',
    params: query
  })
}