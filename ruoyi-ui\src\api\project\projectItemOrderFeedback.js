import request from '@/utils/request'

// 查询子项目订单反馈列表
export function listProjectItemOrderFeedback(query) {
  return request({
    url: '/project/projectItemOrderFeedback/list',
    method: 'get',
    params: query
  })
}

// 查询子项目订单反馈列表
export function listProjectItemOrderFeedbackNew(query) {
  return request({
    url: '/project/projectItemOrderFeedback/listNew',
    method: 'get',
    params: query
  })
}


// 查询子项目订单反馈列表
export function listProjectItemOrderFeedbackAll(query) {
  return request({
    url: '/project/projectItemOrderFeedback/orderFeedbackAll',
    method: 'get',
    params: query
  })
}

// 查询子项目订单反馈详细
export function getProjectItemOrderFeedback(id) {
  return request({
    url: '/project/projectItemOrderFeedback/' + id,
    method: 'get'
  })
}

// 新增子项目订单反馈
export function addProjectItemOrderFeedback(data) {
  return request({
    url: '/project/projectItemOrderFeedback',
    method: 'post',
    data: data
  })
}

// 修改子项目订单反馈
export function updateProjectItemOrderFeedback(data) {
  return request({
    url: '/project/projectItemOrderFeedback',
    method: 'put',
    data: data
  })
}

// 删除子项目订单反馈
export function delProjectItemOrderFeedback(id) {
  return request({
    url: '/project/projectItemOrderFeedback/' + id,
    method: 'delete'
  })
}

// 导出子项目订单反馈
export function exportProjectItemOrderFeedback(query) {
  return request({
    url: '/project/projectItemOrderFeedback/export',
    method: 'get',
    params: query
  })
}
