import request from '@/utils/request'

// 查询物料测试列表
export function listMaterialTesting(query) {
  return request({
    url: '/qc/materialTesting/list',
    method: 'get',
    params: query
  })
}

// 查询物料测试详细
export function getMaterialTesting(id) {
  return request({
    url: '/qc/materialTesting/' + id,
    method: 'get'
  })
}

// 新增物料测试
export function addMaterialTesting(data) {
  return request({
    url: '/qc/materialTesting',
    method: 'post',
    data: data
  })
}

// 修改物料测试
export function updateMaterialTesting(data) {
  return request({
    url: '/qc/materialTesting',
    method: 'put',
    data: data
  })
}

// 删除物料测试
export function delMaterialTesting(id) {
  return request({
    url: '/qc/materialTesting/' + id,
    method: 'delete'
  })
}

export function submitAudit(data) {
  return request({
    url: '/qc/materialTesting/submitAudit',
    method: 'put',
    data: data
  })
}

//撤销申请
export function cancelAudit(data) {
  return request({
    url: '/qc/materialTesting/cancelAudit',
    method: 'put',
    data: data
  })
}

//提交变更审核
export function submitChangeAudit(data) {
  return request({
    url: '/qc/materialTesting/submitChangeAudit',
    method: 'put',
    data: data
  })
}

//撤销变更申请
export function cancelChangeAudit(data) {
  return request({
    url: '/qc/materialTesting/cancelChangeAudit',
    method: 'put',
    data: data
  })
}

export function allMaterialTesting(query) {
  return request({
    url: '/qc/materialTesting/all',
    method: 'get',
    params: query
  })
}

export function exportMaterialTesting(data) {
  return request({
    url: '/qc/materialTesting/export',
    method: 'post',
    data,
  })
}

export function exportMaterialTestingById(id) {
  return request({
    url: '/qc/materialTesting/exportMaterialTesting/' + id,
    method: 'post',
  })
}

