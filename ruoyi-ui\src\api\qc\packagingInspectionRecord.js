import request from '@/utils/request'

// 查询包材检验记录列表
export function listPackagingInspectionRecord(query) {
  return request({
    url: '/qc/packagingInspectionRecord/list',
    method: 'get',
    params: query
  })
}

// 查询包材检验记录详细
export function getPackagingInspectionRecord(id) {
  return request({
    url: '/qc/packagingInspectionRecord/' + id,
    method: 'get'
  })
}

// 查询包材检验记录详细
export function getPackagingInspectionRecordDetail(id) {
  return request({
    url: '/qc/packagingInspectionRecord/detail/' + id,
    method: 'get'
  })
}

// 新增包材检验记录
export function addPackagingInspectionRecord(data) {
  return request({
    url: '/qc/packagingInspectionRecord',
    method: 'post',
    data: data
  })
}

// 修改包材检验记录
export function updatePackagingInspectionRecord(data) {
  return request({
    url: '/qc/packagingInspectionRecord',
    method: 'put',
    data: data
  })
}

// 删除包材检验记录
export function delPackagingInspectionRecord(id) {
  return request({
    url: '/qc/packagingInspectionRecord/' + id,
    method: 'delete'
  })
}

// 导出包材检验记录
export function exportPackagingInspectionRecord(id) {
  return request({
    url: '/qc/packagingInspectionRecord/export/' + id,
    method: 'get',
  })
}

export function allPackagingInspectionRecord(query) {
  return request({
    url: '/qc/packagingInspectionRecord/all',
    method: 'get',
    params: query
  })
}

export function microbePackagingInspectionRecord(query) {
  return request({
    url: '/qc/packagingInspectionRecord/microbeArray',
    method: 'get',
    params: query
  })
}

export function recentAllPackagingInspectionRecord(query) {
  return request({
    url: '/qc/packagingInspectionRecord/recentAll',
    method: 'get',
    params: query
  })
}

//导出标签  留样标签
export function printLableDetail(data) {
  return request({
    url: '/qc/packagingInspectionRecord/printLableDetail',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

//导出标签
export function printLableDetail2(data) {
  return request({
    url: '/qc/packagingInspectionRecord/printLableDetail2',
    method: 'post',
    data,
    responseType: 'blob'
  })
}
