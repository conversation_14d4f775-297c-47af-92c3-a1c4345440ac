import request from '@/utils/request'

// 查询文件类别列表
export function listCategory(query) {
  return request({
    url: '/filemanage/category/list',
    method: 'get',
    params: query
  })
}

export function AlllistCategory(query) {
  return request({
    url: '/filemanage/category/Alllist',
    method: 'get',
    params: query
  })
}


// 查询文件类别详细
export function getCategory(id) {
  return request({
    url: '/filemanage/category/' + id,
    method: 'get'
  })
}

// 新增文件类别
export function addCategory(data) {
  return request({
    url: '/filemanage/category',
    method: 'post',
    data: data
  })
}

// 修改文件类别
export function updateCategory(data) {
  return request({
    url: '/filemanage/category',
    method: 'put',
    data: data
  })
}

// 删除文件类别
export function delCategory(id) {
  return request({
    url: '/filemanage/category/' + id,
    method: 'delete'
  })
}

// 导出文件类别
export function exportCategory(query) {
  return request({
    url: '/filemanage/category/export',
    method: 'get',
    params: query
  })
}
