import request from '@/utils/request'

// 查询开票红字发票明细列表
export function listBillingRevokeItem(query) {
  return request({
    url: '/order/billingRevokeItem/list',
    method: 'get',
    params: query
  })
}

// 查询开票红字发票明细详细
export function getBillingRevokeItem(id) {
  return request({
    url: '/order/billingRevokeItem/' + id,
    method: 'get'
  })
}

// 新增开票红字发票明细
export function addBillingRevokeItem(data) {
  return request({
    url: '/order/billingRevokeItem',
    method: 'post',
    data: data
  })
}

// 修改开票红字发票明细
export function updateBillingRevokeItem(data) {
  return request({
    url: '/order/billingRevokeItem',
    method: 'put',
    data: data
  })
}

// 删除开票红字发票明细
export function delBillingRevokeItem(id) {
  return request({
    url: '/order/billingRevokeItem/' + id,
    method: 'delete'
  })
}

// 导出开票红字发票明细
export function exportBillingRevokeItem(query) {
  return request({
    url: '/order/billingRevokeItem/export',
    method: 'get',
    params: query
  })
}

export function allBillingRevokeItem(query) {
  return request({
    url: '/order/billingRevokeItem/all',
    method: 'get',
    params: query
  })
}
