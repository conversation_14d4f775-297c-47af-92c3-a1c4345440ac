import request from '@/utils/request'

// 查询订单商品计划列表
export function listOrderGoodsPlan(query) {
  return request({
    url: '/order/orderGoodsPlan/list',
    method: 'get',
    params: query
  })
}

// 查询订单商品计划详细
export function getOrderGoodsPlan(id) {
  return request({
    url: '/order/orderGoodsPlan/' + id,
    method: 'get'
  })
}

// 新增订单商品计划
export function addOrderGoodsPlan(data) {
  return request({
    url: '/order/orderGoodsPlan',
    method: 'post',
    data: data
  })
}

// 修改订单商品计划
export function updateOrderGoodsPlan(data) {
  return request({
    url: '/order/orderGoodsPlan',
    method: 'put',
    data: data
  })
}

// 删除订单商品计划
export function delOrderGoodsPlan(id) {
  return request({
    url: '/order/orderGoodsPlan/' + id,
    method: 'delete'
  })
}

// 导出订单商品计划
export function exportOrderGoodsPlan(query) {
  return request({
    url: '/order/orderGoodsPlan/export',
    method: 'get',
    params: query
  })
}