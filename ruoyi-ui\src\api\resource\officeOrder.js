import request from '@/utils/request'

// 查询办公用品订单列表
export function listOfficeOrder(query) {
  return request({
    url: '/resource/officeOrder/list',
    method: 'get',
    params: query
  })
}

// 查询办公用品订单详细
export function getOfficeOrder(id) {
  return request({
    url: '/resource/officeOrder/' + id,
    method: 'get'
  })
}

// 新增办公用品订单
export function addOfficeOrder(data) {
  return request({
    url: '/resource/officeOrder',
    method: 'post',
    data: data
  })
}

// 修改办公用品订单
export function updateOfficeOrder(data) {
  return request({
    url: '/resource/officeOrder',
    method: 'put',
    data: data
  })
}

// 删除办公用品订单
export function delOfficeOrder(id) {
  return request({
    url: '/resource/officeOrder/' + id,
    method: 'delete'
  })
}

//提交审核
export function submitAudit(data) {
  return request({
    url: '/resource/officeOrder/submitAudit',
    method: 'put',
    data: data
  })
}

//撤销申请
export function cancelAudit(data) {
  return request({
    url: '/resource/officeOrder/cancelAudit',
    method: 'put',
    data: data
  })
}

export function myOfficeOrderList(query) {
  return request({
    url: '/resource/officeOrder/myOrderList',
    method: 'get',
    params: query
  })
}

/**
 * 发货
 */
export function handOutOrder(data) {
  return request({
    url: '/resource/officeOrder/handOut',
    method: 'put',
    data,
  })
}

export function exportOfficeOrder(id) {
  return request({
    url: '/resource/officeOrder/export/' + id,
    method: 'get',
  })
}

export function exportOfficeOut(data) {
  return request({
    url: '/resource/officeOrder/exportOut',
    method: 'post',
    data,
  })
}

export function exportOfficeRk(data) {
  return request({
    url: '/resource/officeOrder/exportRk',
    method: 'post',
    data,
  })
}

export function revokeOutOfficeOrder(id) {
  return request({
    url: '/resource/officeOrder/revokeOut/' + id,
    method: 'put'
  })
}

export function exportOfficeOrderList(params) {
  return request({
    url: '/resource/officeOrder/exportOrderList',
    method: 'get',
    params,
  })
}
