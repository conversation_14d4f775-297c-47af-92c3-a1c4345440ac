import request from "@/utils/request";

export function listErpWholePackage(query) {
  return request({
    url: '/generate/purchase/wholePackage/list',
    method: 'get',
    params: query
  })
}
export function listErpPurtc(query) {
  return request({
    url: '/generate/purchase/purtc/list',
    method: 'get',
    params: query
  })
}
export function getErpPurtc(query) {
  return request({
    url: '/generate/purchase/purtc/detail',
    method: 'get',
    params: query
  })
}
export function listErpPurtd(query) {
  return request({
    url: '/generate/purchase/purtd/list',
    method: 'get',
    params: query
  })
}
export function getErpPurtd(data) {
  return request({
    url: '/generate/purchase/purtd/detail',
    method: 'post',
    data: data
  })
}
export function listGenerateErpPurtd(query) {
  return request({
    url: '/generate/purchase/generate/list',
    method: 'get',
    params: query
  })
}
export function getGenerateErpPurtd(data) {
  return request({
    url: '/generate/purchase/generate/detail',
    method: 'post',
    data: data
  })
}
export function batchGenerateErpPurtd(data) {
  return request({
    url: '/generate/purchase/generate/batch',
    method: 'post',
    data: data
  })
}

export function batchGenerateOrderPayStyle(data) {
  return request({
    url: '/generate/purchase/generate/batchGenerateOrderPayStyle',
    method: 'post',
    data: data
  })
}
export function updateErpPurtd(data) {
  return request({
    url: '/generate/purchase/purtd/edit',
    method: 'post',
    data: data
  })
}

