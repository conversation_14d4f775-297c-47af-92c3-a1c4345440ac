import request from '@/utils/request'

// 查询CIR类别列表
export function listCirCategory(query) {
  return request({
    url: '/rd/cirCategory/list',
    method: 'get',
    params: query
  })
}

// 查询CIR类别详细
export function getCirCategory(id) {
  return request({
    url: '/rd/cirCategory/' + id,
    method: 'get'
  })
}

// 新增CIR类别
export function addCirCategory(data) {
  return request({
    url: '/rd/cirCategory',
    method: 'post',
    data: data
  })
}

// 修改CIR类别
export function updateCirCategory(data) {
  return request({
    url: '/rd/cirCategory',
    method: 'put',
    data: data
  })
}

// 删除CIR类别
export function delCirCategory(id) {
  return request({
    url: '/rd/cirCategory/' + id,
    method: 'delete'
  })
}

// 导出CIR类别
export function exportCirCategory(query) {
  return request({
    url: '/rd/cirCategory/export',
    method: 'get',
    params: query
  })
}

export function allCirCategory(query) {
  return request({
    url: '/rd/cirCategory/all',
    method: 'get',
    params: query
  })
}

export function allIncludesIngredientsCirCategory(query) {
  return request({
    url: '/rd/cirCategory/allIncludesIngredients',
    method: 'get',
    params: query
  })
}
