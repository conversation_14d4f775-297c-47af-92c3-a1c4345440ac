import request from "@/utils/request";

export function listUserBankArray(query) {
  return request({
    url: '/hr/user/bank/list',
    method: 'get',
    params: query
  })
}
export function addUserBankArray(data) {
  return request({
    url: '/hr/user/bank/add',
    method: 'post',
    data: data
  })
}
export function updateUserBankArray(data) {
  return request({
    url: '/hr/user/bank/edit',
    method: 'post',
    data: data
  })
}

// 删除用户
export function delHrUserBank(id) {
  return request({
    url: '/hr/user/bank/' + id,
    method: 'delete'
  })
}
