import request from '@/utils/request'

// 查询设备维修记录列表
export function listRepairLog(query) {
  return request({
    url: '/production/repairLog/list',
    method: 'get',
    params: query
  })
}

// 查询设备维修记录详细
export function getRepairLog(id) {
  return request({
    url: '/production/repairLog/' + id,
    method: 'get'
  })
}

// 新增设备维修记录
export function addRepairLog(data) {
  return request({
    url: '/production/repairLog',
    method: 'post',
    data: data
  })
}

// 修改设备维修记录
export function updateRepairLog(data) {
  return request({
    url: '/production/repairLog',
    method: 'put',
    data: data
  })
}

// 删除设备维修记录
export function delRepairLog(id) {
  return request({
    url: '/production/repairLog/' + id,
    method: 'delete'
  })
}

// 导出设备维修记录
export function exportRepairLog(query) {
  return request({
    url: '/production/repairLog/export',
    method: 'get',
    params: query
  })
}