import request from '@/utils/request'

// 查询包材原料价格审核列表
export function listPurchaseAuditPmPrice(query) {
  return request({
    url: '/purchase/purchaseAuditPmPrice/list',
    method: 'get',
    params: query
  })
}
// 查询包材原料价格审核列表
export function listPurchaseAuditPmPriceAudit(query) {
  return request({
    url: '/purchase/purchaseAuditPmPrice/audit',
    method: 'get',
    params: query
  })
}
// 查询包材原料价格审核列表
export function listPurchaseAuditPmPriceHistory(query) {
  return request({
    url: '/purchase/purchaseAuditPmPrice/history',
    method: 'get',
    params: query
  })
}

// 查询包材原料价格审核详细
export function getPurchaseAuditPmPrice(id) {
  return request({
    url: '/purchase/purchaseAuditPmPrice/' + id,
    method: 'get'
  })
}

// 新增包材原料价格审核
export function addPurchaseAuditPmPrice(data) {
  return request({
    url: '/purchase/purchaseAuditPmPrice',
    method: 'post',
    data: data
  })
}

// 修改包材原料价格审核
export function updatePurchaseAuditPmPrice(data) {
  return request({
    url: '/purchase/purchaseAuditPmPrice',
    method: 'put',
    data: data
  })
}

// 删除包材原料价格审核
export function delPurchaseAuditPmPrice(id) {
  return request({
    url: '/purchase/purchaseAuditPmPrice/' + id,
    method: 'delete'
  })
}

// 导出包材原料价格审核
export function exportPurchaseAuditPmPrice(query) {
  return request({
    url: '/purchase/purchaseAuditPmPrice/export',
    method: 'get',
    params: query
  })
}
