import request from '@/utils/request'

// 查询料体损耗列表
export function listProjectOfferNrwLoss(query) {
  return request({
    url: '/project/projectOfferNrwLoss/list',
    method: 'get',
    params: query
  })
}

// 查询料体损耗详细
export function getProjectOfferNrwLoss(id) {
  return request({
    url: '/project/projectOfferNrwLoss/' + id,
    method: 'get'
  })
}

// 新增料体损耗
export function addProjectOfferNrwLoss(data) {
  return request({
    url: '/project/projectOfferNrwLoss',
    method: 'post',
    data: data
  })
}

// 修改料体损耗
export function updateProjectOfferNrwLoss(data) {
  return request({
    url: '/project/projectOfferNrwLoss',
    method: 'put',
    data: data
  })
}

// 删除料体损耗
export function delProjectOfferNrwLoss(id) {
  return request({
    url: '/project/projectOfferNrwLoss/' + id,
    method: 'delete'
  })
}

// 导出料体损耗
export function exportProjectOfferNrwLoss(query) {
  return request({
    url: '/project/projectOfferNrwLoss/export',
    method: 'get',
    params: query
  })
}

// 查询料体损耗列表
export function itemAll(query) {
  return request({
    url: '/project/projectOfferNrwLoss/itemAll',
    method: 'get',
    params: query
  })
}

export function getBcpLossByCategoryAndSjSyl(data) {
  return request({
    url: '/project/projectOfferNrwLoss/lossByCategoryAndSjSyl',
    method: 'post',
    data,
  })
}
