import request from '@/utils/request'

// 查询包材物料类型列表
export function listMaterialType(query) {
  return request({
    url: '/qc/materialType/list',
    method: 'get',
    params: query
  })
}

// 查询包材物料类型详细
export function getMaterialType(id) {
  return request({
    url: '/qc/materialType/' + id,
    method: 'get'
  })
}

// 新增包材物料类型
export function addMaterialType(data) {
  return request({
    url: '/qc/materialType',
    method: 'post',
    data: data
  })
}

// 修改包材物料类型
export function updateMaterialType(data) {
  return request({
    url: '/qc/materialType',
    method: 'put',
    data: data
  })
}

// 删除包材物料类型
export function delMaterialType(id) {
  return request({
    url: '/qc/materialType/' + id,
    method: 'delete'
  })
}

// 导出包材物料类型
export function exportMaterialType(query) {
  return request({
    url: '/qc/materialType/export',
    method: 'get',
    params: query
  })
}

export function allMaterialType(query) {
  return request({
    url: '/qc/materialType/all',
    method: 'get',
    params: query
  })
}
