{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\engineerSampleOrder\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\engineerSampleOrder\\index.vue", "mtime": 1753927608484}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0RW5naW5lZXJTYW1wbGVPcmRlciwNCiAgZ2V0RW5naW5lZXJTYW1wbGVPcmRlciwNCiAgZGVsRW5naW5lZXJTYW1wbGVPcmRlciwNCiAgYWRkRW5naW5lZXJTYW1wbGVPcmRlciwNCiAgdXBkYXRlRW5naW5lZXJTYW1wbGVPcmRlciwNCiAgdXBkYXRlU2FtcGxlT3JkZXJTdGF0dXMsDQogIGdldERhc2hib2FyZFN0YXRzLA0KICBnZXRSZXNlYXJjaERlcGFydG1lbnRzLA0KICBnZXRFbmdpbmVlcnNCeURpZmZpY3VsdHlMZXZlbCwNCiAgY2hhbmdlRW5naW5lZXIsDQogIGdldFJlc2VhcmNoRGVwYXJ0bWVudHNVc2VyLA0KICBnZXRCYXRjaGVzQnlPcmRlcklkLA0KICBnZXRDdXJyZW50QmF0Y2gsDQogIHN0YXJ0TmV3QmF0Y2gsDQogIGZpbmlzaEN1cnJlbnRCYXRjaCwNCiAgYWRkRXhwZXJpbWVudFRvQmF0Y2gsDQogIGdldEV4cGVyaW1lbnRzQnlCYXRjaElkLA0KICBleHBvcnRFbmdpbmVlclNhbXBsZU9yZGVyLCBnZXRFeGVjdXRpb25CeU9yZGVySW5mbw0KfSBmcm9tICJAL2FwaS9zb2Z0d2FyZS9lbmdpbmVlclNhbXBsZU9yZGVyIjsNCmltcG9ydCB7ZXhwb3J0TnJ3SXRlbSwgZXhwb3J0TXVsdGlwbGVOcnd9IGZyb20gIkAvYXBpL3Byb2plY3QvcHJvamVjdEl0ZW1PcmRlciI7DQppbXBvcnQgVHJlZXNlbGVjdCBmcm9tICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdCI7DQppbXBvcnQgIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0L2Rpc3QvdnVlLXRyZWVzZWxlY3QuY3NzIjsNCmltcG9ydCB7Y3VzdG9tZXJCYXNlQWxsfSBmcm9tICJAL2FwaS9jdXN0b21lci9jdXN0b21lciI7DQppbXBvcnQge2lzTnVsbH0gZnJvbSAiQC91dGlscy92YWxpZGF0ZSI7DQppbXBvcnQgZXhlY3V0aW9uQWRkT3JFZGl0IGZyb20gIkAvY29tcG9uZW50cy9Qcm9qZWN0L2NvbXBvbmVudHMvZXhlY3V0aW9uQWRkT3JFZGl0LnZ1ZSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkVuZ2luZWVyU2FtcGxlT3JkZXIiLA0KICBjb21wb25lbnRzOiB7DQogICAgIFRyZWVzZWxlY3QsZXhlY3V0aW9uQWRkT3JFZGl0DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmAieS4reeahOWujOaVtOihjOaVsOaNrg0KICAgICAgc2VsZWN0ZWRSb3dzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDlt6XnqIvluIjmiZPmoLfljZXlhbPogZTooajmoLzmlbDmja4NCiAgICAgIGVuZ2luZWVyU2FtcGxlT3JkZXJMaXN0OiBbXSwNCiAgICAgIC8vIOeglOWPkemDqOmDqOmXqOagkeWIl+ihqA0KICAgICAgcmVzZWFyY2hEZXB0RGF0YXM6W10sDQogICAgICAvLyDmiZPmoLfnsbvliKvlrZflhbgNCiAgICAgIGR5bGJPcHRpb25zOiBbXSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICB1c2VySWQ6IG51bGwsDQogICAgICAgIG5pY2tOYW1lOiBudWxsLA0KICAgICAgICBzYW1wbGVPcmRlckNvZGU6IG51bGwsDQogICAgICAgIGNvbXBsZXRpb25TdGF0dXM6IG51bGwsDQogICAgICAgIHNjaGVkdWxlZERhdGU6IG51bGwsDQogICAgICAgIHN0YXJ0RGF0ZTogbnVsbCwNCiAgICAgICAgYWN0dWFsU3RhcnRUaW1lOiBudWxsLA0KICAgICAgICBhY3R1YWxGaW5pc2hUaW1lOiBudWxsLA0KICAgICAgICBkZXB0SWQ6IG51bGwsDQogICAgICAgIGRlcHRJZHM6IFtdLA0KICAgICAgICBhc3NvY2lhdGlvblN0YXR1czogbnVsbCwNCiAgICAgICAgY3VzdG9tZXJJZDogbnVsbCwNCiAgICAgICAgcHJvZHVjdE5hbWU6IG51bGwsDQogICAgICAgIGNvbmZpcm1Db2RlOiBudWxsLA0KICAgICAgICBpc092ZXJkdWU6IG51bGwsICAvLyDlop7pgL7mnJ/ku7vliqHov4fmu6Tlj4LmlbANCiAgICAgICAgbGFib3JhdG9yeTogbnVsbCAgLy8g5a6e6aqM5a6k562b6YCJ5Y+C5pWwDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgdXNlcklkOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuW3peeoi+W4iElE5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgc2FtcGxlT3JkZXJDb2RlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaJk+agt+WNlee8luWPt+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIGNvbXBsZXRpb25TdGF0dXM6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5a6M5oiQ5oOF5Ya15LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfQ0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgc3RhdHVzT3B0aW9uczogW10sDQogICAgICBzZXJ2aWNlTW9kZU9wdGlvbnM6IFtdLA0KICAgICAgZGF0ZVJhbmdlOiBbXSwgLy8g5paw5aKe55qE5pel5pyf6IyD5Zu0562b6YCJDQogICAgICBzY2hlZHVsZWREYXRlUmFuZ2U6IFtdLA0KICAgICAgc3RhcnREYXRlUmFuZ2U6IFtdLA0KICAgICAgYWN0dWFsU3RhcnRUaW1lUmFuZ2U6IFtdLA0KICAgICAgYWN0dWFsRmluaXNoVGltZVJhbmdlOiBbXSwNCiAgICAgIC8vIOaXpeacn+mAieaLqeWZqOmFjee9rg0KICAgICAgZGF0YVBpY2tlck9wdGlvbnM6IHsNCiAgICAgICAgc2hvcnRjdXRzOiBbew0KICAgICAgICAgIHRleHQ6ICfku4rlpKknLA0KICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7DQogICAgICAgICAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCk7DQogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbdG9kYXksIHRvZGF5XSk7DQogICAgICAgICAgfQ0KICAgICAgICB9LCB7DQogICAgICAgICAgdGV4dDogJ+aYqOWkqScsDQogICAgICAgICAgb25DbGljayhwaWNrZXIpIHsNCiAgICAgICAgICAgIGNvbnN0IHllc3RlcmRheSA9IG5ldyBEYXRlKCk7DQogICAgICAgICAgICB5ZXN0ZXJkYXkuc2V0VGltZSh5ZXN0ZXJkYXkuZ2V0VGltZSgpIC0gMzYwMCAqIDEwMDAgKiAyNCk7DQogICAgICAgICAgICBwaWNrZXIuJGVtaXQoJ3BpY2snLCBbeWVzdGVyZGF5LCB5ZXN0ZXJkYXldKTsNCiAgICAgICAgICB9DQogICAgICAgIH0sIHsNCiAgICAgICAgICB0ZXh0OiAn5pyA6L+R5LiA5ZGoJywNCiAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgew0KICAgICAgICAgICAgY29uc3QgZW5kID0gbmV3IERhdGUoKTsNCiAgICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gbmV3IERhdGUoKTsNCiAgICAgICAgICAgIHN0YXJ0LnNldFRpbWUoc3RhcnQuZ2V0VGltZSgpIC0gMzYwMCAqIDEwMDAgKiAyNCAqIDcpOw0KICAgICAgICAgICAgcGlja2VyLiRlbWl0KCdwaWNrJywgW3N0YXJ0LCBlbmRdKTsNCiAgICAgICAgICB9DQogICAgICAgIH0sIHsNCiAgICAgICAgICB0ZXh0OiAn5pyA6L+R5LiA5Liq5pyIJywNCiAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgew0KICAgICAgICAgICAgY29uc3QgZW5kID0gbmV3IERhdGUoKTsNCiAgICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gbmV3IERhdGUoKTsNCiAgICAgICAgICAgIHN0YXJ0LnNldFRpbWUoc3RhcnQuZ2V0VGltZSgpIC0gMzYwMCAqIDEwMDAgKiAyNCAqIDMwKTsNCiAgICAgICAgICAgIHBpY2tlci4kZW1pdCgncGljaycsIFtzdGFydCwgZW5kXSk7DQogICAgICAgICAgfQ0KICAgICAgICB9XQ0KICAgICAgfSwNCiAgICAgIC8vIOeKtuaAgeabtOaWsOWvueivneahhg0KICAgICAgc3RhdHVzT3BlbjogZmFsc2UsDQogICAgICBkYXNoYm9hcmRTdGF0czogew0KICAgICAgICAidG90YWwiOiAwLA0KICAgICAgICAiY29tcGxldGVkIjogMCwNCiAgICAgICAgImluUHJvZ3Jlc3MiOiAwLA0KICAgICAgICAib3ZlcmR1ZSI6IDANCiAgICAgIH0sDQogICAgICAvLyDmm7TmlLnlt6XnqIvluIjlr7nor53moYYNCiAgICAgIGNoYW5nZUVuZ2luZWVyT3BlbjogZmFsc2UsDQogICAgICAvLyDmm7TmlLnlt6XnqIvluIjooajljZUNCiAgICAgIGNoYW5nZUVuZ2luZWVyRm9ybTogew0KICAgICAgICBpZDogbnVsbCwNCiAgICAgICAgc2FtcGxlT3JkZXJDb2RlOiBudWxsLA0KICAgICAgICBjdXJyZW50RW5naW5lZXJOYW1lOiBudWxsLA0KICAgICAgICBvbGRFbmdpbmVlcklkOiBudWxsLA0KICAgICAgICBuZXdFbmdpbmVlcklkOiBudWxsLA0KICAgICAgICBzY2hlZHVsZWREYXRlOiBudWxsLA0KICAgICAgICBhZGp1c3RXb3JrU2NoZWR1bGU6IDANCiAgICAgIH0sDQogICAgICAvLyDmm7TmlLnlt6XnqIvluIjooajljZXmoKHpqowNCiAgICAgIGNoYW5nZUVuZ2luZWVyUnVsZXM6IHsNCiAgICAgICAgbmV3RW5naW5lZXJJZDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nlt6XnqIvluIgiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0sDQogICAgICAgIHNjaGVkdWxlZERhdGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup5pel5pyfIiwgdHJpZ2dlcjogImNoYW5nZSIgfQ0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgLy8g5bel56iL5biI6YCJ6aG5DQogICAgICBlbmdpbmVlck9wdGlvbnM6IFtdLA0KICAgICAgLy8g5om55qyh566h55CG55u45YWzDQogICAgICBiYXRjaE1hbmFnZW1lbnRPcGVuOiBmYWxzZSwNCiAgICAgIGN1cnJlbnRCYXRjaDogbnVsbCwNCiAgICAgIGhpc3RvcnlCYXRjaGVzOiBbXSwNCiAgICAgIGN1cnJlbnRCYXRjaEV4cGVyaW1lbnRzOiBbXSwNCiAgICAgIHNlbGVjdGVkT3JkZXJGb3JCYXRjaDogbnVsbCwNCiAgICAgIGN1cnJlbnRCYXRjaFJvdzogbnVsbCwgLy8g5b2T5YmN5om55qyh566h55CG55qE6KGM5pWw5o2uDQogICAgICAvLyDmibnmrKHor6bmg4Xlr7nor53moYYNCiAgICAgIGJhdGNoRGV0YWlsT3BlbjogZmFsc2UsDQogICAgICBiYXRjaERldGFpbERhdGE6IG51bGwsDQogICAgICAvLyDmt7vliqDlrp7pqozlr7nor53moYYNCiAgICAgIGFkZEV4cGVyaW1lbnRPcGVuOiBmYWxzZSwNCiAgICAgIGV4cGVyaW1lbnRGb3JtOiB7DQogICAgICAgIGV4cGVyaW1lbnRDb2RlOiAnJywNCiAgICAgICAgZXhwZXJpbWVudE5vdGU6ICcnDQogICAgICB9LA0KICAgICAgZXhwZXJpbWVudFJ1bGVzOiB7DQogICAgICAgIGV4cGVyaW1lbnRDb2RlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWunumqjOe8luWPt+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0NCiAgICAgIH0sDQogICAgICAvLyDnu5PmnZ/mibnmrKHlr7nor53moYYNCiAgICAgIGZpbmlzaEJhdGNoT3BlbjogZmFsc2UsDQogICAgICBmaW5pc2hCYXRjaEZvcm06IHsNCiAgICAgICAgcXVhbGl0eUV2YWx1YXRpb246ICcnLA0KICAgICAgICByZW1hcms6ICcnDQogICAgICB9LA0KICAgICAgLy8g5pyq5p2l5pel5pyf6YCJ5oup5Zmo6YWN572uDQogICAgICBmdXR1cmVEYXRlUGlja2VyT3B0aW9uczogew0KICAgICAgICBkaXNhYmxlZERhdGUodGltZSkgew0KICAgICAgICAgIHJldHVybiB0aW1lLmdldFRpbWUoKSA8IERhdGUubm93KCkgLSA4LjY0ZTc7IC8vIOemgeeUqOS7iuWkqeS5i+WJjeeahOaXpeacnw0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgZW5naW5lZXJTZWxlY3RUeXBlOiAnc3BlY2lmaWVkJywNCiAgICAgIHNlYXJjaGVkRW5naW5lZXJzOiBbXSwNCiAgICAgIC8vIOacquWIhumFjeaJk+agt+WNleWRiuitpg0KICAgICAgdW5hc3NpZ25lZE9yZGVyczogW10sDQogICAgICB1bmFzc2lnbmVkUXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDgNCiAgICAgIH0sDQogICAgICB1bmFzc2lnbmVkVG90YWw6IDAsDQogICAgICAvLyDmnKrliIbphY3pnaLmnb/mipjlj6DnirbmgIENCiAgICAgIGlzVW5hc3NpZ25lZFBhbmVsQ29sbGFwc2VkOiB0cnVlLA0KICAgICAgLy8g5a6M5oiQ5Lu75Yqh5a+56K+d5qGGDQogICAgICBmaW5pc2hUYXNrT3BlbjogZmFsc2UsDQogICAgICBmaW5pc2hUYXNrTG9hZGluZzogZmFsc2UsDQogICAgICBmaW5pc2hUYXNrRm9ybTogew0KICAgICAgICBsYWJvcmF0b3J5Q29kZTogJycsDQogICAgICAgIHJlbWFyazogJycNCiAgICAgIH0sDQogICAgICBmaW5pc2hUYXNrUnVsZXM6IHsNCiAgICAgICAgbGFib3JhdG9yeUNvZGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5a6e6aqM5a6k57yW56CB5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIGN1cnJlbnRGaW5pc2hSb3c6IG51bGwsDQogICAgICAvLyDmm7TmlrDlrp7pqozlrqTnvJbnoIHlr7nor53moYYNCiAgICAgIHVwZGF0ZUxhYm9yYXRvcnlDb2RlT3BlbjogZmFsc2UsDQogICAgICB1cGRhdGVMYWJvcmF0b3J5Q29kZUxvYWRpbmc6IGZhbHNlLA0KICAgICAgdXBkYXRlTGFib3JhdG9yeUNvZGVGb3JtOiB7DQogICAgICAgIGxhYm9yYXRvcnlDb2RlOiAnJywNCiAgICAgICAgcmVtYXJrOiAnJw0KICAgICAgfSwNCiAgICAgIHVwZGF0ZUxhYm9yYXRvcnlDb2RlUnVsZXM6IHsNCiAgICAgICAgbGFib3JhdG9yeUNvZGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5a6e6aqM5a6k57yW56CB5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIGN1cnJlbnRVcGRhdGVMYWJvcmF0b3J5Q29kZVJvdzogbnVsbCwNCiAgICAgIC8vIOWvvOWHuumAieaLqeWvueivneahhg0KICAgICAgZXhwb3J0RGlhbG9nT3BlbjogZmFsc2UsDQogICAgICBleHBvcnRPcHRpb25zOiBbXSwNCiAgICAgIGV4cG9ydExvYWRpbmc6IGZhbHNlLA0KICAgICAgcmVhZG9ubHk6IHRydWUsDQogICAgICAvLyDpobnnm67or6bmg4Xnm7jlhbMNCiAgICAgIGN1cnJlbnRQcm9qZWN0VHlwZTogbnVsbCwNCiAgICAgIGNvbmZpcm1JdGVtQ29kZXM6IFtdLA0KICAgICAgY3VzdG9tZXJPcHRpb25zOiBbXSwNCiAgICAgIGl0ZW1OYW1lczogW10sDQogICAgICAvLyDpqbPlm57lr7nor53moYYNCiAgICAgIHJlamVjdERpYWxvZ09wZW46IGZhbHNlLA0KICAgICAgcmVqZWN0TG9hZGluZzogZmFsc2UsDQogICAgICByZWplY3RGb3JtOiB7DQogICAgICAgIHNhbXBsZU9yZGVyQ29kZTogJycsDQogICAgICAgIHJlamVjdFJlYXNvbjogJycNCiAgICAgIH0sDQogICAgICAvLyDpgL7mnJ/mk43kvZzlr7nor53moYYNCiAgICAgIG92ZXJkdWVPcGVyYXRpb25PcGVuOiBmYWxzZSwNCiAgICAgIG92ZXJkdWVPcGVyYXRpb25Mb2FkaW5nOiBmYWxzZSwNCiAgICAgIG92ZXJkdWVPcGVyYXRpb25Gb3JtOiB7DQogICAgICAgIHNhbXBsZU9yZGVyQ29kZTogJycsDQogICAgICAgIGV4cGVjdGVkU2FtcGxlVGltZTogJycsDQogICAgICAgIHJlYXNvbkZvck5vU2FtcGxlOiAnJywNCiAgICAgICAgc29sdXRpb246ICcnDQogICAgICB9LA0KICAgICAgY3VycmVudE92ZXJkdWVSb3c6IG51bGwsDQogICAgICByZWplY3RSdWxlczogew0KICAgICAgICByZWplY3RSZWFzb246IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6amz5Zue55CG55Sx5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIGN1cnJlbnRSZWplY3RSb3c6IG51bGwNCiAgICB9Ow0KICB9LA0KICBjb21wdXRlZDogew0KICAgIC8qKiDorqHnrpfmmK/lkKblj6/ku6XnvJbovpHmibnmrKEgLSDlj6rmnInnirbmgIHkuLonMSco6L+b6KGM5LitKeeahOaJk+agt+WNleaJjeWPr+S7pee8lui+kSAqLw0KICAgIGNhbkVkaXRCYXRjaCgpIHsNCiAgICAgIHJldHVybiB0aGlzLnNlbGVjdGVkT3JkZXJGb3JCYXRjaCAmJiB0aGlzLnNlbGVjdGVkT3JkZXJGb3JCYXRjaC5jb21wbGV0aW9uU3RhdHVzID09PSAxOw0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICAvLyDorr7nva7pu5jorqTml6XmnJ/ojIPlm7TkuLrlvZPlpKkNCiAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCk7DQogICAgY29uc3QgdG9kYXlTdHIgPSB0b2RheS5nZXRGdWxsWWVhcigpICsgJy0nICsgU3RyaW5nKHRvZGF5LmdldE1vbnRoKCkgKyAxKS5wYWRTdGFydCgyLCAnMCcpICsgJy0nICsgU3RyaW5nKHRvZGF5LmdldERhdGUoKSkucGFkU3RhcnQoMiwgJzAnKTsNCiAgICB0aGlzLmRhdGVSYW5nZSA9IFt0b2RheVN0ciwgdG9kYXlTdHJdOw0KDQogICAgdGhpcy5nZXRMaXN0KCk7DQogICAgY3VzdG9tZXJCYXNlQWxsKCkudGhlbihyZXM9PiB0aGlzLmN1c3RvbWVyT3B0aW9ucyA9IHJlcykNCg0KICAgIHRoaXMuZ2V0RGljdHMoIkRZRF9HQ1NaVCIpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnN0YXR1c09wdGlvbnMgPSByZXNwb25zZS5kYXRhOw0KICAgIH0pOw0KICAgIHRoaXMuZ2V0RGljdHMoIkNVU1RPTUVSX1NFUlZJQ0VfTU9ERSIpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgdGhpcy5zZXJ2aWNlTW9kZU9wdGlvbnMgPSByZXNwb25zZS5kYXRhOw0KICAgIH0pOw0KICAgIHRoaXMuZ2V0RGljdHMoInByb2plY3RfbnJ3X2R5bGIiKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgIHRoaXMuZHlsYk9wdGlvbnMgPSByZXNwb25zZS5kYXRhOw0KICAgIH0pOw0KICAgIHRoaXMubG9hZERhc2hib2FyZFN0YXRzKCk7DQogICAgLy8g6I635Y+W56CU5Y+R6YOo6YOo6Zeo5YiX6KGoDQogICAgZ2V0UmVzZWFyY2hEZXBhcnRtZW50cygpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgdGhpcy5yZXNlYXJjaERlcHREYXRhcyA9IHRoaXMuaGFuZGxlVHJlZShyZXNwb25zZS5kYXRhLCAiZGVwdElkIik7DQogICAgfSk7DQogICAgLy8g6I635Y+W5pyq5YiG6YWN5omT5qC35Y2VDQogICAgdGhpcy5oYW5kbGVVbmFzc2lnbmVkT3JkZXJzKCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog6K6h566X5Lqn5ZOBL+mhueebruetiee6pyAqLw0KICAgIGdldFByb2plY3RMZXZlbChyb3cpIHsNCiAgICAgIGNvbnN0IGN1c3RvbWVyTGV2ZWwgPSByb3cuY3VzdG9tZXJMZXZlbCB8fCAnJzsNCiAgICAgIGNvbnN0IHByb2plY3RMZXZlbCA9IHJvdy5wcm9qZWN0TGV2ZWwgfHwgJyc7DQoNCiAgICAgIC8vIOWmguaenCBwcm9qZWN0TGV2ZWwg5piv56m65a2X56ym5Liy5oiWICIvIiDlsLHkuI3nm7jliqANCiAgICAgIGlmIChwcm9qZWN0TGV2ZWwgPT09ICcnIHx8IHByb2plY3RMZXZlbCA9PT0gJy8nKSB7DQogICAgICAgIHJldHVybiBjdXN0b21lckxldmVsOw0KICAgICAgfQ0KDQogICAgICByZXR1cm4gY3VzdG9tZXJMZXZlbCArIHByb2plY3RMZXZlbDsNCiAgICB9LA0KICAgIC8qKiDmn6Xor6Llt6XnqIvluIjmiZPmoLfljZXlhbPogZTliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgbGV0IHBhcmFtcyA9IHsgLi4udGhpcy5xdWVyeVBhcmFtcyB9Ow0KICAgICAgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UocGFyYW1zLCB0aGlzLnNjaGVkdWxlZERhdGVSYW5nZSwnU2NoZWR1bGVkRGF0ZScpDQogICAgICBwYXJhbXMgPSB0aGlzLmFkZERhdGVSYW5nZShwYXJhbXMsIHRoaXMuc3RhcnREYXRlUmFuZ2UsJ1N0YXJ0RGF0ZScpDQogICAgICBwYXJhbXMgPSB0aGlzLmFkZERhdGVSYW5nZShwYXJhbXMsIHRoaXMuYWN0dWFsU3RhcnRUaW1lUmFuZ2UsJ0FjdHVhbFN0YXJ0VGltZScpDQogICAgICBwYXJhbXMgPSB0aGlzLmFkZERhdGVSYW5nZShwYXJhbXMsIHRoaXMuYWN0dWFsRmluaXNoVGltZVJhbmdlLCdBY3R1YWxGaW5pc2hUaW1lJykNCiAgICAgIC8vIOa4heepuuS5i+WJjeeahOaXpeacn+iMg+WbtOWPguaVsO+8jOagueaNruW9k+WJjeeKtuaAgemHjeaWsOiuvue9rg0KICAgICAgZGVsZXRlIHBhcmFtcy5iZWdpbkRhdGVSYW5nZTsNCiAgICAgIGRlbGV0ZSBwYXJhbXMuZW5kRGF0ZVJhbmdlOw0KICAgICAgLy8g5re75Yqg5pel5pyf6IyD5Zu0562b6YCJ5Y+C5pWwDQogICAgICBpZiAodGhpcy5kYXRlUmFuZ2UgJiYgdGhpcy5kYXRlUmFuZ2UubGVuZ3RoID09PSAyKSB7DQogICAgICAgIHBhcmFtcy5iZWdpbkRhdGVSYW5nZSA9IHRoaXMuZGF0ZVJhbmdlWzBdOw0KICAgICAgICBwYXJhbXMuZW5kRGF0ZVJhbmdlID0gdGhpcy5kYXRlUmFuZ2VbMV07DQogICAgICB9DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgcGFyYW1zLmFzc29jaWF0aW9uU3RhdHVzID0gMQ0KICAgICAgbGlzdEVuZ2luZWVyU2FtcGxlT3JkZXIocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5lbmdpbmVlclNhbXBsZU9yZGVyTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICB1c2VySWQ6IG51bGwsDQogICAgICAgIHNhbXBsZU9yZGVyQ29kZTogbnVsbCwNCiAgICAgICAgc2VydmljZU1vZGU6IG51bGwsDQogICAgICAgIGRpZmZpY3VsdHlMZXZlbElkOiBudWxsLA0KICAgICAgICBjb21wbGV0aW9uU3RhdHVzOiBudWxsLA0KICAgICAgICBzY2hlZHVsZWREYXRlOiBudWxsLA0KICAgICAgICBhY3R1YWxTdGFydFRpbWU6IG51bGwsDQogICAgICAgIGFjdHVhbEZpbmlzaFRpbWU6IG51bGwsDQogICAgICAgIGFjdHVhbE1hbkhvdXJzOiBudWxsLA0KICAgICAgICBlc3RpbWF0ZWRNYW5Ib3VyczogbnVsbCwNCiAgICAgICAgc3RhbmRhcmRNYW5Ib3VyczogbnVsbCwNCiAgICAgICAgcXVhbGl0eUV2YWx1YXRpb246IG51bGwsDQogICAgICAgIGlzTG9ja2VkOiAwLA0KICAgICAgICBzdGFydERhdGU6IG51bGwsDQogICAgICAgIGVuZERhdGU6IG51bGwsDQogICAgICAgIGNoZWNrVHlwZTogbnVsbCwNCiAgICAgICAgc2FtcGxlT3JkZXJSZW1hcms6IG51bGwsDQogICAgICAgIGNyZWF0ZUJ5OiBudWxsLA0KICAgICAgICBjcmVhdGVUaW1lOiBudWxsLA0KICAgICAgICB1cGRhdGVCeTogbnVsbCwNCiAgICAgICAgdXBkYXRlVGltZTogbnVsbCwNCiAgICAgICAgcmVtYXJrOiBudWxsDQogICAgICB9Ow0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsNCiAgICB9LA0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLnVuYXNzaWduZWRRdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgdGhpcy5sb2FkRGFzaGJvYXJkU3RhdHMoKTsNCiAgICAgIHRoaXMuaGFuZGxlVW5hc3NpZ25lZE9yZGVycygpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdIC8vIOmHjee9ruaXpeacn+iMg+WbtA0KICAgICAgdGhpcy5zY2hlZHVsZWREYXRlUmFuZ2UgPSBbXQ0KICAgICAgdGhpcy5zdGFydERhdGVSYW5nZSA9IFtdDQogICAgICB0aGlzLmFjdHVhbFN0YXJ0VGltZVJhbmdlID0gW10NCiAgICAgIHRoaXMuYWN0dWFsRmluaXNoVGltZVJhbmdlID0gW10NCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uaWQpDQogICAgICB0aGlzLnNlbGVjdGVkUm93cyA9IHNlbGVjdGlvbg0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDlt6XnqIvluIjmiZPmoLfljZXlhbPogZQiOw0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIGNvbnN0IGlkID0gcm93LmlkIHx8IHRoaXMuaWRzDQogICAgICBnZXRFbmdpbmVlclNhbXBsZU9yZGVyKGlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnlt6XnqIvluIjmiZPmoLfljZXlhbPogZQiOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uaWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgdXBkYXRlRW5naW5lZXJTYW1wbGVPcmRlcih0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICAgIHRoaXMubG9hZERhc2hib2FyZFN0YXRzKCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgYWRkRW5naW5lZXJTYW1wbGVPcmRlcih0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICAgIHRoaXMubG9hZERhc2hib2FyZFN0YXRzKCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IGlkcyA9IHJvdy5pZCB8fCB0aGlzLmlkczsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOW3peeoi+W4iOaJk+agt+WNleWFs+iBlOe8luWPt+S4uiInICsgcm93LnNhbXBsZU9yZGVyQ29kZSArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgIHJldHVybiBkZWxFbmdpbmVlclNhbXBsZU9yZGVyKGlkcyk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICAgIHRoaXMubG9hZERhc2hib2FyZFN0YXRzKCk7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7IH0pOw0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIC8vIOmHjee9ruWvvOWHuumAiemhueW5tuaYvuekuumAieaLqeWvueivneahhg0KICAgICAgdGhpcy5leHBvcnRPcHRpb25zID0gW107DQogICAgICB0aGlzLmV4cG9ydERpYWxvZ09wZW4gPSB0cnVlOw0KICAgIH0sDQogICAgLyoqIOehruiupOWvvOWHuuaTjeS9nCAqLw0KICAgIGNvbmZpcm1FeHBvcnQoKSB7DQogICAgICBpZiAodGhpcy5leHBvcnRPcHRpb25zLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+iHs+WwkemAieaLqeS4gOenjeWvvOWHuuexu+WeiycpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLmV4cG9ydExvYWRpbmcgPSB0cnVlOw0KICAgICAgdGhpcy5leGVjdXRlRXhwb3J0cygwKTsNCiAgICB9LA0KDQogICAgLyoqIOS4suihjOaJp+ihjOWvvOWHuuaTjeS9nCAqLw0KICAgIGV4ZWN1dGVFeHBvcnRzKGluZGV4KSB7DQogICAgICBpZiAoaW5kZXggPj0gdGhpcy5leHBvcnRPcHRpb25zLmxlbmd0aCkgew0KICAgICAgICAvLyDmiYDmnInlr7zlh7rlrozmiJANCiAgICAgICAgdGhpcy5leHBvcnRMb2FkaW5nID0gZmFsc2U7DQogICAgICAgIHRoaXMuZXhwb3J0RGlhbG9nT3BlbiA9IGZhbHNlOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOWvvOWHuuWujOaIkO+8jOWFseWvvOWHuiR7dGhpcy5leHBvcnRPcHRpb25zLmxlbmd0aH3kuKrmlofku7ZgKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBjb25zdCBvcHRpb24gPSB0aGlzLmV4cG9ydE9wdGlvbnNbaW5kZXhdOw0KICAgICAgY29uc3QgYXNzb2NpYXRpb25TdGF0dXMgPSBvcHRpb24gPT09ICdhc3NpZ25lZCcgPyAxIDogMDsNCiAgICAgIGNvbnN0IHR5cGVOYW1lID0gb3B0aW9uID09PSAnYXNzaWduZWQnID8gJ+W3suWIhumFjScgOiAn5pyq5YiG6YWNJzsNCg0KICAgICAgdGhpcy5kb0V4cG9ydChhc3NvY2lhdGlvblN0YXR1cywgb3B0aW9uKS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGAke3R5cGVOYW1lfeaJk+agt+WNleWvvOWHuuaIkOWKn2ApOw0KICAgICAgICAvLyDnu6fnu63lr7zlh7rkuIvkuIDkuKoNCiAgICAgICAgdGhpcy5leGVjdXRlRXhwb3J0cyhpbmRleCArIDEpOw0KICAgICAgfSkuY2F0Y2goKGVycm9yKSA9PiB7DQogICAgICAgIHRoaXMuZXhwb3J0TG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGAke3R5cGVOYW1lfeaJk+agt+WNleWvvOWHuuWksei0pTogJHtlcnJvci5tZXNzYWdlIHx8IGVycm9yfWApOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5omn6KGM5a+85Ye65pON5L2cICovDQogICAgZG9FeHBvcnQoYXNzb2NpYXRpb25TdGF0dXMsIGV4cG9ydFR5cGUpIHsNCiAgICAgIGxldCBwYXJhbXMgPSB7IC4uLnRoaXMucXVlcnlQYXJhbXMgfTsNCiAgICAgIHBhcmFtcyA9IHRoaXMuYWRkRGF0ZVJhbmdlKHBhcmFtcywgdGhpcy5zY2hlZHVsZWREYXRlUmFuZ2UsJ1NjaGVkdWxlZERhdGUnKQ0KICAgICAgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UocGFyYW1zLCB0aGlzLnN0YXJ0RGF0ZVJhbmdlLCdTdGFydERhdGUnKQ0KICAgICAgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UocGFyYW1zLCB0aGlzLmFjdHVhbFN0YXJ0VGltZVJhbmdlLCdBY3R1YWxTdGFydFRpbWUnKQ0KICAgICAgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UocGFyYW1zLCB0aGlzLmFjdHVhbEZpbmlzaFRpbWVSYW5nZSwnQWN0dWFsRmluaXNoVGltZScpDQogICAgICAvLyDmuIXnqbrkuYvliY3nmoTml6XmnJ/ojIPlm7Tlj4LmlbDvvIzmoLnmja7lvZPliY3nirbmgIHph43mlrDorr7nva4NCiAgICAgIGRlbGV0ZSBwYXJhbXMuYmVnaW5EYXRlUmFuZ2U7DQogICAgICBkZWxldGUgcGFyYW1zLmVuZERhdGVSYW5nZTsNCiAgICAgIC8vIOa3u+WKoOaXpeacn+iMg+WbtOetm+mAieWPguaVsA0KICAgICAgaWYgKHRoaXMuZGF0ZVJhbmdlICYmIHRoaXMuZGF0ZVJhbmdlLmxlbmd0aCA9PT0gMikgew0KICAgICAgICBwYXJhbXMuYmVnaW5EYXRlUmFuZ2UgPSB0aGlzLmRhdGVSYW5nZVswXTsNCiAgICAgICAgcGFyYW1zLmVuZERhdGVSYW5nZSA9IHRoaXMuZGF0ZVJhbmdlWzFdOw0KICAgICAgfQ0KICAgICAgcGFyYW1zLmFzc29jaWF0aW9uU3RhdHVzID0gYXNzb2NpYXRpb25TdGF0dXM7DQogICAgICBwYXJhbXMuZXhwb3J0VHlwZSA9IGV4cG9ydFR5cGU7DQogICAgICByZXR1cm4gZXhwb3J0RW5naW5lZXJTYW1wbGVPcmRlcihwYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmRvd25sb2FkKHJlc3BvbnNlLm1zZyk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmiafooYzlr7zlh7rmiZPmoLfljZXvvIjlt7LliIbphY0v5pyq5YiG6YWN77yJICovDQogICAgZG9FeHBvcnRTYW1wbGVPcmRlcigpIHsNCiAgICAgIGxldCBwYXJhbXMgPSB7IC4uLnRoaXMucXVlcnlQYXJhbXMgfTsNCiAgICAgIHBhcmFtcyA9IHRoaXMuYWRkRGF0ZVJhbmdlKHBhcmFtcywgdGhpcy5zY2hlZHVsZWREYXRlUmFuZ2UsJ1NjaGVkdWxlZERhdGUnKQ0KICAgICAgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UocGFyYW1zLCB0aGlzLnN0YXJ0RGF0ZVJhbmdlLCdTdGFydERhdGUnKQ0KICAgICAgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UocGFyYW1zLCB0aGlzLmFjdHVhbFN0YXJ0VGltZVJhbmdlLCdBY3R1YWxTdGFydFRpbWUnKQ0KICAgICAgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UocGFyYW1zLCB0aGlzLmFjdHVhbEZpbmlzaFRpbWVSYW5nZSwnQWN0dWFsRmluaXNoVGltZScpDQogICAgICAvLyDmuIXnqbrkuYvliY3nmoTml6XmnJ/ojIPlm7Tlj4LmlbDvvIzmoLnmja7lvZPliY3nirbmgIHph43mlrDorr7nva4NCiAgICAgIGRlbGV0ZSBwYXJhbXMuYmVnaW5EYXRlUmFuZ2U7DQogICAgICBkZWxldGUgcGFyYW1zLmVuZERhdGVSYW5nZTsNCiAgICAgIC8vIOa3u+WKoOaXpeacn+iMg+WbtOetm+mAieWPguaVsA0KICAgICAgaWYgKHRoaXMuZGF0ZVJhbmdlICYmIHRoaXMuZGF0ZVJhbmdlLmxlbmd0aCA9PT0gMikgew0KICAgICAgICBwYXJhbXMuYmVnaW5EYXRlUmFuZ2UgPSB0aGlzLmRhdGVSYW5nZVswXTsNCiAgICAgICAgcGFyYW1zLmVuZERhdGVSYW5nZSA9IHRoaXMuZGF0ZVJhbmdlWzFdOw0KICAgICAgfQ0KICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omA5pyJ77yI5bey5YiG6YWNL+acquWIhumFje+8ieaJk+agt+WNleWFs+iBlOaVsOaNru+8nycsICLmj5DnpLoiLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsDQogICAgICAgIHR5cGU6ICJ3YXJuaW5nIg0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZXhwb3J0TG9hZGluZyA9IHRydWU7DQogICAgICAgIHJldHVybiBleHBvcnRFbmdpbmVlclNhbXBsZU9yZGVyKHBhcmFtcyk7DQogICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5kb3dubG9hZChyZXNwb25zZS5tc2cpOw0KICAgICAgICB0aGlzLmV4cG9ydExvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLlr7zlh7rmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy5leHBvcnRMb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDngrnlh7si5byA5aeLIuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVN0YXJ0KHJvdykgew0KICAgICAgdGhpcy4kY29uZmlybSgn56Gu5a6a6KaB5bCG5omT5qC35Y2V77yaJyArIHJvdy5zYW1wbGVPcmRlckNvZGUgKyAn5qCH6K6w5Li66L+b6KGM5Lit5ZCX77yfJywgJ+aPkOekuicsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdXBkYXRlU2FtcGxlT3JkZXJTdGF0dXMoew0KICAgICAgICAgIGlkOiByb3cuaWQsDQogICAgICAgICAgc3RhdHVzOiAnMScNCiAgICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCfmiZPmoLfljZXlt7Lorr7nva7kuLrlt7LlvIDlp4snKTsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB0aGlzLmxvYWREYXNoYm9hcmRTdGF0cygpOw0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOS4i+i9veaJk+agt+WNleaTjeS9nCAqLw0KICAgIGhhbmRsZURvd25sb2FkU2FtcGxlT3JkZXIocm93KSB7DQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTlr7zlh7rmiZPmoLfljZU/JywgIuaPkOekuiIsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgdHlwZTogIndhcm5pbmciDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5leHBvcnRMb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgcmV0dXJuIGV4cG9ydE5yd0l0ZW0oe2l0ZW1JZDogcm93Lml0ZW1JZCxwcm9qZWN0SWQ6IHJvdy5wcm9qZWN0SWR9KQ0KICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZG93bmxvYWQocmVzcG9uc2UubXNnKTsNCiAgICAgICAgdGhpcy5leHBvcnRMb2FkaW5nID0gZmFsc2U7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5om56YeP5a+85Ye65omT5qC35Y2V5Lu75Yqh5pON5L2cICovDQogICAgaGFuZGxlQmF0Y2hFeHBvcnROcncoKSB7DQogICAgICBpZiAodGhpcy5zZWxlY3RlZFJvd3MubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7fpgInmi6nopoHlr7zlh7rnmoTmiZPmoLfljZXku7vliqEiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDmnoTpgKDmibnph4/lr7zlh7rmlbDmja7vvIzkvKDpgJJpdGVtSWTlkoxwcm9qZWN0SWQNCiAgICAgIGNvbnN0IGV4cG9ydERhdGEgPSB0aGlzLnNlbGVjdGVkUm93cy5tYXAocm93ID0+ICh7DQogICAgICAgIGl0ZW1JZDogcm93Lml0ZW1JZCwNCiAgICAgICAgcHJvamVjdElkOiByb3cucHJvamVjdElkDQogICAgICB9KSk7DQoNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWvvOWHuuaJgOmAieaJk+agt+WNleS7u+WKoeeahOWGheWuueeJqeaVsOaNru+8nycsICLmj5DnpLoiLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsDQogICAgICAgIHR5cGU6ICJ3YXJuaW5nIg0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZXhwb3J0TG9hZGluZyA9IHRydWU7DQogICAgICAgIHJldHVybiBleHBvcnRNdWx0aXBsZU5ydyhleHBvcnREYXRhKTsNCiAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmRvd25sb2FkKHJlc3BvbnNlLm1zZyk7DQogICAgICAgIHRoaXMuZXhwb3J0TG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuWvvOWHuuaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLmV4cG9ydExvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOeCueWHuyLlrozmiJAi5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRmluaXNoKHJvdykgew0KICAgICAgdGhpcy5jdXJyZW50RmluaXNoUm93ID0gcm93Ow0KICAgICAgdGhpcy5maW5pc2hUYXNrRm9ybS5sYWJvcmF0b3J5Q29kZSA9ICcnOw0KICAgICAgdGhpcy5maW5pc2hUYXNrRm9ybS5yZW1hcmsgPSAnJzsNCiAgICAgIHRoaXMuZmluaXNoVGFza09wZW4gPSB0cnVlOw0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLiRyZWZzLmZpbmlzaFRhc2tGb3JtLmNsZWFyVmFsaWRhdGUoKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOehruiupOWujOaIkOS7u+WKoSAqLw0KICAgIGNvbmZpcm1GaW5pc2hUYXNrKCkgew0KICAgICAgdGhpcy4kcmVmcy5maW5pc2hUYXNrRm9ybS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIHRoaXMuZmluaXNoVGFza0xvYWRpbmcgPSB0cnVlOw0KICAgICAgICAgIHVwZGF0ZVNhbXBsZU9yZGVyU3RhdHVzKHsNCiAgICAgICAgICAgIGlkOiB0aGlzLmN1cnJlbnRGaW5pc2hSb3cuaWQsDQogICAgICAgICAgICBzdGF0dXM6ICcyJywNCiAgICAgICAgICAgIGxhYm9yYXRvcnlDb2RlOiB0aGlzLmZpbmlzaFRhc2tGb3JtLmxhYm9yYXRvcnlDb2RlLA0KICAgICAgICAgICAgcmVtYXJrOiB0aGlzLmZpbmlzaFRhc2tGb3JtLnJlbWFyaw0KICAgICAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgdGhpcy5maW5pc2hUYXNrTG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgdGhpcy5maW5pc2hUYXNrT3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCfmiZPmoLfljZXlt7Lorr7nva7kuLrlt7LlrozmiJAnKTsNCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgdGhpcy5sb2FkRGFzaGJvYXJkU3RhdHMoKTsNCiAgICAgICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgICB0aGlzLmZpbmlzaFRhc2tMb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOS7juaJueasoeeuoeeQhuWvueivneahhuWujOaIkOS7u+WKoSAqLw0KICAgIGhhbmRsZUZpbmlzaEZyb21CYXRjaCgpIHsNCiAgICAgIHRoaXMuY3VycmVudEZpbmlzaFJvdyA9IHRoaXMuY3VycmVudEJhdGNoUm93Ow0KICAgICAgdGhpcy5maW5pc2hUYXNrRm9ybS5sYWJvcmF0b3J5Q29kZSA9ICcnOw0KICAgICAgdGhpcy5maW5pc2hUYXNrRm9ybS5yZW1hcmsgPSAnJzsNCiAgICAgIHRoaXMuZmluaXNoVGFza09wZW4gPSB0cnVlOw0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLiRyZWZzLmZpbmlzaFRhc2tGb3JtLmNsZWFyVmFsaWRhdGUoKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOeCueWHuyLmm7TmlrDlrp7pqozlrqTnvJbnoIEi5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVXBkYXRlTGFib3JhdG9yeUNvZGUocm93KSB7DQogICAgICB0aGlzLmN1cnJlbnRVcGRhdGVMYWJvcmF0b3J5Q29kZVJvdyA9IHJvdzsNCiAgICAgIC8vIOWbnuaYvuW9k+WJjeeahOWunumqjOWupOe8lueggeWSjOWkh+azqA0KICAgICAgdGhpcy51cGRhdGVMYWJvcmF0b3J5Q29kZUZvcm0ubGFib3JhdG9yeUNvZGUgPSByb3cubGFib3JhdG9yeUNvZGUgfHwgJyc7DQogICAgICB0aGlzLnVwZGF0ZUxhYm9yYXRvcnlDb2RlRm9ybS5yZW1hcmsgPSByb3cucmVtYXJrIHx8ICcnOw0KICAgICAgdGhpcy51cGRhdGVMYWJvcmF0b3J5Q29kZU9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLiRyZWZzLnVwZGF0ZUxhYm9yYXRvcnlDb2RlRm9ybS5jbGVhclZhbGlkYXRlKCk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDnoa7orqTmm7TmlrDlrp7pqozlrqTnvJbnoIEgKi8NCiAgICBjb25maXJtVXBkYXRlTGFib3JhdG9yeUNvZGUoKSB7DQogICAgICB0aGlzLiRyZWZzLnVwZGF0ZUxhYm9yYXRvcnlDb2RlRm9ybS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIHRoaXMudXBkYXRlTGFib3JhdG9yeUNvZGVMb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgICB1cGRhdGVTYW1wbGVPcmRlclN0YXR1cyh7DQogICAgICAgICAgICBpZDogdGhpcy5jdXJyZW50VXBkYXRlTGFib3JhdG9yeUNvZGVSb3cuaWQsDQogICAgICAgICAgICBzdGF0dXM6IHRoaXMuY3VycmVudFVwZGF0ZUxhYm9yYXRvcnlDb2RlUm93LmNvbXBsZXRpb25TdGF0dXMsIC8vIOS/neaMgeW9k+WJjeeKtuaAgQ0KICAgICAgICAgICAgbGFib3JhdG9yeUNvZGU6IHRoaXMudXBkYXRlTGFib3JhdG9yeUNvZGVGb3JtLmxhYm9yYXRvcnlDb2RlLA0KICAgICAgICAgICAgcmVtYXJrOiB0aGlzLnVwZGF0ZUxhYm9yYXRvcnlDb2RlRm9ybS5yZW1hcmsNCiAgICAgICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgIHRoaXMudXBkYXRlTGFib3JhdG9yeUNvZGVMb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLnVwZGF0ZUxhYm9yYXRvcnlDb2RlT3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCflrp7pqozlrqTnvJbnoIHmm7TmlrDmiJDlip8nKTsNCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgdGhpcy5sb2FkRGFzaGJvYXJkU3RhdHMoKTsNCiAgICAgICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgICB0aGlzLnVwZGF0ZUxhYm9yYXRvcnlDb2RlTG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDngrnlh7si6amz5ZueIuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVJlamVjdChyb3cpIHsNCiAgICAgIHRoaXMuY3VycmVudFJlamVjdFJvdyA9IHJvdzsNCiAgICAgIHRoaXMucmVqZWN0Rm9ybS5zYW1wbGVPcmRlckNvZGUgPSByb3cuc2FtcGxlT3JkZXJDb2RlOw0KICAgICAgdGhpcy5yZWplY3RGb3JtLnJlamVjdFJlYXNvbiA9ICcnOw0KICAgICAgdGhpcy5yZWplY3REaWFsb2dPcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy4kcmVmcy5yZWplY3RGb3JtLmNsZWFyVmFsaWRhdGUoKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOehruiupOmps+WbniAqLw0KICAgIGNvbmZpcm1SZWplY3QoKSB7DQogICAgICB0aGlzLiRyZWZzLnJlamVjdEZvcm0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICB0aGlzLnJlamVjdExvYWRpbmcgPSB0cnVlOw0KICAgICAgICAgIHVwZGF0ZVNhbXBsZU9yZGVyU3RhdHVzKHsNCiAgICAgICAgICAgIGlkOiB0aGlzLmN1cnJlbnRSZWplY3RSb3cuaWQsDQogICAgICAgICAgICBzdGF0dXM6ICczJywNCiAgICAgICAgICAgIHJlamVjdFJlYXNvbjogdGhpcy5yZWplY3RGb3JtLnJlamVjdFJlYXNvbg0KICAgICAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgdGhpcy5yZWplY3RMb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLnJlamVjdERpYWxvZ09wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygn5omT5qC35Y2V5bey6amz5ZueJyk7DQogICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIHRoaXMubG9hZERhc2hib2FyZFN0YXRzKCk7DQogICAgICAgICAgICAvLyDlpoLmnpzmmK/mnKrliIbphY3miZPmoLfljZXnmoTpqbPlm57vvIzkuZ/pnIDopoHliLfmlrDmnKrliIbphY3liJfooagNCiAgICAgICAgICAgIHRoaXMuaGFuZGxlVW5hc3NpZ25lZE9yZGVycygpOw0KICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMucmVqZWN0TG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDngrnlh7si6YC+5pyf5pON5L2cIuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZU92ZXJkdWVPcGVyYXRpb24ocm93KSB7DQogICAgICB0aGlzLmN1cnJlbnRPdmVyZHVlUm93ID0gcm93Ow0KICAgICAgdGhpcy5vdmVyZHVlT3BlcmF0aW9uRm9ybS5zYW1wbGVPcmRlckNvZGUgPSByb3cuc2FtcGxlT3JkZXJDb2RlOw0KICAgICAgdGhpcy5vdmVyZHVlT3BlcmF0aW9uRm9ybS5leHBlY3RlZFNhbXBsZVRpbWUgPSByb3cuZXhwZWN0ZWRTYW1wbGVUaW1lOw0KICAgICAgdGhpcy5vdmVyZHVlT3BlcmF0aW9uRm9ybS5yZWFzb25Gb3JOb1NhbXBsZSA9IHJvdy5yZWFzb25Gb3JOb1NhbXBsZTsNCiAgICAgIHRoaXMub3ZlcmR1ZU9wZXJhdGlvbkZvcm0uc29sdXRpb24gPSByb3cuc29sdXRpb247DQogICAgICB0aGlzLm92ZXJkdWVPcGVyYXRpb25PcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy4kcmVmcy5vdmVyZHVlT3BlcmF0aW9uRm9ybS5jbGVhclZhbGlkYXRlKCk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDnoa7orqTpgL7mnJ/mk43kvZwgKi8NCiAgICBjb25maXJtT3ZlcmR1ZU9wZXJhdGlvbigpIHsNCiAgICAgIHRoaXMuJHJlZnMub3ZlcmR1ZU9wZXJhdGlvbkZvcm0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICB0aGlzLm92ZXJkdWVPcGVyYXRpb25Mb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgICB1cGRhdGVTYW1wbGVPcmRlclN0YXR1cyh7DQogICAgICAgICAgICBpZDogdGhpcy5jdXJyZW50T3ZlcmR1ZVJvdy5pZCwNCiAgICAgICAgICAgIHN0YXR1czogIjExIiwgLy8g54m55q6K5aSE55CG77yM5Y+q5pu05paw4oCc6YC+5pyf5oOF5Ya14oCd5aGr5YaZ55qE5a2X5q61DQogICAgICAgICAgICBleHBlY3RlZFNhbXBsZVRpbWU6IHRoaXMub3ZlcmR1ZU9wZXJhdGlvbkZvcm0uZXhwZWN0ZWRTYW1wbGVUaW1lLA0KICAgICAgICAgICAgcmVhc29uRm9yTm9TYW1wbGU6IHRoaXMub3ZlcmR1ZU9wZXJhdGlvbkZvcm0ucmVhc29uRm9yTm9TYW1wbGUsDQogICAgICAgICAgICBzb2x1dGlvbjogdGhpcy5vdmVyZHVlT3BlcmF0aW9uRm9ybS5zb2x1dGlvbg0KICAgICAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgdGhpcy5vdmVyZHVlT3BlcmF0aW9uTG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgdGhpcy5vdmVyZHVlT3BlcmF0aW9uT3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCfpgL7mnJ/mk43kvZzlt7LlrozmiJAnKTsNCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgdGhpcy5sb2FkRGFzaGJvYXJkU3RhdHMoKTsNCiAgICAgICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgICB0aGlzLm92ZXJkdWVPcGVyYXRpb25Mb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOafpeeci+aJk+agt+WNleivpuaDhSAqLw0KICAgIGFzeW5jIG9yZGVyRGV0YWlsKHJvdykgew0KICAgICAgdHJ5IHsNCiAgICAgICAgLy/ojrflj5bmiafooYxJRA0KICAgICAgICBjb25zdCBvcmRlcklkID0gcm93LnByb2plY3RPcmRlcklkOw0KICAgICAgICBsZXQgZGF0YSA9IGF3YWl0IGdldEV4ZWN1dGlvbkJ5T3JkZXJJbmZvKG9yZGVySWQpOw0KICAgICAgICBpZihkYXRhIT1udWxsICYmICFpc051bGwoZGF0YS5pZCkpew0KICAgICAgICAgICAgbGV0IGlkID0gZGF0YS5pZDsNCiAgICAgICAgICAgIHRoaXMuJHJlZnMuZXhlY3V0aW9uQWRkT3JFZGl0Lm9wZW4gPSB0cnVlOw0KICAgICAgICAgICAgYXdhaXQgdGhpcy4kbmV4dFRpY2soKQ0KICAgICAgICAgICAgdGhpcy4kcmVmcy5leGVjdXRpb25BZGRPckVkaXQucmVzZXQoKQ0KICAgICAgICAgICAgdGhpcy4kcmVmcy5leGVjdXRpb25BZGRPckVkaXQuc2hvdyhpZCwgMSkNCiAgICAgICAgfWVsc2V7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W5pWw5o2u5aSx6LSlJyk7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+afpeeci+mhueebruivpuaDheWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+afpeeci+mhueebruivpuaDheWksei0pTogJyArIChlcnJvci5tZXNzYWdlIHx8ICfmnKrnn6XplJnor68nKSk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDplIHlrprnirbmgIHmlLnlj5jlpITnkIYgKi8NCiAgICBoYW5kbGVMb2NrQ2hhbmdlKHZhbHVlKSB7DQogICAgICBpZiAodmFsdWUgPT09IDEpIHsNCiAgICAgICAgdGhpcy4kY29uZmlybSgn6ZSB5a6a5ZCO5bCG5peg5rOV6Ieq5Yqo6LCD5pW05q2k5Y2V55qE5o6S5Y2V5pel5pyf77yM5piv5ZCm56Gu6K6k6ZSB5a6a77yfJywgJ+aPkOekuicsIHsNCiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuZm9ybS5pc0xvY2tlZCA9IDE7DQogICAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmZvcm0uaXNMb2NrZWQgPSAwOw0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDooajmoLzkuK3plIHlrprnirbmgIHmlLnlj5jlpITnkIYgKi8NCiAgICBoYW5kbGVUYWJsZUxvY2tDaGFuZ2UodmFsdWUsIHJvdykgew0KICAgICAgaWYgKHZhbHVlID09PSAxKSB7DQogICAgICAgIHRoaXMuJGNvbmZpcm0oJ+mUgeWumuWQjuWwhuaXoOazleiHquWKqOiwg+aVtOatpOWNleeahOaOkuWNleaXpeacn++8jOaYr+WQpuehruiupOmUgeWumu+8nycsICfmj5DnpLonLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgICAvLyDosIPnlKjmm7TmlrDmjqXlj6MNCiAgICAgICAgICB1cGRhdGVFbmdpbmVlclNhbXBsZU9yZGVyKHsNCiAgICAgICAgICAgIGlkOiByb3cuaWQsDQogICAgICAgICAgICBpc0xvY2tlZDogMQ0KICAgICAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLplIHlrprmiJDlip8iKTsNCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgdGhpcy5sb2FkRGFzaGJvYXJkU3RhdHMoKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIC8vIOWPlua2iOaTjeS9nO+8jOaBouWkjeWOn+WAvA0KICAgICAgICAgIHJvdy5pc0xvY2tlZCA9IDA7DQogICAgICAgIH0pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g6Kej6ZSB5pON5L2cDQogICAgICAgIHVwZGF0ZUVuZ2luZWVyU2FtcGxlT3JkZXIoew0KICAgICAgICAgIGlkOiByb3cuaWQsDQogICAgICAgICAgaXNMb2NrZWQ6IDANCiAgICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLop6PplIHmiJDlip8iKTsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB0aGlzLmxvYWREYXNoYm9hcmRTdGF0cygpOw0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDliqDovb3nu5/orqHmpoLop4jmlbDmja4gKi8NCiAgICBsb2FkRGFzaGJvYXJkU3RhdHMoKSB7DQogICAgICBsZXQgcGFyYW1zID0geyAuLi50aGlzLnF1ZXJ5UGFyYW1zIH07DQogICAgICAvLyDmt7vliqDml6XmnJ/ojIPlm7TnrZvpgInlj4LmlbANCiAgICAgIGlmICh0aGlzLmRhdGVSYW5nZSAmJiB0aGlzLmRhdGVSYW5nZS5sZW5ndGggPT09IDIpIHsNCiAgICAgICAgcGFyYW1zLmJlZ2luRGF0ZVJhbmdlID0gdGhpcy5kYXRlUmFuZ2VbMF07DQogICAgICAgIHBhcmFtcy5lbmREYXRlUmFuZ2UgPSB0aGlzLmRhdGVSYW5nZVsxXTsNCiAgICAgIH0NCiAgICAgIGdldERhc2hib2FyZFN0YXRzKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZGFzaGJvYXJkU3RhdHMgPSByZXNwb25zZS5kYXRhIHx8IHt9Ow0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5pu05pS55bel56iL5biI5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQ2hhbmdlRW5naW5lZXIocm93KSB7DQogICAgICB0aGlzLmNoYW5nZUVuZ2luZWVyRm9ybSA9IHsNCiAgICAgICAgaWQ6IHJvdy5pZCwNCiAgICAgICAgc2FtcGxlT3JkZXJDb2RlOiByb3cuc2FtcGxlT3JkZXJDb2RlLA0KICAgICAgICBjdXJyZW50RW5naW5lZXJOYW1lOiByb3cubmlja05hbWUsDQogICAgICAgIG9sZEVuZ2luZWVySWQ6IHJvdy51c2VySWQsDQogICAgICAgIG5ld0VuZ2luZWVySWQ6IG51bGwsDQogICAgICAgIHNjaGVkdWxlZERhdGU6IG51bGwsDQogICAgICAgIGFkanVzdFdvcmtTY2hlZHVsZTogMA0KICAgICAgfTsNCg0KICAgICAgLy8g6I635Y+W5Y+v55So5bel56iL5biI5YiX6KGoDQogICAgICBnZXRFbmdpbmVlcnNCeURpZmZpY3VsdHlMZXZlbCh7DQogICAgICAgIGRpZmZpY3VsdHlMZXZlbElkOiByb3cuZGlmZmljdWx0eUxldmVsSWQsDQogICAgICAgIGNhdGVnb3J5SWQ6IHJvdy5jYXRlZ29yeUlkDQogICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5lbmdpbmVlck9wdGlvbnMgPSByZXNwb25zZS5kYXRhIHx8IFtdOw0KICAgICAgfSk7DQogICAgICAvLyDojrflj5bnoJTlj5HmiYDmnInlt6XnqIvluIjliJfooagNCiAgICAgIGdldFJlc2VhcmNoRGVwYXJ0bWVudHNVc2VyKCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuc2VhcmNoZWRFbmdpbmVlcnMgPSByZXNwb25zZS5kYXRhIHx8IFtdOw0KICAgICAgfSk7DQogICAgICB0aGlzLmNoYW5nZUVuZ2luZWVyT3BlbiA9IHRydWU7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5pu05pS55bel56iL5biIICovDQogICAgc3VibWl0Q2hhbmdlRW5naW5lZXIoKSB7DQogICAgICB0aGlzLiRyZWZzWyJjaGFuZ2VFbmdpbmVlckZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGNvbnN0IGRhdGEgPSB7DQogICAgICAgICAgICBzYW1wbGVPcmRlcklkOiB0aGlzLmNoYW5nZUVuZ2luZWVyRm9ybS5pZCwNCiAgICAgICAgICAgIG9sZEVuZ2luZWVySWQ6IHRoaXMuY2hhbmdlRW5naW5lZXJGb3JtLm9sZEVuZ2luZWVySWQsDQogICAgICAgICAgICBuZXdFbmdpbmVlcklkOiB0aGlzLmNoYW5nZUVuZ2luZWVyRm9ybS5uZXdFbmdpbmVlcklkLA0KICAgICAgICAgICAgc2NoZWR1bGVkRGF0ZTogdGhpcy5jaGFuZ2VFbmdpbmVlckZvcm0uc2NoZWR1bGVkRGF0ZSwNCiAgICAgICAgICAgIGFkanVzdFdvcmtTY2hlZHVsZTogdGhpcy5jaGFuZ2VFbmdpbmVlckZvcm0uYWRqdXN0V29ya1NjaGVkdWxlDQogICAgICAgICAgfTsNCg0KICAgICAgICAgIC8vIOiwg+eUqOabtOaUueW3peeoi+W4iOeahEFQSeaOpeWPow0KICAgICAgICAgIGNoYW5nZUVuZ2luZWVyKGRhdGEpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLmm7TmlLnlt6XnqIvluIjmiJDlip8iKTsNCiAgICAgICAgICAgIHRoaXMuY2hhbmdlRW5naW5lZXJPcGVuID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIC8vIOiOt+WPluacquWIhumFjeaJk+agt+WNlQ0KICAgICAgICAgICAgdGhpcy5oYW5kbGVVbmFzc2lnbmVkT3JkZXJzKCk7DQogICAgICAgICAgICB0aGlzLmxvYWREYXNoYm9hcmRTdGF0cygpOw0KICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMubXNnRXJyb3IoIuabtOaUueW3peeoi+W4iOWksei0pSIpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDojrflj5bmnKrliIbphY3miZPmoLfljZXliJfooaggKi8NCiAgICBoYW5kbGVVbmFzc2lnbmVkT3JkZXJzKCkgew0KICAgICAgbGV0IHBhcmFtcyA9IHsgLi4udGhpcy5xdWVyeVBhcmFtcyB9Ow0KICAgICAgcGFyYW1zID0gdGhpcy5hZGREYXRlUmFuZ2UocGFyYW1zLCB0aGlzLnNjaGVkdWxlZERhdGVSYW5nZSwnU2NoZWR1bGVkRGF0ZScpDQogICAgICBwYXJhbXMgPSB0aGlzLmFkZERhdGVSYW5nZShwYXJhbXMsIHRoaXMuc3RhcnREYXRlUmFuZ2UsJ1N0YXJ0RGF0ZScpDQogICAgICBwYXJhbXMgPSB0aGlzLmFkZERhdGVSYW5nZShwYXJhbXMsIHRoaXMuYWN0dWFsU3RhcnRUaW1lUmFuZ2UsJ0FjdHVhbFN0YXJ0VGltZScpDQogICAgICBwYXJhbXMgPSB0aGlzLmFkZERhdGVSYW5nZShwYXJhbXMsIHRoaXMuYWN0dWFsRmluaXNoVGltZVJhbmdlLCdBY3R1YWxGaW5pc2hUaW1lJykNCiAgICAgIC8vIOa4heepuuS5i+WJjeeahOaXpeacn+iMg+WbtOWPguaVsO+8jOagueaNruW9k+WJjeeKtuaAgemHjeaWsOiuvue9rg0KICAgICAgZGVsZXRlIHBhcmFtcy5iZWdpbkRhdGVSYW5nZTsNCiAgICAgIGRlbGV0ZSBwYXJhbXMuZW5kRGF0ZVJhbmdlOw0KICAgICAgLy8g5re75Yqg5pel5pyf6IyD5Zu0562b6YCJ5Y+C5pWwDQogICAgICBpZiAodGhpcy5kYXRlUmFuZ2UgJiYgdGhpcy5kYXRlUmFuZ2UubGVuZ3RoID09PSAyKSB7DQogICAgICAgIHBhcmFtcy5iZWdpbkRhdGVSYW5nZSA9IHRoaXMuZGF0ZVJhbmdlWzBdOw0KICAgICAgICBwYXJhbXMuZW5kRGF0ZVJhbmdlID0gdGhpcy5kYXRlUmFuZ2VbMV07DQogICAgICB9DQogICAgICBwYXJhbXMuYXNzb2NpYXRpb25TdGF0dXMgPSAwDQogICAgICBwYXJhbXMucGFnZU51bSA9IHRoaXMudW5hc3NpZ25lZFF1ZXJ5UGFyYW1zLnBhZ2VOdW07DQogICAgICBwYXJhbXMucGFnZVNpemUgPSB0aGlzLnVuYXNzaWduZWRRdWVyeVBhcmFtcy5wYWdlU2l6ZTsNCiAgICAgIGxpc3RFbmdpbmVlclNhbXBsZU9yZGVyKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMudW5hc3NpZ25lZE9yZGVycyA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudW5hc3NpZ25lZFRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliIbphY3lt6XnqIvluIjmk43kvZwgKi8NCiAgICBoYW5kbGVBc3NpZ25FbmdpbmVlcihyb3cpIHsNCiAgICAgIHRoaXMuY2hhbmdlRW5naW5lZXJGb3JtID0gew0KICAgICAgICBpZDogcm93LmlkLA0KICAgICAgICBzYW1wbGVPcmRlckNvZGU6IHJvdy5zYW1wbGVPcmRlckNvZGUsDQogICAgICAgIGN1cnJlbnRFbmdpbmVlck5hbWU6ICcnLA0KICAgICAgICBvbGRFbmdpbmVlcklkOiBudWxsLA0KICAgICAgICBuZXdFbmdpbmVlcklkOiBudWxsLA0KICAgICAgICBzY2hlZHVsZWREYXRlOiBudWxsLA0KICAgICAgICBhZGp1c3RXb3JrU2NoZWR1bGU6IDANCiAgICAgIH07DQoNCiAgICAgIC8vIOiOt+WPluWPr+eUqOW3peeoi+W4iOWIl+ihqA0KICAgICAgZ2V0RW5naW5lZXJzQnlEaWZmaWN1bHR5TGV2ZWwoew0KICAgICAgICBkaWZmaWN1bHR5TGV2ZWxJZDogcm93LmRpZmZpY3VsdHlMZXZlbElkLA0KICAgICAgICBjYXRlZ29yeUlkOiByb3cuY2F0ZWdvcnlJZA0KICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZW5naW5lZXJPcHRpb25zID0gcmVzcG9uc2UuZGF0YSB8fCBbXTsNCiAgICAgIH0pOw0KICAgICAgLy8g6I635Y+W56CU5Y+R5omA5pyJ5bel56iL5biI5YiX6KGoDQogICAgICBnZXRSZXNlYXJjaERlcGFydG1lbnRzVXNlcigpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnNlYXJjaGVkRW5naW5lZXJzID0gcmVzcG9uc2UuZGF0YSB8fCBbXTsNCiAgICAgIH0pOw0KICAgICAgdGhpcy5jaGFuZ2VFbmdpbmVlck9wZW4gPSB0cnVlOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOacquWIhumFjeaJk+agt+WNlSAqLw0KICAgIGhhbmRsZURlbGV0ZVVuYXNzaWduZWQocm93KSB7DQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTmiZPmoLfljZXnvJblj7fkuLoiJyArIHJvdy5zYW1wbGVPcmRlckNvZGUgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxFbmdpbmVlclNhbXBsZU9yZGVyKHJvdy5pZCk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5oYW5kbGVVbmFzc2lnbmVkT3JkZXJzKCk7DQogICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog6amz5Zue5pyq5YiG6YWN5omT5qC35Y2VICovDQogICAgaGFuZGxlUmVqZWN0VW5hc3NpZ25lZChyb3cpIHsNCiAgICAgIHRoaXMuY3VycmVudFJlamVjdFJvdyA9IHJvdzsNCiAgICAgIHRoaXMucmVqZWN0Rm9ybS5zYW1wbGVPcmRlckNvZGUgPSByb3cuc2FtcGxlT3JkZXJDb2RlOw0KICAgICAgdGhpcy5yZWplY3RGb3JtLnJlamVjdFJlYXNvbiA9ICcnOw0KICAgICAgdGhpcy5yZWplY3REaWFsb2dPcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy4kcmVmcy5yZWplY3RGb3JtLmNsZWFyVmFsaWRhdGUoKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOacquWIhumFjeaJk+agt+WNleWIhumhteWkp+Wwj+aUueWPmCAqLw0KICAgIGhhbmRsZVVuYXNzaWduZWRTaXplQ2hhbmdlKG5ld1NpemUpIHsNCiAgICAgIHRoaXMudW5hc3NpZ25lZFF1ZXJ5UGFyYW1zLnBhZ2VTaXplID0gbmV3U2l6ZTsNCiAgICAgIHRoaXMuaGFuZGxlVW5hc3NpZ25lZE9yZGVycygpOw0KICAgIH0sDQogICAgLyoqIOacquWIhumFjeaJk+agt+WNlemhteeggeaUueWPmCAqLw0KICAgIGhhbmRsZVVuYXNzaWduZWRDdXJyZW50Q2hhbmdlKG5ld1BhZ2UpIHsNCiAgICAgIHRoaXMudW5hc3NpZ25lZFF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSBuZXdQYWdlOw0KICAgICAgdGhpcy5oYW5kbGVVbmFzc2lnbmVkT3JkZXJzKCk7DQogICAgfSwNCiAgICAvKiog5YiH5o2i5pyq5YiG6YWN6Z2i5p2/5pi+56S654q25oCBICovDQogICAgdG9nZ2xlVW5hc3NpZ25lZFBhbmVsKCkgew0KICAgICAgdGhpcy5pc1VuYXNzaWduZWRQYW5lbENvbGxhcHNlZCA9ICF0aGlzLmlzVW5hc3NpZ25lZFBhbmVsQ29sbGFwc2VkOw0KICAgIH0sDQogICAgLyoqIOWkhOeQhueKtuaAgei/h+a7pCAqLw0KICAgIGhhbmRsZVN0YXR1c0ZpbHRlcihzdGF0dXMpIHsNCiAgICAgIC8vIOa4hemZpOmAvuacn+i/h+a7pA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5pc092ZXJkdWUgPSBudWxsOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jb21wbGV0aW9uU3RhdHVzID0gc3RhdHVzOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQoNCiAgICAvKiog5aSE55CG6YC+5pyf5Lu75Yqh6L+H5rukICovDQogICAgaGFuZGxlT3ZlcmR1ZUZpbHRlcigpIHsNCiAgICAgIC8vIOa4hemZpOWujOaIkOeKtuaAgei/h+a7pA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jb21wbGV0aW9uU3RhdHVzID0gbnVsbDsNCiAgICAgIC8vIOiuvue9rumAvuacn+i/h+a7pA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5pc092ZXJkdWUgPSB0aGlzLnF1ZXJ5UGFyYW1zLmlzT3ZlcmR1ZSA9PT0gMSA/IG51bGwgOiAxOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KDQogICAgICBpZiAodGhpcy5xdWVyeVBhcmFtcy5pc092ZXJkdWUgPT09IDEpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCflt7LnrZvpgInmmL7npLrpgL7mnJ/ku7vliqEnKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn5bey5riF6Zmk6YC+5pyf5Lu75Yqh562b6YCJJyk7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog6I635Y+W57Sn5oCl56iL5bqm57G75Z6LICovDQogICAgZ2V0VXJnZW5jeVR5cGUoZW5kRGF0ZSwgbGF0ZXN0U3RhcnRUaW1lKSB7DQogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOw0KICAgICAgY29uc3QgZW5kID0gbmV3IERhdGUoZW5kRGF0ZSk7DQogICAgICBjb25zdCBsYXRlc3QgPSBsYXRlc3RTdGFydFRpbWUgPyBuZXcgRGF0ZShsYXRlc3RTdGFydFRpbWUpIDogbnVsbDsNCg0KICAgICAgLy8g6K6h566X6Led56a75oiq5q2i5pel5pyf55qE5aSp5pWwDQogICAgICBjb25zdCBkYXlzVG9FbmQgPSBNYXRoLmNlaWwoKGVuZCAtIG5vdykgLyAoMTAwMCAqIDYwICogNjAgKiAyNCkpOw0KDQogICAgICAvLyDlpoLmnpzmnInmnIDmmZrlvIDlp4vml7bpl7TvvIzmo4Dmn6XmmK/lkKblt7Lnu4/otoXov4cNCiAgICAgIGlmIChsYXRlc3QgJiYgbm93ID4gbGF0ZXN0KSB7DQogICAgICAgIHJldHVybiAnZGFuZ2VyJzsgLy8g5bey6LaF6L+H5pyA5pma5byA5aeL5pe26Ze0DQogICAgICB9DQoNCiAgICAgIGlmIChkYXlzVG9FbmQgPD0gMSkgew0KICAgICAgICByZXR1cm4gJ2Rhbmdlcic7IC8vIOe0p+aApe+8mjHlpKnlhoXmiKrmraINCiAgICAgIH0gZWxzZSBpZiAoZGF5c1RvRW5kIDw9IDMpIHsNCiAgICAgICAgcmV0dXJuICd3YXJuaW5nJzsgLy8g6K2m5ZGK77yaM+WkqeWGheaIquatog0KICAgICAgfSBlbHNlIGlmIChkYXlzVG9FbmQgPD0gNykgew0KICAgICAgICByZXR1cm4gJ3ByaW1hcnknOyAvLyDkuIDoiKzvvJo35aSp5YaF5oiq5q2iDQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gJ3N1Y2Nlc3MnOyAvLyDlhYXotrPml7bpl7QNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDojrflj5bntKfmgKXnqIvluqbmlofmnKwgKi8NCiAgICBnZXRVcmdlbmN5VGV4dChlbmREYXRlLCBsYXRlc3RTdGFydFRpbWUpIHsNCiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7DQogICAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZShlbmREYXRlKTsNCiAgICAgIGNvbnN0IGxhdGVzdCA9IGxhdGVzdFN0YXJ0VGltZSA/IG5ldyBEYXRlKGxhdGVzdFN0YXJ0VGltZSkgOiBudWxsOw0KICAgICAgLy8g6K6h566X6Led56a75oiq5q2i5pel5pyf55qE5aSp5pWwDQogICAgICBjb25zdCBkYXlzVG9FbmQgPSBNYXRoLmNlaWwoKGVuZCAtIG5vdykgLyAoMTAwMCAqIDYwICogNjAgKiAyNCkpOw0KICAgICAgLy8g5aaC5p6c5pyJ5pyA5pma5byA5aeL5pe26Ze077yM5qOA5p+l5piv5ZCm5bey57uP6LaF6L+HDQogICAgICBpZiAobGF0ZXN0ICYmIG5vdyA+IGxhdGVzdCkgew0KICAgICAgICByZXR1cm4gJ+i2heacn+acquW8gOWniyc7DQogICAgICB9DQoNCiAgICAgIGlmIChkYXlzVG9FbmQgPD0gMCkgew0KICAgICAgICByZXR1cm4gJ+W3sumAvuacnyc7DQogICAgICB9IGVsc2UgaWYgKGRheXNUb0VuZCA8PSAxKSB7DQogICAgICAgIHJldHVybiAn57Sn5oClJzsNCiAgICAgIH0gZWxzZSBpZiAoZGF5c1RvRW5kIDw9IDMpIHsNCiAgICAgICAgcmV0dXJuICfovoPmgKUnOw0KICAgICAgfSBlbHNlIGlmIChkYXlzVG9FbmQgPD0gNykgew0KICAgICAgICByZXR1cm4gJ+S4gOiIrCc7DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gJ+WFhei2syc7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog5Yi35paw5pyq5YiG6YWN5omT5qC35Y2V5YiX6KGoICovDQogICAgaGFuZGxlUmVmcmVzaFVuYXNzaWduZWQoKSB7DQogICAgICB0aGlzLmhhbmRsZVVuYXNzaWduZWRPcmRlcnMoKTsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yi35paw5oiQ5YqfJyk7DQogICAgfSwNCiAgICAvKiog6I635Y+W6YC+5pyf5L+h5oGvICovDQogICAgZ2V0T3ZlcmR1ZUluZm8ocm93KSB7DQogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOw0KICAgICAgY29uc3QgZW5kRGF0ZSA9IG5ldyBEYXRlKHJvdy5lbmREYXRlKTsNCiAgICAgIGxldCBpc092ZXJkdWUgPSBmYWxzZTsNCiAgICAgIGxldCBvdmVyZHVlRGF5cyA9IDA7DQogICAgICAvLyDmoLnmja7lkI7lj7BTUUzpgLvovpHliKTmlq3pgL7mnJ8NCiAgICAgIGlmIChyb3cuY29tcGxldGlvblN0YXR1cyA9PT0gMikgew0KICAgICAgICAvLyDlt7LlrozmiJDkuJTpgL7mnJ/vvJrlrp7pmYXlrozmiJDml7bpl7QgPiDmiKrmraLml6XmnJ8NCiAgICAgICAgaWYgKHJvdy5hY3R1YWxGaW5pc2hUaW1lKSB7DQogICAgICAgICAgY29uc3QgYWN0dWFsRmluaXNoVGltZSA9IG5ldyBEYXRlKHJvdy5hY3R1YWxGaW5pc2hUaW1lKTsNCiAgICAgICAgICBpZiAoYWN0dWFsRmluaXNoVGltZSA+IGVuZERhdGUpIHsNCiAgICAgICAgICAgIGlzT3ZlcmR1ZSA9IHRydWU7DQogICAgICAgICAgICBvdmVyZHVlRGF5cyA9IE1hdGguY2VpbCgoYWN0dWFsRmluaXNoVGltZSAtIGVuZERhdGUpIC8gKDEwMDAgKiA2MCAqIDYwICogMjQpKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSBpZiAocm93LmNvbXBsZXRpb25TdGF0dXMgPT09IDEpIHsNCiAgICAgICAgLy8g6L+b6KGM5Lit5LiU6YC+5pyf77ya5b2T5YmN5pe26Ze0ID4g5oiq5q2i5pel5pyfDQogICAgICAgIGlmIChub3cgPiBlbmREYXRlKSB7DQogICAgICAgICAgaXNPdmVyZHVlID0gdHJ1ZTsNCiAgICAgICAgICBvdmVyZHVlRGF5cyA9IE1hdGguY2VpbCgobm93IC0gZW5kRGF0ZSkgLyAoMTAwMCAqIDYwICogNjAgKiAyNCkpOw0KICAgICAgICB9DQogICAgICB9IGVsc2UgaWYgKHJvdy5jb21wbGV0aW9uU3RhdHVzID09PSAwKSB7DQogICAgICAgIC8vIOacquW8gOWni+S4lOmAvuacn++8muacquW8gOWni+S9huW9k+WJjeaXtumXtCA+IOW8gOWni+aXpeacnw0KICAgICAgICBpZiAobm93ID4gZW5kRGF0ZSkgew0KICAgICAgICAgIGlzT3ZlcmR1ZSA9IHRydWU7DQogICAgICAgICAgb3ZlcmR1ZURheXMgPSBNYXRoLmNlaWwoKG5vdyAtIGVuZERhdGUpIC8gKDEwMDAgKiA2MCAqIDYwICogMjQpKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICByZXR1cm4gew0KICAgICAgICBpc092ZXJkdWUsDQogICAgICAgIG92ZXJkdWVEYXlzDQogICAgICB9Ow0KICAgIH0sDQoNCiAgICAvLyA9PT09PT09PT09PT09PT09PT09PSDmibnmrKHnrqHnkIbnm7jlhbPmlrnms5UgPT09PT09PT09PT09PT09PT09PT0NCg0KICAgIC8qKiDmiZPlvIDmibnmrKHnrqHnkIblr7nor53moYYgKi8NCiAgICBoYW5kbGVCYXRjaE1hbmFnZW1lbnQocm93KSB7DQogICAgICB0aGlzLnNlbGVjdGVkT3JkZXJGb3JCYXRjaCA9IHJvdzsNCiAgICAgIHRoaXMuY3VycmVudEJhdGNoUm93ID0gcm93OyAvLyDkv53lrZjlvZPliY3ooYzmlbDmja4NCiAgICAgIHRoaXMuYmF0Y2hNYW5hZ2VtZW50T3BlbiA9IHRydWU7DQogICAgICB0aGlzLmxvYWRCYXRjaERhdGEocm93LmlkKTsNCiAgICB9LA0KDQogICAgLyoqIOWKoOi9veaJueasoeaVsOaNriAqLw0KICAgIGFzeW5jIGxvYWRCYXRjaERhdGEoZW5naW5lZXJTYW1wbGVPcmRlcklkKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDliqDovb3lvZPliY3mibnmrKENCiAgICAgICAgY29uc3QgY3VycmVudEJhdGNoUmVzcG9uc2UgPSBhd2FpdCBnZXRDdXJyZW50QmF0Y2goZW5naW5lZXJTYW1wbGVPcmRlcklkKTsNCiAgICAgICAgdGhpcy5jdXJyZW50QmF0Y2ggPSBjdXJyZW50QmF0Y2hSZXNwb25zZS5kYXRhOw0KDQogICAgICAgIC8vIOWKoOi9veaJgOacieaJueasoQ0KICAgICAgICBjb25zdCBiYXRjaGVzUmVzcG9uc2UgPSBhd2FpdCBnZXRCYXRjaGVzQnlPcmRlcklkKGVuZ2luZWVyU2FtcGxlT3JkZXJJZCk7DQogICAgICAgIGNvbnN0IGFsbEJhdGNoZXMgPSBiYXRjaGVzUmVzcG9uc2UuZGF0YSB8fCBbXTsNCg0KICAgICAgICAvLyDliIbnprvlvZPliY3mibnmrKHlkozljoblj7LmibnmrKHvvIzlubbkuLrmr4/kuKrljoblj7LmibnmrKHliqDovb3lrp7pqozmlbDph48NCiAgICAgICAgdGhpcy5oaXN0b3J5QmF0Y2hlcyA9IGFsbEJhdGNoZXMuZmlsdGVyKGJhdGNoID0+IGJhdGNoLmlzQ3VycmVudEJhdGNoID09PSAwKTsNCg0KICAgICAgICAvLyAvLyDkuLrmr4/kuKrljoblj7LmibnmrKHliqDovb3lrp7pqozmlbDph48NCiAgICAgICAgLy8gZm9yIChsZXQgYmF0Y2ggb2YgdGhpcy5oaXN0b3J5QmF0Y2hlcykgew0KICAgICAgICAvLyAgIHRyeSB7DQogICAgICAgIC8vICAgICBjb25zdCBleHBlcmltZW50c1Jlc3BvbnNlID0gYXdhaXQgZ2V0RXhwZXJpbWVudHNCeUJhdGNoSWQoYmF0Y2guaWQpOw0KICAgICAgICAvLyAgICAgYmF0Y2guZXhwZXJpbWVudENvdW50ID0gZXhwZXJpbWVudHNSZXNwb25zZS5kYXRhID8gZXhwZXJpbWVudHNSZXNwb25zZS5kYXRhLmxlbmd0aCA6IDA7DQogICAgICAgIC8vICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgLy8gICAgIGNvbnNvbGUud2Fybihg5Yqg6L295om55qyhJHtiYXRjaC5pZH3nmoTlrp7pqozmlbDph4/lpLHotKU6YCwgZXJyb3IpOw0KICAgICAgICAvLyAgICAgYmF0Y2guZXhwZXJpbWVudENvdW50ID0gMDsNCiAgICAgICAgLy8gICB9DQogICAgICAgIC8vIH0NCg0KICAgICAgICAvLyDlpoLmnpzmnInlvZPliY3mibnmrKHvvIzliqDovb3lrp7pqozorrDlvZUNCiAgICAgICAgaWYgKHRoaXMuY3VycmVudEJhdGNoKSB7DQogICAgICAgICAgYXdhaXQgdGhpcy5sb2FkQ3VycmVudEJhdGNoRXhwZXJpbWVudHModGhpcy5jdXJyZW50QmF0Y2guaWQpOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3mibnmrKHmlbDmja7lpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliqDovb3mibnmrKHmlbDmja7lpLHotKUnKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOWKoOi9veW9k+WJjeaJueasoeeahOWunumqjOiusOW9lSAqLw0KICAgIGFzeW5jIGxvYWRDdXJyZW50QmF0Y2hFeHBlcmltZW50cyhiYXRjaElkKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldEV4cGVyaW1lbnRzQnlCYXRjaElkKGJhdGNoSWQpOw0KICAgICAgICB0aGlzLmN1cnJlbnRCYXRjaEV4cGVyaW1lbnRzID0gcmVzcG9uc2UuZGF0YSB8fCBbXTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veWunumqjOiusOW9leWksei0pTonLCBlcnJvcik7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDlvIDlp4vmlrDmibnmrKEgKi8NCiAgICBhc3luYyBzdGFydE5ld0JhdGNoKCkgew0KICAgICAgLy8g5qOA5p+l57yW6L6R5p2D6ZmQDQogICAgICBpZiAoIXRoaXMuY2FuRWRpdEJhdGNoKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5b2T5YmN5omT5qC35Y2V54q25oCB5LiN5YWB6K645byA5aeL5paw5om55qyh77yM5Y+q5pyJ54q25oCB5Li6Iui/m+ihjOS4rSLnmoTmiZPmoLfljZXmiY3lj6/ku6XnvJbovpHmibnmrKHkv6Hmga8nKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICB0cnkgew0KICAgICAgICBhd2FpdCB0aGlzLiRjb25maXJtKCfnoa7orqTlvIDlp4vmlrDnmoTmiZPmoLfmibnmrKHvvJ8nLCAn5o+Q56S6Jywgew0KICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSk7DQoNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBzdGFydE5ld0JhdGNoKHRoaXMuc2VsZWN0ZWRPcmRlckZvckJhdGNoLmlkLCAnJyk7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5paw5om55qyh5byA5aeL5oiQ5YqfJyk7DQoNCiAgICAgICAgLy8g6YeN5paw5Yqg6L295om55qyh5pWw5o2uDQogICAgICAgIGF3YWl0IHRoaXMubG9hZEJhdGNoRGF0YSh0aGlzLnNlbGVjdGVkT3JkZXJGb3JCYXRjaC5pZCk7DQoNCiAgICAgICAgLy8g5Yi35paw5Li75YiX6KGoDQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgaWYgKGVycm9yICE9PSAnY2FuY2VsJykgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+W8gOWni+aWsOaJueasoeWksei0pTogJyArIChlcnJvci5tZXNzYWdlIHx8IGVycm9yKSk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOe7k+adn+W9k+WJjeaJueasoSAqLw0KICAgIGZpbmlzaEN1cnJlbnRCYXRjaCgpIHsNCiAgICAgIC8vIOajgOafpee8lui+keadg+mZkA0KICAgICAgaWYgKCF0aGlzLmNhbkVkaXRCYXRjaCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+W9k+WJjeaJk+agt+WNleeKtuaAgeS4jeWFgeiuuOe7k+adn+aJueasoe+8jOWPquacieeKtuaAgeS4uiLov5vooYzkuK0i55qE5omT5qC35Y2V5omN5Y+v5Lul57yW6L6R5om55qyh5L+h5oGvJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgdGhpcy5maW5pc2hCYXRjaEZvcm0gPSB7DQogICAgICAgIHF1YWxpdHlFdmFsdWF0aW9uOiAnJywNCiAgICAgICAgcmVtYXJrOiAnJw0KICAgICAgfTsNCiAgICAgIHRoaXMuZmluaXNoQmF0Y2hPcGVuID0gdHJ1ZTsNCiAgICB9LA0KDQogICAgLyoqIOaPkOS6pOe7k+adn+aJueasoSAqLw0KICAgIGFzeW5jIHN1Ym1pdEZpbmlzaEJhdGNoKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgYXdhaXQgZmluaXNoQ3VycmVudEJhdGNoKA0KICAgICAgICAgIHRoaXMuc2VsZWN0ZWRPcmRlckZvckJhdGNoLmlkLA0KICAgICAgICAgIHRoaXMuZmluaXNoQmF0Y2hGb3JtLnF1YWxpdHlFdmFsdWF0aW9uLA0KICAgICAgICAgIHRoaXMuZmluaXNoQmF0Y2hGb3JtLnJlbWFyaw0KICAgICAgICApOw0KDQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5om55qyh57uT5p2f5oiQ5YqfJyk7DQogICAgICAgIHRoaXMuZmluaXNoQmF0Y2hPcGVuID0gZmFsc2U7DQoNCiAgICAgICAgLy8g6YeN5paw5Yqg6L295om55qyh5pWw5o2uDQogICAgICAgIGF3YWl0IHRoaXMubG9hZEJhdGNoRGF0YSh0aGlzLnNlbGVjdGVkT3JkZXJGb3JCYXRjaC5pZCk7DQoNCiAgICAgICAgLy8g5Yi35paw5Li75YiX6KGoDQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign57uT5p2f5om55qyh5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign57uT5p2f5om55qyh5aSx6LSlOiAnICsgKGVycm9yLm1lc3NhZ2UgfHwgZXJyb3IpKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOa3u+WKoOWunumqjOiusOW9lSAqLw0KICAgIGFkZEV4cGVyaW1lbnQoKSB7DQogICAgICAvLyDmo4Dmn6XnvJbovpHmnYPpmZANCiAgICAgIGlmICghdGhpcy5jYW5FZGl0QmF0Y2gpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCflvZPliY3miZPmoLfljZXnirbmgIHkuI3lhYHorrjmt7vliqDlrp7pqozorrDlvZXvvIzlj6rmnInnirbmgIHkuLoi6L+b6KGM5LitIueahOaJk+agt+WNleaJjeWPr+S7pee8lui+keaJueasoeS/oeaBrycpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIHRoaXMuZXhwZXJpbWVudEZvcm0gPSB7DQogICAgICAgIGV4cGVyaW1lbnRDb2RlOiAnJywNCiAgICAgICAgZXhwZXJpbWVudE5vdGU6ICcnDQogICAgICB9Ow0KICAgICAgdGhpcy5hZGRFeHBlcmltZW50T3BlbiA9IHRydWU7DQogICAgfSwNCg0KICAgIC8qKiDmj5DkuqTlrp7pqozorrDlvZUgKi8NCiAgICBhc3luYyBzdWJtaXRFeHBlcmltZW50KCkgew0KICAgICAgLy8g5qOA5p+l57yW6L6R5p2D6ZmQDQogICAgICBpZiAoIXRoaXMuY2FuRWRpdEJhdGNoKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5b2T5YmN5omT5qC35Y2V54q25oCB5LiN5YWB6K645re75Yqg5a6e6aqM6K6w5b2V77yM5Y+q5pyJ54q25oCB5Li6Iui/m+ihjOS4rSLnmoTmiZPmoLfljZXmiY3lj6/ku6XnvJbovpHmibnmrKHkv6Hmga8nKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBhd2FpdCB0aGlzLiRyZWZzLmV4cGVyaW1lbnRGb3JtLnZhbGlkYXRlKCk7DQoNCiAgICAgIGNvbnN0IGV4cGVyaW1lbnRSZWNvcmQgPSB7DQogICAgICAgIGJhdGNoSWQ6IHRoaXMuY3VycmVudEJhdGNoLmlkLA0KICAgICAgICBleHBlcmltZW50Q29kZTogdGhpcy5leHBlcmltZW50Rm9ybS5leHBlcmltZW50Q29kZSwNCiAgICAgICAgZXhwZXJpbWVudE5vdGU6IHRoaXMuZXhwZXJpbWVudEZvcm0uZXhwZXJpbWVudE5vdGUgfHwgJycNCiAgICAgIH07DQoNCiAgICAgIGF3YWl0IGFkZEV4cGVyaW1lbnRUb0JhdGNoKGV4cGVyaW1lbnRSZWNvcmQpOw0KDQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WunumqjOiusOW9lea3u+WKoOaIkOWKnycpOw0KICAgICAgdGhpcy5hZGRFeHBlcmltZW50T3BlbiA9IGZhbHNlOw0KDQogICAgICAvLyDph43mlrDliqDovb3lvZPliY3mibnmrKHnmoTlrp7pqozorrDlvZUNCiAgICAgIGF3YWl0IHRoaXMubG9hZEN1cnJlbnRCYXRjaEV4cGVyaW1lbnRzKHRoaXMuY3VycmVudEJhdGNoLmlkKTsNCiAgICB9LA0KDQogICAgLyoqIOafpeeci+aJueasoeivpuaDhSAqLw0KICAgIGFzeW5jIHZpZXdCYXRjaERldGFpbChiYXRjaCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRFeHBlcmltZW50c0J5QmF0Y2hJZChiYXRjaC5pZCk7DQogICAgICAgIGNvbnN0IGV4cGVyaW1lbnRzID0gcmVzcG9uc2UuZGF0YSB8fCBbXTsNCg0KICAgICAgICAvLyDorr7nva7mibnmrKHor6bmg4XmlbDmja4NCiAgICAgICAgdGhpcy5iYXRjaERldGFpbERhdGEgPSB7DQogICAgICAgICAgLi4uYmF0Y2gsDQogICAgICAgICAgZXhwZXJpbWVudHM6IGV4cGVyaW1lbnRzDQogICAgICAgIH07DQoNCiAgICAgICAgdGhpcy5iYXRjaERldGFpbE9wZW4gPSB0cnVlOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5p+l55yL5om55qyh6K+m5oOF5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5p+l55yL5om55qyh6K+m5oOF5aSx6LSlJyk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDorqHnrpfmjIHnu63ml7bpl7QgKi8NCiAgICBjYWxjdWxhdGVEdXJhdGlvbihzdGFydFRpbWUpIHsNCiAgICAgIGlmICghc3RhcnRUaW1lKSByZXR1cm4gJzDlsI/ml7YnOw0KDQogICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKHN0YXJ0VGltZSk7DQogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOw0KICAgICAgY29uc3QgZGlmZk1zID0gbm93IC0gc3RhcnQ7DQogICAgICBjb25zdCBkaWZmSG91cnMgPSBNYXRoLmZsb29yKGRpZmZNcyAvICgxMDAwICogNjAgKiA2MCkpOw0KICAgICAgY29uc3QgZGlmZk1pbnV0ZXMgPSBNYXRoLmZsb29yKChkaWZmTXMgJSAoMTAwMCAqIDYwICogNjApKSAvICgxMDAwICogNjApKTsNCg0KICAgICAgaWYgKGRpZmZIb3VycyA+IDApIHsNCiAgICAgICAgcmV0dXJuIGAke2RpZmZIb3Vyc33lsI/ml7Yke2RpZmZNaW51dGVzfeWIhumSn2A7DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gYCR7ZGlmZk1pbnV0ZXN95YiG6ZKfYDsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOiOt+WPlui0qOmHj+ivhOS7t+exu+WeiyAqLw0KICAgIGdldFF1YWxpdHlFdmFsdWF0aW9uVHlwZShldmFsdWF0aW9uKSB7DQogICAgICBpZiAoIWV2YWx1YXRpb24pIHJldHVybiAnJzsNCg0KICAgICAgY29uc3QgbG93ZXJFdmFsID0gZXZhbHVhdGlvbi50b0xvd2VyQ2FzZSgpOw0KICAgICAgaWYgKGxvd2VyRXZhbC5pbmNsdWRlcygn5LyY56eAJykgfHwgbG93ZXJFdmFsLmluY2x1ZGVzKCfoia/lpb0nKSB8fCBsb3dlckV2YWwuaW5jbHVkZXMoJ+WlvScpKSB7DQogICAgICAgIHJldHVybiAnc3VjY2Vzcyc7DQogICAgICB9IGVsc2UgaWYgKGxvd2VyRXZhbC5pbmNsdWRlcygn5LiA6IisJykgfHwgbG93ZXJFdmFsLmluY2x1ZGVzKCfkuK3nrYknKSkgew0KICAgICAgICByZXR1cm4gJ3dhcm5pbmcnOw0KICAgICAgfSBlbHNlIGlmIChsb3dlckV2YWwuaW5jbHVkZXMoJ+W3ricpIHx8IGxvd2VyRXZhbC5pbmNsdWRlcygn5LiN5ZCI5qC8JykgfHwgbG93ZXJFdmFsLmluY2x1ZGVzKCflpLHotKUnKSkgew0KICAgICAgICByZXR1cm4gJ2Rhbmdlcic7DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gJ2luZm8nOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5YWz6Zet5om55qyh566h55CG5a+56K+d5qGG5bm25riF56m65pWw5o2uICovDQogICAgY2xvc2VCYXRjaE1hbmFnZW1lbnQoKSB7DQogICAgICAvLyDmuIXnqbrmibnmrKHnrqHnkIbnm7jlhbPnmoTmlbDmja4NCiAgICAgIHRoaXMuY3VycmVudEJhdGNoID0gbnVsbDsNCiAgICAgIHRoaXMuaGlzdG9yeUJhdGNoZXMgPSBbXTsNCiAgICAgIHRoaXMuY3VycmVudEJhdGNoRXhwZXJpbWVudHMgPSBbXTsNCiAgICAgIHRoaXMuc2VsZWN0ZWRPcmRlckZvckJhdGNoID0gbnVsbDsNCiAgICAgIHRoaXMuY3VycmVudEJhdGNoUm93ID0gbnVsbDsgLy8g5riF56m65b2T5YmN6KGM5pWw5o2uDQogICAgICB0aGlzLmJhdGNoRGV0YWlsRGF0YSA9IG51bGw7DQoNCiAgICAgIC8vIOmHjee9ruihqOWNleaVsOaNrg0KICAgICAgdGhpcy5leHBlcmltZW50Rm9ybSA9IHsNCiAgICAgICAgZXhwZXJpbWVudENvZGU6ICcnLA0KICAgICAgICBleHBlcmltZW50Tm90ZTogJycNCiAgICAgIH07DQogICAgICB0aGlzLmZpbmlzaEJhdGNoRm9ybSA9IHsNCiAgICAgICAgcXVhbGl0eUV2YWx1YXRpb246ICcnLA0KICAgICAgICByZW1hcms6ICcnDQogICAgICB9Ow0KDQogICAgICAvLyDlhbPpl63miYDmnInnm7jlhbPnmoTlrZDlr7nor53moYYNCiAgICAgIHRoaXMuYmF0Y2hEZXRhaWxPcGVuID0gZmFsc2U7DQogICAgICB0aGlzLmFkZEV4cGVyaW1lbnRPcGVuID0gZmFsc2U7DQogICAgICB0aGlzLmZpbmlzaEJhdGNoT3BlbiA9IGZhbHNlOw0KICAgIH0sDQoNCiAgICAvKiog5YWz6Zet5om55qyh6K+m5oOF5a+56K+d5qGG5bm25riF56m65pWw5o2uICovDQogICAgY2xvc2VCYXRjaERldGFpbCgpIHsNCiAgICAgIC8vIOa4heepuuaJueasoeivpuaDheaVsOaNrg0KICAgICAgdGhpcy5iYXRjaERldGFpbERhdGEgPSBudWxsOw0KICAgIH0sDQoNCiAgICAvKiog5YWz6Zet5re75Yqg5a6e6aqM5a+56K+d5qGG5bm25riF56m65pWw5o2uICovDQogICAgY2xvc2VBZGRFeHBlcmltZW50KCkgew0KICAgICAgLy8g5riF56m65a6e6aqM6KGo5Y2V5pWw5o2uDQogICAgICB0aGlzLmV4cGVyaW1lbnRGb3JtID0gew0KICAgICAgICBleHBlcmltZW50Q29kZTogJycsDQogICAgICAgIGV4cGVyaW1lbnROb3RlOiAnJw0KICAgICAgfTsNCiAgICAgIC8vIOa4hemZpOihqOWNlemqjOivgeeKtuaAgQ0KICAgICAgaWYgKHRoaXMuJHJlZnMuZXhwZXJpbWVudEZvcm0pIHsNCiAgICAgICAgdGhpcy4kcmVmcy5leHBlcmltZW50Rm9ybS5jbGVhclZhbGlkYXRlKCk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDlhbPpl63nu5PmnZ/mibnmrKHlr7nor53moYblubbmuIXnqbrmlbDmja4gKi8NCiAgICBjbG9zZUZpbmlzaEJhdGNoKCkgew0KICAgICAgLy8g5riF56m657uT5p2f5om55qyh6KGo5Y2V5pWw5o2uDQogICAgICB0aGlzLmZpbmlzaEJhdGNoRm9ybSA9IHsNCiAgICAgICAgcXVhbGl0eUV2YWx1YXRpb246ICcnLA0KICAgICAgICByZW1hcms6ICcnDQogICAgICB9Ow0KICAgICAgLy8g5riF6Zmk6KGo5Y2V6aqM6K+B54q25oCBDQogICAgICBpZiAodGhpcy4kcmVmcy5maW5pc2hCYXRjaEZvcm0pIHsNCiAgICAgICAgdGhpcy4kcmVmcy5maW5pc2hCYXRjaEZvcm0uY2xlYXJWYWxpZGF0ZSgpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5Yi35paw5om55qyh5pWw5o2uICovDQogICAgYXN5bmMgcmVmcmVzaEJhdGNoRGF0YSgpIHsNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkT3JkZXJGb3JCYXRjaCkgew0KICAgICAgICBhd2FpdCB0aGlzLmxvYWRCYXRjaERhdGEodGhpcy5zZWxlY3RlZE9yZGVyRm9yQmF0Y2guaWQpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aJueasoeaVsOaNruW3suWIt+aWsCcpOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy2CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/software/engineerSampleOrder", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 统计概览 -->\r\n    <el-row :gutter=\"20\" class=\"stats-row\" style=\"margin-bottom: 20px;\">\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card total\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-s-order\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">总任务</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.total || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card completed\" @click.native=\"handleStatusFilter('2')\" :class=\"{'active': queryParams.completionStatus === '2'}\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-check\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">已完成</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.completed || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card in-progress\" @click.native=\"handleStatusFilter('1')\" :class=\"{'active': queryParams.completionStatus === '1'}\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-loading\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">进行中</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.inProgress || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :xs=\"12\" :sm=\"6\" :md=\"6\" :lg=\"6\" :xl=\"6\">\r\n        <el-card class=\"stats-card overdue\" @click.native=\"handleOverdueFilter\" :class=\"{'active': queryParams.isOverdue === 1}\" style=\"cursor: pointer;\">\r\n          <div class=\"stats-content\">\r\n            <div class=\"stats-icon\">\r\n              <i class=\"el-icon-warning\"></i>\r\n            </div>\r\n            <div class=\"stats-info\">\r\n              <div class=\"stats-title\">逾期任务</div>\r\n              <div class=\"stats-number\">{{ dashboardStats.overdue || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 未分配工程师的打样单告警 -->\r\n    <el-card class=\"alert-card\">\r\n      <div slot=\"header\" class=\"alert-header\">\r\n        <div class=\"alert-title\" @click=\"toggleUnassignedPanel\">\r\n          <span><i class=\"el-icon-warning-outline\"></i>待分配打样单({{ unassignedTotal }}个)</span>\r\n        </div>\r\n        <div class=\"alert-actions\">\r\n          <el-button\r\n            type=\"text\"\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"handleRefreshUnassigned\"\r\n            style=\"margin-right: 10px\"\r\n            title=\"刷新列表\">\r\n            刷新\r\n          </el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            @click=\"toggleUnassignedPanel\"\r\n            style=\"margin-right: 10px\">\r\n            {{ isUnassignedPanelCollapsed ? '展开' : '收起' }}\r\n            <i :class=\"isUnassignedPanelCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\r\n          </el-button>\r\n          <el-pagination\r\n            @size-change=\"handleUnassignedSizeChange\"\r\n            @current-change=\"handleUnassignedCurrentChange\"\r\n            :current-page=\"unassignedQueryParams.pageNum\"\r\n            :page-sizes=\"[8, 16, 24, 32]\"\r\n            :page-size=\"unassignedQueryParams.pageSize\"\r\n            layout=\"total, sizes, prev, pager, next\"\r\n            :total=\"unassignedTotal\">\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n      <el-collapse-transition>\r\n        <div v-show=\"!isUnassignedPanelCollapsed\">\r\n          <el-row :gutter=\"20\" class=\"alert-cards\">\r\n            <el-col :xs=\"24\" :sm=\"12\" :md=\"8\" :lg=\"6\" v-for=\"item in unassignedOrders\" :key=\"item.id\">\r\n              <el-card shadow=\"hover\" class=\"alert-item\">\r\n                <div class=\"alert-item-header\">\r\n                  <div class=\"order-code-section\">\r\n                    <span style=\"color: #00afff;cursor: pointer\"  @click=\"orderDetail(item)\" class=\"order-code\">{{ item.sampleOrderCode }}</span>\r\n                    <el-tag\r\n                      :type=\"getUrgencyType(item.endDate, item.latestStartTime)\"\r\n                      size=\"mini\"\r\n                      class=\"urgency-tag\">\r\n                      {{ getUrgencyText(item.endDate, item.latestStartTime) }}\r\n                    </el-tag>\r\n                  </div>\r\n                </div>\r\n                <div class=\"alert-item-content\">\r\n                  <div class=\"info-section\">\r\n                    <!-- 预估工时和难度等级 -->\r\n                    <div class=\"info-row info-row-double\">\r\n                      <div class=\"info-item date-time-item\" v-if=\"item.latestStartTime\">\r\n                        <i class=\"el-icon-alarm-clock\" style=\"color: #909399;\"></i>\r\n                        <span class=\"info-label\">最晚开始:</span>\r\n                        <span class=\"info-value\">{{ parseTime(item.latestStartTime, '{y}-{m}-{d}') }}</span>\r\n                      </div>\r\n                      <div class=\"info-item standard-item\">\r\n                        <i class=\"el-icon-time\" style=\"color: #409EFF;\"></i>\r\n                        <span class=\"info-label\">预估工时:</span>\r\n                        <span class=\"info-value\">{{ item.estimatedManHours || '-' }}H</span>\r\n                      </div>\r\n                    </div>\r\n                    <!-- 最晚开始日期和截止日期排 -->\r\n                    <div class=\"info-row info-row-double\">\r\n                      <div class=\"info-item date-time-item\">\r\n                        <i class=\"el-icon-date\" style=\"color: #F56C6C;\"></i>\r\n                        <span class=\"info-label\">截止日期:</span>\r\n                        <span class=\"info-value\">{{ parseTime(item.endDate, '{y}-{m}-{d}') }}</span>\r\n                      </div>\r\n                      <div class=\"info-item standard-item\">\r\n                        <i class=\"el-icon-star-on\" style=\"color: #E6A23C;\"></i>\r\n                        <span class=\"info-label\">难度等级:</span>\r\n                        <el-tooltip :content=\"selectDictLabel(dylbOptions, item.difficultyLevelId)\" placement=\"top\">\r\n                          <span class=\"info-value difficulty-text\">{{selectDictLabel(dylbOptions,item.difficultyLevelId).replace(/[^a-zA-Z0-9]/g, '')}}</span>\r\n                        </el-tooltip>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <el-tooltip v-if=\"item.failureReason\" :content=\"item.failureReason\" placement=\"top\">\r\n                    <div class=\"info-reason\">\r\n                      <i class=\"el-icon-warning-outline\"></i>\r\n                      <span class=\"reason-text\">{{ item.failureReason }}</span>\r\n                    </div>\r\n                  </el-tooltip>\r\n                </div>\r\n                <div class=\"alert-item-footer\">\r\n                  <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-user-solid\" @click=\"handleAssignEngineer(item)\" v-hasPermi=\"['software:engineerSampleOrder:changeEngineer']\">\r\n                    分配工程师\r\n                  </el-button>\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-close\" @click=\"handleRejectUnassigned(item)\" v-hasPermi=\"['software:engineerSampleOrder:rejectRask']\" class=\"reject-btn\" style=\"color: #F56C6C;\">\r\n                    驳回\r\n                  </el-button>\r\n                </div>\r\n              </el-card>\r\n           </el-col>\r\n          </el-row>\r\n         </div>\r\n      </el-collapse-transition>\r\n    </el-card>\r\n\r\n    <!-- 筛选条件 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"客户\" prop=\"customerId\">\r\n        <el-select v-model=\"queryParams.customerId\" filterable placeholder=\"客户\" clearable size=\"small\">\r\n          <el-option\r\n            v-for=\"dict in customerOptions\"\r\n            :key=\"dict.id\"\r\n            :label=\"dict.name\"\r\n            :value=\"dict.id\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"日期范围\" prop=\"dateRange\" label-width=\"80px\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"\r\n          style=\"width: 240px\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"排单日期\" prop=\"scheduledDate\">\r\n        <el-date-picker\r\n          v-model=\"scheduledDateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"申请日期\" prop=\"startDate\">\r\n        <el-date-picker\r\n          v-model=\"startDateRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"研发组别\" prop=\"deptIds\">\r\n        <el-select v-model=\"queryParams.deptIds\" multiple filterable placeholder=\"请选择对应研发组别\">\r\n          <el-option\r\n            v-for=\"dept in researchDeptDatas\"\r\n            :key=\"dept.deptId\"\r\n            :label=\"dept.deptName\"\r\n            :value=\"dept.deptId\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"服务模式\" prop=\"serviceMode\">\r\n        <el-select v-model=\"queryParams.serviceMode\" placeholder=\"请选择客户服务模式\" clearable>\r\n          <el-option\r\n            v-for=\"dict in serviceModeOptions\"\r\n            :key=\"dict.dictValue\"\r\n            :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"实验室\" prop=\"laboratory\">\r\n        <el-select v-model=\"queryParams.laboratory\" placeholder=\"请选择实验室\" clearable>\r\n          <el-option label=\"宜侬\" value=\"0\" />\r\n          <el-option label=\"瀛彩\" value=\"1\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"确认编码\" prop=\"confirmCode\">\r\n        <el-input\r\n          v-model=\"queryParams.confirmCode\"\r\n          placeholder=\"请输入确认编码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"产品名称\" prop=\"productName\">\r\n        <el-input\r\n          v-model=\"queryParams.productName\"\r\n          placeholder=\"请输入产品名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"姓名\" prop=\"nickName\" label-width=\"50px\">\r\n        <el-input v-model=\"queryParams.nickName\" placeholder=\"请输入工程师姓名\" clearable/>\r\n      </el-form-item>\r\n      <el-form-item label=\"打样单编号\" label-width=\"90px\" prop=\"sampleOrderCode\">\r\n        <el-input v-model=\"queryParams.sampleOrderCode\" placeholder=\"请输入打样单编号\" clearable/>\r\n      </el-form-item>\r\n      <el-form-item label=\"完成情况\" prop=\"completionStatus\">\r\n        <el-select v-model=\"queryParams.completionStatus\" placeholder=\"请选择完成情况\" clearable>\r\n          <el-option\r\n            v-for=\"dict in statusOptions\"\r\n            :key=\"dict.dictValue\"\r\n            :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"实际开始时间\" prop=\"actualStartTime\" label-width=\"100px\">\r\n        <el-date-picker\r\n          v-model=\"actualStartTimeRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"实际完成时间\" prop=\"actualFinishTime\" label-width=\"100px\">\r\n        <el-date-picker\r\n          v-model=\"actualFinishTimeRange\"\r\n          type=\"daterange\"\r\n          :picker-options=\"dataPickerOptions\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          align=\"right\"/>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作按钮 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:add']\">新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" plain icon=\"el-icon-edit\" size=\"mini\" :disabled=\"single\" @click=\"handleUpdate\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:edit']\">修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:remove']\">删除</el-button>\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"doExportSampleOrder\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:export']\">导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" plain icon=\"el-icon-download\" size=\"mini\" :disabled=\"multiple\" @click=\"handleBatchExportNrw\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:export']\">导出打样单任务</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 主要内容区域 -->\r\n     <!-- 工单列表 -->\r\n    <el-table v-loading=\"loading\" :data=\"engineerSampleOrderList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <!-- <el-table-column label=\"主键ID\" align=\"center\" prop=\"id\" /> -->\r\n<!--      <el-table-column label=\"工程师ID\" align=\"center\" prop=\"userId\" />-->\r\n      <el-table-column align=\"center\" prop=\"sampleOrderCode\" width=\"160\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          打样单编号\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              点击编号可查看详细信息\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-button type=\"text\" @click=\"orderDetail(scope.row)\">{{ scope.row.sampleOrderCode }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"startDate\" width=\"140\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          申请日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              客户提交打样申请的日期时间\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d} {h}:{i}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"scheduledDate\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          排单日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              工程师被安排处理此打样单的日期\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.scheduledDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"latestStartTime\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          最晚开始日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              必须在此日期前开始工作，否则可能影响交期\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.latestStartTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"endDate\" width=\"120\" fixed=\"left\">\r\n        <template slot=\"header\">\r\n          最晚截至日期\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              打样工作必须在此日期前完成\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\" prop=\"completionStatus\">\r\n        <template slot=\"header\">\r\n          完成情况\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              当前打样单的状态\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"scope.row.completionStatus == '0' ? 'info' :\r\n                   scope.row.completionStatus == '1' ? 'primary' :\r\n                   scope.row.completionStatus == '2' ? 'success' : 'info'\"\r\n            size=\"mini\"\r\n          >\r\n            {{selectDictLabel(statusOptions,scope.row.completionStatus)}}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\">\r\n        <template slot=\"header\">\r\n          批次信息\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              显示当前批次进度，格式：当前批次/总批次\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"scope.row.totalBatches > 0\">\r\n            <el-tag size=\"mini\" type=\"primary\">\r\n              {{ scope.row.currentBatch || 0 }}/{{ scope.row.totalBatches || 0 }}\r\n            </el-tag>\r\n          </div>\r\n          <div v-else>\r\n            <el-tag size=\"mini\" type=\"info\">未开始</el-tag>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" width=\"90\">\r\n        <template slot=\"header\">\r\n          逾期情况\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              根据截止日期判断是否逾期及逾期天数\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <div v-if=\"getOverdueInfo(scope.row).isOverdue\">\r\n            <el-tag type=\"danger\" size=\"mini\" style=\"margin-bottom: 2px;\">\r\n              逾期 {{ getOverdueInfo(scope.row).overdueDays }}天\r\n            </el-tag>\r\n          </div>\r\n          <el-tag v-else type=\"success\" size=\"mini\">\r\n            正常\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"nickName\" width=\"95\">\r\n        <template slot=\"header\">\r\n          跟进人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此打样单的工程师姓名及职级\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.nickName }}{{ scope.row.rank ? '-' + scope.row.rank : '' }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"assistantName\" width=\"95\">\r\n        <template slot=\"header\">\r\n          协助人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              此打样单难度高于工程师能力范围\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.assistantName }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"isLocked\" width=\"90\">\r\n        <template slot=\"header\">\r\n          锁定状态\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              锁定后不允许更换工程师，防止误操作\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-switch\r\n            v-model=\"scope.row.isLocked\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n            @change=\"(val) => handleTableLockChange(val, scope.row)\">\r\n          </el-switch>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"laboratoryCode\" width=\"150\">\r\n        <template slot=\"header\">\r\n          实验编码\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              实验室分配的唯一编码标识\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"difficultyLevelId\" width=\"80\">\r\n        <template slot=\"header\">\r\n          难度\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              打样工作的技术难度等级\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"selectDictLabel(dylbOptions,scope.row.difficultyLevelId)\" placement=\"top\">\r\n            <span>{{selectDictLabel(dylbOptions,scope.row.difficultyLevelId).replace(/[^a-zA-Z0-9]/g, '')}}</span>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"标准工时\" align=\"center\" prop=\"standardManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.standardManHours\">{{ scope.row.standardManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"laboratory\" width=\"90\">\r\n        <template slot=\"header\">\r\n          实验室\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此打样单的实验室分支\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{\r\n            scope.row.laboratory === '0' ? '宜侬' :\r\n            scope.row.laboratory === '1' ? '瀛彩' :\r\n            ''\r\n          }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"categoryName\" width=\"70\">\r\n        <template slot=\"header\">\r\n          品类\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              产品所属的分类类别\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"SKU数\" align=\"center\" prop=\"sku\" width=\"70\" />\r\n      <el-table-column align=\"center\" prop=\"applicant\" width=\"90\">\r\n        <template slot=\"header\">\r\n          申请人\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              提交此打样申请的人员\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"sales\" width=\"70\">\r\n        <template slot=\"header\">\r\n          销售\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              负责此项目的销售人员\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"customerName\" width=\"150\">\r\n        <template slot=\"header\">\r\n          客户名称\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              委托打样的客户公司名称\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"产品/项目等级\" align=\"center\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getProjectLevel(scope.row) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" prop=\"productName\" width=\"150\">\r\n        <template slot=\"header\">\r\n          产品名称\r\n          <el-tooltip placement=\"top\">\r\n            <i class=\"el-icon-question\"></i>\r\n            <div slot=\"content\">\r\n              需要打样的产品名称\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"实际工作时间\" align=\"center\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <div>\r\n            <div v-if=\"scope.row.actualStartTime\">开始: {{ parseTime(scope.row.actualStartTime, '{y}-{m}-{d} {h}:{i}') }}</div>\r\n            <div v-if=\"scope.row.actualFinishTime\">完成: {{ parseTime(scope.row.actualFinishTime, '{y}-{m}-{d} {h}:{i}') }}</div>\r\n            <span v-if=\"!scope.row.actualStartTime && !scope.row.actualFinishTime\">-</span>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"实际工时\" align=\"center\" prop=\"actualManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.actualManHours\">{{ scope.row.actualManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"预估工时\" align=\"center\" prop=\"estimatedManHours\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.estimatedManHours\">{{ scope.row.estimatedManHours }}H</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"服务模式\" align=\"center\" prop=\"serviceMode\" width=\"150\" />\r\n      <el-table-column label=\"计算类型\" align=\"center\" prop=\"checkType\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.checkType == 1\">客户等级</span>\r\n          <span v-else>打样单难度</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"完成质量\" align=\"center\" prop=\"qualityEvaluation\" />\r\n<!--      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"140\">-->\r\n<!--        <template slot-scope=\"scope\">-->\r\n<!--          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>-->\r\n<!--        </template>-->\r\n<!--      </el-table-column>-->\r\n      <el-table-column label=\"打样单备注\" align=\"center\" prop=\"sampleOrderRemark\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.sampleOrderRemark\" placement=\"top\" :disabled=\"!scope.row.sampleOrderRemark\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.sampleOrderRemark || '-' }}\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.remark\" placement=\"top\" :disabled=\"!scope.row.remark\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.remark || '-' }}\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"未出样原因\" align=\"center\" prop=\"reasonForNoSample\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.reasonForNoSample\" placement=\"top\" :disabled=\"!scope.row.reasonForNoSample\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.reasonForNoSample || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"解决方案\" align=\"center\" prop=\"solution\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.solution\" placement=\"top\" :disabled=\"!scope.row.solution\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.solution || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"驳回原因\" align=\"center\" prop=\"rejectReason\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tooltip :content=\"scope.row.rejectReason\" placement=\"top\" :disabled=\"!scope.row.rejectReason\">\r\n            <div class=\"remark-text-ellipsis\">\r\n              {{ scope.row.rejectReason || '-' }}\r\n            </div>\r\n            </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"fixed-width\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <!-- <el-tooltip content=\"编辑\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:edit']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"删除\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:remove']\" />\r\n          </el-tooltip> -->\r\n          <el-tooltip  v-if=\"scope.row.completionStatus==0\" content=\"更换工程师或更改日期\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-user\" v-if=\"scope.row.isLocked === 0\" @click=\"handleChangeEngineer(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:changeEngineer']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"开始任务\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-video-play\" v-if=\"scope.row.completionStatus == '0'\" @click=\"handleStart(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:startRask']\" />\r\n          </el-tooltip>\r\n\r\n          <el-tooltip content=\"批次管理\" v-if=\"[0,1,2].includes(scope.row.completionStatus)\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-collection\" @click=\"handleBatchManagement(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:batchManagement']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"逾期操作\" v-if=\"getOverdueInfo(scope.row).isOverdue\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-warning\" @click=\"handleOverdueOperation(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:overdueOperation']\" style=\"color: #E6A23C;\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"驳回\" v-if=\"scope.row.completionStatus == 0\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-close\" @click=\"handleReject(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:rejectRask']\" style=\"color: #F56C6C;\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"更新实验室编码\" v-if=\"scope.row.completionStatus == '2' && scope.row.itemStatus == 0\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit-outline\" @click=\"handleUpdateLaboratoryCode(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:editSampleOrderCode']\" />\r\n          </el-tooltip>\r\n          <el-tooltip content=\"下载打样单\" placement=\"top\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-download\" @click=\"handleDownloadSampleOrder(scope.row)\" v-hasPermi=\"['software:engineerSampleOrder:export']\" />\r\n          </el-tooltip>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n      :auto-scroll=\"false\" @pagination=\"getList\" />\r\n\r\n    <!-- 添加或修改工程师打样单关联对话框 -->\r\n    <!-- <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"650px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程师ID\" prop=\"userId\">\r\n              <el-input v-model=\"form.userId\" placeholder=\"请输入工程师ID\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"打样单编号\" prop=\"sampleOrderCode\">\r\n              <el-input v-model=\"form.sampleOrderCode\" placeholder=\"请输入打样单编号\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"难度\" prop=\"difficultyLevelId\">\r\n              <el-select v-model=\"form.difficultyLevelId\" placeholder=\"请选择打样难度等级\">\r\n                  <el-option\r\n                    v-for=\"dict in dylbOptions\"\r\n                    :key=\"dict.dictValue\"\r\n                    :label=\"dict.dictLabel\"\r\n                    :value=\"dict.dictValue\"\r\n                  ></el-option>\r\n                </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"完成情况\" prop=\"completionStatus\">\r\n              <el-select v-model=\"form.completionStatus\" placeholder=\"请选择完成情况\" style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"dict in statusOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"排单日期\" prop=\"scheduledDate\">\r\n              <el-date-picker\r\n                v-model=\"form.scheduledDate\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择排单日期\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"最晚截至日期\" prop=\"endDate\">\r\n              <el-date-picker\r\n                v-model=\"form.endDate\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择最晚截至日期\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"锁定状态\" prop=\"isLocked\">\r\n              <el-switch\r\n                v-model=\"form.isLocked\"\r\n                :active-value=\"1\"\r\n                :inactive-value=\"0\"\r\n                active-text=\"已锁定\"\r\n                inactive-text=\"未锁定\"\r\n                @change=\"handleLockChange\">\r\n              </el-switch>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际开始时间\" prop=\"actualStartTime\">\r\n              <el-date-picker\r\n                v-model=\"form.actualStartTime\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择开始时间\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际完成时间\" prop=\"actualFinishTime\">\r\n              <el-date-picker\r\n                v-model=\"form.actualFinishTime\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择完成时间\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际工时\" prop=\"actualManHours\">\r\n              <el-input-number v-model=\"form.actualManHours\" :min=\"0\" :precision=\"1\" :step=\"0.5\" style=\"width: 100%\" placeholder=\"请输入实际工时\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"预估工时\" prop=\"estimatedManHours\">\r\n              <el-input-number v-model=\"form.estimatedManHours\" :min=\"0\" :precision=\"1\" :step=\"0.5\" style=\"width: 100%\" placeholder=\"请输入预估工时\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"完成质量\" prop=\"qualityEvaluation\">\r\n              <el-input v-model=\"form.qualityEvaluation\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入完成质量情况\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"打样单备注\" prop=\"sampleOrderRemark\">\r\n              <el-input v-model=\"form.sampleOrderRemark\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入打样单备注信息\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"备注\" prop=\"remark\">\r\n              <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入备注信息\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog> -->\r\n\r\n    <!-- 更改工程师对话框 -->\r\n    <el-dialog title=\"更改工程师\" :visible.sync=\"changeEngineerOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"changeEngineerForm\" :model=\"changeEngineerForm\" :rules=\"changeEngineerRules\" label-width=\"150px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"changeEngineerForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"当前工程师\">\r\n          <el-input v-model=\"changeEngineerForm.currentEngineerName\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"选择工程师\" prop=\"newEngineerId\">\r\n          <el-radio-group v-model=\"engineerSelectType\" style=\"margin-bottom: 15px;\">\r\n            <el-radio label=\"specified\">指定部门工程师</el-radio>\r\n            <el-radio label=\"other\">其他部门工程师</el-radio>\r\n          </el-radio-group>\r\n\r\n          <!-- 指定工程师选择 -->\r\n          <div v-show=\"engineerSelectType === 'specified'\">\r\n            <el-select\r\n              v-model=\"changeEngineerForm.newEngineerId\"\r\n              placeholder=\"请选择工程师\"\r\n              style=\"width: 100%\"\r\n              filterable>\r\n              <el-option\r\n                v-for=\"engineer in engineerOptions\"\r\n                :key=\"engineer.userId\"\r\n                :label=\"engineer.nickName\"\r\n                :value=\"engineer.userId\"\r\n              />\r\n            </el-select>\r\n          </div>\r\n\r\n          <!-- 其他工程师选择 -->\r\n          <div v-show=\"engineerSelectType === 'other'\" class=\"other-engineer-select\">\r\n            <div class=\"select-item\" style=\"margin-top: 10px;\">\r\n              <el-select\r\n                v-model=\"changeEngineerForm.newEngineerId\"\r\n                placeholder=\"请选择工程师\"\r\n                style=\"width: 100%\"\r\n                filterable\r\n                remote>\r\n                <el-option\r\n                  v-for=\"engineer in searchedEngineers\"\r\n                  :key=\"engineer.userId\"\r\n                  :label=\"engineer.nickName\"\r\n                  :value=\"engineer.userId\"\r\n                />\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"选择指定日期\" prop=\"scheduledDate\">\r\n          <el-date-picker\r\n            v-model=\"changeEngineerForm.scheduledDate\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择日期\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            :picker-options=\"futureDatePickerOptions\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"重新调整工作安排\">\r\n          <el-switch\r\n            v-model=\"changeEngineerForm.adjustWorkSchedule\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n            active-text=\"是\"\r\n            inactive-text=\"否\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitChangeEngineer\">确 定</el-button>\r\n        <el-button @click=\"changeEngineerOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批次管理对话框 -->\r\n    <el-dialog title=\"批次管理\" :visible.sync=\"batchManagementOpen\" width=\"900px\" append-to-body @close=\"closeBatchManagement\">\r\n      <div class=\"batch-management-container\">\r\n        <!-- 状态提示信息 -->\r\n        <el-alert\r\n          v-if=\"!canEditBatch\"\r\n          title=\"当前打样单状态不允许编辑批次信息\"\r\n          type=\"warning\"\r\n          description=\"只有状态为'进行中'的打样单才可以编辑和添加测试批次内容，其他状态只能查看批次信息。\"\r\n          show-icon\r\n          :closable=\"false\"\r\n          style=\"margin-bottom: 20px;\">\r\n        </el-alert>\r\n\r\n        <!-- 当前批次信息 -->\r\n        <el-card class=\"current-batch-card\" v-if=\"currentBatch\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>当前批次 - 第{{ currentBatch.batchIndex }}批次</span>\r\n            <el-button\r\n              v-if=\"canEditBatch\"\r\n              style=\"float: right; padding: 3px 0\"\r\n              type=\"text\"\r\n              @click=\"finishCurrentBatch\">\r\n              结束当前批次\r\n            </el-button>\r\n            <el-tag\r\n              v-else\r\n              style=\"float: right;\"\r\n              type=\"info\"\r\n              size=\"mini\">\r\n              只读模式\r\n            </el-tag>\r\n          </div>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"8\">\r\n              <div class=\"batch-info-item\">\r\n                <label>开始时间：</label>\r\n                <span>{{ parseTime(currentBatch.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <div class=\"batch-info-item\">\r\n                <label>已用时长：</label>\r\n                <span>{{ calculateDuration(currentBatch.startTime) }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <div class=\"batch-info-item\">\r\n                <label>状态：</label>\r\n                <el-tag type=\"primary\" size=\"mini\">进行中</el-tag>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <!-- 实验记录管理 -->\r\n          <div class=\"experiment-section\" style=\"margin-top: 20px;\">\r\n            <div class=\"section-header\">\r\n              <span>实验记录</span>\r\n              <el-button\r\n                v-if=\"canEditBatch\"\r\n                size=\"mini\"\r\n                type=\"primary\"\r\n                @click=\"addExperiment\">\r\n                添加实验\r\n              </el-button>\r\n              <el-tag\r\n                v-else\r\n                size=\"mini\"\r\n                type=\"info\">\r\n                只读模式\r\n              </el-tag>\r\n            </div>\r\n            <el-table :data=\"currentBatchExperiments\" size=\"mini\" style=\"margin-top: 10px;\">\r\n              <el-table-column prop=\"experimentCode\" label=\"实验编号\" width=\"150\"></el-table-column>\r\n              <el-table-column prop=\"experimentNote\" label=\"实验备注\"></el-table-column>\r\n              <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"150\">\r\n                <template slot-scope=\"scope\">\r\n                  {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </el-card>\r\n\r\n        <!-- 开始新批次按钮 -->\r\n        <div class=\"new-batch-section\" v-if=\"!currentBatch\" style=\"text-align: center; margin: 20px 0;\">\r\n          <el-button\r\n            v-if=\"canEditBatch\"\r\n            type=\"primary\"\r\n            size=\"medium\"\r\n            @click=\"startNewBatch\">\r\n            开始新批次\r\n          </el-button>\r\n          <div v-else style=\"text-align: center; color: #909399;\">\r\n            <i class=\"el-icon-info\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"margin-top: 16px;\">当前状态不允许开始新批次</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 历史批次列表 -->\r\n        <el-card class=\"history-batches-card\" style=\"margin-top: 20px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>历史批次</span>\r\n            <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"refreshBatchData\">刷新</el-button>\r\n          </div>\r\n          <el-table :data=\"historyBatches\" size=\"small\" border stripe>\r\n            <el-table-column prop=\"batchIndex\" label=\"批次序号\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"primary\">第{{ scope.row.batchIndex }}批次</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"startTime\" label=\"开始时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                {{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"endTime\" label=\"结束时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.endTime\">{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}') }}</span>\r\n                <span v-else style=\"color: #909399;\">未结束</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"actualManHours\" label=\"实际工时\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" :type=\"scope.row.actualManHours > 8 ? 'warning' : 'success'\">\r\n                  {{ scope.row.actualManHours || 0 }}小时\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"qualityEvaluation\" label=\"质量评价\" width=\"120\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag v-if=\"scope.row.qualityEvaluation\" size=\"mini\"\r\n                        :type=\"getQualityEvaluationType(scope.row.qualityEvaluation)\">\r\n                  {{ scope.row.qualityEvaluation }}\r\n                </el-tag>\r\n                <span v-else style=\"color: #909399;\">未评价</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"remark\" label=\"批次备注\" min-width=\"150\" show-overflow-tooltip>\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.remark\" style=\"color: #606266;\">{{ scope.row.remark }}</span>\r\n                <span v-else style=\"color: #C0C4CC;\">无备注</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"120\" align=\"center\" fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button size=\"mini\" type=\"text\" @click=\"viewBatchDetail(scope.row)\" icon=\"el-icon-view\">\r\n                  查看详情\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n\r\n          <!-- 空状态 -->\r\n          <div v-if=\"!historyBatches || historyBatches.length === 0\" class=\"empty-state\" style=\"text-align: center; padding: 40px;\">\r\n            <i class=\"el-icon-document\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"color: #909399; margin-top: 16px;\">暂无历史批次记录</p>\r\n          </div>\r\n        </el-card>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button\r\n          v-if=\"currentBatchRow && currentBatchRow.completionStatus == '1'\"\r\n          type=\"primary\"\r\n          icon=\"el-icon-check\"\r\n          @click=\"handleFinishFromBatch\"\r\n          v-hasPermi=\"['software:engineerSampleOrder:endRask']\">\r\n          完成任务\r\n        </el-button>\r\n        <el-button @click=\"batchManagementOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批次详情对话框 -->\r\n    <el-dialog title=\"批次详情\" :visible.sync=\"batchDetailOpen\" width=\"800px\" append-to-body @close=\"closeBatchDetail\">\r\n      <div v-if=\"batchDetailData\" class=\"batch-detail-container\">\r\n        <!-- 批次基本信息 -->\r\n        <el-card class=\"batch-info-card\" style=\"margin-bottom: 20px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>批次基本信息</span>\r\n          </div>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>批次序号：</label>\r\n                <el-tag type=\"primary\">第{{ batchDetailData.batchIndex }}批次</el-tag>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>开始时间：</label>\r\n                <span class=\"info-value\">{{ parseTime(batchDetailData.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>结束时间：</label>\r\n                <span class=\"info-value\">\r\n                  {{ batchDetailData.endTime ? parseTime(batchDetailData.endTime, '{y}-{m}-{d} {h}:{i}:{s}') : '未结束' }}\r\n                </span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>实际工时：</label>\r\n                <el-tag :type=\"batchDetailData.actualManHours > 8 ? 'warning' : 'success'\">\r\n                  {{ batchDetailData.actualManHours || 0 }}小时\r\n                </el-tag>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>质量评价：</label>\r\n                <el-tag v-if=\"batchDetailData.qualityEvaluation\"\r\n                        :type=\"getQualityEvaluationType(batchDetailData.qualityEvaluation)\">\r\n                  {{ batchDetailData.qualityEvaluation }}\r\n                </el-tag>\r\n                <span v-else class=\"info-value\">未评价</span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row style=\"margin-top: 15px;\">\r\n            <el-col :span=\"24\">\r\n              <div class=\"info-item\">\r\n                <label>批次备注：</label>\r\n                <div class=\"remark-content\">\r\n                  {{ batchDetailData.remark || '无备注' }}\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n\r\n        <!-- 实验记录详情 -->\r\n        <el-card class=\"experiments-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>实验记录详情</span>\r\n            <el-tag size=\"mini\" style=\"margin-left: 10px;\">\r\n              共{{ batchDetailData.experiments ? batchDetailData.experiments.length : 0 }}条记录\r\n            </el-tag>\r\n          </div>\r\n          <el-table :data=\"batchDetailData.experiments\" size=\"small\" border stripe>\r\n            <el-table-column prop=\"experimentCode\" label=\"实验编号\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span style=\"color: #409EFF; font-weight: bold;\">{{ scope.row.experimentCode }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"experimentNote\" label=\"实验备注\" min-width=\"200\" show-overflow-tooltip>\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.experimentNote\">{{ scope.row.experimentNote }}</span>\r\n                <span v-else style=\"color: #C0C4CC;\">无备注</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"createTime\" label=\"创建时间\" width=\"150\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"createBy\" label=\"创建人\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"info\">{{ scope.row.createBy || '系统' }}</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n\r\n          <!-- 实验记录空状态 -->\r\n          <div v-if=\"!batchDetailData.experiments || batchDetailData.experiments.length === 0\"\r\n               class=\"empty-experiments\" style=\"text-align: center; padding: 40px;\">\r\n            <i class=\"el-icon-data-line\" style=\"font-size: 48px; color: #C0C4CC;\"></i>\r\n            <p style=\"color: #909399; margin-top: 16px;\">该批次暂无实验记录</p>\r\n          </div>\r\n        </el-card>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"batchDetailOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加实验对话框 -->\r\n    <el-dialog title=\"添加实验记录\" :visible.sync=\"addExperimentOpen\" width=\"500px\" append-to-body @close=\"closeAddExperiment\">\r\n      <el-form :model=\"experimentForm\" :rules=\"experimentRules\" ref=\"experimentForm\" label-width=\"100px\">\r\n        <el-form-item label=\"实验编号\" prop=\"experimentCode\">\r\n          <el-input v-model=\"experimentForm.experimentCode\" placeholder=\"请输入实验编号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"实验备注\" prop=\"experimentNote\">\r\n          <el-input v-model=\"experimentForm.experimentNote\" type=\"textarea\" placeholder=\"请输入实验备注\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitExperiment\">确 定</el-button>\r\n        <el-button @click=\"addExperimentOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 结束批次对话框 -->\r\n    <el-dialog title=\"结束当前批次\" :visible.sync=\"finishBatchOpen\" width=\"500px\" append-to-body @close=\"closeFinishBatch\">\r\n      <el-form :model=\"finishBatchForm\" ref=\"finishBatchForm\" label-width=\"100px\">\r\n        <el-form-item label=\"质量评价\">\r\n          <el-input v-model=\"finishBatchForm.qualityEvaluation\" type=\"textarea\" placeholder=\"请输入质量评价\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"finishBatchForm.remark\" type=\"textarea\" placeholder=\"请输入备注\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFinishBatch\">确 定</el-button>\r\n        <el-button @click=\"finishBatchOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 完成任务对话框 -->\r\n    <el-dialog title=\"完成任务\" :visible.sync=\"finishTaskOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"finishTaskForm\" :model=\"finishTaskForm\" :rules=\"finishTaskRules\" label-width=\"120px\">\r\n        <el-form-item label=\"实验室编码\" prop=\"laboratoryCode\">\r\n          <el-input v-model=\"finishTaskForm.laboratoryCode\" placeholder=\"请输入实验室编码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"finishTaskForm.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注信息（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"finishTaskOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmFinishTask\" :loading=\"finishTaskLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 更新实验室编码对话框 -->\r\n    <el-dialog title=\"更新实验室编码\" :visible.sync=\"updateLaboratoryCodeOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"updateLaboratoryCodeForm\" :model=\"updateLaboratoryCodeForm\" :rules=\"updateLaboratoryCodeRules\" label-width=\"120px\">\r\n        <el-form-item label=\"实验室编码\" prop=\"laboratoryCode\">\r\n          <el-input v-model=\"updateLaboratoryCodeForm.laboratoryCode\" placeholder=\"请输入实验室编码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"updateLaboratoryCodeForm.remark\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入备注信息（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"updateLaboratoryCodeOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmUpdateLaboratoryCode\" :loading=\"updateLaboratoryCodeLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 导出选择对话框 -->\r\n    <el-dialog title=\"选择导出内容\" :visible.sync=\"exportDialogOpen\" width=\"400px\" append-to-body>\r\n      <div class=\"export-options\">\r\n        <p style=\"margin-bottom: 20px; color: #606266;\">请选择要导出的打样单类型：</p>\r\n        <el-checkbox-group v-model=\"exportOptions\" style=\"display: flex; flex-direction: column;\">\r\n          <el-checkbox label=\"assigned\" style=\"margin-bottom: 15px;\">\r\n            <span style=\"font-weight: 500;\">已分配</span>\r\n            <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\r\n              导出已分配给工程师的打样单数据\r\n            </div>\r\n          </el-checkbox>\r\n          <el-checkbox label=\"unassigned\" style=\"margin-bottom: 15px;\">\r\n            <span style=\"font-weight: 500;\">未分配</span>\r\n            <div style=\"color: #909399; font-size: 12px; margin-top: 5px;\">\r\n              导出尚未分配工程师的打样单数据\r\n            </div>\r\n          </el-checkbox>\r\n        </el-checkbox-group>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"exportDialogOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmExport\" :disabled=\"exportOptions.length === 0\" :loading=\"exportLoading\">\r\n          确认导出\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 驳回对话框 -->\r\n    <el-dialog title=\"驳回打样单\" :visible.sync=\"rejectDialogOpen\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"rejectForm\" :model=\"rejectForm\" :rules=\"rejectRules\" label-width=\"100px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"rejectForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"驳回理由\" prop=\"rejectReason\">\r\n          <el-input v-model=\"rejectForm.rejectReason\" type=\"textarea\" :rows=\"4\" placeholder=\"请输入驳回理由\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"rejectDialogOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmReject\" :loading=\"rejectLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 逾期操作对话框 -->\r\n    <el-dialog title=\"逾期操作\" :visible.sync=\"overdueOperationOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"overdueOperationForm\" :model=\"overdueOperationForm\" label-width=\"120px\">\r\n        <el-form-item label=\"打样单编号\">\r\n          <el-input v-model=\"overdueOperationForm.sampleOrderCode\" disabled />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"预计出样时间\">\r\n          <el-date-picker\r\n            v-model=\"overdueOperationForm.expectedSampleTime\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择日期\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            :picker-options=\"futureDatePickerOptions\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"未出样原因\">\r\n          <el-input v-model=\"overdueOperationForm.reasonForNoSample\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入未出样原因（非必填）\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"解决方案\">\r\n          <el-input v-model=\"overdueOperationForm.solution\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入解决方案（非必填）\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"overdueOperationOpen = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmOverdueOperation\" :loading=\"overdueOperationLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n    <!-- 添加或修改项目流程进行对话框 -->\r\n    <executionAddOrEdit ref=\"executionAddOrEdit\"></executionAddOrEdit>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listEngineerSampleOrder,\r\n  getEngineerSampleOrder,\r\n  delEngineerSampleOrder,\r\n  addEngineerSampleOrder,\r\n  updateEngineerSampleOrder,\r\n  updateSampleOrderStatus,\r\n  getDashboardStats,\r\n  getResearchDepartments,\r\n  getEngineersByDifficultyLevel,\r\n  changeEngineer,\r\n  getResearchDepartmentsUser,\r\n  getBatchesByOrderId,\r\n  getCurrentBatch,\r\n  startNewBatch,\r\n  finishCurrentBatch,\r\n  addExperimentToBatch,\r\n  getExperimentsByBatchId,\r\n  exportEngineerSampleOrder, getExecutionByOrderInfo\r\n} from \"@/api/software/engineerSampleOrder\";\r\nimport {exportNrwItem, exportMultipleNrw} from \"@/api/project/projectItemOrder\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\nimport {customerBaseAll} from \"@/api/customer/customer\";\r\nimport {isNull} from \"@/utils/validate\";\r\nimport executionAddOrEdit from \"@/components/Project/components/executionAddOrEdit.vue\";\r\n\r\nexport default {\r\n  name: \"EngineerSampleOrder\",\r\n  components: {\r\n     Treeselect,executionAddOrEdit\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 选中的完整行数据\r\n      selectedRows: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 工程师打样单关联表格数据\r\n      engineerSampleOrderList: [],\r\n      // 研发部部门树列表\r\n      researchDeptDatas:[],\r\n      // 打样类别字典\r\n      dylbOptions: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        userId: null,\r\n        nickName: null,\r\n        sampleOrderCode: null,\r\n        completionStatus: null,\r\n        scheduledDate: null,\r\n        startDate: null,\r\n        actualStartTime: null,\r\n        actualFinishTime: null,\r\n        deptId: null,\r\n        deptIds: [],\r\n        associationStatus: null,\r\n        customerId: null,\r\n        productName: null,\r\n        confirmCode: null,\r\n        isOverdue: null,  // 增逾期任务过滤参数\r\n        laboratory: null  // 实验室筛选参数\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        userId: [\r\n          { required: true, message: \"工程师ID不能为空\", trigger: \"blur\" }\r\n        ],\r\n        sampleOrderCode: [\r\n          { required: true, message: \"打样单编号不能为空\", trigger: \"blur\" }\r\n        ],\r\n        completionStatus: [\r\n          { required: true, message: \"完成情况不能为空\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      statusOptions: [],\r\n      serviceModeOptions: [],\r\n      dateRange: [], // 新增的日期范围筛选\r\n      scheduledDateRange: [],\r\n      startDateRange: [],\r\n      actualStartTimeRange: [],\r\n      actualFinishTimeRange: [],\r\n      // 日期选择器配置\r\n      dataPickerOptions: {\r\n        shortcuts: [{\r\n          text: '今天',\r\n          onClick(picker) {\r\n            const today = new Date();\r\n            picker.$emit('pick', [today, today]);\r\n          }\r\n        }, {\r\n          text: '昨天',\r\n          onClick(picker) {\r\n            const yesterday = new Date();\r\n            yesterday.setTime(yesterday.getTime() - 3600 * 1000 * 24);\r\n            picker.$emit('pick', [yesterday, yesterday]);\r\n          }\r\n        }, {\r\n          text: '最近一周',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }, {\r\n          text: '最近一个月',\r\n          onClick(picker) {\r\n            const end = new Date();\r\n            const start = new Date();\r\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\r\n            picker.$emit('pick', [start, end]);\r\n          }\r\n        }]\r\n      },\r\n      // 状态更新对话框\r\n      statusOpen: false,\r\n      dashboardStats: {\r\n        \"total\": 0,\r\n        \"completed\": 0,\r\n        \"inProgress\": 0,\r\n        \"overdue\": 0\r\n      },\r\n      // 更改工程师对话框\r\n      changeEngineerOpen: false,\r\n      // 更改工程师表单\r\n      changeEngineerForm: {\r\n        id: null,\r\n        sampleOrderCode: null,\r\n        currentEngineerName: null,\r\n        oldEngineerId: null,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      },\r\n      // 更改工程师表单校验\r\n      changeEngineerRules: {\r\n        newEngineerId: [\r\n          { required: true, message: \"请选择工程师\", trigger: \"change\" }\r\n        ],\r\n        scheduledDate: [\r\n          { required: true, message: \"请选择日期\", trigger: \"change\" }\r\n        ]\r\n      },\r\n      // 工程师选项\r\n      engineerOptions: [],\r\n      // 批次管理相关\r\n      batchManagementOpen: false,\r\n      currentBatch: null,\r\n      historyBatches: [],\r\n      currentBatchExperiments: [],\r\n      selectedOrderForBatch: null,\r\n      currentBatchRow: null, // 当前批次管理的行数据\r\n      // 批次详情对话框\r\n      batchDetailOpen: false,\r\n      batchDetailData: null,\r\n      // 添加实验对话框\r\n      addExperimentOpen: false,\r\n      experimentForm: {\r\n        experimentCode: '',\r\n        experimentNote: ''\r\n      },\r\n      experimentRules: {\r\n        experimentCode: [\r\n          { required: true, message: \"实验编号不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      // 结束批次对话框\r\n      finishBatchOpen: false,\r\n      finishBatchForm: {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      },\r\n      // 未来日期选择器配置\r\n      futureDatePickerOptions: {\r\n        disabledDate(time) {\r\n          return time.getTime() < Date.now() - 8.64e7; // 禁用今天之前的日期\r\n        }\r\n      },\r\n      engineerSelectType: 'specified',\r\n      searchedEngineers: [],\r\n      // 未分配打样单告警\r\n      unassignedOrders: [],\r\n      unassignedQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 8\r\n      },\r\n      unassignedTotal: 0,\r\n      // 未分配面板折叠状态\r\n      isUnassignedPanelCollapsed: true,\r\n      // 完成任务对话框\r\n      finishTaskOpen: false,\r\n      finishTaskLoading: false,\r\n      finishTaskForm: {\r\n        laboratoryCode: '',\r\n        remark: ''\r\n      },\r\n      finishTaskRules: {\r\n        laboratoryCode: [\r\n          { required: true, message: \"实验室编码不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentFinishRow: null,\r\n      // 更新实验室编码对话框\r\n      updateLaboratoryCodeOpen: false,\r\n      updateLaboratoryCodeLoading: false,\r\n      updateLaboratoryCodeForm: {\r\n        laboratoryCode: '',\r\n        remark: ''\r\n      },\r\n      updateLaboratoryCodeRules: {\r\n        laboratoryCode: [\r\n          { required: true, message: \"实验室编码不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentUpdateLaboratoryCodeRow: null,\r\n      // 导出选择对话框\r\n      exportDialogOpen: false,\r\n      exportOptions: [],\r\n      exportLoading: false,\r\n      readonly: true,\r\n      // 项目详情相关\r\n      currentProjectType: null,\r\n      confirmItemCodes: [],\r\n      customerOptions: [],\r\n      itemNames: [],\r\n      // 驳回对话框\r\n      rejectDialogOpen: false,\r\n      rejectLoading: false,\r\n      rejectForm: {\r\n        sampleOrderCode: '',\r\n        rejectReason: ''\r\n      },\r\n      // 逾期操作对话框\r\n      overdueOperationOpen: false,\r\n      overdueOperationLoading: false,\r\n      overdueOperationForm: {\r\n        sampleOrderCode: '',\r\n        expectedSampleTime: '',\r\n        reasonForNoSample: '',\r\n        solution: ''\r\n      },\r\n      currentOverdueRow: null,\r\n      rejectRules: {\r\n        rejectReason: [\r\n          { required: true, message: \"驳回理由不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      currentRejectRow: null\r\n    };\r\n  },\r\n  computed: {\r\n    /** 计算是否可以编辑批次 - 只有状态为'1'(进行中)的打样单才可以编辑 */\r\n    canEditBatch() {\r\n      return this.selectedOrderForBatch && this.selectedOrderForBatch.completionStatus === 1;\r\n    }\r\n  },\r\n  created() {\r\n    // 设置默认日期范围为当天\r\n    const today = new Date();\r\n    const todayStr = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0') + '-' + String(today.getDate()).padStart(2, '0');\r\n    this.dateRange = [todayStr, todayStr];\r\n\r\n    this.getList();\r\n    customerBaseAll().then(res=> this.customerOptions = res)\r\n\r\n    this.getDicts(\"DYD_GCSZT\").then(response => {\r\n        this.statusOptions = response.data;\r\n    });\r\n    this.getDicts(\"CUSTOMER_SERVICE_MODE\").then(response => {\r\n      this.serviceModeOptions = response.data;\r\n    });\r\n    this.getDicts(\"project_nrw_dylb\").then(response => {\r\n      this.dylbOptions = response.data;\r\n    });\r\n    this.loadDashboardStats();\r\n    // 获取研发部部门列表\r\n    getResearchDepartments().then(response => {\r\n      this.researchDeptDatas = this.handleTree(response.data, \"deptId\");\r\n    });\r\n    // 获取未分配打样单\r\n    this.handleUnassignedOrders();\r\n  },\r\n  methods: {\r\n    /** 计算产品/项目等级 */\r\n    getProjectLevel(row) {\r\n      const customerLevel = row.customerLevel || '';\r\n      const projectLevel = row.projectLevel || '';\r\n\r\n      // 如果 projectLevel 是空字符串或 \"/\" 就不相加\r\n      if (projectLevel === '' || projectLevel === '/') {\r\n        return customerLevel;\r\n      }\r\n\r\n      return customerLevel + projectLevel;\r\n    },\r\n    /** 查询工程师打样单关联列表 */\r\n    getList() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      this.loading = true;\r\n      params.associationStatus = 1\r\n      listEngineerSampleOrder(params).then(response => {\r\n        this.engineerSampleOrderList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        userId: null,\r\n        sampleOrderCode: null,\r\n        serviceMode: null,\r\n        difficultyLevelId: null,\r\n        completionStatus: null,\r\n        scheduledDate: null,\r\n        actualStartTime: null,\r\n        actualFinishTime: null,\r\n        actualManHours: null,\r\n        estimatedManHours: null,\r\n        standardManHours: null,\r\n        qualityEvaluation: null,\r\n        isLocked: 0,\r\n        startDate: null,\r\n        endDate: null,\r\n        checkType: null,\r\n        sampleOrderRemark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.unassignedQueryParams.pageNum = 1;\r\n      this.getList();\r\n      this.loadDashboardStats();\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.dateRange = [] // 重置日期范围\r\n      this.scheduledDateRange = []\r\n      this.startDateRange = []\r\n      this.actualStartTimeRange = []\r\n      this.actualFinishTimeRange = []\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.selectedRows = selection\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加工程师打样单关联\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids\r\n      getEngineerSampleOrder(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改工程师打样单关联\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateEngineerSampleOrder(this.form).then(response => {\r\n              this.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n              this.loadDashboardStats();\r\n            });\r\n          } else {\r\n            addEngineerSampleOrder(this.form).then(response => {\r\n              this.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n              this.loadDashboardStats();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$confirm('是否确认删除工程师打样单关联编号为\"' + row.sampleOrderCode + '\"的数据项？').then(function () {\r\n        return delEngineerSampleOrder(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.msgSuccess(\"删除成功\");\r\n        this.loadDashboardStats();\r\n      }).catch(() => { });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 重置导出选项并显示选择对话框\r\n      this.exportOptions = [];\r\n      this.exportDialogOpen = true;\r\n    },\r\n    /** 确认导出操作 */\r\n    confirmExport() {\r\n      if (this.exportOptions.length === 0) {\r\n        this.$message.warning('请至少选择一种导出类型');\r\n        return;\r\n      }\r\n      this.exportLoading = true;\r\n      this.executeExports(0);\r\n    },\r\n\r\n    /** 串行执行导出操作 */\r\n    executeExports(index) {\r\n      if (index >= this.exportOptions.length) {\r\n        // 所有导出完成\r\n        this.exportLoading = false;\r\n        this.exportDialogOpen = false;\r\n        this.$message.success(`导出完成，共导出${this.exportOptions.length}个文件`);\r\n        return;\r\n      }\r\n\r\n      const option = this.exportOptions[index];\r\n      const associationStatus = option === 'assigned' ? 1 : 0;\r\n      const typeName = option === 'assigned' ? '已分配' : '未分配';\r\n\r\n      this.doExport(associationStatus, option).then(() => {\r\n        this.$message.success(`${typeName}打样单导出成功`);\r\n        // 继续导出下一个\r\n        this.executeExports(index + 1);\r\n      }).catch((error) => {\r\n        this.exportLoading = false;\r\n        this.$message.error(`${typeName}打样单导出失败: ${error.message || error}`);\r\n      });\r\n    },\r\n    /** 执行导出操作 */\r\n    doExport(associationStatus, exportType) {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      params.associationStatus = associationStatus;\r\n      params.exportType = exportType;\r\n      return exportEngineerSampleOrder(params).then(response => {\r\n        this.download(response.msg);\r\n      });\r\n    },\r\n    /** 执行导出打样单（已分配/未分配） */\r\n    doExportSampleOrder() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      this.$confirm('是否确认导出所有（已分配/未分配）打样单关联数据？', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportEngineerSampleOrder(params);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n        this.$message.success(\"导出成功\");\r\n      }).catch(() => {\r\n        this.exportLoading = false;\r\n      });\r\n    },\r\n    /** 点击\"开始\"按钮操作 */\r\n    handleStart(row) {\r\n      this.$confirm('确定要将打样单：' + row.sampleOrderCode + '标记为进行中吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        updateSampleOrderStatus({\r\n          id: row.id,\r\n          status: '1'\r\n        }).then(response => {\r\n          this.msgSuccess('打样单已设置为已开始');\r\n          this.getList();\r\n          this.loadDashboardStats();\r\n        });\r\n      });\r\n    },\r\n    /** 下载打样单操作 */\r\n    handleDownloadSampleOrder(row) {\r\n      this.$confirm('是否确认导出打样单?', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportNrwItem({itemId: row.itemId,projectId: row.projectId})\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n      }).catch(() => {});\r\n    },\r\n    /** 批量导出打样单任务操作 */\r\n    handleBatchExportNrw() {\r\n      if (this.selectedRows.length === 0) {\r\n        this.$modal.msgError(\"请选择要导出的打样单任务\");\r\n        return;\r\n      }\r\n\r\n      // 构造批量导出数据，传递itemId和projectId\r\n      const exportData = this.selectedRows.map(row => ({\r\n        itemId: row.itemId,\r\n        projectId: row.projectId\r\n      }));\r\n\r\n      this.$confirm('是否确认导出所选打样单任务的内容物数据？', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.exportLoading = true;\r\n        return exportMultipleNrw(exportData);\r\n      }).then(response => {\r\n        this.download(response.msg);\r\n        this.exportLoading = false;\r\n        this.$message.success(\"导出成功\");\r\n      }).catch(() => {\r\n        this.exportLoading = false;\r\n      });\r\n    },\r\n    /** 点击\"完成\"按钮操作 */\r\n    handleFinish(row) {\r\n      this.currentFinishRow = row;\r\n      this.finishTaskForm.laboratoryCode = '';\r\n      this.finishTaskForm.remark = '';\r\n      this.finishTaskOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.finishTaskForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认完成任务 */\r\n    confirmFinishTask() {\r\n      this.$refs.finishTaskForm.validate(valid => {\r\n        if (valid) {\r\n          this.finishTaskLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentFinishRow.id,\r\n            status: '2',\r\n            laboratoryCode: this.finishTaskForm.laboratoryCode,\r\n            remark: this.finishTaskForm.remark\r\n          }).then(response => {\r\n            this.finishTaskLoading = false;\r\n            this.finishTaskOpen = false;\r\n            this.msgSuccess('打样单已设置为已完成');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.finishTaskLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 从批次管理对话框完成任务 */\r\n    handleFinishFromBatch() {\r\n      this.currentFinishRow = this.currentBatchRow;\r\n      this.finishTaskForm.laboratoryCode = '';\r\n      this.finishTaskForm.remark = '';\r\n      this.finishTaskOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.finishTaskForm.clearValidate();\r\n      });\r\n    },\r\n    /** 点击\"更新实验室编码\"按钮操作 */\r\n    handleUpdateLaboratoryCode(row) {\r\n      this.currentUpdateLaboratoryCodeRow = row;\r\n      // 回显当前的实验室编码和备注\r\n      this.updateLaboratoryCodeForm.laboratoryCode = row.laboratoryCode || '';\r\n      this.updateLaboratoryCodeForm.remark = row.remark || '';\r\n      this.updateLaboratoryCodeOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.updateLaboratoryCodeForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认更新实验室编码 */\r\n    confirmUpdateLaboratoryCode() {\r\n      this.$refs.updateLaboratoryCodeForm.validate(valid => {\r\n        if (valid) {\r\n          this.updateLaboratoryCodeLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentUpdateLaboratoryCodeRow.id,\r\n            status: this.currentUpdateLaboratoryCodeRow.completionStatus, // 保持当前状态\r\n            laboratoryCode: this.updateLaboratoryCodeForm.laboratoryCode,\r\n            remark: this.updateLaboratoryCodeForm.remark\r\n          }).then(response => {\r\n            this.updateLaboratoryCodeLoading = false;\r\n            this.updateLaboratoryCodeOpen = false;\r\n            this.msgSuccess('实验室编码更新成功');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.updateLaboratoryCodeLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 点击\"驳回\"按钮操作 */\r\n    handleReject(row) {\r\n      this.currentRejectRow = row;\r\n      this.rejectForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.rejectForm.rejectReason = '';\r\n      this.rejectDialogOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.rejectForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认驳回 */\r\n    confirmReject() {\r\n      this.$refs.rejectForm.validate(valid => {\r\n        if (valid) {\r\n          this.rejectLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentRejectRow.id,\r\n            status: '3',\r\n            rejectReason: this.rejectForm.rejectReason\r\n          }).then(response => {\r\n            this.rejectLoading = false;\r\n            this.rejectDialogOpen = false;\r\n            this.msgSuccess('打样单已驳回');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n            // 如果是未分配打样单的驳回，也需要刷新未分配列表\r\n            this.handleUnassignedOrders();\r\n          }).catch(() => {\r\n            this.rejectLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 点击\"逾期操作\"按钮操作 */\r\n    handleOverdueOperation(row) {\r\n      this.currentOverdueRow = row;\r\n      this.overdueOperationForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.overdueOperationForm.expectedSampleTime = row.expectedSampleTime;\r\n      this.overdueOperationForm.reasonForNoSample = row.reasonForNoSample;\r\n      this.overdueOperationForm.solution = row.solution;\r\n      this.overdueOperationOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.overdueOperationForm.clearValidate();\r\n      });\r\n    },\r\n    /** 确认逾期操作 */\r\n    confirmOverdueOperation() {\r\n      this.$refs.overdueOperationForm.validate(valid => {\r\n        if (valid) {\r\n          this.overdueOperationLoading = true;\r\n          updateSampleOrderStatus({\r\n            id: this.currentOverdueRow.id,\r\n            status: \"11\", // 特殊处理，只更新“逾期情况”填写的字段\r\n            expectedSampleTime: this.overdueOperationForm.expectedSampleTime,\r\n            reasonForNoSample: this.overdueOperationForm.reasonForNoSample,\r\n            solution: this.overdueOperationForm.solution\r\n          }).then(response => {\r\n            this.overdueOperationLoading = false;\r\n            this.overdueOperationOpen = false;\r\n            this.msgSuccess('逾期操作已完成');\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.overdueOperationLoading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 查看打样单详情 */\r\n    async orderDetail(row) {\r\n      try {\r\n        //获取执行ID\r\n        const orderId = row.projectOrderId;\r\n        let data = await getExecutionByOrderInfo(orderId);\r\n        if(data!=null && !isNull(data.id)){\r\n            let id = data.id;\r\n            this.$refs.executionAddOrEdit.open = true;\r\n            await this.$nextTick()\r\n            this.$refs.executionAddOrEdit.reset()\r\n            this.$refs.executionAddOrEdit.show(id, 1)\r\n        }else{\r\n          this.$message.error('获取数据失败');\r\n        }\r\n      } catch (error) {\r\n        console.error('查看项目详情失败:', error);\r\n        this.$message.error('查看项目详情失败: ' + (error.message || '未知错误'));\r\n      }\r\n    },\r\n\r\n    /** 锁定状态改变处理 */\r\n    handleLockChange(value) {\r\n      if (value === 1) {\r\n        this.$confirm('锁定后将无法自动调整此单的排单日期，是否确认锁定？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.form.isLocked = 1;\r\n        }).catch(() => {\r\n          this.form.isLocked = 0;\r\n        });\r\n      }\r\n    },\r\n    /** 表格中锁定状态改变处理 */\r\n    handleTableLockChange(value, row) {\r\n      if (value === 1) {\r\n        this.$confirm('锁定后将无法自动调整此单的排单日期，是否确认锁定？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          // 调用更新接口\r\n          updateEngineerSampleOrder({\r\n            id: row.id,\r\n            isLocked: 1\r\n          }).then(response => {\r\n            this.msgSuccess(\"锁定成功\");\r\n            this.getList();\r\n            this.loadDashboardStats();\r\n          });\r\n        }).catch(() => {\r\n          // 取消操作，恢复原值\r\n          row.isLocked = 0;\r\n        });\r\n      } else {\r\n        // 解锁操作\r\n        updateEngineerSampleOrder({\r\n          id: row.id,\r\n          isLocked: 0\r\n        }).then(response => {\r\n          this.msgSuccess(\"解锁成功\");\r\n          this.getList();\r\n          this.loadDashboardStats();\r\n        });\r\n      }\r\n    },\r\n    /** 加载统计概览数据 */\r\n    loadDashboardStats() {\r\n      let params = { ...this.queryParams };\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      getDashboardStats(params).then(response => {\r\n        this.dashboardStats = response.data || {};\r\n      });\r\n    },\r\n    /** 更改工程师按钮操作 */\r\n    handleChangeEngineer(row) {\r\n      this.changeEngineerForm = {\r\n        id: row.id,\r\n        sampleOrderCode: row.sampleOrderCode,\r\n        currentEngineerName: row.nickName,\r\n        oldEngineerId: row.userId,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      };\r\n\r\n      // 获取可用工程师列表\r\n      getEngineersByDifficultyLevel({\r\n        difficultyLevelId: row.difficultyLevelId,\r\n        categoryId: row.categoryId\r\n      }).then(response => {\r\n        this.engineerOptions = response.data || [];\r\n      });\r\n      // 获取研发所有工程师列表\r\n      getResearchDepartmentsUser().then(response => {\r\n        this.searchedEngineers = response.data || [];\r\n      });\r\n      this.changeEngineerOpen = true;\r\n    },\r\n    /** 提交更改工程师 */\r\n    submitChangeEngineer() {\r\n      this.$refs[\"changeEngineerForm\"].validate(valid => {\r\n        if (valid) {\r\n          const data = {\r\n            sampleOrderId: this.changeEngineerForm.id,\r\n            oldEngineerId: this.changeEngineerForm.oldEngineerId,\r\n            newEngineerId: this.changeEngineerForm.newEngineerId,\r\n            scheduledDate: this.changeEngineerForm.scheduledDate,\r\n            adjustWorkSchedule: this.changeEngineerForm.adjustWorkSchedule\r\n          };\r\n\r\n          // 调用更改工程师的API接口\r\n          changeEngineer(data).then(response => {\r\n            this.msgSuccess(\"更改工程师成功\");\r\n            this.changeEngineerOpen = false;\r\n            this.getList();\r\n            // 获取未分配打样单\r\n            this.handleUnassignedOrders();\r\n            this.loadDashboardStats();\r\n          }).catch(() => {\r\n            this.msgError(\"更改工程师失败\");\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 获取未分配打样单列表 */\r\n    handleUnassignedOrders() {\r\n      let params = { ...this.queryParams };\r\n      params = this.addDateRange(params, this.scheduledDateRange,'ScheduledDate')\r\n      params = this.addDateRange(params, this.startDateRange,'StartDate')\r\n      params = this.addDateRange(params, this.actualStartTimeRange,'ActualStartTime')\r\n      params = this.addDateRange(params, this.actualFinishTimeRange,'ActualFinishTime')\r\n      // 清空之前的日期范围参数，根据当前状态重新设置\r\n      delete params.beginDateRange;\r\n      delete params.endDateRange;\r\n      // 添加日期范围筛选参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.beginDateRange = this.dateRange[0];\r\n        params.endDateRange = this.dateRange[1];\r\n      }\r\n      params.associationStatus = 0\r\n      params.pageNum = this.unassignedQueryParams.pageNum;\r\n      params.pageSize = this.unassignedQueryParams.pageSize;\r\n      listEngineerSampleOrder(params).then(response => {\r\n        this.unassignedOrders = response.rows;\r\n        this.unassignedTotal = response.total;\r\n      });\r\n    },\r\n    /** 分配工程师操作 */\r\n    handleAssignEngineer(row) {\r\n      this.changeEngineerForm = {\r\n        id: row.id,\r\n        sampleOrderCode: row.sampleOrderCode,\r\n        currentEngineerName: '',\r\n        oldEngineerId: null,\r\n        newEngineerId: null,\r\n        scheduledDate: null,\r\n        adjustWorkSchedule: 0\r\n      };\r\n\r\n      // 获取可用工程师列表\r\n      getEngineersByDifficultyLevel({\r\n        difficultyLevelId: row.difficultyLevelId,\r\n        categoryId: row.categoryId\r\n      }).then(response => {\r\n        this.engineerOptions = response.data || [];\r\n      });\r\n      // 获取研发所有工程师列表\r\n      getResearchDepartmentsUser().then(response => {\r\n        this.searchedEngineers = response.data || [];\r\n      });\r\n      this.changeEngineerOpen = true;\r\n    },\r\n    /** 删除未分配打样单 */\r\n    handleDeleteUnassigned(row) {\r\n      this.$confirm('是否确认删除打样单编号为\"' + row.sampleOrderCode + '\"的数据项？').then(function() {\r\n        return delEngineerSampleOrder(row.id);\r\n      }).then(() => {\r\n        this.handleUnassignedOrders();\r\n        this.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 驳回未分配打样单 */\r\n    handleRejectUnassigned(row) {\r\n      this.currentRejectRow = row;\r\n      this.rejectForm.sampleOrderCode = row.sampleOrderCode;\r\n      this.rejectForm.rejectReason = '';\r\n      this.rejectDialogOpen = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.rejectForm.clearValidate();\r\n      });\r\n    },\r\n    /** 未分配打样单分页大小改变 */\r\n    handleUnassignedSizeChange(newSize) {\r\n      this.unassignedQueryParams.pageSize = newSize;\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 未分配打样单页码改变 */\r\n    handleUnassignedCurrentChange(newPage) {\r\n      this.unassignedQueryParams.pageNum = newPage;\r\n      this.handleUnassignedOrders();\r\n    },\r\n    /** 切换未分配面板显示状态 */\r\n    toggleUnassignedPanel() {\r\n      this.isUnassignedPanelCollapsed = !this.isUnassignedPanelCollapsed;\r\n    },\r\n    /** 处理状态过滤 */\r\n    handleStatusFilter(status) {\r\n      // 清除逾期过滤\r\n      this.queryParams.isOverdue = null;\r\n      this.queryParams.completionStatus = status;\r\n      this.handleQuery();\r\n    },\r\n\r\n    /** 处理逾期任务过滤 */\r\n    handleOverdueFilter() {\r\n      // 清除完成状态过滤\r\n      this.queryParams.completionStatus = null;\r\n      // 设置逾期过滤\r\n      this.queryParams.isOverdue = this.queryParams.isOverdue === 1 ? null : 1;\r\n      this.handleQuery();\r\n\r\n      if (this.queryParams.isOverdue === 1) {\r\n        this.$message.info('已筛选显示逾期任务');\r\n      } else {\r\n        this.$message.info('已清除逾期任务筛选');\r\n      }\r\n    },\r\n    /** 获取紧急程度类型 */\r\n    getUrgencyType(endDate, latestStartTime) {\r\n      const now = new Date();\r\n      const end = new Date(endDate);\r\n      const latest = latestStartTime ? new Date(latestStartTime) : null;\r\n\r\n      // 计算距离截止日期的天数\r\n      const daysToEnd = Math.ceil((end - now) / (1000 * 60 * 60 * 24));\r\n\r\n      // 如果有最晚开始时间，检查是否已经超过\r\n      if (latest && now > latest) {\r\n        return 'danger'; // 已超过最晚开始时间\r\n      }\r\n\r\n      if (daysToEnd <= 1) {\r\n        return 'danger'; // 紧急：1天内截止\r\n      } else if (daysToEnd <= 3) {\r\n        return 'warning'; // 警告：3天内截止\r\n      } else if (daysToEnd <= 7) {\r\n        return 'primary'; // 一般：7天内截止\r\n      } else {\r\n        return 'success'; // 充足时间\r\n      }\r\n    },\r\n    /** 获取紧急程度文本 */\r\n    getUrgencyText(endDate, latestStartTime) {\r\n      const now = new Date();\r\n      const end = new Date(endDate);\r\n      const latest = latestStartTime ? new Date(latestStartTime) : null;\r\n      // 计算距离截止日期的天数\r\n      const daysToEnd = Math.ceil((end - now) / (1000 * 60 * 60 * 24));\r\n      // 如果有最晚开始时间，检查是否已经超过\r\n      if (latest && now > latest) {\r\n        return '超期未开始';\r\n      }\r\n\r\n      if (daysToEnd <= 0) {\r\n        return '已逾期';\r\n      } else if (daysToEnd <= 1) {\r\n        return '紧急';\r\n      } else if (daysToEnd <= 3) {\r\n        return '较急';\r\n      } else if (daysToEnd <= 7) {\r\n        return '一般';\r\n      } else {\r\n        return '充足';\r\n      }\r\n    },\r\n    /** 刷新未分配打样单列表 */\r\n    handleRefreshUnassigned() {\r\n      this.handleUnassignedOrders();\r\n      this.$message.success('刷新成功');\r\n    },\r\n    /** 获取逾期信息 */\r\n    getOverdueInfo(row) {\r\n      const now = new Date();\r\n      const endDate = new Date(row.endDate);\r\n      let isOverdue = false;\r\n      let overdueDays = 0;\r\n      // 根据后台SQL逻辑判断逾期\r\n      if (row.completionStatus === 2) {\r\n        // 已完成且逾期：实际完成时间 > 截止日期\r\n        if (row.actualFinishTime) {\r\n          const actualFinishTime = new Date(row.actualFinishTime);\r\n          if (actualFinishTime > endDate) {\r\n            isOverdue = true;\r\n            overdueDays = Math.ceil((actualFinishTime - endDate) / (1000 * 60 * 60 * 24));\r\n          }\r\n        }\r\n      } else if (row.completionStatus === 1) {\r\n        // 进行中且逾期：当前时间 > 截止日期\r\n        if (now > endDate) {\r\n          isOverdue = true;\r\n          overdueDays = Math.ceil((now - endDate) / (1000 * 60 * 60 * 24));\r\n        }\r\n      } else if (row.completionStatus === 0) {\r\n        // 未开始且逾期：未开始但当前时间 > 开始日期\r\n        if (now > endDate) {\r\n          isOverdue = true;\r\n          overdueDays = Math.ceil((now - endDate) / (1000 * 60 * 60 * 24));\r\n        }\r\n      }\r\n\r\n      return {\r\n        isOverdue,\r\n        overdueDays\r\n      };\r\n    },\r\n\r\n    // ==================== 批次管理相关方法 ====================\r\n\r\n    /** 打开批次管理对话框 */\r\n    handleBatchManagement(row) {\r\n      this.selectedOrderForBatch = row;\r\n      this.currentBatchRow = row; // 保存当前行数据\r\n      this.batchManagementOpen = true;\r\n      this.loadBatchData(row.id);\r\n    },\r\n\r\n    /** 加载批次数据 */\r\n    async loadBatchData(engineerSampleOrderId) {\r\n      try {\r\n        // 加载当前批次\r\n        const currentBatchResponse = await getCurrentBatch(engineerSampleOrderId);\r\n        this.currentBatch = currentBatchResponse.data;\r\n\r\n        // 加载所有批次\r\n        const batchesResponse = await getBatchesByOrderId(engineerSampleOrderId);\r\n        const allBatches = batchesResponse.data || [];\r\n\r\n        // 分离当前批次和历史批次，并为每个历史批次加载实验数量\r\n        this.historyBatches = allBatches.filter(batch => batch.isCurrentBatch === 0);\r\n\r\n        // // 为每个历史批次加载实验数量\r\n        // for (let batch of this.historyBatches) {\r\n        //   try {\r\n        //     const experimentsResponse = await getExperimentsByBatchId(batch.id);\r\n        //     batch.experimentCount = experimentsResponse.data ? experimentsResponse.data.length : 0;\r\n        //   } catch (error) {\r\n        //     console.warn(`加载批次${batch.id}的实验数量失败:`, error);\r\n        //     batch.experimentCount = 0;\r\n        //   }\r\n        // }\r\n\r\n        // 如果有当前批次，加载实验记录\r\n        if (this.currentBatch) {\r\n          await this.loadCurrentBatchExperiments(this.currentBatch.id);\r\n        }\r\n      } catch (error) {\r\n        console.error('加载批次数据失败:', error);\r\n        this.$message.error('加载批次数据失败');\r\n      }\r\n    },\r\n\r\n    /** 加载当前批次的实验记录 */\r\n    async loadCurrentBatchExperiments(batchId) {\r\n      try {\r\n        const response = await getExperimentsByBatchId(batchId);\r\n        this.currentBatchExperiments = response.data || [];\r\n      } catch (error) {\r\n        console.error('加载实验记录失败:', error);\r\n      }\r\n    },\r\n\r\n    /** 开始新批次 */\r\n    async startNewBatch() {\r\n      // 检查编辑权限\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许开始新批次，只有状态为\"进行中\"的打样单才可以编辑批次信息');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await this.$confirm('确认开始新的打样批次？', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        });\r\n\r\n        const response = await startNewBatch(this.selectedOrderForBatch.id, '');\r\n        this.$message.success('新批次开始成功');\r\n\r\n        // 重新加载批次数据\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n\r\n        // 刷新主列表\r\n        this.getList();\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('开始新批次失败: ' + (error.message || error));\r\n        }\r\n      }\r\n    },\r\n\r\n    /** 结束当前批次 */\r\n    finishCurrentBatch() {\r\n      // 检查编辑权限\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许结束批次，只有状态为\"进行中\"的打样单才可以编辑批次信息');\r\n        return;\r\n      }\r\n\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n      this.finishBatchOpen = true;\r\n    },\r\n\r\n    /** 提交结束批次 */\r\n    async submitFinishBatch() {\r\n      try {\r\n        await finishCurrentBatch(\r\n          this.selectedOrderForBatch.id,\r\n          this.finishBatchForm.qualityEvaluation,\r\n          this.finishBatchForm.remark\r\n        );\r\n\r\n        this.$message.success('批次结束成功');\r\n        this.finishBatchOpen = false;\r\n\r\n        // 重新加载批次数据\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n\r\n        // 刷新主列表\r\n        this.getList();\r\n      } catch (error) {\r\n        console.error('结束批次失败:', error);\r\n        this.$message.error('结束批次失败: ' + (error.message || error));\r\n      }\r\n    },\r\n\r\n    /** 添加实验记录 */\r\n    addExperiment() {\r\n      // 检查编辑权限\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许添加实验记录，只有状态为\"进行中\"的打样单才可以编辑批次信息');\r\n        return;\r\n      }\r\n\r\n      this.experimentForm = {\r\n        experimentCode: '',\r\n        experimentNote: ''\r\n      };\r\n      this.addExperimentOpen = true;\r\n    },\r\n\r\n    /** 提交实验记录 */\r\n    async submitExperiment() {\r\n      // 检查编辑权限\r\n      if (!this.canEditBatch) {\r\n        this.$message.warning('当前打样单状态不允许添加实验记录，只有状态为\"进行中\"的打样单才可以编辑批次信息');\r\n        return;\r\n      }\r\n\r\n      await this.$refs.experimentForm.validate();\r\n\r\n      const experimentRecord = {\r\n        batchId: this.currentBatch.id,\r\n        experimentCode: this.experimentForm.experimentCode,\r\n        experimentNote: this.experimentForm.experimentNote || ''\r\n      };\r\n\r\n      await addExperimentToBatch(experimentRecord);\r\n\r\n      this.$message.success('实验记录添加成功');\r\n      this.addExperimentOpen = false;\r\n\r\n      // 重新加载当前批次的实验记录\r\n      await this.loadCurrentBatchExperiments(this.currentBatch.id);\r\n    },\r\n\r\n    /** 查看批次详情 */\r\n    async viewBatchDetail(batch) {\r\n      try {\r\n        const response = await getExperimentsByBatchId(batch.id);\r\n        const experiments = response.data || [];\r\n\r\n        // 设置批次详情数据\r\n        this.batchDetailData = {\r\n          ...batch,\r\n          experiments: experiments\r\n        };\r\n\r\n        this.batchDetailOpen = true;\r\n      } catch (error) {\r\n        console.error('查看批次详情失败:', error);\r\n        this.$message.error('查看批次详情失败');\r\n      }\r\n    },\r\n\r\n    /** 计算持续时间 */\r\n    calculateDuration(startTime) {\r\n      if (!startTime) return '0小时';\r\n\r\n      const start = new Date(startTime);\r\n      const now = new Date();\r\n      const diffMs = now - start;\r\n      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\r\n      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\r\n\r\n      if (diffHours > 0) {\r\n        return `${diffHours}小时${diffMinutes}分钟`;\r\n      } else {\r\n        return `${diffMinutes}分钟`;\r\n      }\r\n    },\r\n\r\n    /** 获取质量评价类型 */\r\n    getQualityEvaluationType(evaluation) {\r\n      if (!evaluation) return '';\r\n\r\n      const lowerEval = evaluation.toLowerCase();\r\n      if (lowerEval.includes('优秀') || lowerEval.includes('良好') || lowerEval.includes('好')) {\r\n        return 'success';\r\n      } else if (lowerEval.includes('一般') || lowerEval.includes('中等')) {\r\n        return 'warning';\r\n      } else if (lowerEval.includes('差') || lowerEval.includes('不合格') || lowerEval.includes('失败')) {\r\n        return 'danger';\r\n      } else {\r\n        return 'info';\r\n      }\r\n    },\r\n\r\n    /** 关闭批次管理对话框并清空数据 */\r\n    closeBatchManagement() {\r\n      // 清空批次管理相关的数据\r\n      this.currentBatch = null;\r\n      this.historyBatches = [];\r\n      this.currentBatchExperiments = [];\r\n      this.selectedOrderForBatch = null;\r\n      this.currentBatchRow = null; // 清空当前行数据\r\n      this.batchDetailData = null;\r\n\r\n      // 重置表单数据\r\n      this.experimentForm = {\r\n        experimentCode: '',\r\n        experimentNote: ''\r\n      };\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n\r\n      // 关闭所有相关的子对话框\r\n      this.batchDetailOpen = false;\r\n      this.addExperimentOpen = false;\r\n      this.finishBatchOpen = false;\r\n    },\r\n\r\n    /** 关闭批次详情对话框并清空数据 */\r\n    closeBatchDetail() {\r\n      // 清空批次详情数据\r\n      this.batchDetailData = null;\r\n    },\r\n\r\n    /** 关闭添加实验对话框并清空数据 */\r\n    closeAddExperiment() {\r\n      // 清空实验表单数据\r\n      this.experimentForm = {\r\n        experimentCode: '',\r\n        experimentNote: ''\r\n      };\r\n      // 清除表单验证状态\r\n      if (this.$refs.experimentForm) {\r\n        this.$refs.experimentForm.clearValidate();\r\n      }\r\n    },\r\n\r\n    /** 关闭结束批次对话框并清空数据 */\r\n    closeFinishBatch() {\r\n      // 清空结束批次表单数据\r\n      this.finishBatchForm = {\r\n        qualityEvaluation: '',\r\n        remark: ''\r\n      };\r\n      // 清除表单验证状态\r\n      if (this.$refs.finishBatchForm) {\r\n        this.$refs.finishBatchForm.clearValidate();\r\n      }\r\n    },\r\n\r\n    /** 刷新批次数据 */\r\n    async refreshBatchData() {\r\n      if (this.selectedOrderForBatch) {\r\n        await this.loadBatchData(this.selectedOrderForBatch.id);\r\n        this.$message.success('批次数据已刷新');\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.stats-row {\r\n  margin-bottom: 20px;\r\n}\r\n.stats-card {\r\n  border: none;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n/* 批次详情样式 */\r\n.batch-detail-container {\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.batch-info-card {\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 8px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.info-item label {\r\n  font-weight: 600;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n  min-width: 80px;\r\n}\r\n\r\n.info-value {\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.remark-content {\r\n  background-color: #F5F7FA;\r\n  padding: 12px;\r\n  border-radius: 4px;\r\n  border-left: 4px solid #409EFF;\r\n  color: #606266;\r\n  line-height: 1.5;\r\n  margin-top: 8px;\r\n  min-height: 40px;\r\n}\r\n\r\n.experiments-card {\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 8px;\r\n}\r\n\r\n.empty-state, .empty-experiments {\r\n  background-color: #FAFAFA;\r\n  border-radius: 8px;\r\n  margin: 10px 0;\r\n}\r\n\r\n/* 历史批次表格样式优化 */\r\n.history-batches-card .el-table {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-batches-card .el-table th {\r\n  background-color: #F5F7FA;\r\n  color: #606266;\r\n  font-weight: 600;\r\n}\r\n\r\n.history-batches-card .el-table--striped .el-table__body tr.el-table__row--striped td {\r\n  background-color: #FAFAFA;\r\n}\r\n\r\n.stats-card:not(.total) {\r\n  cursor: pointer;\r\n}\r\n\r\n.stats-card:not(.total):hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.stats-card.active {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n  position: relative;\r\n}\r\n\r\n.stats-card.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 3px;\r\n  background: currentColor;\r\n}\r\n\r\n.stats-card.completed.active::after {\r\n  background: #67C23A;\r\n}\r\n\r\n.stats-card.in-progress.active::after {\r\n  background: #409EFF;\r\n}\r\n\r\n.stats-card.overdue.active::after {\r\n  background: #F56C6C;\r\n}\r\n\r\n.stats-card.overdue .stats-icon {\r\n  background: linear-gradient(135deg, #F56C6C, #F78989);\r\n}\r\n\r\n.stats-content {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20px;\r\n}\r\n.stats-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20px;\r\n}\r\n.stats-icon i {\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n.stats-info {\r\n  flex: 1;\r\n}\r\n.stats-title {\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-bottom: 8px;\r\n}\r\n.stats-number {\r\n  font-size: 28px;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n.stats-card.total .stats-icon {\r\n  background: linear-gradient(135deg, #3a7bd5, #3a6073);\r\n}\r\n.stats-card.completed .stats-icon {\r\n  background: linear-gradient(135deg, #E6A23C, #F0C78A);\r\n}\r\n.stats-card.in-progress .stats-icon {\r\n  background: linear-gradient(135deg, #409EFF, #66B1FF);\r\n}\r\n.stats-card.overdue .stats-icon {\r\n  background: linear-gradient(135deg, #F56C6C, #F78989);\r\n}\r\n@media (max-width: 768px) {\r\n  .stats-card .stats-content {\r\n    padding: 15px;\r\n  }\r\n  .stats-icon {\r\n    width: 50px;\r\n    height: 50px;\r\n    margin-right: 15px;\r\n  }\r\n  .stats-icon i {\r\n    font-size: 20px;\r\n  }\r\n  .stats-number {\r\n    font-size: 24px;\r\n  }\r\n}\r\n.other-engineer-select {\r\n  margin-top: 10px;\r\n}\r\n.select-item {\r\n  width: 100%;\r\n}\r\n\r\n/* 未分配打样单告警样式 */\r\n.alert-card {\r\n  margin-bottom: 20px;\r\n}\r\n.alert-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.alert-title {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n}\r\n.alert-title span {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #FF1414;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.alert-actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.alert-cards {\r\n  margin-top: 20px;\r\n}\r\n.alert-item {\r\n  margin-bottom: 20px;\r\n  transition: all 0.3s;\r\n  border-radius: 6px;\r\n  border: 1px solid #e4e7ed;\r\n  height: 225px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: #fff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n.alert-item:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);\r\n  border-color: #409EFF;\r\n}\r\n.alert-item-header {\r\n  margin-bottom: 12px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px solid #f0f2f5;\r\n  background: rgba(64, 158, 255, 0.02);\r\n  margin: -16px -16px 12px -16px;\r\n  padding: 12px 16px;\r\n  border-radius: 6px 6px 0 0;\r\n}\r\n.order-code-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.order-code {\r\n  font-size: 15px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n.urgency-tag {\r\n  margin-left: 8px;\r\n}\r\n.alert-item-content {\r\n  padding: 0;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n  min-height: 0;\r\n}\r\n.info-section {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-start;\r\n}\r\n.info-row {\r\n  margin-bottom: 8px;\r\n}\r\n.info-row-double {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  gap: 12px;\r\n}\r\n.info-row-double .info-item {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n.info-row-double .info-item.date-time-item {\r\n  flex: 1.5;\r\n}\r\n.info-row-double .info-item.standard-item {\r\n  flex: 1;\r\n}\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #606266;\r\n  font-size: 13px;\r\n}\r\n.info-label {\r\n  margin-left: 6px;\r\n  margin-right: 4px;\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n.info-value {\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n.difficulty-text {\r\n  cursor: pointer;\r\n  border-bottom: 1px dashed #ccc;\r\n}\r\n.info-reason {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  color: #f56c6c;\r\n  margin-top: 8px;\r\n  padding: 6px 8px;\r\n  background-color: #fef0f0;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #f56c6c;\r\n  font-size: 12px;\r\n}\r\n.reason-text {\r\n  margin-left: 4px;\r\n  line-height: 1.4;\r\n  word-break: break-word;\r\n}\r\n.text-overflow {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n.info-reason span {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n.info-item i,\r\n.info-date i,\r\n.info-reason i {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n}\r\n.alert-item-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: auto;\r\n  padding: 12px 0 0 0;\r\n  border-top: 1px solid #f0f2f5;\r\n  flex-shrink: 0;\r\n  background: rgba(250, 251, 252, 0.5);\r\n  border-radius: 0 0 6px 6px;\r\n  margin-left: -16px;\r\n  margin-right: -16px;\r\n  padding-left: 16px;\r\n  padding-right: 16px;\r\n}\r\n.alert-item-footer .el-button {\r\n  border-radius: 4px;\r\n  font-weight: 500;\r\n}\r\n.alert-item-footer .el-button--primary {\r\n  background: #409EFF;\r\n  border-color: #409EFF;\r\n  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);\r\n}\r\n.alert-item-footer .el-button--primary:hover {\r\n  background: #66b1ff;\r\n  border-color: #66b1ff;\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.4);\r\n}\r\n.alert-item-footer .el-button--text {\r\n  color: #909399;\r\n  transition: all 0.3s;\r\n}\r\n.alert-item-footer .el-button--text:hover {\r\n  color: #f56c6c;\r\n  background: rgba(245, 108, 108, 0.1);\r\n}\r\n.delete-btn:hover {\r\n  color: #f56c6c !important;\r\n}\r\n\r\n/* 确保卡片内容区域的统一布局 */\r\n.alert-item .el-card__body {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 16px;\r\n  position: relative;\r\n}\r\n\r\n/* 确保内容区域占据剩余空间 */\r\n.alert-item .alert-item-header {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.alert-item .alert-item-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 0;\r\n}\r\n\r\n.alert-item .alert-item-footer {\r\n  flex-shrink: 0;\r\n  margin-top: auto;\r\n}\r\n\r\n.alert-item .info-group {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.alert-item .info-date {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .alert-item {\r\n    height: auto;\r\n    min-height: 200px;\r\n  }\r\n\r\n  .info-row-double {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n\r\n  .alert-item-footer {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .alert-item-footer .el-button {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* 批次管理相关样式 */\r\n.batch-management-container {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.current-batch-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.batch-info-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.batch-info-item label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n}\r\n\r\n.experiment-section {\r\n  border-top: 1px solid #ebeef5;\r\n  padding-top: 15px;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.section-header span {\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.history-batches-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.new-batch-section {\r\n  padding: 40px 0;\r\n  text-align: center;\r\n  background-color: #fafafa;\r\n  border: 2px dashed #dcdfe6;\r\n  border-radius: 6px;\r\n}\r\n\r\n.new-batch-section .el-button {\r\n  padding: 12px 30px;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .batch-management-container {\r\n    max-height: 500px;\r\n  }\r\n\r\n  .batch-info-item {\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .section-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .section-header .el-button {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n\r\n/* 备注文本省略样式 */\r\n.remark-text-ellipsis {\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 2;\r\n  line-clamp: 2;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  line-height: 1.4;\r\n  max-height: 2.8em; /* 2行的高度，基于line-height */\r\n  word-break: break-word;\r\n  white-space: normal;\r\n  cursor: pointer;\r\n}\r\n\r\n.remark-text-ellipsis:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 导出选择对话框样式 */\r\n.export-options {\r\n  padding: 10px 0;\r\n}\r\n\r\n.export-options .el-checkbox {\r\n  width: 100%;\r\n  margin-right: 0;\r\n  padding: 15px;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.export-options .el-checkbox:hover {\r\n  border-color: #409eff;\r\n  background-color: #f0f9ff;\r\n}\r\n\r\n.export-options .el-checkbox.is-checked {\r\n  border-color: #409eff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.export-options .el-checkbox__label {\r\n  width: 100%;\r\n  padding-left: 10px;\r\n}\r\n</style>\r\n"]}]}