<template>
  <div v-loading="loading" >
    <el-row :gutter="20">
      <el-col :span="6" >
        <el-statistic
          group-separator=","
          :precision="2"
          :value="form.sumNums"
          title="今日上工总人数"
        />
      </el-col>
      <el-col :span="6" >
        <el-statistic
          group-separator=","
          :precision="2"
          :value="minutesToHours(form.sumMinutes)"
          title="今日上工总工时"
        />
      </el-col>
      <el-col :span="6" >
        <el-statistic
          group-separator=","
          :precision="2"
          :value="minutesToHours(form.wagesMinutes)"
          title="今日工资总工时"
        >
          <template #suffix >
            <el-tooltip content="质检除外" >
              <i class="el-icon-question" />
            </el-tooltip>
          </template>
        </el-statistic>
      </el-col>
      <el-col :span="6" >
        <el-statistic
          group-separator=","
          :precision="2"
          :value="minutesToHours(form.mesMinutes)"
          title="mes上工总工时"
        >
          <template #suffix >
            <el-tooltip content="质检除外" >
              <i class="el-icon-question" />
            </el-tooltip>
          </template>
        </el-statistic>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px" >
      <el-col :span="6" >
        <div style="height: 420px;width: 420px;">
          <BaseChart ref="userNumsChart" :styleObj="{height: '400px',width: '400px'}" />
        </div>
      </el-col>
      <el-col :span="6" >
        <div style="height: 420px;width: 420px;">
          <BaseChart ref="userMinutesChart" :styleObj="{height: '400px',width: '400px'}" />
        </div>
      </el-col>
      <el-col :span="6" >
        <div style="height: 420px;width: 420px;">
          <BaseChart ref="hoursTypeCharts" :styleObj="{height: '400px',width: '400px'}" />
        </div>
      </el-col>
      <el-col :span="6" >
        <div style="height: 420px;width: 420px;">
          <BaseChart ref="hoursComposeCharts" :styleObj="{height: '400px',width: '400px'}" />
        </div>
      </el-col>
    </el-row>

    <el-tooltip content="刷新产线工时" >
      <el-button :loading="btnLoading" icon="el-icon-refresh" size="mini" type="text" @click="refreshAreaHours(form.factory,form.workDate)" />
    </el-tooltip>

    <el-tabs v-model="currentTab" >
      <el-tab-pane v-for="tab in tabOptions" :key="tab.value" :label="tab.label" :name="tab.value" >
        <div class="table-wrapper">
          <table class="base-table small-table" >
            <tr >
              <th style="width: 120px" >维度</th>
              <th style="width: 120px" >总数</th>
              <th style="width: 120px" >正式工</th>
              <th style="width: 120px" >称量</th>
              <th style="width: 120px" >间接</th>
              <th style="width: 120px" >管理</th>
              <th style="width: 120px" >质检</th>
              <th style="width: 120px" >劳务</th>
              <th style="width: 120px" >包干</th>
            </tr>
            <tr>
              <th>
                工资工时
                <el-tooltip content="有修正工时时,以修正工时为准(不包含质检)" >
                  <i class="el-icon-question" />
                </el-tooltip>
              </th>
              <td>{{minutesToHours(tab.wagesMinutes).toFixed(2)}}</td>
              <td>{{minutesToHours(tab.userWagesMinutes).toFixed(2)}}</td>
              <td>{{minutesToHours(tab.weightMinutes).toFixed(2)}}</td>
              <td>{{minutesToHours(tab.otherMinutes).toFixed(2)}}</td>
              <td>{{minutesToHours(tab.manageMinutes).toFixed(2)}}</td>
              <td>{{minutesToHours(tab.qcMinutes).toFixed(2)}}</td>
              <td>{{minutesToHours(tab.laborMinutes).toFixed(2)}}</td>
              <td>{{minutesToHours(tab.outerMinutes).toFixed(2)}}</td>
            </tr>
            <tr>
              <th>人数</th>
              <td>{{tab.sumNums}}</td>
              <td>{{tab.userNums}}</td>
              <td>{{tab.weightNums}}</td>
              <td>{{tab.otherNums}}</td>
              <td>{{tab.manageNums}}</td>
              <td>{{tab.qcNums}}</td>
              <td>{{tab.laborNums}}</td>
              <td>{{tab.outerNums}}</td>
            </tr>
          </table>
        </div>
        <DayHoursUserTabs
          :day-hours="form"
          :attendance-log-list="attendanceLogList"
          :mes-hours-list="mesHoursList"
          :day-user-list="dayUserList.filter(i=>i.sailings === tab.value)"
          :weight-minutes-list="weightMinutesList"
          :other-minutes-list="otherMinutesList"
          :manage-minutes-list="manageMinutesList"
          :qc-minutes-list="qcMinutesList"
          :user-list="userList"
          :rest-list="restList"
          :sailings="tab.value"
          @computeItemData="computeItemData"
          @sailingsChange="sailingsChange"
        />
      </el-tab-pane>
    </el-tabs>

    <el-form ref="form" :model="form" :rules="rules" size="mini" style="margin-top: 20px" label-width="80px">
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" type="textarea"/>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer" v-has-permi="['mes:production:hours:push']" >
      <el-button type="primary" @click="submitForm" size="mini" :loading="btnLoading">确 定</el-button>
      <el-button type="primary" @click="pushHrDate" size="mini" :loading="btnLoading">推送人事(班别)</el-button>
      <el-button @click="cancel" size="mini">取 消</el-button>
    </div>
  </div>
</template>
<script >
import {getDayHours, updateDayHours} from "@/api/production/dayHours";
import DayHoursUserTabs from "@/views/production/dayHours/userTabs.vue";
import {allPlanAreaUserHours, getDayAreaHours} from "@/api/production/planAreaHours";
import BaseChart from "../../../../baseCharts.vue";
import {userAll} from "@/api/system/user";
import {allAttendanceRestTime} from "@/api/hr/attendanceRestTime";
import {getOtherDayMinutesByParams, } from "@/api/production/otherMinutes";
import {allMesHours} from "@/api/production/mesHours";
import {allAttendanceLog} from "@/api/hr/attendanceLog";
import {allProductionGroup} from "@/api/production/productionGroup";
import {
  calculateIntersectionMinutes,
  findClosestTimeString,
  findIntersection,
  roundDownToHalfHour
} from "@/utils/production/time";
import form from "@/views/gx/form/index.vue";
import {pushHrUserAnDate} from "@/api/hr/attendance";

export default {
  name: 'dayHoursSave',
  components: {BaseChart, DayHoursUserTabs},
  props: {
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      btnLoading: false,
      form: {},
      rules: {},
      tabOptions: [
        {
          label: '白班',
          value: '0',
          sumMinutes: 0,
          userMinutes: 0,
          laborMinutes: 0,
          outerMinutes: 0,
          weightMinutes: 0,
          otherMinutes: 0,
          manageMinutes: 0,
          qcMinutes: 0,
          sumNums: 0,
          userNums: 0,
          laborNums: 0,
          outerNums: 0,
          weightNums: 0,
          otherNums: 0,
          manageNums: 0,
          qcNums: 0,
        },
        {
          label: '晚班',
          value: '1',
          sumMinutes: 0,
          userMinutes: 0,
          laborMinutes: 0,
          outerMinutes: 0,
          weightMinutes: 0,
          otherMinutes: 0,
          manageMinutes: 0,
          qcMinutes: 0,
          sumNums: 0,
          userNums: 0,
          laborNums: 0,
          outerNums: 0,
          weightNums: 0,
          otherNums: 0,
          manageNums: 0,
          qcNums: 0,
        },
      ],
      currentTab: '0',
      userNumsOptions: {},
      userMinutesOptions: {},
      hoursTypeOptions: {},
      hoursComposeOptions: {},
      dayUserList: [],
      attendanceLogList: [],
      userList: [],
      restList: [],
      weightMinutesList: [],
      otherMinutesList: [],
      manageMinutesList: [],
      qcMinutesList: [],
      mesHoursList: [],
      exceptionOptions: [
        {label: '上工考勤异常',value: 1},
        {label: '下工考勤异常',value: 2},
        {label: 'mes上工异常',value: 3},
        {label: 'mes下工异常',value: 4},
        {label: '转场异常',value: 5},
        {label: '有效工时异常',value: 6},
        {label: 'sap上工异常',value: 7},
        {label: 'sap下工异常',value: 8},
      ],
    }
  },
  async created() {
    this.userList = await userAll()
  },
  methods: {
    async sailingsChange(userCode){//mes班别变更,刷新mes工时
      await this.refreshAreaHours(this.form.factory,this.form.workDate)
    },
    async buildCharts() {
      const form = this.form

      this.userNumsOptions = this.buildOptions("上工人员分布",[
        {name: '正式工',value: form.userNums},
        {name: '称量',value: form.weightNums},
        {name: '间接',value: form.otherNums},
        {name: '管理',value: form.manageNums},
        {name: '质检',value: form.qcNums},
        {name: '劳务工',value: form.laborNums},
        {name: '外包工',value: form.outerNums},
      ])
      this.userMinutesOptions = this.buildOptions("产线上工工时分布",[
        {name: '正式工',value: this.minutesToHours(form.userMinutes).toFixed(2) },
        {name: '劳务工',value: this.minutesToHours(form.laborMinutes).toFixed(2) },
        {name: '外包工',value: this.minutesToHours(form.outerMinutes).toFixed(2) },
      ])
      this.hoursTypeOptions = this.buildOptions("产线工时性质分布",[
        {name: '有效',value: this.minutesToHours(form.effectiveMinutes).toFixed(2)},
        {name: '无效',value: this.minutesToHours(form.invalidMinutes).toFixed(2)},
        {name: '休息',value: this.minutesToHours(form.restMinutes).toFixed(2)},
      ])
      this.hoursComposeOptions = this.buildOptions("工时组成分布",[
        {name: '产线',value: this.minutesToHours(form.sumMinutes).toFixed(2)},
        {name: '称量',value: this.minutesToHours(form.weightMinutes).toFixed(2)},
        {name: '间接',value: this.minutesToHours(form.otherMinutes).toFixed(2)},
        {name: '管理',value: this.minutesToHours(form.manageMinutes).toFixed(2)},
        // {name: '质检',value: this.minutesToHours(form.qcMinutes).toFixed(2)},
      ])

      await this.$nextTick()
      const userNumsChart = this.$refs.userNumsChart
      if(userNumsChart && this.userNumsOptions) {
        await userNumsChart.init(this.userNumsOptions)
      }

      const userMinutesChart = this.$refs.userMinutesChart
      if(userMinutesChart && this.userMinutesOptions) {
        await userMinutesChart.init(this.userMinutesOptions)
      }

      const hoursTypeCharts = this.$refs.hoursTypeCharts
      if(hoursTypeCharts && this.hoursTypeOptions) {
        await hoursTypeCharts.init(this.hoursTypeOptions)
      }

      const hoursComposeCharts = this.$refs.hoursComposeCharts
      if(hoursComposeCharts && this.hoursComposeOptions) {
        await hoursComposeCharts.init(this.hoursComposeOptions)
      }
    },
    async refreshAreaHours(factory,workDate) {
      const oldArray = this.dayUserList.map(i=> {//保存原数组的备注
        return {
          userCode: i.userCode,
          sailings: i.sailings,
          finalMinutes: i.finalMinutes,
          remark: i.remark,
        }
      })
      this.btnLoading = true
      const dayUserList = await allPlanAreaUserHours({factory,workDate,})
      for (const item of dayUserList) {
        const attendanceArr = this.attendanceLogList.filter(i=> i.userId === item.userId).sort((a,b)=> a.userCheckTime - b.userCheckTime)
        const timesArray = attendanceArr.map(i=> this.moment(i.userCheckTime).format('YYYY-MM-DD HH:mm:ss'))
        const upTime = findClosestTimeString(timesArray,item.mesMinTime)
        const downTime = findClosestTimeString(timesArray,item.mesMaxTime)
        const startTimeArray = [
          item.sapMinTime,
          item.mesMinTime,
        ]
        const endTimeArray = [
          item.sapMaxTime,
          item.mesMaxTime,
        ]
        if(upTime && downTime) {
          const attendanceMinutes = this.moment(downTime).diff(upTime,'minutes')

          item.attendanceStartTime = upTime
          item.attendanceEndTime = downTime
          item.attendanceMinutes = attendanceMinutes
          item.attendanceArray = [{startTime: upTime,endTime: downTime}]
          startTimeArray.push(upTime)
          endTimeArray.push(downTime)
        } else {
          item.attendanceArray = []
        }

        const minTime = startTimeArray.reduce((min, current) => {
          return new Date(current) < new Date(min) ? current : min;
        })

        const maxTime = endTimeArray.reduce((max, current) => {
          return new Date(current) > new Date(max) ? current : max;
        })
        item.minTime = minTime
        item.maxTime = maxTime

        let startTime = minTime
        let timeArray = []
        while (startTime <= maxTime) {
          timeArray.push(startTime)
          startTime = this.moment(startTime,'YYYY-MM-DD HH:mm:ss').add(0.25,'hours').format('YYYY-MM-DD HH:mm:ss')
        }
        item.timeArray = timeArray

        item.mesArray = this.mesHoursList.filter(i=>i.userCode === item.userCode).sort((a,b)=> a.startTime - b.startTime)
        // if(item.userCode==='HR23001938') {
        //   console.log(item)
        // }
        for (const o of oldArray) {//匹配原来的备注
          if(o.userCode === item.userCode && o.sailings === item.sailings) {
            item.remark = o.remark
            item.finalMinutes = o.finalMinutes
          }
        }
      }
      //异常提醒
      for (const u of dayUserList) {
        // if(u.userCode === 'HR18000095') {
        //   console.log(u.attendanceEndTime,u.mesMaxTime)
        //   console.log(this.moment(u.attendanceEndTime).diff(u.mesMaxTime,'minutes'))
        // }
        const exceptionArray = []
        if(u.mesMinTime < u.attendanceStartTime) {
          exceptionArray.push(1)//上工考勤异常
        } else if(this.moment(u.mesMinTime).diff(u.attendanceStartTime,'minutes') > 30) {
          exceptionArray.push(3)//上工异常
        }

        if(u.mesMaxTime > u.attendanceEndTime) {
          exceptionArray.push(2)//下工考勤异常
        } else if(this.moment(u.attendanceEndTime).diff(u.mesMaxTime,'minutes') > 15) {
          exceptionArray.push(4)//下工异常
        }
        if(this.moment(u.sapMinTime).diff(u.mesMinTime,'minutes') > 30) {
          exceptionArray.push(7)
        }
        if(this.moment(u.mesMaxTime).diff(u.sapMaxTime,'minutes') > 15) {
          exceptionArray.push(8)
        }
        let flag = false
        for (let i = 0; i < u.sapArray.length; i++) {
          if(i>0) {
            if(this.moment(u.sapArray[i].startTime).diff(u.sapArray[i-1].endTime,'minutes') > 30) {
              flag = true
            }
          }
        }
        if(flag) {
          exceptionArray.push(5)//转场异常
        }
        if(u.wagesMinutes && this.divide(u.effectiveMinutes,u.wagesMinutes) < 0.8) {
          exceptionArray.push(6)//有效工时异常
        }

        u.exceptionArray = exceptionArray
      }

      this.dayUserList = dayUserList

      this.btnLoading = false
    },
    buildOptions(title,data) {
      return   {
        title: {
          text: title,
          left: 'center'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            type: 'pie',
            radius: '50%',
            data,
          }
        ],
        label: {
          show: true,
          formatter: '{b}: {c}' // {b}表示名称，{c}表示数值
        },
      }
    },
    cancel() {
      this.$parent.$parent.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        id: null,
        factory: null,
        workDate: null,
        sumNums: null,
        userNums: null,
        laborNums: null,
        outerNums: null,
        userMinutes: null,
        laborMinutes: null,
        outerMinutes: null,
        sumMinutes: null,
        effectiveMinutes: null,
        invalidMinutes: null,
        restMinutes: null,
        otherMinutes: null,
        manageMinutes: null,
        wagesMinutes: null,
        mesMinutes: null,
        mesNums: null,
        remark: null
      };
      this.resetForm("form");
      this.dayUserList = []
      this.weightMinutesList = []
      this.otherMinutesList = []
      this.manageMinutesList = []
      this.qcMinutesList = []
      this.tabOptions = [
        {
          label: '白班',
          value: '0',
          sumMinutes: 0,
          userMinutes: 0,
          laborMinutes: 0,
          outerMinutes: 0,
          weightMinutes: 0,
          otherMinutes: 0,
          manageMinutes: 0,
          qcMinutes: 0,
          sumNums: 0,
          userNums: 0,
          laborNums: 0,
          outerNums: 0,
          weightNums: 0,
          otherNums: 0,
          manageNums: 0,
          qcNums: 0,
        },
        {
          label: '晚班',
          value: '1',
          sumMinutes: 0,
          userMinutes: 0,
          laborMinutes: 0,
          outerMinutes: 0,
          weightMinutes: 0,
          otherMinutes: 0,
          manageMinutes: 0,
          qcMinutes: 0,
          sumNums: 0,
          userNums: 0,
          laborNums: 0,
          outerNums: 0,
          weightNums: 0,
          otherNums: 0,
          manageNums: 0,
          qcNums: 0,
        },
      ]
    },
    async init(id) {
      this.loading = true
      const res = await getDayHours(id)
      const form = res.data

      this.restList = await allAttendanceRestTime({companyCode: form.factory})
      const workDate = form.workDate
      if(workDate) {
        const searchDateArray = [workDate,]
        searchDateArray.push(this.moment(workDate).add(1, 'days').format('YYYY-MM-DD'))
        const attendanceLogList = await allAttendanceLog({searchDateArray})
        for (const l of attendanceLogList) {
          l.userId = Number(l.userId)
        }
        this.attendanceLogList = attendanceLogList
      }

      const mesHoursList = await allMesHours({workdate: workDate,})
      this.mesHoursList = mesHoursList

      this.form = form

      if(form.userMinutesList && form.userMinutesList.length) {
        this.dayUserList = form.userMinutesList
      }
      await this.refreshAreaHours(form.factory,form.workDate)

      if(form.weightMinutesList) {
        this.weightMinutesList = form.weightMinutesList
      }

      if(form.otherMinutesList) {
        this.otherMinutesList = form.otherMinutesList
      }

      if(form.manageMinutesList) {
        this.manageMinutesList = form.manageMinutesList
      }

      if(form.qcMinutesList) {
        this.qcMinutesList = form.qcMinutesList
      }

      const productionGroup = await allProductionGroup({factory: form.factory})//读取默认名单
      if(!this.weightMinutesList.length) {
        for (const item of productionGroup) {
          if(item.type === 'weight') {
            this.addOtherUser(this.weightMinutesList,item)
          }
        }
        await this.computeItemData('weight')
      }

      if(!this.manageMinutesList.length) {
        for (const item of productionGroup) {
          if(item.type === 'manage') {
            this.addOtherUser(this.manageMinutesList,item)
          }
        }
        await this.computeItemData('manage')
      }

      if(!this.otherMinutesList.length) {
        for (const item of productionGroup) {
          if(item.type === 'other') {
            this.addOtherUser(this.otherMinutesList,item)
          }
        }
        await this.computeItemData('other')
      }

      if(!this.qcMinutesList.length) {
        for (const item of productionGroup) {
          if(item.type === 'qc') {
            this.addOtherUser(this.qcMinutesList,item)
          }
        }
        await this.computeItemData('qc')
      }

      await this.computeDayData()
      await this.buildCharts()

      this.loading = false
    },
    async computeItemData(type) {
      if(type) {
        let wagesMinutes = this.$big(0)
        for (const item of this[type + 'MinutesList']) {
          wagesMinutes = this.add(wagesMinutes,item.wagesMinutes)
        }
        this.form[type + 'Minutes'] = wagesMinutes.toNumber()
        this.form[type + 'Nums'] = this[type + 'MinutesList'].length
      }

      await this.computeDayData()
      await this.$nextTick()
      await this.buildCharts()
    },
    async computeDayData() {
      const tabOptions = this.tabOptions
      const dayUserList = this.dayUserList
      for (const t of tabOptions) {
        const dayUserSailingsList = dayUserList.filter(i=>i.sailings === t.value)
        t.restMinutes= this.sumOfArray(dayUserSailingsList.map(i=>i.restMinutes))
        t.effectiveMinutes= this.sumOfArray(dayUserSailingsList.map(i=>i.effectiveMinutes))
        t.invalidMinutes= this.sumOfArray(dayUserSailingsList.map(i=>i.invalidMinutes))
        t.weightMinutes = this.sumOfArray(this.weightMinutesList.filter(i=>i.sailings === t.value).map(i=>i.finalMinutes ? i.finalMinutes * 60 : i.wagesMinutes))
        t.otherMinutes = this.sumOfArray(this.otherMinutesList.filter(i=>i.sailings === t.value).map(i=>i.finalMinutes ? i.finalMinutes * 60 : i.wagesMinutes))
        t.manageMinutes = this.sumOfArray(this.manageMinutesList.filter(i=>i.sailings === t.value).map(i=>i.finalMinutes ? i.finalMinutes * 60 : i.wagesMinutes))
        t.qcMinutes = this.sumOfArray(this.qcMinutesList.filter(i=>i.sailings === t.value).map(i=>i.finalMinutes ? i.finalMinutes * 60 : i.wagesMinutes))
        const wagesMinutes = this.sumOfArray(dayUserSailingsList.map(i=>i.finalMinutes ? i.finalMinutes * 60 : i.wagesMinutes))

        t.userWagesMinutes = wagesMinutes
        t.wagesMinutes = this.sumOfArray([wagesMinutes,t.weightMinutes,t.otherMinutes,t.manageMinutes])//工资工时去除质检
        t.mesMinutes = this.sumOfArray(dayUserSailingsList.filter(i=>i.sailings === t.value).map(i=>i.mesMinutes))
        t.userMinutes = t.mesMinutes
        t.sumMinutes = this.sumOfArray([t.mesMinutes,t.weightMinutes,t.otherMinutes,t.manageMinutes])//总工时去除质检

        t.userNums = [...new Set(dayUserSailingsList.filter(i=>i.userType === 'user').map(i=>i.userCode))].length
        t.laborNums = [...new Set(dayUserSailingsList.filter(i=>i.userType === 'labor').map(i=>i.userCode))].length
        t.outerNums = [...new Set(dayUserSailingsList.filter(i=>i.userType === 'outer').map(i=>i.userCode))].length
        const sumNums = [...new Set(dayUserSailingsList.map(i=>i.userCode))].length
        t.weightNums = this.weightMinutesList.filter(i=>i.sailings === t.value).length
        t.otherNums = this.otherMinutesList.filter(i=>i.sailings === t.value).length
        t.manageNums = this.manageMinutesList.filter(i=>i.sailings === t.value).length
        t.qcNums = this.qcMinutesList.filter(i=>i.sailings === t.value).length
        t.sumNums = this.sumOfArray([sumNums,t.weightNums,t.otherNums,t.manageNums])//总人数去除质检
      }

      const form = this.form
      form.wagesMinutes = this.sumOfArray(tabOptions.map(i=>i.wagesMinutes))
      form.restMinutes = this.sumOfArray(tabOptions.map(i=>i.restMinutes))
      form.effectiveMinutes = this.sumOfArray(tabOptions.map(i=>i.effectiveMinutes))
      form.invalidMinutes = this.sumOfArray(tabOptions.map(i=>i.invalidMinutes))
      form.weightMinutes = this.sumOfArray(tabOptions.map(i=>i.weightMinutes))
      form.otherMinutes = this.sumOfArray(tabOptions.map(i=>i.otherMinutes))
      form.manageMinutes = this.sumOfArray(tabOptions.map(i=>i.manageMinutes))
      form.qcMinutes = this.sumOfArray(tabOptions.map(i=>i.qcMinutes))
      form.mesMinutes = this.sumOfArray(tabOptions.map(i=>i.mesMinutes))
      form.sumMinutes = this.sumOfArray(tabOptions.map(i=>i.sumMinutes))

      form.userNums = this.sumOfArray(tabOptions.map(i=>i.userNums))
      form.laborNums = this.sumOfArray(tabOptions.map(i=>i.laborNums))
      form.outerNums = this.sumOfArray(tabOptions.map(i=>i.outerNums))
      form.sumNums = this.sumOfArray(tabOptions.map(i=>i.sumNums))
      form.weightNums = this.sumOfArray(tabOptions.map(i=>i.weightNums))
      form.otherNums = this.sumOfArray(tabOptions.map(i=>i.otherNums))
      form.manageNums = this.sumOfArray(tabOptions.map(i=>i.manageNums))
      form.qcNums = this.sumOfArray(tabOptions.map(i=>i.qcNums))

    },
    sumOfArray(array) {
      let nums = this.$big(0)
      for (const n of array) {
        nums = this.add(nums,n)
      }
      return nums.toNumber()
    },
    addOtherUser(array,row) {
      const attendanceArr = this.attendanceLogList.filter(i=> i.userId === row.userId)
      if(attendanceArr && attendanceArr[0]) {
        if(!array.map(i=>i.userId).includes(row.userId)) {
          const attendanceArr = this.attendanceLogList.filter(i=> i.userId === row.userId).sort((a,b)=> a.userCheckTime - b.userCheckTime)
          if(attendanceArr && attendanceArr[1]){ //至少有两个
            const timesArray = attendanceArr.map(i=> this.moment(i.userCheckTime).format('YYYY-MM-DD HH:mm:ss'))
            const startDate = this.moment(this.form.workDate).format('YYYY-MM-DD')
            let upStandTime = startDate + ' 08:30:00'
            let downStandTime =  startDate + ' 20:30:00'
            if(row.sailings === '1') {
              upStandTime = startDate + ' 20:30:00'
              downStandTime = this.moment(startDate).add(1, 'days').format('YYYY-MM-DD') + ' 08:30:00'
            }
            let upTime = findClosestTimeString(timesArray,upStandTime)
            const downTime = findClosestTimeString(timesArray,downStandTime)
            if(upTime && downTime) {
              if(upTime < upStandTime ) {//如果早于8点半,按8点半算
                upTime = upStandTime
              }
              const minutes = this.moment(downTime).diff(upTime,'minutes')
              const workPeriods = [
                {
                  start: this.moment(upTime).format('HH:mm'),
                  end: this.moment(downTime).format('HH:mm'),
                },
              ]
              const restMinutes = calculateIntersectionMinutes(workPeriods,this.restList)
              const wagesMinutes = this.subtract(minutes,restMinutes).toNumber()
              if(minutes > 0) {
                array.push({
                  userId: row.userId,
                  userCode: row.userCode,
                  nickName: row.nickName,
                  startTime: upTime,
                  endTime: downTime,
                  sailings: row.sailings,
                  minutes,
                  restMinutes,
                  wagesMinutes: row.type === 'manage' ? 480 : roundDownToHalfHour(wagesMinutes),//如果是管理工时默认是8小时
                  finalMinutes: undefined,
                })
              }
            }
          }
        }
      }
    },
    async pushHrDate() {
      await this.$refs["form"].validate()
      if(this.dayUserList.length) {
        this.btnLoading = true
        let form = Object.assign({}, this.form)
        let params = {
          workDate: form.workDate,
          factory: form.factory,
          userMinutesList: this.dayUserList,
          otherMinutesList: [
            ...this.weightMinutesList,
            ...this.otherMinutesList,
            ...this.manageMinutesList,
            ...this.qcMinutesList,
          ],
        }
        const res = await pushHrUserAnDate(params)
        if(res.code === 200) {
          this.msgSuccess('推送成功!')
        }
      }
      this.btnLoading = false
    },
    async submitForm() {
      let form = Object.assign({}, this.form)

      const dayUserList = this.dayUserList
      for (const item of dayUserList) {
        item.exceptionTips = this.exceptionOptions.filter(i=> item.exceptionArray.includes(i.value)).map(i=>i.label).join('|')
      }
      form.userMinutesList = dayUserList

      form.weightMinutesList = this.weightMinutesList
      form.otherMinutesList = this.otherMinutesList
      form.manageMinutesList = this.manageMinutesList
      form.qcMinutesList = this.qcMinutesList

      if (form.id != null) {
        try {
          this.btnLoading = true
          await updateDayHours(form)
          this.btnLoading = false
          this.msgSuccess("修改成功")
          // this.$parent.$parent.open = false
          // await this.$parent.$parent.getList()
        } catch (e) {
          this.btnLoading = false
        }
      }
    },
  },
}
</script>
<style scoped lang="scss">

</style>
