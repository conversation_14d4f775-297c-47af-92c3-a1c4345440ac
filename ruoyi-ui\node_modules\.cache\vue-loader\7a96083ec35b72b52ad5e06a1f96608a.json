{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\userTabs.vue?vue&type=style&index=0&id=3384bd2e&scoped=true&lang=scss", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\production\\dayHours\\userTabs.vue", "mtime": 1753954679645}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1744596528942}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1744596530059}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1744596529996}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1744596552583}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQo6OnYtZGVlcCAuZWwtZGl2aWRlciB7DQogIC5lbC1kaXZpZGVyX190ZXh0IHsNCiAgICBmb250LXNpemU6IDE4cHg7DQogICAgZm9udC13ZWlnaHQ6IDY1MDsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["userTabs.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0JA;AACA;AACA;AACA;AACA;AACA", "file": "userTabs.vue", "sourceRoot": "src/views/production/dayHours", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-tabs v-model=\"currentTab\" >\r\n      <el-tab-pane v-for=\"tab in tabOptions\" :key=\"tab.value\" :name=\"tab.value\" :label=\"tab.label\" >\r\n\r\n        <DayHoursUserTable\r\n          :day-hours=\"dayHours\"\r\n          :mes-hours-list=\"mesHoursList\"\r\n          :user-array=\"dayUserList.filter(i=>i.userType === tab.value)\"\r\n          :sum-hours=\"getSumHours(tab.value)\"\r\n          @sailingsChange=\"sailingsChange\"\r\n          @computeItemData=\"computeItemData\"\r\n        />\r\n\r\n        <template v-if=\"tab.value === 'user'\" >\r\n          <DayHoursBaseTable\r\n            title=\"称量\"\r\n            :attendance-log-list=\"attendanceLogList\"\r\n            :rest-list=\"restList\"\r\n            :user-list=\"userList\"\r\n            :user-array=\"weightMinutesList\"\r\n            :day-hours=\"dayHours\"\r\n            :sailings=\"sailings\"\r\n            @computeItemData=\"computeItemData('weight')\"\r\n          />\r\n\r\n          <DayHoursBaseTable\r\n            title=\"间接\"\r\n            :attendance-log-list=\"attendanceLogList\"\r\n            :rest-list=\"restList\"\r\n            :user-list=\"userList\"\r\n            :user-array=\"otherMinutesList\"\r\n            :day-hours=\"dayHours\"\r\n            :sailings=\"sailings\"\r\n            @computeItemData=\"computeItemData('other')\"\r\n          />\r\n\r\n          <DayHoursBaseTable\r\n            title=\"管理\"\r\n            :attendance-log-list=\"attendanceLogList\"\r\n            :rest-list=\"restList\"\r\n            :user-list=\"userList\"\r\n            :user-array=\"manageMinutesList\"\r\n            :day-hours=\"dayHours\"\r\n            :sailings=\"sailings\"\r\n            @computeItemData=\"computeItemData('manage')\"\r\n          />\r\n\r\n          <DayHoursBaseTable\r\n            title=\"质检\"\r\n            :attendance-log-list=\"attendanceLogList\"\r\n            :rest-list=\"restList\"\r\n            :user-list=\"userList\"\r\n            :user-array=\"qcMinutesList\"\r\n            :day-hours=\"dayHours\"\r\n            :sailings=\"sailings\"\r\n            @computeItemData=\"computeItemData('qc')\"\r\n          />\r\n\r\n        </template>\r\n\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n  </div>\r\n</template>\r\n<script >\r\nimport DayHoursUserTable from \"@/views/production/dayHours/userTable.vue\";\r\nimport DayHoursBaseTable from \"@/views/production/dayHours/baseTable.vue\";\r\n\r\nexport default {\r\n  name: 'dayHoursUserTabs',\r\n  components: {DayHoursBaseTable, DayHoursUserTable},\r\n  props: {\r\n    sailings: {\r\n      type: String,\r\n      required: true,\r\n    },\r\n    dayUserList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    weightMinutesList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    otherMinutesList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    manageMinutesList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    qcMinutesList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    attendanceLogList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    mesHoursList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    dayHours: {\r\n      type: Object,\r\n      required: true,\r\n    },\r\n    userList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n    restList: {\r\n      type: Array,\r\n      required: true,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      currentTab: 'user',\r\n      tabOptions: [\r\n        {label: '正式工',value: 'user'},\r\n        {label: '劳务工',value: 'labor'},\r\n        {label: '包干工',value: 'outer'},\r\n      ],\r\n    }\r\n  },\r\n  async created() {\r\n  },\r\n  methods: {\r\n    sailingsChange(userCode) {\r\n      this.$emit('sailingsChange',userCode)\r\n    },\r\n    computeItemData(type) {\r\n      if(type) {\r\n        this.$emit('computeItemData',type)\r\n      } else {\r\n        this.$emit('computeItemData')\r\n      }\r\n    },\r\n    getSumHours(userType) {\r\n      if(userType === 'user') {\r\n        return this.dayHours.userHours\r\n      } else if(userType === 'labor') {\r\n        return this.dayHours.laborHours\r\n      } else if(userType === 'outer') {\r\n        return this.dayHours.outerHours\r\n      }\r\n    }\r\n  },\r\n}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n::v-deep .el-divider {\r\n  .el-divider__text {\r\n    font-size: 18px;\r\n    font-weight: 650;\r\n  }\r\n}\r\n</style>\r\n"]}]}