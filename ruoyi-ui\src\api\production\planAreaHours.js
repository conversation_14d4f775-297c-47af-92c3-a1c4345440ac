import request from '@/utils/request'

// 查询计划区域工时列表
export function listPlanAreaHours(query) {
  return request({
    url: '/production/planAreaHours/list',
    method: 'get',
    params: query
  })
}

// 查询计划区域工时详细
export function getPlanAreaHours(id) {
  return request({
    url: '/production/planAreaHours/' + id,
    method: 'get'
  })
}

// 新增计划区域工时
export function addPlanAreaHours(data) {
  return request({
    url: '/production/planAreaHours',
    method: 'post',
    data: data
  })
}

// 修改计划区域工时
export function updatePlanAreaHours(data) {
  return request({
    url: '/production/planAreaHours',
    method: 'put',
    data: data
  })
}

// 删除计划区域工时
export function delPlanAreaHours(id) {
  return request({
    url: '/production/planAreaHours/' + id,
    method: 'delete'
  })
}

// 导出计划区域工时
export function exportPlanAreaHours(query) {
  return request({
    url: '/production/planAreaHours/export',
    method: 'get',
    params: query
  })
}

export function allPlanAreaHours(query) {
  return request({
    url: '/production/planAreaHours/all',
    method: 'get',
    params: query
  })
}

export function getDayAreaHours(query) {
  return request({
    url: '/production/planAreaHours/getDayHours',
    method: 'get',
    params: query
  })
}

export function allPlanAreaUserHours(query) {
  return request({
    url: '/production/planAreaHours/allUserHours',
    method: 'get',
    params: query
  })
}

export function allWorkDateAndSailings(code) {
  return request({
    url: '/production/planAreaHours/groupWorkDateAndSailings/' + code,
    method: 'get',
  })
}
