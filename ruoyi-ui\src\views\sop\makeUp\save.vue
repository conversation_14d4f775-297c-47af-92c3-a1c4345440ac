<template>
  <div v-loading="loading" >
    <el-form ref="form" :model="form" :rules="rules" size="mini" label-width="100px">
      <div :class="readonly?'mask':''" >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item prop="labCode" label="实验室编码" >
              <el-input v-model="form.labCode" >
                <template #append >
                  <el-tooltip content="根据实验室编码 带出 配方编码" >
                    <el-button icon="el-icon-search" @click="searchFormulaList" />
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="formulaId" >
              <template #label>
                配方编码
                <el-tooltip content="根据配方编码 带出 称量记录、配方分相、半成品检验记录" >
                  <i class="el-icon-question" />
                </el-tooltip>
              </template>
              <el-select v-model="form.formulaId" filterable @change="formulaChange" >
                <el-option
                  v-for="item in formulaList"
                  :key="item.id"
                  :label="item.formulaCode"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="type">
              <template #label>
                工艺类型
              </template>
              <el-select v-model="form.type" >
                <el-option
                  v-for="item in typeOptions"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row >
        <el-row :gutter="20" >
          <el-col :span="8">
            <el-form-item prop="nums">
              <template #label>
                配制量
                <el-tooltip content="根据 此处的配制量 和 称量记录中的配制比例 计算 称量记录中 单个物料的配制量" >
                  <i class="el-icon-question" />
                </el-tooltip>
              </template>
              <el-input v-model="form.nums" @input="computeNums" >
                <template #append>kg</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户" prop="customerName">
              {{form.customerName}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产品名称" prop="productName">
              {{form.productName}}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" >
          <el-col :span="8">
            <el-form-item label="项目" prop="projectNo">
              {{form.projectNo}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="培训人" prop="pxUser">
              <el-input v-model="form.pxUser" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="培训日期" prop="pxDate">
              <el-date-picker
                clearable
                v-model="form.pxDate"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" >
<!--          <el-col :span="8">-->
<!--            <el-form-item label="配制人" prop="pzUser">-->
<!--              <el-input v-model="form.pzUser" />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--          <el-col :span="8">-->
<!--            <el-form-item label="配制日期" prop="pzDate">-->
<!--              <el-date-picker-->
<!--                clearable-->
<!--                v-model="form.pzDate"-->
<!--                type="date"-->
<!--                value-format="yyyy-MM-dd"-->
<!--              />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
          <el-col :span="8" v-if="form.qcCode" >
            <el-form-item prop="qcCode" label="文件受控编号" >
              {{form.qcCode}}
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <el-divider content-position="left" >工艺详情</el-divider>

      <el-tabs v-model="currentTab" >
        <el-tab-pane key="record" label="称量记录" lazy name="record" >
          <div class="table-wrapper">
            <table class="base-table small-table">
              <tr>
                <th style="width: 60px" >序号</th>
                <th style="width: 100px" >物料代码</th>
                <th style="width: 180px" >物料名称</th>
                <th style="width: 180px" >配制比例</th>
                <th style="width: 180px" >配制量(kg)</th>
              </tr>
              <tr v-for="(item,i) in materialArray" :key="i" >
                <td >{{i+1}}</td>
                <td>{{item.erpCode}}</td>
                <td>{{item.materialCode}}</td>
                <td><span :style="percentageStyle(item)">{{item.percentage}}%</span></td>
                <td>{{item.nums}}</td>
              </tr>
            </table>
          </div>
        </el-tab-pane>
        <el-tab-pane key="splitting" label="配方分相" lazy name="splitting" >
          <div class="table-wrapper">
            <table class="base-table small-table">
              <tr>
                <th style="width: 100px" >物料代码</th>
                <th style="width: 180px" >物料名称</th>
                <th style="width: 180px" >比例</th>
                <th >研发分相</th>
              </tr>
              <tr v-for="(item,i) in materialArray" :key="i" :class="readonly?'mask':''">
                <td>{{item.erpCode}}</td>
                <td>
                  {{item.materialCode}}
                  (<span :style="percentageStyle(item)">{{item.percentage}}%</span>)
                  <i class="el-icon-circle-plus-outline" @click="addSubItem(item)" />
                </td>
                <td :colspan="2" style="padding: 0" >
                  <table class="base-table" style="margin: 0">
                    <tr v-for="(sub,i) in item.array" :key="i" >
                      <td style="width: 180px" >
                        <div style="display: flex;align-items: center">
                          <i class="el-icon-remove-outline" @click="delSubItem(item,i)" />
                          <el-input v-model="sub.percentage" size="mini" >
                            <template #append >%</template>
                          </el-input>
                        </div>
                      </td>
                      <td>
                        <el-input v-model="sub.subItem" size="mini" v-input.num_alp />
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <tr>
                <th >合计</th>
                <td >{{sumPercentage}}<span v-if="sumPercentage">%</span></td>
                <td ></td>
                <td ></td>
              </tr>
            </table>
          </div>
        </el-tab-pane>
        <el-tab-pane key="base" label="工艺详情" lazy name="base" >
          <el-tooltip v-if="!readonly" content="重新生成工艺详情" >
            <el-button icon="el-icon-refresh" size="mini" :loading="btnLoading" @click="generateProcessArray" />
          </el-tooltip>

          <div class="table-wrapper">
            <table class="base-table small-table">
              <thead >
                <tr>
                  <th style="width: 120px" class="nth1" >工段</th>
                  <th style="width: 120px" class="nth2" >主分相</th>
                  <th style="width: 120px" class="nth3" >子分相</th>
                  <th style="width: 120px" class="nth4" >物料代码</th>
                  <th style="width: 120px" class="nth5" >物料名称</th>
                  <th style="width: 120px" class="nth6" >比例</th>
                  <th style="width: 120px" class="nth7" >首次投料比例</th>
                  <th style="width: 1560px;" >设备参数</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item,index) in processArray" :key="index" >
                  <td v-if="index === 0" :rowspan="processArray.length" class="nth1" >配制段</td>
                  <td class="nth2" >{{item.category}}</td>
                  <td class="nth3" :colspan="5" style="padding: 0">
                    <table class="base-table small-table" style="margin: 0" >
                      <tr v-for="(m,z) in item.materialArray" :key="z" >
                        <td>{{m.subItem}}</td>
                        <td>{{m.erpCode}}</td>
                        <td>
                          <span v-if="m.materialCode.startsWith('B') && form.id"
                                style="color: #00afff;cursor: pointer"
                                @click="showBProcess(m)">{{m.materialCode}}</span>
                          <span v-else >{{m.materialCode}}</span>
                        </td>
                        <td>{{m.percentage}}%</td>
                        <td>
                          <span v-if="readonly" >{{m.firstPercentage}}%</span>
                          <el-input v-else v-model="m.firstPercentage" size="mini" type="number" >
                            <template #append>%</template>
                          </el-input>
                        </td>
                      </tr>
                    </table>
                  </td>
                  <td style="padding: 0">
                    <table class="base-table small-table" style="margin: 0" :class="readonly?'mask':''" >
                      <tr>
                        <th style="width: 50px" >
                          <i class="el-icon-circle-plus-outline" v-if="!readonly" @click="addArgsItem(item.equipmentArgs,item.materialArray)" />
                        </th>
                        <th style="width: 80px" >
                          步骤
                          <i class="el-icon-s-help" v-if="!readonly" @click="showDialog(item)" />
                        </th>
                        <th style="width: 120px" >原料</th>
                        <th style="width: 300px" >设备</th>
                        <th style="width: 180px" >工艺描述</th>
                        <th style="width: 100px" >温度(℃)</th>
                        <th style="width: 100px" >均质(rpm/min)</th>
                        <th style="width: 100px" >均质时间(min)</th>
                        <th style="width: 100px" >搅拌(转/min)</th>
                        <th style="width: 100px" >搅拌时间(min)</th>
                        <th style="width: 100px" >时长(min)</th>
                        <th style="width: 100px" >真空mpa</th>
                      </tr>
                      <tr v-for="(d,di) in item.equipmentArgs" :key="di">
                        <td>
                          <i class="el-icon-remove-outline" @click="delItem(item.equipmentArgs,di)" />
                        </td>
                        <td >
                          <el-input v-model="d.step" size="mini" type="number" />
                        </td>
                        <td >
                          <el-select v-model="d.materialIds" filterable multiple size="mini" >
                            <el-option
                              v-for="m in item.materialArray"
                              :key="m.materialId"
                              :label="m.materialCode + '|' + m.subItem"
                              :value="m.materialId" />
                          </el-select>
                        </td>
                        <td>
                          <el-select v-model="d.equipmentId" filterable size="mini" style="width: 280px" >
                            <el-option
                              v-for="e in equipmentList"
                              :key="e.id"
                              :value="e.id"
                              :label="e.name" />
                          </el-select>
                        </td>
                        <td >
                          <span v-if="readonly">{{d.process}}</span>
                          <el-input
                            v-else
                            v-model="d.process"
                            autosize
                            size="mini"
                            type="textarea"
                          />
                        </td>
                        <td ><el-input v-model="d.temperature" size="mini" /></td>

                        <td ><el-input v-model="d.jz" size="mini" /></td>
                        <td ><el-input v-model="d.jzTime" size="mini" /></td>
                        <td ><el-input v-model="d.nj" size="mini" /></td>
                        <td ><el-input v-model="d.njTime" size="mini" /></td>
                        <td ><el-input v-model="d.time" size="mini" /></td>
                        <td ><el-input v-model="d.mpa" size="mini" /></td>
                      </tr>
                    </table>
                  </td>
                </tr>
                <tr >
                  <td >出料</td>
                  <td :colspan="9" >
                    <el-row :gutter="20" >
                      <el-col :span="4" >
                        <div class="cell-wrapper">
                          <div class="label">温度</div>
                          <div class="content">
                            <el-input v-model="form.temperature" size="mini" />
                          </div>
                        </div>
                      </el-col>
                      <el-col :span="4" >
                        <div class="cell-wrapper">
                          <div class="label">滤网目数</div>
                          <div class="content">
                            <el-input v-model="form.meshesNums" size="mini" />
                          </div>
                        </div>
                      </el-col>
                      <el-col :span="4" >
                        <span style="color: #F56C6C">加*号的为关键工艺控制点</span>
                      </el-col>
                      <el-col :span="12" ></el-col>
                    </el-row>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-tab-pane>
        <el-tab-pane key="productivity" label="人工/产能" lazy name="productivity">
          <el-tooltip v-if="!readonly" content="工艺带入设备" >
            <i @click="processToEquipment" style="margin-right: 5px" class="el-icon-refresh" />
          </el-tooltip>
          <BcpProductivityArray :data-array="productivityArray" :equipment-list="equipmentList" />
        </el-tab-pane>
        <el-tab-pane key="bcp" label="半成品检验标准" lazy name="bcp" >

          <div class="table-wrapper" >
            <table class="base-table small-table">
              <tr>
                <th style="width: 120px">类型</th>
                <th style="width: 120px">检测项目</th>
                <th style="width: 240px">研发标准</th>
                <th style="width: 320px">标准值</th>
              </tr>
              <tr v-for="(item,index) in bcpArray" :key="item.id" :class="readonly?'mask':''" >
                <td>{{item.type}}</td>
                <td>{{item.label}}</td>
                <td>{{item.standard}}</td>
                <td>{{item.standardVal}}</td>
              </tr>
            </table>
          </div>
        </el-tab-pane>
        <el-tab-pane key="assess" label="配制可行性评估" lazy name="assess" >
          <ProductionPg :pg-tabs="findingArray" :form="form" />
        </el-tab-pane>
        <el-tab-pane key="stability" label="产品稳定性报告" lazy name="stability" >
          <StabilityList v-if="form.labCode" :lab-no="form.labCode" />
        </el-tab-pane>
        <el-tab-pane key="new" label="新品培训纪要" lazy name="new" >
          <el-row :gutter="20" >
            <el-col :span="8">
              <el-form-item label="标样确认" prop="byQr">
                <el-input v-model="pxJson.byQr" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="标样状态" prop="byStatus">
                <el-input v-model="pxJson.byStatus" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" >
            <el-col :span="8">
              <el-form-item label="工程师" prop="gxs" >
                <el-input v-model="pxJson.gxs" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="签样时间" prop="jyTime">
                <el-date-picker
                  clearable
                  v-model="pxJson.jyTime"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="质量建议" prop="zlJy">
            <el-input v-model="pxJson.zlJy" type="textarea" autosize />
          </el-form-item>
          <el-form-item label="中试结果及处理方案" label-width="150px" prop="clFa">
            <el-input v-model="pxJson.clFa" type="textarea" autosize />
          </el-form-item>
          <el-form-item label="产品介绍" prop="cpJs">
            <el-input v-model="pxJson.cpJs" type="textarea" autosize />
          </el-form-item>
          <el-form-item label="产品特性" prop="cpTx">
            <el-input v-model="pxJson.cpTx" type="textarea" autosize />
          </el-form-item>
          <el-form-item label="特别原料" prop="tbYl">
            <el-input v-model="pxJson.tbYl" type="textarea" autosize />
          </el-form-item>
          <el-form-item label="重点工艺注意事项" label-width="150px" prop="zySx">
            <el-input v-model="pxJson.zySx" type="textarea" autosize />
          </el-form-item>
          <el-form-item label="工程师建议" prop="jy">
            <el-input v-model="pxJson.jy" type="textarea" autosize />
          </el-form-item>

          <el-form-item label="兼容性报告" prop="files">
            <FileUpload v-model="files" :readonly="readonly" />
          </el-form-item>

          <el-divider content-position="left" >参与培训人员</el-divider>

          <div class="table-wrapper" >
            <table class="base-table small-table" >
              <tr>
                <th style="width: 50px" >
                  <i class="el-icon-circle-plus-outline" @click="addPxItem()" />
                </th>
                <th style="width: 120px" >部门</th>
                <th style="width: 120px" >姓名</th>
                <th style="width: 120px" >是否理解</th>
                <th style="width: 180px" >合理建议</th>
                <th >评估改善</th>
              </tr>
              <tr v-for="(item,index) in pxArray" :key="index" >
                <td>
                  <i class="el-icon-remove-outline" @click="delItem(pxArray,index)" />
                </td>
                <td>
                  <el-input v-model="item.dept" size="mini" />
                </td>
                <td>
                  <el-input v-model="item.name" size="mini" />
                </td>
                <td>
                  <el-select v-model="item.isLj" size="mini" >
                    <el-option
                      v-for="dict in whetherOptions"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </td>
                <td>
                  <el-input v-model="item.suggestion" size="mini" type="textArea" autosize />
                </td>
                <td>
                  <el-input v-model="item.pg" size="mini" type="textArea" autosize />
                </td>
              </tr>
            </table>
          </div>

          <el-form-item label="培训总结" prop="summary">
            <el-input v-model="pxJson.summary" type="textarea" autosize />
          </el-form-item>

        </el-tab-pane>
      </el-tabs>

    </el-form>
    <div v-if="!readonly" class="dialog-footer" style="margin-top: 20px">
      <el-button type="primary" @click="submitForm" size="mini" :loading="btnLoading" >确 定</el-button>
      <el-button @click="cancel" size="mini" >取 消</el-button>
    </div>

    <el-dialog :fullscreen="fullscreenFlag" :visible.sync="open" width="1200px" :close-on-click-modal="false" append-to-body>
      <div class="dialog-title" slot="title">
        <span>{{ title }}</span>
        <el-button @click="fullscreenFlag = !fullscreenFlag" type="text"
                   :icon="fullscreenFlag?'ali-icon ali-quxiaoquanping':'ali-icon ali-quanping_o'"/>
      </div>
      <MakeUpBSave
        ref="makeUpBSave"
        :readonly="readonly"
        :equipment-list="equipmentList"
        @close="close"
      />
    </el-dialog>

    <el-dialog :visible.sync="textOpen" width="600px" :close-on-click-modal="false" append-to-body >
      <el-form ref="stepForm" :model="stepForm" :rules="stepRules" size="mini" label-width="100px" >
        <el-form-item label="设备" prop="equipmentId" >
          <el-select v-model="stepForm.equipmentId" filterable size="mini" style="width: 280px" >
            <el-option
              v-for="e in equipmentList"
              :key="e.id"
              :value="e.id"
              :label="e.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="培训总结" prop="processText" >
          <el-input v-model="stepForm.processText" type="textarea" autosize />
        </el-form-item>
      </el-form>

      <div class="dialog-footer" style="margin-top: 20px" >
        <el-button type="primary" @click="buildStepArray" size="mini" >确 定</el-button>
        <el-button @click="textOpen = false" size="mini" >取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import { addMakeUp, getMakeUp, updateMakeUp,countMakeUpByLabCode } from "@/api/sop/makeUp";
import { getProjectByNo, } from "@/api/project/project";
import {allFormula, getFormula} from "@/api/software/formula";
import { allMaterialFormula } from "@/api/software/materialFormula";
import { allEquipment } from "@/api/production/equipment";
import ProjectBaseView from "@/views/project/project/base.vue";
import ProductionPg from "@/views/sop/makeUp/productionPg.vue";
import StabilityList from "@/views/rd/stability/list.vue";
import BcpProductivityArray from "@/views/sop/makeUp/bcpProductivityArray.vue";
import MakeUpBSave from "@/views/sop/makeUpB/save.vue";

export default {
  name: "makeUpSave",
  components: {
    MakeUpBSave,
    BcpProductivityArray,
    StabilityList,
    ProductionPg,
    ProjectBaseView
  },
  props: {
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    sumPercentage() {
      let sum = this.$big(0)
      for (const item of this.materialArray) {
        if(item.percentage) {
          sum = this.add(sum,item.percentage)
        }
      }
      return sum.toNumber()
    }
  },
  data() {
    return {
      loading: false,
      btnLoading: false,
      open: false,
      textOpen: false,
      fullscreenFlag: false,
      title: '',
      form: {},
      rules: {
        projectNo: [
          {required: true,message: '请输入正确的项目编号'},
        ],
        nums: [
          {required: true,message: '请输入配置量'},
        ],
        formulaId: [
          {required: true,message: '请选择配方编码'},
        ],
        type: [
          {required: true,message: '请选择工艺类型'},
        ],
      },
      projectList: [],
      formulaList: [],
      currentProject: {},
      currentTab: 'splitting',
      equipmentList: [],
      aidedEquipmentList: [],
      materialArray: [],
      processArray: [],
      productivityArray: [],
      findingArray: [],
      equipmentTypeOptions: [],
      cycleOptions: [],
      riskLevelOptions: [],
      bcpCode: null,
      currentDiagram: {},
      bcpArray: [],
      conclusionOptions: [
        {label: '通过',value: '0'},
        {label: '不通过',value: '1'},
      ],
      erpDataList: [],
      typeOptions: [
        {label: '标准工艺',value: '0'},
        {label: '大货工艺',value: '1'},
      ],
      currentRow: {},
      pxJson: {},
      pxArray: [],
      whetherOptions: [
        {label: '是',value: '1'},
        {label: '否',value: '0'},
      ],
      files: [],
      stepForm: {},
      stepRules: {
        processText: [
          {required: true,message: '请输入工艺'},
        ],
        equipmentId: [
          {required: true,message: '请选择设备'},
        ],
      },
    }
  },
  async created() {
    this.equipmentList = await allEquipment({typeId: 59})
  },
  methods: {
    resetStepForm() {
      this.stepForm = {
        processText: null,
        equipmentId: null,
      }
      this.resetForm("stepForm")
    },
    showDialog(row) {
      this.currentRow = row
      this.resetStepForm()
      this.textOpen = true
    },
    buildStepArray() {
      const row = this.currentRow
      const form = Object.assign({},this.stepForm)

      const array = this.splitSteps(form.processText)
      for (const item of array) {
        this.addArgsItem(row.equipmentArgs,row.materialArray,item,form.equipmentId)
      }
      this.textOpen = false
    },
    //切分步骤
    splitSteps(text) {
      return text.split('\n').reduce((acc, line) => {
        const trimmedLine = line.trim();
        if (!trimmedLine) return acc; // 跳过空行

        if (/^(\d+\.|★\d+\.)/.test(trimmedLine)) {
          // 新步骤开始（以数字或★数字开头）
          acc.push(trimmedLine);
        } else if (acc.length > 0) {
          // 延续上一步骤的内容
          acc[acc.length - 1] += '\n' + trimmedLine;
        }
        return acc;
      }, []);
    },
    async close(id) {
      this.open = false
    },
    async showBProcess(row) {
      this.currentRow = row
      this.title = row.materialCode
      this.open = true
      await this.$nextTick()
      const makeUpBSave = this.$refs.makeUpBSave
      if(makeUpBSave) {
        makeUpBSave.reset()
        makeUpBSave.form.makeUpId = this.form.id
        makeUpBSave.form.labCode = row.materialCode
        await makeUpBSave.init(row.materialCode)
      }
    },
    async processToEquipment() {
      const processArray = this.processArray
      const productivityArray = this.productivityArray
      const equipmentIdSet = new Set()
      for (const p of processArray) {
        for (const e of p.equipmentArgs) {
          if(e.equipmentId) {
            equipmentIdSet.add(e.equipmentId)
          }
        }
      }
      for (const equipmentId of equipmentIdSet) {
        if(!productivityArray.map(i=>i.equipmentId).includes(equipmentId)) {
          productivityArray.push({
            equipmentId,
            weight: undefined,
            max: undefined,
            min: undefined,
            personNums: undefined,
            hours: undefined,
            productivity: 0,
            hourRate: 0,
          })
        }
      }
    },
    async searchFormulaList() {
      this.loading = true
      const laboratoryCode = this.form.labCode
      const formulaList = await allFormula({laboratoryCode})
      this.formulaList = formulaList

      const res = await countMakeUpByLabCode({labCode: laboratoryCode})
      if(res.code === 200 && res.data) {
        this.msgError('实验室编码已存在!')
      }
      this.loading = false
    },
    computeNums() {
      if(this.form.nums) {
        for (const o of this.materialArray) {
          if(this.form.nums && o.percentage) {
            o.nums = this.multiply(this.form.nums,this.divide(o.percentage,100)).toNumber()
          } else {
            o.nums = 0
          }
        }
      }
    },
    addArgsItem(array,materialArray,process,equipmentId) {
      array.push({
        step: array.length+1,
        materialIds: materialArray.map(i=>i.materialId),
        equipmentId,
        process,
        keyPoints: null,
        temperature: null,
        mpa: null,
        jz: null,
        jzTime: null,
        nj: null,
        njTime: null,
        wj: null,
        zk: null,
        time: null,
      })
    },
    delItem(array,i) {
      array.splice(i,1)
    },
    addPxItem() {
      this.pxArray.push({
        dept: null,
        name: null,
        isLj: null,
        suggestion: null,
        pg: null,
      })
    },
    async generateProcessArray() {
      this.btnLoading = true
      const tempArray = []
      for (const m of this.materialArray) {
        for (const sub of m.array) {
          tempArray.push({
            materialId: m.materialId,
            erpCode: m.erpCode,
            materialCode: m.materialCode,
            percentage: sub.percentage,
            subItem: sub.subItem,
            category: sub.subItem.replace(/\s*/g,"").substring(0, 1),
            firstPercentage: null,
            processArray: [],
          })
        }
      }
      const categorySet = new Set()
      for (const temp of tempArray) {
        categorySet.add(temp.category)
      }
      const processArray = []
      for (const category of categorySet) {
        const materialArray = tempArray.filter(i=>i.category === category).sort((a,b)=> a.subItem.replace(/[a-zA-Z]/g, '') - b.subItem.replace(/[a-zA-Z]/g, ''))
        const equipmentArgs = []
        this.addArgsItem(equipmentArgs,materialArray)
        processArray.push({
          category,
          materialArray,
          equipmentArgs,
          processDesc: null,
          keyPoints: null,
          aidedArray: [],
          files:[],
          materialIds: [],//工艺图中选中的物料
        })
      }
      this.processArray = processArray.sort((a,b)=>a.category.localeCompare(b.category))
      this.btnLoading = false
    },
    percentageStyle(item) {
      let percentage = item.array.filter(i=>i.percentage).map(i=>i.percentage).reduce((a,b)=> this.add(a, b),this.$big(0))
      if(percentage.toNumber() === item.percentage) {
        return {
          color: '#67C23A'
        }
      } else {
        return {
          color: '#F56C6C'
        }
      }
    },
    addSubItem(item) {
      item.array.push({
        percentage: null,
        subItem: null,
      })
    },
    delSubItem(item,i) {
      item.array.splice(i,1)
    },
    async formulaChange() {
      const form = this.form
      const formulaId = form.formulaId
      if(formulaId) {
        const formulaRes = await getFormula(formulaId)
        if(formulaRes.code === 200) {
          const data = formulaRes.data

          form.formulaCode = data.formulaCode
          form.labCode = data.laboratoryCode
          form.customerName = data.customerName
          form.productName = data.productName
          form.projectNo = data.projectNo

          if(data.jcXmJson) {
            const bcpArray = JSON.parse(data.jcXmJson)
            this.bcpArray = this.bcpArray.filter(i=>bcpArray.map(b=>b.id).includes(i.id))//过滤研发中已经删除的半成品
            for (const bcp of bcpArray) {
              if(!this.bcpArray.map(i=>i.id).includes(bcp.id)) {
                bcp.zsStandard = null
                bcp.inspectionArray = []
                this.bcpArray.push(bcp)//补充研发新增的
              }
            }
          }
        }
        const array = await allMaterialFormula({formulaId})
        const materialIdSet = new Set()
        for (const item of array) {
          materialIdSet.add(item.materialId)
        }
        const materialArray = []
        for (const materialId of materialIdSet) {
          const arr = array.filter(i=> i.materialId === materialId)
          if(arr && arr[0]) {
            const o = {
              materialId: arr[0].materialId,
              erpCode: arr[0].erpCode,
              materialCode: arr[0].materialCode,
            }
            const subArray = arr.map(i=> {
              return {
                percentage: i.percentage,
                subItem: i.subItem,
              }
            })
            o.array = subArray
            o.percentage = subArray.map(i=>i.percentage).reduce((a,b)=> this.add(a, b),0).toNumber()
            materialArray.push(o)
          }
        }
        this.materialArray = materialArray

        this.computeNums()
      }
    },
    async searchProject() {
      this.btnLoading = true
      let projectNo = this.form.projectNo
      if(projectNo) {
        projectNo = projectNo.replace(/\s*/g,"")
        const res = await getProjectByNo(projectNo)
        if(res.code === 200) {
          const project = res.data
          if(project) {
            this.currentProject = project
            this.form.projectId = project.id
          }
        }
      }
      this.btnLoading = false
    },
    cancel() {
      this.$parent.$parent.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        id: null,
        projectId: null,
        erpCode: null,
        formulaId: null,
        formulaCode: null,
        diagramId: null,
        projectNo: null,
        nums: 100,
        type: '0',
        temperature: null,
        meshesNums: null,
        labCode: null,
        pxUser: null,
        pxDate: null,
        pzUser: null,
        pzDate: null,
        qcCode: null,
      }
      this.resetForm("form")
      this.currentProject = {}
      this.formulaList = []
      this.materialArray = []
      this.processArray = []
      this.productivityArray = []
      this.findingArray = []
      this.bcpArray = []
      this.bcpCode = null
      this.erpDataList = []
      this.pxJson = {
        byQr: null,
        byStatus: null,
        jyTime: null,
        zlJy: null,
        clFa: null,
        cpJs: null,
        cpTx: null,
        tbYl: null,
        zySx: null,
        jy: null,
        gxs: null,
        summary: null,
      }
      this.pxArray = []
      this.files = []
    },
    async init(id) {
      const res = await getMakeUp(id)
      const form = res.data

      if(form.materialArray) {
        this.materialArray = JSON.parse(form.materialArray)
      }

      if(form.processArray) {
        this.processArray = JSON.parse(form.processArray)
      }

      if(form.productivityArray) {
        this.productivityArray = JSON.parse(form.productivityArray)
      }

      if(form.bcpArray) {
        this.bcpArray = JSON.parse(form.bcpArray)
      }

      if(form.findingArray) {
        this.findingArray = JSON.parse(form.findingArray)
      }

      if(form.pxJson) {
        this.pxJson = JSON.parse(form.pxJson)
      }

      if(form.pxArray) {
        this.pxArray = JSON.parse(form.pxArray)
      }

      if(form.files) {
        this.files = JSON.parse(form.files)
      }

      this.form = form
      await this.searchProject()

    },
    async submitForm() {
      await this.$refs["form"].validate()
      let form = Object.assign({},this.form)

      for (const m of this.materialArray) {
        let percentage = m.array.filter(i=>i.percentage).map(i=>i.percentage).reduce((a,b)=> this.add(a, b),0)
        if(percentage.toNumber() !== m.percentage) {
          this.msgError('配方比例不一致,请修改后提交')
          return
        }
      }

      for (const item of this.productivityArray) {
        if(!item.equipmentId) {
          this.msgError('请选择设备')
          return
        }
        if(!item.weight) {
          this.msgError('请输入标准配制量')
          return
        }
        if(!item.personNums) {
          this.msgError('请输入标准人数')
          return
        }
        if(!item.hours) {
          this.msgError('请输入标准工时')
          return
        }
      }

      form.materialArray = JSON.stringify(this.materialArray)
      form.processArray = JSON.stringify(this.processArray)
      form.productivityArray = JSON.stringify(this.productivityArray)
      form.bcpArray = JSON.stringify(this.bcpArray)
      form.findingArray = JSON.stringify(this.findingArray)
      form.pxJson = JSON.stringify(this.pxJson)
      form.pxArray = JSON.stringify(this.pxArray)
      form.files = JSON.stringify(this.files)

      if (form.id != null) {
        try {
          this.btnLoading = true
          await updateMakeUp(form)
          this.btnLoading = false
          if(res.code === 200) {
            this.msgSuccess("操作成功")
          }
        } catch (e) {
          this.btnLoading = false
        }
      } else {
        try {
          this.btnLoading = true
          const res = await addMakeUp(form)
          this.btnLoading = false
          if(res.code === 200) {
            this.msgSuccess("操作成功")
            await this.init(res.data.id)
          }
        } catch (e) {
          this.btnLoading = false
        }
      }
    },
  }
}
</script>
<style scoped lang="scss">

.diagram-wrapper {
  height: 0;
  padding-top: 100%;
  background-size: 100% auto;
  background-position: center center;
  background-repeat: no-repeat;
  position: relative;

  .pointer {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 1px solid #DCDFE6;
    cursor: pointer;
    text-align: center;
    line-height: 20px;
    font-size: 12px;
    white-space: nowrap;
  }

}

.table-wrapper {
  max-height: 90vh;

  .base-table {
    thead {
      position: sticky;
      top: 0;
      z-index: 3;
    }

    .nth1 {
      position: sticky;
      left: 0px;
      z-index: 1;
    }

    .nth2 {
      position: sticky;
      left: 120px;
      z-index: 1;
    }

    .nth3 {
      position: sticky;
      left: 240px;
      z-index: 1;
    }

    .nth4 {
      position: sticky;
      left: 360px;
      z-index: 1;
    }

    .nth5 {
      position: sticky;
      left: 480px;
      z-index: 1;
    }

    .nth6 {
      position: sticky;
      left: 600px;
      z-index: 1;
    }

    .nth7 {
      position: sticky;
      left: 720px;
      z-index: 1;
    }

    th:nth-child(-n+7) {
      background: #f8f8f9;
      color: #515a6e;
    }

    td:nth-child(-n+7) {
      background: #fff;
    }
  }
}
</style>
